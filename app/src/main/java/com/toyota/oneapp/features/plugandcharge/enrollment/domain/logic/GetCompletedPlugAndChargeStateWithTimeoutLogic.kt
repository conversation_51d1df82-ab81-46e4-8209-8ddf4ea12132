/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetCompletedPlugAndChargeStateWithTimeoutUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetEnrollmentStatusInstallationCompleteUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetPlugAndChargeActivatingTimesUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeoutOrNull
import javax.inject.Inject

class GetCompletedPlugAndChargeStateWithTimeoutLogic
    @Inject
    constructor(
        private val getPlugAndChargeActivatingFlowTimes: GetPlugAndChargeActivatingTimesUseCase,
        private val getEnrollmentStatusInstallationComplete: GetEnrollmentStatusInstallationCompleteUseCase,
    ) :
    GetCompletedPlugAndChargeStateWithTimeoutUseCase {
        override suspend fun invoke(): PlugAndChargeEnrollmentState.InstallationComplete? =
            getPlugAndChargeActivatingFlowTimes().run {
                withTimeoutOrNull(timeoutDuration) {
                    var result: PlugAndChargeEnrollmentState.InstallationComplete? = null
                    while (result == null) {
                        result = getEnrollmentStatusInstallationComplete()
                        if (result == null) {
                            delay(pollingInterval)
                        }
                    }
                    result
                }
            }
    }
