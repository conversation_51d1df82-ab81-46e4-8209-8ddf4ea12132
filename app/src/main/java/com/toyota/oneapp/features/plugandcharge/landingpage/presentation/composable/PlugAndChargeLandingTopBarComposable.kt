/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.presentation.composable

import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OAAppBar
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun PlugAndChargeLandingTopBarComposable(
    modifier: Modifier = Modifier,
    onBack: () -> Unit,
) {
    OAAppBar(
        modifier = modifier,
        title = stringResource(id = R.string.plug_and_charge),
        // TODO: set testTagId
        // https://toyotaconnected.atlassian.net/browse/OAD01-28275
        testTagId = "",
        onBack = onBack,
    )
}

@Composable
@Preview
private fun PlugAndChargeLandingTopBarComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = themeMode,
    ) {
        PlugAndChargeLandingTopBarComposable(onBack = {})
    }
}
