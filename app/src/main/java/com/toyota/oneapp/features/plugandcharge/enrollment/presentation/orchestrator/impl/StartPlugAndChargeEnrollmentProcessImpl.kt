/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.impl

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.PostPlugAndChargeEnrollmentUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.ObservePlugAndChargeActivatingState
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.StartPlugAndChargeEnrollmentProcess
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder.PlugAndChargeEnrollmentStateHolder
import javax.inject.Inject

class StartPlugAndChargeEnrollmentProcessImpl
    @Inject
    constructor(
        private val postPlugAndChargeEnrollment: PostPlugAndChargeEnrollmentUseCase,
        private val stateHolder: PlugAndChargeEnrollmentStateHolder,
        private val observePlugAndChargeActivatingState: ObservePlugAndChargeActivatingState,
    ) : StartPlugAndChargeEnrollmentProcess {
        private val defaultConnectingToVehicleState =
            PlugAndChargeEnrollmentState
                .ContainsError
                .ConnectingToVehicle(false)

        override suspend fun invoke() {
            stateHolder.run {
                update(defaultConnectingToVehicleState)
                postPlugAndChargeEnrollment().onSuccess {
                    observePlugAndChargeActivatingState()
                }.onFailure {
                    update(defaultConnectingToVehicleState.copy(isShowingError = true))
                }
            }
        }
    }
