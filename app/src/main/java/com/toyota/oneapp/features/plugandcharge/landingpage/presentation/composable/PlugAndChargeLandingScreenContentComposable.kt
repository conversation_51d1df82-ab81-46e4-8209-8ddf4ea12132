/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.presentation.composable

import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody1TextView
import com.toyota.oneapp.features.core.composable.spacer.VerticalSpacer
import com.toyota.oneapp.features.core.theme.AppTheme.colors
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun PlugAndChargeLandingScreenContentComposable(modifier: Modifier = Modifier) {
    LazyColumn(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        item {
            PlugAndChargeLogoComposable(
                modifier =
                    Modifier.size(
                        width = 100.dp,
                        height = 66.dp,
                    ),
            )
        }
        item {
            VerticalSpacer(size = 32)
        }
        item {
            OABody1TextView(
                text = stringResource(id = R.string.plug_and_charge_description),
                textAlign = TextAlign.Center,
                color = colors.tertiary05,
            )
        }
        item {
            VerticalSpacer(size = 32)
        }
        item {
            PlugAndChargeSettingsComposable()
        }
        item {
            VerticalSpacer(size = 40)
        }
        item {
            PlugAndChargeCompatibleStationsComposable()
        }
        item {
            VerticalSpacer(size = 40)
        }
        item {
            PlugAndChargeDisclaimerComposable()
        }
    }
}

@Composable
@Preview
private fun PlugAndChargeLandingTopBarComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = themeMode,
    ) {
        PlugAndChargeLandingScreenContentComposable()
    }
}
