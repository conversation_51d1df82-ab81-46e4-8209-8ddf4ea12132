/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.presentation.composable

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody1TextView
import com.toyota.oneapp.features.core.theme.AppTheme.colors
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun PlugAndChargeDisclaimerComposable(modifier: Modifier = Modifier) {
    Card(
        backgroundColor = colors.tertiary12,
        elevation = 0.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        OABody1TextView(
            modifier =
                Modifier
                    .padding(
                        vertical = 16.dp,
                        horizontal = 8.dp,
                    ),
            text = stringResource(id = R.string.plug_and_charge_disclaimer),
            textAlign = TextAlign.Center,
            color = colors.tertiary05,
        )
    }
}

@Composable
@Preview
private fun PlugAndChargeDisclaimerComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        themeMode = themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        PlugAndChargeDisclaimerComposable()
    }
}
