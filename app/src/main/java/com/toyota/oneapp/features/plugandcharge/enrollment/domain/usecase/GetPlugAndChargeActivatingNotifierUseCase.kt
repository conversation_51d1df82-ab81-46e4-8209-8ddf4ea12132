/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase

import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import kotlinx.coroutines.flow.Flow

fun interface GetPlugAndChargeActivatingNotifierUseCase {
    operator fun invoke(): Flow<PlugAndChargeEnrollmentState>
}
