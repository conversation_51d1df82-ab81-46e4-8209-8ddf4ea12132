/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase

import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState

fun interface GetCompletedPlugAndChargeStateWithTimeoutUseCase {
    suspend operator fun invoke(): PlugAndChargeEnrollmentState.InstallationComplete?
}
