/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetEnrollmentStatusInstallationCompleteUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetPlugAndChargeEnrollmentStatusUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import javax.inject.Inject

class GetEnrollmentStatusInstallationCompleteLogic
    @Inject
    constructor(
        private val getPlugAndChargeEnrollmentStatus: GetPlugAndChargeEnrollmentStatusUseCase,
    ) :
    GetEnrollmentStatusInstallationCompleteUseCase {
        override suspend fun invoke(): PlugAndChargeEnrollmentState.InstallationComplete? =
            getPlugAndChargeEnrollmentStatus()
                .map { status ->
                    PlugAndChargeEnrollmentState.InstallationComplete.takeIf {
                        status is PlugAndChargeEnrollmentStatus.Enrolled
                    }
                }
                .getOrNull()
    }
