/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetCompletedPlugAndChargeStateWithTimeoutUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetPlugAndChargeActivatingNotifierUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class GetPlugAndChargeActivatingNotifierLogic
    @Inject
    constructor(
        private val getCompletedPlugAndChargeStateWithTimeout: GetCompletedPlugAndChargeStateWithTimeoutUseCase,
    ) : GetPlugAndChargeActivatingNotifierUseCase {
        private val defaultState =
            PlugAndChargeEnrollmentState.ContainsError.ActivatingPlugAndCharge(
                isShowingError = false,
            )

        override fun invoke(): Flow<PlugAndChargeEnrollmentState> =
            flow {
                emit(defaultState)
                emit(getCompletedPlugAndChargeStateWithTimeout() ?: defaultState.copy(isShowingError = true))
            }
    }
