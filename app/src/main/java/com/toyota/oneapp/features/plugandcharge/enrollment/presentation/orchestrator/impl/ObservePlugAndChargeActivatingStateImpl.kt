/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.impl

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetPlugAndChargeActivatingNotifierUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.ObservePlugAndChargeActivatingState
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder.PlugAndChargeEnrollmentStateHolder
import javax.inject.Inject

class ObservePlugAndChargeActivatingStateImpl
    @Inject
    constructor(
        private val getPlugAndChargeActivatingFlow: GetPlugAndChargeActivatingNotifierUseCase,
        private val stateHolder: PlugAndChargeEnrollmentStateHolder,
    ) : ObservePlugAndChargeActivatingState {
        override suspend fun invoke() {
            getPlugAndChargeActivatingFlow().collect {
                stateHolder.update(it)
            }
        }
    }
