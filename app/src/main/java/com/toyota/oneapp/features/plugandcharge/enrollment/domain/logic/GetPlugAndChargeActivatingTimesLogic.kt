/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeActivatingFlowTimesModel
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetPlugAndChargeActivatingTimesUseCase
import javax.inject.Inject
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

class GetPlugAndChargeActivatingTimesLogic
    @Inject
    constructor() : GetPlugAndChargeActivatingTimesUseCase {
        override fun invoke(): PlugAndChargeActivatingFlowTimesModel =
            PlugAndChargeActivatingFlowTimesModel(
                timeoutDuration = 10.minutes,
                pollingInterval = 5.seconds,
            )
    }
