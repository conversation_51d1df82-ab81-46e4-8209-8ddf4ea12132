package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeActivatingFlowTimesModel
import org.junit.Assert.assertEquals
import org.junit.Test
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

class GetPlugAndChargeActivatingTimesLogicTest {
    private val logic = GetPlugAndChargeActivatingTimesLogic()

    @Test
    fun `when invoked, then returns expected durations`() {
        val result = logic()

        assertEquals(
            PlugAndChargeActivatingFlowTimesModel(
                timeoutDuration = 10.minutes,
                pollingInterval = 5.seconds,
            ),
            result,
        )
    }
}
