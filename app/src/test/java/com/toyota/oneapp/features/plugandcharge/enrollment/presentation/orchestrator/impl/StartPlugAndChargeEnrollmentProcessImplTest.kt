/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.impl

import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder.PlugAndChargeEnrollmentStateHolder
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class StartPlugAndChargeEnrollmentProcessImplTest {
    private lateinit var emittedStates: MutableList<PlugAndChargeEnrollmentState>

    private lateinit var startPlugAndChargeEnrollmentProcess: StartPlugAndChargeEnrollmentProcessImpl

    @Before
    fun setUp() {
        emittedStates = mutableListOf()
    }

    @Test
    fun `when post succeeds, then emits connecting state`() =
        runTest {
            prepareScenario(
                postResult = Result.success(Unit),
                onObserve = {},
            )

            startPlugAndChargeEnrollmentProcess()

            assertEquals(
                listOf(
                    PlugAndChargeEnrollmentState.ContainsError.ConnectingToVehicle(false),
                ),
                emittedStates,
            )
        }

    @Test
    fun `when post succeeds, then observer is triggered`() =
        runTest {
            var observerCalled = false
            prepareScenario(
                postResult = Result.success(Unit),
                onObserve = { observerCalled = true },
            )

            startPlugAndChargeEnrollmentProcess()

            assertTrue(observerCalled)
        }

    @Test
    fun `when post fails, then emits connecting and error states`() =
        runTest {
            prepareScenario(
                postResult = Result.failure(Throwable("post failed")),
                onObserve = {},
            )

            startPlugAndChargeEnrollmentProcess()

            assertEquals(
                listOf(
                    PlugAndChargeEnrollmentState.ContainsError.ConnectingToVehicle(false),
                    PlugAndChargeEnrollmentState.ContainsError.ConnectingToVehicle(true),
                ),
                emittedStates,
            )
        }

    @Test
    fun `when post fails, then observer is not triggered`() =
        runTest {
            var observerCalled = false
            prepareScenario(
                postResult = Result.failure(Throwable("post failed")),
                onObserve = { observerCalled = true },
            )

            startPlugAndChargeEnrollmentProcess()

            assertFalse(observerCalled)
        }

    private fun prepareScenario(
        postResult: Result<Unit>,
        onObserve: suspend () -> Unit,
    ) {
        startPlugAndChargeEnrollmentProcess =
            StartPlugAndChargeEnrollmentProcessImpl(
                postPlugAndChargeEnrollment = { postResult },
                stateHolder =
                    object : PlugAndChargeEnrollmentStateHolder {
                        override val state =
                            MutableStateFlow(PlugAndChargeEnrollmentState.InstallationComplete)

                        override fun update(newState: PlugAndChargeEnrollmentState) {
                            emittedStates += newState
                        }
                    },
                observePlugAndChargeActivatingState = onObserve,
            )
    }
}
