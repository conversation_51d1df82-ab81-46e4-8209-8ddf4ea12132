package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Test

class GetEnrollmentStatusInstallationCompleteLogicTest {
    private lateinit var logic: GetEnrollmentStatusInstallationCompleteLogic

    @Test
    fun `when enrollment is enrolled, then returns InstallationComplete`() =
        runTest {
            prepareScenario(
                result = Result.success(PlugAndChargeEnrollmentStatus.Enrolled(isToggleOn = true)),
            )

            val state = logic()

            assertEquals(PlugAndChargeEnrollmentState.InstallationComplete, state)
        }

    @Test
    fun `when enrollment is incomplete, then returns null`() =
        runTest {
            prepareScenario(
                result = Result.success(PlugAndChargeEnrollmentStatus.NotEnrolled.Incomplete(progress = 2)),
            )

            val state = logic()

            assertNull(state)
        }

    @Test
    fun `when enrollment is not started, then returns null`() =
        runTest {
            prepareScenario(
                result = Result.success(PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted),
            )

            val state = logic()

            assertNull(state)
        }

    @Test
    fun `when enrollment is not possible, then returns null`() =
        runTest {
            prepareScenario(
                result = Result.success(PlugAndChargeEnrollmentStatus.NotEnrolled.EnrollmentNotPossible),
            )

            val state = logic()

            assertNull(state)
        }

    @Test
    fun `when result is failure, then returns null`() =
        runTest {
            prepareScenario(
                result = Result.failure(Throwable("Failed to get enrollment")),
            )

            val state = logic()

            assertNull(state)
        }

    private fun prepareScenario(result: Result<PlugAndChargeEnrollmentStatus>) {
        logic =
            GetEnrollmentStatusInstallationCompleteLogic(
                getPlugAndChargeEnrollmentStatus = {
                    result
                },
            )
    }
}
