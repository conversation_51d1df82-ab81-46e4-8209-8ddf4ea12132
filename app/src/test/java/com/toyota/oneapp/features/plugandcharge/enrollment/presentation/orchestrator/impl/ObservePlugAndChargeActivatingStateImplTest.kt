package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.impl

import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder.PlugAndChargeEnrollmentStateHolder
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test

class ObservePlugAndChargeActivatingStateImplTest {
    private val emittedStates = mutableListOf<PlugAndChargeEnrollmentState>()

    private lateinit var logic: ObservePlugAndChargeActivatingStateImpl

    @Test
    fun `when flow emits states, then updates state holder with all of them`() =
        runTest {
            val firstState = PlugAndChargeEnrollmentState.ContainsError.ActivatingPlugAndCharge(false)
            val secondState = firstState.copy(isShowingError = true)

            prepareScenario(
                flowStates = listOf(firstState, secondState),
            )

            logic()

            assertEquals(listOf(firstState, secondState), emittedStates)
        }

    private fun prepareScenario(flowStates: List<PlugAndChargeEnrollmentState>) {
        logic =
            ObservePlugAndChargeActivatingStateImpl(
                getPlugAndChargeActivatingFlow = {
                    flowOf(*flowStates.toTypedArray())
                },
                stateHolder =
                    object : PlugAndChargeEnrollmentStateHolder {
                        override val state =
                            MutableStateFlow(PlugAndChargeEnrollmentState.InstallationComplete)

                        override fun update(newState: PlugAndChargeEnrollmentState) {
                            emittedStates += newState
                        }
                    },
            )
    }
}
