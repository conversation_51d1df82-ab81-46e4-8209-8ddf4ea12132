package com.toyota.oneapp.features.publiccharging.application

import com.toyota.oneapp.features.publiccharging.application.logic.RemoveDisabledChargingNetworksFromChargeStationsInfoLogic
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import org.junit.Assert.assertEquals
import org.junit.Test

class RemoveDisabledChargingNetworksFromChargeStationsInfoLogicTest {
    private val teslaStation =
        ChargeStationInfo(
            stationName = "TESLA",
            addressLine1 = null,
            addressLine2 = null,
            evConnectorSum = null,
            markerInfo = null,
        )
    private val ionnaStation = teslaStation.copy(stationName = "IONNA")
    private val otherStation = teslaStation.copy(stationName = "Other")
    private val nullStation = teslaStation.copy(stationName = null)

    private lateinit var logic: RemoveDisabledChargingNetworksFromChargeStationsInfoLogic

    @Test
    fun `when no networks are disabled, then returns all the networks`() {
        prepareScenario(
            disabledNetworkNames = emptyList(),
        )
        val chargeStationsInfo = listOf(teslaStation, ionnaStation, otherStation)

        val result =
            logic(
                chargeStationsInfo = chargeStationsInfo,
            )

        assertEquals(chargeStationsInfo, result)
    }

    @Test
    fun `when tesla is disabled, then removes tesla station`() {
        prepareScenario(
            disabledNetworkNames = listOf("tesla"),
        )

        val result =
            logic(
                chargeStationsInfo = listOf(teslaStation, ionnaStation, otherStation),
            )

        assertEquals(
            listOf(ionnaStation, otherStation),
            result,
        )
    }

    @Test
    fun `when tesla and other are disabled, then returns only ionna`() {
        prepareScenario(
            disabledNetworkNames = listOf("tesla", "other"),
        )

        val result =
            logic(
                chargeStationsInfo = listOf(teslaStation, ionnaStation, otherStation),
            )

        assertEquals(
            listOf(ionnaStation),
            result,
        )
    }

    @Test
    fun `when stationName is null, then it is preserved`() {
        prepareScenario(
            disabledNetworkNames = listOf("tesla"),
        )

        val result =
            logic(
                chargeStationsInfo = listOf(teslaStation, nullStation),
            )

        assertEquals(
            listOf(nullStation),
            result,
        )
    }

    private fun prepareScenario(disabledNetworkNames: List<String>) {
        logic =
            RemoveDisabledChargingNetworksFromChargeStationsInfoLogic(
                getDisabledChargingNetworkNames = { disabledNetworkNames },
            )
    }
}
