package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeActivatingFlowTimesModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Test
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds

class GetCompletedPlugAndChargeStateWithTimeoutLogicTest {
    private lateinit var logic: GetCompletedPlugAndChargeStateWithTimeoutLogic

    @Test
    fun `when installation is completed before timeout, then returns InstallationComplete`() =
        runTest {
            prepareScenario(
                activatingTimesModel =
                    PlugAndChargeActivatingFlowTimesModel(
                        timeoutDuration = 2.seconds,
                        pollingInterval = 100.milliseconds,
                    ),
                responses = listOf(null, null, PlugAndChargeEnrollmentState.InstallationComplete),
            )

            val result = logic()

            assertEquals(PlugAndChargeEnrollmentState.InstallationComplete, result)
        }

    @Test
    fun `when installation is never completed before timeout, then returns null`() =
        runTest {
            prepareScenario(
                activatingTimesModel =
                    PlugAndChargeActivatingFlowTimesModel(
                        timeoutDuration = 500.milliseconds,
                        pollingInterval = 100.milliseconds,
                    ),
                responses = List(10) { null },
            )

            val result = logic()

            assertNull(result)
        }

    private fun prepareScenario(
        activatingTimesModel: PlugAndChargeActivatingFlowTimesModel,
        responses: List<PlugAndChargeEnrollmentState.InstallationComplete?>,
    ) {
        val responseIterator = responses.iterator()

        logic =
            GetCompletedPlugAndChargeStateWithTimeoutLogic(
                getPlugAndChargeActivatingFlowTimes = {
                    activatingTimesModel
                },
                getEnrollmentStatusInstallationComplete = {
                    if (responseIterator.hasNext()) responseIterator.next() else null
                },
            )
    }
}
