package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test

class GetPlugAndChargeActivatingNotifierLogicTest {
    private lateinit var logic: GetPlugAndChargeActivatingNotifierLogic

    @Test
    fun `when completed state is returned, then emits activating and then complete`() =
        runTest {
            prepareScenario(
                result = PlugAndChargeEnrollmentState.InstallationComplete,
            )

            val result = logic().toList()

            assertEquals(
                listOf(
                    PlugAndChargeEnrollmentState.ContainsError.ActivatingPlugAndCharge(false),
                    PlugAndChargeEnrollmentState.InstallationComplete,
                ),
                result,
            )
        }

    @Test
    fun `when completed state is null, then emits activating and then activating with error`() =
        runTest {
            prepareScenario(
                result = null,
            )

            val result = logic().toList()

            assertEquals(
                listOf(
                    PlugAndChargeEnrollmentState.ContainsError.ActivatingPlugAndCharge(false),
                    PlugAndChargeEnrollmentState.ContainsError.ActivatingPlugAndCharge(true),
                ),
                result,
            )
        }

    private fun prepareScenario(result: PlugAndChargeEnrollmentState.InstallationComplete?) {
        logic =
            GetPlugAndChargeActivatingNotifierLogic(
                getCompletedPlugAndChargeStateWithTimeout = {
                    result
                },
            )
    }
}
