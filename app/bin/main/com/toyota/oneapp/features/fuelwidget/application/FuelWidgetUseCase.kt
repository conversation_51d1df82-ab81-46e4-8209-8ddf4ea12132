package com.toyota.oneapp.features.fuelwidget.application

import android.location.Location
import com.toyota.oneapp.features.core.commonapicalls.application.ElectricStatusState
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.domain.model.ElectricStatusModelResponse
import com.toyota.oneapp.features.dashboard.dashboard.domain.model.Telemetry
import com.toyota.oneapp.features.fuelwidget.presentation.utils.VehicleType
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

abstract class FuelWidgetUseCase {
    abstract fun fetchElectricVehicleStatus(vehicleInfo: VehicleInfo): Flow<ElectricStatusState>

    abstract fun updateGasState(
        vehicleInfo: VehicleInfo,
        telemetry: Telemetry,
    ): Flow<FuelState>

    abstract fun checkVehicleType(vehicleInfo: VehicleInfo): Flow<VehicleType>

    abstract fun postRealTimeStatus(vehicleInfo: VehicleInfo): Flow<String?>

    abstract fun getElectricRealTimeVehicleStatus(
        vehicleInfo: VehicleInfo,
        appRequestNo: String,
    ): Flow<ElectricStatusModelResponse?>

    abstract fun updateEVState(
        vehicleInfo: VehicleInfo,
        telemetry: Telemetry,
        homeLocation: Location?,
        appRequestNo: String,
        forceRestart: () -> Unit,
    ): Flow<FuelState>

    abstract fun updatePHEVState(
        vehicleInfo: VehicleInfo,
        telemetry: Telemetry,
        appRequestNo: String,
        forceRestart: () -> Unit,
    ): Flow<FuelState>

    abstract fun fetchNonRealTimeElectricStatus(
        electricStatusResponse: ElectricStatusResponse?,
        vehicleInfo: VehicleInfo,
        telemetry: Telemetry,
    ): Flow<FuelState>
}
