/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.fuelwidget.application

import android.location.Location
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.fuelwidget.presentation.utils.ButtonType
import com.toyota.oneapp.features.fuelwidget.presentation.utils.VehiclePlugStatus

sealed class FuelState {
    data object NotAvailable : FuelState()

    data object Loading : FuelState()

    data class Success(
        val vehicleFuel: VehicleFuel,
        val realtimeStatus: Boolean,
    ) : FuelState()
}

sealed class VehicleFuel {
    data class Gas(
        val gasVehicleState: GasVehicleState,
    ) : VehicleFuel()

    data class Phev(
        val phevState: PHEVState,
    ) : VehicleFuel()

    data class Ev(
        val evState: EVState,
    ) : VehicleFuel()
}

data class GasVehicleState(
    val distanceToEmpty: UnitValue,
    val fuelLevel: Int?,
    val fuelType: String?,
    val isFuelGaugeVisible: Boolean?,
    val isRangeVisible: Boolean?,
    val fuelRangeIcon: Int,
    val isDisplayDistanceToEmpty: Boolean,
    val distanceToEmptyTitle: Int,
    val fuelGaugeProgressValue: Float,
)

data class PHEVState(
    val electricStatusResponse: ElectricStatusResponse?,
    val distanceToEmpty: UnitValue,
    val fuelLevel: Int?,
    val chargeRemaining: String,
    val isCharging: Boolean,
    val plugStatus: VehiclePlugStatus,
    val isFuelGaugeVisible: Boolean?,
    val isRangeVisible: Boolean?,
    val isChargeInfoLayoutVisible: Boolean,
    val isRemoteStatus: Int?,
    val isPluggedIn: Boolean?,
    val chargePercentage: Int,
    val estimatedTime: String,
    val evBatteryLow: Boolean,
    val displayDistanceToEmpty: Boolean,
    val evOdometerButtonType: ButtonType,
    val isHybridElectricFuelType: Boolean,
    val fuelRangeIcon: Int,
    val distanceToEmptyTitle: Int,
    val fuelGaugeProgressValue: Float,
    val isWattTimeEnabled: Boolean,
)

data class EVState(
    val electricStatusResponse: ElectricStatusResponse?,
    val estimateDistance: UnitValue,
    val chargeLevel: Int,
    val isFuelGaugeVisible: Boolean?,
    val isRangeVisible: Boolean?,
    val isEVChargeStationEnabled: Boolean,
    val evOdometerButtonType: ButtonType,
    val isBatteryLow: Boolean,
    val isPluggedIn: Boolean,
    val isCharging: Boolean,
    val chargeRemaining: Int?,
    val estimatedTime: String,
    val chargePercentage: Int,
    val fuelGaugeProgressValue: Float,
    val isWattTimeEnabled: Boolean,
    val getHomeAddressCoordinates: String,
    val isClosedToHome: Boolean,
    val homeAddressLocation: Location? = null,
)

data class UnitValue(
    val value: Any?,
    val unit: String,
)
