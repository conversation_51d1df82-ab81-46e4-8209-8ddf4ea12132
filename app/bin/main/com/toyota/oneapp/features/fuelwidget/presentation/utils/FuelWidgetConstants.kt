package com.toyota.oneapp.features.fuelwidget.presentation.utils

enum class VehicleType {
    GasVehicle,
    PHEVVehicle,
    EVVehicle,
    ConnectedVehicle,
    NonConnectedVehicle,
}

enum class VehiclePlugStatus {
    Charging,
    NotCharging,
    FullCharge,
    Unplugged,
    FindStations,
    UnDefined,
}

object FuelWidgetConstants {
    // Fuel Type
    const val FUEL_TYPE_PLUGIN_HYBRID = "I"
    const val FUEL_TYPE_HYDROGEN_FUELCELL = "R"
    const val FUEL_TYPE_PURE_ELECTRIC = "E"

    // Vehicle Type
    const val MIRAI = "Mirai"

    // Remote Status
    const val REMOTE_STATUS_HIDE = 0
    const val REMOTE_STATUS_FAILED = 4
    const val REMOTE_STATUS_PENDING = 5
    const val REMOTE_STATUS_ACTIVATED = 7
    const val REMOTE_STATUS_AUTH_REQUIRED = 1
    const val ZERO = 0

    const val STRING_ZERO = "0"
    const val EMPTY_VALUE = " "
    const val DEFAULT_UNIT = "mi"
    const val EMPTY_PERCENTAGE_CHARGED = "--"
    const val NEGATIVE_PERCENTAGE = "-1"

    const val HOME = "home"
    const val HOME_RANGE_METERS = 402 // 402 meters ˜= 0.25 miles
    const val REAL_TIME_STATUS_IN_PROGRESS = 9
    const val REAL_TIME_STATUS_SUCCESS = 0
    const val REAL_TIME_STATUS_TIMEOUT = 3
    const val REAL_TIME_STATUS_SYSTEM_ERROR = 2
    const val REAL_TIME_STATUS_DCM_ERROR = 1
}

enum class ChargeStatus {
    NORMAL_CHARGING,
    NORMAL_CHARGING_COMPLETE,
    FAST_CHARGING,
    FAST_CHARGING_COMPLETE,
    PLUG_WAITING_FOR_TIMER_TO_CHARGE,
    UNKNOWN,
    STOPPED,
    ERROR,
}

enum class ButtonType {
    PARTNER_CHARGING,
    UNKNOWN_CHARGING,
    PARTNER_DETAILS,
    UNPLUG,
    FIND_STATIONS,
    CHARGE_NOW,
}
