/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.fuelwidget.presentation

import android.location.Address
import android.location.Geocoder
import android.location.Location
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.fuelwidget.presentation.utils.ButtonType
import com.toyota.oneapp.features.fuelwidget.presentation.utils.VehicleType
import com.toyota.oneapp.ui.flutter.GO_TO_EV_ACTIVE_CHARGE_SESSION_PAGE
import com.toyota.oneapp.ui.flutter.GO_TO_EV_CHARGE_INFO_PAGE
import com.toyota.oneapp.ui.flutter.GO_TO_EV_CHARGE_MANAGEMENT_PAGE
import com.toyota.oneapp.ui.flutter.GO_TO_FIND_STATIONS
import toyotaone.commonlib.log.LogTool

@Composable
fun iconColor(
    isBatteryLow: Boolean,
    buttonType: ButtonType,
): Color =
    when {
        buttonType == ButtonType.UNPLUG -> AppTheme.colors.primary01
        isBatteryLow -> AppTheme.colors.primary01
        else -> AppTheme.colors.secondary01
    }

@Composable
fun eVFuelGaugeIconColor(chargeLevel: Int): Color = if (chargeLevel <= 30) AppTheme.colors.primary01 else AppTheme.colors.button03d

@Composable
fun pHEVFuelGaugeIconColor(chargeLevel: Int): Color = if (chargeLevel <= 30) AppTheme.colors.primary01 else AppTheme.colors.secondary01

@Composable
fun backgroundColor(
    isBatteryLow: Boolean,
    buttonType: ButtonType,
): Color =
    when {
        buttonType == ButtonType.UNPLUG -> AppTheme.colors.primary02
        isBatteryLow -> AppTheme.colors.primary02
        else -> AppTheme.colors.secondary02
    }

@Composable
fun PlugStatus(
    navHostController: NavHostController,
    buttonType: ButtonType,
    isBatteryLow: Boolean,
    wattTimeEnabled: Boolean = false,
    vehicleType: VehicleType,
) {
    when (buttonType.name) {
        ButtonType.UNKNOWN_CHARGING.name,
        ButtonType.PARTNER_CHARGING.name,
        -> {
            PlugStatusView(
                navHostController = navHostController,
                model =
                    PlugStatusUIModel(
                        title = R.string.ev_charging,
                        color = backgroundColor(isBatteryLow, buttonType),
                        iconColor = iconColor(isBatteryLow, buttonType),
                        onClickCallback = navigateBasedOnButton(buttonType),
                        accessibilityID = AccessibilityId.ID_FUEL_VIEW_CHARGING_CTA,
                        isWattTimeEnabled = wattTimeEnabled,
                        shouldStartCharging = false,
                    ),
            )
        }

        ButtonType.UNPLUG.name -> {
            PlugStatusView(
                navHostController = navHostController,
                model =
                    PlugStatusUIModel(
                        title = R.string.charge_unplug,
                        color = backgroundColor(isBatteryLow, buttonType),
                        iconColor = iconColor(isBatteryLow, buttonType),
                        onClickCallback = navigateBasedOnButton(buttonType),
                        accessibilityID = AccessibilityId.ID_FUEL_VIEW_UNPLUG_CTA,
                        isWattTimeEnabled = wattTimeEnabled,
                        shouldStartCharging = false,
                    ),
            )
        }

        ButtonType.PARTNER_DETAILS.name -> {
            PlugStatusView(
                navHostController = navHostController,
                model =
                    PlugStatusUIModel(
                        title = R.string.charge_details,
                        color = backgroundColor(isBatteryLow, buttonType),
                        iconColor = iconColor(isBatteryLow, buttonType),
                        onClickCallback = navigateBasedOnButton(buttonType),
                        accessibilityID = AccessibilityId.ID_FUEL_VIEW_CHARGE_DETAIL_CTA,
                        isWattTimeEnabled = wattTimeEnabled,
                        shouldStartCharging = false,
                    ),
            )
        }

        ButtonType.CHARGE_NOW.name -> {
            PlugStatusView(
                navHostController = navHostController,
                model =
                    PlugStatusUIModel(
                        title = R.string.charge_now,
                        color = backgroundColor(isBatteryLow, buttonType),
                        iconColor = iconColor(isBatteryLow, buttonType),
                        onClickCallback = navigateBasedOnButton(buttonType),
                        accessibilityID = AccessibilityId.ID_FUEL_VIEW_CHARGE_NOW_CTA,
                        isWattTimeEnabled = wattTimeEnabled,
                        shouldStartCharging = true,
                    ),
            )
        }

        ButtonType.FIND_STATIONS.name -> {
            PlugStatusView(
                navHostController = navHostController,
                model =
                    PlugStatusUIModel(
                        title = R.string.find_stations,
                        color = backgroundColor(isBatteryLow, buttonType),
                        iconColor = iconColor(isBatteryLow, buttonType),
                        onClickCallback = navigateBasedOnButton(buttonType),
                        accessibilityID = AccessibilityId.ID_FUEL_VIEW_FIND_STATIONS_CTA,
                        isWattTimeEnabled = wattTimeEnabled,
                        shouldStartCharging = false,
                    ),
            )
        }

        else -> {}
    }
}

fun navigateBasedOnButton(buttonType: ButtonType): String =
    when (buttonType) {
        ButtonType.PARTNER_DETAILS,
        ButtonType.UNPLUG,
        -> GO_TO_EV_CHARGE_MANAGEMENT_PAGE
        ButtonType.FIND_STATIONS -> GO_TO_FIND_STATIONS
        ButtonType.PARTNER_CHARGING -> GO_TO_EV_ACTIVE_CHARGE_SESSION_PAGE
        ButtonType.UNKNOWN_CHARGING,
        ButtonType.CHARGE_NOW,
        -> GO_TO_EV_CHARGE_INFO_PAGE
    }

@Composable
fun getAddressCoordinates(searchItem: String): Location? {
    val context = LocalContext.current
    var location: Location? = null
    try {
        val geocoder = Geocoder(context)
        val addresses: List<Address> = geocoder.getFromLocationName(searchItem, 1) as List<Address>
        if (addresses.isNotEmpty()) {
            val address: Address = addresses.first()
            location =
                Location("").apply {
                    latitude = address.latitude
                    longitude = address.longitude
                }
        }
    } catch (e: Exception) {
        LogTool.d("FuelWidget", "Error: getAddressCoordinates $e")
    }
    return location
}
