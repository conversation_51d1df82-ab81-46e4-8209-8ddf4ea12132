/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.fuelwidget.presentation

import android.location.Location
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.core.commonapicalls.application.ElectricStatusState
import com.toyota.oneapp.features.dashboard.dashboard.application.TelemetryState
import com.toyota.oneapp.features.dashboard.dashboard.domain.FetchData
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.features.dashboard.dashboard.domain.model.Telemetry
import com.toyota.oneapp.features.fuelwidget.application.FuelState
import com.toyota.oneapp.features.fuelwidget.application.FuelWidgetUseCase
import com.toyota.oneapp.features.fuelwidget.application.VehicleFuel
import com.toyota.oneapp.features.fuelwidget.presentation.utils.ChargeStatus
import com.toyota.oneapp.features.fuelwidget.presentation.utils.FuelWidgetConstants
import com.toyota.oneapp.features.fuelwidget.presentation.utils.VehicleType
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@HiltViewModel
class FuelWidgetViewModel
    @Inject
    constructor(
        private val fuelWidgetUseCase: FuelWidgetUseCase,
        private val dispatcherProvider: DispatcherProvider,
        val applicationData: ApplicationData,
        val analyticsLogger: AnalyticsLogger,
        private val sharedDataSource: SharedDataSource,
    ) : BaseViewModel() {
        private val refreshInterval: Long = 180
        private val interval30: Long = 30
        private val interval120: Long = 120
        private var pollInterval: Long = 0

        private val _state = MutableStateFlow<FuelState>(FuelState.Loading)
        val state = _state.asStateFlow()

        var realtimeStatusExecutor: ScheduledExecutorService? = null
        private var electricStatusExecutor: ScheduledExecutorService? = null
        private var loadingTimeoutJob: Job? = null

        private var telemetryPayload: Telemetry? = null
        private var vehicleInfoData: VehicleInfo? = null

        private val _homeLocation = mutableStateOf<Location?>(null)
        val homeLocation: State<Location?> = _homeLocation

        private fun startLoadingTimeout() {
            loadingTimeoutJob?.cancel()
            loadingTimeoutJob =
                viewModelScope.launch {
                    delay(10000) // 10 second timeout
                    if (_state.value == FuelState.Loading) {
                        _state.value = FuelState.NotAvailable
                    }
                }
        }

        fun updateHomeLocation(homeLocation: Location?) {
            _homeLocation.value = homeLocation
        }

        fun isEVPublicChargingEnabled(): Boolean =
            applicationData.getSelectedVehicle()?.isFeatureEnabled(
                Feature.EV_PUBLIC_CHARGING_CONTROL,
            ) ?: false

        fun isWattTimeEnabled(): Boolean =
            applicationData.getSelectedVehicle()?.isFeatureEnabled(
                Feature.EV_WATT_TIME,
            ) ?: false

        init {
            viewModelScope.launch {
                sharedDataSource.getTelemetryState().collect { telemetryState ->
                    when (telemetryState) {
                        is TelemetryState.Loading -> {
                            _state.value = FuelState.Loading
                            startLoadingTimeout()
                        }

                        is TelemetryState.Success -> {
                            stopActiveScheduleTask()
                            telemetryState.telemetryPayload?.let {
                                applicationData.getSelectedVehicle()?.let { vehicleInfo ->
                                    _state.value = FuelState.Loading
                                    startLoadingTimeout()
                                    getVehicleType(vehicleInfo, it)
                                    telemetryPayload = it
                                    vehicleInfoData = vehicleInfo
                                }
                            }
                        }

                        is TelemetryState.Error, TelemetryState.NotAvailable -> {
                            stopActiveScheduleTask()
                            telemetryPayload = null
                            applicationData.getSelectedVehicle()?.let { vehicleInfo ->
                                vehicleInfoData = vehicleInfo
                            }
                            _state.value = FuelState.NotAvailable
                        }

                        is TelemetryState.Idle -> {
                            stopActiveScheduleTask()
                            _state.value = FuelState.Loading
                        }
                    }
                }
            }
        }

        fun getVehicleType(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                fuelWidgetUseCase.checkVehicleType(vehicleInfo).collect {
                    dispatcherProvider.io()
                    when (it) {
                        VehicleType.EVVehicle -> isEVorPHEV(it, vehicleInfo, telemetry)
                        VehicleType.PHEVVehicle -> isEVorPHEV(it, vehicleInfo, telemetry)
                        VehicleType.NonConnectedVehicle -> notConnectedVehicle()
                        else -> updateGasState(vehicleInfo, telemetry)
                    }
                }
            }
        }

        private fun notConnectedVehicle() {
            _state.value = FuelState.NotAvailable
        }

        private suspend fun isEVorPHEV(
            vehicleType: VehicleType,
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
        ) {
            applicationData.threeDShortActionStatus.collect {
                if (it) {
                    getNonRealTimeElectricStatus(vehicleInfo, telemetry)
                } else {
                    when (vehicleType) {
                        VehicleType.EVVehicle -> updateEVState(vehicleInfo, telemetry, vehicleType)
                        VehicleType.PHEVVehicle -> updatePHEVState(vehicleInfo, telemetry, vehicleType)
                        else -> {
                            // Do nothing.
                        }
                    }
                }
            }
        }

        private fun getNonRealTimeElectricStatus(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                loadElectricStatusData(vehicleInfo, telemetry)
            }
            fetchData(vehicleInfo, telemetry)
        }

        private suspend fun loadElectricStatusData(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
        ) {
            if (sharedDataSource.getElectricStatusState().value != ElectricStatusState.Loading) {
                sharedDataSource.setElectricStatusState(ElectricStatusState.Loading)
                fuelWidgetUseCase
                    .fetchElectricVehicleStatus(vehicleInfo = vehicleInfo)
                    .flowOn(dispatcherProvider.io())
                    .collect {
                        sharedDataSource.setElectricStatusState(it)
                        if (it is ElectricStatusState.Success) {
                            fuelWidgetUseCase
                                .fetchNonRealTimeElectricStatus(
                                    electricStatusResponse = it.response,
                                    vehicleInfo = vehicleInfo,
                                    telemetry = telemetry,
                                ).flowOn(dispatcherProvider.io())
                                .collect {
                                    _state.value = it
                                }
                        }
                    }
            }
        }

        private fun fetchData(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                sharedDataSource.fetchDataState.collect {
                    when (it) {
                        FetchData.ELECTRIC_STATUS -> {
                            loadElectricStatusData(vehicleInfo, telemetry)
                        }

                        else -> {
                            // Do nothing
                        }
                    }
                }
            }
        }

        fun updateEVState(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
            vehicleType: VehicleType,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                fuelWidgetUseCase
                    .postRealTimeStatus(vehicleInfo)
                    .flowOn(dispatcherProvider.io())
                    .collect { appRequestNo ->
                        appRequestNo?.let {
                            schedule30SecElectricStatus(
                                vehicleInfo = vehicleInfo,
                                telemetry = telemetry,
                                appRequestNo = it,
                                vehicleType = vehicleType,
                            )
                        }
                    }
            }
        }

        fun updatePHEVState(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
            vehicleType: VehicleType,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                fuelWidgetUseCase
                    .postRealTimeStatus(vehicleInfo)
                    .flowOn(dispatcherProvider.io())
                    .collect { appRequestNo ->
                        appRequestNo?.let {
                            schedule30SecElectricStatus(
                                vehicleInfo = vehicleInfo,
                                telemetry = telemetry,
                                appRequestNo = it,
                                vehicleType = vehicleType,
                            )
                        }
                    }
            }
        }

        private fun schedule30SecElectricStatus(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
            appRequestNo: String,
            vehicleType: VehicleType,
        ) {
            if (electricStatusExecutor == null ||
                electricStatusExecutor?.isShutdown == true ||
                electricStatusExecutor?.isTerminated == true
            ) {
                pollInterval = 0
                electricStatusExecutor = Executors.newSingleThreadScheduledExecutor()
                electricStatusExecutor?.scheduleWithFixedDelay({
                    if (pollInterval < interval120) {
                        getElectricStatus(
                            vehicleInfo = vehicleInfo,
                            telemetry = telemetry,
                            appRequestNo = appRequestNo,
                            vehicleType = vehicleType,
                        )
                        pollInterval += interval30
                    } else {
                        stopElectricStatusPolling()
                    }
                }, 0, interval30, TimeUnit.SECONDS)
            }
        }

        private fun getElectricStatus(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
            appRequestNo: String,
            vehicleType: VehicleType,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                if (vehicleType == VehicleType.PHEVVehicle) {
                    fuelWidgetUseCase
                        .updatePHEVState(
                            vehicleInfo = vehicleInfo,
                            telemetry = telemetry,
                            appRequestNo = appRequestNo,
                            forceRestart = {
                                stopActiveScheduleTask()
                                updatePHEVState(
                                    vehicleInfo = vehicleInfo,
                                    telemetry = telemetry,
                                    vehicleType = vehicleType,
                                )
                            },
                        ).flowOn(dispatcherProvider.io())
                        .collect {
                            _state.value = it
                            if (it is FuelState.Success && it.realtimeStatus) {
                                sharedDataSource.setElectricStatusState(
                                    ElectricStatusState.Success(
                                        (it.vehicleFuel as VehicleFuel.Phev).phevState.electricStatusResponse,
                                    ),
                                )
                                stopElectricStatusPolling()
                            }
                        }
                } else {
                    fuelWidgetUseCase
                        .updateEVState(
                            vehicleInfo = vehicleInfo,
                            telemetry = telemetry,
                            appRequestNo = appRequestNo,
                            homeLocation = homeLocation.value,
                            forceRestart = {
                                stopActiveScheduleTask()
                                updateEVState(
                                    vehicleInfo = vehicleInfo,
                                    telemetry = telemetry,
                                    vehicleType = vehicleType,
                                )
                            },
                        ).flowOn(dispatcherProvider.io())
                        .collect {
                            _state.value = it
                            if (it is FuelState.Success && it.realtimeStatus) {
                                sharedDataSource.setElectricStatusState(
                                    ElectricStatusState.Success(
                                        (it.vehicleFuel as VehicleFuel.Ev).evState.electricStatusResponse,
                                    ),
                                )
                                stopElectricStatusPolling()
                            }
                        }
                }
            }
        }

        fun stopActiveScheduleTask() {
            realtimeStatusExecutor?.shutdownNow()
            electricStatusExecutor?.shutdownNow()
            realtimeStatusExecutor = null
            electricStatusExecutor = null
        }

        private fun stopElectricStatusPolling() {
            electricStatusExecutor?.shutdown()
            electricStatusExecutor = null
        }

        fun updateGasState(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                fuelWidgetUseCase
                    .updateGasState(vehicleInfo, telemetry)
                    .flowOn(
                        dispatcherProvider.io(),
                    ).collect {
                        _state.value = it
                    }
            }
        }

        fun getVehicleTypeForEmptyState(): VehicleType {
            return vehicleInfoData?.let { vehicleInfo ->
                when {
                    vehicleInfo.isEvVehicle &&
                        vehicleInfo.fuelType?.equals(
                            FuelWidgetConstants.FUEL_TYPE_PLUGIN_HYBRID,
                            true,
                        ) == true -> VehicleType.PHEVVehicle
                    vehicleInfo.isEvVehicle -> VehicleType.EVVehicle
                    else -> VehicleType.GasVehicle
                }
            } ?: VehicleType.GasVehicle // Default to gas vehicle if no vehicle info
        }

        fun logEventWithParameter(event: String) {
            analyticsLogger.logEventWithParameter(AnalyticsEvent.DASHBOARD_FUEL_WIDGET.eventName, event)
        }

        fun getCurrentChargingStatus(): ChargeStatus? =
            when (val currentState = state.value) {
                is FuelState.Success -> {
                    when (val fuelType = currentState.vehicleFuel) {
                        is VehicleFuel.Ev -> {
                            if (fuelType.evState.isCharging) {
                                ChargeStatus.NORMAL_CHARGING
                            } else {
                                null
                            }
                        }
                        is VehicleFuel.Phev -> {
                            if (fuelType.phevState.isCharging) {
                                ChargeStatus.NORMAL_CHARGING
                            } else {
                                null
                            }
                        }
                        else -> null
                    }
                }
                else -> null
            }

        fun getCurrentChargingId(): String? =
            when (val currentState = state.value) {
                is FuelState.Success -> {
                    when (val fuelType = currentState.vehicleFuel) {
                        is VehicleFuel.Ev -> {
                            if (fuelType.evState.isCharging) {
                                fuelType.evState.electricStatusResponse
                                    ?.payload
                                    ?.vehicleInfo
                                    ?.timerChargeInfo
                                    ?.firstOrNull()
                                    ?.settingId
                            } else {
                                null
                            }
                        }
                        is VehicleFuel.Phev -> {
                            if (fuelType.phevState.isCharging) {
                                fuelType.phevState.electricStatusResponse
                                    ?.payload
                                    ?.vehicleInfo
                                    ?.timerChargeInfo
                                    ?.firstOrNull()
                                    ?.settingId
                            } else {
                                null
                            }
                        }
                        else -> null
                    }
                }
                else -> null
            }

        fun onResume() {
            if (_state.value != FuelState.Loading) {
                _state.value = FuelState.Loading
                startLoadingTimeout()
                vehicleInfoData.let { vehicleInfo ->
                    if (vehicleInfo != null) {
                        telemetryPayload.let { telemetry ->
                            if (telemetry != null) {
                                stopActiveScheduleTask()
                                getVehicleType(vehicleInfo, telemetry)
                            } else {
                                _state.value = FuelState.NotAvailable
                            }
                        }
                    } else {
                        _state.value = FuelState.NotAvailable
                    }
                }
            }
        }

        override fun onCleared() {
            super.onCleared()
            loadingTimeoutJob?.cancel()
            stopActiveScheduleTask()
        }
    }
