package com.toyota.oneapp.features.fuelwidget.application

import android.location.Location
import android.location.LocationManager
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.commonapicalls.application.ElectricStatusState
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.repository.CommonApiDefaultRepository
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoAddress
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoCustomer
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.toUIModel
import com.toyota.oneapp.features.core.commonapicalls.domain.model.ElectricStatusModelResponse
import com.toyota.oneapp.features.core.commonapicalls.domain.model.isCharging
import com.toyota.oneapp.features.core.commonapicalls.domain.model.isPlugWaitingForTimerToCharge
import com.toyota.oneapp.features.core.util.DateTimeUtil
import com.toyota.oneapp.features.core.util.EvFormatterUtil
import com.toyota.oneapp.features.dashboard.dashboard.domain.model.Telemetry
import com.toyota.oneapp.features.fuelwidget.presentation.utils.ButtonType
import com.toyota.oneapp.features.fuelwidget.presentation.utils.FuelWidgetConstants
import com.toyota.oneapp.features.fuelwidget.presentation.utils.VehiclePlugStatus
import com.toyota.oneapp.features.fuelwidget.presentation.utils.VehicleType
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.NetworkStatus
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.CorrelationIdProvider
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import javax.inject.Inject

class FuelWidgetLogic
    @Inject
    constructor(
        private val repository: CommonApiDefaultRepository,
        private val preferenceModel: OneAppPreferenceModel,
        private val correlationIdProvider: CorrelationIdProvider,
    ) : FuelWidgetUseCase() {
        private var getHomeAddressCoordinates = ""
        private var isCloseToHome = false
        private var homeAddressLocation: Location? = null
        private var currentChargeSessionActive = false

        override fun fetchElectricVehicleStatus(vehicleInfo: VehicleInfo): Flow<ElectricStatusState> =
            flow {
                if (vehicleInfo.isFeatureEnabled(Feature.TELEMETRY) && vehicleInfo.isConnectedVehicle && vehicleInfo.fuelType != null) {
                    val response =
                        repository.fetchChargeManagementDetail(
                            vin = vehicleInfo.vin,
                            generation = vehicleInfo.generation,
                            brand = vehicleInfo.brand,
                        )
                    if (response is Resource.Success) {
                        emit(ElectricStatusState.Success(response.data))
                    } else {
                        emit(ElectricStatusState.Error(response.responseCode, response.message))
                    }
                } else {
                    emit(ElectricStatusState.Idle)
                }
            }

        override fun getElectricRealTimeVehicleStatus(
            vehicleInfo: VehicleInfo,
            appRequestNo: String,
        ): Flow<ElectricStatusModelResponse?> =
            flow {
                val response =
                    repository.getEvRealTimeStatus(
                        vin = vehicleInfo.vin,
                        generation = vehicleInfo.generation,
                        brand = vehicleInfo.brand,
                        appRequestNo = appRequestNo,
                    )
                response.let {
                    val isAPISuccess = it.status == NetworkStatus.SUCCESS && it.data != null
                    val realtimeResultsStatusStatus =
                        it.data
                            ?.payload
                            ?.realtimeStatusResult
                            ?.status
                    val realtimeStatusResultStatus =
                        it.data
                            ?.payload
                            ?.realtimeStatusResult
                            ?.result
                    val realtimeStatusIsNull = realtimeResultsStatusStatus == null
                    val fuelWidgetStatusInProgress = FuelWidgetConstants.REAL_TIME_STATUS_IN_PROGRESS
                    val fuelWidgetStatusSuccess = FuelWidgetConstants.REAL_TIME_STATUS_SUCCESS
                    val fuelWidgetStatusSystemError = FuelWidgetConstants.REAL_TIME_STATUS_SYSTEM_ERROR
                    val fuelWidgetStatusDCMError = FuelWidgetConstants.REAL_TIME_STATUS_DCM_ERROR
                    val fuelWidgetStatusTimeout = FuelWidgetConstants.REAL_TIME_STATUS_TIMEOUT
                    val isStatusNotSuccess = realtimeResultsStatusStatus == fuelWidgetStatusInProgress || realtimeStatusIsNull
                    val realtimeStatusResultIsNull = realtimeStatusResultStatus == null
                    val isResultNotSuccess = realtimeStatusResultStatus == fuelWidgetStatusInProgress || realtimeStatusResultIsNull
                    val isTimeOut = realtimeStatusResultStatus == fuelWidgetStatusTimeout || realtimeStatusResultStatus == null
                    val isSystemError = realtimeStatusResultStatus == fuelWidgetStatusSystemError || realtimeStatusResultStatus == null
                    val isDCMError = realtimeStatusResultStatus == fuelWidgetStatusDCMError || realtimeStatusResultStatus == null

                    when {
                        isAPISuccess &&
                            realtimeResultsStatusStatus == fuelWidgetStatusSuccess &&
                            realtimeStatusResultStatus == fuelWidgetStatusSuccess -> {
                            it.data?.toUIModel()?.let { uiModel ->
                                emit(uiModel)
                            }
                        }
                        isAPISuccess &&
                            !isStatusNotSuccess &&
                            isResultNotSuccess ||
                            isAPISuccess &&
                            isStatusNotSuccess &&
                            isResultNotSuccess -> {
                            it.data?.toUIModel(realTimeData = false)?.let { uiModel ->
                                emit(uiModel)
                            }
                        }
                        !isStatusNotSuccess && (isTimeOut || isSystemError || isDCMError) -> {
                            it.data?.toUIModel(realTimeData = false, forceRefresh = true)?.let { uiModel ->
                                emit(uiModel)
                            }
                        }
                        else -> {
                            emit(null)
                        }
                    }
                }
            }

        override fun postRealTimeStatus(vehicleInfo: VehicleInfo): Flow<String?> =
            flow {
                if (vehicleInfo.isFeatureEnabled(Feature.TELEMETRY) && vehicleInfo.isConnectedVehicle && vehicleInfo.fuelType != null) {
                    val response =
                        repository.postEvRealTimeStatus(
                            requestId = correlationIdProvider.get(),
                            vin = vehicleInfo.vin,
                            brand = vehicleInfo.brand,
                            generation = vehicleInfo.generation,
                            deviceId = preferenceModel.getDeviceToken(),
                        )
                    emit(response.data?.payload?.appRequestNo)
                } else {
                    emit(null)
                }
            }

        override fun checkVehicleType(vehicleInfo: VehicleInfo): Flow<VehicleType> =
            flow {
                val hasData = vehicleInfo.isEvVehicle && vehicleInfo.fuelType != null
                val isPHEV = isPHEV(vehicleInfo)
                val isNonConnectedVehicle = vehicleInfo.isNonCvtVehicle
                val isEv =
                    hasData &&
                        FuelWidgetConstants.FUEL_TYPE_PURE_ELECTRIC.equals(
                            vehicleInfo.fuelType,
                            true,
                        )

                when {
                    isEv -> {
                        emit(VehicleType.EVVehicle)
                    }
                    isPHEV -> {
                        emit(VehicleType.PHEVVehicle)
                    }
                    isNonConnectedVehicle -> {
                        emit(VehicleType.NonConnectedVehicle)
                    }
                    else -> {
                        emit(VehicleType.GasVehicle)
                    }
                }
            }

        override fun fetchNonRealTimeElectricStatus(
            electricStatusResponse: ElectricStatusResponse?,
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
        ): Flow<FuelState> =
            flow {
                val uiModel = electricStatusResponse?.toUIModel(false)
                uiModel.let {
                    if (it != null) {
                        val phevState =
                            constructPHEVState(
                                vehicleInfo = vehicleInfo,
                                telemetry = telemetry,
                                modelResponse = it,
                            )
                        emit(
                            FuelState.Success(VehicleFuel.Phev(phevState), false),
                        )
                    } else {
                        emit(FuelState.NotAvailable)
                    }
                }
            }

        private fun isPHEV(vehicleInfo: VehicleInfo): Boolean {
            val hasData = vehicleInfo.isEvVehicle && vehicleInfo.fuelType != null
            return hasData &&
                FuelWidgetConstants.FUEL_TYPE_PLUGIN_HYBRID.equals(
                    vehicleInfo.fuelType,
                    true,
                )
        }

        private fun isChargeInfoLayoutVisible(vehicleInfo: VehicleInfo): Boolean {
            val hasData = vehicleInfo.isEvVehicle && vehicleInfo.fuelType != null
            val isEv =
                hasData &&
                    FuelWidgetConstants.FUEL_TYPE_PURE_ELECTRIC.equals(
                        vehicleInfo.fuelType,
                        true,
                    )
            val isPHEV =
                hasData &&
                    FuelWidgetConstants.FUEL_TYPE_PLUGIN_HYBRID.equals(
                        vehicleInfo.fuelType,
                        true,
                    )
            val isMirai = vehicleInfo.modelName?.contains(FuelWidgetConstants.MIRAI, true) == true
            return if ((isEv || isPHEV) && !isMirai) {
                (true)
            } else {
                (false)
            }
        }

        private fun isRemoteStatus(vehicleInfo: VehicleInfo): Int {
            val remoteStatus = vehicleInfo.remoteDisplay
            val remoteStatusActivated = FuelWidgetConstants.REMOTE_STATUS_ACTIVATED
            val remoteStatusPending = FuelWidgetConstants.REMOTE_STATUS_PENDING
            val remoteStatusFailed = FuelWidgetConstants.REMOTE_STATUS_FAILED
            val remoteStatusAuthRequired = FuelWidgetConstants.REMOTE_STATUS_AUTH_REQUIRED

            return if (remoteStatus in
                listOf(
                    remoteStatusActivated,
                    remoteStatusPending,
                    remoteStatusFailed,
                    remoteStatusAuthRequired,
                )
            ) {
                (remoteStatus)
            } else {
                (FuelWidgetConstants.REMOTE_STATUS_HIDE)
            }
        }

        private fun isHybridElectricFuelType(vehicleInfo: VehicleInfo): Boolean {
            val hasData = vehicleInfo.fuelType != null
            val isEVRemoteCapable = vehicleInfo.hasEvRemoteCapability()
            val isCY17 = vehicleInfo.isCY17
            val isHybridElectricFuelType =
                hasData &&
                    FuelWidgetConstants.FUEL_TYPE_HYDROGEN_FUELCELL.equals(
                        vehicleInfo.fuelType,
                        true,
                    ) ||
                    FuelWidgetConstants.FUEL_TYPE_PLUGIN_HYBRID.equals(vehicleInfo.fuelType, true)
            return isEVRemoteCapable || (isHybridElectricFuelType && !isCY17)
        }

        private fun evBatteryLow(
            vehicleInfo: VehicleInfo,
            chargeRemaining: Long?,
        ): Boolean {
            val remoteStatus = isRemoteStatus(vehicleInfo)
            return remoteStatus == FuelWidgetConstants.REMOTE_STATUS_ACTIVATED && chargeRemaining != null && chargeRemaining <= 30
        }

        private fun isEVFindStationsEnabled(vehicleInfo: VehicleInfo): Boolean = vehicleInfo.isFeatureEnabled(Feature.EV_CHARGE_STATION)

        private suspend fun getEVOdometerButtonType(
            chargeInfo: ElectricStatusModelResponse?,
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
            homeLocation: Location?,
        ): ButtonType {
            val currentButtonType: ButtonType

            currentChargeSessionActive = preferenceModel.getStoreChargeSessionId() != ToyotaConstants.EMPTY_STRING
            isCloseToHome = checkIfCustomerIsCloseToHome(homeLocation, telemetry)

            if (chargeInfo != null) {
                currentButtonType =
                    if (currentChargeSessionActive) {
                        when {
                            isCloseToHome && chargeInfo.isCharging() -> ButtonType.UNKNOWN_CHARGING
                            chargeInfo.isCharging() -> ButtonType.PARTNER_CHARGING
                            (chargeInfo.chargeRemainingAmount ?: 0) >= 100 -> ButtonType.UNPLUG
                            else -> ButtonType.PARTNER_DETAILS
                        }
                    } else {
                        when {
                            isCloseToHome && chargeInfo.isCharging() ->
                                ButtonType.UNKNOWN_CHARGING

                            chargeInfo.isCharging() ->
                                ButtonType.PARTNER_CHARGING

                            isCloseToHome &&
                                chargeInfo.isPlugWaitingForTimerToCharge() &&
                                vehicleInfo.is21MMVehicle ->
                                ButtonType.CHARGE_NOW

                            else -> ButtonType.FIND_STATIONS
                        }
                    }
            } else {
                currentButtonType = ButtonType.FIND_STATIONS
            }

            return currentButtonType
        }

        private fun getDistanceTOEmptyTitle(vehicleInfo: VehicleInfo): Int =
            if (VehicleInfo.isToyotaBrand(vehicleInfo.brand)) R.string.distance_to_empty else R.string.range

        override fun updatePHEVState(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
            appRequestNo: String,
            forceRestart: () -> Unit,
        ): Flow<FuelState> =
            flow {
                getElectricRealTimeVehicleStatus(vehicleInfo, appRequestNo).collect {
                    when {
                        it != null -> {
                            if (it.realTimeData == false && it.forceRefresh == true) {
                                forceRestart()
                            }
                            val phevState =
                                constructPHEVState(
                                    vehicleInfo = vehicleInfo,
                                    telemetry = telemetry,
                                    modelResponse = it,
                                )
                            emit(
                                FuelState.Success(VehicleFuel.Phev(phevState), it.realTimeData ?: false),
                            )
                        }
                        else -> {
                            emit(FuelState.NotAvailable)
                        }
                    }
                }
            }

        private fun constructPHEVState(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
            modelResponse: ElectricStatusModelResponse,
        ): PHEVState {
            val isHybridElectricFuelType = isHybridElectricFuelType(vehicleInfo)
            val timeRemaining =
                DateTimeUtil.getTimeFormattedDuration(
                    modelResponse.remainingChargeTime ?: 0,
                )
            val distanceToEmpty =
                telemetry.distanceToEmpty.value
                    .toString()
                    .toInt() + (modelResponse.evDistance?.toInt() ?: 0)

            val isMiraiVehicle = isMiraiVehicle(vehicleInfo)
            val fuelLevel = telemetry.fuelLevel ?: 0

            return PHEVState(
                electricStatusResponse = modelResponse.response,
                distanceToEmpty =
                    getDistanceToEmpty(
                        telemetry,
                        isHybridElectricFuelType,
                        distanceToEmpty,
                        modelResponse,
                    ),
                chargeRemaining =
                    EvFormatterUtil.formattedBatteryPercentage(
                        modelResponse.chargeRemainingAmount ?: 0,
                    ),
                plugStatus = VehiclePlugStatus.FindStations,
                isPluggedIn = modelResponse.isPlugWaitingForTimerToCharge(),
                fuelLevel = fuelLevel,
                isCharging = modelResponse.isCharging(),
                isFuelGaugeVisible = isFuelGaugeEnabled(vehicleInfo),
                isRangeVisible = isRangeVisible(vehicleInfo),
                isChargeInfoLayoutVisible = isChargeInfoLayoutVisible(vehicleInfo),
                isRemoteStatus = isRemoteStatus(vehicleInfo),
                estimatedTime = timeRemaining,
                evBatteryLow =
                    evBatteryLow(
                        vehicleInfo,
                        modelResponse.chargeRemainingAmount,
                    ),
                displayDistanceToEmpty = true,
                evOdometerButtonType = ButtonType.FIND_STATIONS,
                isHybridElectricFuelType = isHybridElectricFuelType,
                fuelRangeIcon = if (isMiraiVehicle) R.drawable.ic_h2_fuel_gauge else R.drawable.ic_fuel_stroke,
                distanceToEmptyTitle = getDistanceTOEmptyTitle(vehicleInfo),
                fuelGaugeProgressValue = (fuelLevel.toFloat()) / (100.toFloat()),
                isWattTimeEnabled = isWattTimeEnabled(vehicleInfo),
                chargePercentage = modelResponse.chargeRemainingAmount?.toInt() ?: 0,
            )
        }

        override fun updateEVState(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
            homeLocation: Location?,
            appRequestNo: String,
            forceRestart: () -> Unit,
        ): Flow<FuelState> =
            flow {
                getElectricRealTimeVehicleStatus(vehicleInfo, appRequestNo).collect {
                    when {
                        it != null -> {
                            if (it.realTimeData == false && it.forceRefresh == true) {
                                forceRestart()
                            }
                            if (homeLocation == null) {
                                setHomeAddressCoordinates(vehicleInfo)
                            }
                            val timeRemaining =
                                DateTimeUtil.getTimeFormattedDuration(
                                    it.remainingChargeTime ?: 0,
                                )
                            val chargeRemaining =
                                EvFormatterUtil
                                    .formattedBatteryPercentage(
                                        it.chargeRemainingAmount ?: 0,
                                    ).toInt()
                            val isPluggedIn = it.isPlugWaitingForTimerToCharge()
                            val isCharging = it.isCharging()
                            val chargeLevel = it.chargeRemainingAmount?.toInt() ?: 0
                            val evState =
                                EVState(
                                    electricStatusResponse = it.response,
                                    estimateDistance =
                                        UnitValue(
                                            value = it.evDistanceAC?.toInt() ?: "${FuelWidgetConstants.ZERO}",
                                            unit = it.evDistanceUnit,
                                        ),
                                    chargeLevel = chargeLevel,
                                    isBatteryLow = (it.chargeRemainingAmount?.toInt() ?: 0) <= 30,
                                    isFuelGaugeVisible = isFuelGaugeEnabled(vehicleInfo),
                                    isRangeVisible = isRangeVisible(vehicleInfo),
                                    isEVChargeStationEnabled = isEVFindStationsEnabled(vehicleInfo),
                                    evOdometerButtonType =
                                        getEVOdometerButtonType(
                                            it,
                                            vehicleInfo,
                                            telemetry,
                                            homeLocation,
                                        ),
                                    isPluggedIn = isPluggedIn,
                                    isCharging = isCharging,
                                    chargeRemaining = chargeRemaining,
                                    chargePercentage = chargeRemaining,
                                    estimatedTime = timeRemaining,
                                    fuelGaugeProgressValue = chargeLevel.toFloat() / 100.toFloat(),
                                    isWattTimeEnabled = isWattTimeEnabled(vehicleInfo),
                                    getHomeAddressCoordinates = getHomeAddressCoordinates,
                                    isClosedToHome = isCloseToHome,
                                    homeAddressLocation = homeAddressLocation,
                                )

                            emit(FuelState.Success(VehicleFuel.Ev(evState), it.realTimeData ?: false))
                        }
                        else -> {
                            val evBlankState =
                                EVState(
                                    electricStatusResponse = null,
                                    estimateDistance =
                                        UnitValue(
                                            value = FuelWidgetConstants.STRING_ZERO,
                                            unit = FuelWidgetConstants.DEFAULT_UNIT,
                                        ),
                                    chargeLevel = 0,
                                    isBatteryLow = true,
                                    isFuelGaugeVisible = isFuelGaugeEnabled(vehicleInfo),
                                    isRangeVisible = isRangeVisible(vehicleInfo),
                                    isEVChargeStationEnabled = isEVFindStationsEnabled(vehicleInfo),
                                    evOdometerButtonType =
                                        getEVOdometerButtonType(
                                            null,
                                            vehicleInfo,
                                            telemetry,
                                            homeLocation,
                                        ),
                                    isPluggedIn = false,
                                    isCharging = false,
                                    chargeRemaining = null,
                                    estimatedTime = FuelWidgetConstants.EMPTY_VALUE,
                                    chargePercentage = 0,
                                    fuelGaugeProgressValue = 0F,
                                    isWattTimeEnabled = isWattTimeEnabled(vehicleInfo),
                                    getHomeAddressCoordinates = getHomeAddressCoordinates,
                                    isClosedToHome = isCloseToHome,
                                    homeAddressLocation = homeAddressLocation,
                                )
                            emit(FuelState.Success(VehicleFuel.Ev(evBlankState), false))
                        }
                    }
                }
            }

        private suspend fun setHomeAddressCoordinates(vehicleInfo: VehicleInfo) {
            getHomeAddressCoordinates(vehicleInfo).collect { homeLocation ->
                if (homeLocation != null) {
                    getHomeAddressCoordinates = homeLocation
                }
            }
        }

        override fun updateGasState(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry,
        ): Flow<FuelState> =
            flow {
                val isMiraiVehicle = isMiraiVehicle(vehicleInfo)
                val fuelLevel = telemetry.fuelLevel ?: 0
                val distanceToEmptyValue = telemetry.distanceToEmpty.value
                val fuelWidgetConstantsZero = FuelWidgetConstants.STRING_ZERO
                val fuelWidgetConstantsEmpty = FuelWidgetConstants.EMPTY_VALUE
                val distanceToEmpty =
                    if (distanceToEmptyValue !=
                        fuelWidgetConstantsZero
                    ) {
                        distanceToEmptyValue
                    } else {
                        fuelWidgetConstantsEmpty
                    }
                val gasState =
                    GasVehicleState(
                        distanceToEmpty =
                            UnitValue(
                                value = distanceToEmpty,
                                unit = telemetry.distanceToEmpty.unit,
                            ),
                        fuelLevel = fuelLevel,
                        fuelType = vehicleInfo.fuelType,
                        isFuelGaugeVisible = isFuelGaugeEnabled(vehicleInfo),
                        isRangeVisible = isRangeVisible(vehicleInfo),
                        fuelRangeIcon = if (isMiraiVehicle) R.drawable.ic_h2_fuel_gauge else R.drawable.ic_fuel_stroke,
                        isDisplayDistanceToEmpty = telemetry.displayDistanceToEmpty,
                        distanceToEmptyTitle =
                            if (VehicleInfo.isToyotaBrand(
                                    vehicleInfo.brand,
                                )
                            ) {
                                R.string.distance_to_empty
                            } else {
                                R.string.range
                            },
                        fuelGaugeProgressValue = (fuelLevel.toFloat()) / (100.toFloat()),
                    )
                emit(FuelState.Success(VehicleFuel.Gas(gasState), false))
            }

        companion object {
            private fun isFuelGaugeEnabled(vehicleInfo: VehicleInfo): Boolean {
                val hasData = vehicleInfo.isEvVehicle && vehicleInfo.fuelType != null
                val isPHEV =
                    hasData &&
                        FuelWidgetConstants.FUEL_TYPE_PLUGIN_HYBRID.equals(
                            vehicleInfo.fuelType,
                            true,
                        )
                val isCY17 = vehicleInfo.isCY17
                val isNG86 = vehicleInfo.isNG86
                val isEv =
                    hasData &&
                        FuelWidgetConstants.FUEL_TYPE_PURE_ELECTRIC.equals(
                            vehicleInfo.fuelType,
                            true,
                        )
                return if (isNG86 || (isCY17 && isPHEV) || isEv) {
                    (false)
                } else {
                    (true)
                }
            }

            private fun isRangeVisible(vehicleInfo: VehicleInfo): Boolean {
                val isTelemetryEnabled = vehicleInfo.isFeatureEnabled(Feature.TELEMETRY)
                return if (isTelemetryEnabled) {
                    (true)
                } else {
                    (false)
                }
            }

            private fun isMiraiVehicle(vehicleInfo: VehicleInfo): Boolean =
                vehicleInfo.modelName?.contains(FuelWidgetConstants.MIRAI, true) == true

            private fun isWattTimeEnabled(vehicleInfo: VehicleInfo): Boolean = vehicleInfo.isFeatureEnabled(Feature.EV_WATT_TIME)
        }

        private fun getHomeAddressCoordinates(vehicleInfo: VehicleInfo): Flow<String?> =
            flow {
                val response =
                    repository.fetchProfileDetails(
                        brand = vehicleInfo.brand,
                        guid = preferenceModel.getGuid(),
                    )
                response.let {
                    when {
                        it.status == NetworkStatus.SUCCESS && it.data != null -> {
                            it.data?.payload.let { customer ->
                                if (customer != null) {
                                    val customerHomeAddress = getCustomerHomeAddress(customer.customer)
                                    if (customerHomeAddress != null) {
                                        val address =
                                            with(customerHomeAddress) {
                                                listOf(address, city, state, country).joinToString(" ")
                                            }
                                        emit(address)
                                    }
                                }
                            }
                        }
                        else -> {
                            emit(null)
                        }
                    }
                }
            }

        private fun getCustomerHomeAddress(customer: ProfileInfoCustomer): ProfileInfoAddress? {
            if (customer.addresses?.isNotEmpty() == true) {
                val address =
                    customer.addresses.firstOrNull { address ->
                        address.addressType.lowercase() == FuelWidgetConstants.HOME
                    }
                return address
            }
            return null
        }

        private suspend fun checkIfCustomerIsCloseToHome(
            homeAddressCoordinates: Location?,
            telemetry: Telemetry,
        ): Boolean {
            if (homeAddressCoordinates != null && telemetry.vehicleLocation != null) {
                homeAddressLocation = homeAddressCoordinates
                val locationDistance =
                    getDistanceBtwLatLong(
                        homeAddressCoordinates.latitude,
                        homeAddressCoordinates.longitude,
                        telemetry.vehicleLocation.latitude,
                        telemetry.vehicleLocation.longitude,
                    )
                return locationDistance <= FuelWidgetConstants.HOME_RANGE_METERS
            }
            return false
        }

        private suspend fun getDistanceBtwLatLong(
            latitudeOne: Double,
            longitudeOne: Double,
            latitudeTwo: Double,
            longitudeTwo: Double,
        ): Double {
            return withContext(Dispatchers.IO) {
                val locationOne = Location(LocationManager.GPS_PROVIDER)
                locationOne.latitude = latitudeOne
                locationOne.longitude = longitudeOne

                val locationTwo = Location(LocationManager.GPS_PROVIDER)
                locationTwo.latitude = latitudeTwo
                locationTwo.longitude = longitudeTwo

                return@withContext locationOne.distanceTo(locationTwo).toDouble()
            }
        }

        private fun getDistanceToEmpty(
            telemetry: Telemetry,
            isHybridElectricFuelType: Boolean,
            distanceToEmpty: Int,
            response: ElectricStatusModelResponse,
        ): UnitValue =
            UnitValue(
                value =
                    when {
                        isHybridElectricFuelType -> {
                            if (distanceToEmpty != 0) {
                                distanceToEmpty
                            } else {
                                FuelWidgetConstants.EMPTY_VALUE
                            }
                        }

                        else -> {
                            if (telemetry.distanceToEmpty.value != FuelWidgetConstants.STRING_ZERO) {
                                telemetry.distanceToEmpty.value
                            } else {
                                FuelWidgetConstants.EMPTY_VALUE
                            }
                        }
                    },
                unit =
                    if (isHybridElectricFuelType && response.evDistance != null) {
                        response.evDistanceUnit
                    } else {
                        telemetry.distanceToEmpty.unit
                    },
            )
    }
