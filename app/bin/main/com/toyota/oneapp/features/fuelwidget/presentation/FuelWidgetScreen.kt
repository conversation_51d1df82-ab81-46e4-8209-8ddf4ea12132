/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.fuelwidget.presentation

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.navigation.NavHostController
import com.google.gson.Gson
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.chargeinfo.navigation.ChargeInfoRoute.Companion.ARG_IS_CHARGE_NOW
import com.toyota.oneapp.features.chargemanagement.presentation.navigation.ChargeManagementRoute.Companion.NAV_ARG_IS_CHARGE_NOW
import com.toyota.oneapp.features.core.composable.BatteryIcon
import com.toyota.oneapp.features.core.composable.FuelWidgetComposableShimmer
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.composable.OACaption1TextView
import com.toyota.oneapp.features.core.composable.OACaption2TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.ContentDescriptions
import com.toyota.oneapp.features.fuelwidget.application.EVState
import com.toyota.oneapp.features.fuelwidget.application.FuelState
import com.toyota.oneapp.features.fuelwidget.application.GasVehicleState
import com.toyota.oneapp.features.fuelwidget.application.PHEVState
import com.toyota.oneapp.features.fuelwidget.application.UnitValue
import com.toyota.oneapp.features.fuelwidget.application.VehicleFuel
import com.toyota.oneapp.features.fuelwidget.presentation.utils.ButtonType
import com.toyota.oneapp.features.fuelwidget.presentation.utils.ChargeStatus
import com.toyota.oneapp.features.fuelwidget.presentation.utils.FuelWidgetConstants
import com.toyota.oneapp.features.fuelwidget.presentation.utils.FuelWidgetConstants.EMPTY_VALUE
import com.toyota.oneapp.features.fuelwidget.presentation.utils.FuelWidgetConstants.REMOTE_STATUS_ACTIVATED
import com.toyota.oneapp.features.fuelwidget.presentation.utils.FuelWidgetConstants.REMOTE_STATUS_HIDE
import com.toyota.oneapp.features.fuelwidget.presentation.utils.VehiclePlugStatus
import com.toyota.oneapp.features.fuelwidget.presentation.utils.VehicleType
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeCtaStatus
import com.toyota.oneapp.features.publiccharging.domain.model.ChargePointAddress
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeRunningStatus
import com.toyota.oneapp.features.publiccharging.domain.model.ChargingStatus
import com.toyota.oneapp.features.publiccharging.domain.model.StartTimeAndEnergy
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.FROM_DASHBOARD
import com.toyota.oneapp.ui.flutter.DashboardFlutterActivity
import com.toyota.oneapp.ui.flutter.GO_TO_EV_ACTIVE_CHARGE_SESSION_PAGE
import com.toyota.oneapp.ui.flutter.GO_TO_EV_CHARGE_INFO_PAGE
import com.toyota.oneapp.ui.flutter.GO_TO_EV_CHARGE_MANAGEMENT_PAGE
import com.toyota.oneapp.ui.flutter.GO_TO_FIND_STATIONS
import com.toyota.oneapp.ui.flutter.GO_TO_VEHICLE_CHARGE_MANAGEMENT_SCREEN
import com.toyota.oneapp.util.NavigationUtil
import com.toyota.oneapp.util.ToyotaConstants

@Composable
fun FuelWidgetScreen(navHostController: NavHostController) {
    val viewModel = hiltViewModel<FuelWidgetViewModel>()
    val fuelWidgetState = viewModel.state.collectAsState()

    LifeCycleEventObserver(
        onStop = {
            viewModel.stopActiveScheduleTask()
        },
        onResume = {
            viewModel.onResume()
        },
    )
    Box(
        modifier =
            Modifier
                .background(AppTheme.colors.tertiary12)
                .padding(8.dp)
                .fillMaxWidth(),
    ) {
        FuelWidgetComposableShimmer(
            isLoading = (fuelWidgetState.value == FuelState.Loading),
            contentAfterLoading = {
                when (fuelWidgetState.value) {
                    is FuelState.Success -> {
                        when (
                            val fuelType =
                                (fuelWidgetState.value as FuelState.Success).vehicleFuel
                        ) {
                            is VehicleFuel.Gas -> {
                                GasOdometerLayout(state = fuelType.gasVehicleState)
                            }

                            is VehicleFuel.Phev -> {
                                PHEVOdometerLayout(
                                    state = fuelType.phevState,
                                    navHostController = navHostController,
                                    bodyContent = {
                                        PlugStatus(
                                            navHostController = navHostController,
                                            buttonType = fuelType.phevState.evOdometerButtonType,
                                            isBatteryLow = fuelType.phevState.evBatteryLow,
                                            wattTimeEnabled = fuelType.phevState.isWattTimeEnabled,
                                            vehicleType = VehicleType.PHEVVehicle,
                                        )
                                    },
                                )
                            }

                            is VehicleFuel.Ev -> {
                                if (fuelType.evState.getHomeAddressCoordinates.isNotEmpty()) {
                                    GetHomeAddressCoordinates(
                                        fuelType.evState.getHomeAddressCoordinates,
                                    )
                                }
                                EVOdometerLayout(
                                    state = fuelType.evState,
                                    navHostController = navHostController,
                                    bodyContent = {
                                        PlugStatus(
                                            navHostController = navHostController,
                                            isBatteryLow = fuelType.evState.isBatteryLow,
                                            buttonType = fuelType.evState.evOdometerButtonType,
                                            wattTimeEnabled = fuelType.evState.isWattTimeEnabled,
                                            vehicleType = VehicleType.EVVehicle,
                                        )
                                    },
                                )
                            }
                        }
                    }

                    else -> {
                        // Show default empty state with zero/null values based on vehicle type
                        DefaultOdometerLayoutView(
                            viewModel = viewModel,
                            navHostController = navHostController,
                        )
                    }
                }
            },
        )
    }
}

@Composable
fun DefaultOdometerLayoutView(
    viewModel: FuelWidgetViewModel,
    navHostController: NavHostController,
) {
    DefaultOdometerLayout(
        vehicleType = viewModel.getVehicleTypeForEmptyState(),
        isWatttimeTimeEnabled = viewModel.isWattTimeEnabled(),
        navHostController = navHostController,
    ) {
        GasOdometerLayout(
            state =
                GasVehicleState(
                    distanceToEmpty =
                        UnitValue(
                            FuelWidgetConstants.STRING_ZERO,
                            FuelWidgetConstants.DEFAULT_UNIT,
                        ),
                    fuelLevel = 0,
                    fuelType = null,
                    isFuelGaugeVisible = false,
                    isRangeVisible = false,
                    fuelRangeIcon = R.drawable.ic_fuel_stroke,
                    isDisplayDistanceToEmpty = false,
                    distanceToEmptyTitle = R.string.range,
                    fuelGaugeProgressValue = 0f,
                ),
        )
    }
}

@Composable
private fun DefaultOdometerLayout(
    vehicleType: VehicleType,
    isWatttimeTimeEnabled: Boolean,
    navHostController: NavHostController,
    onGasOdometerLayout: @Composable () -> Unit,
) {
    when (vehicleType) {
        VehicleType.EVVehicle -> {
            EVOdometerLayoutView(isWatttimeTimeEnabled, navHostController)
        }

        VehicleType.PHEVVehicle -> {
            PHEVOdometerLayout(
                state =
                    PHEVState(
                        electricStatusResponse = null,
                        distanceToEmpty =
                            UnitValue(
                                FuelWidgetConstants.EMPTY_PERCENTAGE_CHARGED,
                                FuelWidgetConstants.DEFAULT_UNIT,
                            ),
                        fuelLevel = 0,
                        chargeRemaining = FuelWidgetConstants.STRING_ZERO,
                        isCharging = false,
                        plugStatus = VehiclePlugStatus.Unplugged,
                        isFuelGaugeVisible = false,
                        isRangeVisible = false,
                        isChargeInfoLayoutVisible = true,
                        isRemoteStatus = REMOTE_STATUS_ACTIVATED,
                        isPluggedIn = false,
                        chargePercentage = 0,
                        estimatedTime = "",
                        evBatteryLow = false,
                        displayDistanceToEmpty = false,
                        evOdometerButtonType = ButtonType.FIND_STATIONS,
                        isHybridElectricFuelType = true,
                        fuelRangeIcon = R.drawable.ic_fuel_stroke,
                        distanceToEmptyTitle = R.string.range,
                        fuelGaugeProgressValue = 0f,
                        isWattTimeEnabled = isWatttimeTimeEnabled,
                    ),
                navHostController = navHostController,
                bodyContent = {
                    PlugStatus(
                        navHostController = navHostController,
                        isBatteryLow = false,
                        buttonType = ButtonType.FIND_STATIONS,
                        wattTimeEnabled = false,
                        vehicleType = VehicleType.PHEVVehicle,
                    )
                },
            )
        }

        else -> {
            onGasOdometerLayout()
        }
    }
}

@Composable
fun EVOdometerLayoutView(
    isWatttimeTimeEnabled: Boolean,
    navHostController: NavHostController,
) {
    EVOdometerLayout(
        state =
            EVState(
                electricStatusResponse = null,
                estimateDistance =
                    UnitValue(
                        FuelWidgetConstants.EMPTY_PERCENTAGE_CHARGED,
                        FuelWidgetConstants.DEFAULT_UNIT,
                    ),
                chargeLevel = 0,
                isFuelGaugeVisible = false,
                isRangeVisible = false,
                isEVChargeStationEnabled = true,
                evOdometerButtonType = ButtonType.FIND_STATIONS,
                isBatteryLow = false,
                isPluggedIn = false,
                isCharging = false,
                chargeRemaining = -1,
                estimatedTime = "",
                chargePercentage = 0,
                fuelGaugeProgressValue = 0f,
                isWattTimeEnabled = isWatttimeTimeEnabled,
                getHomeAddressCoordinates = "",
                isClosedToHome = false,
            ),
        navHostController = navHostController,
        bodyContent = {
            PlugStatus(
                navHostController = navHostController,
                isBatteryLow = false,
                buttonType = ButtonType.FIND_STATIONS,
                wattTimeEnabled = false,
                vehicleType = VehicleType.EVVehicle,
            )
        },
    )
}

@Composable
fun LifeCycleEventObserver(
    onStop: () -> Unit,
    onResume: () -> Unit,
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(Unit) {
        val observer =
            LifecycleEventObserver { _, event ->

                when (event) {
                    Lifecycle.Event.ON_PAUSE,
                    Lifecycle.Event.ON_DESTROY,
                    Lifecycle.Event.ON_STOP,
                    -> {
                        onStop()
                    }

                    Lifecycle.Event.ON_RESUME -> {
                        onResume()
                    }

                    else -> {
                        // do nothing
                    }
                }
            }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

@SuppressLint("UnnecessaryComposedModifier")
fun Modifier.expandedWidth(addWidth: Boolean): Modifier =
    composed {
        when (addWidth) {
            true ->
                size(
                    359.dp,
                    64.dp,
                )

            false -> height(64.dp)
        }
    }

@Composable
fun PlugStatusView(
    navHostController: NavHostController,
    model: PlugStatusUIModel,
) {
    Box(
        modifier =
            Modifier
                .expandedWidth(
                    stringResource(id = model.title).length == 13 || stringResource(id = model.title).length > 13,
                ).padding(end = 8.0.dp),
    ) {
        Column(
            modifier =
                Modifier
                    .align(Alignment.CenterEnd)
                    .testTagID(AccessibilityId.DASHBOARD_FUEL_WIDGET_BUTTON_CTA),
            content = {
                Box(
                    modifier =
                        Modifier
                            .height(42.dp)
                            .background(
                                color = model.color,
                                shape = RoundedCornerShape(100.dp),
                            ).customClickable(
                                model.onClickCallback,
                                navHostController,
                                isWattTimeEnabled = model.isWattTimeEnabled,
                                shouldStartCharging = model.shouldStartCharging,
                            ).testTagID(model.accessibilityID),
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center,
                        modifier = Modifier.fillMaxHeight(),
                    ) {
                        Spacer(modifier = Modifier.width(12.dp))
                        Icon(
                            painter = painterResource(R.drawable.ic_charge_flash),
                            modifier = Modifier.size(width = 11.dp, height = 22.dp),
                            tint = model.iconColor,
                            contentDescription = ContentDescriptions.Charge_Flash_Icon,
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        OACallOut2TextView(
                            text = stringResource(id = model.title),
                            color = AppTheme.colors.tertiary03,
                            textAlign = TextAlign.Center,
                            maxLines = 1,
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                }
            },
        )
    }
}

fun Modifier.customClickable(
    page: String,
    navHostController: NavHostController,
    isWattTimeEnabled: Boolean = false,
    isCloseToHome: Boolean = false,
    shouldStartCharging: Boolean,
): Modifier =
    composed {
        val context = LocalContext.current
        val viewModel = hiltViewModel<FuelWidgetViewModel>()
        val interactionSource = remember { MutableInteractionSource() }
        clickable(
            interactionSource = interactionSource,
            indication = null,
            onClick = {
                fuelWidgetClickAction(
                    page = page,
                    viewModel = viewModel,
                    navHostController = navHostController,
                    context = context,
                    isCloseToHome = isCloseToHome,
                    isWattTimeEnabled = isWattTimeEnabled,
                    shouldStartCharging = shouldStartCharging,
                )
            },
        )
    }

fun fuelWidgetClickAction(
    page: String,
    viewModel: FuelWidgetViewModel,
    navHostController: NavHostController,
    context: Context,
    isCloseToHome: Boolean,
    isWattTimeEnabled: Boolean,
    shouldStartCharging: Boolean,
) {
    when (page) {
        GO_TO_FIND_STATIONS -> {
            viewModel.logEventWithParameter(
                AnalyticsEventParam.DASHBOARD_FUEL_WIDGET_FIND_STATION,
            )
            NavigationUtil.navigateToStations(
                context = context,
                navController = navHostController,
                isEVPublicChargingEnabled = viewModel.isEVPublicChargingEnabled(),
            )
        }

        GO_TO_EV_CHARGE_MANAGEMENT_PAGE -> {
            viewModel.logEventWithParameter(
                AnalyticsEventParam.DASHBOARD_FUEL_WIDGET_CHARGE_INFO,
            )
            GO_TO_EV_CHARGE_MANAGEMENT_PAGE.navigateToFlutter(context)
        }

        GO_TO_EV_CHARGE_INFO_PAGE -> {
            navigateToEvChargeInfoPage(
                isWattTimeEnabled,
                shouldStartCharging,
                isCloseToHome,
                navHostController,
                viewModel,
                context,
            )
        }

        GO_TO_EV_ACTIVE_CHARGE_SESSION_PAGE -> {
            gotoActiveChargeSessionPage(
                viewModel,
                navHostController,
                context,
                isCloseToHome,
                isWattTimeEnabled,
                shouldStartCharging,
            )
        }
    }
}

fun gotoActiveChargeSessionPage(
    viewModel: FuelWidgetViewModel,
    navHostController: NavHostController,
    context: Context,
    isCloseToHome: Boolean,
    isWattTimeEnabled: Boolean,
    shouldStartCharging: Boolean,
) {
    BuildConfig.FLAVOR_brand
        .takeIf { it == PublicChargingConstants.SUBARU }
        ?.let {
            navigateToEvChargeInfoPage(
                isWattTimeEnabled,
                shouldStartCharging,
                isCloseToHome,
                navHostController,
                viewModel,
                context,
            )
        } ?: run {
        viewModel.logEventWithParameter(
            AnalyticsEventParam.DASHBOARD_FUEL_WIDGET_CHARGE_STATUS,
        )
        // Get charging status and ID from the view model
        val chargingStatus = viewModel.getCurrentChargingStatus()
        val chargingId = viewModel.getCurrentChargingId() ?: ""

        if (chargingStatus != null) {
            // Convert the enum to the data class
            val publicChargingStatus =
                ChargingStatus(
                    isCharging = chargingStatus == ChargeStatus.NORMAL_CHARGING,
                    isBatteryLow = isBatteryLow(viewModel),
                    chargePercentage = getChargePercentage(viewModel),
                    chargeRunningStatus =
                        ChargeRunningStatus(
                            isCharging = chargingStatus == ChargeStatus.NORMAL_CHARGING,
                            chargingStatus = chargingStatus.name,
                            travelDistance = null,
                            distanceUnit = null,
                        ),
                    chargePointAddress =
                        ChargePointAddress(),
                    startTimeAndEnergy =
                        StartTimeAndEnergy(),
                    lastUpdatedTime = null,
                    isEvGoPartnerAndStatusIsActive = null,
                    isSessionInvalid = null,
                    ctaStatus =
                        ChargeCtaStatus(
                            isCtaActive = true,
                            ctaText = PublicChargingConstants.STOP_CHARGING_TEXT,
                        ),
                    estimatedTime =
                        when (val state = viewModel.state.value) {
                            is FuelState.Success ->
                                when (
                                    val fuelType =
                                        state.vehicleFuel
                                ) {
                                    is VehicleFuel.Ev -> fuelType.evState.estimatedTime
                                    is VehicleFuel.Phev -> fuelType.phevState.estimatedTime
                                    else -> null
                                }

                            else -> null
                        },
                )
            navHostController.currentBackStackEntry?.savedStateHandle?.apply {
                set("chargingStatus", publicChargingStatus)
                set("chargingId", chargingId)
                set(FROM_DASHBOARD, true)
            }
            if (BuildConfig.CHARGE_INFO_FLUTTER_DEPRECATION) {
                navHostController.navigate(OAScreen.PublicChargingStatusScreen.route)
            } else {
                val isChargingKey = "isChargeNow"
                val bundle = Bundle()
                val data = HashMap<String, Any?>()
                if (isWattTimeEnabled) {
                    data[isChargingKey] = shouldStartCharging
                } else {
                    data["isCloseToHome"] = isCloseToHome
                    data[isChargingKey] = shouldStartCharging
                }
                bundle.putString(
                    ToyotaConstants.FLUTTER_DATA,
                    Gson().toJson(data),
                )
                context.startActivity(
                    DashboardFlutterActivity.createIntent(
                        context,
                        bundle,
                        GO_TO_VEHICLE_CHARGE_MANAGEMENT_SCREEN,
                    ),
                )
            }
        }
    }
}

fun isCharging(viewModel: FuelWidgetViewModel): Boolean =
    when (viewModel.state.value) {
        is FuelState.Success -> {
            when (
                val fuelType =
                    (viewModel.state.value as FuelState.Success).vehicleFuel
            ) {
                is VehicleFuel.Ev -> fuelType.evState.isCharging
                is VehicleFuel.Phev -> fuelType.phevState.isCharging
                else -> false
            }
        }

        else -> false
    }

fun getChargePercentage(viewModel: FuelWidgetViewModel): String? =
    when (
        val state =
            viewModel.state.value
    ) {
        is FuelState.Success ->
            when (
                val fuelType =
                    state.vehicleFuel
            ) {
                is VehicleFuel.Ev -> {
                    fuelType.evState.chargePercentage.toString()
                }

                is VehicleFuel.Phev -> {
                    fuelType.phevState.chargePercentage.toString()
                }

                else -> null
            }

        else -> null
    }

fun isBatteryLow(viewModel: FuelWidgetViewModel): Boolean =
    when (val state = viewModel.state.value) {
        is FuelState.Success ->
            when (
                val fuelType =
                    state.vehicleFuel
            ) {
                is VehicleFuel.Ev -> fuelType.evState.isBatteryLow
                is VehicleFuel.Phev -> fuelType.phevState.evBatteryLow
                else -> false
            }

        else -> false
    }

fun String.navigateToFlutter(
    context: Context,
    bundle: Bundle? = null,
) {
    context.startActivity(
        DashboardFlutterActivity.createIntent(
            context = context,
            bundle = bundle,
            screen = this,
        ),
    )
}

fun navigateToEvChargeInfoPage(
    isWattTimeEnabled: Boolean,
    shouldStartCharging: Boolean,
    isCloseToHome: Boolean,
    navHostController: NavHostController,
    viewModel: FuelWidgetViewModel,
    context: Context,
) {
    val isChargeKey = "isChargeNow"
    val bundle = Bundle()
    val data = HashMap<String, Any?>()
    if (isWattTimeEnabled) {
        data[isChargeKey] = shouldStartCharging
    } else {
        data["isCloseToHome"] = isCloseToHome
        data[isChargeKey] = shouldStartCharging
    }
    bundle.putString(
        ToyotaConstants.FLUTTER_DATA,
        Gson().toJson(data),
    )

    if (BuildConfig.CHARGE_INFO_FLUTTER_DEPRECATION && isWattTimeEnabled) {
        navHostController.currentBackStackEntry?.savedStateHandle?.set(
            ARG_IS_CHARGE_NOW,
            shouldStartCharging,
        )
        navHostController.navigate(OAScreen.ChargeInfo.route)
    } else if (BuildConfig.CHARGE_INFO_FLUTTER_DEPRECATION) {
        navHostController.currentBackStackEntry?.savedStateHandle?.set(
            NAV_ARG_IS_CHARGE_NOW,
            shouldStartCharging,
        )
        navHostController.navigate(OAScreen.ChargeManagementNestedRoute.route)
    } else {
        val pageToNavigate =
            if (isWattTimeEnabled) GO_TO_EV_CHARGE_INFO_PAGE else GO_TO_VEHICLE_CHARGE_MANAGEMENT_SCREEN
        context.startActivity(
            DashboardFlutterActivity.createIntent(
                context,
                bundle,
                pageToNavigate,
            ),
        )
    }
    viewModel.logEventWithParameter(
        AnalyticsEventParam.DASHBOARD_FUEL_WIDGET_CHARGE_INFO,
    )
}

@Composable
fun GasOdometerLayout(state: GasVehicleState) {
    Box(
        modifier =
            Modifier
                .padding(start = 8.dp, end = 8.dp)
                .background(AppTheme.colors.tile01)
                .fillMaxWidth(),
    ) {
        Card(
            elevation = 6.dp,
            backgroundColor = AppTheme.colors.tile01,
            modifier =
                Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth(),
        ) {
            Box(
                modifier =
                    Modifier
                        .padding(start = 8.dp, end = 8.dp, top = 8.dp)
                        .background(AppTheme.colors.tile01),
            ) {
                Column(modifier = Modifier.align(Alignment.CenterStart)) {
                    GasOdometerRangeRow(state)
                    Box(
                        modifier =
                            Modifier
                                .size(120.dp, 20.dp),
                    ) {
                        OACaption1TextView(
                            modifier =
                                Modifier
                                    .align(Alignment.CenterStart)
                                    .testTagID(AccessibilityId.ID_FUEL_VIEW_DISTANCE_TO_EMPTY_TEXT),
                            text = stringResource(state.distanceToEmptyTitle),
                            color = AppTheme.colors.tertiary05,
                        )
                    }

                    if (state.fuelLevel != null) {
                        GasOdometerProgressComposable(state)
                    }
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}

@Composable
private fun GasOdometerRangeRow(state: GasVehicleState) {
    Row(
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(state.fuelRangeIcon),
            tint = AppTheme.colors.tertiary03,
            contentDescription = ContentDescriptions.Fuel_Stroke_Icon,
            modifier = Modifier.testTagID(AccessibilityId.ID_FUEL_VIEW_GAS_ICON),
        )
        if (state.isRangeVisible == true) {
            OASubHeadLine1TextView(
                modifier =
                    Modifier
                        .padding(start = 4.25.dp)
                        .testTagID(
                            AccessibilityId.ID_FUEL_VIEW_RANGE_VALUE_TEXT,
                        ),
                text = state.distanceToEmpty.value.toString(),
                color = AppTheme.colors.tertiary03,
            )
        }
        if (state.isRangeVisible == true) {
            OACaption2TextView(
                modifier =
                    Modifier
                        .padding(start = 4.25.dp)
                        .testTagID(
                            AccessibilityId.ID_FUEL_VIEW_RANGE_UNIT_TEXT,
                        ),
                text = state.distanceToEmpty.unit,
                color = AppTheme.colors.tertiary03,
            )
        }
    }
}

@Composable
private fun GasOdometerProgressComposable(state: GasVehicleState) {
    Spacer(modifier = Modifier.height(4.dp))
    Box(
        modifier =
            Modifier
                .clip(RoundedCornerShape(50)),
    ) {
        LinearProgressIndicator(
            modifier =
                Modifier
                    .size(104.dp, 6.dp)
                    .testTagID(AccessibilityId.ID_FUEL_VIEW_FUEL_BAR_VIEW),
            progress = state.fuelGaugeProgressValue,
            color = pHEVFuelGaugeIconColor(state.fuelLevel as Int),
            backgroundColor = AppTheme.colors.tertiary10,
        )
    }
}

@Composable
fun EVOdometerLayout(
    state: EVState,
    navHostController: NavHostController,
    bodyContent: @Composable () -> Unit,
) {
    Box(
        modifier =
            Modifier
                .background(AppTheme.colors.tile01)
                .padding(start = 8.dp, end = 8.dp)
                .customClickable(
                    GO_TO_EV_CHARGE_INFO_PAGE,
                    navHostController,
                    isWattTimeEnabled = state.isWattTimeEnabled,
                    shouldStartCharging = false,
                ),
    ) {
        Card(
            backgroundColor = AppTheme.colors.tile01,
            elevation = 6.dp,
            modifier =
                Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth(),
        ) {
            Box(
                modifier =
                    Modifier
                        .padding(start = 8.dp, end = 8.dp, top = 8.dp)
                        .background(AppTheme.colors.tile01)
                        .fillMaxWidth(),
            ) {
                Column(
                    modifier =
                        Modifier
                            .align(Alignment.CenterStart)
                            .testTagID(AccessibilityId.DASHBOARD_FUEL_WIDGET_CHARGE_INFO_CTA),
                ) {
                    EVDistanceRow(state)
                    EVTimeRemainingRow(state)
                    EvProgressIndicatorComposable(state)
                    Spacer(
                        modifier = Modifier.height(9.dp),
                    )
                }
            }
            if (state.isEVChargeStationEnabled) bodyContent()
        }
    }
}

@Composable
private fun EVDistanceRow(state: EVState) {
    Row(
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        OASubHeadLine1TextView(
            text = state.estimateDistance.value.toString(),
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier
                    .height(27.dp)
                    .testTagID(
                        AccessibilityId.ID_FUEL_VIEW_RANGE_VALUE_TEXT,
                    ),
        )
        Spacer(modifier = Modifier.width(2.dp))
        OACaption2TextView(
            text =
                stringResource(
                    id = R.string.unit_est_test,
                    state.estimateDistance.unit,
                ),
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier.testTagID(
                    AccessibilityId.ID_FUEL_VIEW_RANGE_UNIT_TEXT,
                ),
        )
        Spacer(modifier = Modifier.width(5.dp))
        Icon(
            painter = painterResource(R.drawable.ic_ev_climate_fan_off),
            tint = AppTheme.colors.tertiary03,
            contentDescription = ContentDescriptions.Climate_Fan_Off_Icon,
            modifier =
                Modifier
                    .size(14.67.dp)
                    .testTagID(AccessibilityId.ID_FUEL_VIEW_CLIMATE_ICON),
        )
    }
}

@Composable
private fun EVTimeRemainingRow(state: EVState) {
    Row(
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(bottom = 5.5.dp),
    ) {
        if (state.chargeRemaining != null) {
            BatteryIcon(
                chargePercentage = state.chargePercentage,
                isCharging = state.isCharging,
                isPluggedIn = state.isPluggedIn,
                contentDescription = ContentDescriptions.Fuel_Charge_Icon,
                testTagId = AccessibilityId.ID_FUEL_VIEW_BATTERY_ICON,
                modifier = Modifier.size(24.dp),
            )

            if (state.isCharging) {
                OACaption1TextView(
                    modifier = Modifier.padding(start = 5.43.dp),
                    text =
                        stringResource(
                            id = R.string.ev_time_until_full,
                            state.estimatedTime,
                        ),
                    color = AppTheme.colors.tertiary03,
                )
            } else {
                OACaption1TextView(
                    modifier = Modifier.padding(start = 5.43.dp),
                    text =
                        if (state.chargeRemaining.toString() != FuelWidgetConstants.NEGATIVE_PERCENTAGE) {
                            stringResource(
                                id = R.string.percentage_charged,
                                state.chargeRemaining,
                            )
                        } else {
                            stringResource(
                                id = R.string.negative_percentage_charged,
                            )
                        },
                    color = AppTheme.colors.tertiary03,
                )
            }
        } else {
            OACaption1TextView(
                text = FuelWidgetConstants.EMPTY_PERCENTAGE_CHARGED,
                color = AppTheme.colors.tertiary03,
            )
        }
        Spacer(modifier = Modifier.width(4.dp))
        Icon(
            painter = painterResource(R.drawable.ic_ev_chevron_right),
            tint = AppTheme.colors.tertiary05,
            contentDescription = ContentDescriptions.Fuel_Widget_Chevron_Icon,
            modifier = Modifier.testTagID(AccessibilityId.ID_FUEL_VIEW_CHEVRON_ICON),
        )
    }
}

@Composable
private fun EvProgressIndicatorComposable(state: EVState) {
    Box(
        modifier = Modifier.clip(RoundedCornerShape(50)),
    ) {
        LinearProgressIndicator(
            modifier =
                Modifier
                    .size(104.dp, 6.dp)
                    .testTagID(AccessibilityId.ID_FUEL_VIEW_FUEL_BAR_VIEW),
            progress = state.fuelGaugeProgressValue,
            color = eVFuelGaugeIconColor(state.chargeLevel),
            backgroundColor = AppTheme.colors.tertiary10,
        )
    }
}

@Composable
fun PHEVOdometerLayout(
    state: PHEVState,
    navHostController: NavHostController,
    bodyContent: @Composable () -> Unit,
) {
    Box(
        modifier =
            Modifier
                .padding(start = 8.dp, end = 8.dp)
                .background(AppTheme.colors.tile01)
                .customClickable(
                    if (state.isRemoteStatus == REMOTE_STATUS_ACTIVATED) GO_TO_EV_CHARGE_INFO_PAGE else EMPTY_VALUE,
                    navHostController,
                    isWattTimeEnabled = state.isWattTimeEnabled,
                    shouldStartCharging = false,
                ).fillMaxWidth(),
    ) {
        Card(
            backgroundColor = AppTheme.colors.tile01,
            elevation = 6.dp,
            modifier =
                Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth(),
        ) {
            PHEVFuelRangeBodyComposable(state)
            if (state.evBatteryLow) bodyContent()
        }
    }
}

@Composable
private fun PHEVFuelRangeBodyComposable(state: PHEVState) {
    Box(
        modifier =
            Modifier
                .padding(start = 8.dp, end = 8.dp, top = 8.dp)
                .background(AppTheme.colors.tile01),
    ) {
        Column(
            modifier =
                Modifier
                    .align(Alignment.CenterStart)
                    .testTagID(AccessibilityId.DASHBOARD_FUEL_WIDGET_CHARGE_INFO_CTA),
        ) {
            PHEVFuelRangeRow(state)
            PHEVFuelDistanceText(state)

            if (state.fuelLevel != null && state.fuelLevel != 0) {
                PHEVFuelProgressComposable(state)
            }
            if (state.isChargeInfoLayoutVisible && state.isRemoteStatus != REMOTE_STATUS_HIDE) {
                PHEVFuelInfoComposable(state)
            } else {
                Spacer(
                    modifier = Modifier.height(9.5.dp),
                )
            }
        }
    }
}

@Composable
private fun PHEVFuelRangeRow(phevState: PHEVState) {
    Row(
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(phevState.fuelRangeIcon),
            contentDescription = ContentDescriptions.Fuel_Stroke_Icon,
            tint = AppTheme.colors.tertiary03,
            modifier = Modifier.testTagID(AccessibilityId.ID_FUEL_VIEW_GAS_ICON),
        )
        if (phevState.isRangeVisible == true) {
            OASubHeadLine1TextView(
                modifier =
                    Modifier
                        .padding(start = 4.25.dp)
                        .testTagID(
                            AccessibilityId.ID_FUEL_VIEW_PHEV_RANGE_VALUE_TEXT,
                        ),
                text = phevState.distanceToEmpty.value.toString(),
                color = AppTheme.colors.tertiary03,
            )
        }
        if (phevState.isRangeVisible == true) {
            OACaption2TextView(
                modifier =
                    Modifier
                        .padding(start = 4.25.dp)
                        .testTagID(
                            AccessibilityId.ID_FUEL_VIEW_PHEV_RANGE_UNIT_TEXT,
                        ),
                text = phevState.distanceToEmpty.unit,
                color = AppTheme.colors.tertiary03,
            )
        }
    }
}

@Composable
private fun PHEVFuelDistanceText(state: PHEVState) {
    Box(
        modifier =
            Modifier
                .size(120.dp, 20.dp),
    ) {
        OACaption1TextView(
            modifier =
                Modifier
                    .align(Alignment.CenterStart)
                    .testTagID(
                        AccessibilityId.ID_FUEL_VIEW_DISTANCE_TO_EMPTY_TEXT,
                    ),
            text = stringResource(state.distanceToEmptyTitle),
            color = AppTheme.colors.tertiary05,
        )
    }
}

@Composable
private fun PHEVFuelProgressComposable(state: PHEVState) {
    Spacer(modifier = Modifier.height(4.dp))
    Box(
        modifier = Modifier.clip(RoundedCornerShape(50)),
    ) {
        LinearProgressIndicator(
            modifier =
                Modifier
                    .size(104.dp, 6.dp)
                    .testTagID(AccessibilityId.ID_FUEL_VIEW_FUEL_BAR_VIEW),
            progress = state.fuelGaugeProgressValue,
            color = pHEVFuelGaugeIconColor(state.fuelLevel as Int),
            backgroundColor = AppTheme.colors.tertiary10,
        )
    }
}

@Composable
private fun PHEVFuelInfoComposable(state: PHEVState) {
    Spacer(
        modifier = Modifier.height(5.5.dp),
    )
    Row(
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(bottom = 5.5.dp),
    ) {
        BatteryIcon(
            chargePercentage = state.chargePercentage,
            isCharging = state.isCharging,
            isPluggedIn = state.isPluggedIn ?: false,
            contentDescription = ContentDescriptions.Fuel_Charge_Icon,
            testTagId = AccessibilityId.ID_FUEL_VIEW_BATTERY_ICON,
        )

        if (state.isCharging) {
            OACaption2TextView(
                modifier =
                    Modifier
                        .padding(start = 5.43.dp)
                        .testTagID(AccessibilityId.ID_FUEL_VIEW_PERCENT_TEXT),
                text =
                    stringResource(
                        if (state.evBatteryLow) R.string.time_until_full else R.string.time_until_fully_charged,
                        state.estimatedTime,
                    ),
                color = AppTheme.colors.tertiary03,
            )
        } else {
            OACaption2TextView(
                modifier =
                    Modifier
                        .padding(start = 5.43.dp)
                        .testTagID(AccessibilityId.ID_FUEL_VIEW_PERCENT_TEXT),
                text =
                    if (state.chargeRemaining != FuelWidgetConstants.NEGATIVE_PERCENTAGE) {
                        stringResource(
                            id = R.string.percentage_charged,
                            state.chargeRemaining,
                        )
                    } else {
                        stringResource(
                            id = R.string.negative_percentage_charged,
                        )
                    },
                color = AppTheme.colors.tertiary03,
            )
        }
        Spacer(modifier = Modifier.width(4.dp))
        Icon(
            painter = painterResource(R.drawable.ic_ev_chevron_right),
            tint = AppTheme.colors.tertiary05,
            contentDescription = ContentDescriptions.Fuel_Widget_Chevron_Icon,
            modifier =
                Modifier.testTagID(
                    AccessibilityId.ID_FUEL_VIEW_CHEVRON_ICON,
                ),
        )
    }
}

@Composable
fun GetHomeAddressCoordinates(homeCoordinates: String) {
    val viewModel = hiltViewModel<FuelWidgetViewModel>()
    viewModel.updateHomeLocation(
        getAddressCoordinates(homeCoordinates),
    )
}
