package com.toyota.oneapp.features.fuelwidget

import com.toyota.oneapp.features.fuelwidget.application.FuelWidgetLogic
import com.toyota.oneapp.features.fuelwidget.application.FuelWidgetUseCase
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class FuelWidgetModule {
    @Binds
    abstract fun provideFuelWidgetUseCase(fuelWidgetLogic: FuelWidgetLogic): FuelWidgetUseCase
}
