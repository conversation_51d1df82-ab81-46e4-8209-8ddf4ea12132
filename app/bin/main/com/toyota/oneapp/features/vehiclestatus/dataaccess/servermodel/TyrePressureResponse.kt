package com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.vehiclestatus.domain.model.TyrePressureValues
import kotlin.collections.ArrayList
import com.toyota.oneapp.features.vehiclestatus.domain.model.TyrePressure as TyrePressureModel

data class TyrePressureResponse(
    @SerializedName("payload") val payload: TyrePressurePayloadResponse?,
    @SerializedName("status") val status: String? = null,
    @SerializedName("code") val code: Int? = null,
    @SerializedName("message") val message: String? = null,
    @SerializedName("errors") var errors: ArrayList<String>,
)

data class TyrePressurePayloadResponse(
    @SerializedName("vin") val vin: String,
    @SerializedName("tirePressureStatus") val tyrePressureStatus: String,
    @SerializedName("tirePressureTimestamp") val tyrePressureTimestamp: String,
    @SerializedName("flTirePressure") val flTyrePressure: TyrePressureDetailsResponse?,
    @SerializedName("frTirePressure") val frTyrePressure: TyrePressureDetailsResponse?,
    @SerializedName("rlTirePressure") val rlTyrePressure: TyrePressureDetailsResponse?,
    @SerializedName("rrTirePressure") val rrTyrePressure: TyrePressureDetailsResponse?,
    @SerializedName("spareTirePressure") val spareTyrePressure: TyrePressureDetailsResponse? = null,
)

data class TyrePressureDetailsResponse(
    @SerializedName("value") val value: Int?,
    @SerializedName("unit") val unit: String?,
    @SerializedName("displayName") val displayName: String?,
    @SerializedName("displayLowTirePressureWarning") val displayLowTyrePressureWarning: Boolean?,
)

fun TyrePressurePayloadResponse.toUiModel(): TyrePressureModel {
    return TyrePressureModel(
        frontLeftTyrePressure =
            flTyrePressure?.let {
                TyrePressureValues(
                    it.value?.toString(),
                    it.displayLowTyrePressureWarning,
                    it.unit,
                )
            },
        frontRightTyrePressure =
            frTyrePressure?.let {
                TyrePressureValues(
                    it.value?.toString(),
                    it.displayLowTyrePressureWarning,
                    it.unit,
                )
            },
        rearLeftTyrePressure =
            rlTyrePressure?.let {
                TyrePressureValues(
                    it.value?.toString(),
                    it.displayLowTyrePressureWarning,
                    it.unit,
                )
            },
        rearRightTyrePressure =
            rrTyrePressure?.let {
                TyrePressureValues(
                    it.value?.toString(),
                    it.displayLowTyrePressureWarning,
                    it.unit,
                )
            },
    )
}
