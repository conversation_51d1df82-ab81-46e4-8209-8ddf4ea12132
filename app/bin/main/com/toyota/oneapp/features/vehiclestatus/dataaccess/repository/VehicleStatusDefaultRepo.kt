package com.toyota.oneapp.features.vehiclestatus.dataaccess.repository

import com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel.TyrePressureResponse
import com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel.VehicleStatusResponse
import com.toyota.oneapp.features.vehiclestatus.dataaccess.service.VehicleStatusApi
import com.toyota.oneapp.features.vehiclestatus.domain.repository.VehicleStatusRepository
import com.toyota.oneapp.model.remote.RemoteRequest
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class VehicleStatusDefaultRepo
    @Inject
    constructor(
        val service: VehicleStatusApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : VehicleStatusRepository(errorParser, ioContext) {
        override suspend fun fetchNg86VehicleStatus(
            vin: String,
            brand: String,
        ): Resource<VehicleStatusResponse?> {
            return makeApiCall {
                service.fetchNg86VehicleStatus(
                    vin = vin,
                    brand = brand,
                )
            }
        }

        override suspend fun fetch17CYPlusVehicleStatus(
            vin: String,
            brand: String,
        ): Resource<VehicleStatusResponse?> {
            return makeApiCall {
                service.fetch17CYPlusVehicleStatus(
                    vin = vin,
                    brand = brand,
                )
            }
        }

        override suspend fun fetch17CYVehicleStatus(
            vin: String,
            brand: String,
        ): Resource<VehicleStatusResponse?> {
            return makeApiCall {
                service.fetch17CYVehicleStatus(
                    vin = vin,
                    brand = brand,
                )
            }
        }

        override suspend fun fetchTyresPressure(
            vin: String,
            brand: String,
            generation: String?,
        ): Resource<TyrePressureResponse?> {
            return makeApiCall {
                service.fetchTiresPressure(
                    vin = vin,
                    brand = brand,
                    generation = generation,
                )
            }
        }

        override suspend fun refreshNg86VehicleStatus(
            vin: String,
            brand: String,
            request: RemoteRequest,
        ): Resource<VehicleStatusResponse?> {
            return makeApiCall {
                service.refreshNg86VehicleStatus(
                    vin = vin,
                    brand = brand,
                    requestBody = request,
                )
            }
        }

        override suspend fun refresh17CYPlusVehicleStatus(
            vin: String,
            brand: String,
            request: RemoteRequest,
        ): Resource<VehicleStatusResponse?> {
            return makeApiCall {
                service.refresh17CYPlusVehicleStatus(
                    vin = vin,
                    brand = brand,
                    requestBody = request,
                )
            }
        }

        override suspend fun refresh17CYVehicleStatus(
            vin: String,
            brand: String,
            request: RemoteRequest,
        ): Resource<VehicleStatusResponse?> {
            return makeApiCall {
                service.refresh17CYVehicleStatus(
                    vin = vin,
                    brand = brand,
                    requestBody = request,
                )
            }
        }
    }
