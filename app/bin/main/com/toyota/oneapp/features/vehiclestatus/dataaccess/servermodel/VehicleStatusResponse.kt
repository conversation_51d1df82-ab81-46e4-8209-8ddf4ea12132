package com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class VehicleStatusResponse(
    @SerializedName("payload") var payload: VehicleStatusPayloadResponse,
)

data class VehicleStatusPayloadResponse(
    @SerializedName("vehicleStatus") var vehicleStatus: ArrayList<VehicleStatusDetailsResponse>,
    @SerializedName("telemetry") var telemetry: TelemetryResponse?,
    @SerializedName("occurrenceDate") var occurrenceDate: String?,
    @SerializedName("cautionOverallCount") var cautionOverallCount: Int?,
    @SerializedName("latitude") var latitude: Double?,
    @SerializedName("longitude") var longitude: Double?,
    @SerializedName("locationAcquisitionDatetime") var locationAcquisitionDatetime: String?,
)

data class VehicleStatusDetailsResponse(
    @SerializedName("category") var category: String?,
    @SerializedName("displayOrder") var displayOrder: Int?,
    @SerializedName("sections") var sections: ArrayList<SectionsResponse>,
)

data class TelemetryResponse(
    @SerializedName("fugage") var fugage: TelemetryValuesResponse,
    @SerializedName("rage") var rage: TelemetryValuesResponse,
    @SerializedName("odo") var odo: TelemetryValuesResponse,
)

data class TelemetryValuesResponse(
    @SerializedName("value") var value: Int?,
    @SerializedName("unit") var unit: String?,
)

data class SectionsResponse(
    @SerializedName("section") var section: String?,
    @SerializedName("values") var values: ArrayList<SectionValuesResponse>,
)

data class SectionValuesResponse(
    @SerializedName("value") var value: String? = null,
    @SerializedName("status") var status: Int? = null,
)
