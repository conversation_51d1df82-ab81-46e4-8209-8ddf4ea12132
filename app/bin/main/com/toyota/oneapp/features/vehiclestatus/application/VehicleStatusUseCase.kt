package com.toyota.oneapp.features.vehiclestatus.application

import com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel.VehicleStatusResponse
import com.toyota.oneapp.features.vehiclestatus.domain.model.VehicleStatusDetails
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow

interface VehicleStatusUseCase {
    suspend fun fetchVehicleStatus(vehicleInfo: VehicleInfo): Flow<VehicleStatusDetails>

    suspend fun refreshVehicleStatus(vehicleInfo: VehicleInfo): Flow<Resource<VehicleStatusResponse?>>
}
