package com.toyota.oneapp.features.vehiclestatus.dataaccess.service

import com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel.TyrePressureResponse
import com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel.VehicleStatusResponse
import com.toyota.oneapp.model.remote.RemoteRequest
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

interface VehicleStatusApi {
    @GET("/oneapi/v1/remote/ng86/status")
    suspend fun fetchNg86VehicleStatus(
        @Header("vin") vin: String,
        @Header("x-BRAND") brand: String,
    ): Response<VehicleStatusResponse?>

    @GET("/oneapi/v1/global/remote/status")
    suspend fun fetch17CYPlusVehicleStatus(
        @Header("vin") vin: String,
        @Header("x-BRAND") brand: String,
    ): Response<VehicleStatusResponse>

    @GET("/oneapi/v2/legacy/remote/status")
    suspend fun fetch17CYVehicleStatus(
        @Header("vin") vin: String,
        @Header("x-BRAND") brand: String,
    ): Response<VehicleStatusResponse?>

    @GET("/oneapi/v1/telemetry/tires/pressure")
    suspend fun fetchTiresPressure(
        @Header("vin") vin: String,
        @Header("x-BRAND") brand: String,
        @Header("generation") generation: String?,
    ): Response<TyrePressureResponse?>

    @POST("/oneapi/v1/remote/ng86/refresh-status")
    suspend fun refreshNg86VehicleStatus(
        @Header("vin") vin: String,
        @Header("x-BRAND") brand: String,
        @Body() requestBody: RemoteRequest,
    ): Response<VehicleStatusResponse>

    @POST("/oneapi/v1/global/remote/refresh-status")
    suspend fun refresh17CYPlusVehicleStatus(
        @Header("vin") vin: String,
        @Header("x-BRAND") brand: String,
        @Body() requestBody: RemoteRequest,
    ): Response<VehicleStatusResponse>

    @POST("/oneapi/v1/legacy/remote/refresh-status")
    suspend fun refresh17CYVehicleStatus(
        @Header("vin") vin: String,
        @Header("x-BRAND") brand: String,
        @Body() requestBody: RemoteRequest,
    ): Response<VehicleStatusResponse>
}
