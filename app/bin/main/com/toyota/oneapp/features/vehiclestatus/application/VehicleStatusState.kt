package com.toyota.oneapp.features.vehiclestatus.application

import com.toyota.oneapp.features.vehiclestatus.domain.model.VehicleStatusDetails

sealed class VehicleStatusState {
    object Idle : VehicleStatusState()

    class Success(val data: VehicleStatusDetails) : VehicleStatusState()

    class Error(val errorCode: String?, val errorMessage: String?) : VehicleStatusState()
}

sealed class VehicleProgressState {
    object Loading : VehicleProgressState()

    object Dismiss : VehicleProgressState()

    object Initialize : VehicleProgressState()
}

/**
 * To avoid multiple api call's in silent push notification
 */
sealed class VehicleStatusLoadingState {
    object Loading : VehicleStatusLoadingState()

    object Dismiss : VehicleStatusLoadingState()
}
