package com.toyota.oneapp.features.vehiclestatus.application

import com.toyota.oneapp.util.AppLanguageUtils

object VehicleStatusConstants {
    // En
    private const val OPEN_EN = "open"
    private const val UNLOCKED_EN = "unlocked"
    private const val DOOR_EN = "door"
    private const val WINDOW_EN = "window"
    private const val HOOD_EN = "hood"
    private const val MOON_ROOF_EN = "moonroof"
    private const val TRUNK_EN = "trunk"
    private const val HATCH_EN = "hatch"
    private const val DRIVER_SIDE_EN = "Driver Side"
    private const val PASSENGER_SIDE_EN = "Passenger Side"
    private const val REAR_DOOR_EN = "Rear Door"
    private const val REAR_WINDOW_EN = "Rear Window"
    private const val TAIL_GATE_EN = "Tailgate"
    private const val OTHER_EN = "Other"

    // es_MX
    private const val OPEN_ES_MX = "abierta"
    private const val UNLOCKED_ES_MX = "desbloqueada"
    private const val DOOR_ES_MX = "Puerta delantera"
    private const val WINDOW_ES_MX = "ventana delantera"
    private const val HOOD_ES_MX = "cofre"
    private const val MOON_ROOF_ES_MX = "techo corredizo"
    private const val TRUNK_ES_MX = "cajuela"
    private const val HATCH_ES_MX = "escotilla"
    private const val DRIVER_SIDE_ES_MX = "Lado del conductor"
    private const val PASSENGER_SIDE_ES_MX = "Lado del Pasajero"
    private const val REAR_DOOR_ES_MX = "Puerta trasera"
    private const val REAR_WINDOW_ES_MX = "Ventana trasera"
    private const val TAIL_GATE_ES_MX = "Puerta de la caja"
    private const val OTHER_ES_MX = "Otro"

    // es_PR
    private const val OPEN_ES_PR = "abrir"
    private const val UNLOCKED_ES_PR = "desbloquada"
    private const val DOOR_ES_PR = "Puerta"
    private const val WINDOW_ES_PR = "ventana"
    private const val HOOD_ES_PR = "capó"
    private const val MOON_ROOF_ES_PR = "techo corredizo"
    private const val TRUNK_ES_PR = "baúl"
    private const val HATCH_ES_PR = "escotilla"
    private const val DRIVER_SIDE_ES_PR = "Lado del conductor"
    private const val PASSENGER_SIDE_ES_PR = "Lado del Pasajero"
    private const val REAR_DOOR_ES_PR = "Puerta trasera"
    private const val REAR_WINDOW_ES_PR = "Ventana trasera"
    private const val TAIL_GATE_ES_PR = "Puerta de baul"
    private const val OTHER_ES_PR = "Otro"

    // fr_CA
    private const val OPEN_FR_CA = "ouvrir"
    private const val UNLOCKED_FR_CA = "déverrouille"
    private const val DOOR_FR_CA = "Porte"
    private const val WINDOW_FR_CA = "vitre"
    private const val HOOD_FR_CA = "capot"
    private const val MOON_ROOF_FR_CA = "toit ouvrant"
    private const val TRUNK_FR_CA = "tronc"
    private const val HATCH_FR_CA = "hayon"
    private const val DRIVER_SIDE_FR_CA = "Côté conducteur"
    private const val PASSENGER_SIDE_FR_CA = "Côté passager"
    private const val REAR_DOOR_FR_CA = "Porte arrière"
    private const val REAR_WINDOW_FR_CA = "Vitre arrière"
    private const val TAIL_GATE_FR_CA = "Hayon"
    private const val OTHER_FR_CA = "Autre"

    val OPEN = arrayOf(OPEN_EN, OPEN_ES_MX, OPEN_ES_PR, OPEN_FR_CA)
    val UNLOCKED = arrayOf(UNLOCKED_EN, UNLOCKED_ES_MX, UNLOCKED_ES_PR, UNLOCKED_FR_CA)
    val DOOR = arrayOf(DOOR_EN, DOOR_ES_MX, DOOR_ES_PR, DOOR_FR_CA)
    val WINDOW = arrayOf(WINDOW_EN, WINDOW_ES_MX, WINDOW_ES_PR, WINDOW_FR_CA)
    val HOOD = arrayOf(HOOD_EN, HOOD_ES_MX, HOOD_ES_PR, HOOD_FR_CA)
    val MOON_ROOF = arrayOf(MOON_ROOF_EN, MOON_ROOF_ES_MX, MOON_ROOF_ES_PR, MOON_ROOF_FR_CA)
    val TRUNK = arrayOf(TRUNK_EN, TRUNK_ES_MX, TRUNK_ES_PR, TRUNK_FR_CA)
    val HATCH = arrayOf(HATCH_EN, HATCH_ES_MX, HATCH_ES_PR, HATCH_FR_CA)
    val DRIVER_SIDE =
        arrayOf(
            DRIVER_SIDE_EN,
            DRIVER_SIDE_ES_MX,
            DRIVER_SIDE_ES_PR,
            DRIVER_SIDE_FR_CA,
        )
    val PASSENGER_SIDE =
        arrayOf(
            PASSENGER_SIDE_EN,
            PASSENGER_SIDE_ES_MX,
            PASSENGER_SIDE_ES_PR,
            PASSENGER_SIDE_FR_CA,
        )
    val REAR_DOOR = arrayOf(REAR_DOOR_EN, REAR_DOOR_ES_MX, REAR_DOOR_ES_PR, REAR_DOOR_FR_CA)
    val REAR_WINDOW =
        arrayOf(
            REAR_WINDOW_EN,
            REAR_WINDOW_ES_MX,
            REAR_WINDOW_ES_PR,
            REAR_WINDOW_FR_CA,
        )
    val TAIL_GATE = arrayOf(TAIL_GATE_EN, TAIL_GATE_ES_MX, TAIL_GATE_ES_PR, TAIL_GATE_FR_CA)
    val OTHER = arrayOf(OTHER_EN, OTHER_ES_MX, OTHER_ES_PR, OTHER_FR_CA)

    const val TODAY = "Today at"
    const val YESTERDAY = "Yesterday at"
    const val NA = "N/A"

    fun byLocale(constByLocale: Array<String>): String =
        when (val locale = AppLanguageUtils.getCurrentLocale()) {
            AppLanguageUtils.MEXICO_SPANISH -> constByLocale[1].lowercase(locale = locale)
            AppLanguageUtils.TDPR_SPANISH -> constByLocale[2].lowercase(locale = locale)
            AppLanguageUtils.CANADA_FRANCE -> constByLocale[3].lowercase(locale = locale)
            else -> constByLocale[0].lowercase(locale = locale)
        }
}
