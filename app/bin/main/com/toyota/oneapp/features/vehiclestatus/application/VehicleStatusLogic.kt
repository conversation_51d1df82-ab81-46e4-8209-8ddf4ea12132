package com.toyota.oneapp.features.vehiclestatus.application

import android.text.format.DateUtils
import com.toyota.oneapp.R
import com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel.SectionsResponse
import com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel.TyrePressurePayloadResponse
import com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel.VehicleStatusPayloadResponse
import com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel.VehicleStatusResponse
import com.toyota.oneapp.features.vehiclestatus.dataaccess.servermodel.toUiModel
import com.toyota.oneapp.features.vehiclestatus.domain.model.VehicleDoorsWindowsStatus
import com.toyota.oneapp.features.vehiclestatus.domain.model.VehicleStatusDetails
import com.toyota.oneapp.features.vehiclestatus.domain.repository.VehicleStatusRepository
import com.toyota.oneapp.model.remote.RemoteRequest
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.DateUtil
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.text.SimpleDateFormat
import java.util.Locale
import javax.inject.Inject

class VehicleStatusLogic
    @Inject
    constructor(
        private val repository: VehicleStatusRepository,
        private val dateUtil: DateUtil,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : VehicleStatusUseCase {
        override suspend fun fetchVehicleStatus(vehicleInfo: VehicleInfo): Flow<VehicleStatusDetails> {
            return flow {
                val vehicleStatusDetails = VehicleStatusDetails()
                vehicleStatusDetails.tailGateEnabled = vehicleInfo.isPowerTailGateEnabled
                if (vehicleInfo.remoteDisplay == VehicleInfo.REMOTE_STATUS_ACTIVATED) {
                    val response =
                        if (vehicleInfo.isNG86) {
                            repository.fetchNg86VehicleStatus(
                                vin = vehicleInfo.vin,
                                brand = vehicleInfo.brand,
                            )
                        } else if (vehicleInfo.isCY17) {
                            repository.fetch17CYVehicleStatus(
                                vin = vehicleInfo.vin,
                                brand = vehicleInfo.brand,
                            )
                        } else {
                            repository.fetch17CYPlusVehicleStatus(
                                vin = vehicleInfo.vin,
                                brand = vehicleInfo.brand,
                            )
                        }

                    if (response is Resource.Success && response.data != null) {
                        vehicleStatusDetails.vehicleDoorsWindowsStatus =
                            processVehicleStatusResponse(response.data!!, vehicleInfo)
                    }
                }

                if (vehicleInfo.isFeatureEnabled(Feature.TIRE_PRESSURE)) {
                    val tyrePressureResponse =
                        repository.fetchTyresPressure(
                            vin = vehicleInfo.vin,
                            brand = vehicleInfo.brand,
                            generation = vehicleInfo.generation,
                        )

                    if (tyrePressureResponse is Resource.Success && tyrePressureResponse.data?.payload != null) {
                        tyrePressureResponse.data?.payload?.let {
                            vehicleStatusDetails.tyrePressure =
                                it.toUiModel().apply {
                                    getTyrePressureWarningCount(it).also { count ->
                                        if (count != 0) {
                                            with(this.tyreTileProp) {
                                                primaryIcon = R.drawable.ic_tyre_pressure_alert
                                                primaryIconColorStatus = false
                                                secIcon = R.drawable.ic_small_alert
                                            }
                                        }
                                        this.tyrePressureCount = count
                                        this.tyrePressureTimestamp = parseDate(it.tyrePressureTimestamp)
                                    }
                                }
                        }
                    }
                }
                with(vehicleStatusDetails) {
                    tirePressureFeatureEnabled = vehicleInfo.isFeatureEnabled(Feature.TIRE_PRESSURE)
                    vehicleDoorsWindowsStatus?.apply {
                        if (!doorTileProp.primaryIconColorStatus ||
                            !windowTileProp.primaryIconColorStatus
                        ) {
                            isStatusAlert = true
                        }
                    }
                    tyrePressure?.apply {
                        if (!tyreTileProp.primaryIconColorStatus) {
                            isStatusAlert = true
                        }
                    }
                    if (vehicleInfo.isPowerTailGateEnabled) {
                        vehicleDoorsWindowsStatus?.apply {
                            if (!tailGateTileProp.primaryIconColorStatus) {
                                isStatusAlert = true
                            }
                        }
                    }
                }
                emit(vehicleStatusDetails)
            }
        }

        override suspend fun refreshVehicleStatus(vehicleInfo: VehicleInfo): Flow<Resource<VehicleStatusResponse?>> {
            return flow {
                val request =
                    RemoteRequest().apply {
                        vin = vehicleInfo.vin
                        guid = oneAppPreferenceModel.getGuid()
                        deviceId = oneAppPreferenceModel.getDeviceToken()
                    }
                val response =
                    if (vehicleInfo.isNG86) {
                        repository.refreshNg86VehicleStatus(
                            vin = vehicleInfo.vin,
                            brand = vehicleInfo.brand,
                            request = request,
                        )
                    } else if (vehicleInfo.isCY17) {
                        repository.refresh17CYVehicleStatus(
                            vin = vehicleInfo.vin,
                            brand = vehicleInfo.brand,
                            request = request,
                        )
                    } else {
                        repository.refresh17CYPlusVehicleStatus(
                            vin = vehicleInfo.vin,
                            brand = vehicleInfo.brand,
                            request = request,
                        )
                    }
                emit(response)
            }
        }

        private fun getTyrePressureWarningCount(payload: TyrePressurePayloadResponse): Int {
            var count = 0
            with(payload) {
                if (frTyrePressure?.displayLowTyrePressureWarning == true) {
                    count++
                }
                if (flTyrePressure?.displayLowTyrePressureWarning == true) {
                    count++
                }
                if (rrTyrePressure?.displayLowTyrePressureWarning == true) {
                    count++
                }
                if (rlTyrePressure?.displayLowTyrePressureWarning == true) {
                    count++
                }
            }
            return count
        }

        private fun processVehicleStatusResponse(
            data: VehicleStatusResponse,
            vehicleInfo: VehicleInfo,
        ): VehicleDoorsWindowsStatus {
            val windowsSubtitleList = ArrayList<Int>()
            val doorsSubtitleList = ArrayList<Int>()
            var windowPrimaryColorStatus = true
            var doorPrimaryColorStatus = true
            var tailgatePrimaryColorStatus = true
            var doorUnlocked = false
            var tailGateSubtitle = R.string.VehicleStatus_locked

            /**
             * check for condition ( value is unlocked and status == 1) which means any one of the doors
             * is opened so the vehicle state is unlocked
             */
            val isVehicleNotLocked =
                doorLockStatus(
                    data = data,
                    isTailgateVehicle = vehicleInfo.isPowerTailGateEnabled,
                )

            val doorOpenCount =
                openDoorsWindowsCount(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.DOOR),
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.REAR_DOOR),
                )
            val windowOpenCount =
                openDoorsWindowsCount(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.WINDOW),
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.REAR_WINDOW),
                )
            val hoodOpened =
                isSectionOpened(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.HOOD),
                )
            val moonRoofOpen =
                isSectionOpened(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.MOON_ROOF),
                )
            val tailGateOpen =
                isSectionOpened(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.TAIL_GATE),
                )

            val isVehicleTailGateNotLocked =
                if (vehicleInfo.isPowerTailGateEnabled) {
                    tailGateLockStatus(data = data)
                } else {
                    false
                }

            var trunkOpened = false
            var trunkOrHatch = R.string.VehicleStatus_trunk
            if (!vehicleInfo.isPowerTailGateEnabled) {
                if ((
                        isSectionOpened(
                            data.payload,
                            VehicleStatusConstants.byLocale(VehicleStatusConstants.TRUNK),
                        )
                    ) ||
                    isSectionOpened(
                        data.payload,
                        VehicleStatusConstants.byLocale(VehicleStatusConstants.HATCH),
                    )
                ) {
                    trunkOrHatch =
                        if (getSectionByType(
                                data.payload,
                                VehicleStatusConstants.byLocale(VehicleStatusConstants.TRUNK),
                            ) != null
                        ) {
                            R.string.VehicleStatus_trunk
                        } else {
                            R.string.VehicleStatus_hatch
                        }
                    trunkOpened = true
                }
            }
            doorsSubtitleList.add(R.string.VehicleStatus_locked)

            when {
                isVehicleNotLocked && doorOpenCount == 0 && !hoodOpened && !trunkOpened -> {
                    doorUnlocked = true
                    doorsSubtitleList.clear()
                    doorsSubtitleList.add(R.string.VehicleStatus_unlocked)
                    doorPrimaryColorStatus = false
                }
                isVehicleNotLocked && doorOpenCount == 0 && hoodOpened && !trunkOpened -> {
                    doorsSubtitleList.clear()
                    doorsSubtitleList.add(R.string.VehicleStatus_hood)
                    doorsSubtitleList.add(R.string.VehicleStatus_open)
                    doorPrimaryColorStatus = false
                }
                isVehicleNotLocked && doorOpenCount == 0 && !hoodOpened && trunkOpened -> {
                    doorsSubtitleList.clear()
                    doorsSubtitleList.add(trunkOrHatch)
                    doorsSubtitleList.add(R.string.VehicleStatus_open)
                    doorPrimaryColorStatus = false
                }
                isVehicleNotLocked && doorOpenCount == 0 && hoodOpened && trunkOpened -> {
                    doorsSubtitleList.clear()
                    doorsSubtitleList.add(trunkOrHatch)
                    doorsSubtitleList.add(R.string.plus)
                    doorsSubtitleList.add(R.string.VehicleStatus_hood)
                    doorsSubtitleList.add(R.string.VehicleStatus_open)
                    doorPrimaryColorStatus = false
                }
                doorOpenCount > 0 -> {
                    /**
                     * if any doors,hood and trunk are open display doors open count + hood/trunk.
                     */
                    when {
                        /**
                         * Doors opened, Hood opened and trunk opened.
                         */
                        hoodOpened && trunkOpened -> {
                            doorsSubtitleList.clear()
                            doorsSubtitleList.add(R.string.VehicleStatus_open)
                            doorsSubtitleList.add(R.string.plus)
                            doorsSubtitleList.add(trunkOrHatch)
                            doorsSubtitleList.add(R.string.plus)
                            doorsSubtitleList.add(R.string.VehicleStatus_hood)
                            doorPrimaryColorStatus = false
                        }
                        /**
                         * Doors opened, Hood opened but trunk closed.
                         */
                        hoodOpened && !trunkOpened -> {
                            doorsSubtitleList.clear()
                            doorsSubtitleList.add(R.string.VehicleStatus_open)
                            doorsSubtitleList.add(R.string.plus)
                            doorsSubtitleList.add(R.string.VehicleStatus_hood)
                            doorPrimaryColorStatus = false
                        }
                        /**
                         * Doors opened,Trunk opened but hood closed.
                         */
                        !hoodOpened && trunkOpened -> {
                            doorsSubtitleList.clear()
                            doorsSubtitleList.add(R.string.VehicleStatus_open)
                            doorsSubtitleList.add(R.string.plus)
                            doorsSubtitleList.add(trunkOrHatch)
                            doorPrimaryColorStatus = false
                        }
                        /**
                         *  Doors only opened, hood and trunk are closed.
                         */
                        else -> {
                            doorsSubtitleList.clear()
                            doorsSubtitleList.add(R.string.VehicleStatus_open)
                            doorPrimaryColorStatus = false
                        }
                    }
                }
                doorOpenCount == 0 && hoodOpened && !trunkOpened -> {
                    doorsSubtitleList.clear()
                    doorsSubtitleList.add(R.string.VehicleStatus_hood)
                    doorsSubtitleList.add(R.string.VehicleStatus_open)
                    doorPrimaryColorStatus = false
                }
                doorOpenCount == 0 && !hoodOpened && trunkOpened -> {
                    doorsSubtitleList.clear()
                    doorsSubtitleList.add(trunkOrHatch)
                    doorsSubtitleList.add(R.string.VehicleStatus_open)
                    doorPrimaryColorStatus = false
                }
            }

            val frontDoorDriverSide =
                isSectionOpendWithCategory(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.DOOR),
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.DRIVER_SIDE),
                )
            val backDoorDriverSide =
                isSectionOpendWithCategory(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.REAR_DOOR),
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.DRIVER_SIDE),
                )
            val frontDoorPassengerSide =
                isSectionOpendWithCategory(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.DOOR),
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.PASSENGER_SIDE),
                )
            val backDoorPassengerSide =
                isSectionOpendWithCategory(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.REAR_DOOR),
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.PASSENGER_SIDE),
                )

            val frontWindowDriverSide =
                isSectionOpendWithCategory(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.WINDOW),
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.DRIVER_SIDE),
                )
            val backWindowDriverSide =
                isSectionOpendWithCategory(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.REAR_WINDOW),
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.DRIVER_SIDE),
                )
            val frontWindowPassengerSide =
                isSectionOpendWithCategory(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.WINDOW),
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.PASSENGER_SIDE),
                )
            val backWindowPassengerSide =
                isSectionOpendWithCategory(
                    data.payload,
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.REAR_WINDOW),
                    VehicleStatusConstants.byLocale(VehicleStatusConstants.PASSENGER_SIDE),
                )

            windowsSubtitleList.add(R.string.VehicleStatus_closed)
            /**
             * Possible Conditions for Windows.
             */

            when {
                windowOpenCount > 0 -> {
                    /**
                     *  If any windows opened show the number of windows opened count.
                     */

                    if (moonRoofOpen) {
                        windowsSubtitleList.clear()
                        windowsSubtitleList.add(R.string.VehicleStatus_open)
                        windowsSubtitleList.add(R.string.plus)
                        windowsSubtitleList.add(R.string.VehicleStatus_moonroof)
                    } else {
                        windowsSubtitleList.clear()
                        windowsSubtitleList.add(R.string.VehicleStatus_open)
                    }
                    windowPrimaryColorStatus = false
                }
                windowOpenCount == 0 && moonRoofOpen -> {
                    /**
                     * All windows are closed but only moonroof is open.
                     */

                    windowsSubtitleList.clear()
                    windowsSubtitleList.add(R.string.VehicleStatus_moonroof)
                    windowsSubtitleList.add(R.string.VehicleStatus_open)
                    windowPrimaryColorStatus = false
                }
            }

            var tailGateLockCapable: LockCapable = LockCapable.Hide
            if (tailGateOpen) {
                tailGateLockCapable = LockCapable.Disabled
                tailgatePrimaryColorStatus = false
                tailGateSubtitle = R.string.Common_open
            } else if (isVehicleTailGateNotLocked) {
                tailGateLockCapable = LockCapable.Show
                tailgatePrimaryColorStatus = false
                tailGateSubtitle = R.string.VehicleStatus_unlocked
            }

            return VehicleDoorsWindowsStatus(
                windowCount = windowOpenCount,
                windowSubtitleList = windowsSubtitleList,
                doorCount = doorOpenCount,
                doorSubtitleList = doorsSubtitleList,
                lastUpdatedDate = parseDate(data.payload.occurrenceDate),
                doorLockCapable = if (vehicleInfo.isLockUnLockCapable && doorUnlocked) LockCapable.Show else LockCapable.Hide,
                frontDoorDriverSide = frontDoorDriverSide,
                backDoorDriverSide = backDoorDriverSide,
                frontDoorPassengerSide = frontDoorPassengerSide,
                backDoorPassengerSide = backDoorPassengerSide,
                frontWindowDriverSide = frontWindowDriverSide,
                backWindowDriverSide = backWindowDriverSide,
                frontWindowPassengerSide = frontWindowPassengerSide,
                backWindowPassengerSide = backWindowPassengerSide,
                moonRoofOpen = moonRoofOpen,
                trunkOpened = trunkOpened,
                hoodOpened = hoodOpened,
                tailGateOpened = tailGateOpen,
                tailGateUnlockStatus = isVehicleTailGateNotLocked,
                tailGateLockCapable = tailGateLockCapable,
                tailGateSubTitle = tailGateSubtitle,
            ).also {
                if (!windowPrimaryColorStatus) {
                    with(it.windowTileProp) {
                        primaryIcon = R.drawable.ic_window_open
                        secIcon = R.drawable.ic_small_alert
                        primaryIconColorStatus = false
                    }
                }
                if (!doorPrimaryColorStatus) {
                    with(it.doorTileProp) {
                        primaryIcon = R.drawable.ic_door_unlocked
                        secIcon = R.drawable.ic_small_alert
                        primaryIconColorStatus = false
                    }
                }
                if (!tailgatePrimaryColorStatus) {
                    with(it.tailGateTileProp) {
                        primaryIcon = R.drawable.tailgate_unlocked_icon
                        secIcon = R.drawable.ic_small_alert
                        primaryIconColorStatus = false
                    }
                }
            }
        }

        private fun openDoorsWindowsCount(
            vehicleStatusPayload: VehicleStatusPayloadResponse,
            frontType: String,
            rearType: String,
        ): Int {
            var count = 0
            vehicleStatusPayload.vehicleStatus.forEach { vehicleStatus ->
                vehicleStatus.sections.forEach { section ->
                    if (section.section?.lowercase(Locale.getDefault()) ==
                        frontType.lowercase(
                            Locale.getDefault(),
                        ) ||
                        section.section?.lowercase(Locale.getDefault()) ==
                        rearType.lowercase(
                            Locale.getDefault(),
                        )
                    ) {
                        section.values.forEach {
                            if (it.value?.lowercase(Locale.getDefault())
                                    ?.contains(VehicleStatusConstants.byLocale(VehicleStatusConstants.OPEN)) == true
                            ) {
                                count++
                            }
                        }
                    }
                }
            }
            return count
        }

        private fun isSectionOpened(
            payload: VehicleStatusPayloadResponse,
            type: String,
        ): Boolean {
            var isSectionOpened = false
            getSectionByType(payload, type)?.values?.forEach {
                if (it.value?.lowercase(Locale.getDefault())
                        ?.contains(VehicleStatusConstants.byLocale(VehicleStatusConstants.OPEN)) == true
                ) {
                    isSectionOpened = true
                }
            }

            return isSectionOpened
        }

        private fun getSectionByType(
            payload: VehicleStatusPayloadResponse,
            type: String,
        ): SectionsResponse? {
            payload.vehicleStatus.forEach { vehicleStatus ->
                vehicleStatus.sections.forEach { section ->
                    if (section.section?.lowercase(Locale.getDefault())
                        == type.lowercase(Locale.getDefault())
                    ) {
                        return section
                    }
                }
            }
            return null
        }

        private fun isSectionOpendWithCategory(
            payload: VehicleStatusPayloadResponse,
            sectionType: String,
            categoryType: String,
        ): Boolean {
            var doorOpen = false
            payload.vehicleStatus.forEach { vehicleStatus ->
                if (vehicleStatus.category.equals(categoryType, ignoreCase = true)) {
                    vehicleStatus.sections.forEach { section ->
                        if (section.section?.lowercase() == sectionType.lowercase()) {
                            section.values.forEach { values ->
                                if (values.value?.lowercase(Locale.getDefault())
                                    == VehicleStatusConstants.byLocale(VehicleStatusConstants.OPEN)
                                ) {
                                    doorOpen = true
                                }
                            }
                        }
                    }
                }
            }

            return doorOpen
        }

        private fun parseDate(date: String?): String {
            val exactDateOutputFormat = SimpleDateFormat("MMM dd 'at' h:mm a", Locale.getDefault())
            val yesterdayOrTodayFormat = SimpleDateFormat("h:mm a", Locale.getDefault())
            var updatedDate = VehicleStatusConstants.NA
            dateUtil.getDateFromString(date)?.let {
                updatedDate =
                    when {
                        DateUtils.isToday(it.time) ->
                            "${VehicleStatusConstants.TODAY} ${
                                yesterdayOrTodayFormat.format(
                                    it,
                                )
                            }"
                        DateUtils.isToday(it.time + DateUtils.DAY_IN_MILLIS) ->
                            "${VehicleStatusConstants.YESTERDAY} ${
                                yesterdayOrTodayFormat.format(it)
                            }"
                        else -> {
                            exactDateOutputFormat.format(it)
                        }
                    }
            }
            return updatedDate
        }

        private fun doorLockStatus(
            data: VehicleStatusResponse,
            isTailgateVehicle: Boolean,
        ): Boolean {
            var isVehicleNotLocked = false
            data.payload.vehicleStatus.forEach stop@{ vehicleStatus ->
                if (isTailgateVehicle) {
                    if (vehicleStatus.category?.lowercase() !=
                        VehicleStatusConstants.byLocale(
                            VehicleStatusConstants.OTHER,
                        )
                    ) {
                        vehicleStatus.sections.forEach { section ->
                            section.values.forEach {
                                if (it.value?.lowercase(Locale.getDefault()) ==
                                    VehicleStatusConstants.byLocale(
                                        VehicleStatusConstants.UNLOCKED,
                                    ) && it.status == 1
                                ) {
                                    isVehicleNotLocked = true
                                    return@stop
                                }
                            }
                        }
                    }
                } else {
                    vehicleStatus.sections.forEach { section ->
                        if (section.section?.lowercase(Locale.getDefault()) !=
                            VehicleStatusConstants.byLocale(VehicleStatusConstants.TAIL_GATE).lowercase()
                        ) {
                            section.values.forEach {
                                if (it.value?.lowercase(Locale.getDefault()) ==
                                    VehicleStatusConstants.byLocale(
                                        VehicleStatusConstants.UNLOCKED,
                                    ) && it.status == 1
                                ) {
                                    isVehicleNotLocked = true
                                    return@stop
                                }
                            }
                        }
                    }
                }
            }
            return isVehicleNotLocked
        }

        private fun tailGateLockStatus(data: VehicleStatusResponse): Boolean {
            var isVehicleNotLocked = false
            data.payload.vehicleStatus.forEach stop@{ vehicleStatus ->
                if (vehicleStatus.category?.lowercase() ==
                    VehicleStatusConstants.byLocale(
                        VehicleStatusConstants.OTHER,
                    )
                ) {
                    vehicleStatus.sections.forEach { section ->
                        if (section.section?.lowercase() ==
                            VehicleStatusConstants.byLocale(
                                VehicleStatusConstants.TAIL_GATE,
                            )
                        ) {
                            section.values.forEach {
                                if (it.value?.lowercase(Locale.getDefault()) ==
                                    VehicleStatusConstants.byLocale(
                                        VehicleStatusConstants.UNLOCKED,
                                    ) && it.status == 1
                                ) {
                                    isVehicleNotLocked = true
                                    return@stop
                                }
                            }
                        }
                    }
                }
            }
            return isVehicleNotLocked
        }
    }
