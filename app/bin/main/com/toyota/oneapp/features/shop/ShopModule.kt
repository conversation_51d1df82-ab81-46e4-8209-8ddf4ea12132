package com.toyota.oneapp.features.shop

import com.toyota.oneapp.features.shop.application.ShopLogic
import com.toyota.oneapp.features.shop.application.ShopUseCase
import com.toyota.oneapp.features.shop.dataaccess.repository.ShopDefaultRepo
import com.toyota.oneapp.features.shop.domain.repository.ShopRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class ShopModule {
    @Binds
    abstract fun provideShopRepository(shopDefaultRepository: ShopDefaultRepo): ShopRepo

    @Binds
    abstract fun provideShopUseCase(shopLogic: ShopLogic): ShopUseCase
}
