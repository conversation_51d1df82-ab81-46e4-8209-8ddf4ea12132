package com.toyota.oneapp.features.shop.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class VehicleRadio(
    @SerializedName("detailDescription") val detailDescription: String?,
    @SerializedName("detailImage") val detailImage: String?,
    @SerializedName("detailTitle") val detailTitle: String?,
    @SerializedName("cardDescription") val cardDescription: String?,
    @SerializedName("cardImage") val cardImage: String?,
    @SerializedName("cardTitle") val cardTitle: String?,
    @SerializedName("expiredDate") val expiredDate: String?,
    @SerializedName("linkOutUrl") val linkOutUrl: String?,
    @SerializedName("radioID") val radioID: String,
    @SerializedName("buttonDescription") val buttonDescription: String?,
    @SerializedName("status") val status: String?,
    @SerializedName("deepLinkUrl") val deepLinkUrl: String?,
    @SerializedName("deepLinkButtonDesc") val deepLinkButtonDesc: String?,
    @SerializedName("trialEndDate") val trialEndDate: String?,
)
