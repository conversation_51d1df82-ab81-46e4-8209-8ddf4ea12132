package com.toyota.oneapp.features.shop.dataaccess.servermodel

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Messages(
    @SerializedName("responseCode") val responseCode: String?,
    @SerializedName("description") val description: String?,
    @SerializedName("detailedDescription") val detailedDescription: String?,
) : Parcelable
