package com.toyota.oneapp.features.shop.presentation

import android.annotation.SuppressLint
import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.composable.*
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.shop.application.ShopState
import com.toyota.oneapp.features.shop.domain.model.ShopItems
import com.toyota.oneapp.features.shop.domain.model.ShoptItemsType
import com.toyota.oneapp.util.ToyUtil

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun ShopScreen(
    navHostController: NavHostController,
    viewModel: ShopViewModel = hiltViewModel(),
) {
    val uiState = viewModel.uiState.collectAsState()

    Scaffold(content = {
        ShopComposableShimmer(
            isLoading = (uiState.value == ShopState.Loading),
            contentAfterLoading = {
                with((uiState.value as ShopState.Success).data) {
                    if (this.isEmpty()) {
                        EmptyBottomNavState(resId = R.drawable.ic_shop_not_support_feature)
                    } else {
                        ShopItemList(
                            viewModel,
                            this,
                            navHostController,
                        )
                    }
                }
            },
            modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .background(AppTheme.colors.tertiary12),
        )
    })
}

@Composable
fun ShopItemList(
    viewModel: ShopViewModel,
    shopItems: List<ShopItems>,
    navHostController: NavHostController,
) {
    BaseCompose {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            modifier =
                Modifier
                    .padding(16.dp)
                    .fillMaxHeight(),
        ) {
            items(shopItems) { item ->
                if (item.shoptItemsType == ShoptItemsType.SUBSCRIPTION) {
                    SubscriptionCard(
                        shopItems = item,
                        viewModel = viewModel,
                        navHostController = navHostController,
                    )
                } else {
                    MenuItem(
                        shopItems = item,
                        viewModel = viewModel,
                        navHostController = navHostController,
                    )
                }
            }
        }
    }
}

@Composable
fun SubscriptionCard(
    shopItems: ShopItems,
    viewModel: ShopViewModel,
    navHostController: NavHostController,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    Card(
        backgroundColor = AppTheme.colors.tertiary15,
        elevation = 15.dp,
        shape = RoundedCornerShape(8.dp),
        modifier = modifier.fillMaxWidth(),
    ) {
        Column {
            Row(
                modifier =
                    Modifier.padding(
                        top = 12.dp,
                        start = 16.dp,
                    ),
            ) {
                Surface(
                    shape = CircleShape,
                    color = AppTheme.colors.button02b,
                    modifier =
                        Modifier
                            .size(48.dp),
                ) {
                    Image(
                        modifier =
                            Modifier
                                .padding(12.dp)
                                .testTagID(shopItems.iconTestTagId),
                        painter = painterResource(id = shopItems.imageId),
                        contentDescription = null,
                    )
                }
                Column(
                    modifier =
                        Modifier.padding(
                            top = 12.dp,
                            start = 12.dp,
                        ),
                ) {
                    OABody4TextView(
                        text = context.getString(shopItems.headers),
                        color = AppTheme.colors.tertiary03,
                        modifier =
                            Modifier
                                .padding(all = 0.dp)
                                .testTagID(shopItems.titleTestTagId),
                    )
                }
            }
            Box(
                modifier =
                    Modifier
                        .padding(16.dp)
                        .fillMaxWidth(),
            ) {
                Image(
                    painter = painterResource(id = shopItems.subscriptionBrandImage),
                    contentDescription = LocalContext.current.getString(shopItems.subHeaders),
                    modifier = Modifier.fillMaxWidth(),
                    contentScale = ContentScale.FillWidth,
                )

                Button(
                    onClick = {
                        onShopItemClicked(context, shopItems, viewModel, navHostController)
                    },
                    modifier =
                        Modifier
                            .align(Alignment.BottomCenter)
                            .padding(horizontal = 21.dp, vertical = 16.dp)
                            .testTagID(shopItems.accessibilityId),
                    colors =
                        ButtonDefaults.textButtonColors(
                            backgroundColor = AppTheme.colors.button03a,
                        ),
                    shape = CircleShape,
                ) {
                    OAButtonTextView(
                        stringResource(shopItems.subHeaders),
                        color = AppTheme.colors.button01b,
                        textAlign = TextAlign.Center,
                        modifier =
                            Modifier
                                .padding(horizontal = 4.dp, vertical = 8.dp)
                                .testTagID(shopItems.subTitleTestTagId),
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun MenuItem(
    shopItems: ShopItems,
    viewModel: ShopViewModel,
    navHostController: NavHostController,
) {
    val context = LocalContext.current
    Card(
        backgroundColor = AppTheme.colors.tertiary15,
        shape = RoundedCornerShape(8.dp),
        elevation = 15.dp,
        modifier =
            Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .testTagID(shopItems.accessibilityId),
        onClick = {
            onShopItemClicked(context, shopItems, viewModel, navHostController)
        },
    ) {
        Row {
            if (shopItems.shoptItemsType == ShoptItemsType.SIRIUS_XM) {
                Surface(
                    color = AppTheme.colors.tertiary15,
                ) {
                    Image(
                        modifier =
                            Modifier
                                .size(72.dp)
                                .padding(12.dp)
                                .testTagID(shopItems.iconTestTagId),
                        painter = painterResource(id = shopItems.imageId),
                        contentDescription = null,
                    )
                }
            } else {
                Surface(
                    shape = CircleShape,
                    color = AppTheme.colors.button02b,
                    modifier =
                        Modifier
                            .size(72.dp)
                            .padding(12.dp),
                ) {
                    Image(
                        modifier =
                            Modifier
                                .padding(12.dp)
                                .testTagID(shopItems.iconTestTagId),
                        painter = painterResource(id = shopItems.imageId),
                        contentDescription = null,
                    )
                }
            }
            Column(
                modifier =
                    Modifier.padding(
                        top = 16.dp,
                        bottom = 13.dp,
                    ),
            ) {
                OABody4TextView(
                    text = LocalContext.current.getString(shopItems.headers),
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .padding(all = 0.dp)
                            .testTagID(shopItems.titleTestTagId),
                )
                OACallOut1TextView(
                    text = LocalContext.current.getString(shopItems.subHeaders),
                    color = AppTheme.colors.tertiary05,
                    modifier =
                        Modifier
                            .padding(top = 2.dp)
                            .testTagID(shopItems.subTitleTestTagId),
                )
            }
            if (shopItems.alert) {
                Spacer(
                    Modifier
                        .weight(1f)
                        .fillMaxHeight(),
                )
                Image(
                    modifier =
                        Modifier
                            .padding(top = 13.dp, end = 13.dp)
                            .testTagID(
                                shopItems.alertId,
                            ),
                    painter = painterResource(id = R.drawable.ic_sxm_alert),
                    contentDescription = null,
                )
            }
        }
    }
}

fun onShopItemClicked(
    context: Context,
    shopItems: ShopItems,
    viewModel: ShopViewModel,
    navHostController: NavHostController,
) {
    when (shopItems.shoptItemsType) {
        ShoptItemsType.PARTS_AND_ACCESSORIES -> {
            viewModel.logEventWithParameter(
                AnalyticsEventParam.BOTTOM_NAV_SHOP_TAP,
                AnalyticsEventParam.SHOPTAB_PARTS_ACCESSORIES_TILE_TAP,
            )
            viewModel.vehicleInfo?.getshopGenuinePartsUrl()?.let { url ->
                ToyUtil.openCustomChromeTab(
                    context,
                    url,
                )
            }
        }
        ShoptItemsType.INSURANCE -> {
            viewModel.logEventWithParameter(
                AnalyticsEventParam.BOTTOM_NAV_SHOP_TAP,
                AnalyticsEventParam.SHOPTAB_INSURANCEOFFERS_TILE_TAP,
            )

            navHostController.currentBackStackEntry?.savedStateHandle?.apply {
                set("insuranceDetails", shopItems.ubiOffers)
            }
            navHostController.navigate(OAScreen.Insurance.route)
        }
        ShoptItemsType.SIRIUS_XM -> {
            viewModel.logEventWithParameter(
                AnalyticsEventParam.BOTTOM_NAV_SHOP_TAP,
                AnalyticsEventParam.SHOPTAB_SIRIUS_XM_TILE_TAP,
            )

            navHostController.currentBackStackEntry?.savedStateHandle?.apply {
                set("siriusXmDetails", shopItems.siriusXm)
            }
            navHostController.navigate(OAScreen.SiriusXm.route)
        }
        ShoptItemsType.SUBSCRIPTION -> {
            viewModel.logEventWithParameter(
                AnalyticsEventParam.BOTTOM_NAV_SHOP_TAP,
                AnalyticsEventParam.SHOPTAB_SUBSC_MANAGESUBSCRIPTIONS_CTA,
            )
            navHostController.navigate(OAScreen.Subscriptions.route)
        }
        ShoptItemsType.ANNOUNCEMENT -> {
            viewModel.logEventWithParameter(
                AnalyticsEventParam.BOTTOM_NAV_SHOP_TAP,
                AnalyticsEventParam.SHOPTAB_ANNOUNCEMENT_TILE_TAP,
            )
            navHostController.navigate(OAScreen.AnnouncementList.route)
        }
    }
}
