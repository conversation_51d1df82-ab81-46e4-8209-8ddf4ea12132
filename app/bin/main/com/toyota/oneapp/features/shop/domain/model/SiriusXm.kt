package com.toyota.oneapp.features.shop.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class SiriusXm(
    val status: String,
    val radioId: String,
    val sxmRadioImage: String,
    val vehicleImage: String,
    val cardDescription: String,
    val expiryDate: String?,
    val detailDescription: String,
    val detailTitle: String,
    val buttonDescription: String,
    val deepLinkButtonDesc: String,
    val deepLinkUrl: String,
    val linkOutUrl: String,
) : Parcelable
