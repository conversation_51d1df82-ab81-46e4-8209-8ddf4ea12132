package com.toyota.oneapp.features.shop.dataaccess.repository

import com.toyota.oneapp.features.shop.dataaccess.servermodel.SiriusXmResponse
import com.toyota.oneapp.features.shop.dataaccess.servermodel.UbiOffersResponse
import com.toyota.oneapp.features.shop.dataaccess.service.ShopApi
import com.toyota.oneapp.features.shop.domain.repository.ShopRepo
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class ShopDefaultRepo
    @Inject
    constructor(
        val service: ShopApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : ShopRepo(errorParser, ioContext) {
        override suspend fun fetchUbiOffers(vin: String): Resource<UbiOffersResponse?> {
            return makeApiCall {
                service.fetchUbiOffers(
                    vin = vin,
                )
            }
        }

        override suspend fun fetchSxmAccount(
            vin: String,
            brand: String,
        ): Resource<SiriusXmResponse?> {
            return makeApiCall {
                service.fetchSxmAccount(
                    vin = vin,
                    brand = brand,
                )
            }
        }
    }
