package com.toyota.oneapp.features.shop.domain.model

import com.toyota.oneapp.R

data class ShopItems(
    val headers: Int,
    val subHeaders: Int,
    val imageId: Int,
    val shoptItemsType: ShoptItemsType,
    val accessibilityId: String,
    val priority: Int,
    val iconTestTagId: String,
    val titleTestTagId: String,
    val subTitleTestTagId: String,
    val alertId: String = "",
) {
    var alert = false
    var subscriptionBrandImage = R.drawable.ic_subscription_t
    lateinit var siriusXm: SiriusXm
    lateinit var ubiOffers: UbiOffers
}

enum class ShoptItemsType {
    SUBSCRIPTION,
    INSURANCE,
    PARTS_AND_ACCESSORIES,
    SIRIUS_XM,
    ANNOUNCEMENT,
}
