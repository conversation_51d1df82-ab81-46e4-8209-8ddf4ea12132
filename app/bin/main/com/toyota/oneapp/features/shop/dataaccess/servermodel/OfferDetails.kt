package com.toyota.oneapp.features.shop.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class OfferDetails(
    @SerializedName("seqnum") var seqnum: String,
    @SerializedName("offerid") var offerid: String,
    @SerializedName("offertype") var offertype: String,
    @SerializedName("offerprovider") var offerprovider: String,
    @SerializedName("content") var content: String,
    @SerializedName("secondoptinflag") var secondoptinflag: String,
    @SerializedName("imageUrl") var imageUrl: String,
    @SerializedName("cta") var cta: String,
)
