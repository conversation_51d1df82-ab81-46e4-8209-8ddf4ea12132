package com.toyota.oneapp.features.shop.domain.repository

import com.toyota.oneapp.features.shop.dataaccess.servermodel.SiriusXmResponse
import com.toyota.oneapp.features.shop.dataaccess.servermodel.UbiOffersResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import kotlin.coroutines.CoroutineContext

abstract class ShopRepo(
    errorParser: ErrorMessageParser,
    ioContext: CoroutineContext,
) : BaseRepository(errorParser, ioContext) {
    abstract suspend fun fetchUbiOffers(vin: String): Resource<UbiOffersResponse?>

    abstract suspend fun fetchSxmAccount(
        vin: String,
        brand: String,
    ): Resource<SiriusXmResponse?>
}
