package com.toyota.oneapp.features.shop.dataaccess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.shop.domain.model.SiriusXm

data class SiriusXmResponse(
    @SerializedName("status") val status: Status?,
    @SerializedName("vehicleRadio") val vehicleRadio: VehicleRadio?,
)

fun VehicleRadio.toUIModel(vehicleImage: String): SiriusXm? {
    if (status != null &&
        detailImage != null &&
        cardDescription != null &&
        detailDescription != null &&
        detailTitle != null &&
        buttonDescription != null &&
        deepLinkButtonDesc != null &&
        deepLinkUrl != null &&
        linkOutUrl != null
    ) {
        return SiriusXm(
            status = status,
            radioId = radioID,
            vehicleImage = vehicleImage,
            sxmRadioImage = detailImage,
            cardDescription = cardDescription,
            expiryDate = (
                (
                    if (expiredDate != null) {
                        expiredDate
                    } else if (trialEndDate != null) {
                        trialEndDate
                    } else {
                        null
                    }
                )
            ),
            detailDescription = detailDescription,
            detailTitle = detailTitle,
            buttonDescription = buttonDescription,
            deepLinkButtonDesc = deepLinkButtonDesc,
            deepLinkUrl = deepLinkUrl,
            linkOutUrl = linkOutUrl,
        )
    } else {
        return null
    }
}
