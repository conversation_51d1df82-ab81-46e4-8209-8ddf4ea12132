package com.toyota.oneapp.features.shop.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
class UbiOffers(
    val payload: ArrayList<UbiOffersPayload>,
    val vin: String,
    val guid: String,
    val ubiOptedIn: Boolean,
) : Parcelable

@Parcelize
class UbiOffersPayload(
    var seqnum: String,
    var offerid: String,
    var offertype: String,
    var offerprovider: String,
    var content: String,
    var secondoptinflag: String,
    var imageUrl: String,
    var cta: String,
    var eventName: String,
) : Parcelable
