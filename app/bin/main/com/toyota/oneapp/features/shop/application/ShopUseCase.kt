package com.toyota.oneapp.features.shop.application

import com.toyota.oneapp.features.dashboard.announcement.domain.model.VehicleAnnouncementCardHelper
import com.toyota.oneapp.features.shop.domain.model.ShopItems
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

abstract class ShopUseCase {
    abstract fun fetchUbiOffers(
        vehicleInfo: VehicleInfo,
        shopItemsList: ArrayList<ShopItems>,
    ): Flow<List<ShopItems>>

    abstract fun fetchSxmAccount(
        vehicleInfo: VehicleInfo,
        shopItemsList: ArrayList<ShopItems>,
    ): Flow<List<ShopItems>>

    abstract fun loadLocalShopItems(
        vehicleItem: VehicleInfo,
        shopItemsList: ArrayList<ShopItems>,
    ): List<ShopItems>

    abstract fun loadMarketingBanner(
        vehicleItem: VehicleInfo,
        marketingBanners: MutableList<VehicleAnnouncementCardHelper>,
        shopItemsList: ArrayList<ShopItems>,
    ): List<ShopItems>
}
