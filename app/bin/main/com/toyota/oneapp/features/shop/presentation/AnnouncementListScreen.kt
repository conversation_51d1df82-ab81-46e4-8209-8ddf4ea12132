package com.toyota.oneapp.features.shop.presentation

import android.annotation.SuppressLint
import android.content.Context
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import coil.compose.AsyncImagePainter
import coil.compose.SubcomposeAsyncImage
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.BaseCompose
import com.toyota.oneapp.features.core.composable.CircleLoading
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.ShopComposableShimmer
import com.toyota.oneapp.features.core.composable.VehicleImageNotFoundWithText
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.features.dashboard.announcement.application.AnnouncementState
import com.toyota.oneapp.features.dashboard.announcement.domain.model.AnnouncementCarousalType
import com.toyota.oneapp.features.dashboard.announcement.domain.model.VehicleAnnouncementCardHelper
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.util.ToyUtil

@ExperimentalMaterialApi
@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun AnnouncementListScreen(
    navHostController: NavHostController,
    shopViewModel: ShopViewModel = hiltViewModel(),
) {
    BackHandler {
        navHostController.popBackStack()
    }
    val announcementState = shopViewModel.sharedDataSource.getMarketingBannerState().collectAsState()
    val selectedVehicle = (announcementState.value as AnnouncementState.Success).selectedVehicle

    val topAppBar: @Composable (() -> Unit) = {
        TopAppBar(
            title = {
                ShowPageTitle(selectedVehicle, navHostController)
            },
            backgroundColor = AppTheme.colors.tertiary15,
            elevation = 0.dp,
            modifier =
                Modifier
                    .padding(0.dp)
                    .height(64.dp),
        )
    }

    Scaffold(
        modifier = Modifier.background(AppTheme.colors.tertiary15),
        topBar = {
            topAppBar()
        },
        content = {
            ShopComposableShimmer(
                isLoading = (announcementState.value == AnnouncementState.Loading),
                contentAfterLoading = {
                    with((announcementState.value as AnnouncementState.Success).announcements) {
                        AnnouncementItemList(this)
                    }
                },
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .fillMaxHeight()
                        .background(AppTheme.colors.tertiary15),
            )
        },
    )
}

@Composable
fun AnnouncementItemList(announcementItems: MutableList<VehicleAnnouncementCardHelper>) {
    BaseCompose {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier =
                Modifier
                    .padding(16.dp)
                    .fillMaxHeight(),
        ) {
            items(announcementItems) { item ->
                AnnouncementCard(item)
            }
        }
    }
}

@Composable
fun AnnouncementCard(
    announcementItem: VehicleAnnouncementCardHelper,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    Card(
        backgroundColor = AppTheme.colors.tertiary15,
        elevation = 2.dp,
        shape = RoundedCornerShape(2.dp),
        modifier = modifier.fillMaxWidth(),
    ) {
        Column {
            Box(
                modifier =
                    Modifier
                        .padding(horizontal = 8.dp)
                        .fillMaxWidth()
                        .clickable {
                            handleClickOnCard(context, announcementItem)
                        },
            ) {
                if (announcementItem.showAssetImage) {
                    Image(
                        modifier =
                            Modifier
                                .testTagID(AccessibilityId.ID_ANNOUNCEMENT_LIST_CARD_CTA)
                                .fillMaxWidth()
                                .height(280.dp),
                        painter = painterResource(R.drawable.ic_ca_enroll_bg),
                        contentDescription = stringResource(R.string.image_not_found),
                    )
                } else {
                    LoadImage(
                        announcementItem.imagePath,
                        280,
                        contentScale = ContentScale.FillBounds,
                    )
                }
            }
        }
    }
}

@Composable
private fun getAnnouncementTitle(vehicleItem: VehicleInfo?): String {
    return if (vehicleItem?.isToyotaBrand == true) {
        stringResource(id = R.string.shop_toyota_announcements_title)
    } else {
        stringResource(id = R.string.shop_lexus_announcements_title)
    }
}

@Composable
fun LoadImage(
    url: String,
    height: Int = 280,
    contentScale: ContentScale = ContentScale.Crop,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier.fillMaxWidth(),
    ) {
        if (url.contains(Constants.ImageNotFound, ignoreCase = true)) {
            VehicleImageNotFoundWithText(R.string.announcement_image)
        } else {
            SubcomposeAsyncImage(
                model = url,
                contentDescription = stringResource(R.string.announcement_image),
            ) {
                when (painter.state) {
                    is AsyncImagePainter.State.Loading -> {
                        Box(contentAlignment = Alignment.Center) {
                            CircleLoading(indicatorSize = 50.dp)
                        }
                    }

                    is AsyncImagePainter.State.Error -> {
                        VehicleImageNotFoundWithText(R.string.announcement_image)
                    }

                    else -> {
                        SetIndicatorImage(painter, height, contentScale)
                    }
                }
            }
        }
    }
}

@Composable
private fun SetIndicatorImage(
    painter: AsyncImagePainter,
    height: Int = 280,
    contentScale: ContentScale = ContentScale.Crop,
) {
    Image(
        painter = painter,
        contentDescription =
            stringResource(
                R.string.switcherIndicatorImageDescription,
            ),
        contentScale = contentScale,
        modifier =
            Modifier
                .testTagID(AccessibilityId.ID_ANNOUNCEMENT_LIST_CARD_CTA)
                .fillMaxSize()
                .height(height.dp),
    )
}

@Composable
fun ShowPageTitle(
    vehicleInfo: VehicleInfo,
    navHostController: NavHostController,
) {
    Box(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column {
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
            ) {
                Surface(
                    shape = CircleShape,
                    color = AppTheme.colors.button02d,
                    modifier =
                        Modifier
                            .size(48.dp),
                ) {
                    Image(
                        modifier =
                            Modifier
                                .clickable {
                                    navHostController.popBackStack()
                                }
                                .padding(
                                    start = 19.dp,
                                    end = 22.dp,
                                    top = 17.dp,
                                    bottom = 17.dp,
                                )
                                .testTagID(AccessibilityId.ID_ANNOUNCEMENT_LIST_BACK_CTA),
                        painter = painterResource(id = R.drawable.ic_back_arrow),
                        colorFilter = ColorFilter.tint(color = AppTheme.colors.tertiary03),
                        contentDescription = stringResource(id = R.string.Common_back),
                    )
                }
                Column(
                    modifier =
                        Modifier.padding(
                            start = 5.dp,
                            top = 7.dp,
                        ),
                ) {
                    OASubHeadLine3TextView(
                        text = getAnnouncementTitle(vehicleInfo),
                        color = AppTheme.colors.tertiary03,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 4.dp)
                                .testTagID(AccessibilityId.ID_ANNOUNCEMENT_LIST_TITLE),
                    )
                }
            }
        }
    }
}

fun handleClickOnCard(
    context: Context,
    announcement: VehicleAnnouncementCardHelper,
) {
    if (announcement.announcementCarousalType.ordinal == AnnouncementCarousalType.MARKETING.ordinal) {
        announcement.marketingTargetUrl?.let {
            ToyUtil.openCustomChromeTab(context, it)
        }
    }
}
