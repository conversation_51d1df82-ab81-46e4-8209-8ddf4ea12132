package com.toyota.oneapp.features.shop.dataaccess.service

import com.toyota.oneapp.features.shop.dataaccess.servermodel.SiriusXmResponse
import com.toyota.oneapp.features.shop.dataaccess.servermodel.UbiOffersResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header

interface ShopApi {
    @GET("/oneapi/v2/ubi/offers")
    suspend fun fetchUbiOffers(
        @Header("vin") vin: String,
    ): Response<UbiOffersResponse?>

    @GET("/oneapi/v1/radio")
    suspend fun fetchSxmAccount(
        @Header("vin") vin: String,
        @Header("brand") brand: String,
    ): Response<SiriusXmResponse?>
}
