package com.toyota.oneapp.features.shop.presentation

import android.annotation.SuppressLint
import android.content.Context
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.composable.*
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.shop.application.isSxmActive
import com.toyota.oneapp.features.shop.domain.model.SiriusXm
import com.toyota.oneapp.util.ToyUtil

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun SiriusXmScreen(
    navHostController: NavHostController,
    viewModel: ShopViewModel = hiltViewModel(),
) {
    BackHandler {
        navHostController.popBackStack()
    }

    val siriusXmDetails =
        navHostController.previousBackStackEntry?.savedStateHandle?.get<SiriusXm>(
            "siriusXmDetails",
        )
    val context = LocalContext.current

    Scaffold(
        content = {
            Column(
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .background(AppTheme.colors.tertiary12)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                siriusXmDetails?.let {
                    NavTitleSection(
                        navHostController,
                        context.getString(R.string.sirius_xm),
                        AccessibilityId.ID_SXM_BACK_ARROW_CTA,
                    )
                    RadioId(context, it)
                    Spacer(modifier = Modifier.height(24.dp))
                    LoadVehicleImage(
                        it.vehicleImage,
                        AccessibilityId.ID_SHOP_SIRIUS_XM_VEHICLE_DISPLAY_IMAGE,
                        R.string.sirius_xm,
                    )
                    detailsCardSection(it, viewModel)
                }
            }
        },
    )
}

@Composable
fun RadioId(
    context: Context,
    siriusXmDetails: SiriusXm,
) {
    Row(
        modifier =
            Modifier
                .padding(top = 2.dp)
                .fillMaxWidth(),
    ) {
        OACallOut1TextView(
            text = context.getString(R.string.radio),
            modifier = Modifier.weight(0.70f),
            textAlign = TextAlign.End,
            color = AppTheme.colors.tertiary03,
        )

        OACallOut2TextView(
            text = siriusXmDetails.radioId,
            modifier =
                Modifier
                    .weight(1f)
                    .padding(start = 6.dp),
            textAlign = TextAlign.Start,
            color = AppTheme.colors.tertiary03,
        )
    }
}

@Composable
fun detailsCardSection(
    siriusXmDetails: SiriusXm,
    viewModel: ShopViewModel,
) {
    val context = LocalContext.current

    Card(
        backgroundColor = AppTheme.colors.tertiary15,
        elevation = 6.dp,
        shape = RoundedCornerShape(8.dp),
        modifier = Modifier.padding(start = 16.dp, end = 18.dp, bottom = 8.dp),
    ) {
        Column {
            loadRemoteImage(
                siriusXmDetails.sxmRadioImage,
            )
            OASubHeadLine1TextView(
                modifier =
                    Modifier
                        .padding(top = 24.dp)
                        .fillMaxWidth(),
                text = siriusXmDetails.detailTitle,
                textAlign = TextAlign.Center,
                color = AppTheme.colors.tertiary03,
            )

            Row(
                modifier =
                    Modifier
                        .padding(start = 22.dp, end = 22.dp, top = 8.dp),
                horizontalArrangement = Arrangement.Start,
            ) {
                OAButtonTextView(
                    text = context.getString(R.string.sxm_status),
                    color = AppTheme.colors.tertiary03,
                )
                OACallOut1TextView(
                    text = siriusXmDetails.status,
                    modifier =
                        Modifier
                            .padding(start = 12.dp),
                    color = AppTheme.colors.tertiary05,
                )
                Row(
                    horizontalArrangement = Arrangement.End,
                    modifier =
                        Modifier
                            .weight(1f),
                ) {
                    OAButtonTextView(
                        text = context.getString(R.string.sxm_expires),
                        modifier =
                            Modifier
                                .padding(end = 12.dp),
                        color = AppTheme.colors.tertiary03,
                    )
                    siriusXmDetails.expiryDate?.let {
                        OACallOut1TextView(
                            text = it,
                            color = AppTheme.colors.tertiary05,
                        )
                    }
                }
            }

            OABody1TextView(
                modifier =
                    Modifier
                        .padding(start = 16.dp, top = 8.dp, end = 16.dp)
                        .fillMaxWidth(),
                text = siriusXmDetails.detailDescription,
                textAlign = TextAlign.Center,
                color = AppTheme.colors.tertiary05,
            )

            TextButton(
                modifier =
                    Modifier
                        .padding(top = 34.dp)
                        .align(CenterHorizontally)
                        .testTagID(AccessibilityId.ID_SXM_DOWNLOAD_SXM_APP),
                onClick = {
                    viewModel.logEventWithParameter(
                        AnalyticsEventParam.BOTTOM_NAV_SHOP_TAP,
                        AnalyticsEventParam.SHOPTAB_DOWNLOADSXMAPP_CTA,
                    )
                    ToyUtil.openCustomChromeTab(
                        context,
                        siriusXmDetails.deepLinkUrl,
                    )
                },
            ) {
                OABody1TextView(
                    siriusXmDetails.deepLinkButtonDesc,
                    textAlign = TextAlign.Center,
                    color = AppTheme.colors.button02a,
                )
            }

            Box(
                modifier =
                    Modifier
                        .padding(top = 16.dp, bottom = 24.dp)
                        .align(CenterHorizontally),
            ) {
                Button(
                    modifier = Modifier.testTagID(AccessibilityId.ID_SXM_LEARN_MORE_SUBSCRIBE_CTA),
                    onClick = {
                        if (siriusXmDetails.status.isSxmActive()) {
                            viewModel.logEventWithParameter(
                                AnalyticsEventParam.BOTTOM_NAV_SHOP_TAP,
                                AnalyticsEventParam.SHOPTAB_DOWNLOADSXMAPP_CTA,
                            )
                        } else {
                            viewModel.logEventWithParameter(
                                AnalyticsEventParam.BOTTOM_NAV_SHOP_TAP,
                                AnalyticsEventParam.SHOPTAB_SUBSC_MANAGESUBSCRIPTIONS_CTA,
                            )
                        }
                        ToyUtil.openCustomChromeTab(
                            context,
                            siriusXmDetails.linkOutUrl,
                        )
                    },
                    colors =
                        ButtonDefaults.buttonColors(
                            backgroundColor = AppTheme.colors.primaryButton02,
                        ),
                    shape = RoundedCornerShape(48.dp),
                    contentPadding = PaddingValues(vertical = 16.dp, horizontal = 60.dp),
                ) {
                    OATextLink1TextView(
                        text = siriusXmDetails.buttonDescription,
                        color = AppTheme.colors.primaryButton01,
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
fun loadRemoteImage(url: String) {
    GlideImage(
        model = url,
        contentDescription = "",
        contentScale = ContentScale.Crop,
        modifier = Modifier.height(130.dp),
    )
}
