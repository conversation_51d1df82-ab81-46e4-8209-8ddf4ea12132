package com.toyota.oneapp.features.shop.application

import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dashboard.announcement.domain.model.VehicleAnnouncementCardHelper
import com.toyota.oneapp.features.shop.dataaccess.servermodel.OfferDetails
import com.toyota.oneapp.features.shop.dataaccess.servermodel.toUIModel
import com.toyota.oneapp.features.shop.domain.model.ShopItems
import com.toyota.oneapp.features.shop.domain.model.ShoptItemsType
import com.toyota.oneapp.features.shop.domain.repository.ShopRepo
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class ShopLogic
    @Inject
    constructor(
        private val repository: ShopRepo,
        private val analyticsLogger: AnalyticsLogger,
    ) : ShopUseCase() {
        override fun fetchUbiOffers(
            vehicleInfo: VehicleInfo,
            shopItemsList: ArrayList<ShopItems>,
        ): Flow<List<ShopItems>> =
            flow {
                if (vehicleInfo.isFeatureEnabled(Feature.INSURANCE)) {
                    val response =
                        repository.fetchUbiOffers(
                            vin = vehicleInfo.vin,
                        )
                    when (response) {
                        is Resource.Success -> {
                            val ubiOffers = response.data?.payload?.toUIModel()
                            ubiOffers?.let {
                                if (it.payload.isNotEmpty()) {
                                    shopItemsList.add(
                                        ShopItems(
                                            R.string.insurance_offers,
                                            R.string.view_your_offers,
                                            R.drawable.ic_insurance,
                                            ShoptItemsType.INSURANCE,
                                            AccessibilityId.ID_INSURANCE_CARD_CTA,
                                            3,
                                            AccessibilityId.ID_SHOP_INSURANCE_ICON,
                                            AccessibilityId.ID_SHOP_INSURANCE_TITLE,
                                            AccessibilityId.ID_SHOP_INSURANCE_TITLE_SUB_TITLE,
                                        ).apply {
                                            this.ubiOffers = ubiOffers
                                        },
                                    )
                                    analyticsLogger.logEvent(AnalyticsEvent.UBI_OFFER_GET_QUOTE)
                                    analyticsLogger.logEvent(AnalyticsEvent.UBI_OFFER_GET_QUOTE_SUCCESS)
                                } else {
                                    analyticsLogger.logEvent(
                                        AnalyticsEvent.UBI_OFFER_GET_QUOTE_UN_AVAILABLE,
                                    )
                                }
                            }
                        }
                        is Resource.Failure -> {
                            analyticsLogger.logEvent(AnalyticsEvent.UBI_OFFER_GET_QUOTE_UN_AVAILABLE)
                        }
                        else -> {}
                    }
                }
                emit(shopItemsList.toSort())
            }

        override fun fetchSxmAccount(
            vehicleInfo: VehicleInfo,
            shopItemsList: ArrayList<ShopItems>,
        ): Flow<List<ShopItems>> =
            flow {
                if (vehicleInfo.isFeatureEnabled(Feature.SXM_RADIO)) {
                    val response =
                        repository.fetchSxmAccount(
                            vin = vehicleInfo.vin,
                            brand = vehicleInfo.brand,
                        )
                    if (response is Resource.Success) {
                        val siriusXm = response.data?.vehicleRadio?.toUIModel(vehicleInfo.image)
                        siriusXm?.let {
                            shopItemsList.add(
                                ShopItems(
                                    R.string.sirius_xm,
                                    if (it.status.isSxmActive()) {
                                        R.string.account_status_active
                                    } else {
                                        R.string.account_status_inactive
                                    },
                                    R.drawable.ic_sirius_xm,
                                    ShoptItemsType.SIRIUS_XM,
                                    AccessibilityId.ID_SXM_CARD_CTA,
                                    4,
                                    AccessibilityId.ID_SHOP_SIRIUS_XM_ICON,
                                    AccessibilityId.ID_SHOP_SIRIUS_XM_TITLE,
                                    AccessibilityId.ID_SHOP_SIRIUS_XM_SUB_TITLE,
                                    AccessibilityId.ID_SHOP_SIRIUS_XM_ALERT,
                                ).apply {
                                    if (!it.status.isSxmActive()) {
                                        this.alert = true
                                    }
                                    this.siriusXm = siriusXm
                                },
                            )
                            analyticsLogger.logEventWithParameter(
                                AnalyticsEventParam.SXM_RADIO,
                                AnalyticsEventParam.SIRIUS_XM_DISPLAYED,
                            )
                        }
                    }
                }
                emit(
                    shopItemsList.toSort(),
                )
            }

        override fun loadLocalShopItems(
            vehicleItem: VehicleInfo,
            shopItemsList: ArrayList<ShopItems>,
        ): List<ShopItems> {
            /**
             * SUBSCRIPTION ITEM
             */
            if (vehicleItem.isConnectedVehicle) {
                shopItemsList.add(
                    ShopItems(
                        headers = R.string.Subscription_subscriptions,
                        subHeaders = R.string.manage_subscription,
                        imageId = R.drawable.ic_small_subscription,
                        shoptItemsType = ShoptItemsType.SUBSCRIPTION,
                        accessibilityId = AccessibilityId.ID_MANAGE_SUBSCRIPTION_CARD_CTA,
                        priority = 1,
                        iconTestTagId = AccessibilityId.ID_SHOP_SUBSCRIPTION_ICON,
                        titleTestTagId = AccessibilityId.ID_SHOP_SUBSCRIPTION_TITLE,
                        subTitleTestTagId = AccessibilityId.ID_SHOP_SUBSCRIPTION_SUB_TITLE,
                    ).apply {
                        this.subscriptionBrandImage =
                            when (vehicleItem.brand) {
                                VehicleInfo.BRAND_TOYOTA -> R.drawable.ic_subscription_t
                                VehicleInfo.BRAND_LEXUS -> R.drawable.ic_subscription_l
                                VehicleInfo.BRAND_SUBARU -> R.drawable.ic_subscription_s
                                else -> R.drawable.ic_subscription_t
                            }
                    },
                )
            }

            /**
             * PART AND ACCESSORIES
             */
            if (vehicleItem.isFeatureEnabled(Feature.SHOP_GENUINE_PARTS) &&
                vehicleItem.getshopGenuinePartsUrl() != null
            ) {
                shopItemsList.add(
                    ShopItems(
                        R.string.part_accessories,
                        R.string.shop_genuine_part,
                        R.drawable.ic_parts_accessories,
                        ShoptItemsType.PARTS_AND_ACCESSORIES,
                        AccessibilityId.ID_PARTS_ACCESSORIES_CARD_CTA,
                        2,
                        AccessibilityId.ID_SHOP_PARTS_ACCESSORIES_ICON,
                        AccessibilityId.ID_SHOP_PARTS_ACCESSORIES_TITLE,
                        AccessibilityId.ID_SHOP_PARTS_ACCESSORIES_SUB_TITLE,
                    ),
                )
            }
            return shopItemsList.toSort()
        }

        override fun loadMarketingBanner(
            vehicleItem: VehicleInfo,
            marketingBanners: MutableList<VehicleAnnouncementCardHelper>,
            shopItemsList: ArrayList<ShopItems>,
        ): List<ShopItems> {
            shopItemsList.add(
                ShopItems(
                    headers =
                        if (vehicleItem.isToyotaBrand) {
                            R.string.shop_toyota_announcements_title
                        } else {
                            R.string.shop_lexus_announcements_title
                        },
                    subHeaders =
                        if (vehicleItem.isToyotaBrand) {
                            R.string.shop_toyota_announcements_sub_title
                        } else {
                            R.string.shop_lexus_announcements_sub_title
                        },
                    imageId = R.drawable.ic_announcements,
                    shoptItemsType = ShoptItemsType.ANNOUNCEMENT,
                    accessibilityId = AccessibilityId.ID_ANNOUNCEMENT_CARD_CTA,
                    priority = 3,
                    iconTestTagId = AccessibilityId.ID_SHOP_ANNOUNCEMENT_ICON,
                    titleTestTagId = AccessibilityId.ID_SHOP_ANNOUNCEMENT_TITLE,
                    subTitleTestTagId = AccessibilityId.ID_SHOP_ANNOUNCEMENT_SUB_TITLE,
                ),
            )
            return shopItemsList.toSort()
        }
    }

fun String?.isSxmActive(): Boolean = this?.lowercase().equals("active")

fun ArrayList<ShopItems>.toSort(): List<ShopItems> = this.sortedBy { it.priority }

fun OfferDetails.insuranceEventName(): String =
    "shoptab_ins${this.offerprovider.filter { !it.isWhitespace() }}${this.seqnum}${this.cta.filter { !it.isWhitespace() }}"
