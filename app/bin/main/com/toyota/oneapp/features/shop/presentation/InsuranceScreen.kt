package com.toyota.oneapp.features.shop.presentation

import android.annotation.SuppressLint
import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.composable.*
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.shop.domain.model.UbiOffers
import com.toyota.oneapp.features.shop.domain.model.UbiOffersPayload
import com.toyota.oneapp.ui.ubi.SpecialOfferGetQuoteActivity

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun InsuranceScreen(
    navHostController: NavHostController,
    viewModel: ShopViewModel = hiltViewModel(),
) {
    BackHandler {
        navHostController.popBackStack()
    }

    val ubiOffers =
        navHostController.previousBackStackEntry?.savedStateHandle?.get<UbiOffers>(
            "insuranceDetails",
        )

    val contex = LocalContext.current

    Scaffold(
        content = {
            Column(
                modifier = Modifier.background(AppTheme.colors.tertiary12),
            ) {
                ubiOffers?.let {
                    NavTitleSection(
                        navHostController,
                        contex.getString(R.string.insurance_offers),
                        AccessibilityId.ID_INSURANCE_BACK_ARROW_CTA,
                    )
                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(16.dp),
                        modifier =
                            Modifier
                                .padding(top = 11.dp)
                                .fillMaxHeight(),
                    ) {
                        items(ubiOffers.payload) { offers ->
                            offerSection(offers, ubiOffers.vin, viewModel)
                        }
                    }
                }
            }
        },
    )
}

@Composable
fun offerSection(
    offers: UbiOffersPayload,
    vin: String?,
    viewModel: ShopViewModel,
) {
    val context = LocalContext.current

    Card(
        backgroundColor = AppTheme.colors.tertiary15,
        elevation = 6.dp,
        shape = RoundedCornerShape(8.dp),
        modifier = Modifier.padding(start = 16.dp, end = 16.dp),
    ) {
        Column {
            loadOfferImage(offers.imageUrl, offers.offerid)

            OASubHeadLine2TextView(
                text = offers.offerprovider,
                color = AppTheme.colors.tertiary03,
                textAlign = TextAlign.Center,
                modifier =
                    Modifier
                        .padding(top = 10.dp)
                        .fillMaxWidth(),
            )
            OACallOut1TextView(
                text = offers.content,
                color = AppTheme.colors.tertiary05,
                modifier =
                    Modifier
                        .padding(start = 16.dp, top = 8.dp, end = 16.dp)
                        .fillMaxWidth(),
                textAlign = TextAlign.Center,
            )

            Box(
                modifier =
                    Modifier
                        .padding(top = 24.dp, bottom = 36.dp)
                        .align(CenterHorizontally),
            ) {
                Button(
                    modifier =
                        Modifier.testTagID(
                            AccessibilityId.ID_INSURANCE_LEARN_MORE_GET_QUOATE_CTA,
                        ),
                    onClick = {
                        viewModel.logEventWithParameter(
                            AnalyticsEventParam.BOTTOM_NAV_SHOP_TAP,
                            offers.eventName,
                        )

                        context.startActivity(
                            Intent(context, SpecialOfferGetQuoteActivity::class.java)
                                .putExtra("vin", vin)
                                .putExtra("timsOfferId", offers.offerid)
                                .putExtra("learnMoreCtaEvent", offers.eventName),
                        )
                    },
                    colors =
                        ButtonDefaults.buttonColors(
                            backgroundColor = AppTheme.colors.primaryButton02,
                        ),
                    shape = RoundedCornerShape(48.dp),
                    contentPadding = PaddingValues(vertical = 16.dp, horizontal = 60.dp),
                ) {
                    OATextLink1TextView(text = offers.cta, color = AppTheme.colors.primaryButton01)
                }
            }
        }
    }
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
fun loadOfferImage(
    url: String?,
    content: String?,
) {
    GlideImage(
        model = url,
        contentDescription = content,
        contentScale = ContentScale.Crop,
    )
}
