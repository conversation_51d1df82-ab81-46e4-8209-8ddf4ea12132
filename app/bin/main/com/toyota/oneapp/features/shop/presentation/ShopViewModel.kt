package com.toyota.oneapp.features.shop.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.dashboard.announcement.application.AnnouncementState
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.features.shop.application.ShopState
import com.toyota.oneapp.features.shop.application.ShopUseCase
import com.toyota.oneapp.features.shop.domain.model.ShopItems
import com.toyota.oneapp.ui.BaseFragmentViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class ShopViewModel
    @Inject
    constructor(
        private val shopUseCase: ShopUseCase,
        private val appData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        private val dispatcherProvider: DispatcherProvider,
        internal val sharedDataSource: SharedDataSource,
    ) : BaseFragmentViewModel() {
        private val shopItems = ArrayList<ShopItems>()
        private val _uiState = MutableStateFlow<ShopState>(value = ShopState.Loading)
        val uiState = _uiState.asStateFlow()
        val vehicleInfo = appData.getSelectedVehicle()

        init {
            if (vehicleInfo != null) {
                _uiState.value = ShopState.Loading
                _uiState.value =
                    ShopState.Success(
                        shopUseCase.loadLocalShopItems(vehicleInfo, shopItems),
                    )
                updateMarketingBannerTile()
                fetchInsuranceOffers()
                fetchSxnDetails()
            }
        }

        fun fetchInsuranceOffers() {
            _uiState.value = ShopState.Loading
            appData.getSelectedVehicle()?.let {
                viewModelScope.launch(dispatcherProvider.main()) {
                    try {
                        shopUseCase.fetchUbiOffers(it, shopItems)
                            .flowOn(dispatcherProvider.io()).collect {
                                _uiState.value = ShopState.Success(it)
                            }
                    } catch (e: Exception) {
                        analyticsLogger.logEvent(AnalyticsEvent.UPI_OFFER_GET_QUOTE_UNSUCCESSFUL)
                    }
                }
            }
        }

        fun fetchSxnDetails() {
            appData.getSelectedVehicle()?.let {
                _uiState.value = ShopState.Loading
                viewModelScope.launch(dispatcherProvider.main()) {
                    shopUseCase.fetchSxmAccount(it, shopItems)
                        .flowOn(dispatcherProvider.io()).collect {
                            _uiState.value = ShopState.Success(it)
                        }
                }
            }
        }

        private fun updateMarketingBannerTile() {
            appData.getSelectedVehicle()?.let { vehicleInfo ->
                viewModelScope.launch(dispatcherProvider.main()) {
                    sharedDataSource.getMarketingBannerState().collect {
                        if (it is AnnouncementState.Success && it.announcements.isNotEmpty()) {
                            val list =
                                shopUseCase.loadMarketingBanner(
                                    vehicleInfo,
                                    it.announcements,
                                    shopItems,
                                )
                            _uiState.value = ShopState.Success(list)
                        }
                    }
                }
            }
        }

        fun logEventWithParameter(
            group: String,
            event: String,
        ) {
            analyticsLogger.logEventWithParameter(group, event)
        }
    }
