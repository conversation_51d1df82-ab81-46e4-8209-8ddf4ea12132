package com.toyota.oneapp.features.shop.dataaccess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.shop.application.insuranceEventName
import com.toyota.oneapp.features.shop.domain.model.UbiOffers
import com.toyota.oneapp.features.shop.domain.model.UbiOffersPayload

data class UbiOffersResponse(
    @SerializedName("payload") val payload: Payload?,
    @SerializedName("status") val status: Status,
    @SerializedName("timestamp") val timestamp: String?,
)

fun Payload.toUIModel(): UbiOffers {
    val data = ArrayList<UbiOffersPayload>()
    offerDetails.forEach {
        data.add(
            UbiOffersPayload(
                seqnum = it.seqnum,
                offerid = it.offerid,
                offertype = it.offertype,
                offerprovider = it.offerprovider,
                content = it.content,
                secondoptinflag = it.secondoptinflag,
                imageUrl = it.imageUrl,
                cta = it.cta,
                eventName = it.insuranceEventName(),
            ),
        )
    }
    return UbiOffers(
        payload = data,
        vin = vin,
        guid = guid,
        ubiOptedIn = ubiOptedIn,
    )
}
