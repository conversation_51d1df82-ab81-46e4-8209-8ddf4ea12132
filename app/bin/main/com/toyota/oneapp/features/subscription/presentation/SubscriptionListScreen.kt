package com.toyota.oneapp.features.subscription.presentation

import android.annotation.SuppressLint
import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.lerp
import androidx.compose.ui.unit.toSize
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.ComposableListShimmer
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OACaption2TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.CommonDimensions
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.subscription.application.SubscriptionState
import com.toyota.oneapp.features.subscription.domain.model.SubscriptionItem
import com.toyota.oneapp.features.subscription.domain.model.SubscriptionList
import com.toyota.oneapp.ui.garage.subscriptionV2.SubscriptionV2Activity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

private val headerHeight = 300.dp
private val toolbarHeight = 80.dp
private val topAppBarHeight = 64.dp

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@ExperimentalMaterialApi
@Composable
fun SubscriptionListScreen(
    bottomSheetState: ModalBottomSheetState? = null,
    navHostController: NavHostController? = null,
    viewModel: SubscriptionListViewModel =
        hiltViewModel(
            LocalContext.current as OADashboardActivity,
        ),
) {
    val coroutineScope = rememberCoroutineScope()
    val infoBottomSheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
        )

    BackHandler {
        coroutineScope.launch {
            if (bottomSheetState != null && bottomSheetState.isVisible) {
                bottomSheetState.hide()
            } else {
                navHostController?.popBackStack()
            }
        }
    }
    val subscriptionState =
        viewModel.sharedDataSource
            .getSubscriptionState()
            .collectAsState()
            .value

    ModalBottomSheetLayout(
        sheetState = infoBottomSheetState,
        sheetElevation = 8.dp,
        sheetShape = RoundedCornerShape(30.dp),
        sheetContent = {
            viewModel.applicationData.getSelectedVehicle()?.brand?.let {
                DisclaimerContent(
                    vehicleBrand = it,
                    modalSheetState = infoBottomSheetState,
                    coroutineScope = coroutineScope,
                )
            }
        },
    ) {
        Scaffold {
            ComposableListShimmer(
                isLoading = subscriptionState == SubscriptionState.Loading,
                contentAfterLoading = {
                    LoadSubscriptionContent(
                        subscriptionState = subscriptionState,
                        viewModel = viewModel,
                        navHostController = navHostController,
                        bottomSheetState = bottomSheetState,
                        infoBottomSheetState = infoBottomSheetState,
                    )
                },
            )
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun LoadSubscriptionContent(
    subscriptionState: SubscriptionState,
    viewModel: SubscriptionListViewModel,
    navHostController: NavHostController?,
    bottomSheetState: ModalBottomSheetState?,
    infoBottomSheetState: ModalBottomSheetState,
) {
    val coroutineScope = rememberCoroutineScope()

    when (subscriptionState) {
        is SubscriptionState.Success -> {
            CollapsingToolbarParallaxEffect(
                navHostController = navHostController,
                bottomSheetState = bottomSheetState,
                infoBottomSheetState = infoBottomSheetState,
                scope = coroutineScope,
                viewModel = viewModel,
                uiModel = subscriptionState.data,
            )
        }
        is SubscriptionState.NoSubscriptions -> {
            val currentVehicle = viewModel.applicationData.getSelectedVehicle()
            NoSubscriptionScreen(
                vehicleImage = subscriptionState.data.vehicleImage,
                title = stringResource(id = subscriptionState.data.title),
                subTitle =
                    if (currentVehicle?.isVehicleCanadian == true && currentVehicle.isOnlyCY17) {
                        stringResource(id = subscriptionState.data.subTitle)
                    } else {
                        String.format(
                            stringResource(id = subscriptionState.data.subTitle),
                            "${currentVehicle?.modelYear} ${currentVehicle?.modelName}",
                        )
                    },
                viewModel = viewModel,
                isActionBtnVisible = subscriptionState.data.isActionBtnVisible,
            )
        }
        is SubscriptionState.Error -> {
            NoSubscriptionScreen(
                vehicleImage = subscriptionState.vehicleImage,
                title =
                    stringResource(
                        id = subscriptionState.errorTitle,
                    ),
                subTitle =
                    stringResource(
                        id = subscriptionState.errorMessage,
                    ),
                viewModel = viewModel,
            )
        }
        else -> {
            // Do nothing.
        }
    }
}

@ExperimentalMaterialApi
@Composable
fun CollapsingToolbarParallaxEffect(
    bottomSheetState: ModalBottomSheetState?,
    navHostController: NavHostController?,
    infoBottomSheetState: ModalBottomSheetState,
    scope: CoroutineScope,
    viewModel: SubscriptionListViewModel,
    uiModel: SubscriptionList,
) {
    val scroll: ScrollState = rememberScrollState(0)
    var screenSize by remember { mutableStateOf(Size.Zero) }

    val headerHeightPx = with(LocalDensity.current) { headerHeight.toPx() }
    val toolbarHeightPx = with(LocalDensity.current) { toolbarHeight.toPx() }

    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(AppTheme.colors.tertiary15)
                .onGloballyPositioned {
                    screenSize = it.size.toSize()
                },
    ) {
        Body(viewModel, uiModel, scroll)
        Header(
            navHostController,
            uiModel.canShowDisclaimer,
            scroll,
            bottomSheetState,
            infoBottomSheetState,
            scope,
        )
        Toolbar(scroll, headerHeightPx, toolbarHeightPx)
        VehicleImage(uiModel.vehicleImage, scroll, headerHeightPx, screenSize)
    }
}

@ExperimentalMaterialApi
@Composable
private fun Header(
    navHostController: NavHostController?,
    canShowDisclaimer: Boolean,
    scroll: ScrollState,
    bottomSheetState: ModalBottomSheetState?,
    infoBottomSheetState: ModalBottomSheetState,
    scope: CoroutineScope,
) {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(start = 16.dp, top = 10.dp, end = 16.dp)
                .graphicsLayer {
                    translationY = -scroll.value.toFloat() / 2f // Parallax effect
                },
    ) {
        Box(
            modifier =
                Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(AppTheme.colors.button02d)
                    .clickable {
                        scope.launch {
                            if (bottomSheetState?.isVisible == true) {
                                bottomSheetState.hide()
                            } else {
                                navHostController?.popBackStack()
                            }
                        }
                    }.testTagID(AccessibilityId.ID_SUBSCRIPTION_BACK_BTN),
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_back_arrow),
                contentDescription = stringResource(id = R.string.Common_back),
                modifier =
                    Modifier
                        .layoutId(AccessibilityId.ID_VEHICLE_INFO_BACK_ARROW_CTA)
                        .padding(
                            start = 19.dp,
                            end = 22.dp,
                            top = 17.dp,
                            bottom = 17.dp,
                        ),
                tint = AppTheme.colors.button02a,
            )
        }

        OASubHeadLine3TextView(
            text = stringResource(id = R.string.Subscription_subscriptions),
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier
                    .padding(top = 6.dp)
                    .align(Alignment.TopCenter)
                    .testTagID(AccessibilityId.ID_SUBSCRIPTION_LIST_TITLE),
        )

        if (canShowDisclaimer) {
            Icon(
                painter = painterResource(id = R.drawable.ic_subscription_info),
                contentDescription = "Subscription info",
                tint = AppTheme.colors.button02a,
                modifier =
                    Modifier
                        .width(24.dp)
                        .height(24.dp)
                        .align(Alignment.TopEnd)
                        .clickable {
                            ShowDisclaimerDialog(state = infoBottomSheetState, coroutineScope = scope)
                        }.testTagID(AccessibilityId.ID_SUBSCRIPTION_INFO_BTN),
            )
        }
    }
}

@Composable
private fun Body(
    viewModel: SubscriptionListViewModel,
    uiModel: SubscriptionList,
    scroll: ScrollState,
) {
    val context = LocalContext.current

    Column(modifier = Modifier.fillMaxSize()) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
                Modifier
                    .verticalScroll(scroll)
                    .weight(8f),
        ) {
            Spacer(Modifier.height(headerHeight))
            uiModel.itemUIModels.forEach { uiItem ->
                when (uiItem) {
                    is SubscriptionItem.Header -> {
                        ServiceTitleItem(
                            uiHeader = uiItem,
                            testTagId = AccessibilityId.ID_SUBSCRIPTION_LIST_HEADER_TITLE,
                        )
                    }

                    is SubscriptionItem.Service -> {
                        ServiceItem(viewModel = viewModel, uiServiceItem = uiItem)
                    }

                    is SubscriptionItem.Bundle -> {
                        BundleItem(viewModel = viewModel, uiBundleItem = uiItem)
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        if (uiModel.showPPODisclaimer) {
            OACallOut1TextView(
                text = uiModel.ppoDiscalimer,
                color = AppTheme.colors.button02a,
                textAlign = TextAlign.Center,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight(),
            )

            Spacer(modifier = Modifier.height(8.dp))
        }

        if (uiModel.isActionBtnVisible) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .align(Alignment.CenterHorizontally)
                        .weight(1f),
            ) {
                PrimaryButton02(
                    text = stringResource(id = uiModel.actionBtnText),
                    modifier =
                        Modifier
                            .wrapContentWidth()
                            .wrapContentHeight()
                            .weight(1f)
                            .layoutId(AccessibilityId.ID_SUBSCRIPTION_CTA_BTN),
                    click = {
                        viewModel.logButtonClickEvents(uiModel.isAddService)

                        context.startActivity(
                            Intent(context, SubscriptionV2Activity::class.java)
                                .putExtra(
                                    SubscriptionV2Activity.EXTRA_VEHICLE,
                                    viewModel.applicationData.getSelectedVehicle(),
                                ).putExtra(SubscriptionV2Activity.EXTRA_PRODUCT_LINE, "")
                                .putExtra(SubscriptionV2Activity.EXTRA_PRODUCT_TYPE, "")
                                .putExtra(SubscriptionV2Activity.EXTRA_SUBSCRIPTION_BTN_FLOW, true),
                        )
                    },
                )
            }
        }
    }
}

@Composable
private fun Toolbar(
    scroll: ScrollState,
    headerHeightPx: Float,
    toolbarHeightPx: Float,
) {
    val toolbarBottom = headerHeightPx - toolbarHeightPx
    val showToolbar by remember {
        derivedStateOf {
            scroll.value >= toolbarBottom
        }
    }

    AnimatedVisibility(
        visible = showToolbar,
        enter = fadeIn(animationSpec = tween(300)),
        exit = fadeOut(animationSpec = tween(300)),
    ) {
        TopAppBar(
            backgroundColor = AppTheme.colors.tertiary15,
            elevation = 0.dp,
            modifier =
                Modifier
                    .height(topAppBarHeight),
        ) {}
    }
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
fun VehicleImage(
    vehicleImage: String,
    scroll: ScrollState,
    headerHeightPx: Float,
    screenSize: Size,
) {
    val scaleStart = 2
    val scaleEnd = 0.4f

    val imageMaxWidth = CommonDimensions.VEHICLE_IMAGE_WIDTH.dp
    val imageMaxHeight = CommonDimensions.VEHICLE_IMAGE_HEIGHT.dp

    if (vehicleImage.contains(Constants.ImageNotFound, true) ||
        vehicleImage.isEmpty()
    ) {
        NoVehicleImage(
            headerHeightPx = headerHeightPx,
            screenSize = screenSize,
            scroll = scroll,
            width = imageMaxWidth,
            height = imageMaxHeight,
        )
    } else {
        GlideImage(
            model = vehicleImage,
            contentDescription = stringResource(id = R.string.vehicle_image_description),
            contentScale = ContentScale.Fit,
            modifier =
                Modifier
                    .padding(top = 16.dp)
                    .width(imageMaxWidth)
                    .height(imageMaxHeight)
                    .graphicsLayer {
                        val collapseRange: Float = (headerHeightPx)
                        val collapseFraction: Float = (scroll.value / collapseRange).coerceIn(0f, 1f)

                        val scaleXY = lerp(scaleStart.dp, scaleEnd.dp, collapseFraction)

                        val imageY =
                            lerp(
                                (headerHeight / 3),
                                -toolbarHeight,
                                collapseFraction,
                            )

                        val imageX =
                            lerp(
                                (screenSize.width.toDp() / 2) - (imageMaxWidth / 2),
                                (screenSize.width.toDp() / 2) - (imageMaxWidth / 2),
                                collapseFraction,
                            )

                        translationY = imageY.toPx()
                        translationX = imageX.toPx()

                        scaleX = scaleXY.value
                        scaleY = scaleXY.value
                    }.padding(16.dp),
        )
    }
}

@Composable
fun NoVehicleImage(
    headerHeightPx: Float,
    screenSize: Size,
    scroll: ScrollState,
    width: Dp,
    height: Dp,
) {
    val scaleStart = 2
    val scaleEnd = 0.4f

    val imageMaxWidth = CommonDimensions.VEHICLE_IMAGE_WIDTH.dp

    Box(
        modifier =
            Modifier
                .width(width)
                .height(height)
                .padding(16.dp)
                .graphicsLayer {
                    val collapseRange: Float = (headerHeightPx)
                    val collapseFraction: Float = (scroll.value / collapseRange).coerceIn(0f, 1f)

                    val scaleXY = lerp(scaleStart.dp, scaleEnd.dp, collapseFraction)

                    val imageY =
                        lerp(
                            (headerHeight / 3),
                            -toolbarHeight,
                            collapseFraction,
                        )

                    val imageX =
                        lerp(
                            (screenSize.width.toDp() / 2) - (imageMaxWidth / 2),
                            (screenSize.width.toDp() / 2) - (imageMaxWidth / 2),
                            collapseFraction,
                        )

                    translationY = imageY.toPx()
                    translationX = imageX.toPx()

                    scaleX = scaleXY.value
                    scaleY = scaleXY.value
                },
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_image_not_found),
            contentDescription = stringResource(id = R.string.vehicle_not_found_description),
            contentScale = ContentScale.Fit,
            modifier =
                Modifier
                    .align(Alignment.Center),
        )

        Box(
            modifier =
                Modifier
                    .clip(shape = RoundedCornerShape(8.dp))
                    .align(Alignment.Center)
                    .background(color = AppTheme.colors.noImageBGColor),
        ) {
            OACaption2TextView(
                text = stringResource(id = R.string.noImageFound),
                textAlign = TextAlign.Center,
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.padding(horizontal = 6.27.dp, vertical = 5.dp),
            )
        }
    }
}
