package com.toyota.oneapp.features.subscription.domain.model

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.toyota.oneapp.model.subscriptionV2.BundleComponent
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2

data class SubscriptionList(
    val vehicleImage: String,
    val canShowDisclaimer: Boolean,
    val itemUIModels: List<SubscriptionItem>,
    val isActionBtnVisible: Boolean,
    @StringRes val actionBtnText: Int,
    val isAddService: Boolean,
    val serviceCount: Int,
    val subscriptionSnippetCanShow: <PERSON>olean,
    val showPPODisclaimer: <PERSON>olean,
    val ppoDiscalimer: String,
)

sealed class SubscriptionItem {
    data class Header(
        @StringRes val title: Int,
    ) : SubscriptionItem()

    data class Service(
        val title: String,
        @StringRes val subTitle: Int?,
        val displayTerm: String,
        val isWarning: Boolean,
        @DrawableRes val serviceIcon: Int,
        @DrawableRes val statusIcon: Int,
        val serviceDetail: SubscriptionV2?,
    ) : SubscriptionItem()

    data class Bundle(
        val title: String,
        @StringRes val subTitle: Int?,
        val displayTerm: String,
        val isWarning: Boolean,
        @DrawableRes val statusIcon: Int,
        val bundleComponents: List<BundleComponent>?,
        val bundleDetail: SubscriptionV2,
    ) : SubscriptionItem()
}

data class NoSubscriptionUIModel(
    val vehicleImage: String,
    @StringRes val title: Int,
    @StringRes val subTitle: Int,
    val isActionBtnVisible: Boolean = false,
)
