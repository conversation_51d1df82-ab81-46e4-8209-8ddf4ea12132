package com.toyota.oneapp.features.subscription.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.toUpperCase
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.util.Brand
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@ExperimentalMaterialApi
@Composable
fun DisclaimerContent(
    vehicleBrand: String,
    modalSheetState: ModalBottomSheetState,
    coroutineScope: CoroutineScope,
) {
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .background(AppTheme.colors.tertiary15),
    ) {
        Spacer(modifier = Modifier.height(24.dp))

        Image(
            painter = painterResource(id = R.drawable.ic_subscription_disclaimer),
            contentDescription = "Disclaimer info",
            modifier =
                Modifier
                    .width(48.dp)
                    .height(48.dp)
                    .align(Alignment.CenterHorizontally),
        )

        Spacer(modifier = Modifier.height(16.dp))

        OASubHeadLine1TextView(
            text = stringResource(id = R.string.disclaimer),
            color = AppTheme.colors.tertiary03,
            textAlign = TextAlign.Center,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(horizontal = 24.dp),
        )

        Spacer(modifier = Modifier.height(8.dp))

        OACallOut1TextView(
            text =
                stringResource(
                    R.string.subscription_disclaimer,
                    when (vehicleBrand.toUpperCase(Locale.current)) {
                        Brand.LEXUS.appBrand -> stringResource(id = R.string.Common_lexus)
                        Brand.SUBARU.appBrand -> stringResource(id = R.string.app_name)
                        else -> stringResource(id = R.string.Common_toyota)
                    },
                ),
            color = AppTheme.colors.tertiary05,
            textAlign = TextAlign.Center,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(horizontal = 24.dp),
        )

        Spacer(modifier = Modifier.height(120.dp))

        PrimaryButton02(
            text = stringResource(id = R.string.exit_button),
            modifier =
                Modifier
                    .wrapContentWidth()
                    .wrapContentHeight()
                    .align(Alignment.CenterHorizontally)
                    .layoutId(AccessibilityId.ID_SUBSCRIPTION_DISCLAIMER_EXIT_BTN),
            click = {
                hideDisclaimerDialog(state = modalSheetState, coroutineScope = coroutineScope)
            },
        )

        Spacer(modifier = Modifier.height(24.dp))
    }
}

@ExperimentalMaterialApi
fun ShowDisclaimerDialog(
    state: ModalBottomSheetState,
    coroutineScope: CoroutineScope,
) {
    coroutineScope.launch {
        if (!state.isVisible) {
            state.show()
        }
    }
}

@ExperimentalMaterialApi
fun hideDisclaimerDialog(
    state: ModalBottomSheetState,
    coroutineScope: CoroutineScope,
) {
    coroutineScope.launch {
        if (state.isVisible) {
            state.hide()
        }
    }
}
