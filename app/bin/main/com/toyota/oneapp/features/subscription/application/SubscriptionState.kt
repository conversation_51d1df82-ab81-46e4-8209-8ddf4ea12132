package com.toyota.oneapp.features.subscription.application

import androidx.annotation.StringRes
import com.toyota.oneapp.features.subscription.domain.model.NoSubscriptionUIModel
import com.toyota.oneapp.features.subscription.domain.model.SubscriptionList

sealed class SubscriptionState {
    object Idle : SubscriptionState()

    object Loading : SubscriptionState()

    class NoSubscriptions(val data: NoSubscriptionUIModel) : SubscriptionState()

    class Success(val data: SubscriptionList) : SubscriptionState()

    class Error(
        val vehicleImage: String,
        @StringRes val errorTitle: Int,
        @StringRes val errorMessage: Int,
    ) : SubscriptionState()
}
