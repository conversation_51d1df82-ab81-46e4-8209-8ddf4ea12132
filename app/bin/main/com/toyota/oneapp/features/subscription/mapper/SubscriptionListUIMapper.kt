package com.toyota.oneapp.features.subscription.mapper

import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.subscription.domain.model.NoSubscriptionUIModel
import com.toyota.oneapp.features.subscription.domain.model.SubscriptionItem
import com.toyota.oneapp.features.subscription.domain.model.SubscriptionList
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.subscriptionV2.VehicleSubscriptionPayload
import com.toyota.oneapp.model.subscriptionV2.isActive
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import kotlin.math.abs

class SubscriptionListUIMapper
    @Inject
    constructor(
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val applicationData: ApplicationData,
    ) {
        companion object {
            private const val WARNING_REMAINING_DAYS = 30
        }

        suspend fun map(vehicleSubscriptionPayload: VehicleSubscriptionPayload): SubscriptionList {
            val vehicle = applicationData.getSelectedVehicle()
            val itemUIModels = mutableListOf<SubscriptionItem>()

            var serviceCount = 0

            val isSubscriptionCardEnabled = vehicle?.isFeatureEnabled(Feature.CONNECTED_SERVICE_INFO) ?: false

            // Trial Services
            if (vehicleSubscriptionPayload.trialSubscriptions.isNotEmpty()) {
                itemUIModels += SubscriptionItem.Header(R.string.trial_services)
                vehicleSubscriptionPayload.trialSubscriptions.forEach { trialService ->

                    val isNoWarning =
                        if (trialService.externalProduct) {
                            true
                        } else if (trialService.status.equals("active", true)) {
                            (
                                trialService.type.equals("trial", true) ||
                                    trialService.type.equals("free", true)
                            ) &&
                                (trialService.subscriptionRemainingDays ?: 0) > WARNING_REMAINING_DAYS
                        } else {
                            (
                                trialService.type.equals("trial", true) ||
                                    trialService.type.equals("free", true)
                            )
                        }
                    if (isSubscriptionCardEnabled) {
                        if (trialService.status != null &&
                            trialService.status.lowercase() == "active" &&
                            trialService.subscriptionRemainingDays != null &&
                            trialService.subscriptionRemainingDays < 14 && !trialService.externalProduct
                        ) {
                            serviceCount++
                        }
                    }

                    val subTitle =
                        if (trialService.isActive()) {
                            R.string.active
                        } else {
                            R.string.trial_available
                        }

                    itemUIModels +=
                        SubscriptionItem.Service(
                            title = trialService.displayProductName.orEmpty(),
                            subTitle = subTitle,
                            displayTerm = trialService.displayTerm,
                            serviceIcon =
                                SubscriptionUtil.getIconForSubscriptionFromProductLine(
                                    trialService.productLine,
                                ),
                            statusIcon = if (isNoWarning) R.drawable.ic_small_tic else R.drawable.ic_small_alert,
                            isWarning = !isNoWarning,
                            serviceDetail = trialService,
                        )
                }
            }

            // Paid Service
            if (vehicleSubscriptionPayload.paidSubscriptions.isNotEmpty()) {
                itemUIModels += SubscriptionItem.Header(R.string.paid_services)
                vehicleSubscriptionPayload.paidSubscriptions.forEach { paidService ->
                    if (isSubscriptionCardEnabled) {
                        if (paidService.status != null &&
                            paidService.status.lowercase() == "active" &&
                            paidService.autoRenew != null &&
                            !paidService.autoRenew &&
                            paidService.subscriptionRemainingDays != null &&
                            paidService.subscriptionRemainingDays < 14
                        ) {
                            serviceCount++
                        }
                    }

                    var subTitle: Int? = null
                    vehicle?.let {
                        if (!vehicle.isCY17 && paidService.renewable && !paidService.externalProduct) {
                            subTitle =
                                if (paidService.autoRenew == true) {
                                    R.string.Subscription_Auto_Renew_On
                                } else {
                                    R.string.Subscription_Auto_Renew_Off
                                }
                        }
                    }

                    val isNoWarning = hasNoPaidWarning(paidService)

                    if (paidService.category == "BUNDLE") {
                        itemUIModels +=
                            SubscriptionItem.Bundle(
                                title = paidService.displayProductName,
                                subTitle = subTitle,
                                displayTerm = paidService.displayTerm,
                                isWarning = !isNoWarning,
                                statusIcon = if (isNoWarning) R.drawable.ic_small_tic else R.drawable.ic_small_alert,
                                bundleComponents = paidService.components,
                                bundleDetail = paidService,
                            )
                    } else {
                        itemUIModels +=
                            SubscriptionItem.Service(
                                title = paidService.displayProductName,
                                subTitle = subTitle,
                                displayTerm = paidService.displayTerm,
                                serviceIcon =
                                    SubscriptionUtil.getIconForSubscriptionFromProductLine(
                                        paidService.productLine,
                                    ),
                                statusIcon = if (isNoWarning) R.drawable.ic_small_tic else R.drawable.ic_small_alert,
                                isWarning = !isNoWarning,
                                serviceDetail = paidService,
                            )
                    }
                }
            }
            val trialSubsPayload = vehicleSubscriptionPayload.trialSubscriptions
            val isAvailableSubsNotEmpty = vehicleSubscriptionPayload.availableSubscriptions.isNotEmpty()
            val isAllTrialServicesActive = trialSubsPayload.filter { !it.externalProduct }.all { it.isActive() }
            var isActionBtnVisible = false
            val isAddService: Boolean
            val actionTxt: Int
            if (isAllTrialServicesActive) {
                actionTxt = R.string.add_service
                isAddService = true
                isActionBtnVisible = vehicle?.isFeatureEnabled(Feature.PAID_SUBSCRIPTION) == true && isAvailableSubsNotEmpty
            } else {
                actionTxt = R.string.enable_all_trials
                isAddService = false
                if (vehicle != null) {
                    isActionBtnVisible = !vehicle.isRemoteOnlyUser
                }
            }
            val subscriptionSnippetCanShow =
                coroutineScope {
                    val subscriptionSnippetCanShow = async { checkIfSubscriptionSnippetCanShow(vehicle) }
                    subscriptionSnippetCanShow.await()
                }

            val uiModel =
                SubscriptionList(
                    vehicleImage = vehicle?.image.orEmpty(),
                    canShowDisclaimer = vehicle?.region.equals("ca", true),
                    itemUIModels = itemUIModels,
                    isActionBtnVisible = isActionBtnVisible,
                    actionBtnText = actionTxt,
                    isAddService = isAddService,
                    serviceCount = serviceCount,
                    subscriptionSnippetCanShow = subscriptionSnippetCanShow,
                    showPPODisclaimer = vehicleSubscriptionPayload.isPPOEligible ?: false,
                    ppoDiscalimer = vehicleSubscriptionPayload.ppoDisclaimer.orEmpty(),
                )

            return uiModel
        }

        private fun hasNoPaidWarning(paidService: SubscriptionV2): Boolean {
            return if (paidService.status.equals("active", true) && paidService.externalProduct == true) {
                true
            } else if (paidService.autoRenew != null) {
                if (paidService.autoRenew) {
                    true
                } else {
                    (
                        paidService.subscriptionRemainingDays
                            ?: 0
                    ) > WARNING_REMAINING_DAYS
                }
            } else {
                false
            }
        }

        fun mapNoSubscription(
            vehicleInfo: VehicleInfo,
            vehicleSubscriptionPayload: VehicleSubscriptionPayload,
        ): NoSubscriptionUIModel {
            val isPaidSubscriptionEnabled = vehicleInfo.isFeatureEnabled(Feature.PAID_SUBSCRIPTION)
            val isAvailableSubsNotEmpty = vehicleSubscriptionPayload.availableSubscriptions.isNotEmpty()
            if (vehicleInfo.isVehicleCanadian && vehicleInfo.isOnlyCY17) {
                return NoSubscriptionUIModel(
                    vehicleImage = vehicleInfo.image.orEmpty(),
                    title = R.string.no_services_found,
                    subTitle =
                        if (vehicleInfo.isToyotaBrand) {
                            R.string.no_service_canadian_toyota_subtitle
                        } else {
                            R.string.no_service_canadian_lexus_subtitle
                        },
                    isActionBtnVisible = isPaidSubscriptionEnabled && isAvailableSubsNotEmpty,
                )
            }

            return NoSubscriptionUIModel(
                vehicleImage = vehicleInfo.image.orEmpty(),
                title = R.string.no_services_found,
                subTitle = R.string.no_subscription_msg,
                isActionBtnVisible = isPaidSubscriptionEnabled && isAvailableSubsNotEmpty,
            )
        }

        private fun checkIfSubscriptionSnippetCanShow(vehicleInfo: VehicleInfo?): Boolean {
            val canShowSubscriptionSnippet: Boolean =
                vehicleInfo?.vin?.let {
                    oneAppPreferenceModel.getSubscriptionSnippetStatus(
                        it,
                    )
                } == true
            val canShowBottomSheet = checkIfAnnouncementSheetClosedThreeDaysBefore(vehicleInfo)
            return if (canShowSubscriptionSnippet || canShowBottomSheet) {
                if (!canShowSubscriptionSnippet && canShowBottomSheet) {
                    vehicleInfo?.vin?.let {
                        oneAppPreferenceModel.enableSubscriptionSnippet(it)
                    }
                }
                true
            } else {
                false
            }
        }

        private fun checkIfAnnouncementSheetClosedThreeDaysBefore(vehicleInfo: VehicleInfo?): Boolean {
            // / Date format : 2023-07-04 10:37:09
            var canShow = true
            val announcementClosingTime =
                oneAppPreferenceModel.getAnnouncementPopupCloseTime(
                    vehicleInfo?.vin ?: ToyotaConstants.EMPTY_STRING,
                )
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            if (announcementClosingTime.isNotEmpty()) {
                val announcementClosedTime = LocalDateTime.parse(announcementClosingTime, formatter)
                if (announcementClosedTime != null) {
                    val daysRemaining =
                        abs(
                            ChronoUnit.DAYS.between(LocalDateTime.now(), announcementClosedTime),
                        )
                    if (daysRemaining < 3) {
                        canShow = false
                    }
                }
            }
            return canShow
        }
    }
