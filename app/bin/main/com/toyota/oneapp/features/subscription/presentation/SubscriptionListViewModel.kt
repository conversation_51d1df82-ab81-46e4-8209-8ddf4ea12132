package com.toyota.oneapp.features.subscription.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.dashboard.dashboard.domain.FetchData
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.features.subscription.application.SubscriptionListUseCase
import com.toyota.oneapp.features.subscription.application.SubscriptionState
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.subscriptionV2.isActive
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import toyotaone.commonlib.log.LogTool
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class SubscriptionListViewModel
    @Inject
    constructor(
        val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        private val useCase: SubscriptionListUseCase,
        private val dispatcherProvider: DispatcherProvider,
        val sharedDataSource: SharedDataSource,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : BaseViewModel() {
        private val tag = "SubscriptionListViewModel"
        private val _uiState = MutableStateFlow<SubscriptionState>(value = SubscriptionState.Loading)
        val uiState: StateFlow<SubscriptionState> = _uiState
        private var prevVin: String? = null

        init {
            viewModelScope.launch(dispatcherProvider.main()) {
                applicationData.getSelectedVehicleState().collectLatest {
                    if (it != null && !it.isNonConnectedVehicle) {
                        loadSubscription(it)
                    }
                }
            }
            fetchData()
        }

        private fun fetchData() {
            viewModelScope.launch {
                sharedDataSource.fetchDataState.collect { it ->
                    when (it) {
                        FetchData.SUBSCRIPTION -> {
                            applicationData.getSelectedVehicle()?.let {
                                if (!it.isNonConnectedVehicle) {
                                    loadSubscription(it)
                                } else {
                                    LogTool.d(
                                        tag,
                                        "Selected Vehicle is NonConnectedVehicle, Skip Calling Subscription API.",
                                    )
                                }
                            } ?: let {
                                LogTool.d(
                                    tag,
                                    "Selected Vehicle is Null, Skip Calling Subscription API.",
                                )
                            }
                        }
                        else -> { }
                    }
                }
            }
        }

        private suspend fun loadSubscription(vehicleInfo: VehicleInfo) {
            if (sharedDataSource.getSubscriptionState().value != SubscriptionState.Loading && prevVin != vehicleInfo.vin) {
                prevVin = vehicleInfo.vin
                sharedDataSource.setSubscriptionData(SubscriptionState.Loading)
                useCase.fetchSubscriptionList(vehicleInfo)
                    .flowOn(dispatcherProvider.io()).collect {
                        sharedDataSource.setSubscriptionData(it)
                    }
            }
        }

        fun logServiceClickEvents(serviceDetail: SubscriptionV2?) {
            if (serviceDetail?.displayProductName?.contains("remote") == true) {
                analyticsLogger.logEvent(AnalyticsEvent.REMOTE_CONNECT)
            }

            serviceDetail?.apply {
                if (!isActive() && type.equals("trial", true)) {
                    analyticsLogger.logEvent(
                        AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_TRIAL_AVAILABLE_CLICKED,
                    )
                } else if (type.equals("paid", true)) {
                    analyticsLogger.logEvent(
                        AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_PAID_SERVICE_CLICKED,
                    )
                } else if (isActive() && type.equals("trial", true)) {
                    analyticsLogger.logEvent(
                        AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_TRIAL_ENABLED_SERVICE_CLICKED,
                    )
                }
            }
        }

        fun logButtonClickEvents(isAddService: Boolean) {
            if (isAddService) {
                analyticsLogger.logEvent(
                    AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_ADD_SERVICE_CLICKED,
                )
            } else {
                analyticsLogger.logEvent(
                    AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_ENABLE_ALL_TRIAL_CLICKED,
                )
            }
        }

        fun getSubscriptionAccessibilityId(productName: String): String {
            val formattedProductName = productName.replace(" ", "_").lowercase(Locale.getDefault())
            return "subscription_${formattedProductName}_tile"
        }

        fun logEventWithParameter(event: String) {
            analyticsLogger.logEventWithParameter(AnalyticsEvent.DASHBOARD_CARD.eventName, event)
        }

        fun closeSubscriptionSnippet() {
            val dateTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US).format(Date())
            viewModelScope.launch(dispatcherProvider.main()) {
                applicationData.getSelectedVehicle()?.vin?.let { vin ->
                    oneAppPreferenceModel.setAnnouncementPopupCloseTime(dateTime, vin)
                    oneAppPreferenceModel.disableSubscriptionSnippetData(vin)
                    refreshSubscriptionData()
                }
            }
        }

        private fun refreshSubscriptionData() {
            viewModelScope.launch(dispatcherProvider.main()) {
                applicationData.getSelectedVehicleState().collectLatest {
                    if (it != null) {
                        loadSubscription(it)
                    }
                }
            }
        }
    }
