package com.toyota.oneapp.features.subscription.dataaccess.repository

import com.toyota.oneapp.features.subscription.domain.repo.SubscriptionListRepo
import com.toyota.oneapp.model.subscriptionV2.VehicleSubscriptionResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.cy17plus.CY17PlusCoroutineServiceApi
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class SubscriptionListDefaultRepo
    @Inject
    constructor(
        val service: CY17PlusCoroutineServiceApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), SubscriptionListRepo {
        override suspend fun getSubscriptionList(
            vin: String,
            brand: String,
            region: String,
            generation: String,
            asiCode: String,
            hwType: String,
        ): Resource<VehicleSubscriptionResponse?> {
            return makeApiCall {
                service.getVehicleSubscriptions(
                    vin = vin,
                    brand = brand,
                    region = region,
                    generation = generation,
                    asiCode = asiCode,
                    hwType = hwType,
                    dateTime = System.currentTimeMillis(),
                )
            }
        }
    }
