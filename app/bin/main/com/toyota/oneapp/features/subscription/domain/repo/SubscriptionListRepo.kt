package com.toyota.oneapp.features.subscription.domain.repo

import com.toyota.oneapp.model.subscriptionV2.VehicleSubscriptionResponse
import com.toyota.oneapp.network.Resource

interface SubscriptionListRepo {
    suspend fun getSubscriptionList(
        vin: String,
        brand: String,
        region: String,
        generation: String,
        asiCode: String,
        hwType: String,
    ): Resource<VehicleSubscriptionResponse?>
}
