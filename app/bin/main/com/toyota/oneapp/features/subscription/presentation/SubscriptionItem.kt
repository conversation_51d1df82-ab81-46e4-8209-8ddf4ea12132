package com.toyota.oneapp.features.subscription.presentation

import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Divider
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.ComposableRoundedCornerCard
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.subscription.domain.model.SubscriptionItem
import com.toyota.oneapp.ui.garage.subscriptionV2.SubscriptionV2Activity
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil

@Composable
fun ServiceTitleItem(
    uiHeader: SubscriptionItem.Header,
    testTagId: String,
) {
    OASubHeadLine1TextView(
        text = stringResource(id = uiHeader.title),
        color = AppTheme.colors.tertiary03,
        textAlign = TextAlign.Start,
        modifier =
            Modifier
                .padding(all = 16.dp)
                .fillMaxWidth()
                .wrapContentHeight()
                .testTagID(testTagId),
    )
}

@Composable
fun ServiceItem(
    viewModel: SubscriptionListViewModel,
    uiServiceItem: SubscriptionItem.Service,
) {
    val subscriptionListViewModel =
        hiltViewModel<SubscriptionListViewModel>(
            LocalContext.current as OADashboardActivity,
        )
    val context = LocalContext.current

    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 16.dp, vertical = 2.dp),
    ) {
        ComposableRoundedCornerCard(
            backgroundColor = AppTheme.colors.tile03,
            cornerRadius = 8.dp,
            modifier =
                Modifier.layoutId(
                    subscriptionListViewModel.getSubscriptionAccessibilityId(uiServiceItem.title),
                ),
            click = {
                uiServiceItem.serviceDetail.let {
                    viewModel.logServiceClickEvents(uiServiceItem.serviceDetail)

                    context.startActivity(
                        Intent(context, SubscriptionV2Activity::class.java)
                            .putExtra(
                                SubscriptionV2Activity.EXTRA_VEHICLE,
                                viewModel.applicationData.getSelectedVehicle(),
                            )
                            .putExtra(
                                SubscriptionV2Activity.EXTRA_PRODUCT_LINE,
                                it?.productLine,
                            )
                            .putExtra(SubscriptionV2Activity.EXTRA_PRODUCT_TYPE, it?.type)
                            .putExtra(SubscriptionV2Activity.EXTRA_SUBSCRIPTION_BTN_FLOW, false),
                    )
                }
            },
        ) {
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(start = 12.dp, top = 12.dp, bottom = 18.dp, end = 12.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .width(55.dp)
                            .height(55.dp)
                            .align(Alignment.CenterVertically),
                ) {
                    Surface(
                        shape = CircleShape,
                        color = if (uiServiceItem.isWarning) AppTheme.colors.primary02 else AppTheme.colors.secondary02,
                        modifier =
                            Modifier
                                .width(48.dp)
                                .height(48.dp),
                    ) {
                        Image(
                            painter = painterResource(id = uiServiceItem.serviceIcon),
                            contentDescription = "Service Icon",
                            contentScale = ContentScale.FillBounds,
                            colorFilter =
                                ColorFilter.tint(
                                    if (uiServiceItem.isWarning) AppTheme.colors.primary01 else AppTheme.colors.secondary01,
                                ),
                            modifier =
                                Modifier
                                    .width(20.dp)
                                    .height(20.dp)
                                    .padding(8.dp),
                        )
                    }
                    Surface(
                        shape = CircleShape,
                        color = AppTheme.colors.tile02,
                        modifier =
                            Modifier
                                .width(24.dp)
                                .height(24.dp)
                                .align(Alignment.BottomEnd),
                    ) {
                        Image(
                            modifier = Modifier.padding(4.dp),
                            painter = painterResource(id = uiServiceItem.statusIcon),
                            contentDescription = "Status Icon",
                            contentScale = ContentScale.FillBounds,
                        )
                    }
                }

                Spacer(modifier = Modifier.width(12.dp))

                Column(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .wrapContentHeight(),
                ) {
                    OABody4TextView(
                        text = uiServiceItem.title,
                        color = AppTheme.colors.tertiary03,
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    OACallOut1TextView(
                        text =
                            if (uiServiceItem.subTitle == null) {
                                uiServiceItem.displayTerm
                            } else {
                                "${stringResource(id = uiServiceItem.subTitle)}\n${uiServiceItem.displayTerm}"
                            },
                        color = AppTheme.colors.tertiary05,
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))
    }
}

@Composable
fun BundleItem(
    viewModel: SubscriptionListViewModel,
    uiBundleItem: SubscriptionItem.Bundle,
) {
    val subscriptionListViewModel =
        hiltViewModel<SubscriptionListViewModel>(
            LocalContext.current as OADashboardActivity,
        )
    val context = LocalContext.current

    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 16.dp, vertical = 4.dp),
    ) {
        ComposableRoundedCornerCard(
            backgroundColor = AppTheme.colors.tile03,
            cornerRadius = 8.dp,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .background(AppTheme.colors.tile03)
                    .layoutId(
                        subscriptionListViewModel.getSubscriptionAccessibilityId(uiBundleItem.title),
                    ),
            click = {
                uiBundleItem.bundleDetail.let {
                    viewModel.logServiceClickEvents(it)

                    context.startActivity(
                        Intent(context, SubscriptionV2Activity::class.java)
                            .putExtra(
                                SubscriptionV2Activity.EXTRA_VEHICLE,
                                viewModel.applicationData.getSelectedVehicle(),
                            )
                            .putExtra(SubscriptionV2Activity.EXTRA_PRODUCT_LINE, it.productLine)
                            .putExtra(SubscriptionV2Activity.EXTRA_PRODUCT_TYPE, it.type)
                            .putExtra(SubscriptionV2Activity.EXTRA_SUBSCRIPTION_BTN_FLOW, false),
                    )
                }
            },
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(start = 32.dp, end = 32.dp, top = 20.dp),
            ) {
                OABody4TextView(
                    text = uiBundleItem.title,
                    color = AppTheme.colors.tertiary03,
                )

                Spacer(modifier = Modifier.height(4.dp))

                OACallOut1TextView(
                    text =
                        if (uiBundleItem.subTitle == null) {
                            uiBundleItem.displayTerm
                        } else {
                            "${stringResource(id = uiBundleItem.subTitle)}\n${uiBundleItem.displayTerm}"
                        },
                    color = AppTheme.colors.tertiary05,
                )

                Spacer(modifier = Modifier.height(16.dp))

                uiBundleItem.bundleComponents?.forEach { component ->
                    Column(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .wrapContentHeight(),
                    ) {
                        Divider(
                            thickness = 1.dp,
                            color = Color(0xFFADADB1),
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .wrapContentHeight(),
                        ) {
                            Box(
                                modifier =
                                    Modifier
                                        .width(55.dp)
                                        .height(55.dp)
                                        .align(Alignment.CenterVertically),
                            ) {
                                Surface(
                                    shape = CircleShape,
                                    color = if (uiBundleItem.isWarning) AppTheme.colors.primary02 else AppTheme.colors.secondary02,
                                    modifier =
                                        Modifier
                                            .width(48.dp)
                                            .height(48.dp),
                                ) {
                                    Image(
                                        painter =
                                            painterResource(
                                                id =
                                                    SubscriptionUtil.getIconForSubscriptionFromProductLine(
                                                        component.productCode,
                                                    ),
                                            ),
                                        contentDescription =
                                            stringResource(
                                                id = R.string.service_icon_content_desc,
                                            ),
                                        contentScale = ContentScale.FillBounds,
                                        colorFilter =
                                            ColorFilter.tint(
                                                if (uiBundleItem.isWarning) AppTheme.colors.primary01 else AppTheme.colors.secondary01,
                                            ),
                                        modifier =
                                            Modifier
                                                .width(20.dp)
                                                .height(20.dp)
                                                .padding(8.dp),
                                    )
                                }

                                Surface(
                                    shape = CircleShape,
                                    color = AppTheme.colors.tile02,
                                    modifier =
                                        Modifier
                                            .width(24.dp)
                                            .height(24.dp)
                                            .align(Alignment.BottomEnd),
                                ) {
                                    Image(
                                        modifier = Modifier.padding(4.dp),
                                        painter = painterResource(id = uiBundleItem.statusIcon),
                                        contentDescription =
                                            stringResource(
                                                id = R.string.status_icon_content_desc,
                                            ),
                                        contentScale = ContentScale.FillBounds,
                                    )
                                }
                            }

                            Spacer(modifier = Modifier.width(24.dp))

                            OABody4TextView(
                                text = component.productName.orEmpty(),
                                color = AppTheme.colors.tertiary03,
                                modifier = Modifier.align(Alignment.CenterVertically),
                            )
                        }

                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
            }
        }
    }
}
