package com.toyota.oneapp.features.subscription.di

import com.toyota.oneapp.features.subscription.application.SubscriptionListLogic
import com.toyota.oneapp.features.subscription.application.SubscriptionListUseCase
import com.toyota.oneapp.features.subscription.dataaccess.repository.SubscriptionListDefaultRepo
import com.toyota.oneapp.features.subscription.domain.repo.SubscriptionListRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class SubscriptionListModule {
    @Binds
    abstract fun bindSubscriptionListRepo(subscriptionListDefaultRepo: SubscriptionListDefaultRepo): SubscriptionListRepo

    @Binds
    abstract fun bindSubscriptionListUseCase(subscriptionListLogic: SubscriptionListLogic): SubscriptionListUseCase
}
