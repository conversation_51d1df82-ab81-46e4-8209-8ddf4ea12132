package com.toyota.oneapp.features.subscription.presentation

import android.annotation.SuppressLint
import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine2TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.ui.garage.subscriptionV2.SubscriptionV2Activity

@OptIn(ExperimentalGlideComposeApi::class)
@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun NoSubscriptionScreen(
    vehicleImage: String,
    title: String,
    subTitle: String,
    viewModel: SubscriptionListViewModel,
    isActionBtnVisible: Boolean = false,
) {
    val context = LocalContext.current
    Scaffold {
        Box(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(AppTheme.colors.tertiary15),
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                GlideImage(
                    model = vehicleImage,
                    contentDescription = viewModel.applicationData.getSelectedVehicle()?.modelName,
                    contentScale = ContentScale.Crop,
                    modifier =
                        Modifier
                            .align(Alignment.CenterHorizontally)
                            .width(320.dp)
                            .height(220.dp),
                )

                Spacer(modifier = Modifier.height(16.dp))

                Column(
                    modifier =
                        Modifier
                            .weight(1f)
                            .fillMaxWidth(),
                    verticalArrangement = Arrangement.Top,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    OASubHeadLine2TextView(
                        text = title,
                        textAlign = TextAlign.Center,
                        color = AppTheme.colors.button02a,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .wrapContentHeight(),
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    OACallOut1TextView(
                        text = subTitle,
                        textAlign = TextAlign.Center,
                        color = AppTheme.colors.button02a,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .wrapContentHeight(),
                    )
                }

                if (isActionBtnVisible) {
                    PrimaryButton02(
                        text = context.getString(R.string.add_service),
                        modifier =
                            Modifier
                                .wrapContentWidth()
                                .wrapContentHeight()
                                .layoutId(AccessibilityId.ID_SUBSCRIPTION_CTA_BTN),
                        click = {
                            viewModel.logButtonClickEvents(true)

                            context.startActivity(
                                Intent(context, SubscriptionV2Activity::class.java)
                                    .putExtra(
                                        SubscriptionV2Activity.EXTRA_VEHICLE,
                                        viewModel.applicationData.getSelectedVehicle(),
                                    )
                                    .putExtra(SubscriptionV2Activity.EXTRA_PRODUCT_LINE, "")
                                    .putExtra(SubscriptionV2Activity.EXTRA_PRODUCT_TYPE, "")
                                    .putExtra(
                                        SubscriptionV2Activity.EXTRA_SUBSCRIPTION_BTN_FLOW,
                                        true,
                                    ),
                            )
                        },
                    )
                }
            }
        }
    }
}
