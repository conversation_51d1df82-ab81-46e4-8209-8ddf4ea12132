package com.toyota.oneapp.features.subscription.application

import com.toyota.oneapp.R
import com.toyota.oneapp.features.subscription.domain.repo.SubscriptionListRepo
import com.toyota.oneapp.features.subscription.mapper.SubscriptionListUIMapper
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class SubscriptionListLogic
    @Inject
    constructor(
        private val repository: SubscriptionListRepo,
        private val uiMapper: SubscriptionListUIMapper,
    ) : SubscriptionListUseCase {
        override suspend fun fetchSubscriptionList(vehicleInfo: VehicleInfo): Flow<SubscriptionState> {
            return flow {
                val response =
                    repository.getSubscriptionList(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        region = vehicleInfo.region,
                        generation = vehicleInfo.generation,
                        asiCode = vehicleInfo.asiCode,
                        hwType = vehicleInfo.hwType,
                    )
                if (response is Resource.Success) {
                    response.data?.payload?.let {
                        if (it == null) {
                            val subscriptionState =
                                SubscriptionState.Error(
                                    errorTitle = R.string.no_services_found,
                                    errorMessage = R.string.no_package_found,
                                    vehicleImage = "",
                                )
                            emit(subscriptionState)
                        } else {
                            val uiModel = uiMapper.map(it)
                            if (uiModel.itemUIModels.isEmpty()) {
                                val subscriptionState =
                                    SubscriptionState.NoSubscriptions(
                                        uiMapper.mapNoSubscription(vehicleInfo, it),
                                    )
                                emit(subscriptionState)
                            } else {
                                val subscriptionState = SubscriptionState.Success(uiModel)
                                emit(subscriptionState)
                            }
                        }
                    }
                } else {
                    emit(
                        SubscriptionState.Error(
                            errorTitle = R.string.no_services_found,
                            errorMessage = R.string.no_package_found,
                            vehicleImage = "",
                        ),
                    )
                }
            }
        }
    }
