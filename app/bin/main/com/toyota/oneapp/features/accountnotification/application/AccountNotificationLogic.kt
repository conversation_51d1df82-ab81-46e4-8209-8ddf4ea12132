package com.toyota.oneapp.features.accountnotification.application

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AccountNotificationHistoryPayloadResponse
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementPayloadResponse
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.toUIModel
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.toUiModel
import com.toyota.oneapp.features.accountnotification.domain.repository.AccountNotificationRepository
import com.toyota.oneapp.features.pay.tfs.application.TFSLogic
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class AccountNotificationLogic
    @Inject
    constructor(
        private val preferenceModel: OneAppPreferenceModel,
        val repository: AccountNotificationRepository,
        val applicationData: ApplicationData,
        val tfsLogic: TFSLogic,
    ) : AccountNotificationUseCase() {
        override fun getProfilePicture(): Flow<ProfilePictureState> {
            return flow {
                val response =
                    repository.getProfilePicture(
                        guid = preferenceModel.getGuid(),
                    )

                emit(
                    response.data?.let {
                        if (it.payload?.isNotEmpty() == true) {
                            ProfilePictureState.Success(data = it.toUIModel())
                        } else {
                            ProfilePictureState.Error(
                                errorCode = response.responseCode,
                                errorMessage = response.message,
                            )
                        }
                    } ?: ProfilePictureState.Error(
                        errorCode = response.responseCode,
                        errorMessage = response.message,
                    ),
                )
            }
        }

        override fun getNotificationHistory(): Flow<NotificationHistoryState> {
            return flow {
                val response =
                    repository.getNotificationHistory(
                        guid = preferenceModel.getGuid(),
                    )

                response.data?.let {
                    emit(
                        if (it.payload?.isNotEmpty() == true) {
                            val hasUpdate = hasNewNotification(response.data?.payload)
                            NotificationHistoryState.Success(
                                data = it.toUiModel(),
                                hasUpdate = hasUpdate,
                            )
                        } else {
                            NotificationHistoryState.Error(
                                errorCode = response.responseCode,
                                errorMessage = response.message,
                            )
                        },
                    )
                }
            }
        }

        override fun getVehicleAlerts(vehicleInfo: VehicleInfo): Flow<AnnouncementPayloadResponse?> {
            return flow {
                val response =
                    repository.getVehicleAlerts(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        region = vehicleInfo.region,
                        includeRenewal = vehicleInfo.isSubscriptionExpirationStatus.toString(),
                    )

                if (response.data != null) {
                    emit(response.data?.payload)
                } else {
                    emit(null)
                }
            }
        }

        private fun hasNewNotification(notificationsPayload: ArrayList<AccountNotificationHistoryPayloadResponse>?): Boolean {
            return notificationsPayload?.any { payloadItem ->
                applicationData.getVehicleList()?.let {
                    it.any { vehicleInfo ->
                        vehicleInfo.vin == payloadItem.vin && payloadItem.payload?.any { it.isRead != true } == true
                    }
                } == true
            } == true
        }
    }
