package com.toyota.oneapp.features.accountnotification.dataaccess.repository

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AccountNotificationHistoryResponse
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AccountNotificationPhotoResponse
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AccountNotificationSignOutRequest
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementsResponse
import com.toyota.oneapp.features.accountnotification.dataaccess.service.AccountNotificationApi
import com.toyota.oneapp.features.accountnotification.domain.repository.AccountNotificationRepository
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.service.CommonApi
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class AccountNotificationDefaultRepository
    @Inject
    constructor(
        private val accountNotificationApi: AccountNotificationApi,
        private val commonApi: CommonApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : AccountNotificationRepository(errorParser, ioContext) {
        override suspend fun getProfilePicture(guid: String): Resource<AccountNotificationPhotoResponse?> {
            return makeApiCall {
                commonApi.getProfilePicture(
                    guid = guid,
                )
            }
        }

        override suspend fun getNotificationHistory(guid: String): Resource<AccountNotificationHistoryResponse?> {
            return makeApiCall {
                accountNotificationApi.getNotificationHistory(
                    guid = guid,
                )
            }
        }

        override suspend fun getVehicleAlerts(
            vin: String,
            brand: String,
            region: String,
            includeRenewal: String,
        ): Resource<AnnouncementsResponse?> {
            return makeApiCall {
                accountNotificationApi.getVehicleAlerts(
                    vin = vin,
                    brand = brand,
                    region = region,
                    includeRenewal = includeRenewal,
                )
            }
        }

        override suspend fun signOutRequest(
            deviceId: String,
            guid: String,
        ): Resource<BaseResponse?> {
            return makeApiCall {
                accountNotificationApi.signOut(
                    AccountNotificationSignOutRequest(deviceId = deviceId, guid = guid),
                )
            }
        }
    }
