package com.toyota.oneapp.features.accountnotification.application

import com.toyota.oneapp.features.accountnotification.domain.model.AccountNotificationHistoryPayload

sealed class NotificationHistoryState {
    object Idle : NotificationHistoryState()

    object Loading : NotificationHistoryState()

    class Success(val data: ArrayList<AccountNotificationHistoryPayload>, val hasUpdate: Boolean) :
        NotificationHistoryState()

    class Error(val errorCode: String?, val errorMessage: String?) : NotificationHistoryState()
}
