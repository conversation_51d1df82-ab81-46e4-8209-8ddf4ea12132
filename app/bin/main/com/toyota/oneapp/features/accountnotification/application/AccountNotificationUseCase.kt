package com.toyota.oneapp.features.accountnotification.application

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementPayloadResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

abstract class AccountNotificationUseCase {
    abstract fun getProfilePicture(): Flow<ProfilePictureState>

    abstract fun getNotificationHistory(): Flow<NotificationHistoryState>

    abstract fun getVehicleAlerts(vehicleInfo: VehicleInfo): Flow<AnnouncementPayloadResponse?>
}
