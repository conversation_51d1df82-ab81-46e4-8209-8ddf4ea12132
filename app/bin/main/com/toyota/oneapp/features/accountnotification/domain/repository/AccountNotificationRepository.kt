package com.toyota.oneapp.features.accountnotification.domain.repository

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AccountNotificationHistoryResponse
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AccountNotificationPhotoResponse
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementsResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import kotlin.coroutines.CoroutineContext

abstract class AccountNotificationRepository(
    errorParser: ErrorMessageParser,
    ioContext: CoroutineContext,
) : BaseRepository(errorParser, ioContext) {
    abstract suspend fun getProfilePicture(guid: String): Resource<AccountNotificationPhotoResponse?>

    abstract suspend fun getNotificationHistory(guid: String): Resource<AccountNotificationHistoryResponse?>

    abstract suspend fun getVehicleAlerts(
        vin: String,
        brand: String,
        region: String,
        includeRenewal: String,
    ): Resource<AnnouncementsResponse?>

    abstract suspend fun signOutRequest(
        deviceId: String,
        guid: String,
    ): Resource<BaseResponse?>
}
