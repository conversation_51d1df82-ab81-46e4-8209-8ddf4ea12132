package com.toyota.oneapp.features.accountnotification.application

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementPayloadResponse

sealed class VehicleAlertState {
    object Idle : VehicleAlertState()

    object Loading : VehicleAlertState()

    class Success(val data: List<AnnouncementPayloadResponse>) : VehicleAlertState()

    object Error : VehicleAlertState()
}
