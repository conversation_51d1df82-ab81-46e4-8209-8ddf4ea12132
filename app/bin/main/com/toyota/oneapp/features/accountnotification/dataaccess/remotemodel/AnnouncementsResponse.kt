package com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel

import com.toyota.oneapp.features.accountnotification.domain.model.AnnouncementMessage
import com.toyota.oneapp.features.accountnotification.domain.model.AnnouncementPayload

data class AnnouncementsResponse(
    val code: Int,
    val errors: List<Any>,
    val message: String,
    val payload: AnnouncementPayloadResponse?,
    val status: String,
)

data class AnnouncementPayloadResponse(
    val announcementMessage: String,
    val announcementTitle: String,
    val criticalAlertText: String,
    val messages: List<AnnouncementMessageResponse>,
    val viewAnnouncementText: String,
    val vin: String,
)

data class AnnouncementMessageResponse(
    val messageId: String?,
    val cardTitle: String?,
    val cardMessage: String?,
    val title: String?,
    val header: String?,
    val body: String?,
    val image: String?,
    val cta: Int?,
    val status: Int?,
    val severity: Int?,
    val locale: String?,
    val expirationDate: String?,
    val cardImage: String?,
    val alertMessage: String?,
    val ctaText: String?,
    val phone: String?,
    val secondaryCtaText: String?,
    val messageCategory: String?,
    val alertPositiveButtonText: String?,
    val alertNegativeButtonText: String?,
    val expiresText: String?,
    val dismissText: String?,
    val moreInfoText: String?,
)

fun AnnouncementPayloadResponse.toUiModel(): AnnouncementPayload {
    val messagesList = ArrayList<AnnouncementMessage>()
    messages.forEach { item ->
        messagesList.add(
            AnnouncementMessage(
                messageId = item.messageId,
                cardTitle = item.cardTitle,
                cardMessage = item.cardMessage,
                title = item.title,
            ),
        )
    }
    return AnnouncementPayload(
        announcementMessage = announcementMessage,
        announcementTitle = announcementTitle,
        messages = messagesList,
    )
}
