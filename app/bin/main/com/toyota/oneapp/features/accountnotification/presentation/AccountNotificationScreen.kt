@file:OptIn(ExperimentalMaterialApi::class)

package com.toyota.oneapp.features.accountnotification.presentation

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.app.NotificationManagerCompat
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.R
import com.toyota.oneapp.features.accountnotification.application.SignOutState
import com.toyota.oneapp.features.accountnotification.presentation.widgets.AccountNotificationContent
import com.toyota.oneapp.features.accountnotification.presentation.widgets.AccountNotificationHeader
import com.toyota.oneapp.features.core.composable.OAButton
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OAClickableButtonTextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.ftue.presentation.extension.w
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun AccountNotificationScreen(
    bottomSheetState: ModalBottomSheetState,
    viewModel: AccountNotificationViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val signOutBottomSheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            skipHalfExpanded = true,
        )

    when (viewModel.signOutState.collectAsState().value) {
        is SignOutState.Completed -> {
            // Clear all of the notifications
            val notificationManager = NotificationManagerCompat.from(context)
            notificationManager.cancelAll()
            (context as OADashboardActivity).signOut()
        }
        is SignOutState.Progress -> {
            ShowProgressIndicator(true)
        }
        else -> {
        }
    }

    LaunchedEffect(bottomSheetState.isVisible) {
        if (!bottomSheetState.isVisible && signOutBottomSheetState.isVisible) {
            signOutBottomSheetState.hide()
        }
    }

    BackHandler {
        coroutineScope.launch {
            if (signOutBottomSheetState.isVisible) {
                signOutBottomSheetState.hide()
            } else {
                bottomSheetState.hide()
            }
        }
    }

    ModalBottomSheetLayout(
        modifier = Modifier.fillMaxSize(),
        sheetBackgroundColor = AppTheme.colors.tertiary15,
        sheetState = signOutBottomSheetState,
        sheetContent = {
            SignedOutBottomSheetDialog(signOutBottomSheetState, coroutineScope) {
                viewModel.onSignOut()
            }
        },
        sheetShape = RoundedCornerShape(topStart = 30.dp, topEnd = 30.dp),
    ) {
        LazyColumn(
            Modifier
                .fillMaxSize()
                .background(AppTheme.colors.tertiary15),
        ) {
            item {
                AccountNotificationHeader(signOutBottomSheetState, coroutineScope, viewModel)
            }
            item {
                AccountNotificationContent(viewModel, coroutineScope, bottomSheetState)
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun SignedOutBottomSheetDialog(
    modalBottomSheetState: ModalBottomSheetState,
    coroutineScope: CoroutineScope,
    onSignOut: () -> Unit,
) {
    Column(
        modifier = Modifier.background(AppTheme.colors.tertiary12),
    ) {
        Column(
            Modifier
                .padding(32.dp)
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
                .background(AppTheme.colors.tertiary12),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_status_indicator),
                contentDescription = null,
                modifier = Modifier.padding(bottom = 16.dp),
            )
            OASubHeadLine1TextView(
                text = stringResource(id = R.string.AccountSettings_sign_out),
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.padding(bottom = 8.dp),
            )
            OACallOut1TextView(
                text = stringResource(id = R.string.AccountSettings_sign_out_note),
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Center,
                modifier =
                    Modifier
                        .padding(bottom = 20.dp)
                        .fillMaxWidth(),
            )
            OACallOut1TextView(
                text = stringResource(id = R.string.AccountSettings_sign_out_confirmation),
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Center,
                modifier =
                    Modifier
                        .padding(bottom = 50.dp)
                        .fillMaxWidth(),
            )

            OAClickableButtonTextView(
                text = stringResource(id = R.string.cancel),
                color = AppTheme.colors.button02a,
                modifier = Modifier.padding(bottom = 20.dp),
                onClick = {
                    coroutineScope.launch { modalBottomSheetState.hide() }
                },
            )

            OAButton(
                text = stringResource(id = R.string.AccountSettings_sign_out),
                modifier = Modifier.size(width = 192.w(), height = 52.h()),
                textModifier = Modifier.padding(all = 0.dp),
                click = {
                    onSignOut()
                },
            )
        }
    }
}
