package com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel

import com.google.gson.annotations.Expose
import com.toyota.oneapp.features.accountnotification.domain.model.ProfilePicture
import com.toyota.oneapp.network.models.BaseResponse

data class AccountNotificationPhotoResponse(
    @Expose val payload: String?,
) : BaseResponse()

fun AccountNotificationPhotoResponse.toUIModel(): ProfilePicture {
    return ProfilePicture(payload = payload)
}
