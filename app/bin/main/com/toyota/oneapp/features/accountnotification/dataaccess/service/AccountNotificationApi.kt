package com.toyota.oneapp.features.accountnotification.dataaccess.service

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AccountNotificationHistoryResponse
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AccountNotificationSignOutRequest
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementsResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.*

interface AccountNotificationApi {
    @GET("/oneapi/v2/notification/history")
    suspend fun getNotificationHistory(
        @Header("GUID") guid: String,
    ): Response<AccountNotificationHistoryResponse>

    @GET("/oneapi/v1/vehicle-alerts")
    suspend fun getVehicleAlerts(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
        @Header("X-REGION") region: String,
        @Header("INCLUDE-RENEWAL") includeRenewal: String,
    ): Response<AnnouncementsResponse>

    @POST("/oneapi/v1/logout")
    suspend fun signOut(
        @Body accountNotificationSignOutRequest: AccountNotificationSignOutRequest,
    ): Response<BaseResponse>
}
