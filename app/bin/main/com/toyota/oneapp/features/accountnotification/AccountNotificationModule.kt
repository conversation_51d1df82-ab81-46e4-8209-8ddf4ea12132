package com.toyota.oneapp.features.accountnotification

import com.toyota.oneapp.features.accountnotification.application.AccountNotificationLogic
import com.toyota.oneapp.features.accountnotification.application.AccountNotificationUseCase
import com.toyota.oneapp.features.accountnotification.dataaccess.repository.AccountNotificationDefaultRepository
import com.toyota.oneapp.features.accountnotification.domain.repository.AccountNotificationRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class AccountNotificationModule {
    @Binds
    abstract fun provideAccountNotificationRepository(
        accountNotificationDefaultRepository: AccountNotificationDefaultRepository,
    ): AccountNotificationRepository

    @Binds
    abstract fun provideAccountNotificationUseCase(accountNotificationLogic: AccountNotificationLogic): AccountNotificationUseCase
}
