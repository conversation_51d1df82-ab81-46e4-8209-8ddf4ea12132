package com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.accountnotification.domain.model.AccountNotificationHistoryItem
import com.toyota.oneapp.features.accountnotification.domain.model.AccountNotificationHistoryPayload
import kotlinx.parcelize.Parcelize

@Parcelize
data class AccountNotificationHistoryResponse(
    @SerializedName("payload") var payload: ArrayList<AccountNotificationHistoryPayloadResponse>?,
) : Parcelable

@Parcelize
data class AccountNotificationHistoryPayloadResponse(
    @SerializedName("notifications") var payload: ArrayList<AccountNotificationHistoryItemResponse>?,
    @SerializedName("modelDesc") var modelDesc: String?,
    @SerializedName("vin") var vin: String? = null,
) : Parcelable

@Parcelize
data class AccountNotificationHistoryItemResponse(
    @SerializedName("title") var title: String?,
    @SerializedName("messageId") val messageId: String?,
    @SerializedName("isRead") var isRead: Boolean? = false,
    @SerializedName("readTimestamp") var readTimestamp: String?,
    @SerializedName("notificationDate") var notificationDate: String?,
    @SerializedName("message") var message: String?,
    @SerializedName("status") var status: String?,
    @SerializedName("iconUrl") var iconUrl: String?,
    @SerializedName("subcategory") var subcategory: String?,
    @SerializedName("category") var category: String?,
    @SerializedName("displayCategory") var displayCategory: String?,
    @SerializedName("vin") var vin: String?,
    @SerializedName("type") var type: String?,
    @SerializedName("lat") val latitude: Double? = null,
    @SerializedName("lon") val longitude: Double? = null,
) : Parcelable

data class MarkReadRequestPayloadResponse(
    @SerializedName("guid") val guid: String,
    @SerializedName("messageIds") val messageIds: List<String>,
)

fun AccountNotificationHistoryResponse.toUiModel(): ArrayList<AccountNotificationHistoryPayload> {
    val response = ArrayList<AccountNotificationHistoryPayload>()
    val data = ArrayList<AccountNotificationHistoryItem>()
    payload?.forEach { payloadItem ->
        payloadItem.payload?.forEach {
            data.add(
                AccountNotificationHistoryItem(
                    title = it.title,
                    messageId = it.messageId,
                    isRead = it.isRead,
                    message = it.message,
                    status = it.status,
                    iconUrl = it.iconUrl,
                ),
            )
        }
        response.add(
            AccountNotificationHistoryPayload(
                payload = data,
                modelDesc = payloadItem.modelDesc,
                vin = payloadItem.vin,
            ),
        )
    }
    return response
}
