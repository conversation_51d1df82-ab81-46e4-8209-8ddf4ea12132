package com.toyota.oneapp.features.accountnotification.presentation

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.account.AccountManager
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.features.accountnotification.application.AccountNotificationUseCase
import com.toyota.oneapp.features.accountnotification.application.NotificationHistoryState
import com.toyota.oneapp.features.accountnotification.application.ProfilePictureState
import com.toyota.oneapp.features.accountnotification.application.SignOutState
import com.toyota.oneapp.features.accountnotification.application.VehicleAlertState
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementPayloadResponse
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.callback.LogoutCallback
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject
import kotlin.coroutines.suspendCoroutine

@HiltViewModel
class AccountNotificationViewModel
    @Inject
    constructor(
        val sharedDataSource: SharedDataSource,
        private val applicationData: ApplicationData,
        private val accountNotificationUseCase: AccountNotificationUseCase,
        private val idpData: IDPData,
        private val analyticsLogger: AnalyticsLogger,
        private val dispatcherProvider: DispatcherProvider,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val accountManager: AccountManager,
    ) : BaseViewModel() {
        private val _profilePictureState =
            MutableStateFlow<ProfilePictureState>(
                value = ProfilePictureState.Loading,
            )
        val profilePictureState = _profilePictureState.asStateFlow()

        private val _signOutState =
            MutableStateFlow<SignOutState>(
                value = SignOutState.Idle,
            )
        val signOutState = _signOutState.asStateFlow()

        private val _profileName = MutableStateFlow(value = "")
        val profileName = _profileName.asStateFlow()

        init {
            getProfileName()
            getProfilePicture()
            getNotificationHistory()
            getVehicleAlerts()
        }

        fun getProfileName() {
            var profileName = ToyotaConstants.EMPTY_STRING
            idpData.getUserProfileName()?.let { userProfileName ->
                profileName = userProfileName
            }
            _profileName.value = profileName
        }

        fun getProfilePicture() {
            viewModelScope.launch(dispatcherProvider.main()) {
                accountNotificationUseCase.getProfilePicture().flowOn(dispatcherProvider.io()).collect {
                    _profilePictureState.value = it
                }
            }
        }

        fun logGroupEvent(
            group: AnalyticsEvent,
            event: String,
        ) {
            analyticsLogger.logEventWithParameter(group.eventName, event)
        }

        fun updateDarkMode(darkMode: Boolean) {
            oneAppPreferenceModel.setDarkModeEnabled(darkMode)
        }

        fun isDarkMode(): Boolean = oneAppPreferenceModel.isDarkModeEnabled()

        fun onSignOut() {
            _signOutState.value = SignOutState.Progress
            accountManager.logout(
                object : LogoutCallback() {
                    override fun onLogoutAPISuccess() {
                        _signOutState.value = SignOutState.Completed
                    }

                    override fun onLogoutAPIError(errorMsg: String?) {
                        _signOutState.value = SignOutState.Completed
                    }
                },
            )
        }

        fun refreshNotificationHistory() {
            sharedDataSource.setNotificationHistoryData(NotificationHistoryState.Loading)
            // delay added to wait for notification read status update to complete.
            Handler(Looper.getMainLooper()).postDelayed({
                getNotificationHistory()
            }, 3000)
        }

        internal fun getNotificationHistory() {
            sharedDataSource.setNotificationHistoryData(NotificationHistoryState.Loading)
            viewModelScope.launch(dispatcherProvider.main()) {
                accountNotificationUseCase.getNotificationHistory().flowOn(dispatcherProvider.io()).collect {
                    sharedDataSource.setNotificationHistoryData(it)
                }
            }
        }

        internal fun getVehicleAlerts() {
            val announcementsList = ArrayList<AnnouncementPayloadResponse>()
            val vehicleList = applicationData.getVehicleList()?.toMutableList()
            val vehicleListIterator = vehicleList?.iterator()

            sharedDataSource.setVehicleAlertsData(VehicleAlertState.Loading)
            viewModelScope.launch(dispatcherProvider.main()) {
                while (vehicleListIterator?.hasNext() == true) {
                    val vehicleInfo = vehicleListIterator.next()
                    val announcementPayloadDeferred =
                        async {
                            fetchVehicleAnnouncement(vehicleInfo)
                        }

                    val announcementPayload = announcementPayloadDeferred.await()
                    if (announcementPayload?.messages?.isNotEmpty() == true) {
                        announcementsList.add(announcementPayload)
                    }
                }

                if (announcementsList.isNotEmpty()) {
                    sharedDataSource.setVehicleAlertsData(
                        VehicleAlertState.Success(data = announcementsList),
                    )
                } else {
                    sharedDataSource.setVehicleAlertsData(
                        VehicleAlertState.Error,
                    )
                }
            }
        }

        private suspend fun fetchVehicleAnnouncement(vehicleInfo: VehicleInfo): AnnouncementPayloadResponse? {
            return suspendCoroutine { continuation ->
                viewModelScope.launch(dispatcherProvider.main()) {
                    accountNotificationUseCase.getVehicleAlerts(vehicleInfo)
                        .flowOn(dispatcherProvider.io())
                        .collect {
                            continuation.resumeWith(Result.success(it))
                        }
                }
            }
        }
    }
