package com.toyota.oneapp.features.accountnotification.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class AccountNotificationHistory(
    var payload: ArrayList<AccountNotificationHistoryPayload>,
) : Parcelable

@Parcelize
data class AccountNotificationHistoryPayload(
    var payload: ArrayList<AccountNotificationHistoryItem>?,
    var modelDesc: String?,
    var vin: String? = null,
) : Parcelable

@Parcelize
data class AccountNotificationHistoryItem(
    var title: String?,
    val messageId: String?,
    var isRead: Boolean? = false,
    var message: String?,
    var status: String?,
    var iconUrl: String?,
) : Parcelable
