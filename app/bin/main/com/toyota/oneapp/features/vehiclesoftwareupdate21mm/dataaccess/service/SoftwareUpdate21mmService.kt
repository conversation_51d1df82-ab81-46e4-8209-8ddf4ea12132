package com.toyota.oneapp.features.vehiclesoftwareupdate21mm.dataaccess.service

import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.dataaccess.servermodel.SoftwareUpdateNotificationResponse
import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.dataaccess.servermodel.SoftwareVersionUpdateRequest
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

interface SoftwareUpdate21mmService {
    @GET("/oa21mm/v1/ota/notification")
    suspend fun fetchSoftwareUpdates(
        @Header("vin") vin: String,
    ): Response<SoftwareUpdateNotificationResponse?>

    @POST("/oa21mm/v1/ota/update/authorize")
    suspend fun softwareVersionUpdateAuthorize(
        @Body request: SoftwareVersionUpdateRequest,
    ): Response<BaseResponse>
}
