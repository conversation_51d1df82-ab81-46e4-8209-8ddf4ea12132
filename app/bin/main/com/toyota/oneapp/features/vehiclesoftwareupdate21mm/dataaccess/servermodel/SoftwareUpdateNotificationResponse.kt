package com.toyota.oneapp.features.vehiclesoftwareupdate21mm.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

class SoftwareUpdateNotificationResponse(
    @SerializedName("payload") val payload: List<SoftwareUpdateNotificationPayload?>,
)

data class SoftwareUpdateNotificationPayload(
    @SerializedName("registrationRequestId") val registrationRequestId: String?,
    @SerializedName("vin") val vin: String?,
    @SerializedName("messageContent") val messageContent: MessageContentResponse?,
)

data class MessageContentResponse(
    @SerializedName("titleOfMessage") val titleOfMessage: String?,
    @SerializedName("message") val message: String?,
    @SerializedName("updateContent") val updateContent: UpdateContentResponse?,
)

data class UpdateContentResponse(
    @SerializedName("termsOfUseStatement") val termsOfUseStatement: String?,
    @SerializedName("updateInformation") val updateInformation: String?,
    @SerializedName("currentVersion") val currentVersion: String?,
    @SerializedName("updateVersion") val updateVersion: String?,
    @SerializedName("caution") val caution: String?,
    @SerializedName("workingTime") val workingTime: String?,
    @SerializedName("contentImageUrl") val contentImageUrlList: List<String>,
    @SerializedName("urlOfOwnersManual") val urlOfOwnersManual: String?,
    @SerializedName("urlOfInstructionManualMovie") val urlOfInstructionManualMovie: String?,
    @SerializedName("howToUse") val howToUse: String?,
    @SerializedName("updatehistory") val updatehistory: String?,
)
