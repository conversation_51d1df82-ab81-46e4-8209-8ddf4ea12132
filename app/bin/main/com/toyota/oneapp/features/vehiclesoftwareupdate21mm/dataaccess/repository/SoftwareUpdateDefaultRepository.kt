package com.toyota.oneapp.features.vehiclesoftwareupdate21mm.dataaccess.repository

import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.dataaccess.servermodel.SoftwareUpdateNotificationResponse
import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.dataaccess.servermodel.SoftwareVersionUpdateRequest
import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.dataaccess.service.SoftwareUpdate21mmService
import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.domain.repository.SoftwareUpdateRepository
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class SoftwareUpdateDefaultRepository
    @Inject
    constructor(
        val service: SoftwareUpdate21mmService,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), SoftwareUpdateRepository {
        override suspend fun fetchSoftwareUpdate(vin: String): Resource<SoftwareUpdateNotificationResponse?> {
            return makeApiCall {
                service.fetchSoftwareUpdates(
                    vin = vin,
                )
            }
        }

        override suspend fun softwareVersionUpdateAuthorize(body: SoftwareVersionUpdateRequest): Resource<BaseResponse?> {
            return makeApiCall {
                service.softwareVersionUpdateAuthorize(
                    body,
                )
            }
        }
    }
