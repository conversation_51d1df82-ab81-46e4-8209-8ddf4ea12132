package com.toyota.oneapp.features.vehiclesoftwareupdate21mm.di

import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.application.SoftwareUpdateLogic
import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.application.SoftwareUpdateUseCase
import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.dataaccess.repository.SoftwareUpdateDefaultRepository
import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.domain.repository.SoftwareUpdateRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class SoftwareUpdateModule {
    @Binds
    abstract fun provideSoftwareUpdateRepository(softwareUpdateDefaultRepository: SoftwareUpdateDefaultRepository): SoftwareUpdateRepository

    @Binds
    abstract fun provideSoftwareUpdateLogic(softwareUpdateLogic: SoftwareUpdateLogic): SoftwareUpdateUseCase
}
