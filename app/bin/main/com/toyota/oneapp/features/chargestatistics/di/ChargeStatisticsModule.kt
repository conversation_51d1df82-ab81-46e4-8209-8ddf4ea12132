/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargestatistics.di

import com.toyota.oneapp.features.chargestatistics.application.ChargeStatisticsLogic
import com.toyota.oneapp.features.chargestatistics.application.ChargeStatisticsUseCase
import com.toyota.oneapp.features.chargestatistics.dataaccess.ChargeStatisticsDefaultRepo
import com.toyota.oneapp.features.chargestatistics.domain.repo.ChargeStatisticsRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class ChargeStatisticsModule {
    @Binds
    abstract fun bindChargeStatisticsRepo(repo: ChargeStatisticsDefaultRepo): ChargeStatisticsRepo

    @Binds
    abstract fun bindChargeStatisticsUseCase(logic: ChargeStatisticsLogic): ChargeStatisticsUseCase
}
