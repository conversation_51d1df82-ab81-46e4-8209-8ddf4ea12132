/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargestatistics.application

import com.toyota.oneapp.features.chargestatistics.domain.model.ChargeStatisticsModel

sealed class ChargeStatisticsState {
    object Init : ChargeStatisticsState()

    object Loading : ChargeStatisticsState()

    object EmptyStatisticsReport : ChargeStatisticsState()

    data class LoadMonthlyStatisticsReport(
        val uiModel: ChargeStatisticsModel,
    ) : ChargeStatisticsState()

    object Error : ChargeStatisticsState()
}
