/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargestatistics.presentation

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.chargehistory.presentation.FilterButton
import com.toyota.oneapp.features.chargehistory.presentation.MonthFilterContent
import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.ChargeHistoryResponse
import com.toyota.oneapp.features.chargeinfo.presentation.FindOutMoreScreen
import com.toyota.oneapp.features.chargestatistics.application.ChargeStatisticsState
import com.toyota.oneapp.features.chargestatistics.domain.model.ChargeStatisticsModel
import com.toyota.oneapp.features.chargestatistics.presentation.previewprovider.ChargeStatisticsStateProvider
import com.toyota.oneapp.features.chargestatistics.util.ChargeStatisticsUtil
import com.toyota.oneapp.features.core.composable.OABody1TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OAFullScreenBottomSheetLayout
import com.toyota.oneapp.features.core.composable.OAMonthPicker
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction

@Composable
fun ChargeStatisticsScreen(
    viewModel: ChargeStatisticsViewModel = hiltViewModel(),
    navController: NavHostController,
    statisticsResponse: ChargeHistoryResponse?,
    modifier: Modifier = Modifier,
) {
    val coroutineScope = rememberCoroutineScope()
    val bottomSheet = LocalBottomSheet.current
    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.onEvent(ChargeStatisticsEvents.InitScreen(statisticsResponse))
    }

    MonthYearPickerDialog(
        uiState = uiState,
        onCancel = { viewModel.onEvent(ChargeStatisticsEvents.OnMonthYearPick(false)) },
    ) { month, monthStr, year ->
        viewModel.onEvent(ChargeStatisticsEvents.OnFetchMonthlyReport(monthStr, month, year))
    }

    OAFullScreenBottomSheetLayout(
        backgroundColor = AppTheme.colors.tertiary15,
        screenTitle = stringResource(R.string.statistics),
        testTagId = AccessibilityId.ID_STATISTICS_BACK_BTN,
        modifier =
            modifier
                .padding(vertical = 8.dp, horizontal = 16.dp),
        onBack = { navController.popBackStack() },
    ) {
        ChargeStatisticsContent(
            uiState = uiState,
            onLaunchPicker = {
                viewModel.onEvent(
                    ChargeStatisticsEvents.OnLogEvent(
                        group = AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                        event = AnalyticsEventParam.STATISTICS_MONTH_YEAR_PICKER,
                    ),
                )
                viewModel.onEvent(ChargeStatisticsEvents.OnMonthYearPick(true))
            },
        ) {
            viewModel.onEvent(
                ChargeStatisticsEvents.OnLogEvent(
                    group = AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                    event = AnalyticsEventParam.LEARN_MORE,
                ),
            )
            coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                FindOutMoreScreen(
                    bottomSheetState = it,
                ) { group, event ->
                    viewModel.onEvent(ChargeStatisticsEvents.OnLogEvent(group, event))
                }
            }
        }
    }
}

@Composable
private fun ChargeStatisticsContent(
    uiState: ChargeStatisticsUIState,
    modifier: Modifier = Modifier,
    onLaunchPicker: () -> Unit,
    onLearnMore: () -> Unit,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier =
            modifier
                .fillMaxSize()
                .padding(vertical = 8.dp),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
                modifier
                    .fillMaxSize()
                    .weight(1f, fill = false),
        ) {
            FilterButton(
                text = stringResource(R.string.month),
                isSelected = uiState.isMonthPicked ?: false,
                accessibilityID = AccessibilityId.ID_STATISTICS_MONTH_YEAR_PICKER_BTN,
                modifier =
                    Modifier
                        .wrapContentSize()
                        .padding(bottom = 16.dp),
                content = {
                    MonthFilterContent(
                        text = uiState.monthLabel ?: stringResource(R.string.filter_by_month),
                        isSelected = uiState.isMonthPicked ?: false,
                        modifier =
                            Modifier
                                .align(Alignment.Center)
                                .testTagID(AccessibilityId.ID_STATISTICS_MONTH_YEAR_PICKER_LABEL),
                    )
                },
            ) {
                onLaunchPicker()
            }

            when (uiState.screenState) {
                is ChargeStatisticsState.Init -> {
                    // don't do anything
                }
                is ChargeStatisticsState.Loading -> {
                    ShowProgressIndicator(dialogState = true)
                }
                is ChargeStatisticsState.LoadMonthlyStatisticsReport -> {
                    MonthlyReports(
                        reports = uiState.screenState.uiModel,
                        testTagID = AccessibilityId.ID_REPORT_AVAILABLE_LAYOUT,
                        modifier =
                            Modifier
                                .fillMaxSize()
                                .weight(1f, false),
                    )
                }
                is ChargeStatisticsState.EmptyStatisticsReport -> {
                    EmptyReport(
                        testTagID = AccessibilityId.ID_NO_REPORT_AVAILABLE_LAYOUT,
                        modifier =
                            Modifier
                                .fillMaxSize()
                                .weight(1f, false),
                    )
                }
                is ChargeStatisticsState.Error -> {
                    // don't do anything
                }
            }
        }

        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(top = 8.dp, bottom = 8.dp),
        ) {
            PrimaryButton02(
                text = stringResource(R.string.eco_learn_more),
                modifier =
                    Modifier
                        .align(Alignment.CenterHorizontally)
                        .testTagID(AccessibilityId.ID_LEARN_MORE_BTN),
            ) {
                onLearnMore()
            }
        }
    }
}

@Composable
private fun MonthlyReports(
    reports: ChargeStatisticsModel,
    testTagID: String,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier =
            modifier
                .fillMaxWidth()
                .testTagID(testTagID),
    ) {
        item {
            ReportCard(
                title = stringResource(R.string.co2_emission, reports.co2Emission),
                description = stringResource(R.string.emission_saved),
                iconRes = R.drawable.ic_carbon_emission,
                testTagID = AccessibilityId.ID_CO2_EMISSION_CARD,
                modifier = Modifier.padding(vertical = 4.dp),
                bgColor = Color(0xFFDEDEDE),
                tint = Color(0xFF262626),
            )
        }

        item {
            ReportCard(
                title = stringResource(R.string.monthly_challenge),
                description =
                    stringResource(
                        R.string.monthly_challenge_description,
                        reports.monthlyChallenge,
                        reports.numOfDays,
                    ),
                iconRes = R.drawable.ic_eco_badge,
                testTagID = AccessibilityId.ID_MONTHLY_CHALLENGE_CARD,
                modifier = Modifier.padding(vertical = 4.dp),
                bgColor = AppTheme.colors.button03d,
                tint = AppTheme.colors.tertiary15,
            )
        }

        reports.healthPercentage.let {
            item {
                ReportCard(
                    title = stringResource(R.string.health_impact_reduction),
                    description =
                        if (it != null && it > 0) {
                            stringResource(R.string.health_impact_description, it)
                        } else {
                            stringResource(R.string.zero_health_impact_description)
                        },
                    iconRes = R.drawable.ic_health_impact_reduction,
                    testTagID = AccessibilityId.ID_HEALTH_IMPACT_REDUCTION_CARD,
                    modifier = Modifier.padding(vertical = 4.dp),
                    bgColor = AppTheme.colors.secondary01,
                    tint = AppTheme.colors.tertiary15,
                )
            }
        }

        item {
            ReportCard(
                title = stringResource(R.string.tree_equivalence),
                description =
                    stringResource(
                        R.string.tree_equivalence_description,
                        reports.co2Emission,
                        reports.equivalentTreesPlanted,
                    ),
                iconRes = R.drawable.ic_tree_equalivent,
                testTagID = AccessibilityId.ID_TREES_EQUIVALENT_CARD,
                modifier = Modifier.padding(vertical = 4.dp),
                bgColor = AppTheme.colors.button03d,
                tint = AppTheme.colors.tertiary15,
            )
        }
    }
}

@Composable
private fun ReportCard(
    title: String,
    description: String,
    @DrawableRes iconRes: Int,
    testTagID: String,
    modifier: Modifier = Modifier,
    bgColor: Color,
    tint: Color,
) {
    Card(
        backgroundColor = AppTheme.colors.tile05,
        elevation = 0.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .testTagID(testTagID),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .size(64.dp)
                        .clip(CircleShape)
                        .background(color = bgColor),
                contentAlignment = Alignment.Center,
            ) {
                Icon(
                    painter = painterResource(id = iconRes),
                    contentDescription = title,
                    tint = tint,
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            Column {
                OASubHeadLine3TextView(
                    text = title,
                    color = AppTheme.colors.tertiary03,
                )

                Spacer(modifier = Modifier.height(8.dp))

                OACallOut1TextView(
                    text = description,
                    color = AppTheme.colors.tertiary05,
                )
            }
        }
    }
}

@Composable
private fun MonthYearPickerDialog(
    uiState: ChargeStatisticsUIState,
    modifier: Modifier = Modifier,
    onCancel: () -> Unit,
    onPicked: (month: Int, monthStr: String, year: Int) -> Unit,
) {
    if (uiState.showMonthYearPickerDialog) {
        val selectedMonthIndex = ChargeStatisticsUtil.getSelectedMonthIndex(uiState.selectedtMonth)
        OAMonthPicker(
            defaultMonth = selectedMonthIndex,
            defaultYear = uiState.selectedYear,
            showDialog = true,
            testTagIds = Pair("", ""),
            onCancel = { onCancel() },
            modifier = modifier,
        ) { month, monthStr, year ->
            onCancel()
            onPicked(month, monthStr, year)
        }
    }
}

@Composable
private fun EmptyReport(
    testTagID: String,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier =
            modifier
                .fillMaxSize()
                .testTagID(testTagID),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
                Modifier
                    .wrapContentSize()
                    .align(Alignment.Center),
        ) {
            Image(
                painter = painterResource(R.drawable.ic_no_statistics_report),
                contentDescription = stringResource(R.string.no_statistics_report),
                modifier = Modifier.size(48.dp),
            )

            Spacer(modifier = Modifier.height(16.dp))

            OASubHeadLine1TextView(
                text = stringResource(R.string.no_statistics_report),
                color = AppTheme.colors.tertiary03,
            )

            Spacer(modifier = Modifier.height(8.dp))

            OABody1TextView(
                text = stringResource(R.string.no_statistics_report_description),
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Center,
            )
        }
    }
}

@Preview
@Composable
fun PreviewChargeStatistics(
    @PreviewParameter(ChargeStatisticsStateProvider::class) uiState: ChargeStatisticsUIState,
) {
    OAFullScreenBottomSheetLayout(
        backgroundColor = AppTheme.colors.tertiary15,
        screenTitle = stringResource(R.string.statistics),
        testTagId = "",
        modifier =
            Modifier
                .padding(vertical = 8.dp, horizontal = 16.dp),
        onBack = { },
    ) {
        ChargeStatisticsContent(
            uiState = uiState,
            onLaunchPicker = {},
        ) { }
    }
}
