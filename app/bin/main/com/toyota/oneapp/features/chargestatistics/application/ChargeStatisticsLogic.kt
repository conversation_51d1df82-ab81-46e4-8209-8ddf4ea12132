/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargestatistics.application

import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.MonthlyReport
import com.toyota.oneapp.features.chargestatistics.domain.repo.ChargeStatisticsRepo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.util.getDateNameAsStringFormats
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Locale
import javax.inject.Inject

class ChargeStatisticsLogic
    @Inject
    constructor(
        private val chargeStatisticsRepo: ChargeStatisticsRepo,
    ) : ChargeStatisticsUseCase {
        companion object {
            private const val FORMAT_MMM_YYYY = "MMM, yyyy"
            private const val FORMAT_MM_YYYY = "MM yyyy"
        }

        override fun fetchMonthlyStatisticsReport(
            selectedMonth: String,
            selectedYear: Int,
            vin: String,
            generation: String,
            brand: String,
        ): Flow<MonthlyReport?> =
            flow {
                val response =
                    chargeStatisticsRepo.fetchMonthlyStatisticsReport(
                        month =
                            getDateNameAsStringFormats(
                                date = "$selectedMonth, $selectedYear",
                                inputFormat = FORMAT_MMM_YYYY,
                                outputFormat = FORMAT_MM_YYYY,
                                inputLocale = Locale.US,
                            ).replace(" ", ""),
                        vin = vin,
                        generation = generation,
                        brand = brand,
                    )

                if (response is Resource.Success && !response.data?.monthlyReports.isNullOrEmpty()) {
                    emit(response.data?.monthlyReports?.firstOrNull())
                } else {
                    emit(null)
                }
            }
    }
