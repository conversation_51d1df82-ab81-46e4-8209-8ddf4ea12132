/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargestatistics.presentation.previewprovider

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.toyota.oneapp.features.chargestatistics.application.ChargeStatisticsState
import com.toyota.oneapp.features.chargestatistics.domain.model.ChargeStatisticsModel
import com.toyota.oneapp.features.chargestatistics.presentation.ChargeStatisticsUIState

class ChargeStatisticsStateProvider : PreviewParameterProvider<ChargeStatisticsUIState> {
    val state1 =
        ChargeStatisticsUIState(
            screenState = ChargeStatisticsState.EmptyStatisticsReport,
        )

    val uiModel =
        ChargeStatisticsModel(
            co2Emission = "175",
            monthlyChallenge = 4,
            numOfDays = 30,
            healthPercentage = 4,
            equivalentTreesPlanted = 5,
        )
    val state2 =
        ChargeStatisticsUIState(
            isMonthPicked = true,
            selectedtMonth = "Jun",
            selectedYear = 2024,
            monthLabel = "June 2024",
            screenState = ChargeStatisticsState.LoadMonthlyStatisticsReport(uiModel),
        )
    override val values = sequenceOf(state1, state2)
}
