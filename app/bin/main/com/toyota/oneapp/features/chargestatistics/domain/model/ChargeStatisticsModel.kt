/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargestatistics.domain.model

import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.MonthlyReport
import com.toyota.oneapp.features.chargestatistics.util.ChargeStatisticsUtil
import com.toyota.oneapp.util.DoubleUtil.roundOffTo

data class ChargeStatisticsModel(
    val co2Emission: String,
    val monthlyChallenge: Int,
    val numOfDays: Int,
    val healthPercentage: Int?,
    val equivalentTreesPlanted: Int,
)

fun MonthlyReport.toChargeStatisticsModel(
    selectedMonth: String,
    selectedYear: Int,
): ChargeStatisticsModel =
    ChargeStatisticsModel(
        co2Emission =
            if (totalCo2 != null && totalCo2 > 0) {
                totalCo2.roundOffTo(2).toString()
            } else {
                "0"
            },
        monthlyChallenge = leafCount ?: 0,
        numOfDays = ChargeStatisticsUtil.getNumberOfDays(selectedMonth, selectedYear),
        healthPercentage = healthDollarPercentage?.roundOffTo(0)?.toInt(),
        equivalentTreesPlanted =
            if (totalCo2EquivalentTreesPlanted != null && totalCo2EquivalentTreesPlanted > 0) {
                totalCo2EquivalentTreesPlanted.toInt()
            } else {
                0
            },
    )
