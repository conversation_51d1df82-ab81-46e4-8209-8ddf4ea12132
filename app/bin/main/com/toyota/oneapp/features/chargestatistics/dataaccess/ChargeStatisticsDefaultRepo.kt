/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargestatistics.dataaccess

import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.ChargeHistoryResponse
import com.toyota.oneapp.features.chargeinfo.dataaccess.service.ChargeInfoAPI
import com.toyota.oneapp.features.chargestatistics.domain.repo.ChargeStatisticsRepo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class ChargeStatisticsDefaultRepo
    @Inject
    constructor(
        private val chargeInfoAPI: ChargeInfoAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext),
        ChargeStatisticsRepo {
        override suspend fun fetchMonthlyStatisticsReport(
            month: String,
            vin: String,
            generation: String,
            brand: String,
        ): Resource<ChargeHistoryResponse?> =
            makeApiCall {
                chargeInfoAPI.fetchChargeHistoryData(
                    brand = brand,
                    generation = generation,
                    vin = vin,
                    reportType = "monthly",
                    month = month,
                )
            }
    }
