/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargestatistics.util

import java.util.Calendar

object ChargeStatisticsUtil {
    private val monthMap =
        mapOf(
            0 to "JAN",
            1 to "FEB",
            2 to "MAR",
            3 to "APR",
            4 to "MAY",
            5 to "JUN",
            6 to "JUL",
            7 to "AUG",
            8 to "SEP",
            9 to "OCT",
            10 to "NOV",
            11 to "DEC",
        )

    private val fullMonthMap =
        mapOf(
            "JAN" to Pair("January", 31),
            "FEB" to Pair("February", 28),
            "MAR" to Pair("March", 31),
            "APR" to Pair("April", 30),
            "MAY" to Pair("May", 31),
            "JUN" to Pair("June", 30),
            "JUL" to Pair("July", 31),
            "AUG" to Pair("August", 31),
            "SEP" to Pair("September", 30),
            "OCT" to Pair("October", 31),
            "NOV" to Pair("November", 30),
            "DEC" to <PERSON><PERSON>("December", 31),
        )

    fun getMonthIndex(monthName: String): Int {
        val keys = monthMap.filterValues { it == monthName.uppercase() }.keys
        return keys.firstOrNull() ?: 0
    }

    fun getMonthShortName(
        index: Int,
        lastMonthId: Int,
    ): String {
        var monthIndex = lastMonthId - index
        if (monthIndex < 0) {
            monthIndex += 12
        }
        return monthMap[monthIndex].orEmpty()
    }

    fun getFullMonthName(month: String?) = fullMonthMap[month?.uppercase()]?.first.orEmpty()

    fun getNumberOfDays(
        month: String,
        year: Int,
    ) = when (month.uppercase()) {
        monthMap[1] -> {
            if (year % 4 == 0) 29 else 28
        }

        else -> fullMonthMap[month.uppercase()]?.second ?: 0
    }

    fun getDefaultMonthYear(
        monthName: String,
        currentMonth: Int = Calendar.getInstance().get(Calendar.MONTH),
        currentYear: Int = Calendar.getInstance().get(Calendar.YEAR),
    ): Pair<String, Int> {
        val defaultMonthIndex = getMonthIndex(monthName)
        val defaultYear = if (currentMonth < defaultMonthIndex) currentYear - 1 else currentYear
        return Pair(monthName, defaultYear)
    }

    fun getSelectedMonthIndex(
        selectedMonthName: String,
        defaultIndex: Int = Calendar.getInstance().get(Calendar.MONTH),
    ): Int {
        val currentMonth =
            selectedMonthName.takeIf { it.isNotEmpty() }?.let {
                getMonthIndex(it)
            } ?: run {
                defaultIndex
            }
        return currentMonth
    }
}
