/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargestatistics.application

import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.MonthlyReport
import kotlinx.coroutines.flow.Flow

interface ChargeStatisticsUseCase {
    fun fetchMonthlyStatisticsReport(
        selectedMonth: String,
        selectedYear: Int,
        vin: String,
        generation: String,
        brand: String,
    ): Flow<MonthlyReport?>
}
