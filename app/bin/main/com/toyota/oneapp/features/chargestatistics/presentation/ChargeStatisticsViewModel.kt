/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargestatistics.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.ChargeHistoryResponse
import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.MonthlyReport
import com.toyota.oneapp.features.chargestatistics.application.ChargeStatisticsState
import com.toyota.oneapp.features.chargestatistics.application.ChargeStatisticsUseCase
import com.toyota.oneapp.features.chargestatistics.domain.model.toChargeStatisticsModel
import com.toyota.oneapp.features.chargestatistics.util.ChargeStatisticsUtil
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import java.time.Year
import javax.inject.Inject

@HiltViewModel
class ChargeStatisticsViewModel
    @Inject
    constructor(
        private val analyticsLogger: AnalyticsLogger,
        private val chargeStatisticsUseCase: ChargeStatisticsUseCase,
        private val applicationData: ApplicationData,
        private val dispatcherProvider: DispatcherProvider,
    ) : BaseViewModel<ChargeStatisticsUIState, ChargeStatisticsEvents>() {
        init {
            onEvent(
                ChargeStatisticsEvents.OnLogEvent(
                    group = AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                    event = AnalyticsEventParam.STATISTICS_CARD_TAP,
                ),
            )
        }

        override fun defaultState() = ChargeStatisticsUIState()

        override fun onEvent(event: ChargeStatisticsEvents) {
            when (event) {
                is ChargeStatisticsEvents.InitScreen -> {
                    event.response?.monthlyReports?.let {
                        if (it.isNotEmpty()) {
                            val defaultMonthYearPair =
                                ChargeStatisticsUtil.getDefaultMonthYear(
                                    it[0].monthName.orEmpty(),
                                )
                            updateButtonUIState(defaultMonthYearPair.first, defaultMonthYearPair.second)
                            handleScreenState(
                                selectedMonth = defaultMonthYearPair.first,
                                selectedYear = defaultMonthYearPair.second,
                                monthlyReport = it[0],
                            )
                        } else {
                            handleScreenState(null)
                        }
                    } ?: run { handleScreenState(null) }
                }
                is ChargeStatisticsEvents.OnLogEvent -> {
                    logAnalytics(event.group, event.event)
                }
                is ChargeStatisticsEvents.OnMonthYearPick -> {
                    state.update {
                        it.copy(showMonthYearPickerDialog = event.showDialog)
                    }
                }
                is ChargeStatisticsEvents.OnFetchMonthlyReport -> {
                    updateButtonUIState(event.pickedMonthStr, event.pickedYear)
                    fetchMonthlyStatisticsReport(event.pickedMonthStr, event.pickedYear)
                }
            }
        }

        private fun logAnalytics(
            group: AnalyticsEvent,
            eventName: String,
        ) {
            analyticsLogger.logEventWithParameter(group.eventName, eventName)
        }

        private fun handleScreenState(
            monthlyReport: MonthlyReport?,
            selectedMonth: String = "",
            selectedYear: Int = 0,
        ) {
            if (monthlyReport == null) {
                state.update { _state ->
                    _state.copy(
                        screenState = ChargeStatisticsState.EmptyStatisticsReport,
                    )
                }
                return
            }

            monthlyReport.toChargeStatisticsModel(selectedMonth, selectedYear).let {
                state.update { _state ->
                    _state.copy(
                        screenState = ChargeStatisticsState.LoadMonthlyStatisticsReport(it),
                    )
                }
            }
        }

        private fun updateButtonUIState(
            selectedMonthStr: String,
            selectedYear: Int,
        ) {
            state.update { _state ->
                _state.copy(
                    isMonthPicked = true,
                    selectedtMonth = selectedMonthStr,
                    selectedYear = selectedYear,
                    monthLabel = "${ChargeStatisticsUtil.getFullMonthName(selectedMonthStr)} $selectedYear",
                )
            }
        }

        private fun fetchMonthlyStatisticsReport(
            selectedMonth: String,
            selectedYear: Int,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                state.update { it.copy(screenState = ChargeStatisticsState.Loading) }
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    chargeStatisticsUseCase
                        .fetchMonthlyStatisticsReport(
                            selectedMonth = selectedMonth,
                            selectedYear = selectedYear,
                            vin = vehicle.vin,
                            brand = vehicle.brand,
                            generation = vehicle.generation,
                        ).flowOn(dispatcherProvider.io())
                        .collect { monthlyReport ->
                            handleScreenState(
                                selectedMonth = selectedMonth,
                                selectedYear = selectedYear,
                                monthlyReport = monthlyReport,
                            )
                        }
                }
            }
        }
    }

data class ChargeStatisticsUIState(
    val isMonthPicked: Boolean? = null,
    val selectedtMonth: String = "",
    val selectedYear: Int = Year.now().value,
    val monthLabel: String? = null,
    val showMonthYearPickerDialog: Boolean = false,
    val screenState: ChargeStatisticsState = ChargeStatisticsState.Init,
)

sealed class ChargeStatisticsEvents {
    data class InitScreen(
        val response: ChargeHistoryResponse?,
    ) : ChargeStatisticsEvents()

    data class OnLogEvent(
        val group: AnalyticsEvent,
        val event: String,
    ) : ChargeStatisticsEvents()

    data class OnMonthYearPick(
        val showDialog: Boolean,
    ) : ChargeStatisticsEvents()

    data class OnFetchMonthlyReport(
        val pickedMonthStr: String,
        val pickedMonth: Int,
        val pickedYear: Int,
    ) : ChargeStatisticsEvents()
}
