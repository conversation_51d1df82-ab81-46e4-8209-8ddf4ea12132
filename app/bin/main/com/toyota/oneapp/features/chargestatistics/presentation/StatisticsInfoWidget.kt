/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargestatistics.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.text.HtmlCompat
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeinfo.application.ChargeStatisticsCardState
import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.ChargeHistoryResponse
import com.toyota.oneapp.features.chargeinfo.domain.model.StatisticsInfoCardModel
import com.toyota.oneapp.features.chargeinfo.domain.model.TipsModel
import com.toyota.oneapp.features.chargeinfo.presentation.ChargeInfoViewModel
import com.toyota.oneapp.features.core.composable.ChargeInfoCommonCardShimmer
import com.toyota.oneapp.features.core.composable.OABarChart
import com.toyota.oneapp.features.core.composable.OABorderButton
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.composable.OAFootNote1AnnotatedTextView
import com.toyota.oneapp.features.core.composable.OAFootNote1TextView
import com.toyota.oneapp.features.core.composable.OAIconArrowRight
import com.toyota.oneapp.features.core.composable.OAPieChart
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.PieData
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.toAnnotatedString

@Composable
fun StatisticsInfoWidget(
    viewModel: ChargeInfoViewModel,
    modifier: Modifier = Modifier,
    onLearnMore: () -> Unit,
    onClick: (statisticsResponse: ChargeHistoryResponse?) -> Unit,
) {
    val viewState by viewModel.statisticsCardState.collectAsState()

    when (viewState) {
        is ChargeStatisticsCardState.Loading -> {
            ChargeInfoCommonCardShimmer(modifier)
        }
        is ChargeStatisticsCardState.ShowStatisticsData -> {
            StatisticsDetailCard(
                uiModel = (viewState as ChargeStatisticsCardState.ShowStatisticsData).uiModel,
                modifier = modifier,
                onLearnMore = { onLearnMore() },
            ) {
                onClick(
                    (viewState as ChargeStatisticsCardState.ShowStatisticsData).uiModel.response,
                )
            }
        }
        else -> {
            // Do nothing
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun StatisticsDetailCard(
    uiModel: StatisticsInfoCardModel,
    modifier: Modifier = Modifier,
    onLearnMore: () -> Unit,
    onClick: () -> Unit,
) {
    Card(
        onClick = { onClick() },
        backgroundColor = AppTheme.colors.tile03,
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(16.dp),
        ) {
            StatisticsTitleLayout(
                modifier =
                    Modifier
                        .padding(bottom = 32.dp),
            )

            EcoChargingInfo(uiModel = uiModel)

            uiModel.monthlyReports?.let {
                OABarChart(
                    barDataModel = it,
                    maxHeight = 80.dp,
                    modifier = Modifier.padding(top = 24.dp),
                )
            }

            uiModel.tips?.let {
                TipsInfoWidget(
                    uiModel = it,
                    modifier =
                        Modifier
                            .padding(top = 8.dp),
                )
            }

            OABorderButton(
                accessibilityID = "",
                text = stringResource(id = R.string.Common_learn_more),
                modifier =
                    Modifier
                        .align(Alignment.CenterHorizontally)
                        .testTagID(AccessibilityId.ID_ECO_LEARN_MORE_BTN)
                        .padding(top = 24.dp),
            ) {
                onLearnMore()
            }
        }
    }
}

@Composable
private fun StatisticsTitleLayout(modifier: Modifier = Modifier) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier =
            modifier
                .fillMaxWidth(),
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Image(
                painter = painterResource(id = R.drawable.ic_ev_statistics),
                contentDescription = "",
                colorFilter = ColorFilter.tint(AppTheme.colors.tertiary00),
                modifier =
                    Modifier
                        .size(24.dp),
            )

            Spacer(modifier = Modifier.width(16.dp))

            OASubHeadLine1TextView(
                text = stringResource(id = R.string.statistics),
                color = AppTheme.colors.tertiary03,
            )
        }

        OAIconArrowRight()
    }
}

@Composable
private fun EcoChargingInfo(
    uiModel: StatisticsInfoCardModel,
    modifier: Modifier = Modifier,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier =
            modifier
                .fillMaxWidth(),
    ) {
        OAPieChart(
            centerImage = R.drawable.ic_eco_badge,
            contentDescription = "",
            pieDataPoints = PieDataPoints(achievedLeaves = uiModel.achievedLeaves),
            modifier =
                Modifier
                    .size(60.dp)
                    .testTagID(AccessibilityId.ID_ACHIEVED_LEAVES_PIE_CHART),
        )

        Spacer(modifier = Modifier.width(16.dp))

        Column {
            OACallOut2TextView(
                text = stringResource(id = R.string.eco_charging_title),
                color = AppTheme.colors.tertiary03,
            )

            Spacer(modifier = Modifier.height(4.dp))

            OAFootNote1TextView(
                text = stringResource(id = R.string.achieved_leaves, uiModel.achievedLeaves),
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Start,
                modifier = Modifier.testTagID(AccessibilityId.ID_ACHIEVED_LEAVES_TXT),
            )
        }
    }
}

@Composable
private fun TipsInfoWidget(
    uiModel: TipsModel,
    modifier: Modifier = Modifier,
) {
    val tips =
        stringResource(
            R.string.statistics_tips_note,
            uiModel.lastMonth,
            uiModel.percentChange,
            uiModel.moreOrLess,
            uiModel.prevMonth,
        )
    val spannedString = HtmlCompat.fromHtml(tips, HtmlCompat.FROM_HTML_MODE_COMPACT)

    Card(
        backgroundColor = AppTheme.colors.tile05,
        elevation = 0.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(12.dp),
        ) {
            Image(
                painter = painterResource(R.drawable.ic_tip),
                contentDescription = "",
                modifier = Modifier.size(32.dp),
            )

            Spacer(modifier = Modifier.width(8.dp))

            OAFootNote1AnnotatedTextView(
                text = spannedString.toAnnotatedString(),
                color = AppTheme.colors.tertiary03,
                textAlign = TextAlign.Start,
            )
        }
    }
}

@Composable
private fun PieDataPoints(achievedLeaves: Int): List<PieData> {
    val pieDataPoints =
        arrayListOf(
            PieData(10, color = AppTheme.colors.tertiary10),
            PieData(10, color = AppTheme.colors.tertiary10),
            PieData(10, color = AppTheme.colors.tertiary10),
            PieData(10, color = AppTheme.colors.tertiary10),
            PieData(10, color = AppTheme.colors.tertiary10),
            PieData(10, color = AppTheme.colors.tertiary10),
            PieData(10, color = AppTheme.colors.tertiary10),
        )

    pieDataPoints.forEachIndexed { index, pieData ->
        if (index < achievedLeaves) {
            pieData.color = AppTheme.colors.button03d
        }
    }
    return pieDataPoints
}
