package com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.repository

import com.toyota.oneapp.features.find.dataacess.servermodel.PreferredDealerResponse
import com.toyota.oneapp.features.find.dataacess.service.FindApi
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleHealthDiagnosticsResponse
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleHealthReportResponse
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleSafetyRecallsResponse
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.service.VehicleHealthAPI
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.domain.repo.VehicleHealthRepo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class VehicleHealthDefaultRepo
    @Inject
    constructor(
        private val findService: FindApi,
        private val vehicleHealthService: VehicleHealthAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), VehicleHealthRepo {
        override suspend fun fetchVehicleDiagnosticsFromAPI(
            vin: String,
            brand: String,
            generation: String?,
            region: String,
        ): Resource<VehicleHealthDiagnosticsResponse?> {
            return makeApiCall {
                vehicleHealthService.fetchVehicleHealthDiagnostics(vin, brand, generation, region)
            }
        }

        override suspend fun fetchVehicleHealthReportFromAPI(
            vin: String,
            brand: String,
            generation: String?,
            region: String,
        ): Resource<VehicleHealthReportResponse?> {
            return makeApiCall {
                vehicleHealthService.fetchVehicleHealthReport(vin, brand, generation, region)
            }
        }

        override suspend fun fetchVehicleSafetyRecallsFromAPI(vin: String): Resource<VehicleSafetyRecallsResponse?> {
            return makeApiCall {
                vehicleHealthService.fetchSafetyRecallsDetails(vin)
            }
        }

        override suspend fun fetchPreferredDealerFromAPI(
            vin: String,
            region: String,
        ): Resource<PreferredDealerResponse?> {
            return makeApiCall {
                findService.fetchPreferredDealer(vin, region)
            }
        }
    }
