package com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class VehicleHealthReportResponse(
    @SerializedName("payload") var payload: VehicleHealthReportPayload?,
)

data class VehicleHealthReportPayload(
    @SerializedName("status") var status: String?,
    @SerializedName("infoIcon") var infoIcons: List<InfoIcon>?,
    @SerializedName("auditTrail") var auditTrail: AuditTrial?,
    @SerializedName("safetyRecall") var safetyRecall: SafetyRecallResponse?,
    @SerializedName("safetyRecallsList") var safetyRecallsList: List<SafetyRecall>?,
    @SerializedName("serviceCampaign") var serviceCampaign: ServiceCampaignResponse?,
    @SerializedName("serviceCampaigns") var serviceCampaigns: List<ServiceCampaign>?,
    @SerializedName("vehicleAlert") var vehicleAlert: VehicleAlertResponse?,
    @SerializedName("vehicleAlertList") var vehicleAlertList: List<VehicleAlert>?,
    @SerializedName("maintenanceInformation") var maintenanceInformation: MaintenanceInformation?,
    @SerializedName("vehicleStatus") var vehicleStatus: VehicleStatus?,
    @SerializedName("vehicleDetails") var vehicleDetails: VehicleDetails?,
    @SerializedName("recallsListExists") var recallsListExists: Boolean?,
    @SerializedName("campaignsExists") var campaignsExists: Boolean?,
    @SerializedName("vhrid") var vhrid: String?,
)

data class InfoIcon(
    @SerializedName("deviceName") var deviceName: String?,
    @SerializedName("deviceUrl") var deviceUrl: String?,
)

data class AuditTrial(
    @SerializedName("vhrgenTime") var vhrgenTime: String?,
    @SerializedName("vhrgenType") var vhrgenType: String?,
    @SerializedName("vhrgenID") var vhrgenID: String?,
    @SerializedName("vhrtype") var vhrtype: String?,
)

data class SafetyRecallResponse(
    @SerializedName("alertIcon") var alertIcon: List<AlertIcon>?,
    @SerializedName("popupTitle") var popupTitle: String?,
    @SerializedName("popupDesc") var popupDesc: String?,
    @SerializedName("sectionTitle") var sectionTitle: String?,
)

data class SafetyRecall(
    @SerializedName("title") var title: String?,
    @SerializedName("remedy") var remedy: String?,
    @SerializedName("description") var description: String?,
    @SerializedName("remedyAvailable") var remedyAvailable: String?,
    @SerializedName("dealerReferenceID") var dealerReferenceID: String?,
    @SerializedName("nhtsarecallDate") var nhtsarecallDate: String?,
    @SerializedName("nhstarecallNumber") var nhstarecallNumber: String?,
    @SerializedName("icon") var icon: String?,
    @SerializedName("faqurl") var faqurl: String?,
)

data class ServiceCampaignResponse(
    @SerializedName("alertIcon") var alertIcon: List<AlertIcon>?,
    @SerializedName("popupTitle") var popupTitle: String?,
    @SerializedName("popupDesc") var popupDesc: String?,
    @SerializedName("sectionTitle") var sectionTitle: String?,
)

data class ServiceCampaign(
    @SerializedName("title") var title: String?,
    @SerializedName("remedyDesc") var remedyDesc: String?,
    @SerializedName("activityDesc") var activityDesc: String?,
    @SerializedName("remedyAvailable") var remedyAvailable: String?,
    @SerializedName("dealerRefID") var dealerRefID: String?,
    @SerializedName("nhstarecallNumber") var nhstarecallNumber: String?,
    @SerializedName("campaignDate") var campaignDate: String?,
    @SerializedName("icon") var icon: String?,
)

data class VehicleAlertResponse(
    @SerializedName("alertIcon") var alertIcon: List<AlertIcon>?,
    @SerializedName("popupTitle") var popupTitle: String?,
    @SerializedName("popupDesc") var popupDesc: String?,
    @SerializedName("sectionTitle") var sectionTitle: String?,
)

data class VehicleAlert(
    @SerializedName("wngname") var wngname: String?,
    @SerializedName("wngdesc") var wngdesc: String?,
    @SerializedName("wngicon") var wngicon: List<WngIcon>?,
    @SerializedName("wngdate") var wngdate: String?,
    @SerializedName("wngtype") var wngtype: String?,
)

data class MaintenanceInformation(
    @SerializedName("alertIcon") var alertIcon: List<AlertIcon>?,
    @SerializedName("currentMileage") var currentMileage: Double?,
    @SerializedName("currentMileageUnit") var currentMileageUnit: String?,
    @SerializedName("popupTitle") var popupTitle: String?,
    @SerializedName("popupDesc") var popupDesc: String?,
    @SerializedName("sectionTitle") var sectionTitle: String?,
    @SerializedName("mntServiceErrorMessage") var mntServiceErrorMessage: String?,
    @SerializedName("mntServiceVerbiage") var mntServiceVerbiage: String?,
)

data class VehicleStatus(
    @SerializedName("fuelIconDesc") var fuelIconDesc: String?,
    @SerializedName("milesToEmptyDesc") var milesToEmptyDesc: String?,
    @SerializedName("engOilLevelStatus") var engOilLevelStatus: String?,
    @SerializedName("engOilLevelIconDesc") var engOilLevelIconDesc: String?,
    @SerializedName("imgUrlEngOilLevelStatus") var imgUrlEngOilLevelStatus: List<AlertIcon>?,
    @SerializedName("smartKeyBatteryTitle") var smartKeyBatteryTitle: String?,
    @SerializedName("smartKeyBatteryDesc") var smartKeyBatteryDesc: String?,
    @SerializedName("imgUrlSmartKeyBatteryStatus") var imgUrlSmartKeyBatteryStatus: List<AlertIcon>?,
)

data class VehicleDetails(
    @SerializedName("color") var color: String?,
    @SerializedName("modelNo") var modelNo: String?,
    @SerializedName("modelYear") var modelYear: String?,
    @SerializedName("dateOfFirstUse") var dateOfFirstUse: String?,
    @SerializedName("imgUrlServActStatus") var imgUrlServActStatus: List<AlertIcon>?,
    @SerializedName("servActStatus") var servActStatus: String?,
    @SerializedName("ersubStatus") var ersubStatus: String?,
    @SerializedName("edsubStatus") var edsubStatus: String?,
    @SerializedName("ednotificationPref") var ednotificationPref: String?,
    @SerializedName("vin") var vin: String?,
)

data class AlertIcon(
    @SerializedName("deviceName") var deviceName: String?,
    @SerializedName("deviceUrl") var deviceUrl: String?,
)

data class WngIcon(
    @SerializedName("deviceName") var deviceName: String?,
    @SerializedName("deviceUrl") var deviceUrl: String?,
)
