package com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class VehicleSafetyRecallsResponse(
    @SerializedName("payload") var payload: List<SafetyRecallsPayload?>?,
)

data class SafetyRecallsPayload(
    @SerializedName("campaignTitle") var campaignTitle: String?,
    @SerializedName("description") var description: String?,
    @SerializedName("remedyDescription") var remedyDescription: String?,
    @SerializedName("recallDate") var recallDate: String?,
    @SerializedName("recallId") var recallId: String?,
    @SerializedName("campaignNumber") var campaignNumber: String?,
    @SerializedName("campaignType") var campaignType: String?,
    @SerializedName("campaignDate") var campaignDate: String?,
    @SerializedName("status") var status: String?,
)
