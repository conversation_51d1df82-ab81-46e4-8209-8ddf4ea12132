package com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.application

import com.toyota.oneapp.features.find.dataacess.servermodel.DealerResponse
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.SafetyRecallsPayload
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleHealthDiagnosticsPayload
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleHealthReportPayload
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.domain.model.VehicleHealthUIModel
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.domain.repo.VehicleHealthRepo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class VehicleHealthLogic
    @Inject
    constructor(
        private val repository: VehicleHealthRepo,
    ) : VehicleHealthUseCase {
        override suspend fun fetchVehicleDiagnostics(
            vin: String,
            brand: String,
            generation: String?,
            region: String,
        ): Flow<VehicleHealthDiagnosticsPayload?> {
            return flow {
                val response = repository.fetchVehicleDiagnosticsFromAPI(vin, brand, generation, region)

                if (response is Resource.Success) {
                    emit(response.data?.payload)
                } else {
                    emit(null)
                }
            }
        }

        override suspend fun fetchVehicleHealthReport(
            vin: String,
            brand: String,
            generation: String?,
            region: String,
        ): Flow<VehicleHealthReportPayload?> {
            return flow {
                val response =
                    repository.fetchVehicleHealthReportFromAPI(
                        vin,
                        brand,
                        generation,
                        region,
                    )

                if (response is Resource.Success) {
                    emit(response.data?.payload)
                } else {
                    emit(null)
                }
            }
        }

        override suspend fun fetchVehicleSafetyRecalls(vin: String): Flow<List<SafetyRecallsPayload?>?> {
            return flow {
                val response = repository.fetchVehicleSafetyRecallsFromAPI(vin)

                if (response is Resource.Success) {
                    emit(response.data?.payload)
                } else {
                    emit(null)
                }
            }
        }

        override suspend fun fetchPreferredDealer(
            vin: String,
            region: String,
        ): Flow<DealerResponse?> {
            return flow {
                val response = repository.fetchPreferredDealerFromAPI(vin, region)

                if (response is Resource.Success) {
                    emit(response.data?.payload?.firstOrNull())
                } else {
                    emit(null)
                }
            }
        }

        override fun isVehicleHealthOK(vehicleHealthList: List<VehicleHealthUIModel>): Boolean {
            for (uiModel in vehicleHealthList) {
                if (uiModel.isAlert) {
                    return false
                }
            }
            return true
        }
    }
