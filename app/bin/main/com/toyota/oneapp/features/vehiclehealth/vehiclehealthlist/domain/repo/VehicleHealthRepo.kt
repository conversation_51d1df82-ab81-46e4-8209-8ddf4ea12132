package com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.domain.repo

import com.toyota.oneapp.features.find.dataacess.servermodel.PreferredDealerResponse
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleHealthDiagnosticsResponse
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleHealthReportResponse
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleSafetyRecallsResponse
import com.toyota.oneapp.network.Resource

interface VehicleHealthRepo {
    suspend fun fetchVehicleDiagnosticsFromAPI(
        vin: String,
        brand: String,
        generation: String?,
        region: String,
    ): Resource<VehicleHealthDiagnosticsResponse?>

    suspend fun fetchVehicleHealthReportFromAPI(
        vin: String,
        brand: String,
        generation: String?,
        region: String,
    ): Resource<VehicleHealthReportResponse?>

    suspend fun fetchVehicleSafetyRecallsFromAPI(vin: String): Resource<VehicleSafetyRecallsResponse?>

    suspend fun fetchPreferredDealerFromAPI(
        vin: String,
        region: String,
    ): Resource<PreferredDealerResponse?>
}
