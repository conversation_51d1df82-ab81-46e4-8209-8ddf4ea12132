package com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.application

import com.toyota.oneapp.features.find.dataacess.servermodel.DealerResponse

sealed class VehicleHealthState {
    object Initialize : VehicleHealthState()

    object Loading : VehicleHealthState()

    object DismissLoading : VehicleHealthState()

    data class Success(
        val dealerPayload: DealerResponse?,
    ) : VehicleHealthState()

    object ShowEmptyScreen : VehicleHealthState()

    class Error(val errorCode: String?, val errorMessage: String?) : VehicleHealthState()
}
