package com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.application

import com.toyota.oneapp.features.find.dataacess.servermodel.DealerResponse
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.SafetyRecallsPayload
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleHealthDiagnosticsPayload
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleHealthReportPayload
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.domain.model.VehicleHealthUIModel
import kotlinx.coroutines.flow.Flow

interface VehicleHealthUseCase {
    suspend fun fetchVehicleDiagnostics(
        vin: String,
        brand: String,
        generation: String?,
        region: String,
    ): Flow<VehicleHealthDiagnosticsPayload?>

    suspend fun fetchVehicleHealthReport(
        vin: String,
        brand: String,
        generation: String?,
        region: String,
    ): Flow<VehicleHealthReportPayload?>

    suspend fun fetchVehicleSafetyRecalls(vin: String): Flow<List<SafetyRecallsPayload?>?>

    suspend fun fetchPreferredDealer(
        vin: String,
        region: String,
    ): Flow<DealerResponse?>

    fun isVehicleHealthOK(vehicleHealthList: List<VehicleHealthUIModel>): Boolean
}
