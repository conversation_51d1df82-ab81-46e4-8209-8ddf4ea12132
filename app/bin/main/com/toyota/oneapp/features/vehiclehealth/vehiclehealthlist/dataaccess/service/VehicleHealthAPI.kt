package com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.service

import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleHealthDiagnosticsResponse
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleHealthReportResponse
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel.VehicleSafetyRecallsResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Headers

interface VehicleHealthAPI {
    @Headers("Content-Type: application/json")
    @GET("/oneapi/v1/vehiclehealth/report")
    suspend fun fetchVehicleHealthReport(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
        @Header("GENERATION") generation: String?,
        @Header("X-REGION") region: String,
    ): Response<VehicleHealthReportResponse?>

    @Headers("Content-Type: application/json")
    @GET("/oneapi/v1/vehiclehealth/status")
    suspend fun fetchVehicleHealthDiagnostics(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
        @Header("GENERATION") generation: String?,
        @Header("X-REGION") region: String,
    ): Response<VehicleHealthDiagnosticsResponse?>

    @Headers("Content-Type: application/json")
    @GET("/oneapi/v2/service-campaign")
    suspend fun fetchSafetyRecallsDetails(
        @Header("vin") vin: String,
    ): Response<VehicleSafetyRecallsResponse?>
}
