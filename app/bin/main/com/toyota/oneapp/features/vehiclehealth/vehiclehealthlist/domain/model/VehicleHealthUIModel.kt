package com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.domain.model

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes

sealed class VehicleHealthUIModel(
    val id: String,
    @DrawableRes val icon: Int,
    @StringRes val title: Int,
    @StringRes val description: Int,
    val count: Int?,
    val isAlert: Boolean,
    val primaryTestTag: String,
    val secondaryTestTag: String,
) {
    data class SafetyRecalls(
        val recallsId: String,
        @DrawableRes val recallsIcon: Int,
        @StringRes val recallsTitle: Int,
        @StringRes val recallsDescription: Int,
        val recallsCount: Int?,
        val isRecallsAlert: Boolean,
        val safetyRecallsDetailList: List<SafetyRecallsUIModel>?,
        val primaryTag: String,
        val secondaryTag: String,
    ) : VehicleHealthUIModel(
            recallsId,
            recallsIcon,
            recallsTitle,
            recallsDescription,
            recallsCount,
            isRecallsAlert,
            primaryTag,
            secondaryTag,
        )

    data class ServiceCampaigns(
        val campaignId: String,
        @DrawableRes val campaignIcon: Int,
        @StringRes val campaignTitle: Int,
        @StringRes val campaignDescription: Int,
        val campaignCount: Int?,
        val isCampaignAlert: Boolean,
        val serviceCampaignsDetailList: List<ServiceCampaignUIModel>?,
        val primaryTag: String,
        val secondaryTag: String,
    ) : VehicleHealthUIModel(
            campaignId,
            campaignIcon,
            campaignTitle,
            campaignDescription,
            campaignCount,
            isCampaignAlert,
            primaryTag,
            secondaryTag,
        )

    data class VehicleAlert(
        val alertId: String,
        @DrawableRes val alertIcon: Int,
        @StringRes val alertTitle: Int,
        @StringRes val alertDescription: Int,
        val alertCount: Int?,
        val isVehicleAlert: Boolean,
        val vehicleAlertDetailList: List<VehicleAlertUIModel>?,
        val isEngineAlert: Boolean? = null,
        val primaryTag: String,
        val secondaryTag: String,
        @StringRes val engineStatus: Int? = null,
    ) : VehicleHealthUIModel(
            alertId,
            alertIcon,
            alertTitle,
            alertDescription,
            alertCount,
            isVehicleAlert,
            primaryTag,
            secondaryTag,
        )

    data class KeyFob(
        val keyFobId: String,
        @DrawableRes val keyFobIcon: Int,
        @StringRes val keyFobTitle: Int,
        @StringRes val keyFobDescription: Int,
        @StringRes val keyFobDetailedDescription: Int,
        val isKeyFobAlert: Boolean,
        val primaryTag: String,
        val secondaryTag: String,
    ) : VehicleHealthUIModel(
            keyFobId,
            keyFobIcon,
            keyFobTitle,
            keyFobDescription,
            null,
            isKeyFobAlert,
            primaryTag,
            secondaryTag,
        )

    data class VehicleHealthReport(
        val vhrId: String,
        @DrawableRes val vhrIcon: Int,
        @StringRes val vhrTitle: Int,
        @StringRes val vhrDescription: Int,
        val isVhrAlert: Boolean,
        val primaryTag: String,
        val secondaryTag: String,
    ) : VehicleHealthUIModel(
            vhrId,
            vhrIcon,
            vhrTitle,
            vhrDescription,
            null,
            isVhrAlert,
            primaryTag,
            secondaryTag,
        )
}

data class SafetyRecallsUIModel(
    val currentFlow: String,
    @StringRes val headerTitle: Int,
    val date: String,
    val overview: String,
    val description: String,
    val remedyStatus: String = "",
    val remedyDescription: String,
    val dealerId: String,
    val title: String = "",
    val dealerReferenceId: String = "",
    val nhtsaId: String = "",
    val isAlert: Boolean = false,
    val id: Int? = null,
    val primaryTestTag: String,
    val secondaryTestTag: String,
)

data class ServiceCampaignUIModel(
    val currentFlow: String,
    @StringRes val headerTitle: Int,
    val date: String,
    val overview: String,
    val description: String,
    val remedyStatus: String = "",
    val remedyDescription: String,
    val dealerId: String,
    val title: String = "",
    val dealerReferenceId: String = "",
    val nhtsaId: String = "",
    val isAlert: Boolean = false,
    val id: Int? = null,
    val primaryTestTag: String,
    val secondaryTestTag: String,
)

data class VehicleAlertUIModel(
    val currentFlow: String,
    @StringRes val headerTitle: Int,
    val date: String = "",
    val overview: String,
    val description: String,
    val remedyStatus: String = "",
    val remedyDescription: String = "",
    val dealerId: String = "",
    val nhtsaId: String = "",
    val isAlert: Boolean = false,
    val title: String = "",
    val primaryTestTag: String,
    val secondaryTestTag: String,
)
