package com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class VehicleHealthDiagnosticsResponse(
    @SerializedName("payload") var payload: VehicleHealthDiagnosticsPayload?,
)

data class VehicleHealthDiagnosticsPayload(
    @SerializedName("mileage") var mileage: String?,
    @SerializedName("mileageUnit") var mileageUnit: String?,
    @SerializedName("fuelLevel") var fuelLevel: String?,
    @SerializedName("fuelUnit") var fuelUnit: String?,
    @SerializedName("smartKeyBatLastUpdTime") var smartKeyBatLastUpdTime: String?,
    @SerializedName("quantityOfEngOilIcon") var quantityOfEngOilIcon: List<AlertIcon>?,
    @SerializedName("quantityOfEngOilLastUpdTime") var quantityOfEngOilLastUpdTime: String?,
    @SerializedName("warning") var warning: List<Warning>?,
    @SerializedName("vin") var vin: String?,
    @SerializedName("wnglastUpdTime") var wnglastUpdTime: String?,
)

data class Warning(
    @SerializedName("wngdesc") var wngdesc: String?,
    @SerializedName("wngownersManual") var wngownersManual: String?,
    @SerializedName("wngdcmtime") var wngdcmtime: String?,
    @SerializedName("wngcondFlag") var wngcondFlag: String?,
    @SerializedName("wngfilterType") var wngfilterType: String?,
    @SerializedName("wngtype") var wngtype: String?,
    @SerializedName("wngicon") var wngicon: List<AlertIcon>?,
    @SerializedName("wngcode") var wngcode: String?,
)
