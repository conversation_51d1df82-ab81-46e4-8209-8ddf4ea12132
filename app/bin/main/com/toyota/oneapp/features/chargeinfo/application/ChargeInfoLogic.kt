/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeinfo.application

import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.ChargeHistoryResponse
import com.toyota.oneapp.features.chargeinfo.domain.model.BottomButtonInfo
import com.toyota.oneapp.features.chargeinfo.domain.model.BottomButtonType
import com.toyota.oneapp.features.chargeinfo.domain.model.ChargeHistoryInfoCardModel
import com.toyota.oneapp.features.chargeinfo.domain.model.ChargeInfoUIModel
import com.toyota.oneapp.features.chargeinfo.domain.model.EVBatteryInfo
import com.toyota.oneapp.features.chargeinfo.domain.model.PHEVBatteryInfo
import com.toyota.oneapp.features.chargeinfo.domain.model.RangeInfo
import com.toyota.oneapp.features.chargeinfo.domain.model.toChargeHistoryModel
import com.toyota.oneapp.features.chargeinfo.domain.model.toStatisticsModel
import com.toyota.oneapp.features.chargeinfo.domain.repo.ChargeInfoRepo
import com.toyota.oneapp.features.chargeinfo.util.ChargeInfoUtil
import com.toyota.oneapp.features.chargeinfo.util.ChargeInfoUtil.appendEstimate
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeInfo
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeTimerRequest
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.getAppRequestNo
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.isCharging
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.isPluggedIn
import com.toyota.oneapp.features.core.commonapicalls.domain.repository.CommonApiRepository
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dashboard.dashboard.domain.model.Telemetry
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.DoubleUtil.roundOffTo
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class ChargeInfoLogic
    @Inject
    constructor(
        private val dateUtil: DateUtil,
        private val commonRepository: CommonApiRepository,
        private val chargeInfoRepo: ChargeInfoRepo,
    ) : ChargeInfoUseCase {
        companion object {
            private const val COMMAND_IMMEDIATE_CHARGE = "immediate-charge"
        }

        override fun fetchEVVechicleInfo(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry?,
        ): Flow<ChargeInfoUIModel?> =
            flow {
                val response =
                    commonRepository.fetchChargeManagementDetail(
                        vin = vehicleInfo.vin,
                        generation = vehicleInfo.generation,
                        brand = vehicleInfo.brand,
                    )

                if (response is Resource.Success) {
                    emit(response.data?.toChargeInfoUIModel(vehicleInfo, telemetry))
                } else {
                    emit(null)
                }
            }

        override fun mapToChargeInfoUIModel(
            response: ElectricStatusResponse?,
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry?,
        ): Flow<ChargeInfoUIModel?> =
            flow {
                val uiModel = response?.toChargeInfoUIModel(vehicleInfo, telemetry)
                emit(uiModel)
            }

        override fun fetchChargeHistoryData(
            reportType: String,
            vehicleInfo: VehicleInfo,
        ): Flow<ChargeHistoryResponse?> =
            flow {
                val response =
                    chargeInfoRepo.fetchChargeHistoryData(
                        reportType = reportType,
                        month = ToyotaConstants.EMPTY_STRING,
                        vehicleInfo = vehicleInfo,
                    )

                emit((response as? Resource.Success)?.data)
            }

        override fun postStartCharging(
            vin: String,
            generation: String,
            brand: String,
        ): Flow<String?> {
            return flow {
                val response =
                    commonRepository.postElectricVehicleCommand(
                        vin = vin,
                        brand = brand,
                        generation = generation,
                        requestBody =
                            ChargeTimerRequest(
                                command = COMMAND_IMMEDIATE_CHARGE,
                                remoteHvac = null,
                                reservationCharge = null,
                            ),
                    )

                if (response is Resource.Success) {
                    emit(response.data?.payload?.getAppRequestNo())
                } else {
                    emit(null)
                }
            }
        }

        override fun fetchElectricStatusForRemoteControl(
            vehicleInfo: VehicleInfo,
            telemetry: Telemetry?,
            appReqNo: String,
        ): Flow<StartChargingState?> {
            return flow {
                val response =
                    commonRepository.getEVRemoteControlStatus(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        generation = vehicleInfo.generation,
                        appRequestNo = appReqNo,
                    )

                if (response is Resource.Success) {
                    response.data?.payload?.let {
                        it.remoteControlResult?.also { result ->
                            if (result.status == 0 && result.result == 0) {
                                emit(
                                    StartChargingState.Success(
                                        uiModel = response.data?.toChargeInfoUIModel(vehicleInfo, telemetry),
                                    ),
                                )
                            } else {
                                emit(null)
                            }
                        }
                    } ?: run {
                        emit(null)
                    }
                } else {
                    emit(StartChargingState.Error)
                }
            }
        }

        override fun constructChargeHistoryCard(historyResponse: ChargeHistoryResponse?): ChargeHistoryCardState {
            val uiModel = historyResponse?.lastCharge?.toChargeHistoryModel(dateUtil)
            when (uiModel) {
                is ChargeHistoryInfoCardModel.ChargeHistoryDetailModel -> {
                    return ChargeHistoryCardState.ShowLastChargeData(uiModel)
                }
                else -> {
                    return ChargeHistoryCardState.NoHistory
                }
            }
        }

        override fun constructStatisticsCardModel(response: ChargeHistoryResponse?): ChargeStatisticsCardState {
            val uiModel = response?.toStatisticsModel()
            uiModel?.let {
                return ChargeStatisticsCardState.ShowStatisticsData(it)
            } ?: run {
                return ChargeStatisticsCardState.Error
            }
        }
    }

private fun ElectricStatusResponse.toChargeInfoUIModel(
    vehicleInfo: VehicleInfo,
    telemetry: Telemetry?,
): ChargeInfoUIModel? {
    this.payload.vehicleInfo?.chargeInfo?.let { chargeInfo ->
        return ChargeInfoUIModel(
            response = this,
            isEV = vehicleInfo.isEVModel,
            isEVPhp = vehicleInfo.isEVPhp,
            range = chargeInfo.calculateWithoutACRangeInfo(vehicleInfo.isEVModel, telemetry),
            rangeWithtAC =
                RangeInfo(
                    range = chargeInfo.evDistanceAC ?: 0.0,
                    unit = chargeInfo.evDistanceUnit.appendEstimate(),
                    description = R.string.ev_with_ac_desc,
                ),
            evBatteryInfo =
                vehicleInfo
                    .takeIf { it.isEVModel }
                    ?.let { chargeInfo.toEVBatteryInfo() },
            phevBatteryInfo =
                vehicleInfo
                    .takeIf { it.isEVPhp }
                    ?.let { chargeInfo.toEVPhpBatteryInfo(telemetry) },
            bottomButtonInfo = chargeInfo.getBottomButtonInfo(),
        )
    }
    return null
}

private fun ChargeInfo.calculateWithoutACRangeInfo(
    isEV: Boolean,
    telemetry: Telemetry?,
): RangeInfo {
    if (isEV) {
        return RangeInfo(
            range = evDistance ?: 0.0,
            unit = evDistanceUnit.appendEstimate(),
            description = R.string.ev_without_ac_desc,
        )
    }

    val phevEVRange = evDistance ?: 0.0
    val phevHVRange = (telemetry?.distanceToEmpty?.value as? Int)?.toDouble() ?: 0.0
    return RangeInfo(
        range = phevEVRange + phevHVRange,
        unit = evDistanceUnit.appendEstimate(),
        description = R.string.phev_without_ac_desc,
    )
}

private fun ChargeInfo.toEVBatteryInfo(): EVBatteryInfo =
    EVBatteryInfo(
        chargingPercentage = chargeRemainingAmount?.toInt() ?: 0,
        isCharging = isCharging(),
        isPlugConnected = isPluggedIn(),
        estimatedRemaining = getEstimatedRemaining(),
    )

private fun ChargeInfo.toEVPhpBatteryInfo(telemetry: Telemetry?): PHEVBatteryInfo =
    PHEVBatteryInfo(
        chargingPercentage = chargeRemainingAmount?.toInt() ?: 0,
        fuelLevel = telemetry?.fuelLevel ?: 0,
        isCharging = isCharging(),
        isPlugConnected = isPluggedIn(),
        estimatedRemaining = getEstimatedRemaining(),
        evRangeInfo =
            RangeInfo(
                range = evDistance ?: 0.0,
                unit = evDistanceUnit,
            ),
        evMaxRangeInfo =
            RangeInfo(
                range = evTravelableDistance ?: 0.0,
                unit = evDistanceUnit,
            ),
        gasRangeInfo =
            RangeInfo(
                range = (telemetry?.distanceToEmpty?.value as? Int)?.toDouble() ?: 0.0,
                unit = telemetry?.distanceToEmpty?.unit ?: "mi",
            ),
        gasMaxRangeInfo =
            RangeInfo(
                range = gasolineTravelableDistance ?: 0.0,
                unit = evDistanceUnit,
            ),
    )

private fun ChargeInfo.getEstimatedRemaining(): String =
    when {
        isCharging() -> {
            ChargeInfoUtil.getTimeFormattedDuration(remainingChargeTime ?: 0)
        }
        isPluggedIn() -> {
            "${(evDistance?.roundOffTo(1) ?: "0")} $evDistanceUnit"
        }
        else -> {
            ToyotaConstants.EMPTY_STRING
        }
    }

private fun ChargeInfo.getBottomButtonInfo(): BottomButtonInfo =
    when {
        isPluggedIn() -> {
            BottomButtonInfo(
                canShow = !isCharging(),
                buttonText = R.string.start_charging_btn,
                buttonType = BottomButtonType.START_CHARGING,
                buttonTestTagId = AccessibilityId.ID_START_CHARGING_BTN,
            )
        }
        else -> {
            BottomButtonInfo(
                canShow = !isCharging(),
                buttonText = R.string.find_nearby_stations_btn,
                buttonType = BottomButtonType.FIND_NEARBY_STATIONS,
                buttonTestTagId = AccessibilityId.ID_FIND_NEARBY_STATIONS_BTN,
            )
        }
    }
