/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeinfo.application

import com.toyota.oneapp.features.chargeinfo.domain.model.ChargeHistoryInfoCardModel
import com.toyota.oneapp.features.chargeinfo.domain.model.ChargeInfoUIModel
import com.toyota.oneapp.features.chargeinfo.domain.model.StatisticsInfoCardModel

sealed class ChargeInfoState {
    object Init : ChargeInfoState()

    object Loading : ChargeInfoState()

    data class ShowChargeInfo(
        val uiModel: ChargeInfoUIModel,
    ) : ChargeInfoState()

    object Error : ChargeInfoState()
}

sealed class ChargeHistoryCardState {
    object Init : ChargeHistoryCardState()

    object Loading : ChargeHistoryCardState()

    data class ShowLastChargeData(
        val uiModel: ChargeHistoryInfoCardModel.ChargeHistoryDetailModel,
    ) : ChargeHistoryCardState()

    object NoHistory : ChargeHistoryCardState()

    object Error : ChargeHistoryCardState()
}

sealed class ChargeStatisticsCardState {
    object Init : ChargeStatisticsCardState()

    object Loading : ChargeStatisticsCardState()

    data class ShowStatisticsData(
        val uiModel: StatisticsInfoCardModel,
    ) : ChargeStatisticsCardState()

    object Error : ChargeStatisticsCardState()
}

sealed class StartChargingState {
    data class Success(
        val uiModel: ChargeInfoUIModel?,
    ) : StartChargingState()

    object Error : StartChargingState()
}
