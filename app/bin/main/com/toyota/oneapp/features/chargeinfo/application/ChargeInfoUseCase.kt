/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeinfo.application

import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.ChargeHistoryResponse
import com.toyota.oneapp.features.chargeinfo.domain.model.ChargeInfoUIModel
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.dashboard.dashboard.domain.model.Telemetry
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface ChargeInfoUseCase {
    fun fetchEVVechicleInfo(
        vehicleInfo: VehicleInfo,
        telemetry: Telemetry?,
    ): Flow<ChargeInfoUIModel?>

    fun mapToChargeInfoUIModel(
        response: ElectricStatusResponse?,
        vehicleInfo: VehicleInfo,
        telemetry: Telemetry?,
    ): Flow<ChargeInfoUIModel?>

    fun fetchChargeHistoryData(
        reportType: String,
        vehicleInfo: VehicleInfo,
    ): Flow<ChargeHistoryResponse?>

    fun postStartCharging(
        vin: String,
        generation: String,
        brand: String,
    ): Flow<String?>

    fun fetchElectricStatusForRemoteControl(
        vehicleInfo: VehicleInfo,
        telemetry: Telemetry?,
        appReqNo: String,
    ): Flow<StartChargingState?>

    fun constructChargeHistoryCard(historyResponse: ChargeHistoryResponse?): ChargeHistoryCardState

    fun constructStatisticsCardModel(response: ChargeHistoryResponse?): ChargeStatisticsCardState
}
