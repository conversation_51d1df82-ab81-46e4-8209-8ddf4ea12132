/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeinfo.dataaccess.repository

import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.ChargeHistoryResponse
import com.toyota.oneapp.features.chargeinfo.dataaccess.service.ChargeInfoAPI
import com.toyota.oneapp.features.chargeinfo.domain.repo.ChargeInfoRepo
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class ChargeInfoDefaultRepo
    @Inject
    constructor(
        private val chargeInfoAPI: ChargeInfoAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext),
        ChargeInfoRepo {
        override suspend fun fetchChargeHistoryData(
            reportType: String,
            month: String,
            vehicleInfo: VehicleInfo,
        ): Resource<ChargeHistoryResponse?> =
            makeApiCall {
                chargeInfoAPI.fetchChargeHistoryData(
                    brand = vehicleInfo.brand,
                    generation = vehicleInfo.generation,
                    vin = vehicleInfo.vin,
                    reportType = reportType,
                    month = month,
                )
            }
    }
