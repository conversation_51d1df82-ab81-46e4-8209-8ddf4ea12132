/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class ChargeHistoryResponse(
    @SerializedName("monthly_reports") val monthlyReports: List<MonthlyReport>?,
    @SerializedName("current_week_report") val currentWeekReport: CurrentWeekReport?,
    @SerializedName("last_charge") val lastCharge: ChargeSession?,
) : Parcelable

@Parcelize
data class ChargeSession(
    @SerializedName("charge_location_type") val chargeLocationType: String?,
    @SerializedName("start_time") val startTime: String?,
    @SerializedName("end_time") val endTime: String?,
    @SerializedName("soc_before_charging") val socBeforeCharging: String?,
    @SerializedName("soc_after_charging") val socAfterCharging: String?,
    @SerializedName("total_charge_kwhr") val totalChargeKwhr: String?,
    @SerializedName("latitude") val latitude: Double?,
    @SerializedName("longitude") val longitude: Double?,
    @SerializedName("address") val address: String?,
    @SerializedName("watttime_details") val watttimeDetails: WattTimeDetails?,
    @SerializedName("cdr_details") val cdrDetails: CdrDetails?,
) : Parcelable

@Parcelize
data class WattTimeDetails(
    @SerializedName("charge_clasification") val chargeClasification: String?,
    @SerializedName("region") val region: String?,
    @SerializedName("co2_value") val co2Value: String?,
    @SerializedName("health_dollar_value") val healthDollarValue: String?,
) : Parcelable

@Parcelize
data class CdrDetails(
    @SerializedName("card_last4") val cardLast4: String?,
    @SerializedName("partner_name") val partnerName: String?,
    @SerializedName("total_amount") val totalAmount: String?,
) : Parcelable

@Parcelize
data class MonthlyReport(
    @SerializedName("month_name") val monthName: String?,
    @SerializedName("charging_count") val chargingCount: Int?,
    @SerializedName("leaf_count") val leafCount: Int?,
    @SerializedName("total_charge_kwhr") val totalChargeKwhr: Double?,
    @SerializedName("total_co2") val totalCo2: Double?,
    @SerializedName("health_dollar_percentage") val healthDollarPercentage: Double?,
    @SerializedName("total_co2_equivalent_trees_planted") val totalCo2EquivalentTreesPlanted: Double?,
) : Parcelable

@Parcelize
data class CurrentWeekReport(
    @SerializedName("charging_count") val chargingCount: Int?,
    @SerializedName("leaf_count") val leafCount: Int?,
    @SerializedName("total_charge_kwhr") val totalChargeKwhr: String?,
) : Parcelable
