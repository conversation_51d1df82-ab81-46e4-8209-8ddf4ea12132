/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeinfo.presentation

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.chargehistory.presentation.ChargeHistoryWidget
import com.toyota.oneapp.features.chargeinfo.domain.model.BottomButtonInfo
import com.toyota.oneapp.features.chargeinfo.domain.model.BottomButtonType
import com.toyota.oneapp.features.chargeinfo.navigation.ChargeInfoRoute
import com.toyota.oneapp.features.chargeinfo.navigation.ChargeInfoRoute.Companion.ARG_STATISTICS_RESPONSE
import com.toyota.oneapp.features.chargeschedule.presentation.widgets.ChargeScheduleWidget
import com.toyota.oneapp.features.chargestatistics.presentation.StatisticsInfoWidget
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.presentation.component.ChargingNetworksComposable
import com.toyota.oneapp.features.chargingnetwork.presentation.viewmodel.ChargingNetworksViewModel
import com.toyota.oneapp.features.cleanassist.presentation.CleanAssistViewModel
import com.toyota.oneapp.features.cleanassist.presentation.CleanAssistWidget
import com.toyota.oneapp.features.core.commonapicalls.domain.model.DialogData
import com.toyota.oneapp.features.core.composable.OAAlertDialog
import com.toyota.oneapp.features.core.composable.OAAppBar
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.navigation.route.launchPlugAndChargeActivation
import com.toyota.oneapp.features.core.theme.AppTheme.colors
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import com.toyota.oneapp.features.core.util.launchSecondaryBottomSheetAction
import com.toyota.oneapp.features.entrollment.application.EnrollmentState
import com.toyota.oneapp.features.entrollment.presentation.ChargePointCard
import com.toyota.oneapp.features.entrollment.presentation.EnrollmentViewModel
import com.toyota.oneapp.features.entrollment.presentation.EvGoCard
import com.toyota.oneapp.features.entrollment.presentation.WalletCard
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.ui.newdashboard.DashboardActivity
import com.toyota.oneapp.ui.newdashboard.kNavItem
import com.toyota.oneapp.util.NavigationUtil
import kotlinx.coroutines.launch

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun ChargeInfoScreen(
    navHostController: NavHostController,
    viewModel: ChargeInfoViewModel = hiltViewModel(),
    chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel(),
    enrollmentViewModel: EnrollmentViewModel = hiltViewModel(),
    chargingNetworksViewModel: ChargingNetworksViewModel = hiltViewModel(),
    cleanAssistViewModel: CleanAssistViewModel = hiltViewModel(),
    isChargeNow: Boolean = false,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val previousRoute = navHostController.previousBackStackEntry?.destination?.route
    val showProgress by viewModel.showProgress.collectAsState()
    val buttonInfo = viewModel.buttonInfo.collectAsState().value
    val enrollmentState = enrollmentViewModel.enrollmentState.collectAsState().value
    val isZipEnteredOnEligibilityScreenValid by chargeAssistViewModel.chargeAssistVMHelper
        .isZipEnteredOnEligibilityScreenValid
        .collectAsState()

    val isEVPluggedIN by chargeAssistViewModel.chargeAssistVMHelper.isEVPluggedIN.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.fetchVehicleTelemetry(isChargeNow)
    }

    ShowProgressIndicator(dialogState = showProgress)

    Box {
        Column(
            modifier =
                modifier
                    .fillMaxSize()
                    .background(colors.tertiary15)
                    .padding(vertical = 8.dp, horizontal = 16.dp),
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .weight(1f, fill = false),
            ) {
                OAAppBar(
                    title = stringResource(id = R.string.charge_info_title),
                    testTagId = AccessibilityId.ID_CHARGE_INFO_BACK_BTN,
                ) {
                    val shouldShowHomeScreen =
                        previousRoute in listOf("application_submitted_screen", "zipcode_not_available_screen") ||
                            previousRoute?.startsWith("enroll_utility_program_screen/") == true
                    when {
                        shouldShowHomeScreen -> navHostController.navigate(OAScreen.Home.route)
                        else -> navHostController.popBackStack()
                    }
                }

                ChargeInfoCardList(
                    navController = navHostController,
                    viewModel = viewModel,
                    chargeAssistViewModel = chargeAssistViewModel,
                    enrollmentViewModel = enrollmentViewModel,
                    chargingNetworksViewModel = chargingNetworksViewModel,
                    cleanAssistViewModel = cleanAssistViewModel,
                    modifier = Modifier.padding(vertical = 8.dp),
                ) {
                    val caBackEndFlagOn = chargeAssistViewModel.isCABackEndFlagOn.value
                    if (caBackEndFlagOn) {
                        navHostController.navigate(
                            ChargeInfoRoute.ChargeScheduleNestedRoute.route +
                                "/$isEVPluggedIN?isZipFromEligibilityScreenValid=" +
                                "$isZipEnteredOnEligibilityScreenValid",
                        )
                    } else {
                        navHostController.navigate(ChargeInfoRoute.ChargeScheduleNestedRoute.route)
                    }
                }
            }

            AnimatedVisibility(
                visible = buttonInfo?.canShow == true,
                enter = EnterTransition.None,
                exit = ExitTransition.None,
            ) {
                ChargeInfoBottomButton(
                    text = stringResource(id = buttonInfo?.buttonText ?: R.string.find_nearby_stations_btn),
                    modifier = Modifier.testTagID(buttonInfo?.buttonTestTagId.orEmpty()),
                ) {
                    // refactored to reduce complexity
                    chargeInfoCanShowFunctionality(
                        buttonInfo,
                        viewModel,
                        navHostController,
                        context,
                    )
                }
            }

            EvGoComplimentaryAlertDialog(enrollmentState)
        }
    }
}

fun chargeInfoCanShowFunctionality(
    buttonInfo: BottomButtonInfo?,
    viewModel: ChargeInfoViewModel,
    navHostController: NavHostController,
    context: Context,
) {
    when (buttonInfo?.buttonType) {
        BottomButtonType.FIND_NEARBY_STATIONS -> {
            viewModel.logAnalytics(
                group = AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                eventName = AnalyticsEventParam.CHARGE_INFO_FIND_NEARBY_STATIONS,
            )
            NavigationUtil.navigateToStations(
                context = context,
                navController = navHostController,
                isEVPublicChargingEnabled = viewModel.isEVPublicChargingFeatureEnabled(),
            )
        }
        BottomButtonType.START_CHARGING -> {
            viewModel.startCharging()
        }
        else -> {
            // Do nothing
        }
    }
}

@Composable
private fun EvGoComplimentaryAlertDialog(enrollmentState: EnrollmentState) {
    val bottomSheetState = LocalBottomSheet.current
    val coroutineScope = rememberCoroutineScope()

    when (enrollmentState) {
        is EnrollmentState.Success -> {
            enrollmentState.data.chargingAlertData?.let { data ->
                bottomSheetState.secondarySheetShape.value =
                    RoundedCornerShape(
                        topStart = 16.dp,
                        topEnd = 16.dp,
                    )
                coroutineScope.launchSecondaryBottomSheetAction(bottomSheetState) { bottomSheet ->
                    OAAlertDialog(
                        DialogData(
                            imageId = data.icon,
                            imageBgColor = if (data.success) colors.primaryLightBlue else colors.primary02,
                            title = stringResource(data.title),
                            subtitle = stringResource(data.description),
                            primaryButtonText = stringResource(data.primaryButtonText),
                            secondaryButtonText =
                                data.additionalButtonText?.let {
                                    stringResource(it)
                                },
                            primaryOnClick = {
                                coroutineScope.launch {
                                    bottomSheet.hide()
                                }
                                data.onPrimaryButtonPressed()
                            },
                            secondaryOnClick = {
                                coroutineScope.launch {
                                    bottomSheet.hide()
                                }
                                data.onAdditionalButtonPressed()
                            },
                        ),
                    )
                }
            }
        }

        else -> {
            return
        }
    }
}

@Composable
private fun ChargeInfoCardList(
    navController: NavHostController,
    viewModel: ChargeInfoViewModel = hiltViewModel(),
    chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel(),
    enrollmentViewModel: EnrollmentViewModel,
    chargingNetworksViewModel: ChargingNetworksViewModel,
    cleanAssistViewModel: CleanAssistViewModel,
    modifier: Modifier = Modifier,
    onNavigateToChargeSchedule: () -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    val bottomSheet = LocalBottomSheet.current
    bottomSheet.primarySheetShape.value =
        RoundedCornerShape(topStart = 0.dp, topEnd = 0.dp)
    val chargingNetworksState by chargingNetworksViewModel.uiState.collectAsState()
    LazyColumn(
        modifier =
            modifier
                .fillMaxSize(),
    ) {
        item { ChargeInfoLayout(viewModel, modifier = Modifier.padding(vertical = 8.dp)) }
        item {
            ChargeHistoryWidget(
                viewModel = viewModel,
                modifier =
                    Modifier
                        .padding(vertical = 8.dp)
                        .testTagID(AccessibilityId.ID_HISTORY_INFO_CARD),
                onShowUnknownPopup = {
                    bottomSheet.primarySheetShape.value =
                        RoundedCornerShape(
                            topStart = 30.dp,
                            topEnd = 30.dp,
                        )
                    coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                        UnknownLocationDialog(it) {
                            context.startActivity(
                                Intent(context, DashboardActivity::class.java)
                                    .putExtra(kNavItem, DashboardActivity.NavItemType.ACCOUNT),
                            )
                        }
                    }
                },
            ) {
                viewModel.logAnalytics(
                    group = AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                    eventName = AnalyticsEventParam.CHARGE_HISTORY_CARD,
                )
                navController.navigate(ChargeInfoRoute.ChargeHistoryNestedRoute.route)
            }
        }

        item {
            if (viewModel.isRemoteSharedUser() == false) {
                ChargeScheduleWidget(
                    navController,
                    modifier =
                        Modifier
                            .padding(vertical = 8.dp)
                            .testTagID(AccessibilityId.ID_SCHEDULE_INFO_CARD),
                    viewModel = viewModel,
                    chargeAssistViewModel = chargeAssistViewModel,
                ) {
                    viewModel.logAnalytics(
                        group = AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                        eventName = AnalyticsEventParam.CHARGE_SCHEDULE_CARD,
                    )
                    onNavigateToChargeSchedule()
                }
            }
        }

        item {
            StatisticsInfoWidget(
                viewModel = viewModel,
                modifier =
                    Modifier
                        .padding(vertical = 8.dp)
                        .testTagID(AccessibilityId.ID_STATISTICS_CARD),
                onLearnMore = {
                    viewModel.logAnalytics(
                        group = AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                        eventName = AnalyticsEventParam.STATISTICS_LEARN_MORE,
                    )
                    coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                        FindOutMoreScreen(
                            bottomSheetState = it,
                        ) { group, event ->
                            viewModel.logAnalytics(group, event)
                        }
                    }
                },
            ) { statisticsResponse ->
                viewModel.logAnalytics(
                    group = AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                    eventName = AnalyticsEventParam.STATISTICS_CARD,
                )
                navController.currentBackStackEntry?.savedStateHandle?.set(
                    ARG_STATISTICS_RESPONSE,
                    statisticsResponse,
                )
                navController.navigate(ChargeInfoRoute.ChargeStatisticsScreen.route)
            }
        }

        item {
            CleanAssistWidget(
                navHostController = navController,
                viewModel = cleanAssistViewModel,
                modifier =
                    Modifier
                        .padding(vertical = 8.dp)
                        .testTagID(AccessibilityId.ID_CLEAN_ASSIST_CARD),
            )
        }

        if (enrollmentViewModel.isEVPublicChargingEnabled()) {
            item {
                ChargeStationSettingsCard(
                    enrollmentViewModel = enrollmentViewModel,
                    modifier = modifier,
                    navController = navController,
                    chargingNetworksState = chargingNetworksState,
                )
            }
        }
    }
}

@Composable
private fun ChargeStationSettingsCard(
    enrollmentViewModel: EnrollmentViewModel,
    chargingNetworksState: ChargingNetworksState,
    navController: NavController,
    modifier: Modifier = Modifier,
) {
    val coroutineScope = rememberCoroutineScope()
    val localBottomSheetState = LocalBottomSheet.current
    Card(
        backgroundColor = colors.tile03,
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            OASubHeadLine1TextView(
                text = stringResource(R.string.charging_station_settings_title),
                color = colors.tertiary03,
                modifier = Modifier.padding(bottom = 16.dp),
            )

            if (enrollmentViewModel.vehicleInfo?.isFeatureEnabled(Feature.WALLET) == true) {
                WalletCard(
                    navController = navController,
                    viewModel = enrollmentViewModel,
                )
            }
            ChargePointCard(
                viewModel = enrollmentViewModel,
                navController = navController,
            )
            if (enrollmentViewModel.vehicleInfo?.isFeatureEnabled(Feature.EV_GO) == true) {
                EvGoCard(
                    viewModel = enrollmentViewModel,
                    navController = navController,
                )
            }

            ChargingNetworksComposable(
                chargingNetworksState = chargingNetworksState,
                onDataConsentStatusClick = {
                    navController.navigate(OAScreen.DataConsentDetailsScreen.route)
                },
                onOpenEnrollmentPageClick = {
                    launchPlugAndChargeActivation(coroutineScope, localBottomSheetState)
                },
                onPlugAndChargeStatusClick = {
                    navController.navigate(OAScreen.PlugAndChargeLandingScreen.route)
                },
            )
        }
    }
}

@Composable
private fun ChargeInfoBottomButton(
    text: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(top = 8.dp, bottom = 8.dp, start = 16.dp, end = 16.dp),
    ) {
        PrimaryButton02(
            text = text,
            modifier =
                Modifier
                    .align(Alignment.CenterHorizontally),
        ) {
            onClick()
        }
    }
}
