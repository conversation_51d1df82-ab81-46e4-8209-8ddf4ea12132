package com.toyota.oneapp.features.vehicleinfo.dataaccess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.vehicleinfo.domain.model.SoftwareUpdates

data class SoftwareUpdateResponse(
    @SerializedName("status") val status: Status?,
    @SerializedName("payload") val payload: Payload?,
)

data class Payload(
    @SerializedName("notificationStatus") val notificationStatus: Int?,
    @SerializedName("updateAvailable") val updateAvailable: Boolean?,
)

fun SoftwareUpdateResponse.toUIModel(): SoftwareUpdates {
    return SoftwareUpdates(this.payload?.notificationStatus, this.payload?.updateAvailable)
}
