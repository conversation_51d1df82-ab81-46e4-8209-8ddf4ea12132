package com.toyota.oneapp.features.vehicleinfo.presentation

import android.content.Context
import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.ComposableRoundedCornerCard
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.Constants.VEHICLE_SOFTWARE_NOTIFICATION_STATUS
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.vehicleinfo.domain.model.VehicleInfoCardItem
import com.toyota.oneapp.features.vehicleinfo.domain.model.VehicleInfoConstance
import com.toyota.oneapp.util.ToyUtil

@Composable
fun MenuItem(
    cardItem: VehicleInfoCardItem,
    navHostController: NavHostController,
    launcher: ManagedActivityResultLauncher<Intent, ActivityResult>,
) {
    val viewModel = hiltViewModel<VehicleInfoViewModel>()
    val context = LocalContext.current
    ComposableRoundedCornerCard(
        backgroundColor = AppTheme.colors.tertiary15,
        cornerRadius = 8.dp,
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp)
                .layoutId(cardItem.accessibilityId),
        click = {
            onVehicleInfoItemClicked(cardItem, navHostController, viewModel, context, launcher)
        },
    ) {
        Row {
            Surface(
                shape = CircleShape,
                color = AppTheme.colors.button02b,
                modifier =
                    Modifier
                        .size(72.dp)
                        .padding(12.dp),
            ) {
                Image(
                    modifier =
                        Modifier
                            .padding(12.dp)
                            .testTagID(cardItem.testTagId),
                    painter = painterResource(id = cardItem.icon),
                    contentDescription = null,
                )
            }
            Column(
                modifier =
                    Modifier.padding(
                        top = 13.dp,
                        bottom = 13.dp,
                    ),
            ) {
                Text(
                    text = stringResource(id = cardItem.title),
                    style = AppTheme.fontStyles.body4,
                    color = AppTheme.colors.tertiary03,
                )

                Text(
                    text = stringResource(id = cardItem.subTitle),
                    style = AppTheme.fontStyles.callout1,
                    color = AppTheme.colors.tertiary05,
                    modifier =
                        Modifier.padding(
                            top = 2.dp,
                        ),
                )
            }
            cardItem.isAlertIcon?.let {
                if (it) {
                    Spacer(
                        Modifier
                            .weight(1f)
                            .fillMaxHeight(),
                    )
                    Image(
                        modifier = Modifier.padding(top = 13.dp, end = 13.dp),
                        painter = painterResource(id = R.drawable.ic_alert),
                        contentDescription = stringResource(id = R.string.alert),
                    )
                }
            }
        }
    }
}

fun onVehicleInfoItemClicked(
    cardItem: VehicleInfoCardItem,
    navHostController: NavHostController,
    viewModel: VehicleInfoViewModel,
    context: Context,
    launcher: ManagedActivityResultLauncher<Intent, ActivityResult>,
) {
    viewModel.logEventWithParameter(cardItem.analyticsEvent)
    when (cardItem) {
        is VehicleInfoCardItem.ConnectedServiceSupport -> {
            viewModel.applicationData.getSelectedVehicle()?.connectTechModel?.link?.let {
                ToyUtil.openCustomChromeTab(
                    context,
                    it,
                )
            }
        }

        is VehicleInfoCardItem.VehicleSoftware -> {
            navigateToVehicleSoftwareScreen(
                cardItem,
                navHostController,
                context,
                launcher,
            )
        }

        is VehicleInfoCardItem.AppSuite -> {
            viewModel.applicationData.getSelectedVehicle()?.let {
                (context as OADashboardActivity).navigateToAppSuite(it)
            }
        }

        is VehicleInfoCardItem.Glovebox -> {
            cardItem.route?.let {
                navHostController.navigate(it)
            }
        }

        else -> {
            cardItem.route?.let {
                navHostController.navigate(it)
            }
        }
    }
}

fun navigateToVehicleSoftwareScreen(
    cardItem: VehicleInfoCardItem,
    navHostController: NavHostController,
    context: Context,
    launcher: ManagedActivityResultLauncher<Intent, ActivityResult>,
) {
    when {
        cardItem.is21mmUpdates &&
            cardItem.notificationStatusValue == VehicleInfoConstance.SOFTWARE_UPDATE_AVAILABLE_21MM &&
            cardItem.isUpdateAvailable -> {
            navHostController.navigate(OAScreen.SoftwareUpdate21mmScreen.route)
        }
        cardItem.is21mmUpdates &&
            cardItem.notificationStatusValue == VehicleInfoConstance.SOFTWARE_UPDATE_INITIALIZED_21MM ||
            cardItem.notificationStatusValue == VehicleInfoConstance.SOFTWARE_UPDATE_21MM ||
            cardItem.notificationStatusValue == VehicleInfoConstance.CUSTOMER_ACTION_COMPLETE
        -> {
            navHostController.currentBackStackEntry?.savedStateHandle?.apply {
                set(VEHICLE_SOFTWARE_NOTIFICATION_STATUS, cardItem.notificationStatusValue)
            }
            navHostController.navigate(OAScreen.SoftwareUpdateAvailable21mmScreen.route)
        }
        else -> {
            navHostController.currentBackStackEntry?.savedStateHandle?.apply {
                set(VEHICLE_SOFTWARE_NOTIFICATION_STATUS, cardItem.notificationStatusValue)
            }
            navHostController.navigate(OAScreen.VehicleSoftwareScreen.route)
        }
    }
}
