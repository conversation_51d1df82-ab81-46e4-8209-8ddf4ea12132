package com.toyota.oneapp.features.vehicleinfo.di

import com.toyota.oneapp.features.vehicleinfo.application.VehicleInfoLogic
import com.toyota.oneapp.features.vehicleinfo.application.VehicleInfoUseCase
import com.toyota.oneapp.features.vehicleinfo.dataaccess.repository.VehicleInfoDefaultRepo
import com.toyota.oneapp.features.vehicleinfo.domain.repo.VehicleInfoRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class VehicleInfoModule {
    @Binds
    abstract fun bindVehicleInfoRepo(vehicleInfoDefaultRepo: VehicleInfoDefaultRepo): VehicleInfoRepo

    @Binds
    abstract fun bindVehicleInfoUseCase(vehicleInfoLogic: VehicleInfoLogic): VehicleInfoUseCase
}
