package com.toyota.oneapp.features.vehicleinfo.dataaccess.repository

import com.toyota.oneapp.features.core.commonapicalls.dataaccess.service.CommonApi
import com.toyota.oneapp.features.vehicleinfo.dataaccess.servermodel.SoftwareUpdateResponse
import com.toyota.oneapp.features.vehicleinfo.domain.repo.VehicleInfoRepo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class VehicleInfoDefaultRepo
    @Inject
    constructor(
        val service: CommonApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), VehicleInfoRepo {
        override suspend fun fetchSoftwareUpdates(vin: String): Resource<SoftwareUpdateResponse?> {
            return makeApiCall {
                service.fetchSoftwareUpdates(
                    vin = vin,
                )
            }
        }
    }
