package com.toyota.oneapp.features.vehicleinfo.application

import com.toyota.oneapp.R
import com.toyota.oneapp.core.BuildWrapper
import com.toyota.oneapp.features.vehicleinfo.dataaccess.servermodel.toUIModel
import com.toyota.oneapp.features.vehicleinfo.domain.model.SoftwareUpdates
import com.toyota.oneapp.features.vehicleinfo.domain.model.VehicleInfoCardItem
import com.toyota.oneapp.features.vehicleinfo.domain.model.VehicleInfoConstance
import com.toyota.oneapp.features.vehicleinfo.domain.repo.VehicleInfoRepo
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class VehicleInfoLogic
    @Inject
    constructor(
        private val repository: VehicleInfoRepo,
        private val buildWrapper: BuildWrapper,
    ) : VehicleInfoUseCase() {
        override fun loadLocalItems(
            vehicleInfo: VehicleInfo,
            vehicleInfoItems: ArrayList<VehicleInfoCardItem>,
        ): ArrayList<VehicleInfoCardItem> {
            if (!vehicleInfo.isDigitalkey) {
                if (vehicleInfo.isFeatureEnabled(Feature.DASHBOARD_LIGHTS)) {
                    vehicleInfoItems.add(
                        VehicleInfoCardItem.Glovebox.apply {
                            this.subTitle = R.string.glovebox_with_light_desc
                        },
                    )
                } else if (vehicleInfo.isFeatureEnabled(Feature.OWNERS_MANUAL)) {
                    vehicleInfoItems.add(
                        VehicleInfoCardItem.Glovebox.apply {
                            this.subTitle = R.string.glovebox_without_light_desc
                        },
                    )
                } else {
                    vehicleInfoItems.add(
                        VehicleInfoCardItem.Glovebox.apply {
                            this.subTitle = capabilitiesString()
                        },
                    )
                }
                if (vehicleInfo.isConnectedVehicle) {
                    vehicleInfoItems.add(VehicleInfoCardItem.Subscriptions)
                }

                if (vehicleInfo.isFeatureEnabled(Feature.CONNECTED_SUPPORT)) {
                    vehicleInfoItems.add(VehicleInfoCardItem.ConnectedServiceSupport)
                }
                if (vehicleInfo.isFeatureEnabled(Feature.XCAPPV2)) {
                    vehicleInfoItems.add(VehicleInfoCardItem.AppSuite)
                }
            }

            return vehicleInfoItems
        }

        override fun fetchSoftwareUpdates(
            vehicleInfo: VehicleInfo,
            vehicleInfoItems: ArrayList<VehicleInfoCardItem>,
        ): Flow<ArrayList<VehicleInfoCardItem>> {
            return flow {
                if (vehicleInfo.isFeatureEnabled(Feature.AUTO_DRIVE) && !vehicleInfo.isDigitalkey) {
                    val response = repository.fetchSoftwareUpdates(vehicleInfo.vin)
                    if (response is Resource.Success) {
                        if (vehicleInfoItems.contains(VehicleInfoCardItem.VehicleSoftware)) {
                            vehicleInfoItems.remove(VehicleInfoCardItem.VehicleSoftware)
                        }
                        vehicleInfoItems.add(
                            VehicleInfoCardItem.VehicleSoftware.apply {
                                response.data?.toUIModel()?.let {
                                    this.subTitle = getSoftwareUpdatesSubTitle(it.notificationStatus)
                                    this.isAlertIcon = isAlertIconRequired(it)
                                    this.is21mmUpdates = vehicleInfo.is21MMVehicle
                                    this.notificationStatusValue = it.notificationStatus
                                    this.isUpdateAvailable = it.updateAvailable ?: false
                                }
                            },
                        )
                    }
                }
                emit(vehicleInfoItems)
            }
        }

        override fun isRemoveVehicleEnabled(vehicleInfo: VehicleInfo?): Boolean {
            if (vehicleInfo == null) return false
            return !vehicleInfo.isRemoteOnlyUser
        }

        private fun isAlertIconRequired(softwareUpdate: SoftwareUpdates): Boolean {
            return softwareUpdate.updateAvailable == true &&
                (
                    softwareUpdate.notificationStatus == VehicleInfoConstance.SOFTWARE_UPDATE_AVAILABLE ||
                        softwareUpdate.notificationStatus
                        == VehicleInfoConstance.SOFTWARE_UPDATE_AVAILABLE_21MM
                )
        }

        fun getSoftwareUpdatesSubTitle(type: Int?): Int {
            return when (type) {
                VehicleInfoConstance.SOFTWARE_UPDATE_AVAILABLE -> R.string.software_update_available
                VehicleInfoConstance.SOFTWARE_UPDATE_PROGRESS -> R.string.Software_update_initiated
                VehicleInfoConstance.SOFTWARE_UPDATE_COMPLETE,
                VehicleInfoConstance.SOFTWARE_UPDATE_21MM,
                -> R.string.up_to_date

                VehicleInfoConstance.SOFTWARE_ERROR_NOTIFICATION -> R.string.unable_to_update_vehicle_software
                VehicleInfoConstance.SOFTWARE_UPDATE_AVAILABLE_21MM -> R.string.software_update_available
                VehicleInfoConstance.CUSTOMER_ACTION_COMPLETE -> R.string.customer_action_complete
                VehicleInfoConstance.SOFTWARE_UPDATE_INITIALIZED_21MM -> R.string.update_initialized
                else -> R.string.software_update_available
            }
        }

        fun capabilitiesString(): Int {
            return if (buildWrapper.isSubaruApp()) {
                R.string.AddVehicle_vehicle_capabilities
            } else {
                R.string.glovebox_specs
            }
        }
    }
