package com.toyota.oneapp.features.vehicleinfo.domain.model

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.util.AccessibilityId

sealed class VehicleInfoCardItem(
    @StringRes val title: Int,
    @StringRes var subTitle: Int,
    @DrawableRes var icon: Int,
    val route: String? = null,
    var is21mmUpdates: Boolean = false,
    val accessibilityId: String,
    var isAlertIcon: Boolean? = false,
    val analyticsEvent: String,
    var notificationStatusValue: Int? = null,
    var isUpdateAvailable: Boolean = false,
    val testTagId: String,
) {
    object Glovebox : VehicleInfoCardItem(
        title = R.string.glovebox,
        subTitle = R.string.glovebox_desc,
        icon = R.drawable.ic_glovebox,
        route = OAScreen.GloveBox.route,
        accessibilityId = AccessibilityId.ID_VEHICLE_INFO_GLOVBOX_CARD,
        analyticsEvent = AnalyticsEventParam.VEHICLE_INFO_GLOVEBOX_TILE_TAP,
        testTagId = AccessibilityId.ID_VEHICLE_INFO_GLOVE_BOX_ICON,
    )

    object Subscriptions : VehicleInfoCardItem(
        title = R.string.Subscription_subscriptions,
        subTitle = R.string.subscriptions_desc,
        icon = R.drawable.ic_small_subscription,
        route = OAScreen.Subscriptions.route,
        accessibilityId = AccessibilityId.ID_VEHICLE_INFO_SUBSCRIPTION,
        analyticsEvent = AnalyticsEventParam.VEHICLE_INFO_SUBSCRIPTIONS_TILE_TAP,
        testTagId = AccessibilityId.ID_VEHICLE_INFO_SUBSCRIPTION_ICON,
    )

    object ConnectedServiceSupport : VehicleInfoCardItem(
        title = R.string.connected_service_support,
        subTitle = R.string.connected_service_support_desc,
        icon = R.drawable.ic_connected_service_support,
        accessibilityId = AccessibilityId.ID_VEHICLE_INFO_CONNECTED_SERVICE_SUPPORT,
        analyticsEvent = AnalyticsEventParam.VEHICLE_INFO_CONNSERVICESSUPP_TILE_TAP,
        testTagId = AccessibilityId.ID_VEHICLE_INFO_CONNECTED_SERVICE_ICON,
    )

    object AppSuite : VehicleInfoCardItem(
        title = R.string.app_suite,
        subTitle = R.string.app_suite_desc,
        icon = R.drawable.ic_app_suite,
        accessibilityId = AccessibilityId.ID_VEHICLE_INFO_APP_SUIT,
        analyticsEvent = AnalyticsEventParam.VEHICLE_INFO_APPSUITE_TILE_TAP,
        testTagId = AccessibilityId.ID_VEHICLE_INFO_APP_SUITE_ICON,
    )

    object VehicleSoftware : VehicleInfoCardItem(
        title = R.string.vehicle_software,
        subTitle = R.string.vehicle_software,
        icon = R.drawable.ic_vehicle_software,
        accessibilityId = AccessibilityId.ID_VEHICLE_INFO_SOFTWARE_UPDATE,
        analyticsEvent = AnalyticsEventParam.VEHICLE_INFO_VEHICLESOFTWARE_TILE_TAP,
        testTagId = AccessibilityId.ID_VEHICLE_INFO_VEHICLE_SOFTWARE_ICON,
        route = OAScreen.VehicleSoftwareScreen.route,
    )
}
