package com.toyota.oneapp.features.vehicleinfo.application

import com.toyota.oneapp.features.vehicleinfo.domain.model.VehicleInfoCardItem
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

abstract class VehicleInfoUseCase {
    abstract fun loadLocalItems(
        vehicleInfo: VehicleInfo,
        vehicleInfoItems: ArrayList<VehicleInfoCardItem>,
    ): ArrayList<VehicleInfoCardItem>

    abstract fun fetchSoftwareUpdates(
        vehicleInfo: VehicleInfo,
        vehicleInfoItems: ArrayList<VehicleInfoCardItem>,
    ): Flow<ArrayList<VehicleInfoCardItem>>

    abstract fun isRemoveVehicleEnabled(vehicleInfo: VehicleInfo?): <PERSON><PERSON><PERSON>
}
