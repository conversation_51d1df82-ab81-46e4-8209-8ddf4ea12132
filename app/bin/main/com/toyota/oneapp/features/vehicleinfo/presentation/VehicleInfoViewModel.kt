package com.toyota.oneapp.features.vehicleinfo.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.vehicleinfo.application.VehicleInfoState
import com.toyota.oneapp.features.vehicleinfo.application.VehicleInfoUseCase
import com.toyota.oneapp.features.vehicleinfo.domain.model.VehicleInfoCardItem
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class VehicleInfoViewModel
    @Inject
    constructor(
        private val vehicleInfoUseCase: VehicleInfoUseCase,
        val applicationData: ApplicationData,
        val analyticsLogger: AnalyticsLogger,
        private val dispatcherProvider: DispatcherProvider,
    ) : BaseViewModel() {
        private val _vehicleInfoState =
            MutableStateFlow<VehicleInfoState>(value = VehicleInfoState.Initialize)
        val vehicleInfoState = _vehicleInfoState.asStateFlow()
        private val vehicleInfoItems = ArrayList<VehicleInfoCardItem>()

        private val _isRemoveVehicleEnabled =
            MutableStateFlow(
                value = true,
            )
        val isRemoveVehicleEnabled: StateFlow<Boolean> get() = _isRemoveVehicleEnabled

        var vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()

        init {
            viewModelScope.launch(dispatcherProvider.main()) {
                vehicleInfo?.let {
                    vehicleInfoItems.clear()
                    vehicleInfoUseCase.loadLocalItems(it, vehicleInfoItems)
                    isRemoveVehicleEnabled(it)
                }
            }
        }

        fun fetchSoftwareUpdate() {
            vehicleInfo?.let {
                _vehicleInfoState.value = VehicleInfoState.Loading
                viewModelScope.launch {
                    vehicleInfoUseCase.fetchSoftwareUpdates(it, vehicleInfoItems).collect {
                        _vehicleInfoState.value = VehicleInfoState.Success(it)
                    }
                }
            }
        }

        private fun isRemoveVehicleEnabled(vehicleInfo: VehicleInfo) {
            _isRemoveVehicleEnabled.value = vehicleInfoUseCase.isRemoveVehicleEnabled(vehicleInfo)
        }

        fun logEventWithParameter(event: String) {
            analyticsLogger.logEventWithParameter(AnalyticsEventParam.VEHICLE_INFO, event)
        }

        fun updateState(vehicleInfoState: VehicleInfoState) {
            _vehicleInfoState.value = vehicleInfoState
        }
    }
