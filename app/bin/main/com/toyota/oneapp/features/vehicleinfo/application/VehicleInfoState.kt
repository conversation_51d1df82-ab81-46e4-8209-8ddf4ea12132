package com.toyota.oneapp.features.vehicleinfo.application

import com.toyota.oneapp.features.vehicleinfo.domain.model.VehicleInfoCardItem

sealed class VehicleInfoState {
    object Loading : VehicleInfoState()

    object Initialize : VehicleInfoState()

    class Success(val data: ArrayList<VehicleInfoCardItem>) : VehicleInfoState()

    class Error(val errorCode: String?, val errorMessage: String?) : VehicleInfoState()
}
