/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.vehicleinfo.presentation

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.Scaffold
import androidx.compose.material.ScaffoldState
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.LoadVehicleImage
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.VehicleInfoComposableShimmer
import com.toyota.oneapp.features.core.composable.VinCopiedSnackbar
import com.toyota.oneapp.features.core.composable.dynamicvin.DynamicVinComponent
import com.toyota.oneapp.features.core.composable.dynamicvin.TestTagIdPrefix.TestTagIdPrefixVehicleInfo
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.LocalBottomSheetState
import com.toyota.oneapp.features.core.util.launchSecondaryBottomSheetAction
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.presentation.ConnectedVehicleViewModel
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.vehicleinfo.application.VehicleInfoState
import com.toyota.oneapp.features.vehicleinfo.application.VehicleInfoState.Success
import com.toyota.oneapp.features.vehicleinfo.domain.model.VehicleInfoCardItem
import com.toyota.oneapp.features.vehicleswitcher.application.LoadingState
import com.toyota.oneapp.features.vehicleswitcher.application.SubscriptionState
import com.toyota.oneapp.features.vehicleswitcher.presentation.RemoveVehicleBottomSheet
import com.toyota.oneapp.features.vehicleswitcher.presentation.ShowErrorToast
import com.toyota.oneapp.features.vehicleswitcher.presentation.SubscriptionBottomSheet
import com.toyota.oneapp.features.vehicleswitcher.presentation.VehicleSwitcherViewModel
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

@OptIn(
    ExperimentalMaterialApi::class,
)
@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun VehicleInfoScreen(
    navHostController: NavHostController,
    modalBottomSheetState: ModalBottomSheetState? = null,
) {
    var vinYCoordinateForSnackbar = MutableStateFlow<Dp>(0.dp)
    val vehicleInfoViewModel = hiltViewModel<VehicleInfoViewModel>()
    val vehicleSwitcherViewModel =
        (LocalContext.current as OADashboardActivity).vehicleSwitcherViewModel
    val dashboardViewModel =
        (LocalContext.current as OADashboardActivity).dashboardViewModel
    val connectedVehicleViewModel = hiltViewModel<ConnectedVehicleViewModel>()
    val context = LocalContext.current
    val vehicleInfoState = vehicleInfoViewModel.vehicleInfoState.collectAsState()
    val isRemoveVehicleEnabled by vehicleInfoViewModel.isRemoveVehicleEnabled.collectAsState()

    val isSecondaryVehicle by connectedVehicleViewModel.isSecondaryVehicle.collectAsState()

    val coroutineScope = rememberCoroutineScope()
    val scaffoldState: ScaffoldState = rememberScaffoldState()

    val subscriptionState = vehicleSwitcherViewModel.subscriptionState.collectAsState()

    val bottomSheet = LocalBottomSheet.current
    bottomSheet.secondarySheetShape.value =
        RoundedCornerShape(
            topStart = 30.dp,
            topEnd = 30.dp,
        )
    LaunchedEffect(modalBottomSheetState?.isVisible) {
        initializeSoftwareUpdates(modalBottomSheetState?.isVisible, vehicleInfoViewModel)
    }
    val launcher =
        rememberLauncherForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) {
            (it.resultCode == Activity.RESULT_OK)
                .takeIf { resultOk ->
                    resultOk
                }.let {
                    coroutineScope.launch {
                        bottomSheet.primarySheetState.value.isVisible
                            .takeIf { isVisible ->
                                isVisible
                            }.let {
                                bottomSheet.primarySheetState.value.hide()
                            }
                    }
                }
        }
    val activeSubscriptionState = vehicleSwitcherViewModel.subscriptionState.collectAsState()
    when (activeSubscriptionState.value) {
        is SubscriptionState.Success -> {
            with((activeSubscriptionState.value as SubscriptionState.Success).data) {
                (!isYearlySubscriptionsRemoveStatus)
                    .takeIf { status ->
                        status
                    }.let {
                        coroutineScope.launchSecondaryBottomSheetAction(bottomSheet) {
                            RemoveVehicleBottomSheet(
                                it,
                                coroutineScope,
                                isActiveSubscription(paidSubscriptionsStatus, trialSubscriptionsStatus),
                                vehicleSwitcherViewModel,
                            )
                        }
                    }
            }
        }

        else -> {}
    }

    Scaffold(
        content = {
            when (vehicleInfoState.value) {
                is VehicleInfoState.Loading -> {
                    VehicleInfoComposableShimmer()
                }

                is Success -> {
                    if (dashboardViewModel.getGoToVehicleSoftwareScreen()) {
                        (vehicleInfoState.value as Success).data.forEach { cardItem ->
                            (cardItem is VehicleInfoCardItem.VehicleSoftware)
                                .takeIf { vehicleSoftware ->
                                    vehicleSoftware
                                }.let {
                                    dashboardViewModel.setGoToVehicleSoftwareScreen(false)
                                    navigateToVehicleSoftwareScreen(
                                        cardItem,
                                        navHostController,
                                        context,
                                        launcher,
                                    )
                                }
                        }
                    } else {
                        Box(
                            modifier =
                                Modifier
                                    .fillMaxSize()
                                    .background(AppTheme.colors.tertiary15),
                        ) {
                            bottomSheet.secondarySheetState.value.let {
                                SubscriptionBottomSheet(
                                    coroutineScope = coroutineScope,
                                    subscriptionState = subscriptionState.value,
                                    (LocalContext.current as OADashboardActivity).vehicleSwitcherViewModel,
                                )
                            }
                            VehicleInfoContent(
                                navHostController = navHostController,
                                vehicleInfoViewModel = vehicleInfoViewModel,
                                scaffoldState = scaffoldState,
                                launcher = launcher,
                                onYCoordinatePositioned = { it ->
                                    vinYCoordinateForSnackbar.value = it
                                },
                            )
                            Column(modifier = Modifier.padding(start = 16.dp)) {
                                Box(
                                    modifier =
                                        Modifier
                                            .testTagID(AccessibilityId.ID_VEHICLE_INFO_BACK_BOX_CTA)
                                            .size(48.dp)
                                            .clip(CircleShape)
                                            .background(AppTheme.colors.button02d)
                                            .clickable {
                                                coroutineScope.launch { bottomSheet.primarySheetState.value.hide() }
                                            },
                                ) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.ic_back_arrow),
                                        contentDescription = null,
                                        modifier =
                                            Modifier
                                                .layoutId(
                                                    AccessibilityId.ID_VEHICLE_INFO_BACK_ARROW_CTA,
                                                ).padding(
                                                    start = 19.dp,
                                                    end = 22.dp,
                                                    top = 17.dp,
                                                    bottom = 17.dp,
                                                ),
                                        tint = AppTheme.colors.button02a,
                                    )
                                }
                            }
                            VinCopiedSnackbar(
                                snackbarHostState = scaffoldState.snackbarHostState,
                                message = stringResource(id = R.string.copied),
                                vinYCoordinateForSnackbar = vinYCoordinateForSnackbar,
                                yCoordinateAdjustment = 73.dp,
                                testTagIdPrefix = TestTagIdPrefixVehicleInfo,
                            )
                            if (isSecondaryVehicle) {
                                Box {
                                    // empty container
                                }
                            } else {
                                (isRemoveVehicleEnabled)
                                    .takeIf { removeVehicle ->
                                        removeVehicle
                                    }.let {
                                        Column(
                                            modifier =
                                                Modifier
                                                    .fillMaxWidth()
                                                    .wrapContentHeight()
                                                    .align(Alignment.BottomCenter)
                                                    .background(AppTheme.colors.tertiary15)
                                                    .clickable {
                                                        vehicleInfoViewModel.applicationData
                                                            .getSelectedVehicle()
                                                            ?.let {
                                                                vehicleSwitcherViewModel.checkSubscriptionAvailableOrNot(
                                                                    it,
                                                                )
                                                            }
                                                    },
                                        ) {
                                            Text(
                                                text =
                                                    stringResource(
                                                        id = R.string.vehicle_info_remove_vehicle,
                                                    ),
                                                style = AppTheme.fontStyles.buttonLink1,
                                                color = AppTheme.colors.button02a,
                                                modifier =
                                                    Modifier
                                                        .align(Alignment.CenterHorizontally)
                                                        .padding(top = 16.dp, bottom = 40.dp)
                                                        .layoutId(
                                                            AccessibilityId.ID_VEHICLE_INFO_REMOVE_VEHICLE,
                                                        ),
                                            )
                                        }
                                    }
                            }
                            RemovedVehicleView(
                                vehicleSwitcherViewModel,
                                coroutineScope,
                                bottomSheet,
                            )
                        }
                    }
                }
                else -> {}
            }
        },
    )
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun RemovedVehicleView(
    vehicleSwitcherViewModel: VehicleSwitcherViewModel,
    coroutineScope: CoroutineScope,
    bottomSheet: LocalBottomSheetState,
) {
    val initialVehiclesSwitcherState =
        vehicleSwitcherViewModel.loadingState.collectAsState()
    when (initialVehiclesSwitcherState.value) {
        is LoadingState.Loading -> {
            ShowProgressIndicator(true)
        }

        is LoadingState.RemoveVehicleSuccess -> {
            with(initialVehiclesSwitcherState.value) {
                coroutineScope.launch {
                    bottomSheet.primarySheetState.value.isVisible
                        .takeIf {
                            it
                        }.let {
                            bottomSheet.primarySheetState.value.hide()
                            vehicleSwitcherViewModel.dismissLoading()
                        }
                }
            }
        }

        is LoadingState.RemoveVehicleError -> {
            val errorMessage =
                (initialVehiclesSwitcherState.value as LoadingState.RemoveVehicleError)
                    .errorMessage
            ShowErrorToast(errorMessage = errorMessage)
            vehicleSwitcherViewModel.dismissLoading()
        }

        else -> {}
    }
}

@Composable
fun VehicleInfoContent(
    navHostController: NavHostController,
    vehicleInfoViewModel: VehicleInfoViewModel,
    scaffoldState: ScaffoldState,
    launcher: ManagedActivityResultLauncher<Intent, ActivityResult>,
    onYCoordinatePositioned: (Dp) -> Unit,
) {
    val vehicleInfo = vehicleInfoViewModel.applicationData.getSelectedVehicle()
    val vehicleInfoState = vehicleInfoViewModel.vehicleInfoState.collectAsState()

    Column(
        modifier =
            Modifier
                .verticalScroll(rememberScrollState())
                .fillMaxSize()
                .padding(horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = "${vehicleInfo?.modelYear.orEmpty()} ${vehicleInfo?.modelName.orEmpty()}",
            style = AppTheme.fontStyles.headline1,
            color = AppTheme.colors.tertiary03,
            textAlign = TextAlign.Center,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 48.dp),
        )

        Row(
            modifier =
                Modifier
                    .align(Alignment.CenterHorizontally),
        ) {
            Text(
                text =
                    if (vehicleInfo?.nickName?.isEmpty() == true) {
                        stringResource(
                            id = R.string.enter_a_nickname,
                        )
                    } else {
                        vehicleInfo?.nickName.orEmpty()
                    },
                style = AppTheme.fontStyles.caption1,
                color = AppTheme.colors.tertiary05,
                modifier =
                    Modifier
                        .align(Alignment.CenterVertically)
                        .layoutId(AccessibilityId.ID_VEHICLE_INFO_NICKNAME),
            )
            Spacer(modifier = Modifier.width(4.dp))

            Surface(
                shape = CircleShape,
                color = AppTheme.colors.button02d,
                modifier =
                    Modifier
                        .size(66.dp)
                        .padding(11.dp),
            ) {
                Icon(
                    modifier =
                        Modifier
                            .padding(11.dp)
                            .layoutId(AccessibilityId.ID_VEHICLE_INFO_NICKNAME_EDIT)
                            .clickable {
                                navHostController.navigate(OAScreen.VehicleNickname.route)
                            },
                    painter = painterResource(id = R.drawable.ic_nickname_edit),
                    contentDescription = stringResource(id = R.string.edit_label),
                    tint = AppTheme.colors.button02a,
                )
            }
        }
        Spacer(modifier = Modifier.height(20.dp))

        LoadVehicleImage(
            url = vehicleInfo?.image ?: ToyotaConstants.EMPTY_STRING,
            accessibilityId = AccessibilityId.ID_VEHICLE_INFO_VEHICLE_IMAGE,
            contentDescription = R.string.vehicleInfo,
            width = 187.dp,
            height = 120.dp,
        )
        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = stringResource(id = R.string.vehicle_identification_number),
            style = AppTheme.fontStyles.caption1,
            color = AppTheme.colors.tertiary03,
            modifier = Modifier.align(Alignment.CenterHorizontally),
        )

        Spacer(modifier = Modifier.height(6.dp))

        vehicleInfo?.let { data ->
            DynamicVinComponent(
                scaffoldState = scaffoldState,
                vin = data.vin,
                onYCoordinatePositioned = onYCoordinatePositioned,
                testTagIdPrefix = TestTagIdPrefixVehicleInfo,
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        (vehicleInfoState.value as Success).data.forEach {
            MenuItem(it, navHostController, launcher)
        }

        Spacer(modifier = Modifier.height(84.dp))
    }
}

fun initializeSoftwareUpdates(
    isVisible: Boolean?,
    viewModel: VehicleInfoViewModel,
) {
    if (isVisible == true) {
        viewModel.fetchSoftwareUpdate()
    } else {
        viewModel.updateState(VehicleInfoState.Loading)
    }
}

fun isActiveSubscription(
    paidSubscriptionsStatus: Boolean,
    trialSubscriptionsStatus: Boolean,
): Boolean = paidSubscriptionsStatus || trialSubscriptionsStatus
