package com.toyota.oneapp.features.core.composable

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun BaseCompose(view: @Composable (PaddingValues) -> Unit) {
    Scaffold(
        backgroundColor = AppTheme.colors.tertiary12,
        content = view,
    )
}

@Composable
fun NavTitleSection(
    navHostController: NavHostController,
    title: String,
    accessibilityId: String,
) {
    Box(
        modifier = Modifier.padding(start = 16.dp, top = 9.dp),
    ) {
        Surface(
            shape = CircleShape,
            color = AppTheme.colors.button02b,
            modifier =
                Modifier
                    .size(48.dp),
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_back_arrow),
                contentDescription = null,
                modifier =
                    Modifier
                        .testTagID(accessibilityId)
                        .padding(16.dp)
                        .clickable {
                            navHostController.popBackStack()
                        },
                tint = AppTheme.colors.button02a,
            )
        }

        OASubHeadLine3TextView(
            text = title,
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier
                    .padding(top = 8.dp)
                    .fillMaxWidth(),
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
fun NavTitleSection(
    title: String,
    accessibilityId: String,
    isCloseIcon: Boolean = false,
    onClick: () -> Unit,
) {
    Box(
        modifier = Modifier.padding(start = 16.dp, top = 9.dp),
    ) {
        Surface(
            shape = CircleShape,
            color = AppTheme.colors.button02b,
            modifier =
                Modifier
                    .size(48.dp),
        ) {
            Icon(
                painter =
                    painterResource(
                        id =
                            if (isCloseIcon) {
                                R.drawable.ic_close
                            } else {
                                R.drawable.ic_back_arrow
                            },
                    ),
                contentDescription = null,
                modifier =
                    Modifier
                        .testTagID(accessibilityId)
                        .padding(16.dp)
                        .clickable {
                            onClick()
                        },
                tint = AppTheme.colors.button02a,
            )
        }

        OASubHeadLine3TextView(
            text = title,
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier
                    .padding(top = 8.dp)
                    .fillMaxWidth(),
            textAlign = TextAlign.Center,
        )
    }
}
