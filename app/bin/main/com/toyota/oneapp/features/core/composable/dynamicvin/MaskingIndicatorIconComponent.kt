/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.composable.dynamicvin

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Unspecified
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R.drawable.ic_hide
import com.toyota.oneapp.R.drawable.ic_show
import com.toyota.oneapp.features.core.composable.testTagID

@Composable
fun MaskingIndicatorIconComponent(
    modifier: Modifier,
    onClick: () -> Unit,
    isVinVisible: Boolean,
    testTagId: String,
) {
    Icon(
        modifier =
            modifier
                .width(24.dp)
                .height(24.dp)
                .padding(start = 3.dp, top = 4.dp, end = 3.dp, bottom = 4.dp)
                .clickable(enabled = true, onClick = onClick)
                .testTagID(tag = testTagId),
        painter = painterResource(if (isVinVisible) ic_hide else ic_show),
        contentDescription = null,
        tint = Unspecified,
    )
}
