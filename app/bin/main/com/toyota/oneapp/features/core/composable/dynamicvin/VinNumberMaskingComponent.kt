/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.composable.dynamicvin

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme.colors

@Composable
fun VinNumberMaskingComponent(
    modifier: Modifier,
    vinText: String,
    testTagId: String,
) {
    OACallOut2TextView(
        text = vinText,
        color = colors.tertiary03,
        modifier = modifier.testTagID(testTagId),
    )
}
