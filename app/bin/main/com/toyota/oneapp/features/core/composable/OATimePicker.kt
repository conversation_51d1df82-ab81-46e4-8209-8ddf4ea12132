package com.toyota.oneapp.features.core.composable

import android.app.TimePickerDialog
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.Constants

@Composable
fun OATimePicker(
    selectedHour: Int,
    selectedMin: Int,
    isAM: Boolean,
    isPickerEnabled: Boolean = true,
    content: @Composable (click: () -> Unit) -> Unit,
    onTimeSelected: (hour: Int, min: Int) -> Unit,
) {
    val mContext = LocalContext.current

    var adjustedHour = if (isAM) selectedHour else selectedHour + Constants.INT_12
    if (adjustedHour == Constants.INT_24) adjustedHour = Constants.INT_12 // 12 AM
    if (adjustedHour == Constants.INT_12 && !isAM) adjustedHour = 0 // 12 PM

    val mTimePickerDialog =
        TimePickerDialog(
            mContext,
            { _, mHour: Int, mMinute: Int ->
                onTimeSelected(mHour, mMinute)
            },
            adjustedHour,
            selectedMin,
            false,
        )
    content {
        if (isPickerEnabled) {
            mTimePickerDialog.show()
        }
    }
}

@Composable
fun TimeTextView(
    time: String,
    modifier: Modifier = Modifier,
    backgroundColor: Color = AppTheme.colors.tile01,
    textColor: Color = AppTheme.colors.tertiary00,
    onClick: () -> Unit,
) {
    Surface(
        elevation = 0.dp,
        shape = RoundedCornerShape(32.dp),
        modifier =
            modifier
                .wrapContentSize()
                .clickable {
                    onClick()
                },
        color = backgroundColor,
    ) {
        OASubHeadLine3TextView(
            text = time,
            color = textColor,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 8.dp),
        )
    }
}
