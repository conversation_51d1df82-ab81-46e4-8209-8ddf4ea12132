/*
 * *
 *  * Copyright © 2024. Toyota Motors North America Inc
 *  * All rights reserved.
 *
 */

package com.toyota.oneapp.features.core.composable

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun OAFullScreenBottomSheetLayout(
    backgroundColor: Color,
    screenTitle: String,
    testTagId: String,
    modifier: Modifier = Modifier,
    onBack: () -> Unit,
    actionWidget: @Composable () -> Unit = { },
    content: @Composable () -> Unit,
) {
    BackHandler {
        onBack()
    }
    Scaffold(
        backgroundColor = backgroundColor,
        modifier =
            Modifier
                .fillMaxSize(),
    ) {
        Column(modifier = modifier) {
            Image(
                painter = painterResource(id = R.drawable.ic_drag_indicator),
                contentDescription = stringResource(id = R.string.content_drag),
                modifier =
                    Modifier
                        .width(28.dp)
                        .height(4.dp)
                        .align(Alignment.CenterHorizontally),
                colorFilter = ColorFilter.tint(AppTheme.colors.outline01),
            )

            OAAppBar(
                title = screenTitle,
                testTagId = testTagId,
                actionWidget = actionWidget,
                modifier =
                    Modifier
                        .padding(vertical = 8.dp),
            ) {
                onBack()
            }

            content()
        }
    }
}
