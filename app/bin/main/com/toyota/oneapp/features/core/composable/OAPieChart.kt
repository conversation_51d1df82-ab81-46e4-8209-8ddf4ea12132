/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.core.composable

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch

private const val GAP_DEGREE = 16f
private const val COMPLETE_DEGREE = 360f

@Composable
fun OAPieChart(
    pieDataPoints: List<PieData>,
    centerImage: Int,
    contentDescription: String,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        AnimatedGapPieChart(
            modifier =
                Modifier
                    .fillMaxSize()
                    .align(Alignment.Center),
            pieDataPoints = pieDataPoints,
        )

        Image(
            painter = painterResource(id = centerImage),
            contentDescription = contentDescription,
            modifier =
                Modifier
                    .align(Alignment.Center)
                    .size(24.dp),
        )
    }
}

data class ArcData(
    val animation: Animatable<Float, AnimationVector1D>,
    val targetSweepAngle: Float,
    val startAngle: Float,
    val color: Color,
)

data class PieData(
    val value: Int,
    var color: Color,
)

@Composable
fun AnimatedGapPieChart(
    modifier: Modifier = Modifier,
    pieDataPoints: List<PieData>,
) {
    val numberOfGaps = pieDataPoints.size
    val remainingDegrees = COMPLETE_DEGREE - (GAP_DEGREE * numberOfGaps)
    val localModifier = modifier.size(80.dp) // Before 200.dp
    val total = pieDataPoints.fold(0f) { acc, pieData -> acc + pieData.value }.div(remainingDegrees)
    var currentSum = 0f
    val arcs =
        pieDataPoints.mapIndexed { index, it ->
            val startAngle = currentSum + (index * GAP_DEGREE)
            currentSum += it.value / total
            ArcData(
                targetSweepAngle = it.value / total,
                animation = Animatable(0f),
                startAngle = -90 + startAngle,
                color = it.color,
            )
        }

    LaunchedEffect(arcs) {
        arcs.mapIndexed { index, arcData ->
            launch {
                arcData.animation.animateTo(
                    targetValue = arcData.targetSweepAngle,
                    animationSpec =
                        tween(
                            durationMillis = 1000,
                            easing = LinearEasing,
                            delayMillis = index * 100,
                        ),
                )
            }
        }
    }

    Canvas(
        modifier =
            localModifier
                .scale(1f),
    ) {
        val stroke = Stroke(width = 5f, cap = StrokeCap.Round) // previous width 50f

        arcs.map {
            drawArc(
                startAngle = it.startAngle,
                sweepAngle = it.animation.value,
                color = it.color,
                useCenter = false,
                style = stroke,
            )
        }
    }
}
