package com.toyota.oneapp.features.core.theme.colors

import androidx.compose.ui.graphics.Color

/**
 * prefix with 0xFF instead of #
 * **/
class SubaruDarkColors : OAColors() {
    override val success01 = Color(0xFF5B82F9)
    override val success02 = Color(0xFF202226)

    override val error01 = Color(0xFFEB0A1E)

    override val brand01 = Color(0xFFFFFFFF)

    override val primary01 = Color(0xFFEB0A1E)
    override val primary02 = Color(0xFF111112)
    override val primaryButton01 = Color(0xFF202226)
    override val primaryButton02 = Color(0xFFFAFAFA)

    override val secondary01 = Color(0xFF5B82F9)
    override val secondary02 = Color(0xFF111112)

    override val tertiary00 = Color(0xFFFFFFFF)
    override val tertiary03 = Color(0xFFFAFAFA)
    override val tertiary03a = Color(0xFF262626)
    override val tertiary05 = Color(0xFFA6A6A6)
    override val tertiary07 = Color(0xFF959595)
    override val tertiary10 = Color(0xFF202226)
    override val tertiary12 = Color(0xFF111112)
    override val tertiary15 = Color(0xFF151517)
    override val dashboard01 = Color(0xFF0D437A)

    override val button01a = Color(0xFFFAFAFA)
    override val button01b = Color(0xFF202226)
    override val button02a = Color(0xFFFAFAFA)
    override val button02b = Color(0xFF202226)
    override val button02c = Color(0xFF202226)
    override val button03a = Color(0xFFFAFAFA)
    override val button03b = Color(0xFF5B82F9)
    override val button03c = Color(0xFFA879FF)
    override val button03d = Color(0xFF43CD93)
    override val button03e = Color(0xFFFF9A3E)
    override val button05a = Color(0xFF959595)
    override val button05b = Color(0xFF202226)

    override val button02d = Color(0xFF18191C)
    override val tile01 = Color(0xFF151517)
    override val tile02 = Color(0xFF18191C)
    override val tile03 = Color(0xFF18191C)
    override val tile05 = Color(0xFF202226)
    override val primaryLightBlue = Color(0XFF202226)
    override val primaryLight02 = Color(0XFF111112)
    override val noImageBGColor = Color(0xFF202226)
    override val outline01 = Color(0XFF575757)
    override val statusBarColorLight = Color(0xFFFAFAFA)
    override val statusBarColorDark = Color(0xFF111112)

    val gradientOneStart = Color(0xFFF26D51)
    val gradientOneEnd = Color(0xFFEB0A1E)
    override val gradientThreeStart = Color(0xFF00A969)
    val gradientThreeEnd = Color(0xFF2FE49F)
    val gradientFourStart = Color(0xFF5A5A5A)
    val gradientFourEnd = Color(0xFFD4D4D4)
    val gradientFiveStart = Color(0xFF5183F8)
    val gradientFiveEnd = Color(0xFFA879FF)
}
