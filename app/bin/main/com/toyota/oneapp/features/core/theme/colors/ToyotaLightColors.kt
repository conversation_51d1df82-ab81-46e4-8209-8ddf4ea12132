package com.toyota.oneapp.features.core.theme.colors

import androidx.compose.ui.graphics.Color

class ToyotaLightColors : OAColors() {
    override val success01 = Color(0xFF5B82F9)
    override val success02 = Color(0xFFEFF2FE)

    override val error01 = Color(0xFFEB0A1E)

    override val brand01 = Color(0xFFEB0A1E)

    override val primary01 = Color(0xFFEB0A1E)
    override val primary02 = Color(0xFFFFF2F2)
    override val primaryButton01 = Color(0xFFFFFFFF)
    override val primaryButton02 = Color(0xFF262626)

    override val secondary01 = Color(0xFF5B82F9)
    override val secondary02 = Color(0xFFEFF2FE)

    override val tertiary00 = Color(0xFF000000)
    override val tertiary03 = Color(0xFF262626)
    override val tertiary03a = Color(0xFF262626)
    override val tertiary05 = Color(0xFF595959)
    override val tertiary07 = Color(0xFF959595)
    override val tertiary10 = Color(0xFFDEDEDE)
    override val tertiary12 = Color(0xFFFAFAFA)
    override val tertiary15 = Color(0xFFFFFFFF)
    override val dashboard01 = Color(0xFFFFFFFF)

    override val button01a = Color(0xFFFFFFFF)
    override val button01b = Color(0xFF262626)
    override val button02a = Color(0xFF262626)
    override val button02b = Color(0xFFFAFAFA)
    override val button02c = Color(0xFFDEDEDE)
    override val button03a = Color(0xFFFFFFFF)
    override val button03b = Color(0xFF5B82F9)
    override val button03c = Color(0xFFA879FF)
    override val button03d = Color(0xFF43CD93)
    override val button03e = Color(0xFFFF9A3E)
    override val button05a = Color(0xFF959595)
    override val button05b = Color(0xFFF1F1F1)
    override val primaryLight02 = Color(0XFFFFF2F2)
    override val noImageBGColor = Color(0x80FFFFFF)

    override val button02d = Color(0xFFFAFAFA)
    override val tile01 = Color(0xFFFFFFFF)
    override val tile02 = Color(0xFFFAFAFA)
    override val tile03 = Color(0xFFFFFFFF)
    override val tile05 = Color(0xFFFAFAFA)
    override val primaryLightBlue = Color(0XFFEFF2FE)
    override val outline01 = Color(0XFFDEDEDE)
    override val statusBarColorLight = Color(0xFFFAFAFA)
    override val statusBarColorDark = Color(0xFF111112)

    override val gradientThreeStart = Color(0xFF00A969)
}
