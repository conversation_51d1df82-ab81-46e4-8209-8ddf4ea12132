/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.composable.dynamicvin

import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R.drawable.ic_copy
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme.colors

@Composable
fun CopyingEnabledIconComponent(
    modifier: Modifier,
    testTagId: String,
) {
    Icon(
        modifier =
            modifier
                .padding(1.dp)
                .width(24.dp)
                .height(24.dp)
                .testTagID(testTagId),
        painter = painterResource(ic_copy),
        contentDescription = null,
        tint = colors.tertiary03,
    )
}
