package com.toyota.oneapp.features.core.commonapicalls.domain.repository

import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeTimerRequest
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.CommonScheduleResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.EVChargeSessionResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.RealTimeStatusResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import kotlin.coroutines.CoroutineContext

abstract class CommonApiRepository(
    errorParser: Error<PERSON>essageParser,
    ioContext: CoroutineContext,
) : BaseRepository(errorParser, ioContext) {
    abstract suspend fun fetchChargeManagementDetail(
        vin: String,
        generation: String,
        brand: String,
    ): Resource<ElectricStatusResponse?>

    abstract suspend fun fetchProfileDetails(
        brand: String,
        guid: String,
    ): Resource<ProfileInfoResponse?>

    abstract suspend fun fetchEvChargingSessionDetails(
        requestId: String,
        chargingId: String,
        apiKey: String,
        partner: String,
    ): Resource<EVChargeSessionResponse?>

    abstract suspend fun postEvRealTimeStatus(
        requestId: String,
        vin: String,
        brand: String,
        generation: String,
        deviceId: String,
    ): Resource<RealTimeStatusResponse?>

    abstract suspend fun postEvRealTimeChargeInfoStatus(
        vin: String,
        brand: String,
        generation: String,
        deviceId: String,
    ): Resource<ElectricStatusResponse?>

    abstract suspend fun getEvRealTimeStatus(
        vin: String,
        brand: String,
        generation: String,
        appRequestNo: String,
    ): Resource<ElectricStatusResponse?>

    abstract suspend fun getEVRemoteControlStatus(
        vin: String,
        brand: String,
        generation: String,
        appRequestNo: String,
    ): Resource<ElectricStatusResponse?>

    abstract suspend fun postElectricVehicleCommand(
        vin: String,
        brand: String,
        generation: String,
        requestBody: ChargeTimerRequest,
    ): Resource<CommonScheduleResponse?>
}
