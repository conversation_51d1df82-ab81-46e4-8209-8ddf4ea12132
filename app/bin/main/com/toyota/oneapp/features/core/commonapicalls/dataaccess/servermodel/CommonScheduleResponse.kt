/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.network.models.BaseResponse

data class CommonScheduleResponse(
    @SerializedName("payload") val payload: CommonSchedulePayload?,
) : BaseResponse()

data class CommonSchedulePayload(
    @SerializedName("appRequestNo") val appRequestNo: String?,
    @SerializedName("message") val message: String?,
    @SerializedName("returnCode") val returnCode: String?,
)

fun CommonSchedulePayload.getAppRequestNo(): String? {
    return if (this.returnCode == "ONE-RES-10000") {
        appRequestNo
    } else {
        null
    }
}
