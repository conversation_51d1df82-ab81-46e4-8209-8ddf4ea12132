package com.toyota.oneapp.features.core.util

object Constants {
    const val ImageNotFound = "image_not_found"
    const val REMOTE_COMMANDS_ACTIVE = 7
    const val SERVICE_HISTORY_ID = "-1"
    const val REFRESH_UI = "refresh_ui"

    // DeepLint Routes
    const val DeepLinkRegistration = "sms_registration"
    const val DeepLinkAccountLinking = "sms_linkaccounts"
    const val DeepLinkSubscription = "sms_subscription"
    const val DeepLinkRemoteAccess = "sms_remoteaccess"
    const val DeepLinkPinReset = "sms_pinreset"
    const val DeepLinkManageSubscription = "manage_subscription"
    const val DeepLinkManageSubscriptions = "manage_subscriptions"
    const val DeepLinkLastParked = "last_parked"
    const val DeepLinkCAS = "cas_sms"
    const val NotificationCAS = "cas"
    const val DeepLinkManageProfile = "manage_profile"
    const val DeepLinkManageGarage = "manage_garage"
    const val DeepLinkManageGuestDriver = "guest_driver"
    const val DeepLinkManageScheduleService = "schedule_service"
    const val DeepLinkManageDigitalKey = "sms_dk_share"
    const val DeepLinkManagePrivacyPortal = "privacy_policy"
    const val DEEP_LINK_PREFERRED_DEALER = "preferred_dealer"
    const val DEEP_LINK_SHARE_POI = "share/poi"
    const val DEEP_LINK_ACTIVATE = "activate"
    const val NOT_FOUND = "not found"
    const val VEHICLE_SOFTWARE_NOTIFICATION_STATUS = "notificationStatus"
    const val SOFTWARE_UPDATE_21MM_DETAILS = "softwareUpdate21mmDetails"
    const val DEEP_LINK_CHARGE_INFO = "charge_info"
    const val DEEP_LINK_CHARGE_ASSIST_ENROLLMENT_STATUS = "charge_assist"

    // Nick Name char limit
    const val MAX_NICKNAME_CHARACTER_ALLOWED = 20

    // Transparent FrameLayout for VIN scan view
    const val BAR_CODE_HEIGHT_DOUBLE = 3.0
    const val BAR_CODE_WIDTH_DOUBLE = 1.2
    const val BAR_CODE_OFFSET_DOUBLE = 3.0
    const val BAR_CODE_ZERO_FIVE_DOUBLE = 0.05
    const val TWELVE = 12

    const val CAS_WARNING = "CAS warning"
    const val CAS_HORN = "CAS horn"

    const val ADVANCED_REAR_SEAT_REMINDER: String = "advanced_rear_seat_reminder"
    const val MILLI_SECOND = 1000L

    const val DATE_FORMATTER_LONG: String = "MMM dd, yyyy"
    const val DATE_TIME_PATTERN_MM_DD_YYYY_HH_MM = "MM-dd-yyyy HH:mm"
    const val TIME_FORMATTER_WITH_PERIOD = "hh:mm a"
    const val DATE_FORMATTER_SHORT = "MM-dd-yyyy"
    const val TIME_FORMATTER_24_HOUR = "HH:mm"
    const val UTC = "UTC"
    const val WEIGHT_90 = .9f
    const val WEIGHT_84 = .84f
    const val INT_0 = 0
    const val INT_1 = 1
    const val INT_2 = 2
    const val INT_3 = 3
    const val INT_5 = 5
    const val INT_7 = 7
    const val INT_10 = 10
    const val INT_12 = 12
    const val INT_24 = 24
    const val INT_25 = 25
    const val INT_47 = 47
    const val INT_50 = 50
    const val INT_60 = 60
    const val INT_90 = 90
    const val INT_100 = 100
    const val INT_125 = 125
    const val INT_140 = 140
    const val INT_500 = 500
    const val INT_550 = 550
    const val INT_1000 = 1000
    const val INT_3600 = 3600
    const val DEC_0_5 = 0.5
    const val FLOAT_0_1 = 0.1f
    const val FLOAT_0_5 = 0.5f
    const val FLOAT_0_7 = 0.7f
    const val FLOAT_0_9 = 0.9f
    const val FLOAT_15 = 15f
    const val FLOAT_14 = 14f
    const val LONG_60000L = 60000L
    const val LONG_1000L = 1000L
    const val LONG_5000L = 5000L
    const val REGEX_EMAIL =
        "^[0-9A-Z_a-z][-0-9A-Z_a-z]*" +
            "(\\.[-0-9A-Z_a-z]+)*@" +
            "([0-9A-Z_a-z][-0-9A-Z_a-z]*" +
            "(\\.[-0-9A-Z_a-z]+)*\\." +
            "(AERO|ARPA|BIZ|COM|COOP|EDU|GOV|INFO|INT|LAW|MIL|MOBI|MUSEUM|NAME|" +
            "NET|ORG|PRO|TRAVEL|aero|arpa|biz|com|coop|edu|gov|info|int|law|mil|" +
            "mobi|museum|name|net|org|pro|travel|[A-Za-z][A-Za-z])|" +
            "([0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}))" +
            "(:[0-9]{1,5})?\$"

    const val PLACEHOLDER_SECONDARY_TEXT = "Secondary text example"

    const val FLEET_SB1394 = "VS-ERROR-SB1394"
}
