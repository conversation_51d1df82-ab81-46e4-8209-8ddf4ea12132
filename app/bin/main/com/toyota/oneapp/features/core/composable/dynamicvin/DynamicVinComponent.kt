/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.composable.dynamicvin

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement.Center
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.material.ScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment.Companion.CenterVertically
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.util.AccessibilityId.VIN_COPY_ICON_CTA
import com.toyota.oneapp.features.core.util.AccessibilityId.VIN_HIDE_ICON_CTA
import com.toyota.oneapp.features.core.util.AccessibilityId.VIN_LABEL_TEXT_CTA
import com.toyota.oneapp.features.core.util.AccessibilityId.VIN_NUMBER_TEXT_CTA
import com.toyota.oneapp.features.core.util.AccessibilityId.VIN_SHOW_ICON_CTA
import com.toyota.oneapp.util.StringUtil.maskVin
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlin.text.orEmpty

/**
 * Jetpack Compose UI component to display VIN with masking option
 *
 * This component has dynamic masking behavior.
 * The user can show or hide the VIN by tapping the icon.
 *
 * When the VIN is not masked,
 * the user can copy it by tapping on the VIN,
 * the copy icon is visible,
 * and the show/hide icon is in the "hide" format.
 *
 * When the VIN is masked (only last 4 digits are visible),
 * the user cannot copy,
 * the copy icon is not visible,
 * and the show/hide icon is in the "show" format.
 *
 * Currently used in combination with VinCopiedSnackbar,
 * which presents a popup just above the VIN
 * to give the user feedback of the copy functionality
 */
@Composable
fun DynamicVinComponent(
    scaffoldState: ScaffoldState,
    vin: String,
    onYCoordinatePositioned: (Dp) -> Unit,
    testTagIdPrefix: TestTagIdPrefix,
) {
    var isVinVisible by remember { mutableStateOf(false) } // VIN should be hidden by default
    val clipboardManager = LocalClipboardManager.current
    val coroutineScope: CoroutineScope = rememberCoroutineScope()
    val localDensity = LocalDensity.current
    var yCoordinate by remember { mutableStateOf(0.dp) }
    val vinVisible by remember { mutableStateOf(vin) }
    val vinMasked by remember { mutableStateOf(maskVin(vin = vin)) }

    Row(
        modifier =
            Modifier.Companion
                .fillMaxWidth()
                .height(24.dp)
                .onGloballyPositioned { coordinates ->
                    with(localDensity) {
                        yCoordinate = coordinates.positionInRoot().y.toDp()
                    }
                },
        verticalAlignment = CenterVertically,
        horizontalArrangement = Center,
    ) {
        Row(
            modifier =
                Modifier.clickable(
                    enabled = isVinVisible,
                    onClick = {
                        onYCoordinatePositioned(yCoordinate)
                        clipboardManager.setText(
                            AnnotatedString(vin.orEmpty()),
                        )
                        coroutineScope.launch {
                            scaffoldState.snackbarHostState.showSnackbar("")
                        }
                    },
                ),
        ) {
            if (isVinVisible) {
                CopyingEnabledIconComponent(
                    modifier = Modifier.align(alignment = CenterVertically),
                    testTagId = "${testTagIdPrefix}$VIN_COPY_ICON_CTA",
                )
                Spacer(modifier = Modifier.Companion.width(4.dp))
            } else {
                // do not display the icon
            }
            VinLabelComponent(
                modifier = Modifier.align(alignment = CenterVertically),
                testTagId = "${testTagIdPrefix}$VIN_LABEL_TEXT_CTA",
            )
            Spacer(modifier = Modifier.Companion.width(7.dp))
            VinNumberMaskingComponent(
                modifier = Modifier.align(alignment = CenterVertically),
                vinText = if (isVinVisible) vinVisible else (vinMasked ?: " - "),
                testTagId = "${testTagIdPrefix}$VIN_NUMBER_TEXT_CTA",
            )
        }
        Spacer(modifier = Modifier.Companion.width(4.dp))
        MaskingIndicatorIconComponent(
            modifier = Modifier.align(CenterVertically),
            onClick = { isVinVisible = !isVinVisible },
            isVinVisible = isVinVisible,
            testTagId =
                if (isVinVisible) {
                    "${testTagIdPrefix}$VIN_HIDE_ICON_CTA"
                } else {
                    "${testTagIdPrefix}$VIN_SHOW_ICON_CTA"
                },
        )
    }
}

sealed class TestTagIdPrefix(prefix: String) {
    object TestTagIdPrefixVehicleInfo : TestTagIdPrefix(prefix = "vehicle_info_")

    object TestTagIdPrefixVehicleSwitcher : TestTagIdPrefix(prefix = "vehicle_switcher_")
}
