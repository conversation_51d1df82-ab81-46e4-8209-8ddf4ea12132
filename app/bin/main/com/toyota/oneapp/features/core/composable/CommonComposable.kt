/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.composable

import android.content.Context
import android.location.Location
import android.util.Base64
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.Snackbar
import androidx.compose.material.SnackbarHost
import androidx.compose.material.SnackbarHostState
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldColors
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTag
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.core.content.ContextCompat
import coil.compose.AsyncImagePainter
import coil.compose.SubcomposeAsyncImage
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.google.android.gms.location.LocationServices
import com.google.android.gms.maps.model.BitmapDescriptor
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MapStyleOptions
import com.google.maps.android.compose.CameraPositionState
import com.google.maps.android.compose.GoogleMap
import com.google.maps.android.compose.MapProperties
import com.google.maps.android.compose.MapUiSettings
import com.google.maps.android.compose.Marker
import com.google.maps.android.compose.MarkerState
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.commonapicalls.domain.model.DialogData
import com.toyota.oneapp.features.core.composable.dynamicvin.TestTagIdPrefix
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.colors.RemoteCommandProgressColors
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.AccessibilityId.VIN_COPIED_POPUP_TEXT
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.features.find.presentation.LocationPermissionsAndSettingDialogs
import com.toyota.oneapp.features.find.utils.LocationUtil
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.ftue.presentation.extension.w
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthdetail.domain.DateModel
import kotlinx.coroutines.flow.MutableStateFlow

@Composable
fun ComposableBottomNavText(
    text: String,
    selected: Boolean,
) {
    Text(
        text = text,
        style =
            if (selected) {
                AppTheme.fontStyles.tabLabel01
            } else {
                AppTheme.fontStyles.tabLabel02
            },
        color =
            if (selected) {
                AppTheme.colors.brand01
            } else {
                AppTheme.colors.tertiary07
            },
    )
}

@Composable
fun ComposableBackground(text: String) {
    Text(
        text = text,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        color = AppTheme.colors.tertiary15,
        textAlign = TextAlign.Left,
        modifier = Modifier.padding(16.dp),
    )
}

@Composable
fun ComposableTitle(text: String) {
    Text(
        text = text,
        fontSize = 16.sp,
        color = AppTheme.colors.tertiary00,
        textAlign = TextAlign.Left,
        modifier =
            Modifier.padding(
                top = 23.dp,
                bottom = 23.dp,
                start = 16.dp,
                end = 16.dp,
            ),
    )
}

@Composable
fun ComposableSubTitle(text: String) {
    Text(
        text = text,
        fontSize = 14.sp,
        color = AppTheme.colors.tertiary00,
        textAlign = TextAlign.Left,
        modifier =
            Modifier.padding(
                top = 23.dp,
                bottom = 23.dp,
                start = 16.dp,
                end = 16.dp,
            ),
    )
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ComposableRoundedCornerCard(
    backgroundColor: Color = AppTheme.colors.tile01,
    cornerRadius: Dp,
    modifier: Modifier,
    click: () -> Unit,
    view: @Composable () -> Unit,
) {
    val context = LocalContext.current
    val cardShadowColor =
        remember {
            Color(ContextCompat.getColor(context, R.color.card_shadow))
        }
    Card(
        backgroundColor = backgroundColor,
        shape = RoundedCornerShape(cornerRadius),
        elevation = 4.dp,
        modifier =
            modifier
                .shadow(
                    elevation = 5.dp,
                    shape = RoundedCornerShape(cornerRadius),
                    ambientColor = cardShadowColor,
                    spotColor = cardShadowColor,
                ),
        content = view,
        onClick = click,
    )
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ComposableCard(
    view: @Composable () -> Unit,
    click: () -> Unit,
) {
    Card(
        backgroundColor = AppTheme.colors.tertiary15,
        elevation = 6.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            Modifier
                .padding(
                    start = 16.dp,
                    end = 16.dp,
                    top = 8.dp,
                    bottom = 8.dp,
                ).fillMaxWidth(),
        content = view,
        onClick = click,
    )
}

@Composable
fun ComposableCircleItem(text: String) {
    Box(
        modifier =
            Modifier
                .width(104.dp)
                .height(104.dp)
                .clip(CircleShape)
                .background(AppTheme.colors.button05a)
                .wrapContentSize(Alignment.Center),
    ) {
        Column(
            modifier = Modifier.align(Alignment.Center),
        ) {
            Text(
                text = text,
                style = AppTheme.fontStyles.caption2,
            )
        }
    }
}

@Composable
fun CommonDigitalKeyStatusText(
    text: String,
    modifier: Modifier,
) {
    Spacer(modifier = Modifier.height(16.dp))
    Text(
        text = text,
        fontSize = 14.sp,
        color = AppTheme.colors.tertiary07,
        textAlign = TextAlign.Center,
        modifier =
            modifier
                .wrapContentSize(align = Alignment.Center)
                .padding(horizontal = 16.dp),
    )
}

@Composable
fun CircleLoading(
    indicatorSize: Dp = 100.dp,
    animationDuration: Int = 1800,
    progressColorRed: Boolean = false,
) {
    val circleColors =
        if (progressColorRed) RemoteCommandProgressColors.redCircularColor else RemoteCommandProgressColors.blueCircularColor

    val infiniteTransition = rememberInfiniteTransition()

    val rotateAnimation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec =
            infiniteRepeatable(
                animation =
                    tween(
                        durationMillis = animationDuration,
                        easing = LinearEasing,
                    ),
            ),
    )

    CircularProgressIndicator(
        modifier =
            Modifier
                .size(size = indicatorSize)
                .rotate(degrees = rotateAnimation)
                .border(
                    width = 2.dp,
                    brush = Brush.sweepGradient(circleColors),
                    shape = CircleShape,
                ),
        progress = 1f,
        strokeWidth = 1.dp,
        color = Color(0xFFDEDEDE),
    )
}

@Preview(showBackground = true)
@Composable
fun PreviewComposableSubTitle() {
    ComposableSubTitle("Preview")
}

@Preview(showBackground = true)
@Composable
fun PreviewComposableBottomNavText() {
    ComposableBottomNavText("Bottom Nav", true)
}

@Composable
fun OABorderButton(
    accessibilityID: String,
    text: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Button(
        onClick = onClick,
        colors =
            ButtonDefaults.buttonColors(
                backgroundColor = AppTheme.colors.primaryButton01,
            ),
        shape = RoundedCornerShape(100.dp),
        border = BorderStroke(2.dp, AppTheme.colors.tertiary10),
        modifier =
            modifier
                .testTagID(accessibilityID)
                .padding(4.dp),
    ) {
        Box(
            Modifier.wrapContentSize(),
        ) {
            OACallOut2TextViewResizable(
                text = text,
                color = AppTheme.colors.primaryButton02,
                modifier =
                    Modifier
                        .padding(all = 2.dp)
                        .align(Alignment.Center),
            )
        }
    }
}

@Composable
fun OATabButton(
    accessibilityID: String,
    text: String,
    modifier: Modifier = Modifier,
    isSelected: Boolean = false,
    hasUpdates: Boolean = false,
    onClick: () -> Unit,
) {
    Button(
        onClick = onClick,
        colors =
            ButtonDefaults.buttonColors(
                backgroundColor = if (isSelected) AppTheme.colors.primaryButton02 else AppTheme.colors.primaryButton01,
            ),
        shape = RoundedCornerShape(100.dp),
        border = BorderStroke(2.dp, AppTheme.colors.tertiary10),
        modifier =
            modifier
                .testTagID(accessibilityID)
                .padding(4.dp),
    ) {
        Box(
            Modifier.fillMaxWidth(),
        ) {
            OACallOut2TextViewResizable(
                text = text,
                color = if (isSelected) AppTheme.colors.primaryButton01 else AppTheme.colors.primaryButton02,
                modifier =
                    Modifier
                        .padding(all = 2.dp)
                        .align(Alignment.Center),
            )
            if (hasUpdates) {
                Box(
                    modifier =
                        Modifier
                            .size(5.dp)
                            .clip(CircleShape)
                            .background(AppTheme.colors.error01)
                            .align(Alignment.CenterEnd),
                )
            }
        }
    }
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
fun CircularImage(
    base64Url: String? = null,
    drawable: Int = 0,
) {
    if (drawable != 0) {
        Image(
            painter = painterResource(id = drawable),
            contentDescription = stringResource(id = R.string.profile_picture),
            modifier =
                Modifier
                    .width(80.dp)
                    .height(80.dp),
        )
    } else {
        base64Url?.let {
            GlideImage(
                model = Base64.decode(it, Base64.DEFAULT),
                contentDescription = null,
                modifier =
                    Modifier
                        .clip(CircleShape)
                        .width(80.dp)
                        .height(80.dp),
            )
        }
    }
}

@Composable
fun CustomSwitch(
    customSwitchParams: CustomSwitchParams = CustomSwitchParams(),
    modifier: Modifier = Modifier,
    onCheckedChange: (Boolean) -> Unit,
    isClickable: Boolean = true,
    isEnabled: Boolean,
    testTagId: String,
    shouldEnableCADisableColor: Boolean = false,
) {
    val switchON =
        remember {
            mutableStateOf(true) // Initially the switch is ON
        }
    switchON.value = isEnabled

    val canClick = remember { mutableStateOf(true) }
    canClick.value = isClickable

    val thumbRadius = (customSwitchParams.height / 2) - 4.dp

    // To move thumb, we need to calculate the position (along x axis)
    val animatePosition =
        animateFloatAsState(
            targetValue =
                if (switchON.value) {
                    with(LocalDensity.current) { (customSwitchParams.width - thumbRadius - 4.dp).toPx() }
                } else {
                    with(LocalDensity.current) { (thumbRadius + 4.dp).toPx() }
                },
        )
    val disableColor = AppTheme.colors.tertiary10
    val trackColor = AppTheme.colors.tertiary10
    val checkedCircleColor = AppTheme.colors.secondary01
    val unCheckedCircleColor = AppTheme.colors.tertiary05

    Canvas(
        modifier =
            modifier
                .testTagID(testTagId)
                .padding(end = 16.dp)
                .size(width = customSwitchParams.width, height = customSwitchParams.height)
                .scale(scale = 1.5f)
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = {
                            if (canClick.value) {
                                switchON.value = !switchON.value
                                onCheckedChange(switchON.value)
                            }
                        },
                    )
                },
    ) {
        // Track
        drawRoundRect(
            color = trackColor,
            cornerRadius = CornerRadius(x = 10.dp.toPx(), y = 10.dp.toPx()),
            style = Stroke(width = 2.dp.toPx()),
        )

        if (shouldEnableCADisableColor) {
            val color =
                shouldEnableCADisableColorFunctionality(
                    isClickable,
                    switchON,
                    checkedCircleColor,
                    disableColor,
                    unCheckedCircleColor,
                )

            // Thumb
            drawCircle(
                color = color,
                radius = thumbRadius.toPx(),
                center =
                    Offset(
                        x = animatePosition.value,
                        y = size.height / 2,
                    ),
            )
        } else {
            // Thumb
            drawCircle(
                color = if (switchON.value) checkedCircleColor else unCheckedCircleColor,
                radius = thumbRadius.toPx(),
                center =
                    Offset(
                        x = animatePosition.value,
                        y = size.height / 2,
                    ),
            )
        }
    }

    Spacer(modifier = Modifier.height(18.dp))
}

fun shouldEnableCADisableColorFunctionality(
    isClickable: Boolean,
    switchON: MutableState<Boolean>,
    checkedCircleColor: Color,
    disableColor: Color,
    unCheckedCircleColor: Color,
): Color {
    val color =
        if (!isClickable) {
            disableColor
        } else if (switchON.value) {
            checkedCircleColor
        } else {
            unCheckedCircleColor
        }
    return color
}

data class CustomSwitchParams(
    //
    val width: Dp = 36.dp,
    val height: Dp = 20.dp,
)

@Composable
fun EmptyBottomNavState(resId: Int) {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Image(
            painter = painterResource(id = resId),
            contentDescription = null,
        )
        OABody3TextView(
            modifier = Modifier.padding(all = 16.dp),
            textAlign = TextAlign.Center,
            text =
                stringResource(
                    R.string.selected_feature_not_supported_for_this_vehicle,
                ),
            color = AppTheme.colors.tertiary05,
        )
    }
}

@Composable
fun ComposableGoogleMap(
    vehicleLocation: LatLng?,
    currentLocation: LatLng?,
    vehicleLocationMarker: BitmapDescriptor?,
    currentLocationMarker: BitmapDescriptor?,
    cameraPositionState: CameraPositionState,
    context: Context,
) {
    val mapUiSettings by remember {
        mutableStateOf(
            MapUiSettings(
                zoomControlsEnabled = false,
                zoomGesturesEnabled = true,
            ),
        )
    }

    val mapProperties by remember {
        mutableStateOf(
            if (AppTheme.darkMode.value) {
                MapProperties(
                    mapStyleOptions =
                        MapStyleOptions.loadRawResourceStyle(
                            context,
                            R.raw.find_map_dark_theme,
                        ),
                )
            } else {
                MapProperties()
            },
        )
    }

    GoogleMap(
        modifier = Modifier.wrapContentHeight(),
        cameraPositionState = cameraPositionState,
        uiSettings = mapUiSettings,
        properties = mapProperties,
    ) {
        vehicleLocation?.let {
            Marker(
                state = MarkerState(position = it),
                icon = vehicleLocationMarker,
                title = stringResource(R.string.find_marker_vehicle_position_title),
            )
        }

        currentLocation?.let {
            Marker(
                state = MarkerState(position = it),
                icon = currentLocationMarker,
                title = stringResource(R.string.find_marker_current_location_title),
            )
        }
    }
}

@Composable
fun OASearchBar(
    modifier: Modifier = Modifier,
    onSearch: (String) -> Unit,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    placeHolder: String,
    trailingIcon: @Composable (() -> Unit)? = null,
    colors: TextFieldColors =
        TextFieldDefaults.textFieldColors(
            textColor = AppTheme.colors.tertiary05,
            backgroundColor = AppTheme.colors.tertiary10,
            cursorColor = AppTheme.colors.tertiary05,
            unfocusedIndicatorColor = Color.Transparent,
            focusedIndicatorColor = Color.Transparent,
        ),
    textInput: MutableState<String>,
) {
    val focusManager = LocalFocusManager.current

    TextField(
        keyboardOptions =
            KeyboardOptions(
                capitalization = KeyboardCapitalization.None,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Search,
            ),
        keyboardActions =
            KeyboardActions(
                onSearch = {
                    onSearch(textInput.value)
                    focusManager.clearFocus()
                },
            ),
        interactionSource = interactionSource,
        value = textInput.value,
        onValueChange = { newText ->
            textInput.value = newText
        },
        shape = RoundedCornerShape(30.dp),
        singleLine = true,
        placeholder = {
            OABody3ResizableTextView(
                text = placeHolder,
                color = AppTheme.colors.tertiary05,
                maxLines = 1,
            )
        },
        leadingIcon = {
            Image(
                painter = painterResource(id = R.drawable.ic_search),
                contentDescription = stringResource(id = R.string.search),
                colorFilter = ColorFilter.tint(AppTheme.colors.tertiary05),
                modifier =
                    Modifier
                        .size(24.dp),
            )
        },
        trailingIcon = trailingIcon,
        colors = colors,
        textStyle = AppTheme.fontStyles.body3,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(vertical = 4.dp),
    )
}

@Composable
fun BatteryIcon(
    chargePercentage: Int,
    isCharging: Boolean,
    isPluggedIn: Boolean,
    contentDescription: String,
    testTagId: String,
    modifier: Modifier = Modifier,
) {
    val batteryIcon =
        when {
            isCharging -> {
                R.drawable.ic_ev_charging
            }

            isPluggedIn -> {
                R.drawable.ic_ev_charge_plugged
            }

            chargePercentage in 40..90 -> {
                R.drawable.ic_ev_partial_charging
            }

            chargePercentage in 10..40 -> {
                R.drawable.ic_ev_low_charging
            }

            chargePercentage < 10 -> {
                R.drawable.ic_ev_battery_empty
            }

            else -> {
                R.drawable.ic_ev_full_charge
            }
        }

    Icon(
        painter = painterResource(batteryIcon),
        tint = AppTheme.colors.tertiary00,
        contentDescription = contentDescription,
        modifier = modifier.testTagID(testTagId),
    )
}

@OptIn(ExperimentalComposeUiApi::class)
fun Modifier.testTagID(tag: String) =
    semantics(
        properties = {
            testTag = tag
            testTagsAsResourceId = true
        },
    )

/**
 * Chat bubble popup for use with DynamicVinComponent
 *
 * After the user taps on the DynamicVinComponent,
 * the Y position of the VIN is passed with a callback
 * from the DynamicVinComponent,
 * which is then passed to this component from the parent
 * using a MutableStateFlow
 *
 * The current implementation makes use of Snackbar.
 * It may be possible to improve this component
 * using the Material3 library Tooltips,
 * but it is not implemented in the code base at this time.
 */
@Composable
fun VinCopiedSnackbar(
    snackbarHostState: SnackbarHostState,
    message: String,
    icon: Int = R.drawable.ic_check_copied_toast,
    vinYCoordinateForSnackbar: MutableStateFlow<Dp>,
    yCoordinateAdjustment: Dp,
    testTagIdPrefix: TestTagIdPrefix,
) {
    val vinCoordinateY = vinYCoordinateForSnackbar.collectAsState()
    val backgroundColor = AppTheme.colors.tertiary03
    val textColor = AppTheme.colors.tertiary15

    SnackbarHost(
        modifier =
            Modifier
                .offset(0.dp, vinCoordinateY.value - yCoordinateAdjustment)
                .wrapContentHeight()
                .fillMaxWidth()
                .background(Color.Transparent),
        hostState = snackbarHostState,
    ) {
        Snackbar(
            modifier =
                Modifier
                    .wrapContentHeight()
                    .fillMaxWidth()
                    .background(Color.Transparent),
            contentColor = Color.Transparent,
            backgroundColor = Color.Transparent,
            elevation = 0.dp,
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .background(Color.Transparent),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Row(
                    modifier =
                        Modifier
                            .align(alignment = Alignment.CenterHorizontally)
                            .width(114.dp)
                            .height(44.dp)
                            .background(
                                color = backgroundColor,
                                shape = RoundedCornerShape(size = 8.dp),
                            )
                            .padding(start = 16.dp, top = 12.dp, end = 16.dp, bottom = 12.dp),
                    horizontalArrangement =
                        Arrangement
                            .spacedBy(
                                2.dp,
                                Alignment.CenterHorizontally,
                            ),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        modifier =
                            Modifier
                                .testTagID(tag = "${testTagIdPrefix}$VIN_COPIED_POPUP_TEXT"),
                        text = message,
                        color = textColor,
                    )
                    Icon(
                        painter = painterResource(id = icon),
                        tint = textColor,
                        contentDescription = null,
                    )
                }
                Icon(
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    painter = painterResource(id = R.drawable.ic_copied_snackbar_caret),
                    tint = backgroundColor,
                    contentDescription = null,
                )
            }
        }
    }
}

@Composable
fun ShowSuccessSnackBar(
    snackBarHostState: SnackbarHostState,
    message: String,
    messageFromResource: Int = 0,
    icon: Int = R.drawable.ic_toast_check,
) {
    Box {
        SnackbarHost(
            modifier =
                Modifier
                    .align(Alignment.TopStart)
                    .padding(top = 8.dp, bottom = 8.dp, start = 16.dp, end = 16.dp),
            hostState = snackBarHostState,
        ) {
            Snackbar {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier =
                        Modifier.padding(
                            top = 6.dp,
                            bottom = 6.dp,
                        ),
                ) {
                    Box(
                        modifier =
                            Modifier
                                .size(48.dp)
                                .background(
                                    color = AppTheme.colors.success01,
                                    shape = CircleShape,
                                ),
                        contentAlignment = Alignment.Center,
                    ) {
                        Icon(
                            painterResource(id = icon),
                            modifier =
                                Modifier
                                    .size(32.dp)
                                    .padding(3.dp),
                            contentDescription = null,
                        )
                    }
                    OABody3TextView(
                        color = Color.White,
                        text =
                            if (messageFromResource != 0) {
                                stringResource(
                                    id = messageFromResource,
                                )
                            } else {
                                message
                            },
                        modifier =
                            Modifier
                                .align(Alignment.CenterVertically)
                                .padding(start = 12.dp),
                    )
                }
            }
        }
    }
}

@Composable
fun LoadVehicleImage(
    url: String,
    accessibilityId: String,
    contentDescription: Int,
    modifier: Modifier = Modifier,
    width: Dp = 317.dp,
    height: Dp = 160.dp,
    showImageOnSmallDevices: Boolean = false,
    isDashboardImage: Boolean = false,
) {
    val screenHeightDp = LocalConfiguration.current.screenHeightDp.dp
    var newHeight =
        when {
            screenHeightDp > 707.dp -> height
            screenHeightDp in 609.dp..707.dp -> (height / 1.6f)
            screenHeightDp in 520.dp..609.dp -> (height / 2f)
            else -> (height / 3f)
        }
    var newWidth = width

    if (isDashboardImage && screenHeightDp < 609.dp) {
        newHeight = 0.dp
        newWidth = 0.dp
    }

    val newModifier =
        modifier
            .height(newHeight)
            .width(newWidth)
    Box(
        contentAlignment = Alignment.Center,
        modifier = newModifier,
    ) {
        if (url.contains(Constants.ImageNotFound, ignoreCase = true)) {
            VehicleImageNotFoundWithText(contentDescription)
        } else {
            SubcomposeAsyncImage(
                model = url,
                contentDescription = stringResource(contentDescription),
            ) {
                when (painter.state) {
                    is AsyncImagePainter.State.Loading -> {
                        // SetImage is added to fix the AsyncImagePainter State update issue.
                        // In Some case when the url don't have permission to load, the state is not updated from loading to error.
                        // so, when the invalid painter is rendered the state is updated to error.
                        SetImage(
                            painter = painter,
                            accessibilityId = accessibilityId,
                            contentDescription = contentDescription,
                        )
                        Box(contentAlignment = Alignment.Center) {
                            CircleLoading(indicatorSize = 50.dp)
                        }
                    }

                    is AsyncImagePainter.State.Error -> {
                        VehicleImageNotFoundWithText(contentDescription)
                    }

                    else -> {
                        if (showImageOnSmallDevices) {
                            SetImageForVehicleSwitcher(
                                painter = painter,
                                accessibilityId = accessibilityId,
                                contentDescription = contentDescription,
                            )
                        } else {
                            SetImage(
                                painter = painter,
                                accessibilityId = accessibilityId,
                                contentDescription = contentDescription,
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AddSpacingAsPerDeviceHeight(defaultSpacing: Dp) {
    val screenHeightDp = LocalConfiguration.current.screenHeightDp.dp
    val spacing =
        when {
            screenHeightDp > 707.dp -> defaultSpacing
            else -> (defaultSpacing / 4.5f)
        }
    Spacer(modifier = Modifier.height(spacing))
}

@Composable
fun GetButtonHeightAsPerDeviceHeight(defaultSpacing: Dp): Dp {
    val screenHeightDp = LocalConfiguration.current.screenHeightDp.dp
    val spacing =
        when {
            screenHeightDp > 707.dp -> defaultSpacing
            else -> 40.dp
        }
    return spacing
}

@Composable
fun VehicleImageNotFoundWithText(contentDescription: Int) {
    Box {
        Image(
            modifier =
                Modifier
                    .fillMaxSize()
                    .testTagID(AccessibilityId.ID_NO_VEHICLE_IMAGE),
            painter = painterResource(R.drawable.ic_image_not_found),
            contentDescription = stringResource(contentDescription),
        )
        Box(
            modifier =
                Modifier
                    .clip(shape = RoundedCornerShape(8.dp))
                    .align(Alignment.Center)
                    .background(color = AppTheme.colors.noImageBGColor),
        ) {
            OACaption2TextView(
                text = stringResource(id = R.string.noImageFound),
                textAlign = TextAlign.Center,
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.padding(horizontal = 6.27.dp, vertical = 5.dp),
            )
        }
    }
}

@Composable
fun VehicleImageNotFound(contentDescription: Int) {
    Box {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(R.drawable.ic_image_not_found),
            contentDescription = stringResource(contentDescription),
        )
    }
}

@Preview(showBackground = true, widthDp = 317, heightDp = 150)
@Composable
fun PreviewVehicleImageNotFoundWithText() {
    VehicleImageNotFoundWithText(contentDescription = R.string.image_not_found)
}

@Preview(showBackground = true, widthDp = 317, heightDp = 150)
@Composable
fun PreviewVehicleImageNotFound() {
    VehicleImageNotFound(contentDescription = R.string.image_not_found)
}

@Composable
private fun SetImage(
    painter: AsyncImagePainter,
    accessibilityId: String,
    contentDescription: Int,
) {
    val configuration = LocalConfiguration.current
    val screenHeightDp = configuration.screenHeightDp.dp
    val defaultHeight = 160.dp
    val defaultWidth = 275.dp
    val imageHeight =
        when {
            screenHeightDp > 707.dp -> defaultHeight
            screenHeightDp in 609.dp..707.dp -> (defaultHeight / 1.6f)
            screenHeightDp in 520.dp..609.dp -> (defaultHeight / 2f)
            else -> 0.dp
        }
    val imageWidth =
        when {
            screenHeightDp > 707.dp -> defaultWidth
            screenHeightDp in 609.dp..707.dp -> (defaultWidth / 1.6f)
            screenHeightDp in 520.dp..609.dp -> (defaultWidth / 2f)
            else -> 0.dp
        }
    if (imageHeight > 0.dp) {
        Image(
            painter = painter,
            contentDescription = stringResource(contentDescription),
            contentScale = ContentScale.FillWidth,
            modifier =
                Modifier
                    .width(imageWidth)
                    .height(imageHeight)
                    .testTagID(accessibilityId),
        )
    }
}

@Composable
private fun SetImageForVehicleSwitcher(
    painter: AsyncImagePainter,
    accessibilityId: String,
    contentDescription: Int,
) {
    val configuration = LocalConfiguration.current
    val screenHeightDp = configuration.screenHeightDp.dp
    val defaultHeight = 160.dp
    val defaultWidth = 275.dp
    val imageHeight =
        when {
            screenHeightDp > 707.dp -> defaultHeight
            screenHeightDp in 609.dp..707.dp -> (defaultHeight / 1.6f)
            screenHeightDp in 520.dp..609.dp -> (defaultHeight / 2f)
            else -> 0.dp
        }
    val imageWidth =
        when {
            screenHeightDp > 707.dp -> defaultWidth
            screenHeightDp in 609.dp..707.dp -> (defaultWidth / 1.6f)
            screenHeightDp in 520.dp..609.dp -> (defaultWidth / 2f)
            else -> 0.dp
        }
    if (imageHeight > 0.dp) {
        Image(
            painter = painter,
            contentDescription = stringResource(contentDescription),
            contentScale = ContentScale.FillWidth,
            modifier =
                Modifier
                    .width(imageWidth)
                    .height(imageHeight)
                    .testTagID(accessibilityId),
        )
    }
}

@Composable
fun LinearProgressWithShimmerIndicator(
    modifier: Modifier = Modifier,
    progress: Float,
    progressColor: Color,
    backgroundColor: Color = AppTheme.colors.tertiary10,
    clipShape: Shape = RoundedCornerShape(16.dp),
) {
    Box(
        modifier =
            modifier
                .clip(clipShape)
                .background(backgroundColor)
                .height(10.dp),
    ) {
        Box(
            modifier =
                Modifier
                    .clip(clipShape)
                    .background(progressShimmer(progressColor))
                    .remoteShimmerEffect(progressColor)
                    .fillMaxHeight()
                    .fillMaxWidth(progress),
        )
    }
}

@Composable
private fun progressShimmer(progressColor: Color): Brush {
    val shimmerColors =
        listOf(
            progressColor.copy(alpha = 0.3f),
            progressColor.copy(alpha = 0.5f),
            Color.White.copy(alpha = 1.0f),
            progressColor.copy(alpha = 0.5f),
            progressColor.copy(alpha = 0.3f),
        )

    return Brush.linearGradient(
        colors = shimmerColors,
        start = Offset(x = 100f, y = 0.0f),
        end = Offset(x = 400f, y = 270f),
    )
}

@Composable
fun Modifier.remoteShimmerEffect(backgroundColor: Color): Modifier =
    composed {
        var size by remember {
            mutableStateOf(IntSize.Zero)
        }
        val transition = rememberInfiniteTransition()
        val startOffsetX by transition.animateFloat(
            initialValue = -2 * size.width.toFloat(),
            targetValue = 2 * size.width.toFloat(),
            animationSpec =
                infiniteRepeatable(
                    animation = tween(1500),
                ),
        )

        background(
            brush =
                Brush.linearGradient(
                    colors =
                        listOf(
                            backgroundColor,
                            Color(0xFFB8B5B5),
                            backgroundColor,
                        ),
                    start = Offset(startOffsetX, 0f),
                    end = Offset(startOffsetX + size.width.toFloat(), size.height.toFloat()),
                ),
        ).onGloballyPositioned {
            size = it.size
        }
    }

@Composable
fun ShowProgressIndicator(dialogState: Boolean) {
    if (dialogState) {
        Dialog(
            onDismissRequest = { },
            DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false),
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier =
                    Modifier
                        .size(100.dp)
                        .background(Color.Transparent, shape = RoundedCornerShape(12.dp)),
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center),
                    color = AppTheme.colors.brand01,
                )
            }
        }
    }
}

@Composable
fun ExpandableCard(
    title: String,
    modifier: Modifier = Modifier,
    isDefaultExpanded: Boolean = false,
    changeCardBgColor: Boolean = true,
    onTileClicked: (Boolean) -> Unit = {},
    expandedLayout: @Composable () -> Unit,
) {
    var isExpanded by remember { mutableStateOf(isDefaultExpanded) }

    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .clip(shape = RoundedCornerShape(6.dp))
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() },
                    onClick = {
                        isExpanded = !isExpanded
                        onTileClicked(isExpanded)
                    },
                ),
    ) {
        Column(
            modifier =
                Modifier
                    .animateContentSize(),
        ) {
            Row(
                modifier =
                    Modifier.background(
                        if (isExpanded && changeCardBgColor) {
                            AppTheme.colors.tertiary15
                        } else {
                            AppTheme.colors.tile05
                        },
                    ),
            ) {
                OABody4TextView(
                    text = title,
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .padding(16.dp),
                )

                Spacer(Modifier.weight(1f))

                Icon(
                    painter =
                        painterResource(
                            id = if (isExpanded) R.drawable.ic_minus else R.drawable.ic_plus,
                        ),
                    contentDescription = null,
                    modifier =
                        Modifier
                            .size(44.dp)
                            .padding(16.dp)
                            .align(Alignment.CenterVertically),
                    tint = AppTheme.colors.tertiary05,
                )
            }

            if (isExpanded) {
                expandedLayout()
            }
        }
    }
}

@Composable
fun OAAlertDialog(
    data: DialogData,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier
            .padding()
            .fillMaxWidth()
            .background(AppTheme.colors.tertiary12),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        data.header?.let {
            Spacer(modifier = Modifier.height(4.dp))
            it.invoke()
        } ?: run {
            Spacer(modifier = Modifier.height(16.dp))
        }

        Surface(
            shape = CircleShape,
            color = data.imageBgColor,
            modifier =
                Modifier
                    .size(72.dp)
                    .padding(12.dp),
        ) {
            Image(
                modifier =
                    Modifier
                        .padding(12.dp),
                painter = painterResource(data.imageId),
                contentDescription =
                    stringResource(
                        R.string.show_dialog_icon_content_description,
                    ),
            )
        }
        OASubHeadLine1TextView(
            text = data.title,
            color = AppTheme.colors.tertiary03,
            modifier = Modifier.padding(bottom = 8.dp),
        )
        data.subtitle?.let { subtitle ->
            OACallOut1TextView(
                text = subtitle,
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Center,
                modifier =
                    Modifier
                        .padding(start = 32.dp, bottom = 20.dp, end = 32.dp)
                        .fillMaxWidth(),
            )
            Spacer(modifier = Modifier.height(32.h()))
        }
        data.annotatedView?.let {
            it.invoke()
            Spacer(modifier = Modifier.height(32.h()))
        }
        data.secondaryButtonText?.let { subtitle ->
            OACallOut1TextView(
                text = subtitle,
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Center,
                modifier =
                    Modifier
                        .padding(bottom = 20.dp)
                        .clickable {
                            data.secondaryOnClick?.let { it() }
                        },
            )
        }
        data.primaryButtonText?.let { primaryButtonText ->
            OAButton(
                text = primaryButtonText,
                modifier = Modifier.size(width = 192.w(), height = 52.h()),
                textModifier = Modifier.padding(all = 0.dp),
                click = {
                    data.primaryOnClick()
                },
            )
        }
        Spacer(modifier = Modifier.height(26.dp))
    }
}

@Composable
fun OAAppBar(
    title: String,
    testTagId: String,
    modifier: Modifier = Modifier,
    actionWidget: @Composable () -> Unit = { },
    onBack: () -> Unit,
) {
    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Box(
            modifier =
                Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(AppTheme.colors.button02d)
                    .clickable {
                        onBack()
                    }
                    .testTagID(testTagId),
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_back_arrow),
                contentDescription = stringResource(id = R.string.Common_back),
                modifier =
                    Modifier
                        .padding(
                            start = 19.dp,
                            end = 22.dp,
                            top = 17.dp,
                            bottom = 17.dp,
                        ),
                tint = AppTheme.colors.button02a,
            )
        }

        OASubHeadLine3TextView(
            text = title,
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier
                    .padding(top = 6.dp)
                    .align(Alignment.TopCenter),
        )

        Box(
            modifier =
                Modifier
                    .wrapContentSize()
                    .align(Alignment.CenterEnd),
        ) {
            actionWidget()
        }
    }
}

@Composable
fun OADateLayout(
    dateModel: DateModel,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        OATabLabel01TextView(
            text = dateModel.month.uppercase(),
            color = AppTheme.colors.button03a,
        )

        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(3.dp)
                    .background(
                        color = AppTheme.colors.tertiary15,
                        shape =
                            RoundedCornerShape(
                                bottomStart = 10.dp,
                                bottomEnd = 10.dp,
                            ),
                    ),
        ) {
            OASubHeadLine4TextView(
                text = dateModel.day,
                color = AppTheme.colors.tertiary03,
            )
        }
    }

    Column(
        modifier =
            Modifier
                .fillMaxWidth(),
    ) {
        Spacer(modifier = Modifier.height(8.dp))

        OABody4TextView(
            text = dateModel.date,
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier
                    .align(Alignment.CenterHorizontally),
        )

        Spacer(modifier = Modifier.height(8.dp))
    }
}

@Composable
fun ScreenDynamicHeightComposable(percentage: Float): Dp {
    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp // Get the screen height in dp
    return screenHeight * percentage // Calculate the height based on the given percentage
}

@Composable
fun CurrentLocationRequest(
    onCurrentLocation: (Location) -> Unit,
    onLocationError: () -> Unit = {},
) {
    val context = LocalContext.current
    val fusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(context)
    var requestLocationUpdate by remember { mutableStateOf(true) }

    if (requestLocationUpdate) {
        LocationPermissionsAndSettingDialogs(
            updateCurrentLocation = {
                requestLocationUpdate = false
                LocationUtil.requestLocationResultCallback(fusedLocationProviderClient) { locationResult ->
                    locationResult.lastLocation?.let { location ->
                        onCurrentLocation(location)
                    } ?: run {
                        onLocationError()
                    }
                }
            },
        )
    }
}

@Preview(name = "NEXUS_7", device = Devices.NEXUS_7, showBackground = true)
@Preview(name = "NEXUS_7_2013", device = Devices.NEXUS_7_2013, showBackground = true)
@Preview(name = "NEXUS_5", device = Devices.NEXUS_5, showBackground = true)
@Preview(name = "NEXUS_6", device = Devices.NEXUS_6, showBackground = true)
@Preview(name = "NEXUS_9", device = Devices.NEXUS_9, showBackground = true)
@Preview(name = "NEXUS_10", device = Devices.NEXUS_10, showBackground = true)
@Preview(name = "NEXUS_5X", device = Devices.NEXUS_5X, showBackground = true)
@Preview(name = "NEXUS_6P", device = Devices.NEXUS_6P, showBackground = true)
@Preview(name = "PIXEL_C", device = Devices.PIXEL_C, showBackground = true)
@Preview(name = "PIXEL", device = Devices.PIXEL, showBackground = true)
@Preview(name = "PIXEL_XL", device = Devices.PIXEL_XL, showBackground = true)
@Preview(name = "PIXEL_2", device = Devices.PIXEL_2, showBackground = true)
@Preview(name = "PIXEL_2_XL", device = Devices.PIXEL_2_XL, showBackground = true)
@Preview(name = "PIXEL_3", device = Devices.PIXEL_3, showBackground = true)
@Preview(name = "PIXEL_3_XL", device = Devices.PIXEL_3_XL, showBackground = true)
@Preview(name = "PIXEL_3A", device = Devices.PIXEL_3A, showBackground = true)
@Preview(name = "PIXEL_3A_XL", device = Devices.PIXEL_3A_XL, showBackground = true)
@Preview(name = "PIXEL_4", device = Devices.PIXEL_4, showBackground = true)
@Preview(name = "PIXEL_4_XL", device = Devices.PIXEL_4_XL, showBackground = true)
@Composable
fun TestPreview() {
    // Test your compose function here
}
