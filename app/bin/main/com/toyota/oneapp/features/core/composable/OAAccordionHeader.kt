/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.core.composable

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.Constants.PLACEHOLDER_SECONDARY_TEXT

@Composable
fun OAAccordionHeader(
    text: String,
    color: Color = AppTheme.colors.tertiary03,
    textAlign: TextAlign = TextAlign.Left,
    modifier: Modifier = Modifier,
    isSelected: Boolean = false,
    showIcon: Boolean = true,
    leadingIcon: Int? = null,
    secondaryText: String? = null,
) {
    Surface(
        modifier = modifier.then(Modifier.fillMaxWidth()),
        shape = RoundedCornerShape(8.dp),
        color = AppTheme.colors.tertiary12,
    ) {
        Row(
            verticalAlignment = Alignment.Top,
            modifier =
                Modifier
                    .padding(start = 6.dp, top = 18.dp, end = 16.dp, bottom = 18.dp),
        ) {
            leadingIcon?.let {
                Image(
                    painter = painterResource(it),
                    contentDescription = null,
                )
            }
            Column(
                modifier =
                    Modifier
                        .weight(1f)
                        .padding(start = 10.dp),
            ) {
                OABody4TextView(
                    text = text,
                    color = color,
                    textAlign = textAlign,
                )
                secondaryText?.let {
                    OABody4TextView(
                        text = it,
                        color = color.copy(alpha = 0.7f),
                        textAlign = textAlign,
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }
            }
            if (showIcon && isSelected) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_toast_check),
                    contentDescription = null,
                    tint = AppTheme.colors.success01,
                )
            }
        }
    }
}

@Preview(
    name = "OAAccordionHeader Preview",
    showBackground = true,
)
@Composable
fun OAAccordionHeaderPreview() {
    OAAccordionHeader(
        leadingIcon = R.string.level_2,
        text = "Accordion Header Example",
        color = AppTheme.colors.tertiary03,
        isSelected = true,
        secondaryText = PLACEHOLDER_SECONDARY_TEXT,
    )
}

@Preview(
    name = "OAAccordionHeader Not Selected Preview",
    showBackground = true,
)
@Composable
fun OAAccordionHeaderNotSelectedPreview() {
    OAAccordionHeader(
        text = "Accordion Header Example (Not Selected)",
        color = AppTheme.colors.tertiary03,
        isSelected = true,
        secondaryText = "Secondary text example",
        showIcon = true,
    )
}

@Preview(
    name = "OAAccordionHeader No Icon Preview",
    showBackground = true,
)
@Composable
fun OAAccordionHeaderNoIconPreview() {
    OAAccordionHeader(
        text = "Accordion Header Example (No Icon)",
        color = AppTheme.colors.tertiary03,
        showIcon = false,
        secondaryText = PLACEHOLDER_SECONDARY_TEXT,
    )
}
