/*
Copyright © 2024. Toyota Motors North America Inc
All rights reserved.
*/

@file:OptIn(ExperimentalMaterialApi::class)

package com.toyota.oneapp.features.core.util

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardBottomSheetScreen
import com.toyota.oneapp.features.dashboard.util.DashboardConstants
import com.toyota.oneapp.features.dealerservice.util.DealerServiceConstants
import com.toyota.oneapp.features.find.utils.FindConstants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

data class LocalBottomSheetState(
    val primarySheetContent: MutableState<@Composable () -> Unit>,
    val secondarySheetContent: MutableState<@Composable () -> Unit>,
    val primarySheetState: MutableState<ModalBottomSheetState>,
    val secondarySheetState: MutableState<ModalBottomSheetState>,
    val primarySheetShape: MutableState<Shape>,
    val secondarySheetShape: MutableState<Shape>,
)

val LocalBottomSheet =
    staticCompositionLocalOf<LocalBottomSheetState> {
        error("No BottomSheetState provided")
    }

@OptIn(ExperimentalMaterialApi::class)
fun CoroutineScope.launchPrimaryBottomSheetAction(
    bottomSheet: LocalBottomSheetState,
    screen_id: String = DashboardConstants.NA,
    screen: @Composable (ModalBottomSheetState) -> Unit,
) {
    launch {
        bottomSheet.primarySheetContent.value = {
            screen(bottomSheet.primarySheetState.value)
        }

        when (screen_id) {
            DashboardConstants.ID_REMOTE_STATES_BOTTOM_SHEET,
            DealerServiceConstants.ID_DEALER_APPOINTMENT_ODOMETER_BOTTOM_SHEET,
            -> {
                bottomSheet.primarySheetShape.value =
                    RoundedCornerShape(
                        topStart = 16.dp,
                        topEnd = 16.dp,
                    )
            }
            DashboardConstants.ID_VEHICLE_HEALTH_STATES_BOTTOM_SHEET,
            DashboardConstants.ID_LINK_ACCOUNT,
            FindConstants.ID_DRIVE_PULSE_SUNSET_BOTTOM_SHEET,
            DashboardConstants.ID_ANNOUNCEMENT_BOTTOM_SHEET,
            DealerServiceConstants.ID_DEALER_SERVICE_MILEAGE_BOTTOM_SHEET,
            -> {
                bottomSheet.primarySheetShape.value =
                    RoundedCornerShape(
                        topStart = 30.dp,
                        topEnd = 30.dp,
                    )
            }
        }

        if (bottomSheet.primarySheetState.value.isVisible) {
            bottomSheet.primarySheetState.value.hide()
        } else {
            bottomSheet.primarySheetState.value.show()
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
fun CoroutineScope.launchSecondaryBottomSheetAction(
    bottomSheet: LocalBottomSheetState,
    screen_id: String = DashboardConstants.NA,
    screen: @Composable (ModalBottomSheetState) -> Unit,
) {
    launch {
        bottomSheet.secondarySheetContent.value = {
            screen(bottomSheet.secondarySheetState.value)
        }

        when (screen_id) {
            DashboardConstants.ID_ANNOUNCEMENT_BOTTOM_SHEET -> {
                bottomSheet.secondarySheetShape.value =
                    RoundedCornerShape(
                        topStart = 30.dp,
                        topEnd = 30.dp,
                    )
            }
        }

        if (bottomSheet.secondarySheetState.value.isVisible) {
            bottomSheet.secondarySheetState.value.hide()
        } else {
            bottomSheet.secondarySheetState.value.show()
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ModalBottomSheets(content: @Composable () -> Unit) {
    val modalBottomSheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            skipHalfExpanded = true,
        )

    val customSheetContentState =
        remember {
            mutableStateOf<@Composable () -> Unit>(
                { OADashboardBottomSheetScreen(modalBottomSheetState) },
            )
        }

    val secondaryModalBottomSheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            skipHalfExpanded = true,
        )

    val secondaryCustomSheetContentState =
        remember {
            mutableStateOf<@Composable () -> Unit>(
                { OADashboardBottomSheetScreen(secondaryModalBottomSheetState) },
            )
        }

    val bottomSheetState =
        remember {
            LocalBottomSheetState(
                primarySheetContent = customSheetContentState,
                secondarySheetContent = secondaryCustomSheetContentState,
                primarySheetState = mutableStateOf(modalBottomSheetState),
                secondarySheetState = mutableStateOf(secondaryModalBottomSheetState),
                primarySheetShape = mutableStateOf(RoundedCornerShape(0.dp)),
                secondarySheetShape = mutableStateOf(RoundedCornerShape(0.dp)),
            )
        }

    CompositionLocalProvider(
        LocalBottomSheet provides bottomSheetState,
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            ModalBottomSheetLayout(
                sheetContent = { bottomSheetState.secondarySheetContent.value() },
                sheetState = bottomSheetState.secondarySheetState.value,
                sheetShape = bottomSheetState.secondarySheetShape.value,
            ) {
                ModalBottomSheetLayout(
                    sheetContent = { bottomSheetState.primarySheetContent.value() },
                    sheetState = bottomSheetState.primarySheetState.value,
                    sheetShape = bottomSheetState.primarySheetShape.value,
                ) {
                    content()
                }
            }
        }
    }
}
