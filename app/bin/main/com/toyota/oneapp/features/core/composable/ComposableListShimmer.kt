/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.composable

import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId

@Composable
fun ComposableListShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Column(
            modifier =
                modifier
                    .background(AppTheme.colors.tertiary12)
                    .fillMaxHeight(),
        ) {
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(8.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(40.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(8.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(40.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(8.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(40.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(8.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(40.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(8.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(40.dp)
                            .shimmerEffect(),
                )
            }
        }
    } else {
        contentAfterLoading()
    }
}

fun Modifier.shimmerEffect(): Modifier =
    composed {
        var size by remember {
            mutableStateOf(IntSize.Zero)
        }
        val transition = rememberInfiniteTransition()
        val startOffsetX by transition.animateFloat(
            initialValue = -2 * size.width.toFloat(),
            targetValue = 2 * size.width.toFloat(),
            animationSpec =
                infiniteRepeatable(
                    animation = tween(2000),
                ),
        )

        background(
            brush =
                Brush.linearGradient(
                    colors =
                        listOf(
                            AppTheme.colors.tertiary10,
                            Color(0xFFB8B5B5),
                            AppTheme.colors.tertiary10,
                        ),
                    start = Offset(startOffsetX, 0f),
                    end = Offset(startOffsetX + size.width.toFloat(), size.height.toFloat()),
                ),
        )
            .onGloballyPositioned {
                size = it.size
            }
    }

@Composable
fun CommonCardShimmer(modifier: Modifier = Modifier) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .height(72.dp)
                .clip(RoundedCornerShape(8.dp))
                .shimmerEffect(),
    ) { }
}

@Composable
fun FuelWidgetComposableShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Column(
            modifier =
                modifier
                    .background(AppTheme.colors.tertiary12),
        ) {
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(start = 15.dp, end = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(65.dp)
                            .shimmerEffect(),
                )
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun GloveBoxGridComposableShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Column(
            modifier =
                modifier
                    .fillMaxSize()
                    .background(AppTheme.colors.tertiary12),
        ) {
            Spacer(modifier = Modifier.height(8.dp))

            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                contentPadding =
                    PaddingValues(
                        horizontal = 12.dp,
                        vertical = 8.dp,
                    ),
            ) {
                val list = (1..4).map { it.toString() }
                items(list.size) { index ->
                    Box(
                        modifier =
                            Modifier
                                .padding(4.dp),
                    ) {
                        Card(
                            shape = RoundedCornerShape(8.dp),
                            backgroundColor = AppTheme.colors.tile03,
                            elevation = 4.dp,
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .height(169.dp),
                        ) {
                            Box(
                                modifier =
                                    Modifier
                                        .fillMaxSize()
                                        .shimmerEffect(),
                            )
                        }
                    }
                }
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun GloveBoxListComposableShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        val list = (1..6).map { it.toString() }
        LazyColumn(
            modifier =
                modifier
                    .fillMaxSize(),
        ) {
            items(list) {
                Column(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .wrapContentHeight()
                            .padding(bottom = 16.dp),
                ) {
                    Box(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(16.dp)
                                .shimmerEffect(),
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Box(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(24.dp)
                                .shimmerEffect(),
                    )
                }
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun ShopComposableShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    isFindPage: Boolean = false,
) {
    if (isLoading) {
        Column(
            modifier =
                modifier
                    .background(AppTheme.colors.tertiary12)
                    .fillMaxHeight()
                    .testTagID(AccessibilityId.ID_SHOP_SHIMMER_LOADING_ID),
        ) {
            val topPadding = if (isFindPage) 96 else 48
            Spacer(modifier = Modifier.height(topPadding.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(3.dp),
                modifier = Modifier.padding(start = 25.dp, end = 125.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(35.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(12.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(8.dp),
                modifier = Modifier.padding(start = 25.dp, end = 39.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(212.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(26.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun FindMapShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Column(
            modifier =
                modifier
                    .background(AppTheme.colors.tertiary12)
                    .fillMaxHeight(),
        ) {
            Spacer(modifier = Modifier.height(96.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(3.dp),
                modifier = Modifier.padding(start = 25.dp, end = 125.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(35.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(12.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(8.dp),
                modifier = Modifier.padding(start = 25.dp, end = 39.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(212.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(26.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun FindListShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Column(
            modifier =
                modifier
                    .background(AppTheme.colors.tertiary12)
                    .fillMaxHeight(),
        ) {
            Spacer(modifier = Modifier.height(26.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun ServiceComposableShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
) {
    if (isLoading) {
        Column(
            modifier =
                Modifier
                    .background(AppTheme.colors.tertiary12)
                    .fillMaxHeight(),
        ) {
            Spacer(modifier = Modifier.height(27.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(3.dp),
                modifier = Modifier.padding(start = 25.dp, end = 125.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(35.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(12.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(8.dp),
                modifier = Modifier.padding(start = 25.dp, end = 39.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(212.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(51.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(3.dp),
                modifier = Modifier.padding(start = 25.dp, end = 125.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(35.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(12.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(8.dp),
                modifier = Modifier.padding(start = 25.dp, end = 39.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(212.dp)
                            .shimmerEffect(),
                )
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun PayComposableShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Column(
            modifier =
                modifier
                    .background(AppTheme.colors.tertiary12)
                    .fillMaxHeight()
                    .padding(top = 20.dp, bottom = 20.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .width(225.dp)
                        .height(35.dp)
                        .padding(horizontal = 16.dp)
                        .clip(shape = RoundedCornerShape(3.dp))
                        .shimmerEffect(),
            )
            Spacer(modifier = Modifier.height(12.dp))
            Card(
                elevation = 0.dp,
                shape = RoundedCornerShape(8.dp),
                modifier =
                    Modifier
                        .padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(212.dp)
                            .shimmerEffect(),
                )
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun TFSTagShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Card(
            elevation = 0.dp,
            shape = RoundedCornerShape(20.dp),
            modifier =
                Modifier
                    .padding(horizontal = 16.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .width(150.dp)
                        .height(35.dp)
                        .shimmerEffectLoader(),
            )
        }
    } else {
        contentAfterLoading()
    }
}

fun Modifier.shimmerEffectLoader(): Modifier =
    composed {
        var size by remember {
            mutableStateOf(IntSize.Zero)
        }
        val transition = rememberInfiniteTransition()
        val startOffsetX by transition.animateFloat(
            initialValue = -2 * size.width.toFloat(),
            targetValue = 2 * size.width.toFloat(),
            animationSpec =
                infiniteRepeatable(
                    animation = tween(2000),
                ),
        )

        background(
            brush =
                Brush.linearGradient(
                    colors =
                        listOf(
                            AppTheme.colors.tertiary10,
                            AppTheme.colors.tertiary10,
                        ),
                    start = Offset(startOffsetX, 0f),
                    end = Offset(startOffsetX + size.width.toFloat(), size.height.toFloat()),
                ),
        )
            .onGloballyPositioned {
                size = it.size
            }
    }

@Composable
fun VehicleInfoComposableShimmer(modifier: Modifier = Modifier) {
    Column(
        modifier =
            modifier
                .background(AppTheme.colors.tertiary12)
                .fillMaxHeight(),
    ) {
        Spacer(modifier = Modifier.height(32.dp))
        Card(
            elevation = 6.dp,
            shape = RoundedCornerShape(3.dp),
            modifier = Modifier.padding(start = 105.dp, end = 105.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(30.dp)
                        .shimmerEffect(),
            )
        }
        Spacer(modifier = Modifier.height(27.dp))
        Card(
            elevation = 6.dp,
            shape = RoundedCornerShape(8.dp),
            modifier = Modifier.padding(start = 105.dp, end = 105.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(90.dp)
                        .shimmerEffect(),
            )
        }
        Spacer(modifier = Modifier.height(30.dp))
        Card(
            elevation = 6.dp,
            shape = RoundedCornerShape(10.dp),
            modifier = Modifier.padding(horizontal = 90.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(35.dp)
                        .shimmerEffect(),
            )
        }
        Spacer(modifier = Modifier.height(30.dp))
        Card(
            elevation = 6.dp,
            shape = RoundedCornerShape(10.dp),
            modifier = Modifier.padding(horizontal = 16.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(72.dp)
                        .shimmerEffect(),
            )
        }
        (0..4).forEach {
            TileMenuItemShimmer()
        }
    }
}

@Composable
fun TileMenuItemShimmer() {
    Spacer(modifier = Modifier.height(8.dp))
    Card(
        elevation = 6.dp,
        shape = RoundedCornerShape(10.dp),
        modifier = Modifier.padding(horizontal = 16.dp),
    ) {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(72.dp)
                    .shimmerEffect(),
        )
    }
}

@Composable
fun DashboardOdometerComposableShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
) {
    if (isLoading) {
        Column(
            modifier =
                Modifier
                    .background(AppTheme.colors.tertiary12)
                    .fillMaxWidth(),
        ) {
            Box(
                modifier =
                    Modifier
                        .width(125.dp)
                        .height(12.dp)
                        .align(Alignment.CenterHorizontally)
                        .clip(shape = RoundedCornerShape(3.dp))
                        .shimmerEffectLoader(),
            )
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun DashboardStatusComposableShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Column(
            modifier =
                modifier
                    .background(AppTheme.colors.tertiary12)
                    .verticalScroll(rememberScrollState()),
        ) {
            Spacer(modifier = Modifier.height(48.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(start = 87.dp, end = 88.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(100.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(75.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun DashboardHealthComposableShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Column(
            modifier =
                modifier
                    .background(AppTheme.colors.tertiary12)
                    .verticalScroll(rememberScrollState()),
        ) {
            Spacer(modifier = Modifier.height(48.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(start = 87.dp, end = 88.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(100.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(75.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun ClimateShimmer(modifier: Modifier = Modifier) {
    Column(
        modifier =
            modifier
                .background(AppTheme.colors.tertiary12)
                .fillMaxHeight(),
    ) {
        Spacer(modifier = Modifier.height(8.dp))
        Card(
            elevation = 6.dp,
            shape = RoundedCornerShape(10.dp),
            modifier = Modifier.padding(start = 16.dp, end = 16.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(130.dp)
                        .shimmerEffect(),
            )
        }
        Spacer(modifier = Modifier.height(18.dp))
        Card(
            elevation = 6.dp,
            shape = RoundedCornerShape(10.dp),
            modifier = Modifier.padding(start = 16.dp, end = 16.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(230.dp)
                        .shimmerEffect(),
            )
        }
        Spacer(modifier = Modifier.height(18.dp))
        Card(
            elevation = 6.dp,
            shape = RoundedCornerShape(10.dp),
            modifier = Modifier.padding(start = 16.dp, end = 16.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(130.dp)
                        .shimmerEffect(),
            )
        }
        Spacer(modifier = Modifier.height(10.dp))
    }
}

@Composable
fun NonConnectedComposableShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Column(
            modifier =
                modifier
                    .background(AppTheme.colors.tertiary12)
                    .fillMaxHeight(),
        ) {
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(start = 16.dp, end = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(start = 16.dp, end = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(start = 16.dp, end = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                elevation = 6.dp,
                shape = RoundedCornerShape(10.dp),
                modifier = Modifier.padding(start = 16.dp, end = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .shimmerEffect(),
                )
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun AppointmentsListShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Box(modifier.fillMaxSize()) {
            Column(modifier = modifier.background(AppTheme.colors.tertiary15)) {
                ShimmerRow(Modifier.padding(horizontal = 10.dp))
                Spacer(modifier = Modifier.height(20.dp))
                repeat(3) {
                    ShimmerAppointmentTile()
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
            Column(
                Modifier
                    .align(Alignment.BottomCenter),
            ) {
                Box(
                    modifier =
                        Modifier
                            .padding(horizontal = 40.dp, vertical = 5.dp)
                            .fillMaxWidth()
                            .height(40.dp)
                            .clip(RoundedCornerShape(18.dp))
                            .shimmerEffect(),
                )
            }
        }
    } else {
        contentAfterLoading()
    }
}

@Composable
fun ShimmerRow(modifier: Modifier = Modifier) {
    Row(modifier = modifier) {
        repeat(2) {
            ShimmerBox(
                modifier =
                    Modifier
                        .weight(1f)
                        .height(35.dp),
            )
            if (it == 0) Spacer(modifier = Modifier.width(8.dp))
        }
    }
}

@Composable
fun ShimmerBox(modifier: Modifier = Modifier) {
    Box(
        modifier =
            modifier
                .clip(RoundedCornerShape(18.dp))
                .shimmerEffect(),
    )
}

@Composable
fun ShimmerAppointmentTile() {
    Row {
        ShimmerBox(
            modifier =
                Modifier
                    .width(50.dp)
                    .height(50.dp)
                    .clip(RoundedCornerShape(10.dp)),
        )
        Spacer(modifier = Modifier.width(8.dp))
        ShimmerBox(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(50.dp)
                    .clip(RoundedCornerShape(10.dp)),
        )
    }
}

@Composable
fun ChargeInfoCommonCardShimmer(
    modifier: Modifier = Modifier,
    elevation: Dp = 4.dp,
    shape: Dp = 8.dp,
) {
    Card(
        backgroundColor = AppTheme.colors.tile03,
        elevation = elevation,
        shape = RoundedCornerShape(shape),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .padding(16.dp),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .weight(1f, fill = false),
            ) {
                Box(
                    modifier =
                        Modifier
                            .size(32.dp)
                            .clip(CircleShape)
                            .shimmerEffect(),
                )

                Spacer(modifier = Modifier.width(16.dp))

                Column {
                    Box(
                        modifier =
                            Modifier
                                .clip(RoundedCornerShape(16.dp))
                                .fillMaxWidth()
                                .height(16.dp)
                                .padding(end = 50.dp)
                                .shimmerEffect(),
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Box(
                        modifier =
                            Modifier
                                .clip(RoundedCornerShape(16.dp))
                                .fillMaxWidth()
                                .height(16.dp)
                                .shimmerEffect(),
                    )
                }
            }
        }
    }
}

@Composable
fun EcoChargingShimmer(
    isLoading: Boolean,
    contentAfterLoading: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (isLoading) {
        Box(
            modifier =
                modifier
                    .fillMaxWidth()
                    .height(20.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .shimmerEffect(),
        )
    } else {
        contentAfterLoading()
    }
}
