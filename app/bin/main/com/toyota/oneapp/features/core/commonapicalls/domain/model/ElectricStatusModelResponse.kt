package com.toyota.oneapp.features.core.commonapicalls.domain.model

import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.fuelwidget.presentation.utils.ChargeStatus

data class ElectricStatusModelResponse(
    val response: ElectricStatusResponse?,
    val plugStatus: Int? = null,
    val remainingChargeTime: Int? = null,
    val evDistanceAC: Double? = null,
    val evDistance: Double? = null,
    val evDistanceUnit: String,
    val chargeRemainingAmount: Long?,
    val realTimeData: Boolean? = true,
    var forceRefresh: Boolean? = false,
)

fun ElectricStatusModelResponse.isNormalCharging(): Boolean = plugStatus == 40

fun ElectricStatusModelResponse.isNormalChargingComplete(): Boolean = plugStatus == 45

fun ElectricStatusModelResponse.isFastCharging(): Boolean = plugStatus == 56

fun ElectricStatusModelResponse.isFastChargingComplete(): Boolean = plugStatus == 60

fun ElectricStatusModelResponse.isCharging(): Boolean = isNormalCharging() || isFastCharging()

fun ElectricStatusModelResponse.isChargingComplete(): Boolean = isNormalChargingComplete() || isFastChargingComplete()

fun ElectricStatusModelResponse.isPlugWaitingForTimerToCharge(): Boolean = plugStatus == 36

fun ElectricStatusModelResponse.isChargingOrWaiting(): Boolean = isCharging() || isPlugWaitingForTimerToCharge()

fun ElectricStatusModelResponse.getChargeStatus(): ChargeStatus =
    when (plugStatus) {
        40 -> ChargeStatus.NORMAL_CHARGING
        45 -> ChargeStatus.NORMAL_CHARGING_COMPLETE
        56 -> ChargeStatus.FAST_CHARGING
        60 -> ChargeStatus.FAST_CHARGING_COMPLETE
        36 -> ChargeStatus.PLUG_WAITING_FOR_TIMER_TO_CHARGE
        else -> ChargeStatus.UNKNOWN
    }
