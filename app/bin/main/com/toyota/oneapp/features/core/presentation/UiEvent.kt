/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.presentation

sealed interface NavigationEvent {
    data class Navigate(val route: String) : NavigationEvent

    object NavigateUp : NavigationEvent
}

sealed interface ProgressEvent {
    object ShowProgress : ProgressEvent

    object DismissProgress : ProgressEvent
}

sealed interface ErrorEvent {
    data class ShowError(val message: String) : ErrorEvent
}

interface UiEvent {
    data class Error(val event: ErrorEvent) : UiEvent

    // Going forward please use NavigationEvent.Navigate
    data class Navigate(val route: String) : UiEvent // Don't use this

    data class Navigation(val event: NavigationEvent) : UiEvent // Please use this

    data class Progress(val event: ProgressEvent) : UiEvent
}

interface ToastUiEvent : UiEvent {
    data class ToastFromString(val message: String?) : UiEvent

    data class ToastFromStringRes(val messageRes: Int) : UiEvent
}
