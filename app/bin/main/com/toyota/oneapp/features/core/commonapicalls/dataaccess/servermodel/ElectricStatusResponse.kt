package com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel

import android.os.Parcelable
import com.toyota.oneapp.features.core.commonapicalls.domain.model.ElectricStatusModelResponse
import kotlinx.parcelize.Parcelize

data class ElectricStatusResponse(
    val payload: Payload,
    val status: Status,
    val timestamp: String,
)

data class Payload(
    val returnCode: String,
    val positionInfo: PositionInfo? = null,
    val vehicleInfo: VehicleInfo? = null,
    val remoteControlResult: RemoteControlResult? = null,
    val realtimeStatusResult: RealtimeStatusResult? = null,
    val vehicleStatusResult: VehicleStatusResult? = null,
)

data class PositionInfo(
    val acquisitionDatetime: String?,
    val longitude: Double = 0.0,
    val latitude: Double = 0.0,
)

data class VehicleInfo(
    val acquisitionDatetime: String? = null,
    val remoteHvacInfo: RemoteHvacInfo? = null,
    val chargeInfo: ChargeInfo? = null,
    val timerChargeInfo: List<TimerChargeInfo>? = null,
    val maxNoOfChargeSchedules: Int? = null,
    val solarPowerGenerationInfo: SolarPowerGenerationInfo? = null,
    val odometerInformation: OdometerInformation? = null,
    val driveMonitorDisplayRange: DriveMonitorDisplayRange? = null,
)

@Parcelize
data class TimerChargeInfo(
    val settingId: String? = null,
    val enabled: Boolean? = null,
    val startTime: String? = null,
    val endTime: String? = null,
    val daysOfTheWeek: List<String>? = null,
    val nextSettingId: String? = null,
) : Parcelable

data class RemoteHvacInfo(
    val remoteHvacMode: Any?,
    val remoteHvacProhibitionSignal: Any?,
    val temperaturelevel: Any?,
    val settingTemperature: Any?,
    val blowerStatus: Any?,
    val frontDefoggerStatus: Long,
    val rearDefoggerStatus: Long,
    val frontSeatHeaterStatus: Any?,
    val rearSeatHeaterStatus: Any?,
    val steeringHeaterStatus: Any?,
    val latestAcStartTime: Any?,
    val temperatureDisplayFlag: Any?,
)

@Parcelize
data class ChargeInfo(
    val plugStatus: Int? = null,
    val plugInHistory: Int? = null,
    val remainingChargeTime: Int? = null,
    val evTravelableDistance: Double? = null,
    val evTravelableDistanceSubtractionRate: Long? = null,
    val evDistanceAC: Double? = null,
    val evDistance: Double? = null,
    val evDistanceUnit: String,
    val chargeRemainingAmount: Long? = null,
    val chargeType: Int? = null,
    val chargeWeek: Int? = null,
    val chargeStartTime: String? = null,
    val chargeEndTime: String? = null,
    val connectorStatus: Long? = null,
    val batteryPowerSupplyPossibleTime: Long? = null,
    val gasolinePowerSupplyPossibleTime: Long? = null,
    val fuelGage: Long? = null,
    val fuelGageStatus: Long? = null,
    val gasolineTravelableDistance: Double? = null,
    val gasolineTravelableDistanceUnit: Double? = null,
) : Parcelable

data class SolarPowerGenerationInfo(
    val solarInfoAvailable: Any?,
    val solarCumulativeEvTravelableDistance: Any?,
    val solarCumulativePowerGeneration: Any?,
    val monthlySolarCumulativePowerGeneration: Any?,
)

data class OdometerInformation(
    val value: String,
    val unit: String,
)

data class DriveMonitorDisplayRange(
    val value: String,
    val unit: String,
)

data class RemoteControlResult(
    val status: Int?,
    val result: Int?,
    val errorCode: Any? = null,
    val acRemainingTime: Any? = null,
    val remoteHvacFactor: Any? = null,
)

data class RealtimeStatusResult(
    val status: Int?,
    val result: Int?,
)

data class VehicleStatusResult(
    val occurrenceDate: String,
    val cautionOverallCount: Long,
    val vehicleStatus: List<VehicleStatu>,
)

data class VehicleStatu(
    val category: String,
    val displayOrder: Long,
    val sections: List<Section>,
)

data class Section(
    val section: String,
    val values: List<Value>,
)

data class Value(
    val value: String,
    val status: Long,
)

data class Status(
    val messages: List<Message>,
)

data class Message(
    val responseCode: String,
    val description: String,
    val detailedDescription: String,
)

fun ElectricStatusResponse.toUIModel(
    realTimeData: Boolean? = true,
    forceRefresh: Boolean? = false,
): ElectricStatusModelResponse =
    ElectricStatusModelResponse(
        response = this,
        plugStatus = payload.vehicleInfo?.chargeInfo?.plugStatus,
        chargeRemainingAmount = payload.vehicleInfo?.chargeInfo?.chargeRemainingAmount,
        evDistance = payload.vehicleInfo?.chargeInfo?.evDistance,
        evDistanceAC = payload.vehicleInfo?.chargeInfo?.evDistanceAC,
        evDistanceUnit = payload.vehicleInfo?.chargeInfo?.evDistanceUnit ?: "mi",
        remainingChargeTime = payload.vehicleInfo?.chargeInfo?.remainingChargeTime,
        realTimeData = realTimeData,
        forceRefresh = forceRefresh,
    )

const val NORMAL_CHARGING_STATUS = 40
const val FAST_CHARGING_STATUS = 56
const val PLUG_WAITING_FOR_TIMER = 36

fun ChargeInfo.isCharging(): Boolean = plugStatus == NORMAL_CHARGING_STATUS || plugStatus == FAST_CHARGING_STATUS

fun ChargeInfo.isPluggedIn(): Boolean = plugStatus == PLUG_WAITING_FOR_TIMER
