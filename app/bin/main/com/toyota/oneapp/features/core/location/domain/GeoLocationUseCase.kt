/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.location.domain

import android.location.Address
import kotlinx.coroutines.flow.Flow

fun interface GeoLocationUseCase {
    fun getByLocationName(locationName: String): Flow<Result<List<Address>>>
}

fun interface GeoLocationService {
    fun getFromLocationName(
        locationName: String,
        maxResult: Int,
    ): Flow<Result<List<Address>>>
}

fun interface GeoLocationRepository {
    fun getFromLocationName(
        locationName: String,
        maxResult: Int,
    ): Flow<Result<List<Address>>>
}
