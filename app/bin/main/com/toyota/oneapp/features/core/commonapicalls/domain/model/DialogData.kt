package com.toyota.oneapp.features.core.commonapicalls.domain.model

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

data class DialogData(
    var imageId: Int,
    var imageBgColor: Color,
    var title: String,
    var subtitle: String? = null,
    var primaryButtonText: String? = null,
    var secondaryButtonText: String? = null,
    var primaryOnClick: () -> Unit,
    var secondaryOnClick: (() -> Unit)? = null,
    var annotatedView: (@Composable () -> Unit)? = null,
    var header: (@Composable () -> Unit)? = null,
)
