/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.location.dataaccess

import com.toyota.oneapp.features.core.location.domain.GeoLocationRepository
import com.toyota.oneapp.features.core.location.domain.GeoLocationService
import javax.inject.Inject

internal class GeoLocationDefaultRepository
    @Inject
    constructor(
        private val service: GeoLocationService,
    ) : GeoLocationRepository {
        override fun getFromLocationName(
            locationName: String,
            maxResult: Int,
        ) = service.getFromLocationName(locationName, maxResult)
    }
