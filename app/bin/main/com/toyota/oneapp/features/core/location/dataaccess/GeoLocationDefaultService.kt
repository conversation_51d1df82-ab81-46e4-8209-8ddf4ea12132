/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.location.dataaccess

import android.location.Address
import android.location.Geocoder
import android.os.Build
import android.os.RemoteException
import androidx.annotation.RequiresApi
import com.toyota.oneapp.features.core.location.domain.GeoLocationService
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flow
import java.io.IOException

@RequiresApi(Build.VERSION_CODES.TIRAMISU)
internal class GeoLocationDefaultService(
    private val geocoderProvider: GeocoderProvider,
) : GeoLocationService {
    override fun getFromLocationName(
        locationName: String,
        maxResult: Int,
    ) = callbackFlow {
        val geocoder = geocoderProvider.getGeocoder()
        val listener =
            object : Geocoder.GeocodeListener {
                override fun onGeocode(addresses: List<Address>) {
                    trySend(Result.success(addresses))
                }

                override fun onError(message: String?) {
                    val exception = IOException(message ?: "Geocoding error")
                    trySend(Result.failure(exception))
                }
            }
        geocoder.getFromLocationName(locationName, maxResult, listener)
        awaitClose()
    }
}

@Suppress("DEPRECATION")
internal class GeoLocationLegacyService(
    private val geocoderProvider: GeocoderProvider,
) : GeoLocationService {
    override fun getFromLocationName(
        locationName: String,
        maxResult: Int,
    ) = flow {
        try {
            val geoCoder = geocoderProvider.getGeocoder()
            val addresses = geoCoder.getFromLocationName(locationName, maxResult)
            emit(Result.success(addresses.orEmpty()))
        } catch (e: IOException) {
            emit(Result.failure(e))
        } catch (e: IllegalArgumentException) {
            emit(Result.failure(e))
        } catch (e: SecurityException) {
            emit(Result.failure(e))
        } catch (e: RemoteException) {
            emit(Result.failure(e))
        }
    }
}
