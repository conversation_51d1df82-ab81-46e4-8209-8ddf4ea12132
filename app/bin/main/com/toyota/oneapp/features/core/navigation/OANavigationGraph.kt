/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.navigation

import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.features.accountnotification.presentation.AccountNotificationScreen
import com.toyota.oneapp.features.bottomnavigation.domain.model.OABottomNavItem
import com.toyota.oneapp.features.chargeassist.chargeAssistNavGraph
import com.toyota.oneapp.features.chargeassist.chargeAssistScheduleNavGraph
import com.toyota.oneapp.features.chargeinfo.navigation.chargeInfoNavGraph
import com.toyota.oneapp.features.chargemanagement.presentation.navigation.chargeManagementNavGraph
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleScreen
import com.toyota.oneapp.features.cleanassist.presentation.metrics.CleanAssistMetricsScreen
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.presentation.BaseViewWrapper
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.ClimatePreferenceContainer
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.ClimateViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.localClimateViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.schedule.presentation.ScheduleDetailScreen
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.schedule.presentation.widgets.ScheduleClimateScreen
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardScreen
import com.toyota.oneapp.features.dashboard.novehicle.presentation.NoVehicleScreen
import com.toyota.oneapp.features.dashboard.novehicle.presentation.NoVehicleViewModel
import com.toyota.oneapp.features.dataconsent.presentation.details.DataConsentDetailsScreen
import com.toyota.oneapp.features.dataconsent.presentation.details.DataConsentDetailsViewModel
import com.toyota.oneapp.features.dealerservice.application.navigation.dealerServiceAppointmentNavGraph
import com.toyota.oneapp.features.entrollment.presentation.ChargePointView
import com.toyota.oneapp.features.entrollment.presentation.DisclaimerScreen
import com.toyota.oneapp.features.find.presentation.FindScreen
import com.toyota.oneapp.features.find.presentation.FindViewModel
import com.toyota.oneapp.features.findstations.presentation.FindStationsScreen
import com.toyota.oneapp.features.glovebox.dashboardlights.presentation.DashboardLightsScreen
import com.toyota.oneapp.features.glovebox.gloveboxlist.presentation.GloveBoxScreen
import com.toyota.oneapp.features.glovebox.manualandwarranties.presentation.ManualsAndWarrantiesScreen
import com.toyota.oneapp.features.glovebox.specsandcapabilities.presentation.SpecsAndCapabilitiesScreen
import com.toyota.oneapp.features.guestdriver.presentation.util.guestNavigationGraph
import com.toyota.oneapp.features.pay.presentation.PayScreen
import com.toyota.oneapp.features.pay.wallet.presentation.addevwaletcard.AddEVWalletCardScreen
import com.toyota.oneapp.features.pay.wallet.presentation.evtransactions.CardTransactionsScreen
import com.toyota.oneapp.features.pay.wallet.presentation.evtransactions.WalletTransactionsScreen
import com.toyota.oneapp.features.pay.wallet.presentation.evwallethome.WalletHomeScreen
import com.toyota.oneapp.features.publiccharging.domain.model.ChargingStatus
import com.toyota.oneapp.features.publiccharging.presentation.PublicChargingScreen
import com.toyota.oneapp.features.publiccharging.presentation.components.PublicChargingStatusScreen
import com.toyota.oneapp.features.rentals.presentation.RentalViewModel
import com.toyota.oneapp.features.service.presentation.ServiceScreen
import com.toyota.oneapp.features.shop.presentation.AnnouncementListScreen
import com.toyota.oneapp.features.shop.presentation.InsuranceScreen
import com.toyota.oneapp.features.shop.presentation.ShopScreen
import com.toyota.oneapp.features.shop.presentation.SiriusXmScreen
import com.toyota.oneapp.features.subscription.presentation.SubscriptionListScreen
import com.toyota.oneapp.features.trips.presentation.navigation.tripsNavGraph
import com.toyota.oneapp.features.vehicleinfo.presentation.VehicleInfoScreen
import com.toyota.oneapp.features.vehiclenickname.presentation.VehicleNicknameScreen
import com.toyota.oneapp.features.vehiclesoftware.presentation.VehicleSoftwareScreen
import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.presentation.VehicleSoftwareUpdate21mmDetailsScreen
import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.presentation.VehicleSoftwareUpdate21mmScreen
import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.presentation.VehicleSoftwareUpdateAvailable21mmScreen
import com.toyota.oneapp.features.vehicleswitcher.presentation.VehicleSwitcherBottomSheet
import com.toyota.oneapp.model.vehicle.VehicleInfo

val OANavController =
    staticCompositionLocalOf<NavHostController> {
        error("OANavController is not provided")
    }

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun OANavigationGraph(
    modalBottomSheetState: ModalBottomSheetState? = null,
    navController: NavHostController,
    analyticsLogger: AnalyticsLogger,
    startDestination: String = OABottomNavItem.Home.route,
) {
    CompositionLocalProvider(OANavController provides navController) {
        Box(
            Modifier
                .background(AppTheme.colors.tertiary12)
                .fillMaxSize(),
        ) {
            NavHost(
                navController,
                startDestination = startDestination,
                modifier = Modifier.background(AppTheme.colors.tertiary15),
            ) {
                composable(route = OABottomNavItem.Service.route) {
                    analyticsLogger.logEvent(AnalyticsEvent.BOTTOM_NAV_SERVICE_TAB)
                    // TODO:Replace with original screen
                    ServiceScreen(navHostController = navController)
                }
                composable(route = OABottomNavItem.Pay.route) {
                    analyticsLogger.logEvent(AnalyticsEvent.BOTTOM_NAV_PAY_TAB)
                    PayScreen(navController)
                }
                composable(route = OABottomNavItem.Home.route) {
                    val viewModel = (LocalContext.current as OADashboardActivity).dashboardViewModel

                    val vehicleInfo: VehicleInfo? =
                        viewModel.getSelectedVehicleState().collectAsState().value
                    when {
                        vehicleInfo != null -> {
                            OADashboardScreen(
                                navController,
                            )
                        }

                        else -> {
                            val noVehicleDashboardViewModel = hiltViewModel<NoVehicleViewModel>()
                            val rentalViewModel = hiltViewModel<RentalViewModel>()
                            val rentalState by rentalViewModel.uiState.collectAsState()

                            BaseViewWrapper(
                                navHostController = navController,
                                viewModel = rentalViewModel,
                            ) {
                                NoVehicleScreen(
                                    onEvent = noVehicleDashboardViewModel::onEvent,
                                    onRentalEvent = rentalViewModel::onEvent,
                                    rentalStateHolder = rentalState,
                                )
                            }
                        }
                    }
                }
                composable(route = OABottomNavItem.Shop.route) {
                    analyticsLogger.logEvent(AnalyticsEvent.BOTTOM_NAV_SHOP_TAB)
                    ShopScreen(navController)
                }
                composable(route = OABottomNavItem.Find.route) {
                    analyticsLogger.logEvent(AnalyticsEvent.BOTTOM_NAV_FIND_TAB)
                    val viewModel = hiltViewModel<FindViewModel>()
                    val state by viewModel.uiState.collectAsState()
                    BaseViewWrapper(
                        navHostController = navController,
                        viewModel = viewModel,
                    ) {
                        FindScreen(
                            viewModel = viewModel,
                            state = state,
                            onEvent = viewModel::onEvent,
                            navHostController = navController,
                        )
                    }
                }
                composable(route = OAScreen.FindStation.route) {
                    FindStationsScreen(navController)
                }

                tripsNavGraph(navHostController = navController)

                composable(route = OAScreen.AccountNotification.route) {
                    modalBottomSheetState?.let {
                        AccountNotificationScreen(
                            it,
                            (LocalContext.current as OADashboardActivity).accountNotificationViewModel,
                        )
                    }
                }
                composable(route = OAScreen.VehicleSwitcher.route) {
                    VehicleSwitcherBottomSheet()
                }
                composable(route = OAScreen.VehicleInfo.route) {
                    VehicleInfoScreen(navController, modalBottomSheetState)
                }

                composable(route = OAScreen.VehicleNickname.route) {
                    VehicleNicknameScreen(navController)
                }

                composable(route = OAScreen.Subscriptions.route) {
                    SubscriptionListScreen(navHostController = navController)
                }
                composable(route = OAScreen.Insurance.route) {
                    InsuranceScreen(navController)
                }
                composable(route = OAScreen.SiriusXm.route) {
                    SiriusXmScreen(navController)
                }
                composable(route = OAScreen.GloveBox.route) {
                    GloveBoxScreen(navController)
                }
                composable(route = OAScreen.SpecsAndCapabilities.route) {
                    SpecsAndCapabilitiesScreen(navController)
                }
                composable(route = OAScreen.ManualsAndWarranties.route) {
                    ManualsAndWarrantiesScreen(onBackClick = {
                        navController.navigateUp()
                    })
                }
                composable(route = OAScreen.DashboardLights.route) {
                    DashboardLightsScreen(navController)
                }
                composable(route = OAScreen.AnnouncementList.route) {
                    AnnouncementListScreen(navController)
                }
                guestNavigationGraph(navController)
                composable(route = OAScreen.VehicleSoftwareScreen.route) {
                    VehicleSoftwareScreen(navController, modalBottomSheetState)
                }
                composable(route = OAScreen.ClimateScreen.route) {
                    val viewModel =
                        (LocalContext.current as OADashboardActivity).dashboardViewModel

                    val climateViewModel = hiltViewModel<ClimateViewModel>()

                    CompositionLocalProvider(
                        localClimateViewModel provides climateViewModel,
                    ) {
                        ClimatePreferenceContainer(
                            navController = navController,
                            isScheduleAvailable = viewModel.isClimateSettingsAndScheduleAvailable(),
                            climateViewModel = climateViewModel,
                        )
                    }
                }
                composable(route = OAScreen.ClimatePreferenceScreen.route) {
                    val climateViewModel = it.getClimateViewModel(navController)
                    CompositionLocalProvider(
                        localClimateViewModel provides climateViewModel,
                    ) {
                        ScheduleClimateScreen(
                            navHostController = navController,
                            viewModel = climateViewModel,
                        )
                    }
                }
                composable(route = OAScreen.ScheduleDetailScreen.route) {
                    val climateViewModel = it.getClimateViewModel(navController)
                    CompositionLocalProvider(
                        localClimateViewModel provides climateViewModel,
                    ) {
                        ScheduleDetailScreen(
                            navHostController = navController,
                            viewModel = climateViewModel,
                        )
                    }
                }
                composable(route = OAScreen.SoftwareUpdate21mmScreen.route) {
                    VehicleSoftwareUpdate21mmScreen(navController, modalBottomSheetState)
                }
                composable(route = OAScreen.SoftwareUpdate21mmDetailsScreen.route) {
                    VehicleSoftwareUpdate21mmDetailsScreen(navController, modalBottomSheetState)
                }
                composable(route = OAScreen.WalletHomeScreen.route) {
                    WalletHomeScreen(navController)
                }
                composable(route = OAScreen.WalletTransactionsScreen.route) {
                    WalletTransactionsScreen(navController = navController)
                }
                composable(
                    route = "CardTransactionScreen/{card}",
                    arguments =
                        listOf(
                            navArgument(name = "card") {
                                type = NavType.StringType
                            },
                        ),
                ) {
                    CardTransactionsScreen(
                        navController = navController,
                        card = it.arguments?.getString("card"),
                    )
                }
                composable(route = OAScreen.AddEVCardScreen.route) {
                    AddEVWalletCardScreen(navController = navController)
                }
                composable(route = OAScreen.SoftwareUpdateAvailable21mmScreen.route) {
                    VehicleSoftwareUpdateAvailable21mmScreen(navController, modalBottomSheetState)
                }
                composable(route = OAScreen.CleanAssistMetricsScreen.route) {
                    CleanAssistMetricsScreen(navController)
                }
                composable(route = OAScreen.ChargeSchedule.route) {
                    ChargeScheduleScreen(navController)
                }

                chargeInfoNavGraph(navController)
                chargeAssistNavGraph(navController)
                chargeAssistScheduleNavGraph(navController)
                chargeManagementNavGraph(navController)
                dealerServiceAppointmentNavGraph(navController)
                composable(route = OAScreen.ChargeStation.route) {
                    PublicChargingScreen(navController)
                }
                composable(route = OAScreen.PublicChargingStatusScreen.route) {
                    // Get the charging status from the backstack entry
                    val previousEntry = navController.previousBackStackEntry
                    Log.d("OANavigationGraph", "Previous backstack entry: ${previousEntry?.destination?.route}")

                    val chargingStatus = previousEntry?.savedStateHandle?.get<ChargingStatus>("chargingStatus")
                    val chargingId = previousEntry?.savedStateHandle?.get<String>("chargingId")

                    Log.d("OANavigationGraph", "Retrieved from savedStateHandle: chargingStatus=$chargingStatus, chargingId=$chargingId")

                    if (chargingStatus != null && chargingId != null) {
                        PublicChargingStatusScreen(
                            navController = navController,
                        )
                    } else {
                        Column(
                            modifier =
                                Modifier
                                    .fillMaxSize()
                                    .padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center,
                        ) {
                            OABody4TextView(
                                text = "Charging session information is not available",
                                color = AppTheme.colors.tertiary03,
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            PrimaryButton02(
                                text = "Go Back",
                                click = { navController.popBackStack() },
                                modifier = Modifier.fillMaxWidth(),
                                enabled = true,
                            )
                        }
                    }
                }
                composable(route = OAScreen.ChargeSettingDisclaimerScreen.route) {
                    DisclaimerScreen(navController)
                }
                composable(route = OAScreen.ChargePointMainScreen.route) {
                    ChargePointView()
                }
                composable(route = OAScreen.DataConsentDetailsScreen.route) {
                        entry ->
                    val viewModel =
                        entry.sharedViewModel<DataConsentDetailsViewModel>(
                            getBackStackEntry = { path ->
                                navController.getBackStackEntry(path)
                            },
                        )
                    DataConsentDetailsScreen(
                        navController,
                        viewModel = viewModel,
                    )
                }
            }
        }
    }
}

@Composable
fun NavBackStackEntry.getClimateViewModel(navController: NavController): ClimateViewModel {
    val backStackEntry =
        remember(this) {
            navController.getBackStackEntry(
                OAScreen.ClimateScreen.route,
            )
        }
    return hiltViewModel(backStackEntry)
}

object OANavArguments {
    const val STRATEGY = "strategy"
}
