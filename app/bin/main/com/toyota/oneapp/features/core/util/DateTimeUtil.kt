/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.core.util

import android.icu.util.Calendar
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyotaConstants
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.util.Date
import java.util.Locale
import kotlin.math.roundToInt

object DateTimeUtil {
    val timeFormat_DateMonthYearWithTime = SimpleDateFormat("MMMM dd, yyyy 'at' h:mm a")
    val todayTimeFormat = SimpleDateFormat("h:mm a")
    const val TODAY_AT = "Today at"
    const val YESTERDAY_AT = "Yesterday at"
    const val TIME_FORMAT_WITH_TSZ = "yyyy-MM-dd'T'HH:mm:ss.SSZZZZZ"
    const val TIME_FORMAT_FULL = "EEEE, MMMM d 'at' h:mm a"
    const val TIME_FORMAT_WITH_HOURS_MINUTES = "hh:mm a"
    const val MONTH_FORMAT = "MMM"
    const val DATE_FORMAT = "dd"
    const val MONTH_DATE_YEAR_FORMAT = "MMM dd, yyyy"
    const val DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd"
    const val TIME_FORMAT_TSZ_SUFFIX = ".00-05:00"
    const val INVALID_DURATION = 65535
    const val TIMESTAMP_REGEX = "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z\$"
    const val TWELVE_HOUR_TIME_FORMAT = "h:mm a"

    fun formatDate(
        dateTime: String?,
        inputDateTimeFormatter: String,
        resultDateTimeFormatter: String,
    ): String {
        return dateTime?.let {
            try {
                val inputFormatter =
                    DateTimeFormatter.ofPattern(
                        inputDateTimeFormatter,
                        Locale.getDefault(),
                    )
                val outputFormatter =
                    DateTimeFormatter.ofPattern(
                        resultDateTimeFormatter,
                        Locale.getDefault(),
                    )
                val date = LocalDate.parse(it, inputFormatter)
                return date.format(outputFormatter)
            } catch (_: Exception) {
                ToyotaConstants.EMPTY_STRING
            }
        } ?: ToyotaConstants.EMPTY_STRING
    }

    fun formatDateTime(
        dateTime: String?,
        inputDateTimeFormatter: String,
        resultDateTimeFormatter: String,
    ): String =
        dateTime?.let {
            val inputFormatter =
                DateTimeFormatter.ofPattern(
                    inputDateTimeFormatter,
                    Locale.getDefault(),
                )
            val outputFormatter =
                DateTimeFormatter.ofPattern(
                    resultDateTimeFormatter,
                    Locale.getDefault(),
                )
            val zonedDateTime = ZonedDateTime.parse(dateTime, inputFormatter)
            zonedDateTime.toLocalTime().format(outputFormatter)
        } ?: ToyotaConstants.EMPTY_STRING

    fun formatActualDateTime(
        dateTime: String?,
        inputDateTimeFormatter: String,
        resultDateTimeFormatter: String,
    ): String =
        try {
            val inputFormatter = DateTimeFormatter.ofPattern(inputDateTimeFormatter)
            val outputFormatter = DateTimeFormatter.ofPattern(resultDateTimeFormatter)
            val offsetDateTime = OffsetDateTime.parse(dateTime, inputFormatter)
            offsetDateTime.format(outputFormatter)
        } catch (_: Exception) {
            ToyotaConstants.EMPTY_STRING
        }

    @JvmStatic
    fun getThirdMonthDateFromNow(): String {
        val endDate = Calendar.getInstance()
        endDate.run {
            this.add(Calendar.MONTH, 3)
            this.set(Calendar.DAY_OF_MONTH, 1)
            this.add(Calendar.DAY_OF_MONTH, -1)
        }
        return SimpleDateFormat(DateUtil.YYYY_MM_DD).format(Date(endDate.timeInMillis))
    }

    fun getTimeFormattedDuration(
        totalMinutes: Int,
        isShort: Boolean = false,
    ): String =
        if (totalMinutes == INVALID_DURATION) {
            ""
        } else {
            var remaining = totalMinutes
            val mins = remaining % 60
            remaining = ((remaining - mins) / 60).toDouble().roundToInt()
            val hours = remaining % 24
            val days = ((remaining - hours) / 24).toDouble().roundToInt()

            formatComponents(days, hours, mins, isShort)
        }

    private fun formatComponents(
        days: Int,
        hours: Int,
        mins: Int,
        isShort: Boolean,
    ): String {
        val dayStr = if (isShort) "d" else " day "
        val hourStr = if (isShort) "h" else " hr "
        val minStr = if (isShort) "m" else " min "

        val dayFormatted = if (days == 0) "" else "$days$dayStr"
        val hourFormatted = if (hours == 0) "" else "$hours$hourStr"
        val minFormatted = if (mins == 0) "0 min" else "$mins$minStr"

        return dayFormatted + hourFormatted + minFormatted
    }

    fun extractedPluggedInTime(pluggedInTimeRaw: String?): String {
        var formatTime = ""
        // if not empty and matches the format do this
        if (pluggedInTimeRaw.isNotNullOrEmpty() &&
            pluggedInTimeRaw.matches(Regex(TIMESTAMP_REGEX))
        ) {
            try {
                // SAMPLE INPUT -> 2025-02-05T01:50:00Z"
                val zonedDateTime = ZonedDateTime.parse(pluggedInTimeRaw)

                // get time based on device local timezone
                val localZonedDateTime = zonedDateTime.withZoneSameInstant(ZoneId.systemDefault())

                // convert to 12 hr format
                val formatter = DateTimeFormatter.ofPattern(TWELVE_HOUR_TIME_FORMAT)

                // return the real time based on user location
                formatTime = localZonedDateTime.format(formatter)
            } catch (e: DateTimeParseException) {
                formatTime = ""
            }
        }

        // if format doesn't match return empty string
        return formatTime
    }
}
