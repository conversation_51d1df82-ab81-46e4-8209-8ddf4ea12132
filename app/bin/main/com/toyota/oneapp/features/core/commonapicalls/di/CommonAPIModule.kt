/*
 *  Created by sudhan.ram on 12/09/24, 2:21 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 12/09/24, 2:21 pm
 *
 */

package com.toyota.oneapp.features.core.commonapicalls.di

import com.toyota.oneapp.features.core.commonapicalls.dataaccess.repository.CommonApiDefaultRepository
import com.toyota.oneapp.features.core.commonapicalls.domain.repository.CommonApiRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class CommonAPIModule {
    @Binds
    abstract fun bindCommonApiRepo(repo: CommonApiDefaultRepository): CommonApiRepository
}
