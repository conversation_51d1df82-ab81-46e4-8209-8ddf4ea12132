/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.util

/** Readme
Accessibility Format

feature_uiwidget_type_action[optional] : id should be in lowercase

eg(without cta): topnav_title_text
eg(without cta): dashboard_vehicleimage_image
eg(without cta): fuelwidget_fuelguage_icon

eg: advanceremote_start_button_cta
eg: fuelwidget_tile_cta
eg: dashboard_accountnotification_icon_cta
eg: ftue_close_icon_cta
eg: ftue_ok_button_cta

Note: For Action
1. button_cta
2. icon_cta
3. image_cta
4. tile_cta

Note: No camel cases, uppercase
 * */

object AccessibilityId {
    // OADashboard
    const val ID_TAB_REMOTE = "ID_TAB_REMOTE"
    const val ID_TAB_STATUS = "ID_TAB_STATUS"
    const val ID_TAB_HEALTH = "ID_TAB_HEALTH"
    const val ID_DASHBOARD_REFRESH = "dashboard_refresh"

    // Vehicle Info
    const val ID_VEHICLE_INFO_CTA = "dashboard_vehicle_info_cta"
    const val ID_VEHICLE_INFO_GLOVBOX_CARD = "vechicleInfo_Glovebox"
    const val ID_VEHICLE_INFO_SUBSCRIPTION = "vechicleInfo_Subscriptions"
    const val ID_VEHICLE_INFO_CONNECTED_SERVICE_SUPPORT =
        "vechiclInfo_connectedServicesSupport"
    const val ID_VEHICLE_INFO_APP_SUIT = "vechiclInfo_appSuite"
    const val ID_VEHICLE_INFO_DYNAMIC_NAVI = "vechiclInfo_dynamicNavi"
    const val ID_VEHICLE_INFO_SOFTWARE_UPDATE = "vechiclInfo_vehicleSoftware"
    const val ID_VEHICLE_INFO_NICKNAME_EDIT = "VehicleInfo_edit_Btn"
    const val ID_VEHICLE_INFO_REMOVE_VEHICLE = "vechiclInfo_removeVehicle"
    const val ID_VEHICLE_INFO_COPIED_VIN = "VehicleInfo_copy_vin"
    const val ID_VEHICLE_INFO_NICKNAME = "vechiclInfo_nickName"
    final val ID_VEHICLE_INFO_BACK_ARROW_CTA = "vehicleInfo_back_button"
    const val ID_VEHICLE_INFO_GLOVE_BOX_ICON = "vehicle_info_glove_box_icon"
    const val ID_VEHICLE_INFO_SUBSCRIPTION_ICON = "vehicle_info_subscription_icon"
    const val ID_VEHICLE_INFO_CONNECTED_SERVICE_ICON = "vehicle_info_connected_service_icon"
    const val ID_VEHICLE_INFO_VEHICLE_SOFTWARE_ICON = "vehicle_info_software_icon"
    const val ID_VEHICLE_INFO_APP_SUITE_ICON = "vehicle_info_app_suite_icon"
    const val ID_VEHICLE_INFO_DYNAMIC_NAVI_ICON = "vehicle_info_dynamic_navi_icon"
    const val ID_VEHICLE_INFO_BACK_BOX_CTA = "vehicle_info_back_box_cta"
    const val ID_VEHICLE_INFO_VEHICLE_IMAGE = "vehicle_info_vehicle_image"

    // Vehicle Nickname
    const val ID_UPDATE_NICKNAME_TITLE = "VehicleNickName_title"
    const val ID_MODEL_NAME = "VehicleNickName_modal_name"
    const val ID_NICKNAME_TEXTFIELD = "VehicleNickName_enter_nickname"
    const val ID_SAVE_NICKNAME = "VehicleNickName_save_button"
    const val ID_CLOSE_NICKNAME = "VehicleNickName_close_button"
    const val ID_VEHICLE_NICKNAME_EDIT_TEXT_FIELD = "vehicle_nickname_edit_text_field"

    // Glovebox
    const val ID_GLOVEBOX_VEHICLE_IMAGE = "GLovebox_vehicle_image"
    const val ID_GLOVEBOX_BACK_BTN = "glovebox_back_button"
    const val ID_SPECS_AND_CAPABILITIES_TILE = "specs_capabilities_tile"
    const val ID_MANUALS_AND_WARRANTIES_TILE = "manuals_warranties_tile"
    const val ID_DASHBOARD_LIGHTS_TILE = "dashboard_lights_tile"
    const val ID_TTF_TILE = "ttf_tile"
    const val ID_PRO_SEATS_TILE = "pro_seats_tile"
    const val ID_HOW_TO_VIDEO_TILE = "how_to_video_tile"
    const val ID_SEE_FAQ_TEXT_BUTTON = "see_faq_text_button"
    const val ID_DASHBOARD_LIGHTS_BACK_BTN = "dashboard_lights_back_btn"
    const val ID_DASHBOARD_LIGHTS_SEARCH_TEXTFIELD = "dashboard_lights_search_textfield"
    const val ID_CLEAR_SEARCH_IMAGE_BUTTON = "clear_search_button"
    const val ID_CLOSE_DASHBOARD_LIGHTS_DETAIL_BUTTON = "close_dashboard_lights_bottom_sheet"
    const val ID_MANUALS_WARRANTIES_BACK_BTN = "manuals_warranties_back_button"
    const val ID_SPECS_AND_CAPABILITIES_BACK_BTN = "specs_and_capabilities_back_button"

    // Subscription List
    const val ID_SUBSCRIPTION_BACK_BTN = "subscription_back_button"
    const val ID_SUBSCRIPTION_INFO_BTN = "subscription_info_button"
    const val ID_SUBSCRIPTION_CTA_BTN = "subscription_action_button"
    const val ID_SUBSCRIPTION_DISCLAIMER_EXIT_BTN = "subscription_disclaimer_exit_button"

    // Vehicle Switcher
    const val ID_VEHICLE_SWITCHER_PULL_DOWN_BUTTON = "vehicle_switcher_pull_down_button"
    const val ID_VEHICLE_SWITCHER_ADD_VEHICLE_BUTTON = "vehicle_switcher_add_vehicle_button"
    const val ID_VEHICLE_SWITCHER_VEHICLE_TITLE_TEXT = "vehicle_switcher_vehicles_title_text"
    const val ID_VEHICLE_SWITCHER_MAKE_DEFAULT_BUTTON = "vehicle_switcher_make_default_button"
    const val ID_VEHICLE_SWITCHER_DEFAULT_TEXT = "vehicle_switcher_default_text"
    const val ID_VEHICLE_SWITCHER_MODEL_NAME_TEXT = "vehicle_switcher_model_name_text"
    const val ID_VEHICLE_SWITCHER_MODEL_YEAR_TEXT = "vehicle_switcher_model_year_text"

    const val ID_VEHICLE_SWITCHER_DISCLAIMER_TEXT = "vehicle_switcher_disclaimer_text"
    const val ID_VEHICLE_SWITCHER_REMOVE_VEHICLE_BUTTON = "vehicle_switcher_remove_vehicle_button"
    const val ID_VEHICLE_SWITCHER_SELECT_BUTTON = "vehicle_switcher_select_button"
    const val ID_VEHICLE_SWITCHER_BOTTOM_SHEET_REMOVE_VEHICLE_TITLE_TEXT = "vehicle_switcher_bottom_sheet_remove_vehicle_title_text"
    const val ID_VEHICLE_SWITCHER_BOTTOM_SHEET_REMOVE_CONFIRMATION_TEXT = "vehicle_switcher_bottom_sheet_remove_confirmation_text"
    const val ID_VEHICLE_SWITCHER_BOTTOM_SHEET_CANCEL_BUTTON = "vehicle_switcher_bottom_sheet_cancel_button"
    const val ID_VEHICLE_SWITCHER_BOTTOM_SHEET_REMOVE_BUTTON = "vehicle_switcher_bottom_sheet_remove_button"
    const val ID_VEHICLE_SWITCHER_SUBS_REMOVE_CONFIRM_TEXT = "vehicle_switcher_bottom_sheet_subscription_remove_confirm_text"
    const val ID_VEHICLE_SWITCHER_DISPLAY_IMAGE = "vehicle_switcher_display_image"
    const val ID_VEHICLE_SWITCHER_INDICATOR_IMAGE = "vehicle_switcher_indicator_image"

    // Dynamic VIN Component
    const val VIN_NUMBER_TEXT_CTA = "vin_number_text_cta"
    const val VIN_LABEL_TEXT_CTA = "vin_label_text_cta"
    const val VIN_COPY_ICON_CTA = "vin_copy_icon_cta"
    const val VIN_SHOW_ICON_CTA = "vin_show_icon_cta"
    const val VIN_HIDE_ICON_CTA = "vin_hide_icon_cta"
    const val VIN_COPIED_POPUP_TEXT = "vin_copied_popup_text"

    // Account & Notification
    const val ID_ACCOUNT_NOTIFICATION_SIGN_OUT_TEXT = "account_notification_signout_text"
    const val ID_ACCOUNT_NOTIFICATION_ACCOUNT_BUTTON = "account_notification_account_button"
    const val ID_ACCOUNT_NOTIFICATION_NOTIFICATION_TILE = "account_notification_notification_tile"
    const val ID_ACCOUNT_NOTIFICATION_ANNOUNCEMENT_TILE = "account_notification_announcement_tile"
    const val ID_ACCOUNT_NOTIFICATION_TAKE_A_TOUR_TILE = "account_notification_take_a_tour_tile"
    const val ID_ACCOUNT_NOTIFICATION_DARK_MODE_SWITCH = "account_notification_dark_mode_switch"

    // Announcement Center
    const val ID_ANNOUNCEMENT_LIST_BACK_BUTTON = "announcement_list_back_button"
    const val ID_ANNOUNCEMENT_DETAIL_BACK_BUTTON = "announcement_detail_back_button"
    const val ID_ANNOUNCEMENT_DISMISS_CTA = "announcement_dismiss_button"
    const val ID_ANNOUNCEMENT_DETAIL_PRIMARY_BUTTON = "announcement_detail_primary_button"
    const val ID_ANNOUNCEMENT_DETAIL_SECONDARY_BUTTON = "announcement_detail_secondary_button"
    const val ID_DISMISS_DIALOG_PRIMARY_BUTTON = "dismiss_dialog_primary_button"
    const val ID_DISMISS_DIALOG_SECONDARY_CTA = "dismiss_dialog_secondary_button"

    // Top Nav
    const val ID_VEHICLE_SWITCHER_CHEVRON = "top_nav_vehicle_info_entry_point_icon"
    const val TOP_NAV_PROFILE_ICON = "top_nav_profile_icon"

    // Shop
    final val ID_INSURANCE_CARD_CTA = "shop_insurance_tile"
    final val ID_SXM_CARD_CTA = "shop_sirius_xm_tile"
    final val ID_MANAGE_SUBSCRIPTION_CARD_CTA = "shop_manage_subscription_button"
    final val ID_PARTS_ACCESSORIES_CARD_CTA = "shop_parts_accessories_tile"
    final val ID_INSURANCE_LEARN_MORE_GET_QUOATE_CTA = "insurance_learn_more_get_quote_button"
    final val ID_SXM_LEARN_MORE_SUBSCRIBE_CTA = "siriusxm_learn_more_subscription_button"
    final val ID_SXM_BACK_ARROW_CTA = "siriusxm_back_button"
    final val ID_INSURANCE_BACK_ARROW_CTA = "insurance_back_button"
    final val ID_SXM_DOWNLOAD_SXM_APP = "siriusxm_download_text"
    final val ID_SHOP_SHIMMER_LOADING_ID = "shop_shimmer_loading"
    const val ID_ANNOUNCEMENT_CARD_CTA = "shop_announcement_card_cta"

    // Advance Remote Commands
    const val ID_REMOTE_ENGINE_START_BUTTON = "remote_engine_start_button"
    const val ID_REMOTE_ENGINE_STOP_BUTTON = "remote_engine_stop_button"
    const val ID_REMOTE_DOOR_LOCK_BUTTON = "remote_door_lock_button"
    const val ID_REMOTE_DOOR_UNLOCK_BUTTON = "remote_door_unlock_button"
    const val ID_REMOTE_LIGHTS_BUTTON = "remote_lights_button"
    const val ID_REMOTE_HORN_BUTTON = "remote_horn_button"
    const val ID_REMOTE_HAZARD_BUTTON = "remote_hazard_button"
    const val ID_REMOTE_BUZZER_BUTTON = "remote_buzzer_button"
    const val ID_REMOTE_TRUNK_LOCK_BUTTON = "remote_trunk_lock_button"
    const val ID_REMOTE_TRUNK_UNLOCK_BUTTON = "remote_trunk_lock_button"
    const val ID_REMOTE_TAILGATE_LOCK_BUTTON = "remote_tailgate_lock_button"
    const val ID_REMOTE_TAILGATE_UNLOCK_BUTTON = "remote_tailgate_unlock_button"
    const val ID_REMOTE_CLIMATE_BUTTON = "remote_trunk_lock_button"
    const val ID_DASHBOARD_REMOTE_OPEN_ICON_BUTTON = "dashboard_remote_open_iconbutton"
    const val ID_DASHBOARD_REMOTE_CLOSE_ICON_BUTTON = "dashboard_remote_close_iconbutton"
    const val ID_REMOTE_PARK_BUTTON = "remote_park_button"

    // Guest Driver
    const val ID_REMOTE_SHARE_TOOLBAR_TITILE = "remote_share_toolbar_title"
    const val ID_REMOTE_SHARE_BACK_BTN = "remote_share_back_btn"
    const val ID_SHARE_REMOTE_CTA = "share_remote_cta"
    const val ID_REMOTE_GUEST_DRIVER_CARD = "remote_guest_driver_card"
    const val ID_ADD_GUEST_DRIVER_TITLE = "add_guest_driver_title"
    const val ID_ADD_GUEST_DRIVER_BACK_BTN = "add_guest_driver_back"
    const val ID_ADD_GUEST_DRIVER_CLEAR_SEARCH_ICON = "add_guest_driver_clear_search"
    const val ID_SEARCH_GUEST_DRIVER_BTN = "search_guest_driver"
    const val ID_INVITE_GUEST_DRIVER_BTN = "invite_guest_driver"
    const val ID_INVITE_SUCCESS_BACK_TO_DASHBOARD_BTN = "invite_success_back_to_dashboard"

    // Service Page
    const val ID_SERVICE_PAGE_SERVICE_APPOINTMENT_TITLE_TEXTVIEW = "service_page_service_appointment_title_textview"
    const val ID_SERVICE_PAGE_MAKE_AN_APPOINTMENT_CTA_BUTTON = "service_page_make_an_appointment_cta_button"
    const val ID_SERVICE_PAGE_ROADSIDE_ASSISTANCE_CALL_CTA_BUTTON = "service_page_roadside_assistance_call_cta_button"
    const val ID_SERVICE_PAGE_ROADSIDE_ASSISTANCE_SEE_FAQ_CTA_BUTTON = "service_page_roadside_assistance_see_faq_cta_button"

    // DK_WIDGET
    const val DK_WIDGET_DOWNLOAD_BUTTON = "DigitalKeyWidget_downloadButton"
    const val DK_WIDGET_TITLE = "DigitalKeyWidget_titleText"
    const val DK_WIDGET_NOTIFICATION_ICON = "DigitalKeyWidget_notificationView"
    const val DK_WIDGET_SHARE_BUTTON = "DigitalKeyWidget_shareWithBtn"
    const val DK_WIDGET_MANAGE_SHARE_BUTTON = "DigitalKeyWidget_manageShareBtn"
    const val DK_WIDGET_MANAGE_BUTTON = "DigitalKeyWidget_manageBtn"
    const val DK_WIDGET_CONNECT_VEHICLE_BUTTON = "DigitalKeyWidget_connectToVehicleBtn"
    const val DK_WIDGET_DOWNLOAD_IN_PROGRESS_VIEW = "DigitalKeyWidget_downloadProgressView"
    const val DK_WIDGET_DOWNLOADING_TEXT = "DigitalKeyWidget_downloadingText"
    const val DK_WIDGET_SYNCING_TEXT = "DigitalKeyWidget_syncingText"
    const val DK_WIDGET_BIOMETRICS_SETTINGS_BUTTON = "DigitalKeyWidget_biometricsSettingsButton"
    const val DK_WIDGET_BIOMETRICS_SETTINGS_CANCEL_BUTTON = "DigitalKeyWidget_biometricsSettingsCancelButton"

    // DK Bluetooth Screen
    const val DK_BLUETOOTH_TITLE_TEXT = "DKErrorTurnOnBluetoothContentView_titleText"
    const val DK_BLUETOOTH_ICON = "DKErrorTurnOnBluetoothContentView_popupImageView"
    const val DK_BLUETOOTH_DESCRIPTION = "DKErrorTurnOnBluetoothContentView_descriptionText"
    const val DK_BLUETOOTH_TRY_AGAIN_BUTTON = "DKErrorTurnOnBluetoothContentView_primaryCTAButton"

    // DK Current VIN Downloading Screen
    const val DK_CURRENT_VIN_DOWNLOADING_TITLE = "DKCurrentVinDownloading_titleText"
    const val DK_CURRENT_VIN_DOWNLOADING_ICON = "DKCurrentVinDownloading_popupImageView"
    const val DK_CURRENT_VIN_DOWNLOADING_DESCRIPTION = "DKCurrentVinDownloading_descriptionText"
    const val DK_CURRENT_VIN_DOWNLOADING_BUTTON = "DKCurrentVinDownloading_OkButton"

    // DK Connect to vehicle Screen
    const val DK_CONNECT_VEHICLE_TITLE = "ConnectToVehicleContentView_titleText"
    const val DK_CONNECT_VEHICLE_IMAGE = "ConnectToVehicleContentView_popupImageView"
    const val DK_CONNECT_VEHICLE_DESCRIPTION = "ConnectToVehicleContentView_descriptionText"
    const val DK_CONNECT_VEHICLE_HEADER_TEXT = "ConnectToVehicleContentView_cta_headerText"
    const val DK_CONNECT_VEHICLE_PRIMARY_BUTTON = "ConnectToVehicleContentView_primaryCTAButton"
    const val DK_CONNECT_VEHICLE_SECONDARY_BUTTON = "ConnectToVehicleContentView_secondaryCTAButton"

    // DK ERROR Screen
    const val DK_ERROR_TITLE = "DKErrorNotDownloadedContentView_titleText"
    const val DK_ERROR_ICON = "DKErrorNotDownloadedContentView_popupImageView"
    const val DK_ERROR_DESCRIPTION = "DKErrorNotDownloadedContentView_descriptionText"
    const val DK_ERROR_PRIMARY_BUTTON = "DKErrorNotDownloadedContentView_primaryCTAButton"
    const val DK_ERROR_SECONDARY_BUTTON = "DKErrorNotDownloadedContentView_secondaryCTAButton"

    // DK Rotation Screen
    const val DK_ROTATION_USER_TEXT = "DownloadGuestKeyContentView_userText"
    const val DK_ROTATION_TITLE = "DownloadGuestKeyContentView_titleText"
    const val DK_ROTATION_SUB_TITLE = "DownloadGuestKeyContentView_subtitleText"
    const val DK_ROTATION_IMAGE = "DownloadGuestKeyContentView_popupImageView"
    const val DK_ROTATION_DESCRIPTION = "DownloadGuestKeyContentView_descriptionText"
    const val DK_ROTATION_HEADER = "DownloadGuestKeyContentView_cta_headerText"
    const val DK_ROTATION_PRIMARY_BUTTON = "DownloadGuestKeyContentView_primaryCTAButton"
    const val DK_ROTATION_SECONDARY_BUTTON = "DownloadGuestKeyContentView_secondaryCTAButton"

    // Find
    const val ID_VEHICLE_LOCATION_CTA = "find_vehicle_location_button"
    const val ID_CURRENT_LOCATION_CTA = "find_current_location_button"
    const val ID_STATION_TILE = "find_station_tile"
    const val ID_DEALER_TILE = "find_dealer_tile"
    const val ID_RENTAL_TILE = "find_rental_tile"
    const val ID_DESTINATIONS_TILE = "find_destination_tile"
    const val ID_DRIVE_PULSE_TRIPS_TILE = "find_drive_pulse_trips_tile"

    // Find Stations
    const val ID_VEHICLE_LOCATION_MAP_ICON = "vehicle_location_map_icon"
    const val ID_CURRENT_LOCATION_MAP_ICON = "current_location_map_icon"
    const val ID_FIND_STATION_BACK_BTN = "find_stations_back_cta"
    const val ID_SEARCH_BAR_TEXTFIELD = "search_nearby_stations_textfield"
    const val ID_DIRECTIONS_ITEM = "directions_item"
    const val ID_WEBSITE_ITEM = "website_item"
    const val ID_CALL_STATION_ITEM = "call_stations_item"
    const val ID_SEND_TO_CAR_ITEM = "send_to_car_item"

    // Public Charging status Screen
    const val ID_PUBLIC_CHARGING_STATUS_BACK_BTN = "public_charging_status_back_cta"

    // Pay Screen - Wallet
    const val ID_WALLET_EV_CARD = "wallet_ev_card"
    const val ID_WALLET_NOT_SETUP_CARD = "wallet_not_setup_card"
    const val ID_REGULAR_WALLET = "regular_wallet"

    // No Vehicle Dashboard
    const val ID_NV_ADD_VEHICLE = "nv_add_vehicle"

    // Remote States
    const val ID_RS_AUTH_REQUIRED_ACTIVATE_BUTTON = "remote_state_auth_activate_button"
    const val ID_RS_SUBSCRIPTION_CANCELLED_RENEW_BUTTON = "remote_state_sub_cancelled_renew_button"
    const val ID_RS_ACTIVATE_FAILED_ACTIVATE_BUTTON = "remote_state_failed_activate_button"
    const val ID_RS_ACTIVATE_PENDING_REFRESH_BUTTON = "remote_state_pending_refresh_button"
    const val ID_RS_ERROR_CONTACT_US_BUTTON = "remote_state_error_contact_button"
    const val ID_RS_SUBSCRIPTION_EXPIRED_RENEW_BUTTON = "remote_state_sub_expired_renew_button"
    const val ID_RS_NOTIFICATION_TURN_ON_BUTTON = "remote_state_notification_on_button"
    const val ID_RS_REMOTE_SHARE_ENABLED = "remote_state_remote_share_remove_button"
    const val ID_RS_REMOTE_SHARE_SHEET_REMOVE_BUTTON = "vehicle_state_remote_share_remove_button"
    const val ID_RS_STOLEN_SHEET_CANCEL_BUTTON = "vehicle_state_stolen_sheet_cancel_button"
    const val ID_RS_STOLEN_CONTACT_US_TEXT_BUTTON = "vehicle_state_stolen_contactus_text_button"
    const val ID_RS_STOLEN_REACTIVATE_BUTTON = "vehicle_state_stolen_reactivate_button"
    const val ID_RS_STOLEN_SHEET_REACTIVATE_BUTTON = "vehicle_state_stolen_sheet_reactivate_button"
    const val ID_RS_AUTH_REQ_SHEET_OKAY_BUTTON = "vehicle_state_auth_required_okay_button"

    // Fuel Widget
    const val DASHBOARD_FUEL_WIDGET_FUEL_GAUGE = "fuel_widget_fuel_gauge"
    const val DASHBOARD_FUEL_WIDGET_BUTTON_CTA = "fuel_widget_button_cta"
    const val DASHBOARD_FUEL_WIDGET_CHARGE_INFO_CTA = "fuel_widget_charge_info_cta"
    const val DASHBOARD_FUEL_WIDGET_DISTANCE_TO_EMPTY = "fuel_widget_distance_to_empty"

    // ChargeInfo Page
    const val ID_CHARGE_INFO_BACK_BTN = "charge_info_back_btn"
    const val ID_EST_MILES_AC_ON_WIDGET = "est_miles_ac_on_widget"
    const val ID_EST_MILES_AC_OFF_WIDGET = "est_miles_ac_off_widget"
    const val ID_PHEV_EST_MILES_AC_OFF_TXT = "phev_est_miles_ac_off_txt"
    const val ID_EV_BATTERY_INFO_CARD = "ev_battery_info_card"
    const val ID_PHEV_BATTERY_INFO_CARD = "phev_battery_info_card"
    const val ID_CHARGE_PERCENTAGE_TXT = "charge_percentage_txt"
    const val ID_CHARGING_PLUGGED_IN_HEADER_TXT = "charging_plugged_in_header_txt"
    const val ID_EV_CHARGE_REMAINING_TXT = "ev_charge_remaining_txt"
    const val ID_PHEV_CHARGE_REMAINING_TXT = "phev_charge_remaining_txt"
    const val ID_EV_CHARGE_PROGRESS_INDICATOR = "ev_charge_progress_bar"
    const val ID_PHEV_CHARGE_PROGRESS_INDICATOR = "phev_charge_progress_bar"
    const val ID_HISTORY_INFO_CARD = "charge_history_info_card"
    const val ID_LAST_CHARGE_INFO_CARD = "last_charge_info_card"
    const val ID_LAST_CHARGE_PLACE_TXT = "last_charge_place_name_txt"
    const val ID_LAST_CHARGE_KWHR_TXT = "last_charge_total_kwhr_txt"
    const val ID_LAST_CHARGE_DATE_TXT = "last_charge_date_txt"
    const val ID_LAST_CHARGE_DURATION_TXT = "last_charge_duration_txt"
    const val ID_SCHEDULE_INFO_CARD = "charge_schedule_info_card"
    const val ID_STATISTICS_CARD = "charge_statistics_card"
    const val ID_ACHIEVED_LEAVES_PIE_CHART = "achieved_leaves_pie_chart"
    const val ID_ACHIEVED_LEAVES_TXT = "achieved_leaves_txt"
    const val ID_ECO_LEARN_MORE_BTN = "eco_learn_more_btn"
    const val ID_CLEAN_ASSIST_CARD = "clean_assist_card"
    const val ID_CLEAN_ASSIST_ENROLL_BTN = "clean_assist_enroll_btn"
    const val ID_CLEAN_ASSIST_VIEW_BTN = "clean_assist_view_btn"
    const val ID_FIND_NEARBY_STATIONS_BTN = "find_nearby_stations_btn"
    const val ID_START_CHARGING_BTN = "start_charging_btn"

    // Charge History
    const val ID_HISTORY_LIST_BACK_BTN = "history_list_back_btn"
    const val ID_FILTER_BY_MONTH_TAB = "filter_by_month_tab"
    const val ID_FILTER_BY_HOME_TYPE_TAB = "filter_by_home_type_tab"
    const val ID_FILTER_BY_PUBLIC_TYPE_TAB = "filter_by_public_type_tab"
    const val ID_CHARGE_SESSION_CARD = "charge_session_card"
    const val ID_UNKNOWN_LOCATION_INFO_ICON = "unknown_location_type_info_icon"
    const val ID_UNKNOWN_LOCATION_CLOSE_BTN = "unknown_location_dialog_close_cta"
    const val ID_UNKNOWN_LOCATION_GO_TO_ACCOUNTS_BTN = "unknown_location_dialog_go_to_accounts_cta"
    const val ID_MONTH_YEAR_PICKER_CLOSE_BTN = "month_year_picker_dialog_close_cta"
    const val ID_MONTH_YEAR_PICKER_OK_BTN = "month_year_picker_dialog_ok_cta"
    const val ID_MONTH_LABEL_TEXT = "month_label_text"
    const val ID_NO_HOME_ADDRESS_LAYOUT = "no_saved_home_address_screen"
    const val ID_NO_HOME_ADDRESS_GO_TO_ACCOUNTS = "no_saved_home_address_go_to_account_btn"
    const val ID_NO_CHARGE_SESSIONS_LAYOUT = "no_charge_sessions_screen"
    const val ID_NO_CHARGE_HISTORY_YET_LAYOUT = "no_charge_history_yet_screen"
    const val ID_CHARGE_DETAILS_BACK_BTN = "history_detail_back_btn"
    const val ID_CHARGE_SESSION_DATE_LAYOUT = "charge_session_date_widget"
    const val ID_CHARGE_SESSION_LOCATION_CARD = "charge_session_location_card"
    const val ID_ECO_CHARGE_INFO_ITEM = "eco_charge_info_item"
    const val ID_ECO_NOT_ELIGIBLE_ICON = "eco_not_eligible_icon"
    const val ID_ECO_BADGE_ICON = "eco_badge_icon"
    const val ID_CHARGE_LEVEL_INFO_ITEM = "soc_info_item"
    const val ID_TIME_INFO_ITEM = "time_range_info_item"
    const val ID_DURATION_INFO_ITEM = "duration_info_item"
    const val ID_ENERGY_USED_INFO_ITEM = "energy_used_info_item"
    const val ID_CARD_INFO_ITEM = "card_info_item"

    // Non Connected Vehicle
    const val ID_NC_PREFERRED_DEALER = "non_cv_preferred_dealer"
    const val ID_NC_MAINTENANCE_SCHEDULE = "non_cv_maintanance_schedule"
    const val ID_NC_SERVICE_HISTORY = "non_cv_service_history"
    const val ID_NC_APPOINTMENTS = "non_cv_appointments"

    // Vehicle Health
    const val ID_SAFETY_RECALLS_TILE = "safety_recalls_tile"
    const val ID_SERVICE_CAMPAIGN_TILE = "service_campaign_tile"
    const val ID_VEHICLE_ALERTS_TILE = "vehicle_alerts_tile"
    const val ID_KEY_FOB_TILE = "key_fob_tile"
    const val ID_VEHICLE_HEALTH_REPORT_TILE = "vehicle_health_report_tile"
    const val ID_SAFETY_RECALLS_ITEM_TILE = "vehicle_health_safety_recalls_item_tile"
    const val ID_SAFETY_RECALLS_CALL_DEALER = "vehicle_health_safety_recalls_call_dealer_button"
    const val ID_SERVICE_CAMPAIGN_ITEM_TILE = "vehicle_health_service_campaign_item_tile"
    const val ID_SERVICE_CAMPAIGN_CALL_DEALER = "vehicle_health_service_campaign_call_dealer_button"
    const val ID_VEHICLE_ALERTS_ITEM_TILE = "vehicle_health_vehicle_alerts_item_tile"
    const val ID_VEHICLE_ALERTS_CALL_DEALER = "vehicle_health_vehicle_alerts_call_dealer_button"
    const val ID_KEY_FOB_OK_BUTTON = "vehicle_health_key_fob_ok_button"
    const val ID_VEHICLE_HEALTH_SAFETY_ICON = "vehicle_health_safety_icon"
    const val ID_VEHICLE_HEALTH_SAFETY_SUB_ICON_SUCCESS = "vehicle_health_safety_sub_icon_success"
    const val ID_VEHICLE_HEALTH_SAFETY_SUB_ICON_FAIL = "vehicle_health_safety_sub_icon_fail"
    const val ID_VEHICLE_HEALTH_SERVICE_CAMP_ICON = "vehicle_health_service_camp_icon"
    const val ID_VEHICLE_HEALTH_SERVICE_CAMP_SUB_ICON_SUCCESS = "vehicle_health_service_camp_sub_icon_success"
    const val ID_VEHICLE_HEALTH_SERVICE_CAMP_SUB_ICON_FAIL = "vehicle_health_service_camp_sub_icon_fail"
    const val ID_VEHICLE_HEALTH_VEHICLE_ALERT_ICON = "vehicle_health_vehicle_alert_icon"
    const val ID_VEHICLE_HEALTH_VEHICLE_ALERT_SUB_ICON_SUCCESS = "vehicle_health_vehicle_alert_sub_icon_success"
    const val ID_VEHICLE_HEALTH_VEHICLE_ALERT_SUB_ICON_FAIL = "vehicle_health_vehicle_alert_sub_icon_fail"
    const val ID_VEHICLE_HEALTH_VEHICLE_REPORT_ICON = "vehicle_health_vehicle_report_icon"
    const val ID_VEHICLE_HEALTH_VEHICLE_REPORT_SUB_ICON_SUCCESS = "vehicle_health_vehicle_report_sub_icon_success"
    const val ID_VEHICLE_HEALTH_KEY_FOB_ICON = "vehicle_health_key_fob_icon"
    const val ID_VEHICLE_HEALTH_KEY_FOB_SUB_ICON_SUCCESS = "vehicle_health_key_fob_sub_icon_success"
    const val ID_VEHICLE_HEALTH_KEY_FOB_SUB_ICON_FAIL = "vehicle_health_key_fob_sub_icon_fail"
    const val ID_VEHICLE_HEALTH_VEHICLE_IMAGE = "vehicle_health_vehicle_image"

    // Vehicle Health detail
    const val ID_VEHICLE_HEALTH_DETAIL_BACK_BTN = "vehicle_health_detail_back_button"
    const val ID_VEHICLE_HEALTH_DETAIL_CALL_DEALER_BTN = "vehicle_health_detail_call_dealer"
    const val ID_VEHICLE_HEALTH_DETAIL_DATE_LAYOUT = "vehicle_health_detail_date_layout"
    const val ID_VEHICLE_HEALTH_DETAIL_OVERVIEW_CARD = "vehicle_health_detail_overview_tile"
    const val ID_VEHICLE_HEALTH_DETAIL_DESCRIPTION_CARD = "vehicle_health_detail_description_tile"
    const val ID_VEHICLE_HEALTH_DETAIL_REMEDY_CARD = "vehicle_health_detail_remedy_tile"

    // Pay Screen - TFS
    const val ID_TFS_OPTIONS_ICON = "tfs_options_icon"
    const val ID_TFS_MAKE_A_PAYMENT_CTA = "tfs_make_a_payment_button"
    const val ID_TFS_LEARN_MORE_CTA = "tfs_learn_more_button"
    const val ID_TFS_LINK_ACCOUNT_CTA = "tfs_link_account_button"
    const val ID_TFS_ACCESS_ACCOUNT_CONTINUE_CTA = "tfs_access_account_button"
    const val ID_TFS_ERROR_CARD_CTA = "tfs_error_button"
    const val ID_TFS_UNDER_MAINTENANCE = "tfs_under_maintenance"
    const val ID_TFS_ACCOUNT_LOCKED = "tfs_account_locked"
    const val ID_TFS_ACCOUNT_UNVERIFIED = "tfs_account_unverified"
    const val ID_TFS_LEARN_MORE_BODY_TEXT = "tfs_learn_more_body_text"
    const val ID_TFS_LEARN_MORE_TITLE_TEXT = "tfs_learn_more_title_text"
    const val ID_TFS_UNDER_MAINTENANCE_BODY_TEXT = "tfs_under_maintenance_body_text"
    const val ID_TFS_LINK_ACCOUNT_BODY_TEXT = "tfs_link_account_body_text"
    const val ID_TFS_LINK_ACCOUNT_TITLE_TEXT = "tfs_link_account_title_text"
    const val ID_TFS_ERROR_CARD_BODY_TEXT = "tfs_error_body_text"
    const val ID_TFS_ACCESS_ACCOUNT_BODY_TEXT = "tfs_access_account_body_text"
    const val ID_TFS_ACCESS_ACCOUNT_TITLE_TEXT = "tfs_access_account_title_text"
    const val ID_TFS_ACCOUNT_NUMBER_TEXT = "tfs_account_number_text"
    const val ID_TFS_PAYMENT_AMOUNT_TEXT = "tfs_payment_amount_text"
    const val ID_TFS_PAYMENT_DATE_TEXT = "tfs_payment_date_text"

    // FTUE
    const val FTUE_CLOSE_ICON_CTA = "ftue_close_icon_cta"
    const val FTUE_TITLE_TEXT = "ftue_title_text"
    const val FTUE_NOTE_TEXT = "ftue_note_text"
    const val FTUE_SKIP_TEXT_CTA = "ftue_skip_text_cta"
    const val FTUE_SEENEW_BUTTON_CTA = "ftue_seenew_button_cta"
    const val FTUE_OK_BUTTON_CTA = "ftue_ok_button_cta"
    const val FTUE_INDEX_TEXT = "ftue_index_text"
    const val FTUE_NEXT_BUTTON_CTA = "ftue_next_button_cta"
    const val FTUE_DONE_BUTTON_CTA = "ftue_done_button_cta"
    const val FTUE_VIDEO = "ftue_video"
    const val FTUE_IMAGE = "ftue_image"

    // Guest Driver-Advanced remote screen
    const val ID_GUEST_DRIVER_ADVANCE_ICON_CTA = "guest_driver_advance_icon_cta"
    const val ID_GUEST_DRIVER_ADVANCE_VALET_ICON_CTA = "guest_driver_valet_icon_cta"

    // Account Notification Content
    const val ID_ACCOUNT_DARK_MODE_ICON = "account_dark_mode_icon"
    const val ID_ACCOUNT_DARK_MODE_TEXT = "account_dark_mode_text"
    const val ID_ACCOUNT_DARK_MODE_SWITCH_ON = "account_dark_mode_switch_on"
    const val ID_ACCOUNT_DARK_MODE_SWITCH_OFF = "account_dark_mode_switch_off"

    // Dashboard Image
    const val ID_DASHBOARD_DISPLAY_IMAGE = "dashboard_display_image"
    const val ID_DASHBOARD_VEHICLE_NAME = "dashboard_vehicle_name"

    // VEHICLE STATUS
    const val ID_VEHICLE_STATUS_TIRE_PRESSURE_TILE_PRIMARY_ICON = "vehicle_status_tire_pressure_tile_primary_icon"
    const val ID_VEHICLE_STATUS_TIRE_PRESSURE_TILE_SECONDARY_ICON_ON = "vehicle_status_tire_pressure_tile_secondary_icon_on"
    const val ID_VEHICLE_STATUS_TIRE_PRESSURE_TILE_SECONDARY_ICON_OFF = "vehicle_status_tire_pressure_tile_secondary_icon_off"
    const val ID_VEHICLE_STATUS_TIRE_PRESSURE_TILE_TITLE = "vehicle_status_tire_pressure_tile_title"
    const val ID_VEHICLE_STATUS_TIRE_PRESSURE_TILE_SUB_TITLE = "vehicle_status_tire_pressure_tile_sub_title"

    const val ID_VEHICLE_STATUS_DOOR_TILE_PRIMARY_ICON = "vehicle_status_door_tile_primary_icon"
    const val ID_VEHICLE_STATUS_DOOR_TILE_SECONDARY_ICON_ON = "vehicle_status_door_tile_secondary_icon_on"
    const val ID_VEHICLE_STATUS_DOOR_TILE_SECONDARY_ICON_OFF = "vehicle_status_door_tile_secondary_icon_off"
    const val ID_VEHICLE_STATUS_DOOR_TILE_TITLE = "vehicle_status_door_tile_title"
    const val ID_VEHICLE_STATUS_DOOR_TILE_SUB_TITLE = "vehicle_status_door_tile_sub_title"
    const val ID_VEHICLE_STATUS_DOOR_TILE_LOCK_BUTTON_CTA = "vehicle_status_door_lock_button_cta"

    // TAIL GATE
    const val ID_VEHICLE_STATUS_TAILGATE_TILE_PRIMARY_ICON = "vehicle_status_tailgate_tile_primary_icon"
    const val ID_VEHICLE_STATUS_TAILGATE_TILE_SECONDARY_ICON_ON = "vehicle_status_tailgate_tile_secondary_icon_on"
    const val ID_VEHICLE_STATUS_TAILGATE_TILE_SECONDARY_ICON_OFF = "vehicle_status_tailgater_tile_secondary_icon_off"
    const val ID_VEHICLE_STATUS_TAILGATE_TILE_TITLE = "vehicle_status_tailgate_tile_title"
    const val ID_VEHICLE_STATUS_TAILGATE_TILE_SUB_TITLE = "vehicle_status_tailgate_tile_sub_title"
    const val ID_VEHICLE_STATUS_TAILGATE_TILE_LOCK_BUTTON_CTA = "vehicle_status_tailgate_lock_button_cta"

    const val ID_VEHICLE_STATUS_WINDOW_TILE_PRIMARY_ICON = "vehicle_status_window_tile_primary_icon"
    const val ID_VEHICLE_STATUS_WINDOW_TILE_SECONDARY_ICON_ON = "vehicle_status_window_tile_secondary_icon_on"
    const val ID_VEHICLE_STATUS_WINDOW_TILE_SECONDARY_ICON_OFF = "vehicle_status_window_tile_secondary_icon_off"
    const val ID_VEHICLE_STATUS_WINDOW_TILE_TITLE = "vehicle_status_window_tile_title"
    const val ID_VEHICLE_STATUS_WINDOW_TILE_SUB_TITLE = "vehicle_status_window_tile_sub_title"

    const val ID_VEHICLE_STATUS_REFRESH_ICON = "vehicle_status_refresh_icon"
    const val ID_VEHICLE_STATUS_TIRE_PRESSURE_LAST_UPDATED_TIME = "vehicle_status_tire_pressure_last_updated_time_text"
    const val ID_VEHICLE_STATUS_INFORMATION_LAST_UPDATED_TIME = "vehicle_status_information_last_updated_time_text"
    const val ID_VEHICLE_STATUS_VEHICLE_IMAGE = "vehicle_status_vehicle_image"

    const val ID_VEHICLE_STATUS_FRONT_LEFT_TIRE_PRESSURE_VALUE = "vehicle_status_front_left_tirepressure_value_text"
    const val ID_VEHICLE_STATUS_FRONT_RIGHT_TIRE_PRESSURE_VALUE = "vehicle_status_front_right_tirepressure_value_text"
    const val ID_VEHICLE_STATUS_REAR_LEFT_TIRE_PRESSURE_VALUE = "vehicle_status_rear_left_tirepressure_value_text"
    const val ID_VEHICLE_STATUS_REAR_RIGHT_TIRE_PRESSURE_VALUE = "vehicle_status_rear_right_tirepressure_value_text"

    const val ID_VEHICLE_STATUS_FRONT_LEFT_TIRE_PRESSURE_UNIT = "vehicle_status_front_left_tirepressure_unit_text"
    const val ID_VEHICLE_STATUS_FRONT_RIGHT_TIRE_PRESSURE_UNIT = "vehicle_status_front_right_tirepressure_unit_text"
    const val ID_VEHICLE_STATUS_REAR_LEFT_TIRE_PRESSURE_UNIT = "vehicle_status_rear_left_tirepressure_unit_text"
    const val ID_VEHICLE_STATUS_REAR_RIGHT_TIRE_PRESSURE_UNIT = "vehicle_status_rear_right_tirepressure_unit_text"

    // Subscription List screen
    const val ID_SUBSCRIPTION_LIST_TITLE = "subscription_list_title"
    const val ID_SUBSCRIPTION_LIST_HEADER_TITLE = "subscription_list_header_title"

    // Subscription Shop Screen
    const val ID_SHOP_SUBSCRIPTION_TITLE = "shop_subscription_title"
    const val ID_SHOP_SUBSCRIPTION_SUB_TITLE = "shop_subscription_sub_title"
    const val ID_SHOP_SUBSCRIPTION_ICON = "shop_subscription_icon"
    const val ID_SHOP_PARTS_ACCESSORIES_TITLE = "shop_parts_accessories_title"
    const val ID_SHOP_PARTS_ACCESSORIES_SUB_TITLE = "shop_parts_accessories_sub_title"
    const val ID_SHOP_PARTS_ACCESSORIES_ICON = "shop_parts_accessories_icon"
    const val ID_SHOP_INSURANCE_TITLE = "shop_parts_insurance_title"
    const val ID_SHOP_INSURANCE_TITLE_SUB_TITLE = "shop_parts_insurance_sub_title"
    const val ID_SHOP_INSURANCE_ICON = "shop_insurance_icon"
    const val ID_SHOP_SIRIUS_XM_TITLE = "shop_sirius_xm_title"
    const val ID_SHOP_SIRIUS_XM_SUB_TITLE = "shop_sirius_xm_sub_title"
    const val ID_SHOP_SIRIUS_XM_ICON = "shop_sirius_xm_sub_icon"
    const val ID_SHOP_SIRIUS_XM_VEHICLE_DISPLAY_IMAGE = "shop_sirius_xm_vehicle_display_image"
    const val ID_SHOP_SIRIUS_XM_ALERT = "shop_sirius_xm_alert"
    const val ID_SHOP_ANNOUNCEMENT_ICON = "shop_announcement_icon"
    const val ID_SHOP_ANNOUNCEMENT_TITLE = "shop_announcement_title"
    const val ID_SHOP_ANNOUNCEMENT_SUB_TITLE = "shop_announcement_sub_title"

    // Fuel Widget

    const val ID_FUEL_VIEW_DISTANCE_TO_EMPTY_TEXT = "fuel_view_distance_to_empty_text"
    const val ID_FUEL_VIEW_FUEL_BAR_VIEW = "fuel_view_fuel_bar_view"
    const val ID_FUEL_VIEW_FIND_STATIONS_CTA = "fuel_view_find_stations_button_cta"
    const val ID_FUEL_VIEW_CHARGE_NOW_CTA = "fuel_view_charge_now_button_cta"
    const val ID_FUEL_VIEW_UNPLUG_CTA = "fuel_view_unplug_button_cta"
    const val ID_FUEL_VIEW_BATTERY_ICON = "fuel_view_battery_icon"
    const val ID_FUEL_VIEW_GAS_ICON = "fuel_view_gas_icon"
    const val ID_FUEL_VIEW_PERCENT_TEXT = "fuel_view_percent_text"
    const val ID_FUEL_VIEW_RANGE_VALUE_TEXT = "fuel_view_range_value_text"
    const val ID_FUEL_VIEW_PHEV_RANGE_VALUE_TEXT = "fuel_view_phev_range_value_text"
    const val ID_FUEL_VIEW_RANGE_UNIT_TEXT = "fuel_view_range_unit_text"
    const val ID_FUEL_VIEW_PHEV_RANGE_UNIT_TEXT = "fuel_view_phev_range_unit_text"

    const val ID_FUEL_VIEW_CHARGE_DETAIL_CTA = "fuel_view_charge_detail_button_cta"
    const val ID_FUEL_VIEW_CHARGING_CTA = "fuel_view_charging_button_cta"
    const val ID_FUEL_VIEW_CLIMATE_ICON = "fuel_view_climate_icon"
    const val ID_FUEL_VIEW_CHEVRON_ICON = "fuel_view_chevron_icon"

    const val ID_SUB_SNIPPET_CARD = "sub_snippet_card"
    const val ID_SUB_SNIPPET_CLOSE_ICON = "sub_snippet_close_icon"

    // Announcement
    const val ID_ANNOUNCEMENT_SUBSCRIPTION_CARD = "announcement_subscription_card"
    const val ID_ANNOUNCEMENT_SUBSCRIPTION_BUTTON_CTA = "announcement_subscription_button_cta"
    const val ID_ANNOUNCEMENT_CLEAN_ASSIST_CARD = "announcement_clean_assist_card"
    const val ID_ANNOUNCEMENT_CLEAN_ASSIST_BUTTON_CTA = "announcement_clean_assist_button_cta"
    const val ID_ANNOUNCEMENT_CLEAN_ASSIST_O32D_VEHICLE_CARD = "announcement_clean_assist_o32dvehicle_card"
    const val ID_ANNOUNCEMENT_CLEAN_ASSIST_O32D_VEHICLE_BUTTON_CTA = "announcement_clean_assist_o32dvehicle_button_cta"
    const val ID_ANNOUNCEMENT_EV_SWAP_CARD = "announcement_ev_swap_card"
    const val ID_ANNOUNCEMENT_EV_SWAP_BUTTON_CTA = "announcement_ev_swap_button_cta"
    const val ID_ANNOUNCEMENT_MARKETING_BANNERS_CARD = "announcement_marketing_banners_card"
    const val ID_ANNOUNCEMENT_MARKETING_CONSENTS_CARD = "announcement_marketing_consents_card"
    const val ID_ANNOUNCEMENT_MARKETING_CONSENTS_BUTTON_CTA = "announcement_marketing_consents_button_cta"
    const val ID_ANNOUNCEMENT_WIFI_TRIALS_CARD = "announcement_wifi_trials_card"
    const val ID_ANNOUNCEMENT_WIFI_TRIALS_BUTTON_CTA = "announcement_wifi_trials_button_cta"
    const val ID_ANNOUNCEMENT_LIST_BACK_CTA = "announcement_list_back_cta"
    const val ID_ANNOUNCEMENT_LIST_TITLE = "announcement_list_title"
    const val ID_ANNOUNCEMENT_LIST_CARD_CTA = "announcement_list_card_cta"

    // Common
    const val ID_NO_VEHICLE_IMAGE = "no_vehicle_image"

    const val ID_VEHICLE_STATUS_BOTTOM_SHEET_TITLE_TEXT = "vehicle_status_bottom_sheet_title_text"
    const val ID_VEHICLE_STATUS_BOTTOM_SHEET_DESCRIPTION_TEXT = "vehicle_status_bottom_sheet_description_text"
    const val ID_VEHICLE_STATUS_BOTTOM_SHEET_DISMISS_BUTTON = "vehicle_status_bottom_sheet_dismiss_button"

    const val REMOTE_PROGRESS_TEXT = "remote_progress_text"
    const val REMOTE_PROGRESS_VIEW = "remote_progress_view"

    // CAS
    const val ID_CAS_REMOTE_SCREEN_CARD_CTA = "cas_remote_screen_card"
    const val ID_CAS_VEHICLE_STATUS_SCREEN_CARD_CTA = "cas_vehicle_status_screen_card"
    const val ID_CAS_DETAIL_APP_BAR_TEXT = "cas_detail_app_bar_text"
    const val ID_CAS_DETAIL_TITLE_TEXT = "cas_detail_title_text"
    const val ID_CAS_DETAIL_DESCRIPTION_TEXT = "cas_detail_description_text"
    const val ID_CAS_REPORT_APP_BAR_TEXT = "cas_report_app_bar_text"
    const val ID_CAS_REPORT_CANCEL_CTA = "cas_report_cancel_cta"
    const val ID_CAS_REPORT_REPORT_CTA = "cas_report_report_cta"

    const val ID_VEHICLE_SOFTWARE_BACK_ARROW_CTA = "vehicle_software_back_button"
    const val ID_VEHICLE_SOFTWARE_STATUS_TITLE_TEXT = "vehicle_software_status_title_text"
    const val ID_VEHICLE_SOFTWARE_STATUS_SUB_TITLE_TEXT = "vehicle_software_status_sub_title_text"
    const val ID_VEHICLE_SOFTWARE_INSTRUCTION_TITLE_TEXT = "vehicle_software_instruction_title_text"
    const val ID_VEHICLE_SOFTWARE_INSTRUCTION_SUB_TITLE_TEXT = "vehicle_software_instruction_sub_title_text"
    const val ID_VEHICLE_SOFTWARE_WHATS_NEW_TITLE_TEXT = "vehicle_software_whats_new_title_text"
    const val ID_VEHICLE_SOFTWARE_WHATS_NEW_SUB_TITLE_TEXT = "vehicle_software_whats_new_sub_title_text"
    const val ID_VEHICLE_SOFTWARE_WORKING_TIME_TITLE_TEXT = "vehicle_software_working_time_title_text"
    const val ID_VEHICLE_SOFTWARE_WORKING_TIME_SUB_TITLE_TEXT = "vehicle_software_working_time_sub_title_text"
    const val ID_VEHICLE_SOFTWARE_PREVIOUS_UPDATE_TITLE_TEXT = "vehicle_software_previous_update_title_text"
    const val ID_VEHICLE_SOFTWARE_PREVIOUS_UPDATE_SUB_TITLE_TEXT = "vehicle_software_previous_update_sub_title_text"

    // Climate
    const val ID_CLIMATE_TITLE_TEXT = "climate_title"
    const val ID_CLIMATE_TAB_SCHEDULE_TITLE_TEXT = "climate_tab_schedule_title"
    const val ID_CLIMATE_TAB_SETTINGS_TITLE_TEXT = "climate_tab_settings_title"
    const val ID_CLIMATE_REMAINING_TIME_TEXT = "climate_remaining_time_text"
    const val ID_CLIMATE_CUSTOM_CLIMATE_TITLE_TEXT = "climate_custom_climate_title"
    const val ID_CLIMATE_CUSTOM_CLIMATE_SUBTITLE_TEXT = "climate_custom_climate_subtitle"
    const val ID_CLIMATE_CUSTOM_CLIMATE_SWITCH = "climate_custom_climate_switch"
    const val ID_CLIMATE_TEMP_VALUE_TEXT = "climate_temperature_value_text"
    const val ID_CLIMATE_TEMP_SLIDER = "climate_temperature_slider"
    const val ID_CLIMATE_AIR_FLOW_TITLE_TEXT = "climate_air_flow_title"
    const val ID_CLIMATE_AIR_FLOW_ICON = "climate_air_flow_icon"
    const val ID_CLIMATE_FAN_SPEED_TITLE_TEXT = "climate_fan_speed_title"
    const val ID_CLIMATE_FAN_SPEED_VALUE_TEXT = "climate_fan_speed_value_text"
    const val ID_CLIMATE_FAN_SPEED_ICON = "climate_fan_speed_icon"
    const val ID_CLIMATE_FAN_SPEED_INCREASE_ICON = "climate_fan_speed_increase_icon"
    const val ID_CLIMATE_FAN_SPEED_DECREASE_ICON = "climate_fan_speed_decrease_icon"
    const val ID_CLIMATE_STEERING_WHEEL_TITLE_TEXT = "climate_steering_wheel_title"
    const val ID_CLIMATE_STEERING_WHEEL_SWITCH = "climate_steering_wheel_switch"
    const val ID_CLIMATE_STEERING_WHEEL_ICON = "climate_steering_wheel_icon"
    const val ID_CLIMATE_STEERING_WHEEL_HEATING_TEXT = "climate_steering_wheel_heating_text"
    const val ID_CLIMATE_DEFROST_TITLE_TEXT = "climate_defrost_title"
    const val ID_CLIMATE_DEFROST_ICON = "climate_defrost_icon"
    const val ID_CLIMATE_DEFROST_FRONT_SWITCH = "climate_defrost_front_switch"
    const val ID_CLIMATE_DEFROST_REAR_SWITCH = "climate_defrost_rear_switch"
    const val ID_CLIMATE_DEFROST_FRONT_TEXT = "climate_defrost_front_text"
    const val ID_CLIMATE_DEFROST_REAR_TEXT = "climate_defrost_rear_text"
    const val ID_CLIMATE_AIR_CIRCULATION_TITLE_TEXT = "climate_air_circulation_title"
    const val ID_CLIMATE_AIR_CIRCULATION_ICON = "climate_air_circulation_icon"
    const val ID_CLIMATE_AIR_CIRCULATION_SLIDER = "climate_air_circulation_slider"
    const val ID_CLIMATE_AIR_CIRCULATION_INSIDE_TEXT = "climate_air_circulation_inside_text"
    const val ID_CLIMATE_AIR_CIRCULATION_OUTSIDE_TEXT = "climate_air_circulation_outside_text"
    const val ID_CLIMATE_START_CTA = "climate_start_button_cta"
    const val ID_CLIMATE_STOP_CTA = "climate_stop_button_cta"
    const val ID_CLIMATE_SAVE_CTA = "climate_save_button_cta"

    const val ID_CLIMATE_SHEET_OK_CTA = "climate_sheet_ok_cta"
    const val ID_CLIMATE_SHEET_KEEP_ON_CTA = "climate_sheet_keepon_cta"
    const val ID_CLIMATE_SHEET_TURN_OFF_CTA = "climate_sheet_turnoff_cta"
    const val ID_CLIMATE_SHEET_CANCEL_CTA = "climate_sheet_cancel_cta"
    const val ID_CLIMATE_SHEET_APPLY_CTA = "climate_sheet_apply_cta"

    const val ID_CLIMATE_SEAT_HEAT_ICON = "climate_seat_heat_icon"
    const val ID_CLIMATE_SEAT_VENT_ICON = "climate_seat_vent_icon"
    const val ID_CLIMATE_SEAT_OFF_ICON = "climate_seat_off_icon"
    const val ID_CLIMATE_SEAT_HEAT_TEXT = "climate_seat_heat_text"
    const val ID_CLIMATE_SEAT_VENT_TEXT = "climate_seat_vent_text"
    const val ID_CLIMATE_SEAT_OFF_TEXT = "climate_seat_off_text"

    const val ID_CLIMATE_AIR_FLOW_FRONT_ICON = "climate_air_flow_front_icon"
    const val ID_CLIMATE_AIR_FLOW_FEET_ICON = "climate_air_flow_feet_icon"
    const val ID_CLIMATE_AIR_FLOW_DEFROST_ICON = "climate_air_flow_defrost_icon"
    const val ID_CLIMATE_AIR_FLOW_ALL_ICON = "climate_air_flow_all_icon"

    // Climate schedule
    const val ID_CLIMATE_SCHEDULE_TITLE = "climate_schedule_title"
    const val ID_CLIMATE_SCHEDULE_DATE_TEXT = "climate_schedule_date_text"
    const val ID_CLIMATE_SCHEDULE_TIME_TEXT = "climate_schedule_time_text"
    const val ID_CLIMATE_SCHEDULE_SWITCH = "climate_schedule_switch"
    const val ID_CLIMATE_SCHEDULE_NAVIGATION_ICON = "climate_schedule_navigation_icon"
    const val ID_CLIMATE_SCHEDULE_DETAIL_TIME_TITLE = "climate_schedule_detail_time_title"
    const val ID_CLIMATE_SCHEDULE_DETAIL_TIME_VALUE = "climate_schedule_detail_time_value"
    const val ID_CLIMATE_SCHEDULE_DETAIL_TIME_AM = "climate_schedule_detail_time_am_switch"
    const val ID_CLIMATE_SCHEDULE_DETAIL_TIME_PM = "climate_schedule_detail_time_pm_switch"
    const val ID_CLIMATE_SCHEDULE_DETAIL_DATE_TITLE = "climate_schedule_detail_date_title"
    const val ID_CLIMATE_SCHEDULE_DETAIL_DATE_VALUE = "climate_schedule_detail_date_value"
    const val ID_CLIMATE_SCHEDULE_DETAIL_DAYS_TITLE = "climate_schedule_detail_day_of_week_title"
    const val ID_CLIMATE_SCHEDULE_DETAIL_DAYS_MONDAY = "climate_schedule_detail_day_monday_view"
    const val ID_CLIMATE_SCHEDULE_DETAIL_DAYS_TUESDAY = "climate_schedule_detail_day_tuesday_view"
    const val ID_CLIMATE_SCHEDULE_DETAIL_DAYS_WEDNESDAY = "climate_schedule_detail_day_wednesday_view"
    const val ID_CLIMATE_SCHEDULE_DETAIL_DAYS_THURSDAY = "climate_schedule_detail_day_thursday_view"
    const val ID_CLIMATE_SCHEDULE_DETAIL_DAYS_FRIDAY = "climate_schedule_detail_day_friday_view"
    const val ID_CLIMATE_SCHEDULE_DETAIL_DAYS_SATURDAY = "climate_schedule_detail_day_saturday_view"
    const val ID_CLIMATE_SCHEDULE_DETAIL_DAYS_SUNDAY = "climate_schedule_detail_day_sunday_view"
    const val ID_CLIMATE_SCHEDULE_DETAIL_CLIMATE_CARD = "climate_schedule_detail_climate_card"

    const val ID_CLIMATE_SCHEDULE_DELETE_CTA = "climate_schedule_delete_cta"
    const val ID_CLIMATE_SCHEDULE_CREATE_CTA = "climate_schedule_create_cta"
    const val ID_CLIMATE_SCHEDULE_DELETE_SHEET_CANCEL_CTA = "climate_schedule_delete_sheet_cancel_cta"
    const val ID_CLIMATE_SCHEDULE_DELETE_SHEET_REMOVE_CTA = "climate_schedule_delete_sheet_remove_cta"

    const val ID_VEHICLE_SOFTWARE_21MM_BACK_ARROW_CTA = "vehicle_software_21mm_back_button"
    const val ID_VEHICLE_SOFTWARE_21MM_RELEASE_NOTE_TITLE_TEXT = "vehicle_software_21mm_release_note_title_text"
    const val ID_VEHICLE_SOFTWARE_21MM_UPDATE_HISTORY_TITLE_TEXT = "vehicle_software_21mm_update_history_title_text"
    const val ID_VEHICLE_SOFTWARE_21MM_UPDATE_AVAILABLE_TITLE_TEXT = "vehicle_software_21mm_update_available_title_text"
    const val ID_VEHICLE_SOFTWARE_21MM_WHATS_NEW_TITLE_TEXT = "vehicle_software_21mm_whats_new_title_text"
    const val ID_VEHICLE_SOFTWARE_21MM_CONTINUE_BUTTON = "vehicle_software_21mm_continue_button"
    const val ID_VEHICLE_SOFTWARE_21MM_CAUTION_TITLE_TEXT = "vehicle_software_21mm_caution_title_text"
    const val ID_VEHICLE_SOFTWARE_21MM_VERSION_TITLE_TEXT = "vehicle_software_21mm_version_title_text"
    const val ID_VEHICLE_SOFTWARE_21MM_IMPORTANT_TITLE_TEXT = "vehicle_software_21mm_important_title_text"

    const val GUEST_TITLE = "guest_title"

    // Speed
    const val ID_SPEED_TITLE = "speed_title"
    const val ID_SPEED_BACK_BUTTON = "speed_back_button"
    const val ID_SPEED_SWITCH = "speed_switch"
    const val ID_SPEED_SWITCH_ICON = "speed_switch_icon"
    const val ID_SPEED_SWITCH_TITLE = "speed_switch_title"
    const val ID_SPEED_SWITCH_SUB_TITLE = "speed_switch_sub_title"

    // Area
    const val ID_AREA_TITLE = "area_title"
    const val ID_AREA_BACK_BUTTON = "area_back_button"
    const val ID_AREA_SWITCH = "area_switch"
    const val ID_AREA_SWITCH_ICON = "area_switch_icon"
    const val ID_AREA_SWITCH_TITLE = "area_switch_title"
    const val ID_AREA_SWITCH_SUB_TITLE = "area_switch_sub_title"

    // Miles
    const val ID_MILES_TITLE = "miles_title"
    const val ID_MILES_BACK_BUTTON = "miles_back_button"
    const val ID_MILES_SWITCH = "miles_switch"
    const val ID_MILES_SWITCH_ICON = "miles_switch_icon"
    const val ID_MILES_SWITCH_TITLE = "miles_switch_title"
    const val ID_MILES_SWITCH_SUB_TITLE = "miles_switch_sub_title"
    const val ID_MILES_TIME_PICKER = "miles_time_picker"
    const val ID_MILES_TIME_AM = "miles_time_am"
    const val ID_MILES_TIME_PM = "miles_time_pm"

    // Miles
    const val ID_TIME_TITLE = "time_title"
    const val ID_TIME_BACK_BUTTON = "time_back_button"
    const val ID_TIME_SWITCH = "time_switch"
    const val ID_TIME_SWITCH_ICON = "time_switch_icon"
    const val ID_TIME_SWITCH_TITLE = "time_switch_title"
    const val ID_TIME_SWITCH_SUB_TITLE = "time_switch_sub_title"
    const val ID_TIME_PICKER = "time_picker"
    const val ID_TIME_AM = "time_am"
    const val ID_TIME_PM = "time_pm"

    // Curfew
    const val ID_CURFEW_TITLE = "curfew_title"
    const val ID_CURFEW_BACK_BUTTON = "curfew_back_button"
    const val ID_CURFEW_SWITCH = "curfew_switch"
    const val ID_CURFEW_SWITCH_ICON = "curfew_switch_icon"
    const val ID_CURFEW_SWITCH_TITLE = "curfew_switch_title"
    const val ID_CURFEW_SWITCH_SUB_TITLE = "curfew_switch_sub_title"
    const val ID_CURFEW_END_TIME_PICKER_TITLE = "curfew_end_time_picker_title"
    const val ID_CURFEW_END_TIME_PICKER = "curfew_end_time_picker"
    const val ID_CURFEW_END_TIME_AM = "curfew_end_time_am"
    const val ID_CURFEW_END_TIME_PM = "curfew_end_time_pm"
    const val ID_CURFEW_START_TIME_PICKER_TITLE = "curfew_start_time_picker_title"
    const val ID_CURFEW_START_TIME_PICKER = "curfew_start_time_picker"
    const val ID_CURFEW_START_TIME_AM = "curfew_start_time_am"
    const val ID_CURFEW_START_TIME_PM = "curfew_start_time_pm"

    const val ID_GUEST_AVATAR_ICON = "guest_avatar_icon"

    // Driving Limits List
    const val ID_DL_SAVE_CTA = "dl_save_cta"

    // Dealer Service Appointment
    const val ID_DEALER_SERVICE_APPOINTMENT_LIST_BACK_CTA = "dealer_service_appointment_back_cta"
    const val ID_DEALER_SERVICE_LANDING_PAGE_REFRESH = "dealer_service_landing_page_refresh"
    const val ID_DSA_NAVIGATION_BAR = "DealerServiceAppointment_NavigationBar"
    const val ID_DSA_PREFERRED_DEALER_TILE = "DealerServiceAppointment_PreferredDealerTileText"
    const val ID_DSA_NO_PREFERRED_DEALER_TILE = "DealerServiceAppointment_NoPreferredDealerTileText"
    const val ID_DSA_MAP_VIEW = "DealerServiceAppointment_MapView"
    const val ID_DSA_MAKE_APPOINTMENT_CARD = "DealerServiceAppointment_MakeAppointmentCardButton"
    const val ID_DSA_CALL_DEALER_CARD = "DealerServiceAppointment_CallDealerCardButton"
    const val ID_DSA_APPOINTMENT_DATE_TILE = "DealerServiceAppointment_AppointmentDateTileText"
    const val ID_DSA_MAINTENANCE_SCHEDULE = "DealerServiceAppointment_MaintenanceScheduleButton"
    const val ID_DSA_APPOINTMENTS_SECTION = "DealerServiceAppointment_AppointmentsSectionText"
    const val ID_DSA_PROGRESS_VIEW = "DealerServiceAppointment_ProgressView"
    const val ID_DSA_DEALER_SEARCH_BAR = "DealerSearch_NavigationBar"
    const val ID_DSA_DEALER_SEARCH_BACK_CTA = "dealer_search_back_cta"
    const val ID_DSA_DEALER_SEARCH_FILTER_OPTION = "DealerSearchFilterOption"
    const val ID_DSA_DEALER_SEARCH_DEALER_LIST_DRAG_HANDLE = "DealerSearchListDragHandle"
    const val ID_DEALER_SERVICE_APPOINTMENT_TITLE = "dealer_service_appointment_title"
    const val ID_DSA_MAINTENANCE_SCHEDULE_NAVIGATION_BAR = "MaintenanceSchedule_NavigationBar"
    const val ID_DSA_MAINTENANCE_SCHEDULE_BACK_CTA = "maintenance_schedule_back_cta"

    // Dealer Service Call Dealer/Make Appointment
    const val ID_DEALER_SERVICE_CALL_AGENT_OR_MAKE_APPOINTMENT = "dealer_service_call_agent_or_maker_appointment"
    const val ID_DEALER_SERVICE_PRIVACY_STATEMENT = "dealer_service_privacy_statement"
    const val ID_DEALER_SERVICE_PRIVACY_NOTICE = "dealer_service_privacy_notice"
    const val ID_DEALER_APPOINTMENT_ODOMETER_TITLE = "dealer_appointment_odometer_title"
    const val ID_DEALER_APPOINTMENT_ODOMETER_BACK_CTA = "dealer_appointment_odometer_back_cta"
    const val ID_DSA_MAINTENANCE_SCHEDULE_DETAIL_NAVIGATION_BAR = "MaintenanceSchedule_Detail_NavigationBar"
    const val ID_DSA_MAINTENANCE_SCHEDULE_DETAIL_BACK_CTA = "maintenance_schedule_detail_back_cta"

    // Dealer Service Confirm Appointment
    const val ID_DSA_CONFIRM_APPOINTMENT_NAVIGATION_BAR = "ConfirmAppointment_NavigationBar"
    const val ID_DSA_CONFIRM_APPOINTMENT_BACK_CTA = "confirm_appointment_back_cta"

    // Dealer Appointments screen
    const val ID_DSA_APPOINTMENTS_LIST = "dealer_service_appointments_list"
    const val ID_DSA_APPOINTMENTS_UPCOMING_TAB = "dealer_service_appointments_upcoming_tab"
    const val ID_DSA_APPOINTMENTS_PAST_TAB = "dealer_service_appointments_past_tab"
    const val ID_DSA_APPOINTMENT_TILE = "dealer_service_appointment_tile"
    const val ID_DSA_APPOINTMENT_DATE = "dealer_service_appointment_date_text"
    const val ID_DSA_APPOINTMENT_MONTH = "dealer_service_appointment_month_text"
    const val ID_DSA_APPOINTMENT_TIME = "dealer_service_appointment_time_text"
    const val ID_DSA_APPOINTMENT_DEALER = "dealer_service_appointment_dealer_text"
    const val ID_DSA_APPOINTMENT_SERVICES = "dealer_service_appointment_services_text"
    const val ID_DSA_NO_APPOINTMENT_TITLE = "dealer_service_no_appointment_title_text"
    const val ID_DSA_NO_APPOINTMENT_DESCRIPTION = "dealer_service_no_appointment_description_text"
    const val ID_DSA_NO_APPOINTMENT_ICON = "dealer_service_no_appointment_icon"
    const val ID_DSA_MAKE_APPOINTMENT_SHEET_TITLE = "dealer_service_make_appointment_sheet_title_text"
    const val ID_DSA_MAKE_APPOINTMENT_SHEET_DESCRIPTION = "dealer_service_make_appointment_sheet_description_text"
    const val ID_DSA_MAKE_APPOINTMENT_CHANGE_DEALER = "dealer_service_make_appointment_change_dealer_text"
    const val ID_DSA_DEALER_TIME_ZONE = "dealer_service_dealer_time_zone_text"

    // Dealer Appointment Service Screen
    const val ID_DEALER_APPOINTMENT_SERVICE_TITLE = "dealer_appointment_service_title"
    const val ID_DEALER_APPOINTMENT_SERVICE_BACK_CTA = "dealer_appointment_service_back_cta"
    const val ID_DEALER_APPOINTMENT_SERVICE_PREVIOUS = "dealer_appointment_service_previous"
    const val ID_DEALER_APPOINTMENT_SERVICE_CURRENT = "dealer_appointment_service_current"
    const val ID_DEALER_APPOINTMENT_SERVICE_NEXT = "dealer_appointment_service_next"
    const val ID_DEALER_APPOINTMENT_SERVICE_ALL = "dealer_appointment_service_all"
    const val ID_DEALER_APPOINTMENT_SERVICE_SEARCH = "dealer_appointment_service_search"

    // Service History
    const val ID_SERVICE_HISTORY_TITLE = "service_history_title"
    const val ID_SERVICE_HISTORY_BACK_CTA = "service_history_back_cta"
    const val ID_SERVICE_HISTORY_LIST = "service_history_list"
    const val ID_SERVICE_HISTORY_DEALER = "service_history_dealer"
    const val ID_SERVICE_HISTORY_SERVICE = "service_history_service"
    const val ID_SERVICE_HISTORY_NO_HISTORY_TITLE = "service_history_no_history_title"
    const val ID_SERVICE_HISTORY_NO_HISTORY_DESCRIPTION = "service_history_no_history_description"
    const val ID_SERVICE_HISTORY_NO_HISTORY_ICON = "service_history_no_history_icon"
    const val ID_SERVICE_HISTORY_ADD_SERVICE_CTA = "service_history_add_service_cta"
    const val ID_SERVICE_HISTORY_ADD_SERVICE_SHEET_TITLE = "service_history_add_service_sheet_title"
    const val ID_SERVICE_HISTORY_ADD_SERVICE_SHEET_DESCRIPTION = "service_history_add_service_sheet_description"
    const val ID_SERVICE_HISTORY_ODOMETER_TITLE = "service_history_odometer_title"
    const val ID_SERVICE_HISTORY_NOTES_TITLE = "service_history_notes_title"
    const val ID_SERVICE_HISTORY_SAVE_CTA = "service_history_save_cta"
    const val ID_SERVICE_HISTORY_DELETE_DIALOG_CANCEL_CTA = "service_history_delete_dialog_cancel_cta"
    const val ID_SERVICE_HISTORY_DELETE_DIALOG_DELETE_CTA = "service_history_delete_dialog_delete_cta"
    const val ID_SERVICE_HISTORY_DELETE_CTA = "service_history_delete_cta"
    const val ID_SERVICE_HISTORY_EDIT_CTA = "service_history_edit_cta"
    const val ID_SERVICE_HISTORY_CALL_DEALER_CTA = "service_history_call_dealer_cta"
    const val ID_SERVICE_HISTORY_DEALER_LOCATION = "service_history_dealer_location"
    const val ID_SERVICE_HISTORY_DETAIL_TITLE = "service_history_detail_title"

    // Dealer Appointment Transportation Screen
    const val ID_DEALER_APPOINTMENT_TRANSPORTATION_TITLE = "dealer_appointment_transportation_title"
    const val ID_DEALER_APPOINTMENT_TRANSPORTATION_BACK_CTA = "dealer_appointment_transportation_back_cta"
    const val ID_DEALER_APPOINTMENT_PICKUP_TITLE = "dealer_appointment_pickup_title"
    const val ID_DEALER_APPOINTMENT_PICKUP_BACK_CTA = "dealer_appointment_pickup_back_cta"

    const val ID_DEALER_APPOINTMENT_DELIVERY_TITLE = "dealer_appointment_delivery_title"
    const val ID_DEALER_APPOINTMENT_DELIVERY_BACK_CTA = "dealer_appointment_delivery_back_cta"

    // Dealer Appointment Date and TIme Screen
    const val ID_DEALER_APPOINTMENT_DATE_TIME_TITLE = "dealer_appointment_date_time_title"
    const val ID_DEALER_APPOINTMENT_DATE_TIME_BACK_BTN = "dealer_appointment_date_time_back_btn"
    const val ID_DEALER_APPOINTMENT_DATE_TIME_CONTINUE_BTN = "dealer_appointment_date_time_continue_btn"
    const val ID_DEALER_APPOINTMENT_DATE_TIME_TIME_SLOTS = "dealer_appointment_date_time_time_slot_btn"
    const val ID_DEALER_APPOINTMENT_DATE_TIME_DATE_PICKER = "dealer_appointment_date_time_date_picker"
    const val ID_DEALER_APPOINTMENT_DATE_TIME_TIME_SLOT_PICKER = "dealer_appointment_date_time_time_slot_picker"

    // Appointment Details
    const val ID_APPOINTMENT_DETAILS_APP_BAR_TEXT = "appointment_details_app_bar_text"
    const val ID_APPOINTMENT_DETAILS_BACK_CTA = "appointment_details_back_cta"
    const val ID_APPOINTMENT_DETAILS_ADD_TO_CALENDAR_ICON_CTA = "appointment_details_add_to_calendar_icon_cta"
    const val ID_APPOINTMENT_DETAILS_DELETE_BUTTON_CTA = "appointment_details_delete_button_cta"
    const val ID_APPOINTMENT_DETAILS_DELETE_CANCEL_TEXT_CTA = "appointment_details_delete_cancel_text_cta"
    const val ID_APPOINTMENT_DETAILS_TIME_TEXT = "appointment_details_time_text"
    const val ID_APPOINTMENT_CALL_DEALER_CTA = "appointment_call_dealer_cta"
    const val ID_APPOINTMENT_DEALER_LOCATION = "appointment_dealer_location"
    const val ID_APPOINTMENT_DEALER_NAME_TEXT = "appointment_dealer_name_text"
    const val ID_APPOINTMENT_DEALER_ADDRESS_TEXT = "appointment_dealer_address_text"
    const val ID_APPOINTMENT_SERVICE_DETAIL_TITLE_TEXT = "appointment_service_detail_title_text"
    const val ID_APPOINTMENT_SERVICE_DETAIL_TEXT = "appointment_service_detail_text"
    const val ID_APPOINTMENT_ADVISOR_TITLE_TEXT = "appointment_advisor_title_text"
    const val ID_APPOINTMENT_ADVISOR_TEXT = "appointment_advisor_text"
    const val ID_APPOINTMENT_TRANSPORTATION_TITLE_TEXT = "appointment_transportation_title_text"
    const val ID_APPOINTMENT_TRANSPORTATION_TEXT = "appointment_transportation_text"

    // Appointment Service Details
    const val ID_APPOINTMENT_SERVICE_DETAILS_APP_BAR_TEXT = "appointment_service_details_app_bar_text"
    const val ID_APPOINTMENT_SERVICE_DETAILS_BACK_CTA = "appointment_service_details_back_cta"

    // Clean assist
    const val ID_CA_APPBAR_TITLE = "clean_assist_appbar_text"
    const val ID_WHAT_IS_CA_TITLE = "clean_assist_what_is_title_text"
    const val ID_CA_ENROLLMENT_INFO = "clean_assist_enrollment_information_text"
    const val ID_CA_PRIVACY_TERMS_TITLE = "clean_assist_privacy_terms_title_text"
    const val ID_CA_PRIVACY_TERMS_INFO = "clean_assist_privacy_terms_information_text"
    const val ID_CA_ENROLLMENT_DECLINE = "clean_assist_enrollment_decline_text"
    const val ID_CA_ENROLLMENT_ACCEPT = "clean_assist_enrollment_accept_text"
    const val ID_CA_CLEAN_ENERGY_CHARGE_GRAPH_VIEW = "clean_assist_clean_energy_charge_graph_view"
    const val ID_CA_CO2_ENERGY_EMISSION_GRAPH_VIEW = "clean_assist_co2_emission_graph_view"

    // Charge Management
    const val ID_CHARGE_MANAGEMENT_BACK_BTN = "charge_management_back_btn"
    const val ID_CHARGE_PERCENTAGE_TEXT = "charge_management_charge_percentage_txt"
    const val ID_CHARGE_PERCENTAGE_PROGRESS_INDICATOR = "charge_management_progress_indicator"
    const val ID_CHARGE_STATUS_WIDGET = "charge_management_charge_status"
    const val ID_ESTIMATED_DISTANCE_TEXT = "charge_management_estimated_distance_txt"
    const val ID_DISTANCE_TO_EMPTY_DESCRIPTION_TXT = "charge_management_distance_to_empty_text"
    const val ID_CREATE_SCHEDULE_BTN = "charge_management_create_schedule_btn"
    const val ID_CHARGEMANAGEMENT_START_CHARGING_BTN = "charge_management_start_charging_btn"
    const val ID_OFF_PEARK_HOURS_SWITCH = "off_peak_on_off_switch"
    const val ID_STATIONS_TAB = "stations_tab_btn"
    const val ID_HOME_TAB = "home_tab_btn"
    const val ID_STATIONS_TAB_FIND_STATIONS_BTN = "stations_tab_find_stations_btn"
    const val ID_WALLET_SETUP_BTN = "wallet_setup_btn"
    const val ID_WALLET_DETAIL_BTN = "wallet_detail_btn"
    const val ID_INVOICES_CARD = "invoice_card"
    const val ID_INVOICES_CARD_VIEW_BTN = "invoice_view_btn"
    const val ID_PARTNER_ITEM_CARD = "partner_item_card"

    // Charge schedule
    const val ID_CHARGE_SCHEDULE_BACK_BTN = "charge_schedule_back_btn"
    const val ID_ECO_CHARGING_CARD = "eco_charging_card"
    const val ID_OFF_PEAK_DESCRIPTION_TEXT = "eco_charging_off_peak_text"
    const val ID_FIND_OUT_MORE_CARD_BTN = "eco_charging_find_out_more_btn"
    const val ID_ECO_CHARGING_SWITCH = "eco_charging_switch"
    const val ID_PHEV_START_TIME_CARD = "phev_start_time_card"
    const val ID_PHEV_ON_OFF_VIEW = "phev__on_off_view"
    const val ID_PHEV_DEPART_TIME_CARD = "phev_depart_time_card"
    const val ID_PHEV_REFRESH_BUTTON = "phev_refresh_btn"
    const val ID_LAST_UPDATED_TEXT = "schedule_last_updated_text"
    const val ID_PHEV_DETAIL_BACK_BTN = "phev_detail_screen_back_btn"
    const val ID_PHEV_DETAIL_CARD = "phev_detail_card"
    const val ID_PHEV_TIME_PICKER = "phev_time_picker"
    const val ID_AM_BTN = "am_btn"
    const val ID_PM_BTN = "pm_btn"
    const val ID_PHEV_CONFIRM_BTN = "phev_confirm_btn"
    const val ID_PHEV_CLIMATE_SWITCH = "phev_climate_switch"
    const val ID_MULTIDAY_ITEM_CARD = "multiday_item_card"
    const val ID_MULTIDAY_ITEM_TITLE_TEXT = "multiday_item_title_text"
    const val ID_MULTIDAY_ITEM_SUBTITLE_TEXT = "multiday_item_subtitle_text"
    const val ID_MULTIDAY_ITEM_SWITCH = "multiday_item_switch"
    const val ID_MULTIDAY_REFRESH_BUTTON = "multiday_refresh_btn"
    const val ID_MULTIDAY_CREATE_SCHEDULE_BTN = "multiday_create_schedule_btn"
    const val ID_MULTIDAY_BACK_BTN = "multiday_back_btn"
    const val ID_MULTIDAY_START_TIME_CARD = "multiday_start_time_card"
    const val ID_MULTIDAY_START_TIME_TIME_PICKER = "multiday_start_time_time_picker"
    const val ID_MULTIDAY_END_TIME_CARD = "multiday_end_time_card"
    const val ID_MULTIDAY_END_TIME_TIME_PICKER = "multiday_end_time_time_picker"
    const val ID_MULTIDAY_END_TIME_SWITCH = "multiday_end_time_switch"
    const val ID_CREATE_SCHEDULE_IMAGE_BTN = "multiday_create_schedule_image_btn"
    const val ID_DELETE_SCHEDULE_IMAGE_BTN = "multiday_delete_schedule_image_btn"
    const val ID_NO_SCHEDULE_TITLE_TEXT = "no_schedule_title_text"
    const val ID_NO_SCHEDULE_SUBTITLE_TEXT = "no_schedule_subtitle_text"
    const val ID_NO_SCHEDULE_CREATE_SCHEDULE_BTN = "no_schedule_create_schedule_btn"
    const val ID_FIND_OUT_MORE_BACK_BTN = "find_out_more_back_btn"

    // EV Wallet
    const val ID_EV_WALLET_HOME = "ev_wallet_home"
    const val ID_EV_WALLET_HOME_BACK_BTN = "ev_wallet_home_back_btn"
    const val ID_EV_WALLET_TRANSACTION_SCREEN_BACK_BTN = "ev_wallet_transaction_screen_back_btn"

    // Charge Statistics
    const val ID_STATISTICS_BACK_BTN = "statistics_back_btn"
    const val ID_STATISTICS_MONTH_YEAR_PICKER_BTN = "statistics_month_year_picker_btn"
    const val ID_STATISTICS_MONTH_YEAR_PICKER_LABEL = "statistics_month_year_picker_label"
    const val ID_REPORT_AVAILABLE_LAYOUT = "report_available_report"
    const val ID_NO_REPORT_AVAILABLE_LAYOUT = "no_report_available_report"
    const val ID_CO2_EMISSION_CARD = "co2_emission_card"
    const val ID_MONTHLY_CHALLENGE_CARD = "monthly_challenge_card"
    const val ID_HEALTH_IMPACT_REDUCTION_CARD = "health_impact_reduction_card"
    const val ID_TREES_EQUIVALENT_CARD = "trees_equivalent_card"
    const val ID_LEARN_MORE_BTN = "learn_more_btn"

    // Drive Pulse Sunset
    const val ID_DRIVE_PULSE_SUNSET_BOTTON_CTA = "drive_pulse_sunset_ok_button_cta"
    const val ID_DRIVE_PULSE_SUNSET_CARD = "drive_pulse_sunset_card"
    const val ID_EV_GO_DISCLAIMER_BACK_ARROW_CTA = "ev_go_disclaimer_back_button"
    const val ID_EV_GO_SIGN_IN_EDIT_TEXT_FIELD = "ev_go_sign_in_edit_text_field"

    const val LOADING = "charging_screen_loading_view"
    const val CHARGING_ICON = "charging_screen_charging_icon"
    const val CHARGE_LEVEL = "charging_screen_charging_level"
    const val CHARGING_LEVEL_BAR = "charging_screen_charging_level_bar"
    const val INFO_BUTTON = "charging_screen_info_button"
    const val STATION_PIN_ICON = "charging_screen_station_pin_icon"
    const val STATION_NAME = "charging_screen_station_name"
    const val STATION_PARTNER = "charging_screen_station_partner"
    const val STATION_ADDRESS = "charging_screen_station_address"
    const val START_TIME_LABEL = "charging_screen_start_time_label"
    const val START_TIME_TEXT = "charging_screen_start_time_text"
    const val ENERGY_LABEL = "charging_screen_energy_label"
    const val ENERGY_TEXT = "charging_screen_energy_text"
    const val PLUG_TYPE_CIRCLE_IMAGE = "charging_screen_plug_type_circle_image"
    const val PLUG_TYPE_CIRCLE_TEXT = "charging_screen_plug_type_circle_text"
    const val PC_BACK_BUTTON = "charging_screen_pc_back_button"

    // Drive Pulse & Trips
    const val DRIVE_PULSE_AND_TRIPS_DRIVE_PULSE_INFO_SCREEN =
        "drive_pulse_and_trips_drive_pulse_info_screen"
    const val DRIVE_PULSE_AND_TRIPS_DRIVE_PULSE_INFO_SCREEN_BACK_BUTTON_CTA =
        "drive_pulse_and_trips_drive_pulse_info_screen_back_button_cta"
    const val DRIVE_PULSE_AND_TRIPS_TRIPS_SCREEN = "drive_pulse_and_trips_trips_screen"
    const val DRIVE_PULSE_AND_TRIPS_TRIP_DETAILS = "drive_pulse_and_trips_trip_details"
    const val DRIVE_PULSE_AND_TRIPS_CLEAR_TRIPS_BUTTON_CTA =
        "drive_pulse_and_trips_clear_trips_button_cta"

    // Plug & Charge
    const val PLUG_AND_CHARGE_ENROLLMENT = "plug_and_charge_enrollment_screen"
}

// Please refer accessibility format for new Ids, Format available in top of this page
// Don't update existing id's
