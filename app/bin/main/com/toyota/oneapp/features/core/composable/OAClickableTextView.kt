/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.core.composable

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.ClickableText
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
private fun OAClickableTextView(
    text: String,
    color: Color = AppTheme.colors.tertiary00,
    style: TextStyle = AppTheme.fontStyles.body4,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    onClick: (Int) -> Unit,
) {
    ClickableText(
        text = AnnotatedString(text),
        style =
            style.copy(
                color = color,
            ),
        modifier = modifier,
        onClick = onClick,
    )
}

@Composable
fun OAClickableBody4TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    onClick: (Int) -> Unit,
) {
    OAClickableTextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.body4,
        onClick = onClick,
    )
}

@Composable
fun OAClickableButtonTextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    onClick: (Int) -> Unit,
) {
    OAClickableTextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.buttonLink1,
        onClick = onClick,
    )
}

@Composable
fun OACallOut1SpannableTextView(
    text: String,
    spanText: String,
    color: Color,
    spanStyle: SpanStyle,
    modifier: Modifier = Modifier,
    style: TextStyle = AppTheme.fontStyles.callout1,
    onClick: (String) -> Unit,
) {
    val annotatedString = buildSpannable(text, spanText, spanStyle, true)

    ClickableText(
        text = annotatedString,
        modifier,
        style.copy(
            color = color,
        ),
        onClick = { offset ->
            spanText.split(" ").forEach { tag ->
                annotatedString.getStringAnnotations(tag, offset, offset).firstOrNull()?.let {
                    onClick.invoke(it.item)
                }
            }
        },
    )
}

private fun buildSpannable(
    text: String,
    spanText: String,
    spanStyle: SpanStyle,
    isClickable: Boolean,
) = buildAnnotatedString {
    text.split(" ").forEach {
        if (spanText == it) {
            if (isClickable) pushStringAnnotation(it, it)
            withStyle(style = spanStyle) {
                append("$it ")
            }
            if (isClickable) pop()
        } else {
            append("$it ")
        }
    }
}
