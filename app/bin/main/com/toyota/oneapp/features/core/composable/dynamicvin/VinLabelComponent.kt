/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.composable.dynamicvin

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.toyota.oneapp.R.string.vehicle_acronym_vin
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme.colors

@Composable
fun VinLabelComponent(
    modifier: Modifier,
    testTagId: String,
) {
    OACallOut2TextView(
        text =
            stringResource(
                id = vehicle_acronym_vin,
            ),
        color = colors.tertiary03,
        modifier = modifier.testTagID(testTagId),
    )
}
