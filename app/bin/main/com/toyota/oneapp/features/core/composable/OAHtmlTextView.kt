package com.toyota.oneapp.features.core.composable

import android.widget.TextView
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.res.ResourcesCompat
import androidx.core.text.HtmlCompat
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun OABody3HtmlTextView(
    modifier: Modifier = Modifier,
    text: String,
    color: Color,
    maxLines: Int = Int.MAX_VALUE,
) {
    OAHtmlTextView(
        modifier = modifier,
        text = text,
        textStyle = AppTheme.fontStyles.body3,
        color = color,
        maxLines = maxLines,
    )
}

@Composable
fun OABody4HtmlTextView(
    modifier: Modifier = Modifier,
    text: String,
    color: Color,
    maxLines: Int = Int.MAX_VALUE,
) {
    OAHtmlTextView(
        modifier = modifier,
        text = text,
        textStyle = AppTheme.fontStyles.body4,
        color = color,
        maxLines = maxLines,
    )
}

@Composable
fun OAHtmlTextView(
    modifier: Modifier = Modifier,
    text: String,
    textStyle: TextStyle,
    color: Color,
    linkColor: Color = Color.Unspecified,
    maxLines: Int = Int.MAX_VALUE,
) {
    AndroidView(
        modifier = modifier,
        update = {
            it.text = HtmlCompat.fromHtml(text, HtmlCompat.FROM_HTML_MODE_LEGACY)
            it.setLinkTextColor(linkColor.hashCode())
            it.movementMethod = android.text.method.LinkMovementMethod.getInstance()
        },
        factory = { context ->
            val fontResId =
                when (textStyle.fontWeight) {
                    FontWeight.W600, FontWeight.W700 -> com.toyota.one_ui.R.font.toyotatype_bold
                    else -> com.toyota.one_ui.R.font.toyotatype_regular
                }
            val font = ResourcesCompat.getFont(context, fontResId)

            TextView(context).apply {
                // general style
                textSize = textStyle.fontSize.value
                setTextColor(color.toArgb())
                typeface = font
                setMaxLines(maxLines)
                movementMethod = android.text.method.LinkMovementMethod.getInstance()
            }
        },
    )
}
