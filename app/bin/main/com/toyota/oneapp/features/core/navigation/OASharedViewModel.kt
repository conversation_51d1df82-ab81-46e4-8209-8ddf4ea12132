package com.toyota.oneapp.features.core.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisallowComposableCalls
import androidx.compose.runtime.remember
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.ViewModel
import androidx.navigation.NavBackStackEntry

@Composable
inline fun <reified T : ViewModel> NavBackStackEntry.sharedViewModel(
    crossinline getBackStackEntry: @DisallowComposableCalls (path: String) -> NavBackStackEntry,
): T {
    val navGraphRoute = destination.parent?.route ?: return hiltViewModel()
    val parentEntry =
        remember(this) {
            getBackStackEntry(navGraphRoute)
        }
    return hiltViewModel(parentEntry)
}
