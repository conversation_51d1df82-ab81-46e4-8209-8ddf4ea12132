package com.toyota.oneapp.features.core.composable

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.isUnspecified
import androidx.compose.ui.unit.sp
import com.toyota.oneapp.features.core.theme.AppTheme

/**
 * Base OATextView with default color & style.
 * create a fun based on figma name
 *
 * **/
@Composable
private fun OATextView(
    text: String,
    color: Color = AppTheme.colors.tertiary00,
    style: TextStyle = AppTheme.fontStyles.body4,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    lineHeight: TextUnit = TextUnit.Unspecified,
) {
    Text(
        text = text,
        color = color,
        style = style,
        modifier = modifier,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        lineHeight = lineHeight,
    )
}

// Overload accepting AnnotatedString
@Composable
private fun OATextView(
    modifier: Modifier = Modifier.padding(all = 0.dp),
    text: AnnotatedString,
    color: Color = AppTheme.colors.tertiary00,
    style: TextStyle = AppTheme.fontStyles.body4,
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    lineHeight: TextUnit = TextUnit.Unspecified,
) {
    Text(
        text = text,
        color = color,
        style = style,
        modifier = modifier,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        lineHeight = lineHeight,
    )
}

@Composable
fun OAHeadline1TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLine: Int = Int.MAX_VALUE,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.headline1,
        textAlign = textAlign,
        maxLines = maxLine,
    )
}

@Composable
fun OACaption1TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.caption1,
        textAlign = textAlign,
    )
}

@Composable
fun OACaption2TextView(
    text: String,
    color: Color,
    textAlign: TextAlign = TextAlign.Left,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    lineHeight: TextUnit = TextUnit.Unspecified,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Ellipsis,
) {
    OATextView(
        text = text,
        color = color,
        textAlign = textAlign,
        modifier = modifier,
        style = AppTheme.fontStyles.caption2,
        lineHeight = lineHeight,
        maxLines = maxLines,
        overflow = overflow,
    )
}

@Composable
fun OASubHeadLine1TextView(
    text: String,
    color: Color,
    textAlign: TextAlign = TextAlign.Left,
    modifier: Modifier =
        Modifier.padding(
            all = 0.dp,
        ),
) {
    OATextView(
        text = text,
        color = color,
        textAlign = textAlign,
        modifier = modifier,
        style = AppTheme.fontStyles.subHeadline1,
    )
}

@Composable
fun OASubHeadLine2TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.subHeadline2,
        textAlign = textAlign,
    )
}

@Composable
fun OASubHeadLine3TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = Int.MAX_VALUE,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.subHeadline3,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

@Composable
fun OASubHeadLine4TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.subHeadline4,
        textAlign = textAlign,
    )
}

@Composable
fun OABody1TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = Int.MAX_VALUE,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.body1,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

@Composable
fun OABody2TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    maxLines: Int = 100,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.body2,
        maxLines = maxLines,
    )
}

@Composable
fun OABody3TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = 100,
    lineHeight: TextUnit = TextUnit.Unspecified,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.body3,
        textAlign = textAlign,
        maxLines = maxLines,
        lineHeight = lineHeight,
    )
}

@Composable
fun OABody3ResizableTextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = 100,
) {
    OATextViewResizable(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.body3,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

@Composable
fun OABody4TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = Int.MAX_VALUE,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.body4,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

@Composable
fun OACallOut1TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = Int.MAX_VALUE,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.callout1,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

// Overload accepting AnnotatedString
@Composable
fun OACallOut1TextView(
    modifier: Modifier = Modifier.padding(all = 0.dp),
    text: AnnotatedString,
    color: Color,
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = Int.MAX_VALUE,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.callout1,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

@Composable
fun OAFootNote1TextView(
    text: AnnotatedString,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.footNote1,
        textAlign = textAlign,
    )
}

@Composable
fun OACallOut2TextView(
    text: String,
    color: Color,
    maxLines: Int = 1,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    modifier: Modifier =
        Modifier.padding(
            all = 0.dp,
        ),
    textAlign: TextAlign = TextAlign.Left,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.callout2,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
    )
}

@Composable
fun OACallOut3TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.callout3,
    )
}

@Composable
fun OASubHeadingLine1TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = 1,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.subHeadline1,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

@Composable
fun OASubHeadingLine2TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.subHeadline2,
        textAlign = textAlign,
    )
}

@Composable
fun OASubHeadingLine3TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.subHeadline3,
        textAlign = textAlign,
    )
}

@Composable
fun OASubHeadingLine4TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.subHeadline4,
    )
}

@Composable
fun OAButtonTextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.buttonLink1,
        textAlign = textAlign,
    )
}

@Composable
fun OAFootNote1TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.footNote1,
        textAlign = textAlign,
    )
}

@Composable
fun OAFootNote1AnnotatedTextView(
    text: AnnotatedString,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.footNote1,
        textAlign = textAlign,
    )
}

@Composable
fun OATitle1TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign,
    maxLines: Int,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.title1,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

@Composable
@Preview(showBackground = true)
fun testHeadLine() {
    OAButtonTextView(
        text = "HeadLine",
        color = AppTheme.colors.primary01,
    )
}

@Composable
fun OATextLink1TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.textLink1,
    )
}

@Composable
fun OATextTitle2TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.title2,
        textAlign = textAlign,
    )
}

@Composable
fun OATextTitle4TextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.title4,
        textAlign = textAlign,
    )
}

@Composable
fun OATextTitle3(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = 3,
    lineHeight: TextUnit = TextUnit.Unspecified,
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.title3,
        textAlign = textAlign,
        maxLines = maxLines,
        lineHeight = lineHeight,
    )
}

@Composable
fun OATabLabel01TextView(
    text: String,
    color: Color,
    maxLine: Int = Int.MAX_VALUE,
    fontSize: TextUnit = 11.sp,
    textAlign: TextAlign = TextAlign.Left,
    modifier: Modifier =
        Modifier.padding(
            all = 0.dp,
        ),
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        textAlign = textAlign,
        maxLines = maxLine,
        style = AppTheme.fontStyles.tabLabel01.copy(fontSize = fontSize),
    )
}

@Composable
fun OATabLabel02TextView(
    text: String,
    color: Color,
    textAlign: TextAlign = TextAlign.Left,
    modifier: Modifier =
        Modifier.padding(
            all = 0.dp,
        ),
) {
    OATextView(
        text = text,
        color = color,
        modifier = modifier,
        textAlign = textAlign,
        style = AppTheme.fontStyles.tabLabel02,
    )
}

@Composable
fun OACallOut2TextViewResizable(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    maxLines: Int = 1,
    textAlign: TextAlign = TextAlign.Left,
) {
    OATextViewResizable(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.callout2,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

@Composable
fun OATextTitle2ResizableTextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    maxLines: Int = 1,
    textAlign: TextAlign = TextAlign.Left,
    lineHeight: TextUnit = TextUnit.Unspecified,
) {
    OATextViewResizable(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.title2,
        textAlign = textAlign,
        maxLines = maxLines,
        lineHeight = lineHeight,
    )
}

@Composable
fun OATextHeadline1ResizableTextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    maxLines: Int = 1,
    textAlign: TextAlign = TextAlign.Left,
) {
    OATextViewResizable2(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.headline1,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

@Composable
fun OASubHeadline3ResizableTextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    maxLines: Int = 1,
    textAlign: TextAlign = TextAlign.Left,
) {
    OATextViewResizable2(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.subHeadline3,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

@Composable
fun OASearchResizableTextView(
    text: String,
    color: Color,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    maxLines: Int = 1,
    textAlign: TextAlign = TextAlign.Left,
) {
    OATextViewResizable(
        text = text,
        color = color,
        modifier = modifier,
        style = AppTheme.fontStyles.body3,
        textAlign = textAlign,
        maxLines = maxLines,
    )
}

@Composable
private fun OATextViewResizable(
    text: String,
    color: Color = AppTheme.colors.tertiary00,
    style: TextStyle = AppTheme.fontStyles.body4,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = Int.MAX_VALUE,
    lineHeight: TextUnit = TextUnit.Unspecified,
) {
    val fontSize = style.fontSize
    var resizedTextStyle by remember {
        mutableStateOf(style)
    }
    var shouldDraw by remember {
        mutableStateOf(false)
    }
    Text(
        text = text,
        color = color,
        modifier =
            modifier.drawWithContent {
                if (shouldDraw) {
                    drawContent()
                }
            },
        textAlign = textAlign,
        maxLines = maxLines,
        lineHeight = lineHeight,
        softWrap = false,
        style = resizedTextStyle,
        onTextLayout = { result ->
            if (result.didOverflowWidth) {
                if (style.fontSize.isUnspecified) {
                    resizedTextStyle =
                        resizedTextStyle.copy(
                            fontSize = fontSize,
                        )
                }
                resizedTextStyle =
                    resizedTextStyle.copy(
                        fontSize = resizedTextStyle.fontSize * 0.95,
                    )
            } else {
                shouldDraw = true
            }
        },
    )
}

@Composable
private fun OATextViewResizable2(
    text: String,
    color: Color = AppTheme.colors.tertiary00,
    style: TextStyle = AppTheme.fontStyles.body4,
    modifier: Modifier = Modifier.padding(all = 0.dp),
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = Int.MAX_VALUE,
    lineHeight: TextUnit = TextUnit.Unspecified,
) {
    var resizedTextStyle by remember {
        mutableStateOf(style)
    }
    var shouldDraw by remember {
        mutableStateOf(false)
    }
    var isScaling by remember {
        mutableStateOf(false)
    }
    var isScalingComplete by remember {
        mutableStateOf(false)
    }
    Text(
        text = text,
        color = color,
        modifier =
            modifier.drawWithContent {
                if (shouldDraw) {
                    drawContent()
                }
            },
        textAlign = textAlign,
        maxLines = maxLines,
        lineHeight = lineHeight,
        softWrap = false,
        style = resizedTextStyle,
        onTextLayout = { result ->
            if (result.didOverflowWidth && !isScalingComplete) {
                resizedTextStyle =
                    resizedTextStyle.copy(
                        fontSize = resizedTextStyle.fontSize * 0.95,
                    )
                isScaling = true
            } else if (isScalingComplete && !isScaling) {
                resizedTextStyle =
                    resizedTextStyle.copy(
                        fontSize = style.fontSize,
                    )
                isScalingComplete = false
            } else {
                if (isScaling) {
                    isScaling = false
                    isScalingComplete = true
                }
                shouldDraw = true
            }
        },
    )
}

@Preview(showBackground = true)
@Composable
private fun OASearchResizableTextViewPreview() {
    OASearchResizableTextView(
        text = "This is very long text view that could not fit into one line",
        color = AppTheme.colors.tertiary00,
    )
}

@Preview(showBackground = true)
@Composable
private fun OATextViewPreview() {
    Column(modifier = Modifier.padding(16.dp)) {
        OABody1TextView(
            text = "BodyTextView - 1",
            color = AppTheme.colors.tertiary05,
        )
        OABody2TextView(
            text = "BodyTextView - 2",
            color = AppTheme.colors.tertiary05,
        )
        OABody3TextView(
            text = "BodyTextView - 3",
            color = AppTheme.colors.tertiary05,
        )
        OABody4TextView(
            text = "BodyTextView - 4",
            color = AppTheme.colors.tertiary05,
        )
    }
}
