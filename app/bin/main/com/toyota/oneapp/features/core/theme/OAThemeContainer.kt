package com.toyota.oneapp.features.core.theme

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.ProvidableCompositionLocal
import androidx.compose.runtime.staticCompositionLocalOf
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.features.core.theme.colors.LexusDarkColors
import com.toyota.oneapp.features.core.theme.colors.LexusLightColors
import com.toyota.oneapp.features.core.theme.colors.OAColors
import com.toyota.oneapp.features.core.theme.colors.SubaruDarkColors
import com.toyota.oneapp.features.core.theme.colors.SubaruLightColors
import com.toyota.oneapp.features.core.theme.colors.ToyotaDarkColors
import com.toyota.oneapp.features.core.theme.colors.ToyotaLightColors
import com.toyota.oneapp.features.core.theme.fonts.LexusFontStyle
import com.toyota.oneapp.features.core.theme.fonts.OAFontStyle
import com.toyota.oneapp.features.core.theme.fonts.SubaruFontStyle
import com.toyota.oneapp.features.core.theme.fonts.ToyotaFontStyle
import com.toyota.oneapp.util.Brand
import kotlinx.coroutines.flow.MutableStateFlow

val themeBrand: ThemeBrand =
    when (BuildConfig.APP_BRAND) {
        Brand.TOYOTA.appBrand -> ThemeBrand.Toyota
        Brand.LEXUS.appBrand -> ThemeBrand.Lexus
        Brand.SUBARU.appBrand -> ThemeBrand.Subaru
        else -> ThemeBrand.Lexus
    }

// Colors
val toyotaLightColors: OAColors by lazy {
    ToyotaLightColors()
}
val toyotaDarkColors: OAColors by lazy {
    ToyotaDarkColors()
}
val lexusLightColors: OAColors by lazy {
    LexusLightColors()
}
val lexusDarkColors: OAColors by lazy {
    LexusDarkColors()
}
val subaruLightColors: OAColors by lazy {
    SubaruLightColors()
}
val subaruDarkColors: OAColors by lazy {
    SubaruDarkColors()
}

// Text Style
val toyotaLightFontStyle: OAFontStyle by lazy {
    ToyotaFontStyle()
}
val toyotaDarkFontStyle: OAFontStyle by lazy {
    ToyotaFontStyle()
}
val lexusLightFontStyle: OAFontStyle by lazy {
    LexusFontStyle()
}
val lexusDarkFontStyle: OAFontStyle by lazy {
    LexusFontStyle()
}
val subaruLightFontStyle: OAFontStyle by lazy {
    SubaruFontStyle()
}
val subaruDarkFontStyle: OAFontStyle by lazy {
    SubaruFontStyle()
}

// Themes
val toyotaLightTheme: OATheme by lazy {
    DefaultTheme(
        colors = toyotaLightColors,
        fontStyles = toyotaLightFontStyle,
        type = ThemeType.ToyotaLight,
    )
}
val toyotaDarkTheme: OATheme by lazy {
    DefaultTheme(
        colors = toyotaDarkColors,
        fontStyles = toyotaDarkFontStyle,
        type = ThemeType.ToyotaDark,
    )
}
val lexusLightTheme: OATheme by lazy {
    DefaultTheme(
        colors = lexusLightColors,
        fontStyles = lexusLightFontStyle,
        type = ThemeType.LexusLight,
    )
}
val lexusDarkTheme: OATheme by lazy {
    DefaultTheme(
        colors = lexusDarkColors,
        fontStyles = lexusDarkFontStyle,
        type = ThemeType.LexusDark,
    )
}
val subaruLightTheme: OATheme by lazy {
    DefaultTheme(
        colors = subaruLightColors,
        fontStyles = subaruLightFontStyle,
        type = ThemeType.SubaruLight,
    )
}
val subaruDarkTheme: OATheme by lazy {
    DefaultTheme(
        colors = subaruDarkColors,
        fontStyles = subaruDarkFontStyle,
        type = ThemeType.SubaruDark,
    )
}

val LocalDefaultTheme: ProvidableCompositionLocal<OATheme> =
    staticCompositionLocalOf {
        DefaultTheme(
            colors = ToyotaLightColors(),
            fontStyles = ToyotaFontStyle(),
            type = ThemeType.ToyotaLight,
        )
    }

@Composable
fun OAThemeProvider(
    isDarkMode: Boolean = false,
    content: @Composable () -> Unit,
) {
    val themeMode = if (isDarkMode) ThemeMode.Dark else ThemeMode.Light

    val currentTheme =
        when {
            themeMode == ThemeMode.Light && themeBrand == ThemeBrand.Toyota -> {
                toyotaLightTheme
            }
            themeMode == ThemeMode.Dark && themeBrand == ThemeBrand.Toyota -> {
                toyotaDarkTheme
            }
            themeMode == ThemeMode.Light && themeBrand == ThemeBrand.Lexus -> {
                lexusLightTheme
            }
            themeMode == ThemeMode.Dark && themeBrand == ThemeBrand.Lexus -> {
                lexusDarkTheme
            }
            themeMode == ThemeMode.Light && themeBrand == ThemeBrand.Subaru -> {
                subaruLightTheme
            }
            themeMode == ThemeMode.Dark && themeBrand == ThemeBrand.Subaru -> {
                subaruDarkTheme
            }
            else -> {
                toyotaLightTheme
            }
        }

    CompositionLocalProvider(
        LocalDefaultTheme provides currentTheme,
        content = content,
    )
}

object AppTheme {
    val darkMode = MutableStateFlow<Boolean>(value = false)
    val colors: OAColors
        @Composable
        get() = LocalDefaultTheme.current.colors
    val fontStyles: OAFontStyle
        @Composable
        get() = LocalDefaultTheme.current.fontStyles
}
