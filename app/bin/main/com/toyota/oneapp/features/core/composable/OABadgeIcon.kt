package com.toyota.oneapp.features.core.composable

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun OABadgeIcon(
    imageResId: Int,
    backgroundColor: Color = AppTheme.colors.secondary02,
    iconTint: Color = AppTheme.colors.tertiary03,
    containerSize: Dp = 48.dp,
    iconSize: Dp = 24.dp,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier =
            modifier
                .size(containerSize)
                .background(color = backgroundColor, shape = CircleShape),
        contentAlignment = Alignment.Center,
    ) {
        Image(
            painter = painterResource(imageResId),
            contentDescription = "",
            colorFilter = ColorFilter.tint(iconTint),
            modifier = modifier.size(iconSize),
        )
    }
}

@Preview
@Composable
private fun OABadgeIconPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode = themeMode) {
        Column(
            modifier = Modifier.padding(16.dp).fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            OABadgeIcon(
                imageResId = R.drawable.ic_location,
                backgroundColor = AppTheme.colors.secondary02,
                iconTint = AppTheme.colors.tertiary03,
                containerSize = 96.dp,
                iconSize = 48.dp,
            )
            OABadgeIcon(
                imageResId = R.drawable.ic_accept_amber_alert,
                backgroundColor = AppTheme.colors.secondary01,
                iconTint = AppTheme.colors.tertiary00,
                containerSize = 84.dp,
                iconSize = 42.dp,
            )
            OABadgeIcon(
                imageResId = R.drawable.ic_eco_badge,
                backgroundColor = AppTheme.colors.secondary02,
                iconTint = AppTheme.colors.tertiary03,
                containerSize = 72.dp,
                iconSize = 36.dp,
            )
            OABadgeIcon(
                imageResId = R.drawable.ic_ev_home,
                backgroundColor = AppTheme.colors.secondary02,
                iconTint = AppTheme.colors.tertiary03,
                containerSize = 60.dp,
                iconSize = 30.dp,
            )
            OABadgeIcon(
                imageResId = R.drawable.ic_history_info,
                backgroundColor = AppTheme.colors.secondary02,
                iconTint = AppTheme.colors.secondary01,
            )
            OABadgeIcon(
                imageResId = R.drawable.ic_schedule,
                backgroundColor = AppTheme.colors.secondary02,
                iconTint = AppTheme.colors.tertiary03,
            )
        }
    }
}
