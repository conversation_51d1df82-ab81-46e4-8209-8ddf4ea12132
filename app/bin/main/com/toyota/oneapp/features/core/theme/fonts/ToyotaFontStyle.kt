package com.toyota.oneapp.features.core.theme.fonts

import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp

class ToyotaFontStyle : OAFontStyle() {
    override val title4: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(700),
                fontSize = 60.sp,
            )

    override val title3: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(700),
                fontSize = 48.sp,
            )

    override val title2: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(700),
                fontSize = 32.sp,
            )

    override val title1: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(600),
                fontSize = 28.sp,
            )

    override val headline1: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(700),
                fontSize = 24.sp,
            )

    override val subHeadline1: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(600),
                fontSize = 18.sp,
            )

    override val subHeadline2: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(700),
                fontSize = 18.sp,
            )

    override val subHeadline3: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(600),
                fontSize = 20.sp,
            )

    override val subHeadline4: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(700),
                fontSize = 20.sp,
            )

    override val body1: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 15.sp,
            )

    override val body2: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(600),
                fontSize = 15.sp,
            )

    override val body3: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 16.sp,
            )

    override val body4: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(600),
                fontSize = 16.sp,
            )

    override val callout1: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 14.sp,
            )

    override val callout2: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(600),
                fontSize = 14.sp,
            )

    override val callout3: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(700),
                fontSize = 14.sp,
            )

    override val caption1: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 12.sp,
            )

    override val caption2: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(600),
                fontSize = 12.sp,
            )

    override val tabLabel01: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 11.sp,
            )

    override val tabLabel02: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(600),
                fontSize = 11.sp,
            )

    override val footNote1: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 12.sp,
            )

    override val buttonLink1: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(600),
                fontSize = 14.sp,
            )

    override val textLink1: TextStyle
        get() =
            TextStyle(
                fontFamily = toyotaFontFamily,
                fontWeight = FontWeight(600),
                fontSize = 14.sp,
            )
}
