/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.core.composable

import android.annotation.SuppressLint
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.AlertDialog
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.material.ContentAlpha
import androidx.compose.material.Icon
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowDown
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.accompanist.flowlayout.FlowCrossAxisAlignment
import com.google.accompanist.flowlayout.FlowRow
import com.google.accompanist.flowlayout.MainAxisAlignment
import com.toyota.oneapp.features.core.theme.AppTheme
import java.util.Calendar

@Composable
fun OAMonthPicker(
    showDialog: Boolean,
    testTagIds: Pair<String, String>,
    modifier: Modifier = Modifier,
    defaultMonth: Int? = null,
    defaultYear: Int? = null,
    onCancel: () -> Unit,
    onPicked: (month: Int, monthStr: String, year: Int) -> Unit,
) {
    val months =
        listOf(
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
        )

    val currentMonth = Calendar.getInstance().get(Calendar.MONTH)
    val currentYear = Calendar.getInstance().get(Calendar.YEAR)
    var month by remember {
        defaultMonth?.let { mutableStateOf(months[it]) } ?: run {
            mutableStateOf(months[currentMonth])
        }
    }
    var year by remember {
        defaultYear?.let { mutableStateOf(it) } ?: run {
            mutableStateOf(currentYear)
        }
    }

    if (showDialog) {
        AlertDialog(
            modifier = modifier,
            backgroundColor = Color.White,
            shape = RoundedCornerShape(10),
            title = {},
            text = {
                Column {
                    YearPicker(
                        selectedYear = year,
                        currentYear = currentYear,
                    ) { pickedYear ->
                        year = pickedYear
                    }

                    MonthPicker(
                        months = months,
                        currentMonth = currentMonth,
                        currentYear = currentYear,
                        selectedMonth = month,
                        selectedYear = year,
                    ) { selectedMonth ->
                        month = selectedMonth
                    }
                }
            },
            buttons = {
                PickerButtons(
                    negativeLabel = "Cancel",
                    positiveLabel = "Ok",
                    negativeTestTagId = testTagIds.second,
                    positiveTestTagId = testTagIds.first,
                    onNegativeButtonClicked = {
                        onCancel()
                    },
                ) {
                    onPicked(
                        months.indexOf(month) + 1,
                        month,
                        year,
                    )
                }
            },
            onDismissRequest = {},
        )
    }
}

@Composable
private fun YearPicker(
    selectedYear: Int,
    currentYear: Int,
    modifier: Modifier = Modifier,
    onYearChanged: (year: Int) -> Unit,
) {
    var yearIncrementEnabled by remember { mutableStateOf(selectedYear < currentYear) }
    var year by remember { mutableStateOf(selectedYear) }
    val interactionSource = remember { MutableInteractionSource() }

    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            modifier =
                Modifier
                    .size(35.dp)
                    .rotate(90f)
                    .clickable(
                        indication = null,
                        interactionSource = interactionSource,
                        onClick = {
                            year--
                            yearIncrementEnabled = year < currentYear
                            onYearChanged(year)
                        },
                    ),
            imageVector = Icons.Rounded.KeyboardArrowDown,
            contentDescription = null,
        )

        Text(
            modifier = Modifier.padding(horizontal = 20.dp),
            text = year.toString(),
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
        )

        Icon(
            modifier =
                Modifier
                    .size(35.dp)
                    .rotate(-90f)
                    .alpha(if (yearIncrementEnabled) 1f else ContentAlpha.disabled)
                    .clickable(
                        enabled = yearIncrementEnabled,
                        indication = null,
                        interactionSource = interactionSource,
                        onClick = {
                            year++
                            yearIncrementEnabled = year < currentYear
                            onYearChanged(year)
                        },
                    ),
            imageVector = Icons.Rounded.KeyboardArrowDown,
            contentDescription = null,
        )
    }
}

@Composable
private fun Modifier.enabled(isEnabled: Boolean) = alpha(if (isEnabled) 1f else ContentAlpha.disabled)

@SuppressLint("ModifierFactoryUnreferencedReceiver")
@Composable
private fun Modifier.monthBackground(isMonthSelected: Boolean) =
    background(
        color = if (isMonthSelected) AppTheme.colors.button01b else Color.Transparent,
        shape = CircleShape,
    )

@Composable
private fun MonthPicker(
    months: List<String>,
    currentMonth: Int,
    currentYear: Int,
    selectedYear: Int,
    selectedMonth: String,
    modifier: Modifier = Modifier,
    onMonthSelected: (month: String) -> Unit,
) {
    var month by remember { mutableStateOf(selectedMonth) }
    var isMonthDisabled by remember { mutableStateOf(true) }
    val interactionSource = remember { MutableInteractionSource() }

    Card(
        modifier =
            modifier
                .padding(top = 30.dp)
                .fillMaxWidth(),
        elevation = 0.dp,
    ) {
        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            mainAxisSpacing = 8.dp,
            crossAxisSpacing = 16.dp,
            mainAxisAlignment = MainAxisAlignment.Center,
            crossAxisAlignment = FlowCrossAxisAlignment.Center,
        ) {
            months.forEach {
                isMonthDisabled =
                    (selectedYear == currentYear && months.indexOf(it) > currentMonth)
                Box(
                    modifier =
                        Modifier
                            .size(60.dp)
                            .enabled(!isMonthDisabled)
                            .clickable(
                                enabled = !isMonthDisabled,
                                indication = null,
                                interactionSource = interactionSource,
                                onClick = {
                                    month = it
                                    onMonthSelected(month)
                                },
                            ).background(
                                color = Color.Transparent,
                            ),
                    contentAlignment = Alignment.Center,
                ) {
                    val animatedSize by animateDpAsState(
                        targetValue = if (month == it) 60.dp else 0.dp,
                        animationSpec =
                            tween(
                                durationMillis = 500,
                                easing = LinearOutSlowInEasing,
                            ),
                    )

                    Box(
                        modifier =
                            Modifier
                                .size(animatedSize)
                                .monthBackground(month == it),
                    )

                    Text(
                        text = it,
                        color = if (month == it) Color.White else Color.Black,
                        fontWeight = FontWeight.Medium,
                    )
                }
            }
        }
    }
}

@Composable
private fun PickerButtons(
    modifier: Modifier = Modifier,
    negativeLabel: String,
    positiveLabel: String,
    negativeTestTagId: String,
    positiveTestTagId: String,
    onNegativeButtonClicked: () -> Unit,
    onPositiveButtonClicked: () -> Unit,
) {
    Row(
        modifier =
            modifier
                .fillMaxWidth()
                .padding(end = 20.dp, bottom = 30.dp),
        horizontalArrangement = Arrangement.End,
    ) {
        OutlinedButton(
            modifier =
                Modifier
                    .padding(end = 20.dp)
                    .testTagID(negativeTestTagId),
            onClick = {
                onNegativeButtonClicked()
            },
            shape = CircleShape,
            border = BorderStroke(1.dp, color = Color.Transparent),
            colors = ButtonDefaults.outlinedButtonColors(backgroundColor = Color.Transparent),
        ) {
            Text(
                text = negativeLabel,
                color = Color.Black,
                fontSize = 20.sp,
                fontWeight = FontWeight.Medium,
            )
        }

        OutlinedButton(
            modifier =
                Modifier
                    .padding(end = 20.dp)
                    .testTagID(positiveTestTagId),
            onClick = {
                onPositiveButtonClicked()
            },
            shape = CircleShape,
            border = BorderStroke(1.dp, color = Color.Transparent),
            colors = ButtonDefaults.outlinedButtonColors(backgroundColor = Color.Transparent),
        ) {
            Text(
                text = positiveLabel,
                color = Color.Black,
                fontSize = 20.sp,
                fontWeight = FontWeight.Medium,
            )
        }
    }
}
