/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.util

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.OAThemeProvider
import com.toyota.oneapp.features.core.theme.ThemeMode

internal class OAThemePreviewProvider : PreviewParameterProvider<ThemeMode> {
    override val values = sequenceOf(ThemeMode.Light, ThemeMode.Dark)
}

@Composable
internal fun ContentPreview(
    themeMode: ThemeMode,
    modifier: Modifier = Modifier.fillMaxSize(),
    content: @Composable () -> Unit = {},
) {
    ContentPreview(
        isDarkMode = themeMode == ThemeMode.Dark,
        modifier = modifier,
        content = content,
    )
}

@Composable
internal fun ContentPreview(
    isDarkMode: Boolean,
    modifier: Modifier = Modifier.fillMaxSize(),
    content: @Composable () -> Unit,
) {
    OAThemeProvider(isDarkMode = isDarkMode) {
        Box(modifier = modifier.background(AppTheme.colors.tertiary15)) {
            content()
        }
    }
}
