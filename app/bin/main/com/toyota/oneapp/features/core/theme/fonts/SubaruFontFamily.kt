package com.toyota.oneapp.features.core.theme.fonts

import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import com.toyota.oneapp.R

val subaruFontFamily =
    FontFamily(
        fonts =
            listOf(
                Font(
                    resId = com.toyota.one_ui.R.font.toyotatype_regular,
                ),
                Font(
                    resId = com.toyota.one_ui.R.font.toyotatype_bold,
                    weight = FontWeight.W600,
                ),
                Font(
                    resId = com.toyota.one_ui.R.font.toyotatype_bold,
                    weight = FontWeight.W700,
                    style = FontStyle.Normal,
                ),
            ),
    )
