/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.location.application

import android.location.Address
import com.toyota.oneapp.features.core.location.domain.GeoLocationRepository
import com.toyota.oneapp.features.core.location.domain.GeoLocationUseCase
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

private const val MAX_RESULT = 2

internal class GeoLocationLogic
    @Inject
    constructor(
        private val repository: GeoLocationRepository,
    ) : GeoLocationUseCase {
        override fun getByLocationName(locationName: String): Flow<Result<List<Address>>> {
            return repository.getFromLocationName(locationName, MAX_RESULT)
        }
    }
