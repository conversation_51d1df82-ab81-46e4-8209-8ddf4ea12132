package com.toyota.oneapp.features.core.navigation

sealed class OAScreen(
    val route: String,
) {
    data object GloveBox : OAScreen("gloveBox")

    data object ManualsAndWarranties : OAScreen("manualsAndWarranties")

    data object DashboardLights : <PERSON><PERSON><PERSON><PERSON>("DashboardLights")

    data object SpecsAndCapabilities : OAScreen("SpecsAndCapabilities")

    data object Subscriptions : OAScreen("subscriptions")

    data object Service : OAScreen("service")

    data object Pay : OAScreen("pay")

    data object Home : OAScreen("home")

    data object Shop : OAScreen("shop")

    data object Find : OAScreen("find")

    data object FindStation : OAScreen("findStation")

    data object DrivePulseAndTrips : OAScreen("drivePulseAndTrips")

    data object AccountNotification : OAScreen("accountNotification")

    data object VehicleInfo : OAScreen("vehicleInfo")

    data object VehicleNickname : OAScreen("vehicleNickname")

    data object VehicleSwitcher : OAScreen("vehicleSwitcher")

    data object Insurance : OAScreen("insurance")

    data object SiriusXm : OAScreen("siriusXM")

    data object FTUE : OAScreen("ftueWidget")

    data object FTUEInitialScreen : OAScreen("FTUEInitialScreen")

    data object SkipForNowScreen : OAScreen("SkipForNowScreen")

    data object FTUERevisitScreen : OAScreen("FTUERevisitScreen")

    data object AnnouncementList : OAScreen("announcementList")

    data object VehicleSoftwareScreen : OAScreen("VehicleSoftwareScreen")

    data object ClimateScreen : OAScreen("climateScreen")

    data object ScheduleDetailScreen : OAScreen("scheduleDetailScreen")

    data object ClimatePreferenceScreen : OAScreen("climatePreferenceScreen")

    data object SoftwareUpdate21mmScreen : OAScreen("softwareUpdate21mmScreen")

    data object SoftwareUpdate21mmDetailsScreen : OAScreen("softwareUpdate21mmDetailsScreen")

    data object SoftwareUpdateAvailable21mmScreen : OAScreen("softwareUpdateAvailable21mmScreen")

    data object GuestDriver : OAScreen("GuestDriver")

    data object DrivingLimitSelectionScreen : OAScreen("drivingLimitSelection")

    data object DrivingLimitListScreen : OAScreen("drivingLimits")

    data object SpeedSettingsScreen : OAScreen("speedSettings")

    data object MilesSettingsScreen : OAScreen("milesSettings")

    data object AreaSettingsScreen : OAScreen("areaSettings")

    data object CurfewSettingsScreen : OAScreen("curfewSettings")

    data object TimeSettingsScreen : OAScreen("timeSettings")

    data object IgnitionSettingsScreen : OAScreen("ignitionSettings")

    data object RemoteSharingScreen : OAScreen("remoteSharing")

    data object AddDriverScreen : OAScreen("addDriver")

    data object InviteDriverScuccessScreen : OAScreen("inviteDriverSuccess")

    data object DealerServiceAppointment : OAScreen("dealerServiceAppointment")

    data object ChargeInfo : OAScreen("chargeinfo")

    data object EvgoSuccessScreen : OAScreen("evgoSuccessScreen")

    data object CleanAssistEnrollScreen : OAScreen("cleanAssistDetailScreen")

    data object CleanAssistMetricsScreen : OAScreen("cleanAssistMetricsScreen")

    data object ChargeSchedule : OAScreen("chargeSchedule")

    data object WalletHomeScreen : OAScreen("WalletHomeScreen")

    data object WalletTransactionsScreen : OAScreen("WalletTransactionScreen")

    data object AddEVCardScreen : OAScreen("AddEVCardScreen")

    data object ChargeManagementNestedRoute : OAScreen("chargeManagementNestedRoute")

    data object EvgoRegScreen : OAScreen("evgoRegScreen")

    data object EvgoMainScreen : OAScreen("evgoMainScreen")

    data object EvgoSignScreen : OAScreen("evgoSignScreen")

    data object EvgoForgotPasswordScreen : OAScreen("evgoSuccessScreen")

    data object ChargeStation : OAScreen("chargeStation")

    data object ChargeStationListScreen : OAScreen("chargeStationListScreen")

    data object ChargeStationDetailsScreen : OAScreen("chargeStationDetailsScreen")

    data object ChargeSettingDisclaimerScreen : OAScreen("chargeSettingDisclaimerScreen")

    data object ChargePointMainScreen : OAScreen("chargePointMainScreen")

    data object DataConsentDetailsScreen : OAScreen("dataConsentDetailsScreen")

    data object PublicChargingStatusScreen : OAScreen("publicChargingStatusScreen")

    data object TariffDetailsScreen : OAScreen("tariffDetailsScreen")

    data object ChargeStationSelectConnectorsScreen : OAScreen("chargeStationSelectConnectorsScreen")
}
