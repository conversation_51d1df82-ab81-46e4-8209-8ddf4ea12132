/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.location.dataaccess

import com.toyota.oneapp.core.BuildWrapper
import com.toyota.oneapp.features.core.location.domain.GeoLocationService
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GeoLocationServiceFactory
    @Inject
    constructor(
        private val geocoderProvider: GeocoderProvider,
        private val buildWrapper: BuildWrapper,
    ) {
        fun create(): GeoLocationService {
            return if (buildWrapper.isNewerAndroidTiramisu()) {
                GeoLocationDefaultService(geocoderProvider)
            } else {
                GeoLocationLegacyService(geocoderProvider)
            }
        }
    }
