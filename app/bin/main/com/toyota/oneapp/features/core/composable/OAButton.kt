/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.composable

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.ContentPreview

@Composable
private fun _OAButton(
    modifier: Modifier? = Modifier,
    textModifier: Modifier =
        Modifier.padding(
            top = 16.dp,
            bottom = 16.dp,
            start = 21.dp,
            end = 21.dp,
        ),
    text: String,
    bgColor: Color = AppTheme.colors.button01a,
    borderStroke: BorderStroke = BorderStroke(0.dp, Color.Transparent),
    textColor: Color = AppTheme.colors.tertiary00,
    click: () -> Unit,
    shape: RoundedCornerShape = RoundedCornerShape(100.dp),
) {
    Button(
        onClick = click,
        colors =
            ButtonDefaults.buttonColors(
                backgroundColor = bgColor,
            ),
        border = borderStroke,
        shape = shape,
        modifier = modifier ?: Modifier.padding(all = 0.dp),
    ) {
        OACallOut2TextView(
            text = text,
            color = textColor,
            modifier = textModifier,
        )
    }
}

@Composable
fun PrimaryButton02(
    text: String,
    modifier: Modifier,
    enabled: Boolean = true,
    click: () -> Unit,
) {
    Button(
        onClick = click,
        shape = RoundedCornerShape(100.dp),
        colors =
            ButtonDefaults.buttonColors(
                contentColor = AppTheme.colors.primaryButton01,
                backgroundColor = AppTheme.colors.primaryButton02,
            ),
        contentPadding = PaddingValues(vertical = 16.dp, horizontal = 60.dp),
        modifier = modifier,
        enabled = enabled,
    ) {
        OAButtonTextView(
            text = text,
            color = AppTheme.colors.primaryButton01,
        )
    }
}

@Composable
fun ButtonCallOut02(
    text: String,
    textColor: Color,
    bgColor: Color = AppTheme.colors.button02a,
    click: () -> Unit,
    textModifier: Modifier =
        Modifier.padding(
            top = 16.dp,
            bottom = 16.dp,
            start = 21.dp,
            end = 21.dp,
        ),
) {
    OAButton(
        bgColor = bgColor,
        text = text,
        textColor = textColor,
        textModifier = textModifier,
        click = click,
    )
}

@Composable
fun OAButton(
    text: String,
    textColor: Color = AppTheme.colors.primaryButton01,
    bgColor: Color = AppTheme.colors.primaryButton02,
    borderStroke: BorderStroke = BorderStroke(0.dp, Color.Transparent),
    shape: RoundedCornerShape = RoundedCornerShape(100.dp),
    click: () -> Unit,
    modifier: Modifier = Modifier,
    textModifier: Modifier =
        Modifier.padding(
            top = 16.dp,
            bottom = 16.dp,
            start = 66.dp,
            end = 66.dp,
        ),
) {
    _OAButton(
        bgColor = bgColor,
        borderStroke = borderStroke,
        text = text,
        shape = shape,
        textColor = textColor,
        modifier = modifier,
        textModifier = textModifier,
        click = click,
    )
}

@Composable
fun ButtonLink1(
    bgColor: Color,
    color: Color = AppTheme.colors.button02a,
    shape: RoundedCornerShape = RoundedCornerShape(100.dp),
    modifier: Modifier =
        Modifier.padding(
            top = 16.dp,
            bottom = 16.dp,
            start = 21.dp,
            end = 21.dp,
        ),
    text: String,
    onClick: () -> Unit,
) {
    OAButton(
        textColor = color,
        textModifier = modifier,
        bgColor = bgColor,
        shape = shape,
        text = text,
        click = onClick,
    )
}

@Composable
fun OAPrimaryButton(
    text: String,
    textColor: Color = AppTheme.colors.primaryButton01,
    bgColor: Color = AppTheme.colors.primaryButton02,
    minWith: Dp = 192.dp,
    modifier: Modifier = Modifier,
    textModifier: Modifier = Modifier,
    enabled: Boolean = true,
    onClick: () -> Unit,
) {
    Button(
        shape = RoundedCornerShape(100.dp),
        colors = ButtonDefaults.buttonColors(backgroundColor = bgColor),
        contentPadding = PaddingValues(vertical = 16.dp, horizontal = 32.dp),
        modifier = modifier.widthIn(minWith),
        enabled = enabled,
        onClick = onClick,
    ) {
        OAButtonTextView(
            text = text,
            color = textColor,
            modifier = textModifier,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun OAButtonLightPreview() {
    ContentPreview(isDarkMode = false) {
        OAButtonPreview()
    }
}

@Preview(showBackground = true)
@Composable
private fun OAButtonDarkPreview() {
    ContentPreview(isDarkMode = true) {
        OAButtonPreview()
    }
}

@Composable
private fun OAButtonPreview() {
    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        ButtonLink1(
            color = AppTheme.colors.primaryButton01,
            bgColor = AppTheme.colors.primaryButton02,
            text = "ButtonLink1",
            onClick = {},
        )
        OAButton(
            text = "OAButton",
            click = {},
        )
        PrimaryButton02(
            text = "PrimaryButton02",
            modifier = Modifier,
            enabled = true,
            click = {},
        )
        OAPrimaryButton(
            text = "Save",
            onClick = {},
        )
        OAPrimaryButton(
            text = "Share Remote",
            onClick = {},
        )

        OAPrimaryButton(
            text = "Define Driving Limit",
            onClick = {},
        )
        OAPrimaryButton(
            text = "If Not You Then Who, If Not Now Then When",
            onClick = {},
        )
    }
}
