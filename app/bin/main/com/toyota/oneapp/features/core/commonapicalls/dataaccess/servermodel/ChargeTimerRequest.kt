/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.Time

data class ChargeTimerRequest(
    @SerializedName("command") val command: String,
    @SerializedName("reservationCharge") val reservationCharge: Reservation?,
    @SerializedName("remoteHvac") val remoteHvac: Any? = null,
)

data class Reservation(
    @SerializedName("chargeType") val chargeType: String,
    @SerializedName("day") val day: String,
    @SerializedName("startTime") val startTime: Time?,
    @SerializedName("endTime") val endTime: Time?,
)
