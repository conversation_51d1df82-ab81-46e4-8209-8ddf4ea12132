/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.location.di

import com.toyota.oneapp.features.core.location.application.GeoLocationLogic
import com.toyota.oneapp.features.core.location.dataaccess.GeoLocationDefaultRepository
import com.toyota.oneapp.features.core.location.dataaccess.GeoLocationServiceFactory
import com.toyota.oneapp.features.core.location.domain.GeoLocationRepository
import com.toyota.oneapp.features.core.location.domain.GeoLocationUseCase
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
internal interface GeoLocationModule {
    @Binds
    fun bindGeoLocationUseCase(useCase: GeoLocationLogic): GeoLocationUseCase

    @Singleton
    @Binds
    fun bindGeoLocationRepository(repo: GeoLocationDefaultRepository): GeoLocationRepository

    companion object {
        @Singleton
        @Provides
        fun provideGeoLocationService(factory: GeoLocationServiceFactory) = factory.create()
    }
}
