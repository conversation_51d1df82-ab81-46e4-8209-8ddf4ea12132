package com.toyota.oneapp.features.core.theme.colors

import androidx.compose.ui.graphics.Color

object RemoteCommandProgressColors {
    val blueCircularColor: List<Color> =
        listOf(
            Color(0xFFDEDEDE),
            Color(0xFFDEDEDE),
            Color(0xFFDEDEDE),
            Color(0xFFDEDEDE),
            Color(0xFFDEDEDE),
            Color(0xFFDEDEDE),
            Color(0xFFA5BDF5),
            Color(0xFF7EA2F5),
            Color(0xFF6A94F5),
            Color(0xFF5B82F9),
        )

    val redCircularColor: List<Color> =
        listOf(
            Color(0xFFDEDEDE),
            Color(0xFFDEDEDE),
            Color(0xFFDEDEDE),
            Color(0xFFDEDEDE),
            Color(0xFFDEDEDE),
            Color(0xFFDEDEDE),
            Color(0xFFF0BDC2),
            Color(0xFFEC9AA2),
            Color(0xFFF16975),
            Color(0xFFEB0A1E),
        )
}
