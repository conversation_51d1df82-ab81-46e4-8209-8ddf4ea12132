/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.date

import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import javax.inject.Inject

private const val ISO8601_FULL_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
private const val ISO8601_SHORT_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'"
private const val ISO8601_DATE_ONLY_FORMAT = "yyyy-MM-dd"
private const val ISO8601_FULL_DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS"
private const val ISO8601_SHORT_DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss"
private const val NON_TIMEZONE_FULL_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS"
private const val NON_TIMEZONE_SHORT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss"

interface DateParser {
    var nextParser: DateParser?

    fun parse(dateString: String): Date?

    fun setNextParser(nextParser: DateParser): DateParser {
        this.nextParser = nextParser
        return nextParser
    }
}

abstract class BaseDateParser(
    format: String,
    private val useUtc: Boolean = true,
) : DateParser {
    override var nextParser: DateParser? = null
    private val inputFormat =
        SimpleDateFormat(format, Locale.getDefault()).apply {
            if (useUtc) {
                timeZone = TimeZone.getTimeZone("UTC")
            }
        }

    override fun parse(dateString: String) =
        try {
            inputFormat.parse(dateString)
        } catch (e: ParseException) {
            nextParser?.parse(dateString)
        }
}

internal class Iso8601FullDateParser : BaseDateParser(ISO8601_FULL_DATE_FORMAT)

internal class Iso8601ShortDateParser : BaseDateParser(ISO8601_SHORT_DATE_FORMAT)

internal class Iso8601DateOnlyParser : BaseDateParser(ISO8601_DATE_ONLY_FORMAT)

internal class Iso8601FullDateTimeParser : BaseDateParser(ISO8601_FULL_DATE_TIME_FORMAT)

internal class Iso8601ShortDateTimeParser : BaseDateParser(ISO8601_SHORT_DATE_TIME_FORMAT)

internal class NonTimezoneFullDateTimeParser : BaseDateParser(NON_TIMEZONE_FULL_DATE_TIME_FORMAT, useUtc = false)

internal class NonTimezoneShortDateTimeParser : BaseDateParser(NON_TIMEZONE_SHORT_DATE_TIME_FORMAT, useUtc = false)

class DateParseHelper
    @Inject
    constructor() {
        private val iso8601FullDateParser = Iso8601FullDateParser()
        private val iso8601ShortDateParser = Iso8601ShortDateParser()
        private val iso8601DateOnlyParser = Iso8601DateOnlyParser()
        private val iso8601FullDateTimeParser = Iso8601FullDateTimeParser()
        private val iso8601ShortDateTimeParser = Iso8601ShortDateTimeParser()
        private val nonTimezoneFullDateTimeParser = NonTimezoneFullDateTimeParser()
        private val nonTimezoneShortDateTimeParser = NonTimezoneShortDateTimeParser()

        init {
            iso8601FullDateParser
                .setNextParser(iso8601ShortDateParser)
                .setNextParser(iso8601DateOnlyParser)
                .setNextParser(iso8601FullDateTimeParser)
                .setNextParser(iso8601ShortDateTimeParser)
                .setNextParser(nonTimezoneFullDateTimeParser)
                .setNextParser(nonTimezoneShortDateTimeParser)
        }

        fun parse(dateString: String) = iso8601FullDateParser.parse(dateString)
    }
