package com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel

data class EVChargeSessionResponse(
    val messages: Messages,
    val payload: EVChargeSessionPayload?,
)

data class Messages(
    val responseCode: Long,
    val description: String,
    val detailedDescription: String,
)

data class EVChargeSessionPayload(
    val sessionDetails: SessionDetails,
)

data class SessionDetails(
    val id: String,
    val chargingId: String,
    val data: Data,
    val timestamp: String,
    val createdAt: String,
    val updatedAt: String,
)

data class Data(
    val lastUpdated: String,
    val startDatetime: String,
    val location: Location,
    val id: String,
    val status: String,
    val startOn: String,
    val stopOn: String,
    val lastMeterReadOn: String,
)

data class Location(
    val id: String,
)
