/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.util

import com.toyota.oneapp.features.fuelwidget.presentation.utils.FuelWidgetConstants

object EvFormatterUtil {
    fun formattedBatteryPercentage(percentValue: Any?): String {
        val numPercent: Double?
        if (percentValue != null) {
            return try {
                numPercent = percentValue.toString().toDoubleOrNull()
                val regex = Regex("([.]*0)(?!.*\\d)")
                val doubleNumberNoTrailingZero = numPercent?.toString()?.replace(regex, "")
                if (numPercent != null && numPercent >= 0) {
                    doubleNumberNoTrailingZero.toString()
                } else {
                    FuelWidgetConstants.NEGATIVE_PERCENTAGE
                }
            } catch (e: NumberFormatException) {
                FuelWidgetConstants.NEGATIVE_PERCENTAGE
            }
        }
        return FuelWidgetConstants.NEGATIVE_PERCENTAGE
    }
}
