/*
 *  Created by sudhan.ram on 30/10/24, 10:55 am
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 30/10/24, 10:55 am
 *
 */

package com.toyota.oneapp.features.core.commonapicalls.application

import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse

sealed class ElectricStatusState {
    object Idle : ElectricStatusState()

    object Loading : ElectricStatusState()

    class Success(val response: ElectricStatusResponse?) : ElectricStatusState()

    class Error(val errorCode: String? = null, val errorMessage: String? = null) : ElectricStatusState()
}
