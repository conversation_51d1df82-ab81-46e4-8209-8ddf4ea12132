/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.composable

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.ContentPreview

@Composable
fun OACheckBox(
    isSelected: Boolean = false,
    modifier: Modifier = Modifier.size(24.dp),
) {
    Box(
        modifier =
            modifier
                .clip(CircleShape)
                .border(width = 2.dp, color = AppTheme.colors.button02c, shape = CircleShape)
                .background(AppTheme.colors.tertiary12, shape = CircleShape),
        contentAlignment = Alignment.Center,
    ) {
        if (isSelected) {
            Icon(
                painter = painterResource(id = R.drawable.ic_check_icon),
                contentDescription = "",
                tint = AppTheme.colors.button03b,
            )
        }
    }
}

@Preview
@Composable
fun OACheckBoxLightPreview() {
    ContentPreview(
        isDarkMode = false,
        modifier = Modifier.fillMaxWidth().height(120.dp),
    ) {
        Column {
            OACheckBox()
            Spacer(modifier = Modifier.height(4.dp))
            OACheckBox(isSelected = true)
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun OACheckBoxDarkPreview() {
    ContentPreview(
        isDarkMode = true,
        modifier = Modifier.fillMaxWidth().height(120.dp),
    ) {
        Column {
            OACheckBox()
            Spacer(modifier = Modifier.height(4.dp))
            OACheckBox(isSelected = true)
        }
    }
}
