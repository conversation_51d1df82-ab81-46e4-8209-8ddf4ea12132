package com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel

data class ProfileInfoResponse(
    val status: ProfileInfoStatus,
    val payload: ProfileInfoPayload,
)

data class ProfileInfoStatus(
    val messages: List<ProfileInfoMessage>,
)

data class ProfileInfoMessage(
    val responseCode: String,
    val description: String,
)

data class ProfileInfoPayload(
    val customer: ProfileInfoCustomer,
)

data class ProfileInfoCustomer(
    val guid: String,
    val forgerockId: String,
    val customerType: String,
    val firstName: String,
    val lastName: String,
    val phoneNumbers: List<PhoneNumber>?,
    val addresses: List<ProfileInfoAddress>?,
    val emails: List<Email>,
    val accountStatus: String,
    val uiLanguage: String,
    val preferredLanguage: String,
    val updateUserId: String,
    val createSource: String,
    val lastUpdateSource: String,
    val createDate: Long,
    val lastUpdateDate: Long,
)

data class PhoneNumber(
    val countryCode: Long,
    val phoneVerified: Boolean,
    val phoneNumber: Long,
    val phoneType: String,
    val verificationDate: Long,
)

data class ProfileInfoAddress(
    val addressType: String,
    val city: String,
    val state: String,
    val zipCode: String,
    val country: String,
    val addressVerified: Boolean,
    val address: String,
)

data class Email(
    val emailAddress: String,
    val emailType: String,
    val emailVerified: Boolean,
    val verificationDate: Long,
)
