package com.toyota.oneapp.features.core.theme.colors

import androidx.compose.ui.graphics.Color

abstract class OAColors {
    abstract val success01: Color
    abstract val success02: Color

    abstract val error01: Color

    abstract val brand01: Color

    abstract val primary01: Color
    abstract val primary02: Color
    abstract val primaryButton01: Color
    abstract val primaryButton02: Color

    abstract val secondary01: Color
    abstract val secondary02: Color

    abstract val tertiary00: Color
    abstract val tertiary03: Color
    abstract val tertiary03a: Color
    abstract val tertiary05: Color
    abstract val tertiary07: Color
    abstract val tertiary10: Color
    abstract val tertiary12: Color
    abstract val tertiary15: Color
    abstract val dashboard01: Color

    abstract val button01a: Color
    abstract val button01b: Color
    abstract val button02a: Color
    abstract val button02b: Color
    abstract val button02c: Color
    abstract val button03a: Color
    abstract val button03b: Color
    abstract val button03c: Color
    abstract val button03d: Color
    abstract val button03e: Color
    abstract val button05a: Color
    abstract val button05b: Color

    abstract val button02d: Color
    abstract val tile01: Color
    abstract val tile02: Color
    abstract val tile03: Color
    abstract val tile05: Color
    abstract val primaryLightBlue: Color
    abstract val primaryLight02: Color

    abstract val noImageBGColor: Color
    abstract val outline01: Color
    abstract val statusBarColorLight: Color
    abstract val statusBarColorDark: Color

    abstract val gradientThreeStart: Color
}
