/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.location.dataaccess

import android.content.Context
import android.location.Geocoder
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GeocoderProvider
    @Inject
    constructor(private val context: Context) {
        fun getGeocoder() = Geocoder(context, Locale.getDefault())
    }
