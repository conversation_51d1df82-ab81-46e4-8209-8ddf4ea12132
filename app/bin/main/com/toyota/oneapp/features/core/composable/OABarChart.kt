/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.core.composable

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.toyota.oneapp.features.core.theme.AppTheme

const val BUFFER_MAX_HEIGHT = 20
const val MIN_ITEM_HEIGHT = 2

@Composable
fun OABarChart(
    barDataModel: BarDataModel,
    modifier: Modifier = Modifier,
    maxHeight: Dp = 200.dp,
) {
    Row(
        modifier =
            modifier.then(
                Modifier
                    .fillMaxWidth()
                    .height((maxHeight.value + BUFFER_MAX_HEIGHT).dp),
            ),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Bottom,
    ) {
        barDataModel.dataPoints.forEach { item ->
            Bar(
                maxValue = barDataModel.maxValue,
                dataPoint = item,
                maxHeight = maxHeight,
            )
        }
    }
}

@Composable
private fun RowScope.Bar(
    maxValue: Double,
    dataPoint: BarDataPoint,
    maxHeight: Dp,
    modifier: Modifier = Modifier,
) {
    val percent = (dataPoint.value / maxValue) * 100
    val itemHeight = remember(percent) { percent * maxHeight.value / 100 }

    Column(
        verticalArrangement = Arrangement.Bottom,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier =
            modifier
                .height((maxHeight.value + BUFFER_MAX_HEIGHT).dp)
                .padding(horizontal = 2.dp)
                .weight(1f),
    ) {
        Spacer(
            modifier =
                Modifier
                    .height(
                        if (itemHeight > 0) {
                            itemHeight.dp
                        } else {
                            MIN_ITEM_HEIGHT.dp
                        },
                    ).fillMaxWidth()
                    .clip(RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp))
                    .background(
                        if (dataPoint.isHighlight) {
                            AppTheme.colors.button03b
                        } else {
                            AppTheme.colors.button02c
                        },
                    ),
        )

        Spacer(modifier = Modifier.height(4.dp))

        OATabLabel01TextView(
            text = dataPoint.barLabel,
            fontSize = 10.sp,
            maxLine = 1,
            color = AppTheme.colors.tertiary03,
        )
    }
}

data class BarDataModel(
    val maxValue: Double,
    val dataPoints: List<BarDataPoint>,
)

data class BarDataPoint(
    val value: Double,
    val barLabel: String,
    val isHighlight: Boolean,
)
