package com.toyota.oneapp.features.core.presentation

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController

@Composable
fun <State, ScreenEvent> BaseViewWrapper(
    navHostController: NavHostController,
    viewModel: BaseViewModel<State, ScreenEvent>,
    content: @Composable () -> Unit,
) {
    val scaffoldState = rememberScaffoldState()

    LaunchedEffect(key1 = Unit) {
        viewModel.uiEvent.collect { uiEvent ->
            when (uiEvent) {
                is UiEvent.Error -> {
                }
                is UiEvent.Navigate -> {
                    navHostController.navigate(uiEvent.route)
                }
            }
        }
    }

    Scaffold(scaffoldState = scaffoldState, modifier = Modifier.fillMaxSize()) { padding ->
        Box(modifier = Modifier.padding(padding)) {
            content()
        }
    }
}
