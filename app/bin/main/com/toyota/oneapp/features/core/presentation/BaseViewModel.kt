package com.toyota.oneapp.features.core.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

abstract class BaseViewModel<State, ScreenEvent> : ViewModel() {
    // Generic UI State
    internal val state = MutableStateFlow(this.defaultState())
    val uiState = state.asStateFlow()

    // Communication from ViewModel to BaseViewWrapper-UI
    private val uiEventChannel = Channel<UiEvent> { }
    val uiEvent = uiEventChannel.receiveAsFlow()

    // Set Current Screen State in ViewModel
    protected abstract fun defaultState(): State

    // Communication from UI to ViewModel
    internal abstract fun onEvent(event: ScreenEvent)

    // For Navigation & Display Common Error
    protected fun sendEvent(event: UiEvent) {
        viewModelScope.launch {
            uiEventChannel.send(event)
        }
    }
}
