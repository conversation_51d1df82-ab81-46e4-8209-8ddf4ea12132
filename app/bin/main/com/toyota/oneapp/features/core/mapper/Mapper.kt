/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.core.mapper

interface Mapper<Data, Domain> {
    fun to(data: Data): Domain
}

interface MapperList<Data, Domain> {
    fun toList(data: Data): List<Domain>
}

interface MapperDetail<Data, Domain> {
    fun toDetail(data: Data): Domain
}

interface MapperEdit<Data, Domain> {
    fun toEdit(data: Data): Domain
}

interface BodyRequestMapper<Domain, Request> {
    fun toBodyRequest(request: Domain): Request
}
