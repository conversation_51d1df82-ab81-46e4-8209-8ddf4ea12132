package com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel

data class RealTimeStatusResponse(
    val payload: RealTimeStatusPayload,
    val status: RealTimeStatusResponseStatus,
    val timestamp: String,
)

data class RealTimeStatusPayload(
    val returnCode: String,
    val appRequestNo: String,
)

data class RealTimeStatusResponseStatus(
    val messages: List<RealTimeStatusResponseStatusMessage>,
)

data class RealTimeStatusResponseStatusMessage(
    val responseCode: String,
    val description: String,
    val detailedDescription: String,
)
