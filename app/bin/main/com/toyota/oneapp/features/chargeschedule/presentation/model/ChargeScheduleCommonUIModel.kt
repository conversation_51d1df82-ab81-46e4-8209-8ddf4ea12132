/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeschedule.presentation.model

import androidx.annotation.StringRes
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil

data class TimeObj(
    var time24HrFormat: String = "",
    var hour: String = "",
    var minute: String = "",
    var ampm: String = "",
)

data class DaysOfWeekObj(
    var dayId: Int,
    var dayName: String,
    @StringRes var dayNameRes: Int,
    var isDaySelected: Boolean,
)

data class ScheduleResult<T>(
    val result: T?,
    val errorMessage: String?,
)

fun TimeObj.toPickedTime(
    pickedHour: Int,
    pickedMin: Int,
) {
    val pickedTime =
        ChargeScheduleUtil.convert24HrTimeTo12Hr(
            time = "$pickedHour:$pickedMin",
        )
    val splittedTime = pickedTime?.split(" ")
    splittedTime?.let {
        time24HrFormat = "$pickedHour:$pickedMin"
        hour = it.first().split(":").first()
        minute = it.first().split(":").last()
        ampm = it.last().uppercase()
    }
}

fun TimeObj.toPickedAMPM(
    isAM: Boolean,
    time24HrFormat: String,
) {
    ampm = if (isAM) "AM" else "PM"
    this.time24HrFormat = time24HrFormat
}
