package com.toyota.oneapp.features.chargeschedule.application

import com.toyota.oneapp.features.chargeschedule.domain.model.EcoScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.MultiDayScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.PHEVScheduleModel

sealed class ChargeScheduleState {
    object Init : ChargeScheduleState()

    object MultiDayScheduleLoading : ChargeScheduleState()

    data class SchedulingList(
        val vehicleBrand: String,
        val uiModel: MultiDayScheduleModel.ListMultiDayScheduleModel,
    ) : ChargeScheduleState()

    data class PHEVScheduleTimer(
        val uiModel: PHEVScheduleModel.PHEVScheduleSuccessModel,
    ) : ChargeScheduleState()

    data class NoSchedule(
        val vehicleBrand: String,
        val isMultiDaySchedule: Boolean,
        val errorMessage: Int?,
    ) : ChargeScheduleState()

    data class RefreshLastUpdateFailure(
        val errorMessage: String,
    ) : ChargeScheduleState()
}

sealed class EcoSchduleCardState {
    object Init : EcoSchduleCardState()

    object Loading : EcoSchduleCardState()

    data class Success(
        val uiModel: EcoScheduleModel,
    ) : EcoSchduleCardState()
}

sealed class EditScheduleState {
    object Init : EditScheduleState()

    object Loading : EditScheduleState()

    object LoadContent : EditScheduleState()

    data class RefreshMultiDaySchedule(
        val appRequestNo: String,
    ) : EditScheduleState()

    object Error : EditScheduleState()
}

sealed class PHEVScheduleDetailState {
    object Init : PHEVScheduleDetailState()

    object Loading : PHEVScheduleDetailState()

    object LoadContent : PHEVScheduleDetailState()

    object RefreshPHEVSchedule : PHEVScheduleDetailState()
}
