package com.toyota.oneapp.features.chargeschedule.domain.repo

import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.CreateScheduleRequest
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.EcoScheduleDetailsResponse
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.UpdateScheduleRequest
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.CommonScheduleResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource

interface ChargeScheduleRepo {
    suspend fun fetchEcoScheduleDetails(vin: String): Resource<EcoScheduleDetailsResponse?>

    suspend fun saveEcoSchedulesDetails(
        vin: String,
        isEcoScheduleEnabled: Boolean,
        settingsId: String,
    ): Resource<EcoScheduleDetailsResponse?>

    suspend fun createMultiDaySchedule(
        vehicleInfo: VehicleInfo,
        requestBody: CreateScheduleRequest,
    ): Resource<CommonScheduleResponse?>

    suspend fun deleteMultiDayChargeSchedule(
        scheduleId: String,
        vehiceInfo: VehicleInfo,
    ): Resource<CommonScheduleResponse?>

    suspend fun updateMultiDayChargeSchedule(
        vehicleInfo: VehicleInfo,
        request: UpdateScheduleRequest,
    ): Resource<CommonScheduleResponse?>
}
