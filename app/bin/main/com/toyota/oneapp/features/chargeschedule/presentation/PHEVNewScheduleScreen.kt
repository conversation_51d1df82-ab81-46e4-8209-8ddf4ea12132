package com.toyota.oneapp.features.chargeschedule.presentation

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Divider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeschedule.application.PHEVScheduleDetailState
import com.toyota.oneapp.features.chargeschedule.presentation.model.PHEVScheduleDetailUIModel
import com.toyota.oneapp.features.chargeschedule.presentation.model.TimeObj
import com.toyota.oneapp.features.chargeschedule.presentation.previewprovider.PHEVScheduleDetailUIModelProvider
import com.toyota.oneapp.features.chargeschedule.presentation.widgets.AMPMWidget
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.CONST_AM
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeInfo
import com.toyota.oneapp.features.core.composable.CustomSwitch
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OAFullScreenBottomSheetLayout
import com.toyota.oneapp.features.core.composable.OASubHeadLine2TextView
import com.toyota.oneapp.features.core.composable.OATimePicker
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.TimeTextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.presentation.ToastUiEvent
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.CommonUtil

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun PHEVNewScheduleScreen(
    navController: NavHostController,
    isStartScheduleFlow: Boolean,
    viewModel: ChargeScheduleViewModel,
    phevViewModel: PHEVNewScheduleViewModel = hiltViewModel(),
    chargeInfo: ChargeInfo?,
    modifier: Modifier = Modifier,
) {
    val viewState by phevViewModel.phevScheduleDetailState.collectAsState()
    val uiModel by phevViewModel.state.collectAsState()
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        phevViewModel.uiEvent.collect { toastEvent ->
            if (toastEvent is ToastUiEvent.ToastFromStringRes) {
                CommonUtil.showToast(context, context.getString(toastEvent.messageRes))
            }
        }
    }

    BackHandler {
        navController.popBackStack()
    }

    LaunchedEffect(Unit) { phevViewModel.reset() }

    LaunchedEffect(viewState is PHEVScheduleDetailState.RefreshPHEVSchedule) {
        if (viewState is PHEVScheduleDetailState.RefreshPHEVSchedule) {
            navController.popBackStack()
            phevViewModel.reset()
            viewModel.refreshLastUpdatedData(false)
        }
    }

    when (viewState) {
        is PHEVScheduleDetailState.Init -> {
            phevViewModel.onEvent(PHEVScheduleEvents.InitScreen(isStartScheduleFlow, chargeInfo))
        }
        is PHEVScheduleDetailState.Loading -> {
            ShowProgressIndicator(dialogState = true)
        }
        is PHEVScheduleDetailState.LoadContent -> {
            ScheduleDetailContent(
                uiModel = uiModel,
                modifier = modifier,
                onTimePicked = { hour, min ->
                    phevViewModel.onEvent(PHEVScheduleEvents.OnTimePicked(hour, min))
                },
                onAMPMSelected = { isAM, time ->
                    phevViewModel.onEvent(PHEVScheduleEvents.OnAMPMSelected(isAM, time))
                },
                onClimateCheckChanged = { isEnabled ->
                    phevViewModel.onEvent(PHEVScheduleEvents.OnClimateCheckChanged(isEnabled))
                },
                onSaveSchedule = { isStartFlow ->
                    phevViewModel.onEvent(PHEVScheduleEvents.SaveSchedule(isStartFlow))
                },
                onBackPressed = {
                    navController.popBackStack()
                },
            )
        }
        else -> {
            // Do nothing
        }
    }
}

@Composable
private fun ScheduleDetailContent(
    uiModel: PHEVScheduleDetailUIModel,
    modifier: Modifier = Modifier,
    onTimePicked: (hour: Int, min: Int) -> Unit,
    onAMPMSelected: (isAM: Boolean, time: String) -> Unit,
    onClimateCheckChanged: (isEnabled: Boolean) -> Unit,
    onSaveSchedule: (isStartFlow: Boolean) -> Unit,
    onBackPressed: () -> Unit,
) {
    OAFullScreenBottomSheetLayout(
        backgroundColor = AppTheme.colors.tertiary15,
        screenTitle = stringResource(id = R.string.new_schedule),
        testTagId = AccessibilityId.ID_PHEV_DETAIL_BACK_BTN,
        onBack = { onBackPressed() },
        modifier =
            modifier
                .padding(vertical = 8.dp, horizontal = 16.dp),
    ) {
        Column {
            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .weight(1f, fill = false),
            ) {
                ScheduleDetailCard(
                    uiModel = uiModel,
                    modifier =
                        Modifier
                            .padding(vertical = 8.dp)
                            .testTagID(AccessibilityId.ID_PHEV_DETAIL_CARD),
                    onTimePicked = { hour, min ->
                        onTimePicked(hour, min)
                    },
                    onAMPMSelected = { isAM, time ->
                        onAMPMSelected(isAM, time)
                    },
                    onClimateCheckChanged = { isEnabled ->
                        onClimateCheckChanged(isEnabled)
                    },
                )
            }

            ConfirmButton(
                modifier =
                    Modifier
                        .testTagID(AccessibilityId.ID_PHEV_CONFIRM_BTN),
            ) {
                onSaveSchedule(uiModel is PHEVScheduleDetailUIModel.PHEVStartTimeDetailUIModel)
            }
        }
    }
}

@Composable
private fun ScheduleDetailCard(
    uiModel: PHEVScheduleDetailUIModel,
    modifier: Modifier = Modifier,
    onTimePicked: (hour: Int, min: Int) -> Unit,
    onAMPMSelected: (isAM: Boolean, time: String) -> Unit,
    onClimateCheckChanged: (isEnabled: Boolean) -> Unit,
) {
    Card(
        shape = RoundedCornerShape(8.dp),
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tile02,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(12.dp),
        ) {
            OASubHeadLine2TextView(
                text = stringResource(id = uiModel.title),
                color = AppTheme.colors.tertiary03,
            )

            Divider(
                color = AppTheme.colors.tertiary10,
                modifier =
                    Modifier
                        .padding(vertical = 24.dp),
            )

            OACallOut1TextView(
                text = stringResource(id = uiModel.subTitle),
                color = AppTheme.colors.tertiary05,
            )

            ScheduleTimePicker(
                uiModel = uiModel.timeObj,
                modifier =
                    Modifier
                        .padding(vertical = 24.dp)
                        .testTagID(AccessibilityId.ID_PHEV_TIME_PICKER),
                onTimePicked = { hour, min ->
                    onTimePicked(hour, min)
                },
                onAMPMSelected = { isAM, time ->
                    onAMPMSelected(isAM, time)
                },
            )

            Divider(
                color = AppTheme.colors.tertiary10,
                modifier =
                    Modifier
                        .padding(bottom = 24.dp),
            )

            OABody4TextView(
                text = stringResource(id = R.string.days_of_week),
                color = AppTheme.colors.tertiary03,
            )

            DayOfWeekLayout(
                uiModel = uiModel,
                modifier =
                    Modifier
                        .padding(vertical = 16.dp),
            )

            if (uiModel is PHEVScheduleDetailUIModel.PHEVDepartureTimeDetailUIModel) {
                Divider(
                    color = AppTheme.colors.tertiary10,
                )

                ClimateLayout(
                    uiModel = uiModel,
                    modifier =
                        Modifier
                            .padding(vertical = 24.dp),
                ) { isEnabled ->
                    onClimateCheckChanged(isEnabled)
                }
            }
        }
    }
}

@Composable
private fun ScheduleTimePicker(
    uiModel: TimeObj?,
    modifier: Modifier = Modifier,
    onTimePicked: (hour: Int, min: Int) -> Unit,
    onAMPMSelected: (isAM: Boolean, time: String) -> Unit,
) {
    var isAMSelected by remember { mutableStateOf(false) }
    isAMSelected = uiModel?.ampm == CONST_AM
    var selectedTime by remember { mutableStateOf("") }
    selectedTime = "${uiModel?.hour} : ${uiModel?.minute}"

    Row(
        horizontalArrangement = Arrangement.Center,
        modifier =
            modifier
                .wrapContentSize(),
    ) {
        OATimePicker(
            selectedHour = 12,
            selectedMin = 0,
            isAM = true,
            content = { click ->
                TimeTextView(
                    time = selectedTime,
                    onClick = click,
                )
            },
        ) { pickedHour, pickedMin ->
            onTimePicked(pickedHour, pickedMin)
            selectedTime = "${uiModel?.hour} : ${uiModel?.minute}"
            isAMSelected = uiModel?.ampm == CONST_AM
        }

        AMPMWidget(
            timeObj = uiModel,
            isAMSelected = isAMSelected,
        ) { isAM, time24HrFormat ->
            onAMPMSelected(isAM, time24HrFormat)
            isAMSelected = uiModel?.ampm == CONST_AM
        }
    }
}

@Composable
private fun DayOfWeekLayout(
    uiModel: PHEVScheduleDetailUIModel,
    modifier: Modifier = Modifier,
) {
    var selectedDay by remember { mutableStateOf("") }
    selectedDay = uiModel.selectedDay

    Row(
        horizontalArrangement = Arrangement.SpaceEvenly,
        modifier =
            modifier
                .fillMaxWidth()
                .padding(horizontal = 4.dp),
    ) {
        uiModel.daysOfWeek?.forEach { dayOfWeek ->
            Box(
                modifier =
                    modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(
                            if (selectedDay == dayOfWeek.dayName) {
                                AppTheme.colors.tertiary03
                            } else {
                                AppTheme.colors.tile02
                            },
                        ).clickable {
                            uiModel.selectedDay = dayOfWeek.dayName
                            selectedDay = dayOfWeek.dayName
                        },
            ) {
                OACallOut1TextView(
                    text = stringResource(id = dayOfWeek.dayNameRes).substring(0, 2),
                    color =
                        if (selectedDay == dayOfWeek.dayName) {
                            AppTheme.colors.button05b
                        } else {
                            AppTheme.colors.button02a
                        },
                    modifier =
                        Modifier
                            .align(Alignment.Center),
                )
            }
        }
    }
}

@Composable
private fun ClimateLayout(
    uiModel: PHEVScheduleDetailUIModel,
    modifier: Modifier = Modifier,
    onClimateCheckChanged: (isEnabled: Boolean) -> Unit,
) {
    var isClimateEnabled by remember { mutableStateOf(false) }
    isClimateEnabled = uiModel.isACEnabled ?: false

    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        OASubHeadLine2TextView(
            text = stringResource(id = R.string.climateHeading),
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier
                    .align(Alignment.CenterStart),
        )

        CustomSwitch(
            onCheckedChange = {
                onClimateCheckChanged(it)
                isClimateEnabled = it
            },
            isEnabled = isClimateEnabled,
            testTagId = "",
            modifier =
                Modifier
                    .align(Alignment.CenterEnd)
                    .testTagID(AccessibilityId.ID_PHEV_CLIMATE_SWITCH),
        )
    }
}

@Composable
private fun ConfirmButton(
    modifier: Modifier = Modifier,
    onConfirmClick: () -> Unit,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(top = 8.dp, bottom = 16.dp, start = 16.dp, end = 16.dp),
    ) {
        PrimaryButton02(
            text = stringResource(id = R.string.Common_confirm),
            modifier =
                Modifier
                    .align(Alignment.CenterHorizontally),
        ) {
            onConfirmClick()
        }
    }
}

@Preview
@Composable
fun PHEVScheduleContentPreview(
    @PreviewParameter(PHEVScheduleDetailUIModelProvider::class) uiModel: PHEVScheduleDetailUIModel,
) {
    ScheduleDetailContent(
        uiModel = uiModel,
        onTimePicked = { _, _ -> },
        onAMPMSelected = { _, _ -> },
        onClimateCheckChanged = { _ -> },
        onSaveSchedule = {},
        onBackPressed = {},
    )
}
