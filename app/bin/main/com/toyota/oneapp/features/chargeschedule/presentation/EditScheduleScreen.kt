package com.toyota.oneapp.features.chargeschedule.presentation

import android.annotation.SuppressLint
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Card
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeschedule.application.EditScheduleState
import com.toyota.oneapp.features.chargeschedule.presentation.model.DaysOfWeekObj
import com.toyota.oneapp.features.chargeschedule.presentation.model.EditScheduleUIModel
import com.toyota.oneapp.features.chargeschedule.presentation.model.TimeObj
import com.toyota.oneapp.features.chargeschedule.presentation.previewprovider.EditScheduleUIModelProvider
import com.toyota.oneapp.features.chargeschedule.presentation.widgets.AMPMWidget
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.CONST_AM
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.TimerChargeInfo
import com.toyota.oneapp.features.core.commonapicalls.domain.model.DialogData
import com.toyota.oneapp.features.core.composable.CustomSwitch
import com.toyota.oneapp.features.core.composable.OAAlertDialog
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OAFullScreenBottomSheetLayout
import com.toyota.oneapp.features.core.composable.OASubHeadLine2TextView
import com.toyota.oneapp.features.core.composable.OATimePicker
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.TimeTextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.presentation.ToastUiEvent
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.AppTheme.colors
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.CommonUtil
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import kotlinx.coroutines.launch

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun EditScheduleScreen(
    navController: NavHostController,
    viewModel: ChargeScheduleViewModel,
    editScheduleViewModel: EditScheduleViewModel = hiltViewModel(),
    modifier: Modifier = Modifier,
    isChargeManagementFlow: Boolean = false,
    isOffPeakSchedule: Boolean = false,
    dataModel: TimerChargeInfo? = null,
) {
    val coroutineScope = rememberCoroutineScope()
    val bottomSheet = LocalBottomSheet.current
    bottomSheet.primarySheetShape.value = RoundedCornerShape(topStart = 30.dp, topEnd = 30.dp)

    val viewState by editScheduleViewModel.editScheduleState.collectAsState()
    val uiModel by editScheduleViewModel.state.collectAsState()
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        editScheduleViewModel.reset()
    }

    LaunchedEffect(Unit) {
        editScheduleViewModel.uiEvent.collect { toastEvent ->
            when (toastEvent) {
                is ToastUiEvent.ToastFromString -> {
                    toastEvent.message?.let { CommonUtil.showToast(context, it) }
                }
                is ToastUiEvent.ToastFromStringRes -> {
                    CommonUtil.showToast(context, context.getString(toastEvent.messageRes))
                }
            }
        }
    }

    LaunchedEffect(viewState is EditScheduleState.RefreshMultiDaySchedule) {
        if (viewState is EditScheduleState.RefreshMultiDaySchedule) {
            navController.popBackStack()
            viewModel.refreshUI(
                (viewState as EditScheduleState.RefreshMultiDaySchedule).appRequestNo,
            )
        }
    }

    EditScheduleContent(
        viewState = viewState,
        uiModel = uiModel,
        onBack = { navController.popBackStack() },
        onInit = { editScheduleViewModel.mapToEditScheduleUIModel(isOffPeakSchedule, dataModel) },
        onLoadContent = {
            EditScheduleContent(
                isChargeManagementFlow = isChargeManagementFlow,
                isOffPeakSchedule = uiModel.isOffPeakScheduleEnabled,
                uiModel = uiModel,
            ) { event ->
                editScheduleViewModel.onEvent(event)
            }
        },
        modifier = modifier,
    ) { type ->
        when (type) {
            ButtonType.TYPE_CREATE_SCHEDULE -> {
                editScheduleViewModel.onEvent(EditScheduleEvents.CreateSchedule(uiModel))
            }
            ButtonType.TYPE_EDIT_SCHEDULE -> {
                editScheduleViewModel.onEvent(EditScheduleEvents.UpdateSchedule(uiModel))
            }
            else -> {
                coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                    DeleteScheduleAlertDialog(
                        onSecondaryClick = {
                            coroutineScope.launch { it.hide() }
                        },
                    ) {
                        coroutineScope.launch { it.hide() }
                        editScheduleViewModel.onEvent(
                            EditScheduleEvents.DeleteSchedule(dataModel?.settingId),
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun EditScheduleContent(
    viewState: EditScheduleState,
    uiModel: EditScheduleUIModel?,
    modifier: Modifier = Modifier,
    onBack: () -> Unit,
    onInit: () -> Unit,
    onLoadContent: @Composable () -> Unit,
    onButtonClick: (type: ButtonType) -> Unit,
) {
    val context = LocalContext.current
    OAFullScreenBottomSheetLayout(
        backgroundColor = AppTheme.colors.tertiary15,
        screenTitle = stringResource(id = R.string.charge_schedule),
        testTagId = AccessibilityId.ID_MULTIDAY_BACK_BTN,
        onBack = { onBack() },
        modifier = modifier.padding(vertical = 8.dp, horizontal = 16.dp),
    ) {
        Column {
            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .weight(1f, fill = false),
            ) {
                when (viewState) {
                    is EditScheduleState.Init -> {
                        onInit()
                    }
                    is EditScheduleState.Loading -> {
                        ShowProgressIndicator(dialogState = true)
                    }
                    is EditScheduleState.LoadContent -> {
                        onLoadContent()
                    }

                    is EditScheduleState.Error -> {
                        onLoadContent()
                    }

                    else -> {}
                }
            }

            BottomButtonLayout(
                uiModel = uiModel,
                onCreateSchedule = {
                    onButtonClick(ButtonType.TYPE_CREATE_SCHEDULE)
                },
                onUpdateSchedule = {
                    onButtonClick(ButtonType.TYPE_EDIT_SCHEDULE)
                },
                onDeleteSchedule = {
                    onButtonClick(ButtonType.TYPE_DELETE_SCHEDULE)
                },
                onValidationError = { message ->
                    CommonUtil.showToast(context, context.getString(message))
                },
            )
        }
    }
}

@Composable
private fun DeleteScheduleAlertDialog(
    onSecondaryClick: () -> Unit,
    onPrimaryClick: () -> Unit,
) {
    OAAlertDialog(
        data =
            DialogData(
                imageId = R.drawable.ic_small_alert,
                imageBgColor = colors.primary02,
                title = stringResource(id = R.string.delete_schedule),
                subtitle = stringResource(id = R.string.delete_schedule_description),
                primaryButtonText =
                    stringResource(
                        id = R.string.delete_schedule_primary_btn,
                    ),
                primaryOnClick = {
                    onPrimaryClick()
                },
                secondaryButtonText = stringResource(id = R.string.go_back),
                secondaryOnClick = {
                    onSecondaryClick()
                },
            ),
    )
}

@Composable
fun EditScheduleContent(
    isChargeManagementFlow: Boolean,
    isOffPeakSchedule: Boolean,
    uiModel: EditScheduleUIModel?,
    modifier: Modifier = Modifier,
    onEvent: (event: EditScheduleEvents) -> Unit,
) {
    Column(modifier = modifier.verticalScroll(rememberScrollState())) {
        uiModel?.let {
            AnimatedVisibility(
                visible = isChargeManagementFlow,
            ) {
                OffPeakScheduleSwitch(
                    isEnabled = isOffPeakSchedule,
                    modifier = Modifier.padding(vertical = 16.dp),
                ) { isEnabled ->
                    onEvent(EditScheduleEvents.OnOffPeakHoursCheckChanged(isEnabled))
                }
            }

            StartTimeCard(
                uiModel = uiModel,
                modifier =
                    Modifier
                        .padding(vertical = 16.dp)
                        .testTagID(AccessibilityId.ID_MULTIDAY_START_TIME_CARD),
                onStartTimePicked = { hour, min ->
                    onEvent(EditScheduleEvents.OnStartTimePicked(hour, min))
                },
                onStartAMSelected = { isAM, time ->
                    onEvent(EditScheduleEvents.OnStartAMPMSelected(isAM, time))
                },
            )

            EndTimeCard(
                uiModel = uiModel,
                modifier =
                    Modifier
                        .padding(vertical = 16.dp)
                        .testTagID(AccessibilityId.ID_MULTIDAY_END_TIME_CARD),
                onEndTimeCheckChanged = { isEnabled ->
                    onEvent(EditScheduleEvents.OnEndTimeCheckChanged(isEnabled))
                },
                onEndTimePicked = { hour, min ->
                    onEvent(EditScheduleEvents.OnEndTimePicked(hour, min))
                },
                onEndAMSelected = { isAM, time ->
                    onEvent(EditScheduleEvents.OnEndAMPMSelected(isAM, time))
                },
            )

            DaysOfWeekCard(
                uiModel = uiModel,
                modifier =
                    Modifier
                        .padding(vertical = 16.dp),
            )
        }
    }
}

@Composable
private fun OffPeakScheduleSwitch(
    isEnabled: Boolean,
    modifier: Modifier = Modifier,
    onCheckChanged: (isEnabled: Boolean) -> Unit,
) {
    Box(modifier = modifier.fillMaxWidth()) {
        OASubHeadLine2TextView(
            text = "Off-Peak Hours",
            color = AppTheme.colors.tertiary03,
        )

        CustomSwitch(
            onCheckedChange = {
                onCheckChanged(it)
            },
            isEnabled = isEnabled,
            testTagId = "",
            modifier =
                Modifier
                    .align(Alignment.CenterEnd)
                    .padding(top = 4.dp)
                    .testTagID(AccessibilityId.ID_OFF_PEARK_HOURS_SWITCH),
        )
    }
}

@Composable
fun StartTimeCard(
    uiModel: EditScheduleUIModel,
    modifier: Modifier = Modifier,
    onStartTimePicked: (hour: Int, min: Int) -> Unit,
    onStartAMSelected: (isAM: Boolean, time: String) -> Unit,
) {
    Card(
        shape = RoundedCornerShape(8.dp),
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tile02,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(144.dp)
                    .padding(12.dp),
        ) {
            OASubHeadLine2TextView(
                text = stringResource(id = R.string.ev_climate_schedule_start_time),
                color = AppTheme.colors.tertiary03,
            )

            Spacer(modifier = Modifier.height(32.dp))

            TimeLayout(
                isOffPeakHours = uiModel.isOffPeakScheduleEnabled ?: false,
                uiModel = uiModel.startTime ?: TimeObj(),
                modifier =
                    Modifier
                        .align(Alignment.CenterHorizontally)
                        .testTagID(AccessibilityId.ID_MULTIDAY_START_TIME_TIME_PICKER),
                onTimePicked = { hour, min ->
                    onStartTimePicked(hour, min)
                },
                onAMPMSelected = { isAM, time ->
                    onStartAMSelected(isAM, time)
                },
            )
        }
    }
}

@Composable
fun EndTimeCard(
    uiModel: EditScheduleUIModel,
    modifier: Modifier = Modifier,
    onEndTimeCheckChanged: (isEnabled: Boolean) -> Unit,
    onEndTimePicked: (hour: Int, min: Int) -> Unit,
    onEndAMSelected: (isAM: Boolean, time: String) -> Unit,
) {
    Card(
        shape = RoundedCornerShape(8.dp),
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tile02,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(144.dp)
                    .padding(12.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth(),
            ) {
                OASubHeadLine2TextView(
                    text = stringResource(id = R.string.GuestProfile_end_time),
                    color = AppTheme.colors.tertiary03,
                )

                CustomSwitch(
                    onCheckedChange = {
                        onEndTimeCheckChanged(it)
                    },
                    isClickable = uiModel.isOffPeakScheduleEnabled == false,
                    isEnabled = uiModel.isEndTimeEnabled ?: false,
                    testTagId = AccessibilityId.ID_MULTIDAY_END_TIME_SWITCH,
                    modifier =
                        Modifier
                            .align(Alignment.CenterEnd)
                            .padding(top = 4.dp),
                )
            }

            if (uiModel.isEndTimeEnabled == true) {
                Spacer(modifier = Modifier.height(32.dp))

                TimeLayout(
                    isOffPeakHours = uiModel.isOffPeakScheduleEnabled ?: false,
                    uiModel = uiModel.endTime ?: TimeObj(),
                    modifier =
                        Modifier
                            .align(Alignment.CenterHorizontally)
                            .testTagID(AccessibilityId.ID_MULTIDAY_END_TIME_TIME_PICKER),
                    onTimePicked = { hour, min ->
                        onEndTimePicked(hour, min)
                    },
                    onAMPMSelected = { isAM, time ->
                        onEndAMSelected(isAM, time)
                    },
                )
            } else {
                Spacer(modifier = Modifier.height(16.dp))

                Divider(color = AppTheme.colors.tertiary10)

                OACallOut1TextView(
                    text = stringResource(id = R.string.departure_time_description_2),
                    color = AppTheme.colors.tertiary05,
                    modifier =
                        Modifier
                            .padding(top = 16.dp, bottom = 8.dp),
                )
            }
        }
    }
}

@Composable
fun DaysOfWeekCard(
    uiModel: EditScheduleUIModel,
    modifier: Modifier = Modifier,
) {
    Card(
        shape = RoundedCornerShape(8.dp),
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tile02,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(144.dp)
                    .padding(vertical = 16.dp),
        ) {
            OASubHeadLine2TextView(
                text = stringResource(id = R.string.days_of_week),
                color = AppTheme.colors.tertiary03,
                modifier =
                    Modifier
                        .padding(horizontal = 12.dp),
            )

            Spacer(modifier = Modifier.height(32.dp))

            Row(
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 4.dp),
            ) {
                val selectedDays = uiModel.selectedDaysOfWeek?.toMutableList()
                uiModel.daysOfWeek?.forEach { dayOfWeek ->
                    DayOfWeekCheckbox(
                        isOffPeakSchedule = uiModel.isOffPeakScheduleEnabled,
                        dayOfWeek = dayOfWeek,
                    ) { isSelected, dayName ->
                        if (isSelected && selectedDays?.contains(dayName) == false) {
                            selectedDays.add(dayName)
                        } else if (!isSelected && selectedDays?.contains(dayName) == true) {
                            selectedDays.remove(dayName)
                        }

                        uiModel.selectedDaysOfWeek = selectedDays
                    }
                }
            }
        }
    }
}

@Composable
fun DayOfWeekCheckbox(
    isOffPeakSchedule: Boolean,
    dayOfWeek: DaysOfWeekObj,
    modifier: Modifier = Modifier,
    onValueChanged: (isSelected: Boolean, dayOfWeek: String) -> Unit,
) {
    var isDaySelected by remember { mutableStateOf(false) }
    isDaySelected = dayOfWeek.isDaySelected

    Box(
        modifier =
            modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(
                    if (isDaySelected) {
                        AppTheme.colors.tertiary03
                    } else {
                        AppTheme.colors.tile02
                    },
                )
                .clickable {
                    if (!isOffPeakSchedule) {
                        dayOfWeek.isDaySelected = !isDaySelected
                        onValueChanged(!isDaySelected, dayOfWeek.dayName)
                        isDaySelected = !isDaySelected
                    }
                },
    ) {
        OACallOut1TextView(
            text = stringResource(id = dayOfWeek.dayNameRes).substring(0, 1),
            color =
                if (isDaySelected) {
                    AppTheme.colors.button05b
                } else {
                    AppTheme.colors.button02a
                },
            modifier =
                Modifier
                    .align(Alignment.Center),
        )
    }
}

@Composable
fun BottomButtonLayout(
    uiModel: EditScheduleUIModel?,
    onCreateSchedule: () -> Unit,
    onUpdateSchedule: () -> Unit,
    onDeleteSchedule: () -> Unit,
    onValidationError: (message: Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(top = 8.dp, bottom = 8.dp),
    ) {
        if (uiModel?.isUpdateScheduleFlow == true) {
            Box(
                modifier =
                    Modifier
                        .size(48.dp)
                        .clip(CircleShape)
                        .background(AppTheme.colors.tertiary03)
                        .clickable {
                            onDeleteSchedule()
                        }
                        .testTagID(AccessibilityId.ID_DELETE_SCHEDULE_IMAGE_BTN),
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_delete),
                    contentDescription = stringResource(id = R.string.Common_delete),
                    modifier =
                        Modifier
                            .size(24.dp)
                            .align(Alignment.Center),
                    tint = AppTheme.colors.tile01,
                )
            }

            Spacer(modifier = Modifier.width(32.dp))
        }

        Box(
            modifier =
                Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(AppTheme.colors.button03b)
                    .clickable {
                        if (uiModel?.selectedDaysOfWeek?.isNotEmpty() == true) {
                            if (uiModel.isUpdateScheduleFlow == true) {
                                onUpdateSchedule()
                            } else {
                                onCreateSchedule()
                            }
                        } else {
                            onValidationError(R.string.select_days_of_week_error)
                        }
                    }.testTagID(AccessibilityId.ID_CREATE_SCHEDULE_IMAGE_BTN),
        ) {
            Icon(
                // TODO: Need to update icon ic done white
                painter = painterResource(id = R.drawable.ic_baseline_done_primary_24),
                contentDescription = stringResource(id = R.string.Common_ok),
                modifier =
                    Modifier
                        .size(24.dp)
                        .align(Alignment.Center),
                tint = AppTheme.colors.tile01,
            )
        }
    }
}

@Composable
fun TimeLayout(
    isOffPeakHours: Boolean,
    uiModel: TimeObj,
    modifier: Modifier = Modifier,
    onTimePicked: (hour: Int, min: Int) -> Unit,
    onAMPMSelected: (isAM: Boolean, time: String) -> Unit,
) {
    var isAMSelected by remember { mutableStateOf(false) }
    isAMSelected = uiModel.ampm == CONST_AM
    var selectedTime by remember { mutableStateOf("") }
    selectedTime = "${uiModel.hour} : ${uiModel.minute}"

    Row(
        horizontalArrangement = Arrangement.Center,
        modifier =
            modifier
                .wrapContentSize(),
    ) {
        OATimePicker(
            selectedHour = uiModel.hour.toInt(),
            selectedMin = uiModel.minute.toInt(),
            isAM = uiModel.ampm == CONST_AM,
            isPickerEnabled = !isOffPeakHours,
            content = { click ->
                TimeTextView(
                    time = selectedTime,
                    onClick = click,
                )
            },
        ) { pickedHour, pickedMin ->
            onTimePicked(pickedHour, pickedMin)
            selectedTime = "${uiModel.hour} : ${uiModel.minute}"
            isAMSelected = uiModel.ampm == CONST_AM
        }

        AMPMWidget(
            isOffPeakHours = isOffPeakHours,
            timeObj = uiModel,
            isAMSelected = isAMSelected,
        ) { isAM, time24HrFormat ->
            onAMPMSelected(isAM, time24HrFormat)
            isAMSelected = uiModel.ampm == CONST_AM
        }
    }
}

enum class ButtonType {
    TYPE_CREATE_SCHEDULE,
    TYPE_EDIT_SCHEDULE,
    TYPE_DELETE_SCHEDULE,
}

@Preview
@Composable
fun EditScheduleContentPreview(
    @PreviewParameter(EditScheduleUIModelProvider::class) uiModel: EditScheduleUIModel,
) {
    val viewState = EditScheduleState.LoadContent
    EditScheduleContent(
        viewState = viewState,
        uiModel = uiModel,
        onBack = {},
        onInit = {},
        onLoadContent = {
            EditScheduleContent(
                true,
                false,
                uiModel,
            ) { }
        },
        onButtonClick = {},
    )
}
