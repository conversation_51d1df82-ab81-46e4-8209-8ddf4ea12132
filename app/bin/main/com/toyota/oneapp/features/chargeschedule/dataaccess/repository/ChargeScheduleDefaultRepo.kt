package com.toyota.oneapp.features.chargeschedule.dataaccess.repository

import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.CreateScheduleRequest
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.EcoScheduleDetailsResponse
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.UpdateScheduleRequest
import com.toyota.oneapp.features.chargeschedule.dataaccess.service.ChargeScheduleAPI
import com.toyota.oneapp.features.chargeschedule.domain.repo.ChargeScheduleRepo
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.CommonScheduleResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import java.util.UUID
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class ChargeScheduleDefaultRepo
    @Inject
    constructor(
        private val chargeScheduleAPI: ChargeScheduleAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext),
        ChargeScheduleRepo {
        override suspend fun fetchEcoScheduleDetails(vin: String): Resource<EcoScheduleDetailsResponse?> =
            makeApiCall {
                chargeScheduleAPI.fetchEcoScheduleDetails(vin)
            }

        override suspend fun saveEcoSchedulesDetails(
            vin: String,
            isEcoScheduleEnabled: Boolean,
            settingsId: String,
        ): Resource<EcoScheduleDetailsResponse?> =
            makeApiCall {
                chargeScheduleAPI.saveEcoScheduleDetails(
                    vin = vin,
                    isEcoScheduleEnabled = isEcoScheduleEnabled,
                    settingsId = settingsId,
                )
            }

        override suspend fun createMultiDaySchedule(
            vehicleInfo: VehicleInfo,
            requestBody: CreateScheduleRequest,
        ): Resource<CommonScheduleResponse?> =
            makeApiCall {
                chargeScheduleAPI.createMultiDayChargeSchedule(
                    requestBody = requestBody,
                    vin = vehicleInfo.vin,
                    generation = vehicleInfo.generation,
                    deviceId = UUID.randomUUID().toString(),
                )
            }

        override suspend fun deleteMultiDayChargeSchedule(
            scheduleId: String,
            vehiceInfo: VehicleInfo,
        ): Resource<CommonScheduleResponse?> =
            makeApiCall {
                chargeScheduleAPI.deleteMultiDayChargeSchedule(
                    scheduleId = scheduleId,
                    vin = vehiceInfo.vin,
                    generation = vehiceInfo.generation,
                    deviceId = UUID.randomUUID().toString(),
                )
            }

        override suspend fun updateMultiDayChargeSchedule(
            vehicleInfo: VehicleInfo,
            request: UpdateScheduleRequest,
        ): Resource<CommonScheduleResponse?> =
            makeApiCall {
                chargeScheduleAPI.updateMultiDayChargeSchedule(
                    requestBody = request,
                    vin = vehicleInfo.vin,
                    generation = vehicleInfo.generation,
                    deviceId = UUID.randomUUID().toString(),
                )
            }
    }
