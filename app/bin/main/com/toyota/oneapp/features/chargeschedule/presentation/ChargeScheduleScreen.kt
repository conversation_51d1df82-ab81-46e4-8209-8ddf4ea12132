/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeschedule.presentation

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeassist.view.helper.MergedBottomDescriptionCard
import com.toyota.oneapp.features.chargeassist.view.screens.CAEnabledLayoutDependencies
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_SUCCESSFUL_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistToggleCardParams
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistToggleSwitches.Companion.ECO_CHARGE_SWITCH
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistToggleSwitches.Companion.MANUAL_SCHEDULE_SWITCH
import com.toyota.oneapp.features.chargeassist.view.screens.EcoChargeCardParamsForCAToggle
import com.toyota.oneapp.features.chargeassist.view.screens.ManualScheduleCardParamsForCAToggle
import com.toyota.oneapp.features.chargeassist.view.screens.ManualScheduleWidget
import com.toyota.oneapp.features.chargeassist.view.screens.entrypoint2.ChargeAssistScheduleCardComposable
import com.toyota.oneapp.features.chargeassist.view.screens.entrypoint2.ChargeAssistSwitchOffWithBottomSheet
import com.toyota.oneapp.features.chargeassist.view.screens.manualschedule.chargeScheduleLaunchedEffect
import com.toyota.oneapp.features.chargeassist.view.viewmodel.CHARGE_ASSIST_SWITCH
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.chargeinfo.presentation.ChargeInfoViewModel
import com.toyota.oneapp.features.chargeinfo.presentation.FindOutMoreScreen
import com.toyota.oneapp.features.chargeschedule.application.ChargeScheduleState
import com.toyota.oneapp.features.chargeschedule.application.EcoSchduleCardState
import com.toyota.oneapp.features.chargeschedule.domain.model.EcoScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.MultiDayScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.PHEVScheduleModel
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.ChargeScheduleRoute
import com.toyota.oneapp.features.core.composable.CustomSwitch
import com.toyota.oneapp.features.core.composable.EcoChargingShimmer
import com.toyota.oneapp.features.core.composable.OABody1TextView
import com.toyota.oneapp.features.core.composable.OAButtonTextView
import com.toyota.oneapp.features.core.composable.OAFootNote1TextView
import com.toyota.oneapp.features.core.composable.OAFullScreenBottomSheetLayout
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.CommonUtil
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import kotlinx.coroutines.launch

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun ChargeScheduleScreen(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    viewModel: ChargeScheduleViewModel = hiltViewModel(),
    chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel(),
    chargeInfoViewModel: ChargeInfoViewModel = hiltViewModel(),
    isZipFromEligibilityScreenValid: Boolean? = null,
) {
    val caBackEndFlagOn by chargeAssistViewModel.isCABackEndFlagOn.collectAsState()
    if (caBackEndFlagOn) {
        ChargeScheduleCAEnableComponent(
            navController,
            modifier,
            viewModel,
            chargeAssistViewModel,
            chargeInfoViewModel,
            isZipFromEligibilityScreenValid,
        )
    } else {
        val coroutineScope = rememberCoroutineScope()
        val bottomSheet = LocalBottomSheet.current
        val uiState by viewModel.uiState.collectAsState()
        val context = LocalContext.current

        chargeScheduleLaunchedEffect(uiState = uiState, context, viewModel = viewModel)
        ShowProgressIndicator(dialogState = uiState.showProgress)

        fun shouldShowSwitch() =
            uiState.viewState is ChargeScheduleState.SchedulingList ||
                uiState.viewState is ChargeScheduleState.MultiDayScheduleLoading ||
                (uiState.viewState as? ChargeScheduleState.NoSchedule)?.isMultiDaySchedule == true

        OAFullScreenBottomSheetLayout(
            backgroundColor = AppTheme.colors.tertiary15,
            screenTitle = stringResource(id = R.string.ev_schedule),
            testTagId = AccessibilityId.ID_CHARGE_SCHEDULE_BACK_BTN,
            onBack = { navController.popBackStack() },
            modifier =
                modifier
                    .fillMaxSize()
                    .padding(vertical = 8.dp, horizontal = 16.dp),
        ) {
            Column {
                EcoChargingCard(
                    shouldShowSwitch = shouldShowSwitch(),
                    cardState = uiState.ecoCardState,
                    onSaveEcoSchedule = { isEnabled ->
                        viewModel.saveEcoScheduleDetails(isEcoScheduleEnabled = isEnabled)
                    },
                    modifier =
                        Modifier
                            .padding(vertical = 16.dp)
                            .testTagID(AccessibilityId.ID_ECO_CHARGING_CARD),
                ) {
                    coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                        FindOutMoreScreen(bottomSheetState = it) { group, event ->
                            viewModel.onEvent(ChargeScheduleEvents.OnLogEvent(group.eventName, event))
                        }
                    }
                }

                ChargeScheduleContainer(
                    viewState = uiState.viewState,
                    onNoSchedule = { isMultiDay ->
                        NoScheduleWidget(isMultiDaySchedule = isMultiDay) {
                            navController.navigate(ChargeScheduleRoute.EditMultidayScheduleScreen.route)
                        }
                    },
                    onMultiDaySchedule = { uiModel ->
                        MultiDayScheduleScreen(
                            navController = navController,
                            uiModel = uiModel,
                            viewModel = viewModel,
                        )
                    },
                    onPHEVSchedule = { uiModel ->
                        PHEVScheduleScreen(navController = navController, uiModel = uiModel, viewModel = viewModel)
                    },
                )
            }
        }
    }
}

@Composable
fun ChargeScheduleCAEnableComponent(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    viewModel: ChargeScheduleViewModel = hiltViewModel(),
    chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel(),
    chargeInfoViewModel: ChargeInfoViewModel = hiltViewModel(),
    isZipFromEligibilityScreenValid: Boolean? = null,
) {
    val coroutineScope = rememberCoroutineScope()
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current
    val toastMessage = viewModel.toastMessage
    val toastMessageRes = viewModel.toastMessageRes
    val bottomSheetStateCA =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            skipHalfExpanded = true,
        )
    val caEnabledLayoutDependencies =
        CAEnabledLayoutDependencies(
            bottomSheetStateCA = bottomSheetStateCA,
            uiState = uiState,
        )

    LaunchedEffect(key1 = uiState.viewState) {
        if (uiState.viewState is ChargeScheduleState.Init) {
            viewModel.onEvent(ChargeScheduleEvents.InitChargeSchedule)
        }
    }
    LaunchedEffect(key1 = toastMessage, key2 = toastMessageRes) {
        if (toastMessage != null) CommonUtil.showToast(context, toastMessage)
        if (toastMessageRes != null) CommonUtil.showToast(context, context.getString(toastMessageRes))
        viewModel.reset()
    }

    ShowProgressIndicator(dialogState = uiState.showProgress)
    ChargeAssistEnabledLayout(
        navController,
        modifier,
        viewModel,
        chargeAssistViewModel,
        chargeInfoViewModel,
        isZipFromEligibilityScreenValid,
        caEnabledLayoutDependencies,
    )
    ChargeAssistSwitchOffWithBottomSheet(bottomSheetState = bottomSheetStateCA)
    BackHandler {
        when (bottomSheetStateCA.isVisible) {
            true -> coroutineScope.launch { bottomSheetStateCA.hide() }
            else -> navController.popBackStack()
        }
    }

    BackHandler {
        if (bottomSheetStateCA.isVisible) {
            coroutineScope.launch { bottomSheetStateCA.hide() }
        } else {
            navController.popBackStack()
        }
    }
}

@Composable
fun ChargeAssistEnabledLayout(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    viewModel: ChargeScheduleViewModel = hiltViewModel(),
    chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel(),
    chargeInfoViewModel: ChargeInfoViewModel = hiltViewModel(),
    isZipFromEligibilityScreenValid: Boolean? = null,
    caEnabledLayoutDependencies: CAEnabledLayoutDependencies,
) {
    val isPluggedIn by chargeAssistViewModel.chargeAssistVMHelper.isEVPluggedIN.collectAsState()
    OAFullScreenBottomSheetLayout(
        backgroundColor = AppTheme.colors.tertiary15,
        screenTitle = stringResource(id = R.string.ev_schedule),
        testTagId = AccessibilityId.ID_CHARGE_SCHEDULE_BACK_BTN,
        onBack = { navController.popBackStack() },
        modifier =
            modifier
                .fillMaxSize()
                .padding(vertical = 8.dp, horizontal = 16.dp),
    ) {
        CAEnabledChargedScheduleScreenWidgets(
            viewModel,
            chargeAssistViewModel,
            isZipFromEligibilityScreenValid,
            caEnabledLayoutDependencies,
            navController,
            isPluggedIn,
            chargeInfoViewModel,
        )
    }
}

@Composable
fun CAEnabledChargedScheduleScreenWidgets(
    viewModel: ChargeScheduleViewModel,
    chargeAssistViewModel: ChargeAssistViewModel,
    isZipFromEligibilityScreenValid: Boolean?,
    caEnabledLayoutDependencies: CAEnabledLayoutDependencies,
    navController: NavHostController,
    isPluggedIn: Boolean?,
    chargeInfoViewModel: ChargeInfoViewModel = hiltViewModel(),
) {
    val coroutineScope = rememberCoroutineScope()
    val bottomSheet = LocalBottomSheet.current
    val fetchIsInProgressStatusOver5min by chargeAssistViewModel.isInProgressStatusOver5min.collectAsState()
    val currentEnrollmentStatus by chargeAssistViewModel.userEnrollmentStatusDataString.collectAsState()
    val activeSwitch by chargeAssistViewModel.chargeAssistVMHelper.activeSwitch.collectAsState()
    val isChargeAssistActive = activeSwitch == CHARGE_ASSIST_SWITCH
    val isEcoActive = activeSwitch == ECO_CHARGE_SWITCH
    val isManualScheduleActive = activeSwitch == MANUAL_SCHEDULE_SWITCH
    val isAnySwitchActive = isChargeAssistActive || isEcoActive || isManualScheduleActive

    LaunchedEffect(Unit) {
        chargeAssistViewModel.isUserEligibleForCAEnrollmentAndEnrollmentStatus()
        if (currentEnrollmentStatus in ENROLLMENT_SUCCESSFUL_STATUSES) {
            chargeAssistViewModel.chargeAssistVMHelper.checkEnrollmentStatusForAutoToggle()
        }
        chargeAssistViewModel.chargeAssistVMHelper.isEcoChargingEnabled(viewModel.uiState.value.ecoCardState)
        chargeAssistViewModel.chargeAssistVMHelper.isChargeAssistActive()
        chargeAssistViewModel.chargeAssistVMHelper.loadSavedActiveSwitch()
    }
    val chargeAssistToggleCardParams =
        ChargeAssistToggleCardParams(
            // if CA is Active or All toggle switches are all off (allow user to click)
            isClickable = isChargeAssistActive || !isAnySwitchActive,
            isEnabled = isChargeAssistActive,
            onToggle = { chargeAssistViewModel.chargeAssistVMHelper.toggleSwitch(CHARGE_ASSIST_SWITCH) },
            sheetState = caEnabledLayoutDependencies.bottomSheetStateCA,
            isZipFromEligibilityScreenValid = isZipFromEligibilityScreenValid,
            fetchIsInProgressStatusOver5min = fetchIsInProgressStatusOver5min,
            isEVPluggedIN = isPluggedIn,
            isChargeAssistActive = isChargeAssistActive,
        )
    ChargeAssistScheduleCardComposable(
        chargeAssistToggleCardParams = chargeAssistToggleCardParams,
        navController = navController,
        chargeAssistViewModel = chargeAssistViewModel,
        chargeInfoViewModel = chargeInfoViewModel,
    )

    val ecoChargeCardParamsForCAToggle =
        EcoChargeCardParamsForCAToggle(
            // if Eco is Active or All toggle switches are all off (allow user to click)
            isClickable = isEcoActive || !isAnySwitchActive,
            isEnabled = isEcoActive,
            onToggle = { chargeAssistViewModel.chargeAssistVMHelper.toggleSwitch(ECO_CHARGE_SWITCH) },
            isChargeAssistActive = isChargeAssistActive,
        )
    Column {
        EcoChargingCard(
            shouldShowSwitch =
                caEnabledLayoutDependencies.uiState.viewState is ChargeScheduleState.SchedulingList ||
                    caEnabledLayoutDependencies.uiState.viewState is ChargeScheduleState.MultiDayScheduleLoading ||
                    (
                        caEnabledLayoutDependencies.uiState.viewState as? ChargeScheduleState.NoSchedule
                    )?.isMultiDaySchedule == true,
            cardState = caEnabledLayoutDependencies.uiState.ecoCardState,
            onSaveEcoSchedule = { isEnabled ->
                viewModel.saveEcoScheduleDetails(isEcoScheduleEnabled = isEnabled)
                viewModel.toastMessage = ""
            },
            ecoChargeCardParamsForCAToggle = ecoChargeCardParamsForCAToggle,
            toastMessage = viewModel.toastMessage,
            modifier =
                Modifier
                    .padding(vertical = 16.dp)
                    .testTagID(AccessibilityId.ID_ECO_CHARGING_CARD),
        ) {
            coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                FindOutMoreScreen(bottomSheetState = it) { group, event ->
                    viewModel.onEvent(ChargeScheduleEvents.OnLogEvent(group.eventName, event))
                }
            }
        }

        val manualScheduleCardParamsForCAToggle =
            ManualScheduleCardParamsForCAToggle(
                // if ManualSchedule Active or All toggle switches are all off (allow user to click)
                isClickable = isManualScheduleActive || !isAnySwitchActive,
                isEnabled = isManualScheduleActive,
                onToggle = { chargeAssistViewModel.chargeAssistVMHelper.toggleSwitch(MANUAL_SCHEDULE_SWITCH) },
                isChargeAssistActive = isChargeAssistActive,
            )
        // Manual Schedule Widget Toggle
        ManualScheduleWidget(
            navController,
            showEntryPointSection = isManualScheduleActive,
            manualScheduleCardParamsForCAToggle = manualScheduleCardParamsForCAToggle,
            chargeAssistViewModel = chargeAssistViewModel,
            isChargeAssistActive = isChargeAssistActive,
        )
        BottomToggleDescriptionCard()
    }
}

@Composable
fun BottomToggleDescriptionCard() {
    val context = LocalContext.current
    MergedBottomDescriptionCard(
        text = stringResource(R.string.bottom_schedule_toggle_card_description),
        contentDescription = context.getString(R.string.charge_assist_one_toggle_at_a_time_text),
        useFixedHeight = true,
    )
}

@Composable
fun ChargeScheduleContainer(
    viewState: ChargeScheduleState,
    onNoSchedule: @Composable (isMultiDay: Boolean) -> Unit,
    onMultiDaySchedule: @Composable (uiModel: MultiDayScheduleModel.ListMultiDayScheduleModel) -> Unit,
    onPHEVSchedule: @Composable (uiModel: PHEVScheduleModel.PHEVScheduleSuccessModel) -> Unit,
) {
    ResultHandler(
        viewState = viewState,
        onLoading = {
            MultiDayScheduleShimmer()
        },
        onNoSchedule = { isMultiDay ->
            onNoSchedule(isMultiDay)
        },
        onMultiDaySchedule = { uiModel ->
            onMultiDaySchedule(uiModel)
        },
        onPHEVSchedule = { uiModel ->
            onPHEVSchedule(uiModel)
        },
    )
}

@Composable
fun ResultHandler(
    viewState: ChargeScheduleState,
    onLoading: @Composable () -> Unit,
    onNoSchedule: @Composable (isMultiDay: Boolean) -> Unit,
    onMultiDaySchedule: @Composable (MultiDayScheduleModel.ListMultiDayScheduleModel) -> Unit,
    onPHEVSchedule: @Composable (PHEVScheduleModel.PHEVScheduleSuccessModel) -> Unit,
) {
    when (viewState) {
        is ChargeScheduleState.MultiDayScheduleLoading -> {
            onLoading()
        }

        is ChargeScheduleState.NoSchedule -> {
            onNoSchedule(viewState.isMultiDaySchedule)
        }

        is ChargeScheduleState.SchedulingList -> {
            onMultiDaySchedule(viewState.uiModel)
        }

        is ChargeScheduleState.PHEVScheduleTimer -> {
            onPHEVSchedule(viewState.uiModel)
        }

        else -> {
            // Do nothing
        }
    }
}

@Composable
private fun EcoChargingCard(
    shouldShowSwitch: Boolean,
    cardState: EcoSchduleCardState,
    modifier: Modifier = Modifier,
    onSaveEcoSchedule: (isEnabled: Boolean) -> Unit,
    ecoChargeCardParamsForCAToggle: EcoChargeCardParamsForCAToggle? = null,
    toastMessage: String? = null,
    onFindMore: () -> Unit,
) {
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()

    LaunchedEffect(toastMessage) { if (toastMessage == "Failure") ecoChargeCardParamsForCAToggle?.onToggle?.invoke() }
    Card(
        shape = RoundedCornerShape(8.dp),
        backgroundColor = AppTheme.colors.tile03,
        elevation = 8.dp,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column {
            Row(modifier = Modifier.padding(16.dp)) {
                Box(
                    modifier =
                        Modifier
                            .size(56.dp)
                            .clip(CircleShape)
                            .background(color = AppTheme.colors.button03d),
                    contentAlignment = Alignment.Center,
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_eco_badge),
                        contentDescription = stringResource(id = R.string.eco_charging),
                        tint = AppTheme.colors.tertiary15,
                    )
                }
                Image(
                    painter = painterResource(id = R.drawable.ic_eco_badge_circle),
                    contentDescription = stringResource(id = R.string.eco_charging),
                    modifier = Modifier.size(56.dp),
                )
                Spacer(modifier = Modifier.width(16.dp))

                Column(modifier = Modifier.weight(1f, fill = false)) {
                    OASubHeadLine1TextView(
                        text = stringResource(id = R.string.eco_charging),
                        color = AppTheme.colors.tertiary03,
                    )
                    Spacer(modifier = Modifier.height(4.dp))

                    EcoChargingShimmer(
                        isLoading = cardState == EcoSchduleCardState.Loading,
                        contentAfterLoading = {
                            if (cardState is EcoSchduleCardState.Success) {
                                EcoChargingContentAfterLoading(
                                    uiModel = cardState.uiModel,
                                    modifier = Modifier.testTagID(AccessibilityId.ID_OFF_PEAK_DESCRIPTION_TEXT),
                                )
                            }
                        },
                    )
                }

                val caBackEndFlagOn by chargeAssistViewModel.isCABackEndFlagOn.collectAsState()

                when {
                    caBackEndFlagOn -> {
                        ChargeAssistEnabledEcoScheduleCard(
                            cardState,
                            shouldShowSwitch,
                            ecoChargeCardParamsForCAToggle,
                            onSaveEcoSchedule,
                            chargeAssistViewModel,
                        )
                    }

                    (cardState is EcoSchduleCardState.Success && shouldShowSwitch) -> {
                        Spacer(modifier = Modifier.width(16.dp))
                        CustomSwitch(
                            onCheckedChange = { onSaveEcoSchedule(it) },
                            isEnabled = cardState.uiModel.optIn,
                            testTagId = "",
                            modifier =
                                Modifier
                                    .padding(top = 8.dp)
                                    .testTagID(AccessibilityId.ID_ECO_CHARGING_SWITCH),
                        )
                    }
                }
            }
            FindOutMoreButton(modifier = Modifier.testTagID(AccessibilityId.ID_FIND_OUT_MORE_CARD_BTN)) { onFindMore() }
        }
    }
}

@Composable
fun ChargeAssistEnabledEcoScheduleCard(
    cardState: EcoSchduleCardState,
    showSwitch: Boolean,
    ecoChargeCardParamsForCAToggle: EcoChargeCardParamsForCAToggle? = null,
    onSaveEcoSchedule: (isEnabled: Boolean) -> Unit,
    chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel(),
) {
    if (cardState is EcoSchduleCardState.Success && showSwitch) {
        Spacer(modifier = Modifier.width(16.dp))
        CustomSwitch(
            isEnabled =
                ecoChargeCardParamsForCAToggle?.isEnabled == true || cardState.uiModel.optIn,
            isClickable = ecoChargeCardParamsForCAToggle?.isClickable == true,
            onCheckedChange = {
                chargeAssistViewModel.chargeAssistVMHelper.toggleSwitch("ecoChargingSwitch")
                onSaveEcoSchedule(it)
            },
            shouldEnableCADisableColor = true,
            testTagId = "",
            modifier =
                Modifier
                    .padding(top = 8.dp)
                    .testTagID(AccessibilityId.ID_ECO_CHARGING_SWITCH),
        )
    }
}

@Composable
private fun EcoChargingContentAfterLoading(
    uiModel: EcoScheduleModel,
    modifier: Modifier = Modifier,
) {
    val annotatedString =
        buildAnnotatedString {
            append("${stringResource(id = R.string.off_peak_charging_time)} ")
            withStyle(
                style = SpanStyle(AppTheme.colors.tertiary03, fontWeight = FontWeight.Bold),
            ) {
                append(uiModel.formattedStartTime)
            }
            append(" ${stringResource(id = R.string.common_and)} ")
            withStyle(
                style = SpanStyle(AppTheme.colors.tertiary03, fontWeight = FontWeight.Bold),
            ) {
                append(uiModel.formattedEndTime)
            }
        }
    OAFootNote1TextView(
        text = annotatedString,
        color = AppTheme.colors.tertiary03,
        textAlign = TextAlign.Start,
        modifier = modifier,
    )
}

@Composable
private fun FindOutMoreButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Box(
        modifier =
            modifier
                .background(AppTheme.colors.tile02)
                .fillMaxWidth()
                .clickable {
                    onClick()
                }.padding(16.dp),
    ) {
        OAButtonTextView(
            text = stringResource(id = R.string.eco_learn_more),
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier
                    .align(Alignment.CenterStart),
        )

        Image(
            painter = painterResource(id = R.drawable.ic_arrow_right),
            contentDescription = "Right arrow",
            modifier =
                Modifier
                    .size(24.dp)
                    .align(Alignment.CenterEnd),
        )
    }
}

@Composable
fun NoScheduleWidget(
    isMultiDaySchedule: Boolean,
    modifier: Modifier = Modifier,
    onCreateSchedule: () -> Unit,
) {
    Box(
        modifier =
            modifier
                .fillMaxSize()
                .padding(vertical = 16.dp),
    ) {
        Column(
            modifier =
                Modifier
                    .wrapContentSize()
                    .align(Alignment.Center),
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_no_schedule),
                contentDescription = stringResource(id = R.string.no_schedule_title),
                modifier =
                    Modifier
                        .size(48.dp)
                        .align(Alignment.CenterHorizontally)
                        .testTagID(AccessibilityId.ID_NO_SCHEDULE_TITLE_TEXT),
            )
            Spacer(modifier = Modifier.height(8.dp))
            OASubHeadLine1TextView(
                text = stringResource(id = R.string.no_schedule_title),
                color = AppTheme.colors.tertiary03,
                modifier =
                    Modifier
                        .align(Alignment.CenterHorizontally)
                        .testTagID(AccessibilityId.ID_NO_SCHEDULE_SUBTITLE_TEXT),
            )

            Spacer(modifier = Modifier.height(8.dp))
            OABody1TextView(
                text =
                    if (isMultiDaySchedule) {
                        stringResource(id = R.string.no_multiday_schedule_description)
                    } else {
                        stringResource(id = R.string.no_phev_schedule_description)
                    },
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Center,
                modifier =
                    Modifier
                        .align(Alignment.CenterHorizontally),
            )
        }

        if (isMultiDaySchedule) {
            PrimaryButton02(
                text = stringResource(id = R.string.create_schedule),
                modifier =
                    Modifier
                        .align(Alignment.BottomCenter)
                        .testTagID(AccessibilityId.ID_NO_SCHEDULE_CREATE_SCHEDULE_BTN),
            ) {
                onCreateSchedule()
            }
        }
    }
}
