/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeschedule.presentation

import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.chargeschedule.application.ChargeScheduleUseCase
import com.toyota.oneapp.features.chargeschedule.application.PHEVScheduleDetailState
import com.toyota.oneapp.features.chargeschedule.presentation.model.PHEVScheduleDetailUIModel
import com.toyota.oneapp.features.chargeschedule.presentation.model.toChargeTimerRequest
import com.toyota.oneapp.features.chargeschedule.presentation.model.toPHEVScheduleDetailUIModel
import com.toyota.oneapp.features.chargeschedule.presentation.model.toPickedAMPM
import com.toyota.oneapp.features.chargeschedule.presentation.model.toPickedTime
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeInfo
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.features.core.presentation.ToastUiEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class PHEVNewScheduleViewModel
    @Inject
    constructor(
        private val chargeScheduleUseCase: ChargeScheduleUseCase,
        private val analyticsLogger: AnalyticsLogger,
        private val applicationData: ApplicationData,
        private val dispatcherProvider: DispatcherProvider,
    ) : BaseViewModel<PHEVScheduleDetailUIModel, PHEVScheduleEvents>() {
        private val _phevScheduleDetailState =
            MutableStateFlow<PHEVScheduleDetailState>(
                value = PHEVScheduleDetailState.Init,
            )
        val phevScheduleDetailState = _phevScheduleDetailState.asStateFlow()

        fun reset() {
            _phevScheduleDetailState.value = PHEVScheduleDetailState.Init
        }

        override fun defaultState() = PHEVScheduleDetailUIModel.Init

        override fun onEvent(event: PHEVScheduleEvents) {
            when (event) {
                is PHEVScheduleEvents.InitScreen -> {
                    mapToPHEVScheduleDetailUIModel(event.isStartTimeFlow, event.chargeInfo)
                }
                is PHEVScheduleEvents.OnTimePicked -> {
                    state.update {
                        state.value.apply {
                            timeObj?.toPickedTime(event.hour, event.min)
                        }
                    }
                }
                is PHEVScheduleEvents.OnAMPMSelected -> {
                    state.update {
                        state.value.apply {
                            timeObj?.toPickedAMPM(event.isAM, event.time24HrFormat)
                        }
                    }
                }
                is PHEVScheduleEvents.OnClimateCheckChanged -> {
                    state.update {
                        state.value.apply { isACEnabled = event.isEnabled }
                    }
                }
                is PHEVScheduleEvents.SaveSchedule -> {
                    savePHEVSchedule(event.isStartTimeFlow)
                }
            }
        }

        private fun mapToPHEVScheduleDetailUIModel(
            isStartTimeFlow: Boolean,
            chargeInfo: ChargeInfo?,
        ) {
            _phevScheduleDetailState.value = PHEVScheduleDetailState.Init
            val phevScheduleDetailUIModel = chargeInfo?.toPHEVScheduleDetailUIModel(isStartTimeFlow)
            phevScheduleDetailUIModel?.let {
                state.value = it
                _phevScheduleDetailState.value = PHEVScheduleDetailState.LoadContent
            }
        }

        private fun savePHEVSchedule(isStartTimeFlow: Boolean) {
            logFirebaseAnalyticsEvents(
                groupName = AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP.eventName,
                eventName =
                    if (isStartTimeFlow) {
                        AnalyticsEventParam.SCHEDULE_START_TIME
                    } else {
                        AnalyticsEventParam.SCHEDULE_DEP_TIME
                    },
            )
            viewModelScope.launch(dispatcherProvider.main()) {
                _phevScheduleDetailState.value = PHEVScheduleDetailState.Loading
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    chargeScheduleUseCase.savePHEVSchedule(
                        vehicleInfo = vehicle,
                        requestModel = state.value.toChargeTimerRequest(isStartTimeFlow),
                    )
                        .flowOn(dispatcherProvider.io())
                        .collect { appRequestNo ->
                            if (appRequestNo != null) {
                                sendEvent(
                                    ToastUiEvent.ToastFromStringRes(
                                        R.string.phev_save_schedule_success,
                                    ),
                                )
                                _phevScheduleDetailState.value = PHEVScheduleDetailState.RefreshPHEVSchedule
                            } else {
                                sendEvent(
                                    ToastUiEvent.ToastFromStringRes(
                                        R.string.phev_save_schedule_failure,
                                    ),
                                )
                            }
                        }
                }
            }
        }

        fun logFirebaseAnalyticsEvents(
            groupName: String,
            eventName: String,
        ) {
            analyticsLogger.logEventWithParameter(groupName, eventName)
        }
    }

sealed class PHEVScheduleEvents {
    data class InitScreen(val isStartTimeFlow: Boolean, val chargeInfo: ChargeInfo?) : PHEVScheduleEvents()

    data class OnTimePicked(val hour: Int, val min: Int) : PHEVScheduleEvents()

    data class OnAMPMSelected(val isAM: Boolean, val time24HrFormat: String) : PHEVScheduleEvents()

    data class OnClimateCheckChanged(val isEnabled: Boolean) : PHEVScheduleEvents()

    class SaveSchedule(val isStartTimeFlow: Boolean) : PHEVScheduleEvents()
}
