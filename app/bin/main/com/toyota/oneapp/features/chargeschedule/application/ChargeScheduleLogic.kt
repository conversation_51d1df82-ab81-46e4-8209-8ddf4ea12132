package com.toyota.oneapp.features.chargeschedule.application

import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.CreateScheduleRequest
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.EcoScheduleDetailsResponse
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.Time
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.UpdateScheduleRequest
import com.toyota.oneapp.features.chargeschedule.domain.model.EcoScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.MultiDayScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.PHEVScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.toMultiDayScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.toPHEVScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.repo.ChargeScheduleRepo
import com.toyota.oneapp.features.chargeschedule.presentation.model.ScheduleResult
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.CONST_COLON
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.CONST_SPACE
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeTimerRequest
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.RealTimeStatusPayload
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.TimerChargeInfo
import com.toyota.oneapp.features.core.commonapicalls.domain.repository.CommonApiRepository
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.CorrelationIdProvider
import com.toyota.oneapp.util.DateUtil
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.UUID
import javax.inject.Inject

class ChargeScheduleLogic
    @Inject
    constructor(
        private val commonRepository: CommonApiRepository,
        private val chargeScheduleRepo: ChargeScheduleRepo,
        private val correlationIdProvider: CorrelationIdProvider,
        private val dateUtil: DateUtil,
    ) : ChargeScheduleUseCase {
        override fun fetchEcoScheduleDetails(vin: String): Flow<EcoScheduleModel?> =
            flow {
                val response = chargeScheduleRepo.fetchEcoScheduleDetails(vin)

                if (response is Resource.Success) {
                    emit(response.data?.toEcoScheduleModel())
                } else {
                    emit(null)
                }
            }

        override fun mapToPHEVSceduleModel(
            isChargeManagementFlow: Boolean,
            electricStatusResponse: ElectricStatusResponse?,
        ): PHEVScheduleModel? = electricStatusResponse?.toPHEVScheduleModel(isChargeManagementFlow, dateUtil)

        override fun fetchEVVechicleInfo(
            isChargeManagementFlow: Boolean,
            vehicleInfo: VehicleInfo,
        ): Flow<PHEVScheduleModel?> =
            flow {
                val response =
                    commonRepository.fetchChargeManagementDetail(
                        vin = vehicleInfo.vin,
                        generation = vehicleInfo.generation,
                        brand = vehicleInfo.brand,
                    )

                if (response is Resource.Success) {
                    emit(response.data?.toPHEVScheduleModel(isChargeManagementFlow, dateUtil))
                } else {
                    emit(null)
                }
            }

        override fun fetchEVVehicleRealTimeStatusForMultiDaySchedule(
            isChargeManagementFlow: Boolean,
            appRequestNo: String,
            ecoStartAndEndTime: List<String>,
            vehicleInfo: VehicleInfo,
        ): Flow<MultiDayScheduleModel?> =
            flow {
                val response =
                    commonRepository.getEvRealTimeStatus(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        generation = vehicleInfo.generation,
                        appRequestNo = appRequestNo,
                    )

                if (response is Resource.Success) {
                    val payload = response.data?.payload
                    val realtimeStatusResult = payload?.realtimeStatusResult

                    if (payload == null || realtimeStatusResult == null) {
                        emit(null)
                    } else if (realtimeStatusResult.status == 0) {
                        when (realtimeStatusResult.result) {
                            0 -> {
                                emit(
                                    payload.toMultiDayScheduleModel(
                                        isChargeManagementFlow,
                                        response.data,
                                        dateUtil,
                                        ecoStartAndEndTime,
                                    ),
                                )
                            }
                            else -> {
                                emit(
                                    MultiDayScheduleModel.EmptyMultiDaySchedule(
                                        null,
                                        R.string.api_failure,
                                    ),
                                )
                            }
                        }
                    } else {
                        emit(null)
                    }
                } else {
                    emit(null)
                }
            }

        override fun fetchEVVehicleRealTimeStatusForPHEV(
            isChargeManagementFlow: Boolean,
            appRequestNo: String,
            vehicleInfo: VehicleInfo,
        ): Flow<PHEVScheduleModel?> =
            flow {
                val response =
                    commonRepository.getEvRealTimeStatus(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        generation = vehicleInfo.generation,
                        appRequestNo = appRequestNo,
                    )

                if (response is Resource.Success) {
                    emit(getPHEVScheduleModel(isChargeManagementFlow, dateUtil, response.data))
                } else {
                    emit(null)
                }
            }

        override fun postEVVehicleRealTimeStatus(vehicleInfo: VehicleInfo): Flow<String?> =
            flow {
                val response =
                    commonRepository.postEvRealTimeStatus(
                        requestId = correlationIdProvider.get(),
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        generation = vehicleInfo.generation,
                        deviceId = UUID.randomUUID().toString(),
                    )

                if (response is Resource.Success) {
                    emit(response.data?.payload?.getAppRequestNo())
                } else {
                    emit(null)
                }
            }

        override fun mapToMultiDayScheduleUIModel(
            isChargeManagementFlow: Boolean,
            electricStatusResponse: ElectricStatusResponse?,
            ecoStartAndEndTime: List<String>,
        ): MultiDayScheduleModel? =
            electricStatusResponse?.payload?.toMultiDayScheduleModel(
                isChargeManagementFlow,
                response = electricStatusResponse,
                dateUtil = dateUtil,
                ecoStartAndEndTime = ecoStartAndEndTime,
            )

        override fun fetchMultiDayScheduleStatus(
            isChargeManagementFlow: Boolean,
            appRequestNo: String,
            ecoStartAndEndTime: List<String>,
            vehicleInfo: VehicleInfo,
        ): Flow<MultiDayScheduleModel?> =
            flow {
                val response =
                    commonRepository.getEVRemoteControlStatus(
                        vin = vehicleInfo.vin,
                        generation = vehicleInfo.generation,
                        brand = vehicleInfo.brand,
                        appRequestNo = appRequestNo,
                    )

                if (response is Resource.Success) {
                    response.data?.payload?.let {
                        it.remoteControlResult?.also { result ->
                            if (result.status == 0 && result.result == 0) {
                                emit(
                                    it.toMultiDayScheduleModel(
                                        isChargeManagementFlow,
                                        response.data,
                                        dateUtil,
                                        ecoStartAndEndTime,
                                    ),
                                )
                            } else {
                                emit(null)
                            }
                        } ?: run {
                            emit(
                                it.toMultiDayScheduleModel(
                                    isChargeManagementFlow,
                                    response.data,
                                    dateUtil,
                                    ecoStartAndEndTime,
                                ),
                            )
                        }
                    } ?: kotlin.run {
                        emit(MultiDayScheduleModel.EmptyMultiDaySchedule(response.data, null))
                    }
                } else {
                    emit(MultiDayScheduleModel.EmptyMultiDaySchedule(null, null))
                }
            }

        override fun saveEcoScheduleDetails(
            vin: String,
            isEcoScheduleEnabled: Boolean,
            settingsId: String,
        ): Flow<Boolean> =
            flow {
                val response =
                    chargeScheduleRepo.saveEcoSchedulesDetails(
                        vin = vin,
                        isEcoScheduleEnabled = isEcoScheduleEnabled,
                        settingsId = settingsId,
                    )

                emit(response is Resource.Success)
            }

        override fun createMultiDaySchedule(
            isScheduleEnabled: Boolean,
            vehicleInfo: VehicleInfo,
            startAndEndTime: List<String?>,
            daysOfWeek: List<String>,
        ): Flow<ScheduleResult<String>> =
            flow {
                val startTime = startAndEndTime.first()?.split(CONST_COLON)
                val endTime = startAndEndTime.last()?.split(CONST_COLON)
                val requestBody =
                    CreateScheduleRequest(
                        enabled = isScheduleEnabled,
                        startTime =
                            startTime?.let {
                                Time(
                                    hour = it.first().toInt(),
                                    minute = it.last().toInt(),
                                )
                            },
                        endTime =
                            endTime?.let {
                                Time(
                                    hour = it.first().toInt(),
                                    minute = it.last().toInt(),
                                )
                            },
                        daysOfTheWeek = daysOfWeek,
                    )

                val response =
                    chargeScheduleRepo.createMultiDaySchedule(
                        vehicleInfo = vehicleInfo,
                        requestBody = requestBody,
                    )

                if (response is Resource.Success) {
                    emit(
                        ScheduleResult(
                            result = response.data?.payload?.appRequestNo,
                            errorMessage = null,
                        ),
                    )
                } else {
                    emit(
                        ScheduleResult(
                            result = null,
                            errorMessage = (response as? Resource.Failure)?.error?.message,
                        ),
                    )
                }
            }

        override fun deleteMultiDaySchedule(
            scheduleId: String,
            vehicleInfo: VehicleInfo,
        ): Flow<ScheduleResult<String>> =
            flow {
                val response =
                    chargeScheduleRepo.deleteMultiDayChargeSchedule(
                        scheduleId = scheduleId,
                        vehiceInfo = vehicleInfo,
                    )

                if (response is Resource.Success) {
                    emit(
                        ScheduleResult(
                            result = response.data?.payload?.appRequestNo,
                            errorMessage = null,
                        ),
                    )
                } else {
                    ScheduleResult(
                        result = null,
                        errorMessage = (response as? Resource.Failure)?.error?.message,
                    )
                }
            }

        override fun updateMultiDaySchedule(
            isScheduleEnabled: Boolean,
            timerChargeInfo: TimerChargeInfo,
            vehicleInfo: VehicleInfo,
        ): Flow<ScheduleResult<String>> =
            flow {
                val startTime = timerChargeInfo.startTime?.split(CONST_COLON)
                val endTime = timerChargeInfo.endTime?.split(CONST_COLON)
                val requestBody =
                    UpdateScheduleRequest(
                        settingId = timerChargeInfo.settingId?.toInt(),
                        enabled = isScheduleEnabled,
                        startTime =
                            if (startTime?.isNotEmpty() == true) {
                                Time(
                                    hour = startTime.first().toInt(),
                                    minute = startTime.last().toInt(),
                                )
                            } else {
                                null
                            },
                        endTime =
                            if (endTime?.isNotEmpty() == true) {
                                Time(
                                    hour = endTime.first().toInt(),
                                    minute = endTime.last().toInt(),
                                )
                            } else {
                                null
                            },
                        daysOfTheWeek = timerChargeInfo.daysOfTheWeek ?: emptyList(),
                    )

                val response =
                    chargeScheduleRepo.updateMultiDayChargeSchedule(
                        vehicleInfo = vehicleInfo,
                        request = requestBody,
                    )

                if (response is Resource.Success) {
                    emit(
                        ScheduleResult(
                            result = response.data?.payload?.appRequestNo,
                            errorMessage = null,
                        ),
                    )
                } else {
                    emit(
                        ScheduleResult(
                            result = null,
                            errorMessage = (response as? Resource.Failure)?.error?.message,
                        ),
                    )
                }
            }

        override fun savePHEVSchedule(
            requestModel: ChargeTimerRequest,
            vehicleInfo: VehicleInfo,
        ): Flow<String?> =
            flow {
                val response =
                    commonRepository.postElectricVehicleCommand(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        generation = vehicleInfo.generation,
                        requestBody = requestModel,
                    )

                if (response is Resource.Success) {
                    emit(response.data?.payload?.appRequestNo)
                } else {
                    emit(null)
                }
            }
    }

private fun getPHEVScheduleModel(
    isChargeManagementFlow: Boolean,
    dateUtil: DateUtil,
    response: ElectricStatusResponse?,
): PHEVScheduleModel? {
    response?.payload?.realtimeStatusResult?.let { statusResult ->
        statusResult.status.takeIf { it == 0 }?.let {
            return when (statusResult.result) {
                0 -> {
                    response.toPHEVScheduleModel(isChargeManagementFlow, dateUtil)
                }
                else -> {
                    PHEVScheduleModel.PHEVScheduleErrorModel(
                        R.string.phev_realtime_api_error,
                    )
                }
            }
        }
    }
    return null
}

private fun RealTimeStatusPayload.getAppRequestNo(): String =
    if (returnCode == "ONE-RES-10000") {
        ""
    } else {
        appRequestNo
    }

private fun EcoScheduleDetailsResponse.toEcoScheduleModel(): EcoScheduleModel =
    EcoScheduleModel(
        startTime =
            this.baInfo
                ?.echoTimes
                ?.start
                .orEmpty(),
        endTime =
            this.baInfo
                ?.echoTimes
                ?.end
                .orEmpty(),
        formattedStartTime =
            ChargeScheduleUtil
                .convert24HrTimeTo12Hr(
                    this.baInfo?.echoTimes?.start,
                    false,
                )?.replace(CONST_SPACE, "")
                ?.lowercase() ?: "11pm",
        formattedEndTime =
            ChargeScheduleUtil
                .convert24HrTimeTo12Hr(
                    this.baInfo?.echoTimes?.end,
                    false,
                )?.replace(CONST_SPACE, "")
                ?.lowercase() ?: "4am",
        optIn = this.ecoEnrollmentDetails?.enabled ?: false,
    )
