package com.toyota.oneapp.features.chargeschedule.domain.model

import androidx.annotation.StringRes
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeInfo
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.lastUpdatedDate

sealed class PHEVScheduleModel(
    val response: ElectricStatusResponse?,
) {
    data class PHEVScheduleSuccessModel(
        val phevScheduleResponse: ElectricStatusResponse?,
        val shouldSetTimerInVehicle: Boolean,
        val startTimeInfo: TileInfoUIModel,
        val departureTimeInfo: TileInfoUIModel,
        val chargeInfo: ChargeInfo?,
        val lastUpdated: String,
    ) : PHEVScheduleModel(phevScheduleResponse)

    data class PHEVScheduleErrorModel(
        @StringRes val errorMessageRes: Int,
    ) : PHEVScheduleModel(null)
}

data class TileInfoUIModel(
    @StringRes val title: Int,
    @StringRes val description: Int,
    val onOffStatus: OnOffStatus,
)

enum class OnOffStatus {
    ON,
    OFF,
}

fun ElectricStatusResponse.toPHEVScheduleModel(
    isChargeManagementFlow: Boolean,
    dateUtil: DateUtil,
): PHEVScheduleModel? {
    this.payload.vehicleInfo?.let {
        return if (!isChargeManagementFlow &&
            (it.chargeInfo?.chargeType == 0 || it.chargeInfo?.chargeType == 15)
        ) {
            PHEVScheduleModel.PHEVScheduleErrorModel(R.string.phev_realtime_api_error)
        } else {
            PHEVScheduleModel.PHEVScheduleSuccessModel(
                phevScheduleResponse = this,
                shouldSetTimerInVehicle = it.chargeInfo?.chargeType == 0 || it.chargeInfo?.chargeType == 15,
                startTimeInfo =
                    TileInfoUIModel(
                        title = R.string.ev_climate_schedule_start_time,
                        description = R.string.start_time_description,
                        onOffStatus =
                            when (it.chargeInfo?.chargeType) {
                                1 -> {
                                    OnOffStatus.ON
                                }
                                else -> {
                                    OnOffStatus.OFF
                                }
                            },
                    ),
                departureTimeInfo =
                    TileInfoUIModel(
                        title = R.string.ev_depart_time,
                        description = R.string.departure_time_description,
                        onOffStatus =
                            when (it.chargeInfo?.chargeType) {
                                2, 3 -> {
                                    OnOffStatus.ON
                                }
                                else -> {
                                    OnOffStatus.OFF
                                }
                            },
                    ),
                lastUpdated = dateUtil.getDateFromString(it.acquisitionDatetime)?.lastUpdatedDate().orEmpty(),
                chargeInfo = it.chargeInfo,
            )
        }
    }
    return null
}
