package com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class EcoScheduleDetailsResponse(
    @SerializedName("message") val message: String?,
    @SerializedName("ba_info") val baInfo: BAInfo?,
    @SerializedName("ecoEnrollmentDetails") val ecoEnrollmentDetails: EcoEnrollmentDetails?,
)

data class BAInfo(
    @SerializedName("abbrev") val abbrev: String?,
    @SerializedName("name") val name: String?,
    @SerializedName("echo_times") val echoTimes: StartAndEndTime?,
    @SerializedName("echo_percentile") val echoPercentile: String?,
)

data class EcoEnrollmentDetails(
    @SerializedName("vin") val vin: String?,
    @SerializedName("guid") val guid: String?,
    @SerializedName("watttime_ba") val watttimeBA: String?,
    @SerializedName("eco_start_time") val ecoStartTime: String?,
    @SerializedName("eco_end_time") val ecoEndTime: String?,
    @SerializedName("opted_from_date") val optedFromDate: String?,
    @SerializedName("opted_to_date") val optedToDate: String?,
    @SerializedName("enabled") val enabled: Boolean?,
    @SerializedName("settingId") val settingId: String?,
)

data class StartAndEndTime(
    @SerializedName("start") val start: String?,
    @SerializedName("end") val end: String?,
)
