/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeschedule.presentation.previewprovider

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeschedule.domain.model.ChargingTimeInfo
import com.toyota.oneapp.features.chargeschedule.domain.model.MultiDayScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.OnOffStatus
import com.toyota.oneapp.features.chargeschedule.domain.model.PHEVScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.TileInfoUIModel
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.TimerChargeInfo

class MultiDayScheduleModelProvider : PreviewParameterProvider<MultiDayScheduleModel.ListMultiDayScheduleModel> {
    val model =
        MultiDayScheduleModel.ListMultiDayScheduleModel(
            listScheduleResponse = null,
            maxSchedulingLimit = 1,
            ecoScheduleObj = null,
            chargingTimeInfoList =
                listOf(
                    ChargingTimeInfo(
                        settingsId = "1",
                        timeRange = "6:00am - 11:00am",
                        dayOfWeek = listOf(1, 2, 3),
                        isEnabled = true,
                        isEcoSchedule = false,
                        isOffPeekSchedule = false,
                        responseObj = TimerChargeInfo(),
                    ),
                ),
            lastUpdated = "Today at 2:01 pm",
        )
    override val values = sequenceOf(model)
}

class PHEVScheduleProvider : PreviewParameterProvider<PHEVScheduleModel.PHEVScheduleSuccessModel> {
    val model =
        PHEVScheduleModel.PHEVScheduleSuccessModel(
            phevScheduleResponse = null,
            shouldSetTimerInVehicle = false,
            startTimeInfo =
                TileInfoUIModel(
                    title = R.string.ev_climate_schedule_start_time,
                    description = R.string.start_time_description,
                    onOffStatus = OnOffStatus.ON,
                ),
            departureTimeInfo =
                TileInfoUIModel(
                    title = R.string.ev_depart_time,
                    description = R.string.departure_time_description,
                    onOffStatus = OnOffStatus.OFF,
                ),
            chargeInfo = null,
            lastUpdated = "Today at 2:01 pm",
        )
    override val values = sequenceOf(model)
}
