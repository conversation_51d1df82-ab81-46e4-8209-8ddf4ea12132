/*
 *  Created by su<PERSON>n.ram on 25/10/24, 10:28 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 25/10/24, 10:28 pm
 *
 */

package com.toyota.oneapp.features.chargeschedule.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.chargeschedule.application.ChargeScheduleUseCase
import com.toyota.oneapp.features.chargeschedule.application.EditScheduleState
import com.toyota.oneapp.features.chargeschedule.presentation.model.EditScheduleUIModel
import com.toyota.oneapp.features.chargeschedule.presentation.model.toCreateScheduleUIModel
import com.toyota.oneapp.features.chargeschedule.presentation.model.toEditScheduleUIModel
import com.toyota.oneapp.features.chargeschedule.presentation.model.toOffPeakScheduleUIModel
import com.toyota.oneapp.features.chargeschedule.presentation.model.toPickedAMPM
import com.toyota.oneapp.features.chargeschedule.presentation.model.toPickedTime
import com.toyota.oneapp.features.chargeschedule.presentation.model.toTimerChargeInfo
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.TimerChargeInfo
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.features.core.presentation.ToastUiEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class EditScheduleViewModel
    @Inject
    constructor(
        private val chargeScheduleUseCase: ChargeScheduleUseCase,
        private val analyticsLogger: AnalyticsLogger,
        private val applicationData: ApplicationData,
        private val dispatcherProvider: DispatcherProvider,
    ) : BaseViewModel<EditScheduleUIModel, EditScheduleEvents>() {
        private val _editScheduleState =
            MutableStateFlow<EditScheduleState>(
                value = EditScheduleState.Init,
            )
        val editScheduleState = _editScheduleState.asStateFlow()

        fun reset() {
            _editScheduleState.value = EditScheduleState.Init
        }

        override fun defaultState() = EditScheduleUIModel()

        override fun onEvent(event: EditScheduleEvents) {
            when (event) {
                is EditScheduleEvents.OnOffPeakHoursCheckChanged -> {
                    handleOffPeakCheckChange(event.isEnabled)
                }
                is EditScheduleEvents.OnStartTimePicked -> {
                    state.update {
                        state.value.apply {
                            startTime?.toPickedTime(event.hour, event.min)
                        }
                    }
                }
                is EditScheduleEvents.OnStartAMPMSelected -> {
                    state.update {
                        state.value.apply {
                            startTime?.toPickedAMPM(event.isAM, event.time24HrFormat)
                        }
                    }
                }
                is EditScheduleEvents.OnEndTimePicked -> {
                    state.update {
                        state.value.apply {
                            endTime?.toPickedTime(event.hour, event.min)
                        }
                    }
                }
                is EditScheduleEvents.OnEndAMPMSelected -> {
                    state.update {
                        state.value.apply {
                            endTime?.toPickedAMPM(event.isAM, event.time24HrFormat)
                        }
                    }
                }
                is EditScheduleEvents.OnEndTimeCheckChanged -> {
                    if (event.isEnabled && state.value.endTime?.time24HrFormat.isNullOrEmpty()) {
                        onEvent(EditScheduleEvents.OnEndTimePicked(2, 0))
                    }
                    state.update {
                        it.copy(
                            isEndTimeEnabled = event.isEnabled,
                        )
                    }
                }
                is EditScheduleEvents.CreateSchedule -> {
                    createMultiDaySchedule(event.uiModel)
                }
                is EditScheduleEvents.DeleteSchedule -> {
                    deleteMultiDaySchedule(event.scheduleId)
                }
                is EditScheduleEvents.UpdateSchedule -> {
                    updateMultiDaySchedule(event.uiModel)
                }
            }
        }

        fun mapToEditScheduleUIModel(
            isOffPeakHour: Boolean,
            timerChargeInfo: TimerChargeInfo?,
        ) {
            val uiModel =
                timerChargeInfo?.toEditScheduleUIModel() ?: run {
                    toCreateScheduleUIModel()
                }
            state.value = uiModel
            state.update {
                it.copy(isOffPeakScheduleEnabled = isOffPeakHour)
            }
            _editScheduleState.value = EditScheduleState.LoadContent
        }

        private fun handleOffPeakCheckChange(isEnabled: Boolean) {
            state.update {
                it.copy(isOffPeakScheduleEnabled = isEnabled)
            }

            if (isEnabled) {
                val uiModel = state.value.toOffPeakScheduleUIModel()
                state.value = uiModel
                _editScheduleState.value = EditScheduleState.LoadContent
            }
        }

        private fun createMultiDaySchedule(createScheduleUIModel: EditScheduleUIModel?) {
            createScheduleUIModel?.let {
                analyticsLogger.logEventWithParameter(
                    AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP.eventName,
                    AnalyticsEventParam.VEHICLE_EV_CREATE_A_SCHEDULE_BUTTON_CTA,
                )
                viewModelScope.launch(dispatcherProvider.main()) {
                    _editScheduleState.value = EditScheduleState.Loading
                    applicationData.getSelectedVehicleState().value?.let { vehicle ->
                        chargeScheduleUseCase
                            .createMultiDaySchedule(
                                isScheduleEnabled = true,
                                vehicleInfo = vehicle,
                                startAndEndTime =
                                    listOf(
                                        createScheduleUIModel.startTime?.time24HrFormat,
                                        if (createScheduleUIModel.isEndTimeEnabled == true) {
                                            createScheduleUIModel.endTime?.time24HrFormat
                                        } else {
                                            null
                                        },
                                    ),
                                daysOfWeek = createScheduleUIModel.selectedDaysOfWeek ?: emptyList(),
                            ).flowOn(dispatcherProvider.io())
                            .collect { result ->
                                if (result.result != null) {
                                    sendEvent(
                                        ToastUiEvent.ToastFromStringRes(
                                            R.string.create_schedule_success,
                                        ),
                                    )
                                    _editScheduleState.value =
                                        EditScheduleState.RefreshMultiDaySchedule(
                                            result.result,
                                        )
                                } else {
                                    _editScheduleState.value = EditScheduleState.Error
                                    sendEvent(
                                        ToastUiEvent.ToastFromString(result.errorMessage),
                                    )
                                }
                            }
                    }
                }
            }
        }

        private fun deleteMultiDaySchedule(scheduleId: String?) {
            viewModelScope.launch(dispatcherProvider.main()) {
                _editScheduleState.value = EditScheduleState.Loading
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    chargeScheduleUseCase
                        .deleteMultiDaySchedule(
                            scheduleId = scheduleId.orEmpty(),
                            vehicleInfo = vehicle,
                        ).flowOn(dispatcherProvider.io())
                        .collect { result ->
                            if (result.result != null) {
                                sendEvent(
                                    ToastUiEvent.ToastFromStringRes(
                                        R.string.delete_schedule_success,
                                    ),
                                )
                                _editScheduleState.value =
                                    EditScheduleState.RefreshMultiDaySchedule(
                                        result.result,
                                    )
                            } else {
                                sendEvent(
                                    ToastUiEvent.ToastFromString(result.errorMessage),
                                )
                            }
                        }
                }
            }
        }

        private fun updateMultiDaySchedule(editScheduleUIModel: EditScheduleUIModel?) {
            editScheduleUIModel?.let {
                viewModelScope.launch(dispatcherProvider.main()) {
                    _editScheduleState.value = EditScheduleState.Loading
                    applicationData.getSelectedVehicleState().value?.let { vehicle ->
                        chargeScheduleUseCase
                            .updateMultiDaySchedule(
                                isScheduleEnabled = editScheduleUIModel.isScheduleEnabled ?: false,
                                timerChargeInfo = editScheduleUIModel.toTimerChargeInfo(),
                                vehicleInfo = vehicle,
                            ).flowOn(dispatcherProvider.io())
                            .collect { result ->
                                if (result.result != null) {
                                    sendEvent(
                                        ToastUiEvent.ToastFromStringRes(
                                            R.string.update_schedule_success,
                                        ),
                                    )
                                    _editScheduleState.value =
                                        EditScheduleState.RefreshMultiDaySchedule(
                                            result.result,
                                        )
                                } else {
                                    _editScheduleState.value = EditScheduleState.Error
                                    sendEvent(
                                        ToastUiEvent.ToastFromString(result.errorMessage),
                                    )
                                }
                            }
                    }
                }
            }
        }
    }

sealed class EditScheduleEvents {
    data class OnOffPeakHoursCheckChanged(
        val isEnabled: Boolean,
    ) : EditScheduleEvents()

    data class OnStartTimePicked(
        val hour: Int,
        val min: Int,
    ) : EditScheduleEvents()

    data class OnStartAMPMSelected(
        val isAM: Boolean,
        val time24HrFormat: String,
    ) : EditScheduleEvents()

    data class OnEndTimePicked(
        val hour: Int,
        val min: Int,
    ) : EditScheduleEvents()

    data class OnEndAMPMSelected(
        val isAM: Boolean,
        val time24HrFormat: String,
    ) : EditScheduleEvents()

    data class OnEndTimeCheckChanged(
        val isEnabled: Boolean,
    ) : EditScheduleEvents()

    data class CreateSchedule(
        val uiModel: EditScheduleUIModel?,
    ) : EditScheduleEvents()

    data class DeleteSchedule(
        val scheduleId: String?,
    ) : EditScheduleEvents()

    data class UpdateSchedule(
        val uiModel: EditScheduleUIModel?,
    ) : EditScheduleEvents()
}
