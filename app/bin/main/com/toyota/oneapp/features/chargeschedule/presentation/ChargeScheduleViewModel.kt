package com.toyota.oneapp.features.chargeschedule.presentation

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.chargeschedule.application.ChargeScheduleState
import com.toyota.oneapp.features.chargeschedule.application.ChargeScheduleUseCase
import com.toyota.oneapp.features.chargeschedule.application.EcoSchduleCardState
import com.toyota.oneapp.features.chargeschedule.domain.model.ChargingTimeInfo
import com.toyota.oneapp.features.chargeschedule.domain.model.MultiDayScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.PHEVScheduleModel
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil
import com.toyota.oneapp.features.chargeschedule.util.collectAndRetry
import com.toyota.oneapp.features.core.commonapicalls.application.ElectricStatusState
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.TimerChargeInfo
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject
import kotlin.coroutines.suspendCoroutine

@HiltViewModel
class ChargeScheduleViewModel
    @Inject
    constructor(
        private val chargeScheduleUseCase: ChargeScheduleUseCase,
        private val sharedDataSource: SharedDataSource,
        private val analyticsLogger: AnalyticsLogger,
        private val applicationData: ApplicationData,
        private val dispatcherProvider: DispatcherProvider,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : BaseViewModel<ChargeScheduleUIState, ChargeScheduleEvents>() {
        private var job: Job? = null
        private var ecoStartAndEndTime = listOf<String>()
        private var ecoScheduleObj: ChargingTimeInfo? = null
        private var isChargeManagementFlow = false

        var toastMessage by mutableStateOf<String?>(null)
        var toastMessageRes by mutableStateOf<Int?>(null)

        private val _visitZipInvalidCount = MutableStateFlow(0)
        val visitZipInvalidCount: StateFlow<Int> = _visitZipInvalidCount

        private val _visitEnrollmentFailureCount = MutableStateFlow(0)
        val visitEnrollmentFailureCount: StateFlow<Int> = _visitEnrollmentFailureCount

        init {
            loadVisitCount()
        }

        private fun loadVisitCount() {
            viewModelScope.launch {
                flow {
                    emit(oneAppPreferenceModel.getVisitCount())
                }
                    .catch {
                        _visitZipInvalidCount.value = 0
                    }
                    .collect { visitCount ->
                        _visitZipInvalidCount.value = visitCount
                    }
            }
            viewModelScope.launch {
                flow {
                    emit(oneAppPreferenceModel.getVisitCountForEnrollmentFailure())
                }
                    .catch {
                        _visitEnrollmentFailureCount.value = 0
                    }
                    .collect { enrollmentFailCount ->
                        _visitEnrollmentFailureCount.value = enrollmentFailCount
                    }
            }
        }

        fun reset() {
            toastMessage = null
            toastMessageRes = null
        }

        override fun defaultState() = ChargeScheduleUIState()

        override fun onEvent(event: ChargeScheduleEvents) {
            when (event) {
                is ChargeScheduleEvents.InitChargeSchedule -> {
                    if (isRemoteSharedUser() == false) {
                        initChargeSchedule()
                    }
                }

                is ChargeScheduleEvents.InitMultiDaySchedule -> {
                    initMultiDaySchedule(
                        startTime = System.currentTimeMillis(),
                        isChargeManagementFlow = event.isChargeManagementFlow,
                    )
                }

                is ChargeScheduleEvents.InitPHEVSchedule -> {
                    initPHEVScheduleTimer(event.isChargeManagementFlow)
                }

                is ChargeScheduleEvents.OnLogEvent -> {
                    analyticsLogger.logEventWithParameter(event.group, event.event)
                }

                else -> { // left intentionally blank
                }
            }
        }

        private fun initChargeSchedule() {
            applicationData.getSelectedVehicleState().value?.apply {
                val isMultiDayChargingEnabled = isFeatureEnabled(Feature.MULTI_DAY_CHARGING)

                when {
                    isMultiDayChargingEnabled -> {
                        analyticsLogger.logEvent(AnalyticsEvent.VEHICLE_EV_MULTIDAY_SCHEDULE_PAGE)
                        viewModelScope.launch(dispatcherProvider.main()) {
                            state.update {
                                it.copy(
                                    viewState = ChargeScheduleState.MultiDayScheduleLoading,
                                )
                            }
                            val ecoScheduleDeffered =
                                async {
                                    fetchEcoScheduleDetails()
                                }
                            ecoStartAndEndTime = ecoScheduleDeffered.await()
                            initMultiDaySchedule(
                                startTime = System.currentTimeMillis(),
                                ecoStartAndEndTime = ecoStartAndEndTime,
                                isChargeManagementFlow = isChargeManagementFlow,
                            )
                        }
                    }

                    (isCY17Plus && isEVPhpModel) ||
                        (is21MMVehicle && isEVPhpModel) -> {
                        viewModelScope.launch(dispatcherProvider.main()) {
                            state.update {
                                it.copy(
                                    showProgress = true,
                                )
                            }
                            fetchEcoScheduleDetails()
                            initPHEVScheduleTimer()
                        }
                    }

                    else -> {
                        state.update {
                            it.copy(
                                viewState =
                                    ChargeScheduleState.NoSchedule(
                                        vehicleBrand = brand,
                                        isMultiDaySchedule = true,
                                        errorMessage = null,
                                    ),
                            )
                        }
                    }
                }
            }
        }

        private suspend fun fetchEcoScheduleDetails(): List<String> =
            suspendCoroutine { continuation ->
                viewModelScope.launch(dispatcherProvider.main()) {
                    state.update {
                        it.copy(
                            ecoCardState = EcoSchduleCardState.Loading,
                        )
                    }
                    applicationData.getSelectedVehicleState().value?.let { vehicle ->
                        chargeScheduleUseCase
                            .fetchEcoScheduleDetails(vehicle.vin)
                            .flowOn(dispatcherProvider.io())
                            .collect { ecoScheduleUIModel ->
                                if (ecoScheduleUIModel != null) {
                                    state.update {
                                        it.copy(
                                            ecoCardState =
                                                EcoSchduleCardState.Success(
                                                    ecoScheduleUIModel,
                                                ),
                                        )
                                    }
                                    continuation.resumeWith(
                                        Result.success(
                                            listOf<String>(
                                                ecoScheduleUIModel.startTime,
                                                ecoScheduleUIModel.endTime,
                                            ),
                                        ),
                                    )
                                }
                            }
                    }
                }
            }

        private fun initMultiDaySchedule(
            startTime: Long,
            appRequestNo: String = "",
            isChargeManagementFlow: Boolean,
            ecoStartAndEndTime: List<String> = emptyList(),
        ) {
            this.isChargeManagementFlow = isChargeManagementFlow
            job =
                viewModelScope.launch(dispatcherProvider.main()) {
                    applicationData.getSelectedVehicleState().value?.let { vehicle ->
                        sharedDataSource.getElectricStatusState().collect { electricStatusState ->
                            when (electricStatusState) {
                                is ElectricStatusState.Success -> {
                                    val uiModel =
                                        chargeScheduleUseCase.mapToMultiDayScheduleUIModel(
                                            isChargeManagementFlow,
                                            electricStatusState.response,
                                            ecoStartAndEndTime,
                                        )
                                    handleMultiDayScheduleUIState(vehicle.brand, uiModel)
                                }

                                else -> {
                                    fetchMultiDayScheduleStatusFromAPI(
                                        startTime,
                                        appRequestNo,
                                        vehicle,
                                        ecoStartAndEndTime,
                                    )
                                }
                            }
                        }
                    }
                }
        }

        private fun fetchMultiDayScheduleStatusFromAPI(
            startTime: Long,
            appRequestNo: String = "",
            vehicleInfo: VehicleInfo,
            ecoStartAndEndTime: List<String>,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                sharedDataSource.setElectricStatusState(ElectricStatusState.Loading)
                chargeScheduleUseCase
                    .fetchMultiDayScheduleStatus(
                        isChargeManagementFlow = isChargeManagementFlow,
                        appRequestNo = appRequestNo,
                        ecoStartAndEndTime = ecoStartAndEndTime,
                        vehicleInfo = vehicleInfo,
                    ).flowOn(dispatcherProvider.io())
                    .collectAndRetry(
                        startTime = startTime,
                        onRetry = {
                            fetchMultiDayScheduleStatusFromAPI(
                                startTime,
                                appRequestNo,
                                vehicleInfo,
                                ecoStartAndEndTime,
                            )
                        },
                        onTimeout = {
                            state.update {
                                it.copy(
                                    viewState =
                                        ChargeScheduleState.NoSchedule(
                                            vehicleInfo.brand,
                                            true,
                                            null,
                                        ),
                                )
                            }
                        },
                    ) { multiDayScheduleUIModel ->
                        sharedDataSource.saveRealTimeElectricStatus(multiDayScheduleUIModel.response)
                        handleMultiDayScheduleUIState(vehicleInfo.brand, multiDayScheduleUIModel)
                    }
            }
        }

        private fun handleMultiDayScheduleUIState(
            vehicleBrand: String,
            multiDayScheduleUIModel: MultiDayScheduleModel?,
        ) {
            job?.cancel()
            when (multiDayScheduleUIModel) {
                is MultiDayScheduleModel.ListMultiDayScheduleModel -> {
                    ecoScheduleObj = multiDayScheduleUIModel.ecoScheduleObj
                    state.update {
                        it.copy(
                            viewState =
                                ChargeScheduleState.SchedulingList(
                                    vehicleBrand,
                                    multiDayScheduleUIModel,
                                ),
                        )
                    }
                }

                is MultiDayScheduleModel.EmptyMultiDaySchedule -> {
                    state.update {
                        it.copy(
                            viewState =
                                ChargeScheduleState.NoSchedule(
                                    vehicleBrand,
                                    true,
                                    multiDayScheduleUIModel.message,
                                ),
                        )
                    }
                }

                else -> { // left intentionally blank
                }
            }
        }

        private fun initPHEVScheduleTimer(isChargeManagementFlow: Boolean = false) {
            this.isChargeManagementFlow = isChargeManagementFlow
            job =
                viewModelScope.launch(dispatcherProvider.main()) {
                    applicationData.getSelectedVehicleState().value?.let { vehicle ->
                        sharedDataSource.getElectricStatusState().collect { electricStatusState ->
                            when (electricStatusState) {
                                is ElectricStatusState.Success -> {
                                    val uiModel =
                                        chargeScheduleUseCase.mapToPHEVSceduleModel(
                                            isChargeManagementFlow,
                                            electricStatusState.response,
                                        )
                                    handlePHEVUIState(vehicle.brand, uiModel)
                                }

                                else -> {
                                    fetchEVVehicleInfoFromAPI(vehicle)
                                }
                            }
                        }
                    }
                }
        }

        fun isRemoteSharedUser(): Boolean? {
            return applicationData.getSelectedVehicleState().value?.isPrimaryButNotRemoteUser
        }

        private fun fetchEVVehicleInfoFromAPI(vehicleInfo: VehicleInfo) {
            viewModelScope.launch(dispatcherProvider.main()) {
                sharedDataSource.setElectricStatusState(ElectricStatusState.Loading)
                chargeScheduleUseCase
                    .fetchEVVechicleInfo(isChargeManagementFlow, vehicleInfo)
                    .flowOn(dispatcherProvider.io())
                    .collect { phevScheduleUIModel ->
                        sharedDataSource.saveRealTimeElectricStatus(phevScheduleUIModel?.response)
                        handlePHEVUIState(vehicleInfo.brand, phevScheduleUIModel)
                    }
            }
        }

        private fun handlePHEVUIState(
            vehicleBrand: String,
            phevScheduleUIModel: PHEVScheduleModel?,
        ) {
            job?.cancel()
            if (isRemoteSharedUser() == false) {
                when (phevScheduleUIModel) {
                    is PHEVScheduleModel.PHEVScheduleSuccessModel -> {
                        state.update {
                            it.copy(
                                showProgress = false,
                                viewState =
                                    ChargeScheduleState.PHEVScheduleTimer(
                                        phevScheduleUIModel,
                                    ),
                            )
                        }
                    }

                    is PHEVScheduleModel.PHEVScheduleErrorModel -> {
                        state.update {
                            it.copy(
                                showProgress = false,
                                viewState =
                                    ChargeScheduleState.NoSchedule(
                                        vehicleBrand,
                                        false,
                                        phevScheduleUIModel.errorMessageRes,
                                    ),
                            )
                        }
                    }

                    else -> { // left intentionally blank
                    }
                }
            }
        }

        fun refreshLastUpdatedData(isMultiDayRefresh: Boolean = false) {
            viewModelScope.launch(dispatcherProvider.main()) {
                sharedDataSource.resetElectricStatusState()

                if (isMultiDayRefresh) {
                    state.update {
                        it.copy(
                            viewState = ChargeScheduleState.MultiDayScheduleLoading,
                        )
                    }
                } else {
                    state.update {
                        it.copy(
                            showProgress = true,
                        )
                    }
                }
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    chargeScheduleUseCase
                        .postEVVehicleRealTimeStatus(vehicle)
                        .flowOn(dispatcherProvider.io())
                        .collect { appRequestNo ->
                            if (isMultiDayRefresh) {
                                fetchRealTimeChargeInfoForMultiDaySchedule(
                                    System.currentTimeMillis(),
                                    appRequestNo.orEmpty(),
                                )
                            } else {
                                fetchRealTimeChargeInfoForPHEV(
                                    System.currentTimeMillis(),
                                    appRequestNo.orEmpty(),
                                )
                            }
                        }
                }
            }
        }

        private fun fetchRealTimeChargeInfoForMultiDaySchedule(
            startTime: Long,
            appRequestNo: String,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    chargeScheduleUseCase
                        .fetchEVVehicleRealTimeStatusForMultiDaySchedule(
                            isChargeManagementFlow = isChargeManagementFlow,
                            appRequestNo = appRequestNo,
                            ecoStartAndEndTime = ecoStartAndEndTime,
                            vehicleInfo = vehicle,
                        ).flowOn(dispatcherProvider.io())
                        .collectAndRetry(
                            startTime = startTime,
                            onRetry = {
                                fetchRealTimeChargeInfoForMultiDaySchedule(startTime, appRequestNo)
                            },
                            onTimeout = {
                                toastMessageRes = R.string.api_failure
                            },
                        ) { multidayScheduleUIModel ->
                            when (multidayScheduleUIModel) {
                                is MultiDayScheduleModel.ListMultiDayScheduleModel -> {
                                    sharedDataSource.saveRealTimeElectricStatus(
                                        multidayScheduleUIModel.response,
                                    )
                                    ecoScheduleObj = multidayScheduleUIModel.ecoScheduleObj
                                    state.update {
                                        it.copy(
                                            viewState =
                                                ChargeScheduleState.SchedulingList(
                                                    vehicle.brand,
                                                    multidayScheduleUIModel,
                                                ),
                                        )
                                    }
                                }

                                is MultiDayScheduleModel.EmptyMultiDaySchedule -> {
                                    sharedDataSource.saveRealTimeElectricStatus(
                                        multidayScheduleUIModel.response,
                                    )
                                    state.update {
                                        it.copy(
                                            viewState =
                                                ChargeScheduleState.NoSchedule(
                                                    vehicle.brand,
                                                    true,
                                                    multidayScheduleUIModel.message,
                                                ),
                                        )
                                    }
                                }

                                else -> { // left intentionally blank
                                }
                            }
                        }
                }
            }
        }

        private fun fetchRealTimeChargeInfoForPHEV(
            startTime: Long,
            appRequestNo: String,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    chargeScheduleUseCase
                        .fetchEVVehicleRealTimeStatusForPHEV(
                            isChargeManagementFlow = isChargeManagementFlow,
                            appRequestNo = appRequestNo,
                            vehicleInfo = vehicle,
                        ).flowOn(dispatcherProvider.io())
                        .collectAndRetry(
                            startTime = startTime,
                            onRetry = {
                                fetchRealTimeChargeInfoForPHEV(startTime, appRequestNo)
                            },
                            onTimeout = {
                                state.update {
                                    it.copy(
                                        showProgress = false,
                                    )
                                }
                                toastMessageRes = R.string.phev_realtime_api_error
                            },
                        ) { phevScheduleUIModel ->
                            when (phevScheduleUIModel) {
                                is PHEVScheduleModel.PHEVScheduleSuccessModel -> {
                                    sharedDataSource.saveRealTimeElectricStatus(
                                        phevScheduleUIModel.response,
                                    )
                                    state.update {
                                        it.copy(
                                            showProgress = false,
                                            viewState =
                                                ChargeScheduleState.PHEVScheduleTimer(
                                                    phevScheduleUIModel,
                                                ),
                                        )
                                    }
                                }

                                is PHEVScheduleModel.PHEVScheduleErrorModel -> {
                                    if (isChargeManagementFlow) {
                                        state.update {
                                            it.copy(
                                                showProgress = false,
                                            )
                                        }
                                    } else {
                                        state.update {
                                            it.copy(
                                                showProgress = false,
                                                viewState =
                                                    ChargeScheduleState.NoSchedule(
                                                        vehicle.brand,
                                                        false,
                                                        phevScheduleUIModel.errorMessageRes,
                                                    ),
                                            )
                                        }
                                    }
                                    toastMessageRes = phevScheduleUIModel.errorMessageRes
                                }

                                else -> { // left intentionally blank
                                }
                            }
                        }
                }
            }
        }

        fun saveEcoScheduleDetails(isEcoScheduleEnabled: Boolean) {
            viewModelScope.launch(dispatcherProvider.main()) {
                state.update {
                    it.copy(
                        showProgress = true,
                    )
                }
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    chargeScheduleUseCase
                        .saveEcoScheduleDetails(
                            vin = vehicle.vin,
                            isEcoScheduleEnabled = isEcoScheduleEnabled,
                            settingsId = "",
                        ).flowOn(dispatcherProvider.io())
                        .collect {
                            if (it) {
                                if (isEcoScheduleEnabled) {
                                    createEcoChargingSchedule(isEcoScheduleEnabled)
                                } else {
                                    deleteEcoChargingSchedule()
                                }
                            } else {
                                toastMessage = "Failure"
                            }
                            state.update { it.copy(showProgress = false) }
                        }
                }
            }
        }

        private fun createEcoChargingSchedule(isScheduleEnabled: Boolean) {
            viewModelScope.launch(dispatcherProvider.main()) {
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    chargeScheduleUseCase
                        .createMultiDaySchedule(
                            isScheduleEnabled = isScheduleEnabled,
                            vehicleInfo = vehicle,
                            startAndEndTime = ecoStartAndEndTime,
                            daysOfWeek = ChargeScheduleUtil.daysOfWeekmap.keys.toList(),
                        ).flowOn(dispatcherProvider.io())
                        .collect { result ->
                            if (result.result != null) {
                                refreshUI(result.result)
                            } else {
                                toastMessage = result.errorMessage
                            }
                        }
                }
            }
        }

        private fun deleteEcoChargingSchedule() {
            viewModelScope.launch(dispatcherProvider.main()) {
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    chargeScheduleUseCase
                        .deleteMultiDaySchedule(
                            scheduleId = ecoScheduleObj?.settingsId.orEmpty(),
                            vehicleInfo = vehicle,
                        ).flowOn(dispatcherProvider.io())
                        .collect { result ->
                            if (result.result != null) {
                                refreshUI(result.result)
                            }
                        }
                }
            }
        }

        fun enableOrDisableMultiDaySchedule(
            isScheduleEnabled: Boolean,
            selectedSchedule: TimerChargeInfo,
        ) {
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP.eventName,
                AnalyticsEventParam.VEHICLE_EV_ECO_CHARGE_TOGGLE,
            )
            viewModelScope.launch(dispatcherProvider.main()) {
                state.update {
                    it.copy(
                        viewState = ChargeScheduleState.MultiDayScheduleLoading,
                    )
                }
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    chargeScheduleUseCase
                        .updateMultiDaySchedule(
                            isScheduleEnabled = isScheduleEnabled,
                            timerChargeInfo = selectedSchedule,
                            vehicleInfo = vehicle,
                        ).flowOn(dispatcherProvider.io())
                        .collect { result ->
                            if (result.result != null) {
                                sharedDataSource.resetElectricStatusState()
                                initMultiDaySchedule(
                                    System.currentTimeMillis(),
                                    result.result,
                                    isChargeManagementFlow,
                                    ecoStartAndEndTime,
                                )
                            } else {
                                toastMessage = result.errorMessage
                            }
                        }
                }
            }
        }

        fun refreshUI(appRequestNo: String) {
            viewModelScope.launch(dispatcherProvider.main()) {
                state.update {
                    it.copy(
                        showProgress = false,
                        viewState = ChargeScheduleState.MultiDayScheduleLoading,
                    )
                }
                if (!isChargeManagementFlow) {
                    val ecoScheduleDeffered =
                        async {
                            fetchEcoScheduleDetails()
                        }
                    ecoStartAndEndTime = ecoScheduleDeffered.await()
                }
                sharedDataSource.resetElectricStatusState()
                initMultiDaySchedule(
                    startTime = System.currentTimeMillis(),
                    appRequestNo = appRequestNo,
                    ecoStartAndEndTime = ecoStartAndEndTime,
                    isChargeManagementFlow = isChargeManagementFlow,
                )
            }
        }
    }

data class ChargeScheduleUIState(
    val showProgress: Boolean = false,
    val ecoCardState: EcoSchduleCardState = EcoSchduleCardState.Init,
    val viewState: ChargeScheduleState = ChargeScheduleState.Init,
)

sealed class ChargeScheduleEvents {
    object InitChargeSchedule : ChargeScheduleEvents()

    data class InitMultiDaySchedule(
        val isChargeManagementFlow: Boolean,
    ) : ChargeScheduleEvents()

    data class InitPHEVSchedule(
        val isChargeManagementFlow: Boolean,
    ) : ChargeScheduleEvents()

    data class OnLogEvent(
        val group: String,
        val event: String,
    ) : ChargeScheduleEvents()
}
