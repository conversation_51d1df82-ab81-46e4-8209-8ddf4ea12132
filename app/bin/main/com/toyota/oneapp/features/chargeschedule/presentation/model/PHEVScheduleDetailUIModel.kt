/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeschedule.presentation.model

import androidx.annotation.StringRes
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.Time
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.CONST_COLON
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.CONST_SPACE
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeInfo
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeTimerRequest
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.Reservation

sealed class PHEVScheduleDetailUIModel(
    @StringRes val title: Int = 0,
    @StringRes val subTitle: Int = 0,
    var timeObj: TimeObj? = null,
    var selectedDay: String = "",
    var daysOfWeek: List<DaysOfWeekObj>? = null,
    var isACEnabled: Boolean? = null,
) {
    object Init : PHEVScheduleDetailUIModel()

    data class PHEVStartTimeDetailUIModel(
        @StringRes val startTitle: Int,
        @StringRes val startSubTitle: Int,
        var startTime: TimeObj,
        var startSelectedDay: String,
        var startDaysOfWeek: List<DaysOfWeekObj>,
    ) : PHEVScheduleDetailUIModel(
            startTitle,
            startSubTitle,
            startTime,
            startSelectedDay,
            startDaysOfWeek,
            null,
        )

    data class PHEVDepartureTimeDetailUIModel(
        @StringRes val endTitle: Int,
        @StringRes val endSubTitle: Int,
        var endTime: TimeObj,
        var endSelectedDay: String,
        var endDaysOfWeek: List<DaysOfWeekObj>,
        var isClimateEnabled: Boolean,
    ) : PHEVScheduleDetailUIModel(
            endTitle,
            endSubTitle,
            endTime,
            endSelectedDay,
            endDaysOfWeek,
            isClimateEnabled,
        )
}

fun PHEVScheduleDetailUIModel.toChargeTimerRequest(isStartTimeFlow: Boolean): ChargeTimerRequest {
    val reservation =
        Reservation(
            chargeType =
                when {
                    isStartTimeFlow -> "CHARGE_START_TIME"
                    isACEnabled == true -> "CHARGE_PRE_AC_END_TIME"
                    else -> "CHARGE_END_TIME"
                },
            day = selectedDay,
            startTime =
                if (isStartTimeFlow) {
                    Time(
                        hour =
                            timeObj
                                ?.time24HrFormat
                                ?.split(CONST_COLON)
                                ?.first()
                                ?.toInt() ?: 0,
                        minute =
                            timeObj
                                ?.time24HrFormat
                                ?.split(CONST_COLON)
                                ?.last()
                                ?.toInt() ?: 0,
                    )
                } else {
                    null
                },
            endTime =
                if (isStartTimeFlow) {
                    null
                } else {
                    Time(
                        hour =
                            timeObj
                                ?.time24HrFormat
                                ?.split(CONST_COLON)
                                ?.first()
                                ?.toInt() ?: 0,
                        minute =
                            timeObj
                                ?.time24HrFormat
                                ?.split(CONST_COLON)
                                ?.last()
                                ?.toInt() ?: 0,
                    )
                },
        )
    return ChargeTimerRequest(
        command = "reserve-charge",
        reservationCharge = reservation,
    )
}

fun ChargeInfo.toPHEVScheduleDetailUIModel(isStartTimeFlow: Boolean): PHEVScheduleDetailUIModel =
    if (isStartTimeFlow) {
        val startTime =
            (ChargeScheduleUtil.convert24HrTimeTo12Hr(chargeStartTime) ?: "12:00 AM").split(
                CONST_SPACE,
            )
        val selectedDayIndex =
            if (chargeType == 1) {
                chargeWeek?.minus(1) ?: 0
            } else {
                0
            }
        PHEVScheduleDetailUIModel.PHEVStartTimeDetailUIModel(
            startTitle = R.string.ev_climate_schedule_start_time,
            startSubTitle = R.string.phev_start_time_description,
            startTime =
                TimeObj(
                    time24HrFormat = chargeStartTime ?: "12:00",
                    hour = startTime.first().split(CONST_COLON).first(),
                    minute = startTime.first().split(CONST_COLON).last(),
                    ampm = startTime.last().uppercase(),
                ),
            startSelectedDay = ChargeScheduleUtil.getSelectedDayFromIndex(selectedDayIndex),
            startDaysOfWeek = ChargeScheduleUtil.getDaysOfWeekForPHEVSchedule(selectedDayIndex),
        )
    } else {
        val endTime =
            (ChargeScheduleUtil.convert24HrTimeTo12Hr(chargeEndTime) ?: "12:00 AM").split(
                CONST_SPACE,
            )
        val selectedDayIndex =
            if (chargeType == 2 || chargeType == 3) {
                chargeWeek?.minus(1) ?: 0
            } else {
                0
            }
        PHEVScheduleDetailUIModel.PHEVDepartureTimeDetailUIModel(
            endTitle = R.string.ev_depart_time,
            endSubTitle = R.string.phev_depart_time_description,
            endTime =
                TimeObj(
                    time24HrFormat = chargeEndTime ?: "12:00",
                    hour = endTime.first().split(CONST_COLON).first(),
                    minute = endTime.first().split(CONST_COLON).last(),
                    ampm = endTime.last().uppercase(),
                ),
            endSelectedDay = ChargeScheduleUtil.getSelectedDayFromIndex(selectedDayIndex),
            endDaysOfWeek = ChargeScheduleUtil.getDaysOfWeekForPHEVSchedule(selectedDayIndex),
            isClimateEnabled = chargeType == 3,
        )
    }
