package com.toyota.oneapp.features.chargeschedule.presentation

import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeassist.view.screens.ManualScheduleCardParamsForCAToggle
import com.toyota.oneapp.features.chargemanagement.presentation.EmptyChargeManagementScreen
import com.toyota.oneapp.features.chargemanagement.presentation.navigation.navigateToChargeManagementMultiDaySchedule
import com.toyota.oneapp.features.chargeschedule.application.ChargeScheduleState
import com.toyota.oneapp.features.chargeschedule.domain.model.ChargingTimeInfo
import com.toyota.oneapp.features.chargeschedule.domain.model.MultiDayScheduleModel
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.ChargeScheduleRoute
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.navigateToMultiDaySchedule
import com.toyota.oneapp.features.chargeschedule.presentation.previewprovider.MultiDayScheduleModelProvider
import com.toyota.oneapp.features.chargeschedule.presentation.widgets.RefreshLayout
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.TimerChargeInfo
import com.toyota.oneapp.features.core.composable.CustomSwitch
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.shimmerEffect
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.util.ToyUtil

@Composable
fun MultiDayScheduleShimmer(modifier: Modifier = Modifier) {
    val list = (1..3).map { it.toString() }
    Column(
        modifier =
            modifier
                .fillMaxSize(),
    ) {
        list.forEach {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(100.dp)
                        .padding(vertical = 8.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .shimmerEffect(),
            )
        }
    }
}

@Composable
fun MultiDayScheduleScreen(
    navController: NavHostController,
    uiModel: MultiDayScheduleModel.ListMultiDayScheduleModel,
    modifier: Modifier = Modifier,
    viewModel: ChargeScheduleViewModel = hiltViewModel(),
    manualScheduleCardParamsForCAToggle: ManualScheduleCardParamsForCAToggle? = null,
) {
    if (uiModel.chargingTimeInfoList.isEmpty()) {
        NoScheduleWidget(isMultiDaySchedule = true) {
            navController.navigate(ChargeScheduleRoute.EditMultidayScheduleScreen.route)
        }
        return
    }

    MultiDayScheduleContent(
        uiModel = uiModel,
        onEnableOrDisableSchedule = { isEnabled, infoModel ->
            viewModel.enableOrDisableMultiDaySchedule(
                isScheduleEnabled = isEnabled,
                selectedSchedule = infoModel.responseObj,
            )
        },
        onEditSchedule = { _, item ->
            navController.navigateToMultiDaySchedule(item)
        },
        onRefresh = { viewModel.refreshLastUpdatedData(isMultiDayRefresh = true) },
        modifier = modifier,
        onCreateSchedule = { navController.navigateToMultiDaySchedule(null) },
        manualScheduleCardParamsForCAToggle = manualScheduleCardParamsForCAToggle,
    )
}

@Composable
fun ChargeManagementMultiDayScheduleScreen(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    viewModel: ChargeScheduleViewModel = hiltViewModel(),
    onScheduleListCount: (count: Int) -> Unit,
) {
    val uiState by viewModel.uiState.collectAsState()
    LaunchedEffect(uiState.viewState) {
        if (uiState.viewState is ChargeScheduleState.Init) {
            viewModel.onEvent(ChargeScheduleEvents.InitMultiDaySchedule(true))
        }
    }

    when (uiState.viewState) {
        is ChargeScheduleState.Init -> {
            // don't do anything
        }
        is ChargeScheduleState.MultiDayScheduleLoading -> {
            MultiDayScheduleShimmer(modifier = modifier)
        }
        is ChargeScheduleState.SchedulingList -> {
            val viewState = uiState.viewState as ChargeScheduleState.SchedulingList
            onScheduleListCount(viewState.uiModel.chargingTimeInfoList.size)
            if (viewState.uiModel.chargingTimeInfoList.isEmpty()) {
                EmptyChargeManagementScreen(
                    vehicleBrand = viewState.vehicleBrand,
                    title = stringResource(R.string.no_multiday_schedules_title),
                    description = stringResource(R.string.no_multiday_schedules_subtitle),
                    modifier = modifier,
                )
                return
            }
            ChargeManagementMultiDayScheduleContent(
                uiModel = viewState.uiModel,
                onEnableOrDisableSchedule = { isEnabled, infoModel ->
                    viewModel.enableOrDisableMultiDaySchedule(
                        isScheduleEnabled = isEnabled,
                        selectedSchedule = infoModel.responseObj,
                    )
                },
                onEditSchedule = { isOffPeakSchedule, item ->
                    navController.navigateToChargeManagementMultiDaySchedule(
                        isOffPeakSchedule,
                        item,
                    )
                },
                onRefresh = { viewModel.refreshLastUpdatedData(isMultiDayRefresh = true) },
            )
        }
        is ChargeScheduleState.NoSchedule -> {
            EmptyChargeManagementScreen(
                vehicleBrand = (uiState.viewState as ChargeScheduleState.NoSchedule).vehicleBrand,
                title = stringResource(R.string.no_multiday_schedules_title),
                description = stringResource(R.string.no_multiday_schedules_subtitle),
            )
        }
        else -> {
            // don't do anything
        }
    }
}

@Composable
private fun MultiDayScheduleContent(
    uiModel: MultiDayScheduleModel.ListMultiDayScheduleModel,
    modifier: Modifier = Modifier,
    onEnableOrDisableSchedule: (isEnabled: Boolean, infoModel: ChargingTimeInfo) -> Unit,
    onEditSchedule: (isOffPeakSchedule: Boolean, item: TimerChargeInfo) -> Unit,
    onRefresh: () -> Unit,
    onCreateSchedule: () -> Unit,
    manualScheduleCardParamsForCAToggle: ManualScheduleCardParamsForCAToggle? = null,
) {
    Column(
        modifier =
            modifier
                .fillMaxSize()
                .padding(vertical = 16.dp),
    ) {
        LazyColumn(
            modifier =
                modifier
                    .fillMaxSize()
                    .weight(1f, fill = false),
        ) {
            items(uiModel.chargingTimeInfoList) { timerUIModel ->
                ScheduleItem(
                    isChargeManagementFlow = false,
                    uiModel = timerUIModel,
                    onToggleSchedule = {
                        onEnableOrDisableSchedule(it, timerUIModel)
                    },
                    onClick = { isOffPeakSchedule, editUIModel ->
                        onEditSchedule(isOffPeakSchedule, editUIModel)
                    },
                    manualScheduleCardParamsForCAToggle = manualScheduleCardParamsForCAToggle,
                )
            }

            item {
                Column(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp),
                ) {
                    RefreshLayout(
                        lastUpdatedInfo = uiModel.lastUpdated,
                        modifier =
                            Modifier
                                .align(Alignment.CenterHorizontally)
                                .testTagID(AccessibilityId.ID_MULTIDAY_REFRESH_BUTTON),
                    ) {
                        onRefresh()
                    }
                }
            }
        }

        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(top = 8.dp, bottom = 8.dp),
        ) {
            if (manualScheduleCardParamsForCAToggle?.isEnabled != false) {
                PrimaryButton02(
                    text = stringResource(id = R.string.create_schedule),
                    modifier =
                        Modifier
                            .align(Alignment.CenterHorizontally)
                            .testTagID(AccessibilityId.ID_MULTIDAY_CREATE_SCHEDULE_BTN),
                ) {
                    onCreateSchedule()
                }
            }
        }
    }
}

@Composable
fun ChargeManagementMultiDayScheduleContent(
    uiModel: MultiDayScheduleModel.ListMultiDayScheduleModel,
    modifier: Modifier = Modifier,
    onEnableOrDisableSchedule: (isEnabled: Boolean, infoModel: ChargingTimeInfo) -> Unit,
    onEditSchedule: (isOffPeakSchedule: Boolean, item: TimerChargeInfo) -> Unit,
    onRefresh: () -> Unit,
    manualScheduleCardParamsForCAToggle: ManualScheduleCardParamsForCAToggle? = null,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp),
    ) {
        uiModel.chargingTimeInfoList.forEach { timerUIModel ->
            ScheduleItem(
                isChargeManagementFlow = true,
                uiModel = timerUIModel,
                onToggleSchedule = {
                    onEnableOrDisableSchedule(it, timerUIModel)
                },
                onClick = { isOffPeakSchedule, editUIModel ->
                    onEditSchedule(isOffPeakSchedule, editUIModel)
                },
                manualScheduleCardParamsForCAToggle = manualScheduleCardParamsForCAToggle,
            )
        }

        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
        ) {
            RefreshLayout(
                lastUpdatedInfo = uiModel.lastUpdated,
                modifier =
                    Modifier
                        .align(Alignment.CenterHorizontally)
                        .testTagID(AccessibilityId.ID_MULTIDAY_REFRESH_BUTTON),
            ) {
                onRefresh()
            }
        }
    }
}

@Composable
private fun ScheduleItem(
    isChargeManagementFlow: Boolean,
    uiModel: ChargingTimeInfo,
    modifier: Modifier = Modifier,
    onToggleSchedule: (isEnabled: Boolean) -> Unit,
    onClick: (isOffPeakSchedule: Boolean, TimerChargeInfo) -> Unit,
    manualScheduleCardParamsForCAToggle: ManualScheduleCardParamsForCAToggle? = null,
) {
    val context = LocalContext.current
    Card(
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tile02,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(vertical = 8.dp)
                .testTagID(AccessibilityId.ID_MULTIDAY_ITEM_CARD),
    ) {
        Box(
            modifier =
                modifier
                    .clickable { onClick(uiModel.isOffPeekSchedule, uiModel.responseObj) }
                    .fillMaxWidth()
                    .padding(16.dp),
        ) {
            Column(
                modifier =
                    Modifier
                        .wrapContentSize()
                        .align(Alignment.CenterStart),
            ) {
                OASubHeadLine1TextView(
                    text = isChargeManagementFlow.scheduleTitle(context, uiModel),
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .testTagID(AccessibilityId.ID_MULTIDAY_ITEM_TITLE_TEXT),
                )

                Spacer(modifier = Modifier.height(4.dp))

                OACallOut1TextView(
                    text = isChargeManagementFlow.scheduleSubTitle(context, uiModel),
                    color = AppTheme.colors.tertiary05,
                    modifier =
                        Modifier
                            .testTagID(AccessibilityId.ID_MULTIDAY_ITEM_SUBTITLE_TEXT),
                )
            }

            CustomSwitch(
                onCheckedChange = {
                    onToggleSchedule(it)
                },
                isEnabled = uiModel.isEnabled,
                isClickable = ToyUtil.isSubaru() || manualScheduleCardParamsForCAToggle?.isEnabled ?: true,
                testTagId = AccessibilityId.ID_MULTIDAY_ITEM_SWITCH,
                shouldEnableCADisableColor = ToyUtil.isNotSubaru(),
                modifier =
                    Modifier
                        .align(Alignment.CenterEnd),
            )
        }
    }
}

private fun Boolean.scheduleTitle(
    context: Context,
    uiModel: ChargingTimeInfo,
): String =
    when {
        this && uiModel.isOffPeekSchedule -> {
            context.getString(R.string.off_peak_schedule)
        }
        this -> {
            context.getString(R.string.other_schedule, uiModel.timeRange)
        }
        else -> {
            uiModel.timeRange
        }
    }

private fun Boolean.scheduleSubTitle(
    context: Context,
    uiModel: ChargingTimeInfo,
): String =
    if (this && uiModel.isOffPeekSchedule) {
        context.getString(R.string.off_peak_time_range)
    } else {
        uiModel.dayOfWeek.getDaysOfCharging(context)
    }

private fun List<Int>.getDaysOfCharging(context: Context): String =
    if (this.size == 1) {
        context.getString(this.first())
    } else {
        val daysOfWeek: List<String> = this.map { context.getString(it).substring(0, 3) }
        daysOfWeek.joinToString()
    }

@Preview
@Composable
fun MultiDayScheduleContentPreview(
    @PreviewParameter(MultiDayScheduleModelProvider::class) uiModel: MultiDayScheduleModel.ListMultiDayScheduleModel,
) {
    MultiDayScheduleContent(
        uiModel = uiModel,
        onEnableOrDisableSchedule = { _, _ -> },
        onEditSchedule = { _, _ -> },
        onRefresh = {},
        onCreateSchedule = {},
    )
}

@Preview
@Composable
fun NoMultiDaySchedulePreview() {
    NoScheduleWidget(true) { }
}
