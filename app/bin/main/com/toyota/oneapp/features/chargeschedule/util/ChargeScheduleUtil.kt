package com.toyota.oneapp.features.chargeschedule.util

import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeschedule.presentation.model.DaysOfWeekObj
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import toyotaone.commonlib.log.LogTool
import java.text.SimpleDateFormat

object ChargeScheduleUtil {
    private const val SATURDAY = "Saturday"
    private const val SUNDAY = "Sunday"
    private const val MONDAY = "Monday"
    private const val TUESDAY = "Tuesday"
    private const val WEDNESDAY = "Wednesday"
    private const val THURSDAY = "Thursday"
    private const val FRIDAY = "Friday"
    const val CONST_COLON = ":"
    const val CONST_SPACE = " "
    const val CONST_AM = "AM"
    const val CONST_PM = "PM"
    const val OFF_PEAK_START_TIME = "11:00 pm"
    const val OFF_PEAK_END_TIME = "4:00 am"
    private const val ZERO_HOUR = "00:00"
    private const val TWELVE_HOUR = "12:00"

    val daysOfWeekmap =
        mapOf(
            SUNDAY to R.string.sunday,
            MONDAY to R.string.monday,
            TUESDAY to R.string.tuesday,
            WEDNESDAY to R.string.wednesday,
            THURSDAY to R.string.thursday,
            FRIDAY to R.string.friday,
            SATURDAY to R.string.saturday,
        )
    val daysOfWeekWeekEndmap =
        mapOf(
            MONDAY to R.string.monday,
            TUESDAY to R.string.tuesday,
            WEDNESDAY to R.string.wednesday,
            THURSDAY to R.string.thursday,
            FRIDAY to R.string.friday,
            SATURDAY to R.string.saturday,
            SUNDAY to R.string.sunday,
        )

    fun convert24HrTimeTo12Hr(
        time: String?,
        canShowMinutes: Boolean = true,
    ): String? {
        try {
            if (time?.isNotEmpty() == true) {
                val twentyFourSDF = SimpleDateFormat("HH:mm")
                val twelveHourSDF =
                    if (canShowMinutes) {
                        SimpleDateFormat("h:mm a")
                    } else {
                        SimpleDateFormat("h a")
                    }
                val twentyFourHourTime = twentyFourSDF.parse(time)

                return twentyFourHourTime?.let { twelveHourSDF.format(it) }
            }
        } catch (ex: IllegalArgumentException) {
            LogTool.e(OADashboardActivity::class.simpleName, ex.message)
        }
        return null
    }

    fun convert12HrTimeTo24Hr(
        time: String,
        isAM: Boolean,
    ): String {
        val hour = time.split(":").first().toInt()
        val minute = time.split(":").last().toInt()
        var adjustedHour = if (isAM) hour else hour + 12
        if (adjustedHour == 24) adjustedHour = 12
        if (adjustedHour == 12 && !isAM) adjustedHour = 0

        return "$adjustedHour:$minute"
    }

    fun convertToHHmm(time: String?): String? =
        try {
            time?.let {
                val twentyFourSDF = SimpleDateFormat("h:mm")
                val twelveHourSDF = SimpleDateFormat("HH:mm")
                val twentyFourHourTime = twentyFourSDF.parse(time)

                twentyFourHourTime?.let { twelveHourSDF.format(it) }
            }
        } catch (ex: IllegalArgumentException) {
            LogTool.e(OADashboardActivity::class.simpleName, ex.message)
            null
        }

    fun getDaysOfWeek(selectedDays: List<String>): List<Int> {
        val displayDays = arrayListOf<Int>()
        when {
            selectedDays.size == 7 -> {
                displayDays.add(R.string.everyday)
            }
            weekendOnly(selectedDays) -> {
                displayDays.add(R.string.weekend)
            }
            selectedDays.size == 5 && !anyDuringWeekend(selectedDays) -> {
                displayDays.add(R.string.weekdays)
            }
            else -> {
                selectedDays.forEach { dayOfWeek ->
                    displayDays.add(daysOfWeekmap[dayOfWeek] ?: 0)
                }
            }
        }
        return displayDays
    }

    private fun weekendOnly(selectedDays: List<String>): Boolean =
        selectedDays.size == 2 &&
            selectedDays.contains(SATURDAY) &&
            selectedDays.contains(SUNDAY)

    private fun anyDuringWeekend(selectedDays: List<String>): Boolean {
        return selectedDays.contains(SATURDAY) ||
            selectedDays.contains(SUNDAY)
    }

    fun hasOffPeekSchedule(
        selectedDays: List<String>?,
        ecoStartAndEndTime: List<String>,
        scheduleStartTime: String?,
        scheduleEndTime: String?,
    ): Boolean {
        val ecoStartTime = convertToHHmm(ecoStartAndEndTime.firstOrNull())?.replace(ZERO_HOUR, TWELVE_HOUR)
        val ecoEndTime = convertToHHmm(ecoStartAndEndTime.lastOrNull())?.replace(ZERO_HOUR, ZERO_HOUR)
        return selectedDays?.size == 7 &&
            ecoStartTime == scheduleStartTime &&
            ecoEndTime == scheduleEndTime
    }

    /*
     * A schedule is off-peak schedule if all days of the week is selected and
     * selected time is between 11:00PM and 4:00AM
     * */
    fun isOffPeekSchedule(
        selectedDays: List<String>?,
        scheduleStartTime: String?,
        scheduleEndTime: String?,
    ): Boolean {
        val startHour = scheduleStartTime?.split(":")?.first()
        val startMin = scheduleStartTime?.split(":")?.last()
        val endHour = scheduleEndTime?.split(":")?.first()
        val endMin = scheduleEndTime?.split(":")?.last()

        return selectedDays?.size == 7 && startHour == "23" && startMin == "00" && endHour == "04" && endMin == "00"
    }

    fun getDaysOfWeekForEditSchedule(selectedDays: List<String>): List<DaysOfWeekObj> {
        val list = ArrayList<DaysOfWeekObj>()
        daysOfWeekmap.keys.forEachIndexed { id, dayName ->
            list.add(
                DaysOfWeekObj(
                    dayId = id,
                    dayName = dayName,
                    dayNameRes = daysOfWeekmap.get(dayName) ?: 0,
                    isDaySelected = selectedDays.contains(dayName),
                ),
            )
        }
        return list
    }

    fun getDaysOfWeekForPHEVSchedule(selectedDayIndex: Int): List<DaysOfWeekObj> {
        val list = ArrayList<DaysOfWeekObj>()

        daysOfWeekWeekEndmap.keys.forEachIndexed { id, dayName ->
            list.add(
                DaysOfWeekObj(
                    dayId = id,
                    dayName = dayName,
                    dayNameRes = daysOfWeekmap.get(dayName) ?: 0,
                    isDaySelected = selectedDayIndex == id,
                ),
            )
        }
        return list
    }

    fun getSelectedDayFromIndex(index: Int): String = daysOfWeekWeekEndmap.keys.toList()[index]

    fun getOffPeakDefaultDaysOfWeeks(): List<DaysOfWeekObj> {
        val list = ArrayList<DaysOfWeekObj>()
        daysOfWeekWeekEndmap.keys.forEachIndexed { id, dayName ->
            list.add(
                DaysOfWeekObj(
                    dayId = id,
                    dayName = dayName,
                    dayNameRes = daysOfWeekmap.get(dayName) ?: 0,
                    isDaySelected = true,
                ),
            )
        }
        return list
    }

    fun getOffPeakDefaultSelectedWeeks(): List<String> = daysOfWeekWeekEndmap.keys.toList()
}
