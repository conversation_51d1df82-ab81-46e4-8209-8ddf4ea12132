/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeschedule.presentation.model

import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.CONST_AM
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.CONST_COLON
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.CONST_PM
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.CONST_SPACE
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.OFF_PEAK_END_TIME
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.OFF_PEAK_START_TIME
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.TimerChargeInfo

data class EditScheduleUIModel(
    val isOffPeakScheduleEnabled: Boolean = false,
    var isUpdateScheduleFlow: Boolean? = false,
    var scheduleId: String? = null,
    var isScheduleEnabled: Boolean? = false,
    var startTime: TimeObj? = null,
    var endTime: TimeObj? = null,
    val isEndTimeEnabled: Boolean? = null,
    var selectedDaysOfWeek: List<String>? = null,
    var daysOfWeek: List<DaysOfWeekObj>? = null,
)

fun EditScheduleUIModel.toTimerChargeInfo(): TimerChargeInfo =
    TimerChargeInfo(
        settingId = scheduleId,
        enabled = isScheduleEnabled,
        startTime = startTime?.time24HrFormat,
        endTime =
            if (isEndTimeEnabled == true) {
                endTime?.time24HrFormat
            } else {
                null
            },
        daysOfTheWeek = selectedDaysOfWeek,
        nextSettingId = null,
    )

fun TimerChargeInfo.toEditScheduleUIModel(): EditScheduleUIModel {
    val startTime =
        (ChargeScheduleUtil.convert24HrTimeTo12Hr(this.startTime) ?: "12:00 am").split(
            CONST_SPACE,
        )
    val endTime =
        (ChargeScheduleUtil.convert24HrTimeTo12Hr(this.endTime) ?: "2:00 pm").split(
            CONST_SPACE,
        )
    return EditScheduleUIModel(
        isUpdateScheduleFlow = true,
        scheduleId = this.settingId.orEmpty(),
        isScheduleEnabled = this.enabled ?: false,
        startTime =
            TimeObj(
                time24HrFormat = this.startTime.orEmpty(),
                hour = startTime.first().split(CONST_COLON).first(),
                minute = startTime.first().split(CONST_COLON).last(),
                ampm = startTime.last(),
            ),
        endTime =
            TimeObj(
                time24HrFormat = this.endTime.orEmpty(),
                hour = endTime.first().split(CONST_COLON).first(),
                minute = endTime.first().split(CONST_COLON).last(),
                ampm = endTime.last(),
            ),
        isEndTimeEnabled = this.endTime != null,
        selectedDaysOfWeek = this.daysOfTheWeek ?: emptyList(),
        daysOfWeek =
            ChargeScheduleUtil.getDaysOfWeekForEditSchedule(
                this.daysOfTheWeek ?: emptyList(),
            ),
    )
}

fun toCreateScheduleUIModel(): EditScheduleUIModel =
    EditScheduleUIModel(
        isUpdateScheduleFlow = false,
        scheduleId = "",
        isScheduleEnabled = true,
        startTime =
            TimeObj(
                time24HrFormat = "00:00",
                hour = "12",
                minute = "00",
                ampm = CONST_AM,
            ),
        endTime =
            TimeObj(
                time24HrFormat = "14:00",
                hour = "2",
                minute = "00",
                ampm = CONST_PM,
            ),
        isEndTimeEnabled = false,
        selectedDaysOfWeek = emptyList(),
        daysOfWeek = ChargeScheduleUtil.getDaysOfWeekForEditSchedule(emptyList()),
    )

fun EditScheduleUIModel.toOffPeakScheduleUIModel(): EditScheduleUIModel {
    val startTime = OFF_PEAK_START_TIME.split(CONST_SPACE)
    val endTime = OFF_PEAK_END_TIME.split(CONST_SPACE)

    return EditScheduleUIModel(
        isOffPeakScheduleEnabled = true,
        isUpdateScheduleFlow = this.isUpdateScheduleFlow,
        scheduleId = this.scheduleId.orEmpty(),
        isScheduleEnabled = this.isScheduleEnabled ?: false,
        startTime =
            TimeObj(
                time24HrFormat = "23:00",
                hour = startTime.first().split(CONST_COLON).first(),
                minute = startTime.first().split(CONST_COLON).last(),
                ampm = CONST_PM,
            ),
        endTime =
            TimeObj(
                time24HrFormat = "4:00",
                hour = endTime.first().split(CONST_COLON).first(),
                minute = endTime.first().split(CONST_COLON).last(),
                ampm = CONST_AM,
            ),
        isEndTimeEnabled = true,
        selectedDaysOfWeek = ChargeScheduleUtil.getOffPeakDefaultSelectedWeeks(),
        daysOfWeek = ChargeScheduleUtil.getOffPeakDefaultDaysOfWeeks(),
    )
}
