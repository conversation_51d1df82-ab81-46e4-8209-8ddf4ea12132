package com.toyota.oneapp.features.chargeschedule.presentation.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeschedule.presentation.model.TimeObj
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil
import com.toyota.oneapp.features.core.composable.OABody2TextView
import com.toyota.oneapp.features.core.composable.OACaption1TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId

@Composable
fun RefreshLayout(
    lastUpdatedInfo: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    val translatedString =
        lastUpdatedInfo
            .replace("Today", stringResource(id = R.string.last_updated_today))
            .replace("Yesterday", stringResource(id = R.string.last_updated_yesterday))
            .replace("at", stringResource(id = R.string.VehicleStatus_at))
    Column(
        modifier =
            modifier
                .wrapContentSize(),
    ) {
        Box(
            modifier =
                Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(AppTheme.colors.button02d)
                    .align(Alignment.CenterHorizontally)
                    .clickable {
                        onClick()
                    },
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_refresh),
                contentDescription = stringResource(id = R.string.tap_to_refresh),
                modifier =
                    Modifier
                        .size(20.dp)
                        .align(Alignment.Center),
                tint = AppTheme.colors.button02a,
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        OACaption1TextView(
            text = stringResource(id = R.string.VehicleStatus_last_updated, translatedString),
            color = AppTheme.colors.tertiary05,
            modifier =
                Modifier
                    .testTagID(AccessibilityId.ID_LAST_UPDATED_TEXT),
        )
    }
}

@Composable
fun AMPMWidget(
    timeObj: TimeObj?,
    isAMSelected: Boolean,
    modifier: Modifier = Modifier,
    isOffPeakHours: Boolean = false,
    onSelected: (Boolean, String) -> Unit,
) {
    Row(
        modifier =
            modifier
                .padding(horizontal = 12.dp),
    ) {
        TimeButton(
            name = stringResource(id = R.string.ev_am).lowercase(),
            testTagId = AccessibilityId.ID_AM_BTN,
            backgroundColor =
                if (isAMSelected) {
                    AppTheme.colors.tertiary03
                } else {
                    Color.Transparent
                },
            nameColor =
                if (isAMSelected) {
                    AppTheme.colors.button05b
                } else {
                    AppTheme.colors.tertiary00
                },
        ) {
            if (!isOffPeakHours) {
                val time24HrFormat =
                    ChargeScheduleUtil.convert12HrTimeTo24Hr(
                        time = "${timeObj?.hour}:${timeObj?.minute}",
                        isAM = true,
                    )
                onSelected(true, time24HrFormat)
            }
        }

        Spacer(modifier = Modifier.width(8.dp))

        TimeButton(
            name = stringResource(id = R.string.ev_pm).lowercase(),
            testTagId = AccessibilityId.ID_PM_BTN,
            backgroundColor =
                if (isAMSelected) {
                    Color.Transparent
                } else {
                    AppTheme.colors.tertiary03
                },
            nameColor =
                if (isAMSelected) {
                    AppTheme.colors.tertiary00
                } else {
                    AppTheme.colors.button05b
                },
        ) {
            if (!isOffPeakHours) {
                val time24HrFormat =
                    ChargeScheduleUtil.convert12HrTimeTo24Hr(
                        time = "${timeObj?.hour}:${timeObj?.minute}",
                        isAM = false,
                    )
                onSelected(false, time24HrFormat)
            }
        }
    }
}

@Composable
fun TimeButton(
    name: String,
    testTagId: String,
    backgroundColor: Color,
    nameColor: Color,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Surface(
        elevation = 0.dp,
        shape = RoundedCornerShape(32.dp),
        color = backgroundColor,
        modifier =
            modifier
                .wrapContentSize()
                .clickable { onClick() }
                .testTagID(testTagId),
    ) {
        OABody2TextView(
            text = name,
            color = nameColor,
            modifier =
                Modifier
                    .padding(horizontal = 16.dp, vertical = 8.dp),
        )
    }
}

@Composable
private fun Boolean.backgroundColor(): Pair<Color, Color> =
    if (this) {
        Pair(AppTheme.colors.tertiary03, Color.Transparent)
    } else {
        Pair(Color.Transparent, AppTheme.colors.tertiary03)
    }

@Composable
private fun Boolean.labelColor(): Pair<Color, Color> =
    if (this) {
        Pair(AppTheme.colors.button05b, AppTheme.colors.tertiary00)
    } else {
        Pair(AppTheme.colors.tertiary00, AppTheme.colors.button05b)
    }
