package com.toyota.oneapp.features.chargeschedule.domain.model

import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil.CONST_SPACE
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.Payload
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.TimerChargeInfo
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.lastUpdatedDate

sealed class MultiDayScheduleModel(
    val response: ElectricStatusResponse?,
) {
    data class ListMultiDayScheduleModel(
        val listScheduleResponse: ElectricStatusResponse?,
        val maxSchedulingLimit: Int,
        val ecoScheduleObj: ChargingTimeInfo?,
        val chargingTimeInfoList: List<ChargingTimeInfo>,
        val lastUpdated: String,
    ) : MultiDayScheduleModel(listScheduleResponse)

    data class EmptyMultiDaySchedule(
        val emptyScheduleresponse: ElectricStatusResponse?,
        val message: Int?,
    ) : MultiDayScheduleModel(emptyScheduleresponse)
}

data class ChargingTimeInfo(
    val settingsId: String,
    val timeRange: String,
    val dayOfWeek: List<Int>,
    val isOffPeekSchedule: Boolean,
    val isEnabled: Boolean,
    val isEcoSchedule: Boolean,
    val responseObj: TimerChargeInfo,
)

fun Payload.toMultiDayScheduleModel(
    isChargeManagementFlow: Boolean,
    response: ElectricStatusResponse?,
    dateUtil: DateUtil,
    ecoStartAndEndTime: List<String>,
): MultiDayScheduleModel {
    this.vehicleInfo?.apply {
        if (timerChargeInfo != null) {
            val multiDayScheduleList =
                timerChargeInfo.toUIModel(
                    isChargeManagementFlow,
                    ecoStartAndEndTime,
                )
            val ecoSchedule = multiDayScheduleList.filter { it.isEcoSchedule }
            val ecoScheduleObj =
                if (ecoSchedule.isNotEmpty()) {
                    ecoSchedule.first()
                } else {
                    null
                }
            return MultiDayScheduleModel.ListMultiDayScheduleModel(
                listScheduleResponse = response,
                maxSchedulingLimit = maxNoOfChargeSchedules ?: 0,
                ecoScheduleObj = ecoScheduleObj,
                chargingTimeInfoList = multiDayScheduleList.filterNot { it.isEcoSchedule },
                lastUpdated = dateUtil.getDateFromString(acquisitionDatetime)?.lastUpdatedDate().orEmpty(),
            )
        }
    }
    return MultiDayScheduleModel.EmptyMultiDaySchedule(response, null)
}

private fun List<TimerChargeInfo>.toUIModel(
    isChargeManagementFlow: Boolean,
    ecoStartAndEndTime: List<String>,
): List<ChargingTimeInfo> {
    val scheduleList = arrayListOf<ChargingTimeInfo>()
    var hasOffPeakSchedule = false
    this.forEach { timerInfo ->
        val isOffPeekSchedule =
            if (isChargeManagementFlow && !hasOffPeakSchedule) {
                ChargeScheduleUtil.isOffPeekSchedule(
                    timerInfo.daysOfTheWeek,
                    timerInfo.startTime,
                    timerInfo.endTime,
                )
            } else {
                false
            }
        if (!hasOffPeakSchedule) {
            hasOffPeakSchedule = isOffPeekSchedule
        }
        val startTime =
            ChargeScheduleUtil
                .convert24HrTimeTo12Hr(timerInfo.startTime)
                ?.replace(
                    CONST_SPACE,
                    "",
                )?.lowercase()
                .orEmpty()
        val endTime =
            ChargeScheduleUtil.convert24HrTimeTo12Hr(timerInfo.endTime)?.lowercase()?.replace(
                CONST_SPACE,
                "",
            )
        val uiModel =
            ChargingTimeInfo(
                settingsId = timerInfo.settingId.orEmpty(),
                timeRange =
                    if (endTime != null) {
                        "$startTime - $endTime"
                    } else {
                        startTime
                    },
                dayOfWeek = ChargeScheduleUtil.getDaysOfWeek(timerInfo.daysOfTheWeek ?: emptyList()),
                isEnabled = timerInfo.enabled ?: false,
                isEcoSchedule =
                    if (isChargeManagementFlow) {
                        false
                    } else {
                        ChargeScheduleUtil.hasOffPeekSchedule(
                            timerInfo.daysOfTheWeek,
                            ecoStartAndEndTime,
                            timerInfo.startTime,
                            timerInfo.endTime,
                        )
                    },
                isOffPeekSchedule = isOffPeekSchedule,
                responseObj = timerInfo,
            )

        scheduleList.addItem(isOffPeekSchedule, uiModel)
    }
    return scheduleList
}

fun ArrayList<ChargingTimeInfo>.addItem(
    isOffPeakSchedule: Boolean,
    uiModel: ChargingTimeInfo,
) {
    if (isOffPeakSchedule) {
        this.add(0, uiModel)
    } else {
        this.add(uiModel)
    }
}
