package com.toyota.oneapp.features.chargeschedule.application

import com.toyota.oneapp.features.chargeschedule.domain.model.EcoScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.MultiDayScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.PHEVScheduleModel
import com.toyota.oneapp.features.chargeschedule.presentation.model.ScheduleResult
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeTimerRequest
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.TimerChargeInfo
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface ChargeScheduleUseCase {
    fun fetchEcoScheduleDetails(vin: String): Flow<EcoScheduleModel?>

    fun mapToPHEVSceduleModel(
        isChargeManagementFlow: Boolean,
        electricStatusResponse: ElectricStatusResponse?,
    ): PHEVScheduleModel?

    fun fetchEVVechicleInfo(
        isChargeManagementFlow: Boolean,
        vehicleInfo: VehicleInfo,
    ): Flow<PHEVScheduleModel?>

    fun fetchEVVehicleRealTimeStatusForMultiDaySchedule(
        isChargeManagementFlow: Boolean,
        appRequestNo: String,
        ecoStartAndEndTime: List<String>,
        vehicleInfo: VehicleInfo,
    ): Flow<MultiDayScheduleModel?>

    fun fetchEVVehicleRealTimeStatusForPHEV(
        isChargeManagementFlow: Boolean,
        appRequestNo: String,
        vehicleInfo: VehicleInfo,
    ): Flow<PHEVScheduleModel?>

    fun postEVVehicleRealTimeStatus(vehicleInfo: VehicleInfo): Flow<String?>

    fun mapToMultiDayScheduleUIModel(
        isChargeManagementFlow: Boolean,
        electricStatusResponse: ElectricStatusResponse?,
        ecoStartAndEndTime: List<String>,
    ): MultiDayScheduleModel?

    fun fetchMultiDayScheduleStatus(
        isChargeManagementFlow: Boolean,
        appRequestNo: String = "",
        ecoStartAndEndTime: List<String>,
        vehicleInfo: VehicleInfo,
    ): Flow<MultiDayScheduleModel?>

    fun saveEcoScheduleDetails(
        vin: String,
        isEcoScheduleEnabled: Boolean,
        settingsId: String,
    ): Flow<Boolean>

    fun createMultiDaySchedule(
        isScheduleEnabled: Boolean,
        vehicleInfo: VehicleInfo,
        startAndEndTime: List<String?>,
        daysOfWeek: List<String>,
    ): Flow<ScheduleResult<String>>

    fun deleteMultiDaySchedule(
        scheduleId: String,
        vehicleInfo: VehicleInfo,
    ): Flow<ScheduleResult<String>>

    fun updateMultiDaySchedule(
        isScheduleEnabled: Boolean,
        timerChargeInfo: TimerChargeInfo,
        vehicleInfo: VehicleInfo,
    ): Flow<ScheduleResult<String>>

    fun savePHEVSchedule(
        requestModel: ChargeTimerRequest,
        vehicleInfo: VehicleInfo,
    ): Flow<String?>
}
