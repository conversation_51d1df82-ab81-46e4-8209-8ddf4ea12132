/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeschedule.presentation.previewprovider

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.toyota.oneapp.features.chargeschedule.presentation.model.EditScheduleUIModel
import com.toyota.oneapp.features.chargeschedule.presentation.model.TimeObj
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil

class EditScheduleUIModelProvider : PreviewParameterProvider<EditScheduleUIModel> {
    val startTimeObj = TimeObj("11:00", "11", "00", "am")
    val endTimeObj = TimeObj("18:00", "6", "00", "pm")
    val model1 =
        EditScheduleUIModel(
            isUpdateScheduleFlow = true,
            scheduleId = "1",
            isScheduleEnabled = true,
            isEndTimeEnabled = false,
            startTime = startTimeObj,
            endTime = endTimeObj,
            selectedDaysOfWeek = emptyList(),
            daysOfWeek = ChargeScheduleUtil.getDaysOfWeekForEditSchedule(emptyList()),
        )
    val model2 =
        EditScheduleUIModel(
            isUpdateScheduleFlow = true,
            scheduleId = "2",
            isScheduleEnabled = true,
            isEndTimeEnabled = true,
            startTime = startTimeObj,
            endTime = endTimeObj,
            selectedDaysOfWeek = emptyList(),
            daysOfWeek = ChargeScheduleUtil.getDaysOfWeekForEditSchedule(emptyList()),
        )
    val model3 =
        EditScheduleUIModel(
            isUpdateScheduleFlow = false,
            scheduleId = "1",
            isScheduleEnabled = true,
            isEndTimeEnabled = false,
            startTime = startTimeObj,
            endTime = endTimeObj,
            selectedDaysOfWeek = emptyList(),
            daysOfWeek = ChargeScheduleUtil.getDaysOfWeekForEditSchedule(emptyList()),
        )
    override val values =
        sequenceOf(
            model1,
            model2,
            model3,
        )
}
