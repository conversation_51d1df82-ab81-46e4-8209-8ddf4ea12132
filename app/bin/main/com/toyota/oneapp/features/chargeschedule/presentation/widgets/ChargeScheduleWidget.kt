/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.chargeschedule.presentation.widgets

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeassist.view.screens.entrypoint2.ChargeAssistEntryTile
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.chargeinfo.presentation.ChargeInfoViewModel
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoResponse
import com.toyota.oneapp.features.core.composable.OAFootNote1TextView
import com.toyota.oneapp.features.core.composable.OAIconArrowRight
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.dashboard.dashboard.presentation.DashboardViewModel

@Composable
fun ChargeScheduleWidget(
    navController: NavController,
    modifier: Modifier = Modifier,
    viewModel: ChargeInfoViewModel = hiltViewModel(),
    chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel(),
    onClick: () -> Unit,
) {
    val dashboardViewModel: DashboardViewModel = hiltViewModel()
    val profileData by dashboardViewModel.profileData.collectAsState() // if this is null then the api is down
    val isEligibleForCAEnrollment by chargeAssistViewModel.isUserEligibilityForCAEnrollment.collectAsState()

    LaunchedEffect(isEligibleForCAEnrollment) {
        chargeAssistViewModel.isUserEligibleForCAEnrollmentAndEnrollmentStatus()
    }

    ScheduleWidgetCard(
        navController,
        modifier = modifier,
        onClick,
        viewModel,
        isEligibleForCAEnrollment,
        profileData,
        chargeAssistViewModel,
    )
}

@Composable
fun ScheduleWidgetCard(
    navController: NavController,
    modifier: Modifier,
    onClick: () -> Unit,
    viewModel: ChargeInfoViewModel,
    isEligibleForCAEnrolment: Boolean,
    profileData: ProfileInfoResponse?,
    chargeAssistViewModel: ChargeAssistViewModel,
) {
    val systemZip =
        profileData
            ?.payload
            ?.customer
            ?.addresses
            ?.get(0)
            ?.zipCode
    Card(
        backgroundColor = AppTheme.colors.tile03,
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .clickable {
                    onClick()
                },
    ) {
        Column {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier =
                    Modifier
                        .padding(16.dp),
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .weight(1f, fill = false),
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_schedule_watt_time),
                        contentDescription = "",
                        colorFilter = ColorFilter.tint(AppTheme.colors.tertiary00),
                        modifier =
                            Modifier
                                .size(24.dp),
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Column {
                        OASubHeadLine1TextView(
                            text = stringResource(id = R.string.schedule),
                            color = AppTheme.colors.tertiary03,
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        ShowHideScheduleDescription(
                            isEligibleForCAEnrolment,
                            systemZip,
                            chargeAssistViewModel,
                        )
                    }
                }

                Spacer(modifier = Modifier.width(16.dp))
                OAIconArrowRight(iconSize = 18.dp)
            }

            val caBackEndFlagOn by chargeAssistViewModel.isCABackEndFlagOn.collectAsState()

            if (caBackEndFlagOn) {
                ChargeAssistEntryTile(
                    navController,
                    viewModel,
                    chargeAssistViewModel,
                    profileData,
                ) // accessed via ChargeAssist feature module
            }
        }
    }
}

@Composable
fun ShowHideScheduleDescription(
    isEligibleForCAEnrollment: Boolean,
    systemZip: String?,
    chargeAssistViewModel: ChargeAssistViewModel,
) {
    val caBackEndFlagOn by chargeAssistViewModel.isCABackEndFlagOn.collectAsState()

    if (caBackEndFlagOn) {
        /*
            The Schedule description text only shows under these conditions
             - In market by default
             - customer system zip isn’t eligible for charge assist and user system zip is not empty
             - customer enters ineligible zip on ZipCodeNotAvailableScreen Screen after its visited once
         */

        val showDescription by chargeAssistViewModel.shouldShowDescription.collectAsState()
        val currentEnrollmentStatus by chargeAssistViewModel.userEnrollmentStatusDataString.collectAsState()
        val isProfileZipEligible = chargeAssistViewModel.isProfileZipEligible.collectAsState().value

        val systemZipIsNotEmpty = systemZip.isNotNullOrEmpty()

        LaunchedEffect(currentEnrollmentStatus, isEligibleForCAEnrollment, systemZipIsNotEmpty) {
            chargeAssistViewModel.shouldShowDescription(
                currentEnrollmentStatus.toString(),
                isEligibleForCAEnrollment,
                systemZip,
            )
        }

        when (isEligibleForCAEnrollment || isProfileZipEligible) {
            true -> {
                if (showDescription) {
                    OAFootNote1TextView(
                        text =
                            stringResource(
                                id = R.string.eco_charging_description,
                            ),
                        color = AppTheme.colors.tertiary03,
                        textAlign = TextAlign.Start,
                    )
                }
            }

            false -> {
                OAFootNote1TextView(
                    text = stringResource(id = R.string.eco_charging_description),
                    color = AppTheme.colors.tertiary03,
                    textAlign = TextAlign.Start,
                )
            }

            else -> { // left intentionally blank
            }
        }
    } else {
        OAFootNote1TextView(
            text = stringResource(id = R.string.eco_charging_description),
            color = AppTheme.colors.tertiary03,
            textAlign = TextAlign.Start,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ChargeScheduleWidgetPreview(
    @PreviewParameter(OAThemePreviewProvider::class) themeMode: ThemeMode,
) {
    ContentPreview(themeMode) {
        val navController = rememberNavController()
        ChargeScheduleWidget(navController) {}
    }
}
