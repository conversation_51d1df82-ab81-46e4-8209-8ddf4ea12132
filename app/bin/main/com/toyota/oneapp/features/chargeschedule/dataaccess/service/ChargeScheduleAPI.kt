package com.toyota.oneapp.features.chargeschedule.dataaccess.service

import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.CreateScheduleRequest
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.EcoScheduleDetailsResponse
import com.toyota.oneapp.features.chargeschedule.dataaccess.servermodel.UpdateScheduleRequest
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.CommonScheduleResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path

interface ChargeScheduleAPI {
    @GET("/charging/vehicle/eco-schedules")
    suspend fun fetchEcoScheduleDetails(
        @Header("x-vin") vin: String,
    ): Response<EcoScheduleDetailsResponse?>

    @POST("/charging/vehicle/mc-eco-enrollment")
    suspend fun saveEcoScheduleDetails(
        @Header("x-vin") vin: String,
        @Header("x-is-eco-enrollment-enabled") isEcoScheduleEnabled: Boolean,
        @Header("x-setting-id") settingsId: String,
    ): Response<EcoScheduleDetailsResponse?>

    @POST("/oneapi/v1/electric/charging")
    suspend fun createMultiDayChargeSchedule(
        @Body requestBody: CreateScheduleRequest,
        @Header("vin") vin: String,
        @Header("X-GENERATION") generation: String,
        @Header("device-id") deviceId: String,
    ): Response<CommonScheduleResponse?>

    @DELETE("/oneapi/v1/electric/charging/{scheduleId}")
    suspend fun deleteMultiDayChargeSchedule(
        @Path("scheduleId") scheduleId: String,
        @Header("vin") vin: String,
        @Header("X-GENERATION") generation: String,
        @Header("device-id") deviceId: String,
    ): Response<CommonScheduleResponse?>

    @PUT("/oneapi/v1/electric/charging")
    suspend fun updateMultiDayChargeSchedule(
        @Body requestBody: UpdateScheduleRequest,
        @Header("vin") vin: String,
        @Header("X-GENERATION") generation: String,
        @Header("device-id") deviceId: String,
    ): Response<CommonScheduleResponse?>
}
