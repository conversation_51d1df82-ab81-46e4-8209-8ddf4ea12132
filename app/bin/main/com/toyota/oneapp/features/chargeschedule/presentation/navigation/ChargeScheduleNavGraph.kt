/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeschedule.presentation.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import com.toyota.oneapp.features.chargeinfo.navigation.ChargeInfoRoute
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleScreen
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleViewModel
import com.toyota.oneapp.features.chargeschedule.presentation.EditScheduleScreen
import com.toyota.oneapp.features.chargeschedule.presentation.PHEVNewScheduleScreen
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.ChargeScheduleRoute.Companion.ARG_IS_START_FLOW
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeInfo
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.TimerChargeInfo
import com.toyota.oneapp.features.core.navigation.sharedViewModel

fun NavGraphBuilder.chargeScheduleNavGraph(navController: NavHostController) {
    navigation(
        route = ChargeInfoRoute.ChargeScheduleNestedRoute.route,
        startDestination = ChargeScheduleRoute.ChargeScheduleScreen.route,
    ) {
        composable(route = ChargeScheduleRoute.ChargeScheduleScreen.route) { entry ->
            val chargeScheduleViewModel =
                entry.sharedViewModel<ChargeScheduleViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )

            ChargeScheduleScreen(
                navController = navController,
                viewModel = chargeScheduleViewModel,
            )
        }

        composable(route = ChargeScheduleRoute.EditMultidayScheduleScreen.route) { entry ->
            val chargeScheduleViewModel =
                entry.sharedViewModel<ChargeScheduleViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            val dataModel =
                navController.previousBackStackEntry?.savedStateHandle?.get<TimerChargeInfo>(
                    ChargeScheduleRoute.ARG_EDIT_SCHEDULE_MODEL,
                )

            EditScheduleScreen(
                navController = navController,
                viewModel = chargeScheduleViewModel,
                dataModel = dataModel,
            )
        }
        composable(
            route = "${ChargeScheduleRoute.NewPHEVScheduleScreen.route}/{$ARG_IS_START_FLOW}",
            arguments = listOf(navArgument(ARG_IS_START_FLOW) { defaultValue = false }),
        ) { entry ->
            val chargeScheduleViewModel =
                entry.sharedViewModel<ChargeScheduleViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            val isStartFlow =
                entry.arguments?.getBoolean(ChargeScheduleRoute.ARG_IS_START_FLOW) ?: false
            val chargeInfo =
                navController.previousBackStackEntry?.savedStateHandle?.get<ChargeInfo>(
                    ChargeScheduleRoute.ARG_CHARGE_INFO_MODEL,
                )

            PHEVNewScheduleScreen(
                navController = navController,
                isStartScheduleFlow = isStartFlow,
                viewModel = chargeScheduleViewModel,
                chargeInfo = chargeInfo,
            )
        }
    }
}

sealed class ChargeScheduleRoute(
    val route: String,
) {
    companion object {
        const val ARG_IS_START_FLOW = "arg_is_start"
        const val ARG_EDIT_SCHEDULE_MODEL = "arg_edit_schedule_model"
        const val ARG_CHARGE_INFO_MODEL = "arg_charge_info_model"
        const val ARG_IS_PLUGGED_IN = "{is_plugged_in}"
        const val ARG_IS_ELIGIBILITY_SCREEN_ZIP_VALID_QUERY_PARAM =
            "isZipFromEligibilityScreenValid={isZipFromEligibilityScreenValid}"
        const val ARG_IS_ELIGIBILITY_SCREEN_ZIP_VALID = "isZipFromEligibilityScreenValid"
    }

    object ChargeScheduleScreen : ChargeScheduleRoute("charge_schedule_screen")

    object EditMultidayScheduleScreen : ChargeScheduleRoute("edit_multiday_schedule")

    object NewPHEVScheduleScreen : ChargeScheduleRoute("new_phev_schedul_screen")
}

fun NavHostController.navigateToPHEVSchedule(
    isStartFlow: Boolean,
    chargeInfo: ChargeInfo?,
) {
    this.currentBackStackEntry?.savedStateHandle?.set(
        ChargeScheduleRoute.ARG_CHARGE_INFO_MODEL,
        chargeInfo,
    )
    this.navigate(
        "${ChargeScheduleRoute.NewPHEVScheduleScreen.route}/$isStartFlow",
    )
}

fun NavHostController.navigateToMultiDaySchedule(dataModel: TimerChargeInfo?) {
    this.currentBackStackEntry?.savedStateHandle?.set(
        ChargeScheduleRoute.ARG_EDIT_SCHEDULE_MODEL,
        dataModel,
    )
    this.navigate(ChargeScheduleRoute.EditMultidayScheduleScreen.route)
}
