package com.toyota.oneapp.features.chargeschedule.presentation

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargemanagement.presentation.navigation.navigateToChargeManagementPHEVSchedule
import com.toyota.oneapp.features.chargeschedule.application.ChargeScheduleState
import com.toyota.oneapp.features.chargeschedule.domain.model.OnOffStatus
import com.toyota.oneapp.features.chargeschedule.domain.model.PHEVScheduleModel
import com.toyota.oneapp.features.chargeschedule.domain.model.TileInfoUIModel
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.navigateToPHEVSchedule
import com.toyota.oneapp.features.chargeschedule.presentation.previewprovider.PHEVScheduleProvider
import com.toyota.oneapp.features.chargeschedule.presentation.widgets.RefreshLayout
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine2TextView
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.CommonUtil

@Composable
fun PHEVScheduleScreen(
    navController: NavHostController,
    uiModel: PHEVScheduleModel.PHEVScheduleSuccessModel,
    modifier: Modifier = Modifier,
    viewModel: ChargeScheduleViewModel = hiltViewModel(),
) {
    val context = LocalContext.current

    PHEVScheduleContent(
        isChargeManagementFlow = false,
        uiModel = uiModel,
        modifier = modifier,
        onRefresh = { viewModel.refreshLastUpdatedData() },
        onShowToast = { message ->
            CommonUtil.showToast(context, context.getString(message))
        },
    ) { isStartTime ->
        navController.navigateToPHEVSchedule(isStartTime, uiModel.chargeInfo)
    }
}

@Composable
fun ChargeManagementPHEVScheduleScreen(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    viewModel: ChargeScheduleViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.onEvent(ChargeScheduleEvents.InitPHEVSchedule(true))
    }

    ShowProgressIndicator(dialogState = uiState.showProgress)

    when (uiState.viewState) {
        is ChargeScheduleState.PHEVScheduleTimer -> {
            val uiModel = (uiState.viewState as ChargeScheduleState.PHEVScheduleTimer).uiModel
            PHEVScheduleContent(
                isChargeManagementFlow = true,
                uiModel = uiModel,
                modifier = modifier,
                onRefresh = { viewModel.refreshLastUpdatedData() },
                onShowToast = { message ->
                    CommonUtil.showToast(context, context.getString(message))
                },
            ) { isStartTime ->
                navController.navigateToChargeManagementPHEVSchedule(
                    isStartTime,
                    uiModel.chargeInfo,
                )
            }
        }
        else -> {
            // don't do anything
        }
    }
}

@Composable
private fun PHEVScheduleContent(
    isChargeManagementFlow: Boolean,
    uiModel: PHEVScheduleModel.PHEVScheduleSuccessModel,
    modifier: Modifier = Modifier,
    onRefresh: () -> Unit,
    onShowToast: (message: Int) -> Unit,
    onNavigate: (isStartTime: Boolean) -> Unit,
) {
    Column(
        modifier =
            modifier
                .fillMaxSize()
                .padding(vertical = 16.dp),
    ) {
        AnimatedVisibility(
            visible = !isChargeManagementFlow,
        ) {
            OABody4TextView(
                text = stringResource(id = R.string.manual_charging_schedule),
                color = AppTheme.colors.tertiary03,
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        PHEVTimeTile(
            uiModel = uiModel.startTimeInfo,
            description =
                if (isChargeManagementFlow) {
                    stringResource(R.string.charge_management_start_time_desc)
                } else {
                    stringResource(uiModel.startTimeInfo.description)
                },
            modifier =
                Modifier
                    .testTagID(AccessibilityId.ID_PHEV_START_TIME_CARD),
        ) {
            if (uiModel.shouldSetTimerInVehicle) {
                onShowToast(R.string.set_time_in_vehicle)
            } else {
                onNavigate(true)
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        PHEVTimeTile(
            uiModel = uiModel.departureTimeInfo,
            description =
                if (isChargeManagementFlow) {
                    stringResource(R.string.charge_management_departure_time_desc)
                } else {
                    stringResource(uiModel.departureTimeInfo.description)
                },
            modifier =
                Modifier
                    .testTagID(AccessibilityId.ID_PHEV_DEPART_TIME_CARD),
        ) {
            if (uiModel.shouldSetTimerInVehicle) {
                onShowToast(R.string.set_time_in_vehicle)
            } else {
                onNavigate(false)
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        RefreshLayout(
            lastUpdatedInfo = uiModel.lastUpdated,
            modifier =
                Modifier
                    .align(Alignment.CenterHorizontally)
                    .testTagID(AccessibilityId.ID_PHEV_REFRESH_BUTTON),
        ) {
            onRefresh()
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun PHEVTimeTile(
    uiModel: TileInfoUIModel,
    description: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Card(
        onClick = {
            onClick()
        },
        shape = RoundedCornerShape(8.dp),
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tile02,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
        ) {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier =
                    Modifier
                        .fillMaxWidth(),
            ) {
                OASubHeadLine2TextView(
                    text = stringResource(id = uiModel.title),
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .align(Alignment.CenterVertically),
                )

                Surface(
                    elevation = 0.dp,
                    shape = RoundedCornerShape(32.dp),
                    modifier =
                        Modifier
                            .wrapContentSize()
                            .testTagID(AccessibilityId.ID_PHEV_ON_OFF_VIEW),
                    color =
                        if (uiModel.onOffStatus == OnOffStatus.ON) {
                            AppTheme.colors.secondary02
                        } else {
                            AppTheme.colors.button05b
                        },
                ) {
                    OACallOut1TextView(
                        text =
                            if (uiModel.onOffStatus == OnOffStatus.ON) {
                                stringResource(id = R.string.Common_on)
                            } else {
                                stringResource(id = R.string.Common_off)
                            },
                        color = AppTheme.colors.button02a,
                        modifier = Modifier.padding(horizontal = 24.dp, vertical = 8.dp),
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Divider(color = AppTheme.colors.tertiary10)

            OACallOut1TextView(
                text = description,
                color = AppTheme.colors.tertiary05,
                modifier =
                    Modifier
                        .padding(top = 16.dp, bottom = 8.dp),
            )
        }
    }
}

@Preview
@Composable
fun PHEVScheduleContent(
    @PreviewParameter(PHEVScheduleProvider::class) uiModel: PHEVScheduleModel.PHEVScheduleSuccessModel,
) {
    PHEVScheduleContent(
        isChargeManagementFlow = false,
        uiModel = uiModel,
        onRefresh = {},
        onShowToast = {},
        onNavigate = {},
    )
}

@Preview
@Composable
fun NoPHEVSchedulePreview() {
    NoScheduleWidget(false) { }
}
