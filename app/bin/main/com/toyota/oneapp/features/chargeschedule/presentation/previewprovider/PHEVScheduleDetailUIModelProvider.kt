/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeschedule.presentation.previewprovider

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeschedule.presentation.model.PHEVScheduleDetailUIModel
import com.toyota.oneapp.features.chargeschedule.presentation.model.TimeObj
import com.toyota.oneapp.features.chargeschedule.util.ChargeScheduleUtil

class PHEVScheduleDetailUIModelProvider : PreviewParameterProvider<PHEVScheduleDetailUIModel> {
    val model1 =
        PHEVScheduleDetailUIModel.PHEVStartTimeDetailUIModel(
            startTitle = R.string.ev_climate_schedule_start_time,
            startSubTitle = R.string.phev_start_time_description,
            startTime = TimeObj("11:00", "11", "00", "am"),
            startSelectedDay = "",
            startDaysOfWeek = ChargeScheduleUtil.getDaysOfWeekForPHEVSchedule(1),
        )
    val model2 =
        PHEVScheduleDetailUIModel.PHEVDepartureTimeDetailUIModel(
            endTitle = R.string.ev_depart_time,
            endSubTitle = R.string.phev_depart_time_description,
            endTime = TimeObj("11:00", "11", "00", "am"),
            endSelectedDay = "",
            endDaysOfWeek = ChargeScheduleUtil.getDaysOfWeekForPHEVSchedule(1),
            isClimateEnabled = true,
        )
    override val values = sequenceOf(model1, model2)
}
