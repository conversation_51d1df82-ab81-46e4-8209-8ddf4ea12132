/*
 *  Created by sudhan.ram on 16/09/24, 5:23 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 16/09/24, 10:00 am
 *
 */

package com.toyota.oneapp.features.cleanassist.domain.repo

import com.toyota.oneapp.features.cleanassist.dataaccess.servermodel.CleanAssistEligibilityResponse
import com.toyota.oneapp.features.cleanassist.dataaccess.servermodel.EligibleConsentsResponse
import com.toyota.oneapp.features.cleanassist.dataaccess.servermodel.LCFSChargeMetricResponse
import com.toyota.oneapp.features.dataconsent.dataaccess.servermodel.ConsentBody
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsentRequest
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse

interface CleanAssistRepo {
    suspend fun checkCleanAssistEligibility(vin: String): Resource<CleanAssistEligibilityResponse?>

    suspend fun fetchCleanAssistDetail(
        vin: String,
        brand: String,
        region: String,
        consent: String,
        assistConsents: CombineDataConsentRequest,
    ): Resource<EligibleConsentsResponse?>

    suspend fun acceptCleanAssistEnrollment(body: ConsentBody): Resource<BaseResponse?>

    suspend fun getLCFSChargeMetric(vin: String): Resource<LCFSChargeMetricResponse?>
}
