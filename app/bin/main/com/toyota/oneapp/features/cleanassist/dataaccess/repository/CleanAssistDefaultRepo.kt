/*
 *  Created by sudhan.ram on 16/09/24, 5:23 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 16/09/24, 10:00 am
 *
 */

package com.toyota.oneapp.features.cleanassist.dataaccess.repository

import com.toyota.oneapp.features.cleanassist.dataaccess.servermodel.CleanAssistEligibilityResponse
import com.toyota.oneapp.features.cleanassist.dataaccess.servermodel.EligibleConsentsResponse
import com.toyota.oneapp.features.cleanassist.dataaccess.servermodel.LCFSChargeMetricResponse
import com.toyota.oneapp.features.cleanassist.dataaccess.service.CleanAssistAPI
import com.toyota.oneapp.features.cleanassist.domain.repo.CleanAssistRepo
import com.toyota.oneapp.features.dataconsent.dataaccess.servermodel.ConsentBody
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsentRequest
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class CleanAssistDefaultRepo
    @Inject
    constructor(
        private val cleanAssistAPI: CleanAssistAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), CleanAssistRepo {
        override suspend fun checkCleanAssistEligibility(vin: String): Resource<CleanAssistEligibilityResponse?> {
            return makeApiCall {
                cleanAssistAPI.checkCleanAssistEligibility(vin)
            }
        }

        override suspend fun fetchCleanAssistDetail(
            vin: String,
            brand: String,
            region: String,
            consent: String,
            assistConsents: CombineDataConsentRequest,
        ): Resource<EligibleConsentsResponse?> {
            return makeApiCall {
                cleanAssistAPI.fetchCleanAssistDetail(
                    vin,
                    brand,
                    region,
                    consent,
                    assistConsents,
                )
            }
        }

        override suspend fun acceptCleanAssistEnrollment(body: ConsentBody): Resource<BaseResponse?> {
            return makeApiCall {
                cleanAssistAPI.acceptCleanAssistEnrollment(
                    body,
                )
            }
        }

        override suspend fun getLCFSChargeMetric(vin: String): Resource<LCFSChargeMetricResponse?> {
            return makeApiCall {
                cleanAssistAPI.getLCFSChargeMetric(vin)
            }
        }
    }
