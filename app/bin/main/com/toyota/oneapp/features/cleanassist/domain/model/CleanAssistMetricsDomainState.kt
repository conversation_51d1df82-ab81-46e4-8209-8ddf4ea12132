/*
 * Copyright © 2024. Toyota Motors North America IncAll rights reserved.
 */

package com.toyota.oneapp.features.cleanassist.domain.model

import com.toyota.oneapp.features.cleanassist.dataaccess.servermodel.LCFSChargeMetricResponse
import com.toyota.oneapp.features.cleanassist.util.StringUtil

sealed interface CleanAssistMetricsDomainState {
    data class Success(val data: CleanAssistMetricsUIModel) : CleanAssistMetricsDomainState

    data class Error(val message: String) : CleanAssistMetricsDomainState
}

data class CleanAssistMetricsUIModel(
    val cleanEnergyCharge: List<GraphData>,
    val co2Emissions: List<GraphData>,
)

fun LCFSChargeMetricResponse.toUiModel(): CleanAssistMetricsUIModel {
    val cleanEnergyCharged =
        payload?.cleanChargingHistory?.map {
            GraphData(
                StringUtil.getMonth(extractMonthFromDate(it.monthAndYear)),
                it.cleanElectricityChargedInMonth ?: 0.0,
            )
        }
    val co2Emissions =
        payload?.cleanChargingHistory?.map {
            GraphData(
                StringUtil.getMonth(extractMonthFromDate(it.monthAndYear)),
                it.co2eAvoidedInMonth ?: 0.0,
            )
        }

    return CleanAssistMetricsUIModel(
        cleanEnergyCharge = cleanEnergyCharged ?: emptyList(),
        co2Emissions = co2Emissions ?: emptyList(),
    )
}

fun extractMonthFromDate(monthAndYear: String?): String? {
    // Ensures the output is month from the monthAndYear(MM/YYYY) string
    return monthAndYear?.split("/")?.getOrNull(0)
}
