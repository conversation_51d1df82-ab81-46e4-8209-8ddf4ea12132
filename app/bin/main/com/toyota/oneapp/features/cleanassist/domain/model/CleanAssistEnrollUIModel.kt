/*
 * Copyright © 2024. Toyota Motors North America IncAll rights reserved.
 */

package com.toyota.oneapp.features.cleanassist.domain.model

import com.toyota.oneapp.features.cleanassist.dataaccess.servermodel.EligibleConsentsResponse
import com.toyota.oneapp.util.ToyotaConstants

data class CleanAssistEnrollUIModel(
    val cleanAssistDetail: String = ToyotaConstants.EMPTY_STRING,
    val termsAndPrivacyDetail: String = ToyotaConstants.EMPTY_STRING,
)

fun EligibleConsentsResponse.toUiModel(consentName: String): CleanAssistEnrollUIModel {
    return eligibleConsentsPayload?.eligibleConsents?.firstOrNull { consent ->
        consent.name == consentName
    }?.description?.let {
        CleanAssistEnrollUIModel(
            cleanAssistDetail = it.dialogs?.firstOrNull()?.body ?: ToyotaConstants.EMPTY_STRING,
            termsAndPrivacyDetail = it.body ?: ToyotaConstants.EMPTY_STRING,
        )
    } ?: CleanAssistEnrollUIModel()
}
