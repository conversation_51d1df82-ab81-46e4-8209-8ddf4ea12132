package com.toyota.oneapp.features.guestdriver.application

import com.toyota.oneapp.features.guestdriver.domain.model.GuestDriverSettings
import com.toyota.oneapp.features.guestdriver.domain.model.GuestSettingsList

sealed class GuestDriverState {
    object Loading : GuestDriverState()

    class Success(
        val settingsList: GuestSettingsList,
    ) : GuestDriverState()

    object Error : GuestDriverState()
}

sealed class GuestDriverSettingsState {
    object Loading : GuestDriverSettingsState()

    class Success(
        val settings: GuestDriverSettings,
    ) : GuestDriverSettingsState()

    object Error : GuestDriverSettingsState()
}

sealed class LoadingState {
    object Dismissed : LoadingState()

    object Loading : LoadingState()

    class Error(
        val messageId: Int,
    ) : LoadingState()

    class Navigate(
        val route: String,
    ) : LoadingState()

    object SaveProfileSuccess : LoadingState()
}

sealed class AddDriverState {
    object Init : AddDriverState()

    object Loading : AddDriverState()

    class SearchAgain(
        val driverName: String,
        val searchText: String,
    ) : AddDriverState()

    class SearchDriverSuccess(
        val driverName: String,
        val searchText: String,
    ) : AddDriverState()

    object NoSearchResult : AddDriverState()

    object InviteRemoteUserSuccess : AddDriverState()

    object Error : AddDriverState()
}
