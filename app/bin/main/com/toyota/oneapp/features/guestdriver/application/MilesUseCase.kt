package com.toyota.oneapp.features.guestdriver.application

import com.toyota.oneapp.features.guestdriver.dataaccess.servermodel.GuestDriverResponse.Payload.Settings
import com.toyota.oneapp.features.guestdriver.domain.model.GuestDriverSettings
import com.toyota.oneapp.features.guestdriver.domain.model.SettingsItem

interface MilesUseCase : GDUseCase {
    fun updateMiles(
        settings: GuestDriverSettings,
        miles: String,
    ): GuestDriverSettings

    fun updateMilesStatus(
        settings: GuestDriverSettings,
        status: Boolean,
    ): GuestDriverSettings

    fun resetMilesTime(
        settings: GuestDriverSettings,
        hour: Int,
        min: Int,
    ): GuestDriverSettings

    fun updateAMPM(
        settings: GuestDriverSettings,
        hour: Int,
        min: Int,
        isAM: Boolean,
    ): GuestDriverSettings

    fun getUnit(): Int

    fun getMilesList(item: Settings): List<SettingsItem.ListItem>

    fun getDistanceLimitOnStatus(item: Settings): String
}
