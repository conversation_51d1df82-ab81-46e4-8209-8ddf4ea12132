package com.toyota.oneapp.features.guestdriver.application.util

import com.idemia.acs.exceptions.DateFormatException
import com.toyota.oneapp.features.core.util.Constants.REGEX_EMAIL
import com.toyota.oneapp.features.guestdriver.domain.model.SettingsItem
import com.toyota.oneapp.features.guestdriver.presentation.util.GuestConstant
import com.toyota.oneapp.util.convertTo12HourFormat
import toyotaone.commonlib.log.LogTool
import java.util.regex.Pattern

object GuestLogicUtil {
    fun getResetTime(resetTimeValue: String?): SettingsItem.ResetTime {
        val resetTime =
            if (resetTimeValue?.isNotEmpty() == true) {
                resetTimeValue
            } else {
                "12:00:00"
            }

        try {
            return if (resetTime.uppercase().contains(GuestConstant.AM) ||
                resetTime.uppercase().contains(GuestConstant.PM)
            ) {
                val hour = resetTime.split(GuestConstant.COLON).first().trim()
                val last = resetTime.split(GuestConstant.COLON).last().trim()
                val minute = last.replace(GuestConstant.AM, "").replace(GuestConstant.PM, "").trim()
                val isAM = resetTime.uppercase().contains(GuestConstant.AM)

                SettingsItem.ResetTime(
                    hour = hour.toInt(),
                    minute = minute.toInt(),
                    isAM = isAM,
                )
            } else {
                val formattedTime = convertTo12HourFormat(time24 = resetTime, convertLocal = true)
                val hour = formattedTime.split(GuestConstant.COLON).first()
                val last = formattedTime.split(GuestConstant.COLON).last().uppercase()
                val minute =
                    last
                        .replace(GuestConstant.AM, "")
                        .replace(
                            GuestConstant.PM,
                            "",
                        ).trim()
                val isAM = formattedTime.uppercase().contains(GuestConstant.AM)
                SettingsItem.ResetTime(
                    hour = hour.toInt(),
                    minute = minute.toInt(),
                    isAM = isAM,
                )
            }
        } catch (e: DateFormatException) {
            LogTool.d("GuestLogicUtil", "getResetTime-DateFormatError:$e")
        } catch (e: NumberFormatException) {
            LogTool.d("GuestLogicUtil", "getResetTime-NumberFormatError:$e")
        }
        return SettingsItem.ResetTime(
            hour = 12,
            minute = 0,
            isAM = false,
        )
    }
}

fun String.isValidEmail(): Boolean = Pattern.matches(REGEX_EMAIL, this) && this.isNotEmpty()
