package com.toyota.oneapp.features.guestdriver.application

import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.features.guestdriver.application.GuestLogicHelper.getShortDay
import com.toyota.oneapp.features.guestdriver.dataaccess.servermodel.CurfewSetLimitRequest
import com.toyota.oneapp.features.guestdriver.dataaccess.servermodel.GuestDriverResponse.Payload.Settings
import com.toyota.oneapp.features.guestdriver.domain.model.GuestDriverSettings
import com.toyota.oneapp.features.guestdriver.domain.model.SettingsId
import com.toyota.oneapp.features.guestdriver.domain.model.SettingsItem
import com.toyota.oneapp.features.guestdriver.domain.repository.GuestDriverRepository
import com.toyota.oneapp.features.guestdriver.presentation.driverlimit.timeFormat
import com.toyota.oneapp.features.guestdriver.presentation.util.GuestConstant
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import javax.inject.Inject
import javax.inject.Named

class CurfewLogic
    @Inject
    constructor(
        private val repository: GuestDriverRepository,
        private val preferenceModel: OneAppPreferenceModel,
        @Named(GuestConstant.GD_LANGUAGE) private val guestLanguage: String,
    ) : CurfewUseCase {
        override fun getSettings(settings: Settings): SettingsItem {
            val curfewList = getCurfewList(settings)
            return SettingsItem(
                id = SettingsId.CURFEW.name,
                icon = R.drawable.ic_light,
                title = R.string.GuestProfile_curfew,
                subTitle =
                    SettingsItem.SubTitle(
                        value = getCurfewSubTitle(settings, curfewList),
                    ),
                status = if (settings.curfewOn == "1") GuestConstant.ON else GuestConstant.OFF,
                enableSwitch = false,
                route = OAScreen.CurfewSettingsScreen.route,
                list = curfewList,
                startTime = GuestLogicHelper.getStartTime(settings),
                endTime = GuestLogicHelper.getEndTime(settings),
            )
        }

        override fun updateCurfewSelectedItem(
            settings: GuestDriverSettings,
            curfew: SettingsItem.ListItem,
        ): GuestDriverSettings {
            settings.curfew.list?.forEach {
                if (it.value == curfew.value) {
                    it.isSelected = !it.isSelected
                }
            }
            updateCurfewSubTitle(settings)
            return settings
        }

        private fun updateCurfewSubTitle(settings: GuestDriverSettings) {
            var curfewDays = ""
            var selectedCount = Constants.INT_0
            val everyDayValue = getEveryDayInCurrentLanguage()
            settings.curfew.list?.forEach {
                val dayShort: String = it.value.substring(Constants.INT_0, Constants.INT_3)
                if (it.isSelected) {
                    selectedCount++
                    curfewDays =
                        if (curfewDays.isEmpty()) {
                            dayShort
                        } else {
                            "$curfewDays, $dayShort"
                        }
                }
            }
            if (selectedCount == Constants.INT_7) {
                curfewDays = everyDayValue
            }

            val startTimeValue =
                settings.curfew.startTime?.let {
                    "${it.hour.timeFormat()}:${it.minute.timeFormat()} ${if (it.isAM) GuestConstant.AM else GuestConstant.PM}"
                } ?: ""
            val endTimeValue =
                settings.curfew.endTime?.let {
                    "${it.hour.timeFormat()}:${it.minute.timeFormat()} ${if (it.isAM) GuestConstant.AM else GuestConstant.PM}"
                } ?: ""
            settings.curfew.subTitle =
                settings.curfew.subTitle.copy(
                    value = "$startTimeValue to $endTimeValue ${if (curfewDays.isNotEmpty()) "on $curfewDays" else ""}",
                )
        }

        override fun updateStartTime(
            settings: GuestDriverSettings,
            hour: Int,
            min: Int,
        ): GuestDriverSettings {
            val hr =
                if (hour >= Constants.INT_12) {
                    hour - Constants.INT_12
                } else {
                    hour
                }
            settings.curfew.startTime =
                SettingsItem.ResetTime(
                    hour = hr,
                    minute = min,
                    isAM = hour < Constants.INT_12,
                )
            updateCurfewSubTitle(settings)
            return settings
        }

        override fun updateEndTime(
            settings: GuestDriverSettings,
            hour: Int,
            min: Int,
        ): GuestDriverSettings {
            val hr =
                if (hour >= Constants.INT_12) {
                    hour - Constants.INT_12
                } else {
                    hour
                }
            settings.curfew.endTime =
                SettingsItem.ResetTime(
                    hour = hr,
                    minute = min,
                    isAM = hour < Constants.INT_12,
                )
            updateCurfewSubTitle(settings)
            return settings
        }

        override fun updateCurfewStatus(
            settings: GuestDriverSettings,
            status: Boolean,
        ): GuestDriverSettings {
            settings.curfew.status = if (status) GuestConstant.ON else GuestConstant.OFF
            return settings
        }

        override fun updateStartTimeAMPM(
            settings: GuestDriverSettings,
            hour: Int,
            min: Int,
            isAM: Boolean,
        ): GuestDriverSettings {
            settings.curfew.startTime =
                SettingsItem.ResetTime(
                    hour = hour,
                    minute = min,
                    isAM = isAM,
                )
            return settings
        }

        override fun updateEndTimeAMPM(
            settings: GuestDriverSettings,
            hour: Int,
            min: Int,
            isAM: Boolean,
        ): GuestDriverSettings {
            settings.curfew.endTime =
                SettingsItem.ResetTime(
                    hour = hour,
                    minute = min,
                    isAM = isAM,
                )
            return settings
        }

        private fun getDaysListInCurrentLanguage(): List<String>? {
            return when (guestLanguage) {
                GuestConstant.GUEST_LANGUAGE_ENGLISH -> GuestConstant.daysEnglish
                GuestConstant.GUEST_LANGUAGE_FRENCH -> GuestConstant.daysFrench
                GuestConstant.GUEST_LANGUAGE_SPANISH -> GuestConstant.daysSpanish
                else -> null
            }
        }

        private fun getEveryDayInCurrentLanguage(): String {
            return when (guestLanguage) {
                GuestConstant.GUEST_LANGUAGE_ENGLISH -> GuestConstant.EVERY_DAY_ENGLISH
                GuestConstant.GUEST_LANGUAGE_FRENCH -> GuestConstant.EVERY_DAY_FRENCH
                GuestConstant.GUEST_LANGUAGE_SPANISH -> GuestConstant.EVERY_DAY_SPANISH
                else -> GuestConstant.EVERY_DAY_ENGLISH
            }
        }

        private fun getCurfewList(settings: Settings): List<SettingsItem.ListItem>? {
            val list = mutableListOf<SettingsItem.ListItem>()
            val currentLanguageDays = getDaysListInCurrentLanguage()
            GuestConstant.daysEnglish.forEachIndexed { index, englishDayItem ->
                val dayValue = currentLanguageDays?.get(index)
                if (dayValue != null) {
                    val isSelected =
                        settings.curfew?.any {
                            it.day?.lowercase() == englishDayItem.getShortDay().lowercase() ||
                                it.day?.lowercase() == dayValue.getShortDay().lowercase()
                        } == true
                    list.add(
                        SettingsItem.ListItem(
                            value = currentLanguageDays[index],
                            isSelected = isSelected,
                        ),
                    )
                }
            }
            return list.ifEmpty { null }
        }

        private fun getCurfewSubTitle(
            settings: Settings,
            curfewList: List<SettingsItem.ListItem>?,
        ): String {
            var curfewDays = ""
            var selectedCount = Constants.INT_0
            val everyDayValue = getEveryDayInCurrentLanguage()
            curfewList?.forEach {
                if (it.isSelected) {
                    selectedCount++
                    curfewDays =
                        if (curfewDays.isEmpty()) {
                            it.value.substring(Constants.INT_0, Constants.INT_3)
                        } else {
                            "$curfewDays, ${it.value.substring(Constants.INT_0, Constants.INT_3)}"
                        }
                }
            }

            val startTimeValue =
                GuestLogicHelper.getStartTime(settings)?.let {
                    val amPM = if (it.isAM) GuestConstant.AM else GuestConstant.PM
                    "${it.hour.timeFormat()}: ${it.minute.timeFormat()} $amPM to "
                } ?: ""

            val endTimeValue =
                GuestLogicHelper.getEndTime(settings)?.let {
                    "${it.hour.timeFormat()}: ${it.minute.timeFormat()} ${if (it.isAM) GuestConstant.AM else GuestConstant.PM}"
                } ?: ""

            if (selectedCount == Constants.INT_7) {
                curfewDays = everyDayValue
            }
            return "$startTimeValue $endTimeValue on ${curfewDays.ifEmpty { "" }}"
        }

        override suspend fun sendNG86CurfewLimit(
            vehicleInfo: VehicleInfo,
            name: String,
            successString: String,
            isOn: Int,
            curfewList: List<CurfewSetLimitRequest.Curfew>,
        ): Boolean {
            val request =
                CurfewSetLimitRequest(
                    name = name,
                    speedLimitOn = isOn,
                    curfew = curfewList,
                    guid = preferenceModel.getGuid(),
                )
            val response =
                repository.sendNG86CurfewLimit(
                    vin = vehicleInfo.vin,
                    brand = vehicleInfo.brand,
                    curfewSetLimitRequest = request,
                )
            return response.data?.status?.messages?.first()?.description?.contains(successString) == true
        }
    }
