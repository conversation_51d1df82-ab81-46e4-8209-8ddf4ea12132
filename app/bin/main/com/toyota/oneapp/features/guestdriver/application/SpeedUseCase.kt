package com.toyota.oneapp.features.guestdriver.application

import com.toyota.oneapp.features.guestdriver.dataaccess.servermodel.GuestDriverResponse.Payload.Settings
import com.toyota.oneapp.features.guestdriver.domain.model.GuestDriverSettings
import com.toyota.oneapp.features.guestdriver.domain.model.SettingsItem

interface SpeedUseCase : GDUseCase {
    fun updateSpeed(
        settings: GuestDriverSettings,
        speed: String,
    ): GuestDriverSettings

    fun updateSpeedStatus(
        settings: GuestDriverSettings,
        status: Boolean,
    ): GuestDriverSettings

    fun getSpeedLimitStatus(item: Settings): String

    fun getSpeedLimitList(item: Settings): List<SettingsItem.ListItem>

    fun getUnit(isShortUnit: Boolean): Int
}
