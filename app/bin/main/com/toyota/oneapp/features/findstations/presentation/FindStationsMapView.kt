/*
 *  Created by su<PERSON><PERSON>.ram on 07/10/24, 11:17 am
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 07/10/24, 11:14 am
 *
 */

package com.toyota.oneapp.features.findstations.presentation

import android.location.Location
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.model.CameraPosition
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MapStyleOptions
import com.google.maps.android.compose.CameraPositionState
import com.google.maps.android.compose.GoogleMap
import com.google.maps.android.compose.MapProperties
import com.google.maps.android.compose.MapUiSettings
import com.google.maps.android.compose.Marker
import com.google.maps.android.compose.MarkerState
import com.google.maps.android.compose.rememberCameraPositionState
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.find.presentation.currentLatLng
import com.toyota.oneapp.features.find.presentation.widgets.bitmapDescriptor
import com.toyota.oneapp.features.find.utils.LocationUtil
import com.toyota.oneapp.features.findstations.application.FindStationsBottomSheetState
import com.toyota.oneapp.features.findstations.application.FindStationsMapState
import com.toyota.oneapp.features.findstations.application.toMarkInfo
import com.toyota.oneapp.features.findstations.domain.model.MarkerInfo
import com.toyota.oneapp.features.findstations.domain.model.StationUIModel
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.DEFAULT_LAT_LNG
import kotlinx.coroutines.launch

@Composable
fun MapScreenView(
    currentLocation: Location,
    viewModel: FindStationViewModel,
    modifier: Modifier = Modifier,
) {
    val mapViewState = viewModel.mapState.collectAsState().value
    val coroutineScope = rememberCoroutineScope()
    val cameraPositionState = rememberCameraPositionState()
    var canShowVehicleLocationMapIcon by remember { mutableStateOf(false) }

    if (mapViewState is FindStationsMapState.ShowVehicleLocationMapIcons) {
        canShowVehicleLocationMapIcon = true
    }

    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .fillMaxHeight(0.5f),
    ) {
        var vehicleLocation: LatLng? by remember { mutableStateOf(null) }
        var stationsList: List<StationUIModel> by remember { mutableStateOf(emptyList()) }

        if (vehicleLocation != null && vehicleLocation?.latitude != DEFAULT_LAT_LNG &&
            vehicleLocation?.longitude != DEFAULT_LAT_LNG
        ) {
            cameraPositionState.position =
                CameraPosition.fromLatLngZoom(
                    vehicleLocation ?: LocationUtil.getPosition(LocationUtil.getDefaultLocation()),
                    15f,
                )
        } else {
            cameraPositionState.position =
                CameraPosition.fromLatLngZoom(
                    LocationUtil.getPosition(currentLocation),
                    15f,
                )
        }

        when (mapViewState) {
            is FindStationsMapState.UpdateMapWithMarkers -> {
                mapViewState.focusLocation?.let {
                    cameraPositionState.move(
                        CameraUpdateFactory.newCameraPosition(
                            CameraPosition.fromLatLngZoom(it, 15f),
                        ),
                    )
                }
                vehicleLocation = mapViewState.vehicleLocation
                stationsList = mapViewState.stations
            }
            is FindStationsMapState.MoveToSelectedStationMarker -> {
                mapViewState.stationItem.let {
                    cameraPositionState.move(
                        CameraUpdateFactory.newCameraPosition(
                            CameraPosition.fromLatLngZoom(it.stationPosition, 15f),
                        ),
                    )
                    vehicleLocation = mapViewState.vehicleLocation
                    stationsList = listOf(it)
                }
            }
            else -> {}
        }

        StationsGoogleMap(
            cameraPositionState = cameraPositionState,
            vehicleLocation = vehicleLocation,
            currentLocation = LocationUtil.getPosition(currentLocation),
            markerInfo = stationsList.toMarkInfo(),
            modifier = modifier,
        ) { latlng ->
            if (viewModel.bottomSheetState.value is FindStationsBottomSheetState.LoadAllNearbyStations) {
                stationsList.find { latlng == it.stationPosition }?.let { stationsItem ->
                    viewModel.onShowStationDetail(stationsItem)
                }
            }
        }

        StationsMapIcons(
            canShowVehicleLocationMapIcon = canShowVehicleLocationMapIcon,
            modifier =
                Modifier
                    .wrapContentSize()
                    .align(Alignment.BottomEnd)
                    .padding(end = 16.dp, bottom = 94.dp),
            vehicleLocationOnClick = {
                coroutineScope.launch {
                    viewModel.vehicleLatLng?.let {
                        cameraPositionState.animate(
                            CameraUpdateFactory.newCameraPosition(
                                CameraPosition.fromLatLngZoom(it, 15f),
                            ),
                            500,
                        )
                    }
                }
            },
            currentLocationOnClick = {
                coroutineScope.launch {
                    currentLatLng?.let {
                        cameraPositionState.animate(
                            CameraUpdateFactory.newCameraPosition(
                                CameraPosition.fromLatLngZoom(it, 15f),
                            ),
                            500,
                        )
                    }
                }
            },
        )
    }
}

@Composable
fun StationsMapIcons(
    canShowVehicleLocationMapIcon: Boolean,
    modifier: Modifier = Modifier,
    vehicleLocationOnClick: () -> Unit,
    currentLocationOnClick: () -> Unit,
) {
    Column(
        modifier = modifier,
    ) {
        if (canShowVehicleLocationMapIcon) {
            MapIcon(
                icon = R.drawable.ic_car_park_location,
                iconPadding = 12.dp,
                contentDescription = stringResource(id = R.string.vehicle_location_description),
                modifier =
                    Modifier
                        .testTagID(AccessibilityId.ID_VEHICLE_LOCATION_MAP_ICON),
            ) {
                vehicleLocationOnClick()
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        MapIcon(
            icon = R.drawable.ic_current_location,
            iconPadding = 6.dp,
            contentDescription = stringResource(id = R.string.current_location_description),
            modifier =
                Modifier
                    .testTagID(AccessibilityId.ID_CURRENT_LOCATION_MAP_ICON),
        ) {
            currentLocationOnClick()
        }
    }
}

@Composable
fun StationsGoogleMap(
    cameraPositionState: CameraPositionState,
    vehicleLocation: LatLng?,
    currentLocation: LatLng?,
    markerInfo: List<MarkerInfo>,
    modifier: Modifier = Modifier,
    onMarkerClick: (LatLng) -> Unit,
) {
    val context = LocalContext.current

    val mapUiSettings by remember {
        mutableStateOf(
            MapUiSettings(
                zoomControlsEnabled = false,
                zoomGesturesEnabled = true,
            ),
        )
    }

    val mapProperties by remember {
        mutableStateOf(
            if (AppTheme.darkMode.value) {
                MapProperties(
                    mapStyleOptions =
                        MapStyleOptions.loadRawResourceStyle(
                            context,
                            R.raw.find_map_dark_theme,
                        ),
                )
            } else {
                MapProperties()
            },
        )
    }

    GoogleMap(
        modifier = modifier.wrapContentHeight(),
        cameraPositionState = cameraPositionState,
        uiSettings = mapUiSettings,
        properties = mapProperties,
    ) {
        vehicleLocation?.let {
            Marker(
                state = MarkerState(position = it),
                icon =
                    bitmapDescriptor(
                        context,
                        R.drawable.ic_vehicle_location_marker,
                    ),
                title = stringResource(R.string.find_marker_vehicle_position_title),
            )
        }

        markerInfo.forEach { markerInfo ->
            Marker(
                state = MarkerState(position = markerInfo.stationPosition),
                icon =
                    bitmapDescriptor(
                        context,
                        if (markerInfo.isHydrogenStation) {
                            R.drawable.ic_h2_station_marker
                        } else {
                            R.drawable.ic_charge_station_marker
                        },
                    ),
                title = markerInfo.stationName,
                onClick = {
                    onMarkerClick(markerInfo.stationPosition)
                    false
                },
            )
        }

        currentLocation?.let {
            Marker(
                state = MarkerState(position = it),
                icon =
                    bitmapDescriptor(
                        context,
                        R.drawable.ic_current_location_marker,
                    ),
                title = stringResource(R.string.find_marker_current_location_title),
            )
        }
    }
}

@Composable
private fun MapIcon(
    icon: Int,
    iconPadding: Dp,
    contentDescription: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Surface(
        shape = CircleShape,
        color = AppTheme.colors.tile01,
        modifier =
            modifier
                .size(48.dp)
                .clickable {
                    onClick()
                },
    ) {
        Icon(
            modifier =
                Modifier
                    .padding(iconPadding),
            painter = painterResource(id = icon),
            contentDescription = contentDescription,
            tint = AppTheme.colors.button02a,
        )
    }
}
