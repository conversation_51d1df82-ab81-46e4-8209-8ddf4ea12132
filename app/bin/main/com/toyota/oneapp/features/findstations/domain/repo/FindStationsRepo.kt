/*
 *  Created by sudhan.ram on 07/10/24, 11:17 am
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 07/10/24, 11:14 am
 *
 */

package com.toyota.oneapp.features.findstations.domain.repo

import com.toyota.oneapp.features.findstations.dataaccess.servermodel.NearestStationsListResponse
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationsListResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource

interface FindStationsRepo {
    suspend fun fetchStationList(
        latitude: Double,
        longitude: Double,
        vin: String,
        region: String,
        brand: String,
        radius: Int,
    ): Resource<StationsListResponse?>

    suspend fun fetchNearestStationList(
        vehicleInfo: VehicleInfo,
        radius: Int,
        latitude: Double,
        longitude: Double,
    ): Resource<NearestStationsListResponse?>
}
