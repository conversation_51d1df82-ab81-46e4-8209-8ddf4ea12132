/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.findstations.presentation

import android.location.Geocoder
import android.os.Build
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.core.commonapicalls.application.ElectricStatusState
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.features.findstations.application.FindStationsBottomSheetState
import com.toyota.oneapp.features.findstations.application.FindStationsMapState
import com.toyota.oneapp.features.findstations.application.FindStationsUseCase
import com.toyota.oneapp.features.findstations.domain.model.SendToCarState
import com.toyota.oneapp.features.findstations.domain.model.StationListUIModel
import com.toyota.oneapp.features.findstations.domain.model.StationUIModel
import com.toyota.oneapp.model.vehicle.VehicleInfo.FUELTYPE_HYDROGENFUELCELL
import com.toyota.oneapp.model.vehicle.VehicleInfo.REMOTE_STATUS_ACTIVATED
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseFragmentViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@Suppress("DEPRECATION")
@HiltViewModel
class FindStationViewModel
    @Inject
    constructor(
        private val findStationsUseCase: FindStationsUseCase,
        private val sharedDataSource: SharedDataSource,
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        private val dispatcherProvider: DispatcherProvider,
        val preferenceModel: OneAppPreferenceModel,
    ) : BaseFragmentViewModel() {
        var currentLatLng: LatLng? = null
        var vehicleLatLng: LatLng? = null
        var focusLatLng: LatLng? = null

        private val _bottomSheetState =
            MutableStateFlow<FindStationsBottomSheetState>(
                value = FindStationsBottomSheetState.Init,
            )
        val bottomSheetState = _bottomSheetState.asStateFlow()

        private val _mapState =
            MutableStateFlow<FindStationsMapState>(
                value = FindStationsMapState.Init,
            )
        val mapState = _mapState.asStateFlow()

        var stationsUIModel: StationListUIModel? = null

        private val _showSendToCarSuccess = MutableStateFlow(false)
        val showSendToCarSuccess = _showSendToCarSuccess.asStateFlow()

        init {
            analyticsLogger.logEvent(AnalyticsEvent.VEHICLE_PREFERRED_SERVICE_PAGE)
            applicationData.getSelectedVehicleState().value?.let { vehicle ->
                if (vehicle.remoteDisplay == REMOTE_STATUS_ACTIVATED) {
                    _mapState.update { FindStationsMapState.ShowVehicleLocationMapIcons }
                }
            }
            _bottomSheetState.update { FindStationsBottomSheetState.Loading }
        }

        fun isHydrogenFuelVehicle() =
            applicationData.getSelectedVehicleState().value?.run {
                fuelType == FUELTYPE_HYDROGENFUELCELL
            } ?: false

        fun loadPage() {
            applicationData.getSelectedVehicleState().value?.run {
                if (!isCY17 && hasEvRemoteCapability()) {
                    fetchVehicleLocation()
                } else {
                    currentLatLng?.let {
                        fetchStations(it)
                    }
                }
            }
        }

        fun canShowSendToCar(): Boolean {
            return applicationData.getSelectedVehicle()?.let {
                it.is21MMVehicle && !it.isEVPhp
            } == true
        }

        private fun fetchVehicleLocation() {
            _bottomSheetState.update { FindStationsBottomSheetState.Loading }
            viewModelScope.launch(dispatcherProvider.default()) {
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    sharedDataSource.getElectricStatusState().collect { electricStatusState ->
                        if (electricStatusState is ElectricStatusState.Success) {
                            val positionDetail =
                                findStationsUseCase.getVehicleLocationFromElectricStatus(
                                    electricStatusState.response,
                                )
                            handleElectricStatus(positionDetail)
                        }
                    }
                }
            }
        }

        private fun handleElectricStatus(positionDetail: LatLng?) {
            if (positionDetail != null) {
                vehicleLatLng = positionDetail
                fetchStations(positionDetail, true)
            } else {
                fetchStations(currentLatLng)
            }
        }

        fun fetchStationsForSearchedLocation(
            searchText: String,
            geocoder: Geocoder,
        ) {
            viewModelScope.launch(dispatcherProvider.default()) {
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.TIRAMISU) {
                    geocoder.getFromLocationName(searchText, 2) { addresses ->
                        if (addresses.isEmpty()) {
                            _bottomSheetState.value = FindStationsBottomSheetState.EmptyStations
                            return@getFromLocationName
                        }
                        addresses.first().let {
                            fetchStations(
                                LatLng(
                                    it.latitude,
                                    it.longitude,
                                ),
                            )
                        }
                    }
                } else {
                    val addresses = geocoder.getFromLocationName(searchText, 2)
                    addresses?.let {
                        if (it.isEmpty()) {
                            _bottomSheetState.value = FindStationsBottomSheetState.EmptyStations
                            return@launch
                        }
                        it.first()?.run {
                            fetchStations(
                                LatLng(
                                    latitude,
                                    longitude,
                                ),
                            )
                        }
                    }
                }
            }
        }

        private fun fetchStations(
            positionInfo: LatLng?,
            isVehicleLocation: Boolean = false,
        ) {
            viewModelScope.launch(dispatcherProvider.default()) {
                _bottomSheetState.update { FindStationsBottomSheetState.Loading }
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    val flowResponse =
                        when {
                            vehicle.isHydrogenFuelCell -> {
                                findStationsUseCase.fetchHydrogenStationList(
                                    vehicleInfo = vehicle,
                                    positionInfo = positionInfo,
                                )
                            }
                            vehicle.isEVPhp || vehicle.isVehicleCanadian -> {
                                findStationsUseCase.fetchNearestStationListForPHEV(
                                    vehicleInfo = vehicle,
                                    positionInfo = positionInfo,
                                )
                            }
                            else -> {
                                findStationsUseCase.fetchStationList(
                                    vehicleInfo = vehicle,
                                    positionInfo = positionInfo,
                                )
                            }
                        }

                    flowResponse
                        .flowOn(dispatcherProvider.default())
                        .collect { uiModel ->
                            stationsUIModel = uiModel
                            if (uiModel?.stationList?.isNotEmpty() == true) {
                                focusLatLng = positionInfo
                                _mapState.update {
                                    FindStationsMapState.UpdateMapWithMarkers(
                                        vehicleLocation = if (isVehicleLocation) positionInfo else null,
                                        focusLocation = positionInfo,
                                        stations = uiModel.stationList,
                                    )
                                }
                                _bottomSheetState.update {
                                    FindStationsBottomSheetState.LoadAllNearbyStations(
                                        uiModel,
                                    )
                                }
                            } else {
                                _bottomSheetState.update { FindStationsBottomSheetState.EmptyStations }
                            }
                        }
                }
            }
        }

        fun onShowStationDetail(stationItem: StationUIModel) {
            _mapState.update {
                FindStationsMapState.MoveToSelectedStationMarker(
                    vehicleLatLng,
                    stationItem,
                )
            }
            _bottomSheetState.update { FindStationsBottomSheetState.ShowStationDetail(stationItem) }
        }

        fun onBackToStationList() {
            if (stationsUIModel != null && stationsUIModel?.stationList?.isNotEmpty() == true) {
                _mapState.update {
                    FindStationsMapState.UpdateMapWithMarkers(
                        vehicleLocation = vehicleLatLng,
                        focusLocation = focusLatLng,
                        stations = stationsUIModel?.stationList ?: emptyList(),
                    )
                }
                stationsUIModel?.let { uiModel ->
                    _bottomSheetState.update {
                        FindStationsBottomSheetState.LoadAllNearbyStations(
                            uiModel,
                        )
                    }
                }
            } else {
                _bottomSheetState.update { FindStationsBottomSheetState.EmptyStations }
            }
        }

        fun sendPOIToCar(stationUIModel: StationUIModel) {
            viewModelScope.launch(dispatcherProvider.default()) {
                findStationsUseCase.sendPOIToCar(
                    text = getPOIRequestText(stationUIModel),
                    guid = preferenceModel.getGuid(),
                )
                    .collect {
                        if (it is SendToCarState.Success) {
                            setShowSendToCar(true)
                        }
                    }
            }
        }

        private fun getPOIRequestText(stationUIModel: StationUIModel): String {
            return with(stationUIModel) {
                "$stationName, $addressLine1, $addressLine2, $province, $postalCode, $country"
            }
        }

        fun setShowSendToCar(show: Boolean) {
            _showSendToCarSuccess.value = show
        }
    }
