/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.findstations.domain.model

import androidx.annotation.StringRes
import com.google.android.gms.maps.model.LatLng

data class StationListUIModel(
    val totalResults: Int,
    val stationList: List<StationUIModel>,
)

data class EVChargeInfoModel(
    val chargersCount: String,
    val levelCount: String,
)

sealed class StationUIModel(
    val stationName: String,
    val addressLine1: String,
    val addressLine2: String,
    val province: String,
    val postalCode: String,
    val country: String,
    val stationPosition: LatLng,
    val id: String,
    val placeId: String,
    @StringRes val availability: Int?,
    val distance: String,
    val phoneNumber: String,
    val website: String,
) {
    class EVStationUIModel(
        val evStationName: String,
        val evAddressLine1: String,
        val evAddressLine2: String,
        val evProvince: String,
        val evPostalCode: String,
        val evCountry: String,
        val evStationPosition: LatLng,
        val evStationId: String,
        val evPlaceId: String,
        @StringRes val evAvailability: Int?,
        val chargeInfoModel: EVChargeInfoModel,
        val evDistance: String,
        val evPhoneNumber: String,
        val evWebsite: String,
    ) : StationUIModel(
            stationName = evStationName,
            addressLine1 = evAddressLine1,
            addressLine2 = evAddressLine2,
            province = evProvince,
            postalCode = evPostalCode,
            country = evCountry,
            stationPosition = evStationPosition,
            id = evStationId,
            placeId = evPlaceId,
            availability = evAvailability,
            distance = evDistance,
            phoneNumber = evPhoneNumber,
            website = evWebsite,
        )

    class HydrogenStationUIModel(
        val hyStationName: String,
        val hyAddressLine1: String,
        val hyAddressLine2: String,
        val hyProvince: String,
        val hyPostalCode: String,
        val hyCountry: String,
        val hyStationPosition: LatLng,
        val hyStationID: String,
        val hydrogenPlaceId: String,
        @StringRes val hyAvailability: Int?,
        val pressures: String,
        val hyDistance: String,
        val hyPhoneNumber: String,
        val hyWebsite: String,
    ) : StationUIModel(
            stationName = hyStationName,
            addressLine1 = hyAddressLine1,
            addressLine2 = hyAddressLine2,
            province = hyProvince,
            postalCode = hyPostalCode,
            country = hyCountry,
            stationPosition = hyStationPosition,
            id = hyStationID,
            placeId = hydrogenPlaceId,
            availability = hyAvailability,
            distance = hyDistance,
            phoneNumber = hyPhoneNumber,
            website = hyWebsite,
        )
}
