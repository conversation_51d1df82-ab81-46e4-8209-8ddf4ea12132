/*
 *  Created by su<PERSON><PERSON><PERSON>ram on 07/10/24, 11:17 am
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 07/10/24, 11:14 am
 *
 */

package com.toyota.oneapp.features.findstations.presentation

import android.location.Geocoder
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Divider
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody2TextView
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.findstations.domain.model.StationListUIModel
import com.toyota.oneapp.features.findstations.domain.model.StationUIModel
import java.util.Locale

@Composable
fun StationsListView(
    isH2Vehicle: Boolean,
    navController: NavController,
    uiModel: StationListUIModel,
    modifier: Modifier = Modifier,
    searchText: MutableState<TextFieldValue>,
    onStationDetailNavigation: (StationUIModel) -> Unit,
    onFetchStations: (geocoder: Geocoder, searchText: String) -> Unit,
) {
    val context = LocalContext.current
    BackHandler {
        navController.popBackStack()
    }

    Column(modifier = modifier) {
        TotalSearchResults(
            isHydrogenFuelVehicle = isH2Vehicle,
            searchResultCount = uiModel.totalResults,
        )

        SearchTextField(
            searchText = searchText,
        ) { searchText ->
            onFetchStations(Geocoder(context, Locale.getDefault()), searchText)
        }

        LazyColumn {
            items(uiModel.stationList) { stationItem ->
                StationItem(stationItem) {
                    onStationDetailNavigation(stationItem)
                }
            }
        }
    }
}

@Composable
fun EmptyStationsContent(
    isH2Vehicle: Boolean,
    navController: NavController,
    searchText: MutableState<TextFieldValue>,
    modifier: Modifier = Modifier,
    onFetchStations: (geocoder: Geocoder, searchText: String) -> Unit,
) {
    val context = LocalContext.current
    BackHandler {
        navController.popBackStack()
    }
    Column(modifier = modifier) {
        TotalSearchResults(
            isHydrogenFuelVehicle = isH2Vehicle,
        )

        SearchTextField(
            searchText = searchText,
        ) { searchText ->
            onFetchStations(Geocoder(context, Locale.getDefault()), searchText)
        }

        Spacer(modifier = Modifier.height(16.dp))

        OACallOut1TextView(
            text = stringResource(id = R.string.no_charge_station_hint_one),
            color = AppTheme.colors.tertiary05,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(16.dp))

        OACallOut1TextView(
            text = stringResource(id = R.string.no_charge_station_hint_two),
            color = AppTheme.colors.tertiary05,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Composable
fun SearchTextField(
    modifier: Modifier = Modifier,
    searchText: MutableState<TextFieldValue>,
    onSearch: (String) -> Unit,
) {
    TextField(
        value = searchText.value,
        keyboardOptions =
            KeyboardOptions(
                capitalization = KeyboardCapitalization.None,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Search,
            ),
        keyboardActions =
            KeyboardActions(
                onSearch = {
                    onSearch(searchText.value.text)
                },
            ),
        onValueChange = {
            searchText.value = it
        },
        shape = RoundedCornerShape(30.dp),
        placeholder = {
            OABody2TextView(
                text = stringResource(id = R.string.current_location),
                color = AppTheme.colors.tertiary07,
                maxLines = 1,
            )
        },
        leadingIcon = {
            Image(
                painter = painterResource(id = R.drawable.ic_search),
                contentDescription = stringResource(id = R.string.dashboard_light_search),
                colorFilter = ColorFilter.tint(AppTheme.colors.tertiary03),
                modifier =
                    Modifier
                        .clickable { onSearch(searchText.value.text) }
                        .size(24.dp),
            )
        },
        colors =
            TextFieldDefaults.textFieldColors(
                textColor = AppTheme.colors.tertiary03,
                backgroundColor = AppTheme.colors.tile05,
                cursorColor = AppTheme.colors.tertiary05,
                unfocusedIndicatorColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent,
            ),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(all = 16.dp)
                .testTagID(AccessibilityId.ID_SEARCH_BAR_TEXTFIELD),
    )
}

@Composable
fun TotalSearchResults(
    isHydrogenFuelVehicle: Boolean,
    modifier: Modifier = Modifier,
    searchResultCount: Int = 0,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 8.dp, vertical = 16.dp),
    ) {
        OABody4TextView(
            text =
                if (isHydrogenFuelVehicle) {
                    stringResource(id = R.string.fuel_stations)
                } else {
                    stringResource(id = R.string.charge_stations)
                },
            color = AppTheme.colors.tertiary03,
        )

        OACallOut1TextView(
            text = stringResource(id = R.string.nearby_stations_count, searchResultCount),
            color = AppTheme.colors.tertiary05,
        )
    }
}

@Composable
fun StationItem(
    stationUIModel: StationUIModel,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 8.dp, vertical = 8.dp)
                .clickable { onClick() },
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth(),
        ) {
            OABody4TextView(
                text = stationUIModel.stationName,
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.weight(0.8f),
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.End,
                modifier = Modifier.weight(0.2f),
            ) {
                OACallOut1TextView(
                    text = stationUIModel.distance,
                    color = AppTheme.colors.tertiary05,
                )

                Spacer(modifier = Modifier.width(8.dp))

                Image(
                    painter = painterResource(id = R.drawable.ic_right_arrow),
                    contentDescription = stringResource(id = R.string.right_chevron),
                    modifier = Modifier.size(16.dp),
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        OACallOut1TextView(
            text = stationUIModel.addressLine1,
            color = AppTheme.colors.tertiary05,
            maxLines = 1,
        )

        OACallOut1TextView(
            text = stationUIModel.addressLine2,
            color = AppTheme.colors.tertiary05,
            maxLines = 1,
        )

        stationUIModel.availability?.let {
            OACallOut1TextView(
                text = stringResource(id = it),
                color = AppTheme.colors.secondary01,
                maxLines = 1,
            )
        }

        when (stationUIModel) {
            is StationUIModel.HydrogenStationUIModel -> {
                H2SpecificInfo(h2StationUIModel = stationUIModel)
            }
            is StationUIModel.EVStationUIModel -> {
                EVSpecificInfo(evStationUIModel = stationUIModel)
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        Divider(
            color = AppTheme.colors.tertiary10,
            thickness = 1.dp,
            modifier =
                Modifier
                    .fillMaxWidth(),
        )
    }
}

@Composable
private fun H2SpecificInfo(
    h2StationUIModel: StationUIModel.HydrogenStationUIModel,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier =
            modifier
                .fillMaxWidth(),
    ) {
        OACallOut1TextView(
            text = stringResource(id = R.string.pressure),
            color = AppTheme.colors.tertiary05,
        )

        OACallOut1TextView(
            text = h2StationUIModel.pressures,
            color = AppTheme.colors.tertiary05,
        )
    }
}

@Composable
private fun EVSpecificInfo(
    evStationUIModel: StationUIModel.EVStationUIModel,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier =
                Modifier
                    .fillMaxWidth(),
        ) {
            OACallOut1TextView(
                text = stringResource(id = R.string.ev_chargers),
                color = AppTheme.colors.tertiary05,
            )

            OACallOut1TextView(
                text = evStationUIModel.chargeInfoModel.chargersCount,
                color = AppTheme.colors.tertiary05,
            )
        }

        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier =
                Modifier
                    .fillMaxWidth(),
        ) {
            OACallOut1TextView(
                text = stringResource(id = R.string.ev_levels),
                color = AppTheme.colors.tertiary05,
            )

            OACallOut1TextView(
                text = evStationUIModel.chargeInfoModel.levelCount,
                color = AppTheme.colors.tertiary05,
            )
        }
    }
}
