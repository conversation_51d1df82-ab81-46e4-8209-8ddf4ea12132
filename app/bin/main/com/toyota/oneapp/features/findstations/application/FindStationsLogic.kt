/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.findstations.application

import android.location.Location
import android.location.LocationManager
import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.PositionInfo
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.NearestStationsListPayload
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationsListPayload
import com.toyota.oneapp.features.findstations.domain.model.EVChargeInfoModel
import com.toyota.oneapp.features.findstations.domain.model.MarkerInfo
import com.toyota.oneapp.features.findstations.domain.model.SendToCarState
import com.toyota.oneapp.features.findstations.domain.model.StationListUIModel
import com.toyota.oneapp.features.findstations.domain.model.StationUIModel
import com.toyota.oneapp.features.findstations.domain.repo.FindStationsRepo
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.DEFAULT_LAT_LNG
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.FCV_RADIUS
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.KM_TO_METER
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.MILE_TO_METER
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.OPEN_24_HRS_A_DAY
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.OPEN_24_HRS_DAILY
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.PHEV_RADIUS
import com.toyota.oneapp.model.poi.Address
import com.toyota.oneapp.model.poi.Coordinates
import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.model.poi.SendPOIToCarRequest
import com.toyota.oneapp.model.poi.SendToCarLocation
import com.toyota.oneapp.model.poi.SharePOIRequest
import com.toyota.oneapp.model.poi.SharePOIResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.model.vehicle.VehicleInfo.FUELTYPE_HYDROGENFUELCELL
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.LocationRepository
import com.toyota.oneapp.util.DoubleUtil.roundOffTo
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class FindStationsLogic
    @Inject
    constructor(
        private val findStationsRepo: FindStationsRepo,
        private val locationRepository: LocationRepository,
    ) : FindStationsUseCase {
        override fun getVehicleLocationFromElectricStatus(response: ElectricStatusResponse?): LatLng? {
            return response?.payload?.positionInfo?.toLatLng()
        }

        override fun fetchStationList(
            vehicleInfo: VehicleInfo,
            positionInfo: LatLng?,
        ): Flow<StationListUIModel?> {
            return flow {
                if (positionInfo != null && positionInfo.latitude != DEFAULT_LAT_LNG &&
                    positionInfo.longitude != DEFAULT_LAT_LNG
                ) {
                    val response =
                        findStationsRepo.fetchStationList(
                            latitude = positionInfo.latitude,
                            longitude = positionInfo.longitude,
                            vin = vehicleInfo.vin,
                            region = vehicleInfo.region,
                            brand = vehicleInfo.brand,
                            radius =
                                if (vehicleInfo.fuelType == FUELTYPE_HYDROGENFUELCELL) {
                                    FCV_RADIUS
                                } else {
                                    PHEV_RADIUS
                                },
                        )

                    if (response is Resource.Success) {
                        emit(response.data?.payload?.toUIModel(positionInfo, vehicleInfo))
                    } else {
                        emit(null)
                    }
                }
            }
        }

        override fun fetchNearestStationListForPHEV(
            vehicleInfo: VehicleInfo,
            positionInfo: LatLng?,
        ): Flow<StationListUIModel?> {
            return flow {
                if (positionInfo != null && positionInfo.latitude != DEFAULT_LAT_LNG &&
                    positionInfo.longitude != DEFAULT_LAT_LNG
                ) {
                    val response =
                        findStationsRepo.fetchNearestStationList(
                            vehicleInfo = vehicleInfo,
                            radius = PHEV_RADIUS,
                            latitude = positionInfo.latitude,
                            longitude = positionInfo.longitude,
                        )

                    if (response is Resource.Success) {
                        emit(response.data?.payload?.toUIModel(positionInfo, vehicleInfo))
                    } else {
                        emit(null)
                    }
                }
            }
        }

        override fun fetchHydrogenStationList(
            vehicleInfo: VehicleInfo,
            positionInfo: LatLng?,
        ): Flow<StationListUIModel?> {
            return flow {
                if (positionInfo != null && positionInfo.latitude != DEFAULT_LAT_LNG &&
                    positionInfo.longitude != DEFAULT_LAT_LNG
                ) {
                    val response =
                        findStationsRepo.fetchNearestStationList(
                            vehicleInfo = vehicleInfo,
                            radius = FCV_RADIUS,
                            latitude = positionInfo.latitude,
                            longitude = positionInfo.longitude,
                        )

                    if (response is Resource.Success) {
                        emit(response.data?.payload?.toH2UIModel(positionInfo, vehicleInfo))
                    } else {
                        emit(null)
                    }
                }
            }
        }

        override fun sendPOIToCar(
            text: String,
            guid: String,
        ): Flow<SendToCarState> {
            return flow {
                val sharedLocationResponse =
                    locationRepository.getPOIDataFromSharedText(
                        locationRequest =
                            SharePOIRequest(
                                source = "android",
                                text = text,
                            ),
                    )
                when (sharedLocationResponse) {
                    is Resource.Success -> {
                        sharedLocationResponse.data?.payload?.let {
                            emit(sendToCar(it, guid))
                        } ?: run { emit(SendToCarState.Error()) }
                    }

                    is Resource.Failure -> {
                        emit(SendToCarState.Error())
                    }

                    is Resource.Loading -> {
                        // No ui state.
                    }
                }
                return@flow emit(SendToCarState.Error())
            }
        }

        private suspend fun sendToCar(
            sharePOIResponse: SharePOIResponse,
            guid: String,
        ): SendToCarState {
            val locationDetail = sharePOIResponse.toLocationDetails()
            val sendToCarResponse =
                locationRepository.sendPOIToCar(
                    sendToCarLocationDetails =
                        SendPOIToCarRequest(
                            guid = guid,
                            poiName = locationDetail.formattedAddress.orEmpty(),
                            location = sendToCarLocation(locationDetail),
                        ),
                )
            when (sendToCarResponse) {
                is Resource.Failure -> {
                    return SendToCarState.Error()
                }

                is Resource.Loading -> {
                    // No ui state.
                }

                is Resource.Success -> {
                    return SendToCarState.Success
                }
            }
            return SendToCarState.Error()
        }

        private fun sendToCarLocation(locationDetails: LocationDetails): SendToCarLocation {
            val location =
                Coordinates(
                    latitude = locationDetails.locCoordinate?.latitude ?: 0.0,
                    longitude = locationDetails.locCoordinate?.longitude ?: 0.0,
                )
            return SendToCarLocation(
                latitude = location.latitude,
                longitude = location.longitude,
                source = "google_maps",
                name = locationDetails.name ?: ToyotaConstants.EMPTY_STRING,
                locCoordinate = location,
                formattedAddress = locationDetails.formattedAddress ?: ToyotaConstants.EMPTY_STRING,
                address = locationDetails.address ?: Address(),
                locationType = locationDetails.location_type ?: 0,
                location,
                placeId = locationDetails.placeId ?: ToyotaConstants.EMPTY_STRING,
                phoneNumber = locationDetails.phoneNumber ?: ToyotaConstants.EMPTY_STRING,
            )
        }

        private fun StationsListPayload.toUIModel(
            startPoint: LatLng,
            vehicleInfo: VehicleInfo,
        ): StationListUIModel {
            val stationUIList = ArrayList<StationUIModel>()
            try {
                this.stations?.filter { it.evLevel1 > 0 || it.evLevel2 > 0 || (it.evDCFastNum ?: 0) > 0 }?.forEach { stationItem ->
                    stationUIList.add(
                        StationUIModel.EVStationUIModel(
                        evStationName = stationItem.name.orEmpty(),
                        evAddressLine1 = stationItem.address.orEmpty(),
                        evAddressLine2 = "${stationItem.city}, ${stationItem.province} ${stationItem.postalCode}",
                        evProvince = stationItem.province.orEmpty(),
                        evPostalCode = stationItem.postalCode.orEmpty(),
                        evCountry = stationItem.country.orEmpty(),
                        evStationPosition =
                            LatLng(
                                stationItem.geometry?.coordinates?.last() ?: DEFAULT_LAT_LNG,
                                stationItem.geometry?.coordinates?.first() ?: DEFAULT_LAT_LNG,
                            ),
                        evAvailability =
                            if (stationItem.openingTimes?.timing == OPEN_24_HRS_A_DAY ||
                                stationItem.openingTimes?.timing == OPEN_24_HRS_DAILY
                            ) {
                                R.string.open_24_hours
                            } else {
                                null
                            },
                        evStationId = stationItem.id.orEmpty(),
                        evPlaceId = stationItem.placeId.orEmpty(),
                        chargeInfoModel =
                            EVChargeInfoModel(
                                chargersCount = (stationItem.evLevel1 + stationItem.evLevel2 + (stationItem.evDCFastNum ?: 0)).toString(),
                                levelCount =
                                    when {
                                        stationItem.evLevel1 > 0 && stationItem.evLevel2 > 0 && (stationItem.evDCFastNum ?: 0) > 0 -> "1,2,DC"
                                        stationItem.evLevel1 > 0 && (stationItem.evDCFastNum ?: 0) > 0 -> "1,DC"
                                        stationItem.evLevel2 > 0 && (stationItem.evDCFastNum ?: 0) > 0 -> "2,DC"
                                        stationItem.evLevel1 > 0 && stationItem.evLevel2 > 0 -> "1,2"
                                        stationItem.evLevel2 > 0 -> "2"
                                        stationItem.evLevel1 > 0 -> "1"
                                        (stationItem.evDCFastNum ?: 0) > 0 -> "DC"
                                        else -> "-"
                                    },
                            ),
                        evDistance =
                            startPoint.distanceBetween(
                                LatLng(
                                    stationItem.geometry?.coordinates?.last() ?: DEFAULT_LAT_LNG,
                                    stationItem.geometry?.coordinates?.first() ?: DEFAULT_LAT_LNG,
                                ),
                                vehicleInfo.isVehicleCanadian,
                            ),
                        evPhoneNumber = stationItem.phoneNumber.orEmpty(),
                        evWebsite = stationItem.owner?.website.orEmpty(),
                    ),
                )
                }
            } catch (e: Exception) {
                // Log the error and continue with empty list
                android.util.Log.e("FindStationsLogic", "Error processing station data", e)
            }

            return StationListUIModel(
                totalResults = stationUIList.size,
                stationList = stationUIList,
            )
        }

        private fun NearestStationsListPayload.toUIModel(
            startPoint: LatLng,
            vehicleInfo: VehicleInfo,
        ): StationListUIModel {
            val stationUIList = ArrayList<StationUIModel>()
            try {
                this.fuelStations?.forEach { station ->
                val stationPosition =
                    LatLng(
                        station.latitude ?: DEFAULT_LAT_LNG,
                        station.longitude ?: DEFAULT_LAT_LNG,
                    )
                stationUIList.add(
                    StationUIModel.EVStationUIModel(
                        evStationName = station.stationName.orEmpty(),
                        evAddressLine1 = station.streetAddress.orEmpty(),
                        evAddressLine2 = "${station.city}, ${station.state} ${station.zip}",
                        evProvince = station.state.orEmpty(),
                        evPostalCode = station.zip.orEmpty(),
                        evCountry = station.country.orEmpty(),
                        evStationPosition = stationPosition,
                        evStationId = station.id.toString(),
                        evPlaceId = ToyotaConstants.EMPTY_STRING,
                        evAvailability =
                            if (station.availability == OPEN_24_HRS_A_DAY ||
                                station.availability == OPEN_24_HRS_DAILY
                            ) {
                                R.string.open_24_hours
                            } else {
                                null
                            },
                        chargeInfoModel =
                            EVChargeInfoModel(
                                chargersCount = (station.chargeLevel1 + station.chargeLevel2).toString(),
                                levelCount =
                                    if (station.chargeLevel1 > 0 && station.chargeLevel2 > 0) {
                                        "1,2"
                                    } else if (station.chargeLevel2 > 0) {
                                        "2"
                                    } else {
                                        "-"
                                    },
                            ),
                        evDistance =
                            startPoint.distanceBetween(
                                stationPosition,
                                vehicleInfo.isVehicleCanadian,
                            ),
                        evPhoneNumber = station.stationPhone.orEmpty(),
                        evWebsite = station.website.orEmpty(),
                    ),
                )
                }
            } catch (e: Exception) {
                // Log the error and continue with empty list
                android.util.Log.e("FindStationsLogic", "Error processing fuel station data", e)
            }
            return StationListUIModel(
                totalResults = stationUIList.size,
                stationList = stationUIList,
            )
        }
    }

private fun NearestStationsListPayload.toH2UIModel(
    startPoint: LatLng,
    vehicleInfo: VehicleInfo,
): StationListUIModel {
    val stationUIList = ArrayList<StationUIModel>()
    try {
        this.fuelStations?.forEach { stationItem ->
        stationUIList.add(
            StationUIModel.HydrogenStationUIModel(
                hyStationName = stationItem.stationName.orEmpty(),
                hyAddressLine1 = stationItem.streetAddress.orEmpty(),
                hyAddressLine2 = "${stationItem.city}, ${stationItem.state} ${stationItem.zip}",
                hyProvince = stationItem.state.orEmpty(),
                hyPostalCode = stationItem.zip.orEmpty(),
                hyCountry = stationItem.country.orEmpty(),
                hyStationID = stationItem.id.toString(),
                hydrogenPlaceId = ToyotaConstants.EMPTY_STRING,
                hyStationPosition =
                    LatLng(
                        stationItem.latitude ?: DEFAULT_LAT_LNG,
                        stationItem.longitude ?: DEFAULT_LAT_LNG,
                    ),
                hyAvailability =
                    if (stationItem.availability == OPEN_24_HRS_A_DAY ||
                        stationItem.availability == OPEN_24_HRS_DAILY
                    ) {
                        R.string.open_24_hours
                    } else {
                        null
                    },
                pressures =
                    if (stationItem.pressures.isNullOrEmpty()) {
                        "-"
                    } else {
                        stationItem.pressures.joinToString { it }
                    },
                hyDistance =
                    startPoint.distanceBetween(
                        LatLng(
                            stationItem.latitude ?: DEFAULT_LAT_LNG,
                            stationItem.longitude ?: DEFAULT_LAT_LNG,
                        ),
                        vehicleInfo.isVehicleCanadian,
                    ),
                hyPhoneNumber = stationItem.stationPhone.orEmpty(),
                hyWebsite = stationItem.hyStatusLink.orEmpty(),
            ),
        )
        }
    } catch (e: Exception) {
        // Log the error and continue with empty list
        android.util.Log.e("FindStationsLogic", "Error processing hydrogen station data", e)
    }

    return StationListUIModel(
        totalResults = stationUIList.size,
        stationList = stationUIList,
    )
}

fun PositionInfo.toLatLng(): LatLng {
    return LatLng(
        this.latitude,
        this.longitude,
    )
}

fun LatLng.distanceBetween(
    stationLatLng: LatLng,
    isCanadian: Boolean,
): String {
    val locationOne = Location(LocationManager.GPS_PROVIDER)
    locationOne.latitude = this.latitude
    locationOne.longitude = this.longitude

    val locationTwo = Location(LocationManager.GPS_PROVIDER)
    locationTwo.latitude = stationLatLng.latitude
    locationTwo.longitude = stationLatLng.longitude

    val distanceInMeters = locationOne.distanceTo(locationTwo).toDouble()
    if (isCanadian) {
        val distanceInKm = distanceInMeters / KM_TO_METER
        return "${distanceInKm.roundOffTo(2)} km"
    }
    val distanceInMiles = distanceInMeters * MILE_TO_METER
    return "${distanceInMiles.roundOffTo(2)} mi"
}

fun List<StationUIModel>.toMarkInfo(): List<MarkerInfo> {
    val markerInfo = ArrayList<MarkerInfo>()
    this.forEach {
        markerInfo.add(
            MarkerInfo(
                it.stationPosition,
                it.stationName,
                it is StationUIModel.HydrogenStationUIModel,
            ),
        )
    }
    return markerInfo
}
