/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.findstations.presentation

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.BottomSheetScaffold
import androidx.compose.material.BottomSheetValue
import androidx.compose.material.Surface
import androidx.compose.material.rememberBottomSheetScaffoldState
import androidx.compose.material.rememberBottomSheetState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.CurrentLocationRequest
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OAHeadline1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dealerservice.presentation.landingpage.components.getIconContentDescription
import com.toyota.oneapp.features.find.utils.LocationUtil
import com.toyota.oneapp.features.findstations.application.FindStationsBottomSheetState
import com.toyota.oneapp.features.publiccharging.presentation.components.StationHeader

@OptIn(ExperimentalMaterial3Api::class)
@SuppressLint("UnusedBoxWithConstraintsScope", "StateFlowValueCalledInComposition")
@Composable
fun FindStationsScreen(
    navController: NavController,
    modifier: Modifier = Modifier,
    viewModel: FindStationViewModel = hiltViewModel(),
) {
    val sheetState =
        rememberBottomSheetState(
            initialValue = BottomSheetValue.Collapsed,
        )
    val scaffoldState = rememberBottomSheetScaffoldState(bottomSheetState = sheetState)
    val viewState = viewModel.bottomSheetState.collectAsState()

    var currentLocation by remember { mutableStateOf(LocationUtil.getDefaultLocation()) }

    val modalSheetState = rememberModalBottomSheetState()
    val showSendToCarSuccess = viewModel.showSendToCarSuccess.collectAsState().value

    BoxWithConstraints {
        val configuration = LocalConfiguration.current
        val screenHeight = configuration.screenHeightDp
        val peekHeight = 0.6 * screenHeight
        BottomSheetScaffold(
            sheetPeekHeight = peekHeight.dp,
            sheetShape = RoundedCornerShape(topStart = 30.dp, topEnd = 30.dp),
            sheetBackgroundColor = AppTheme.colors.tertiary15,
            scaffoldState = scaffoldState,
            sheetContent = {
                FindStationsBottomSheetContent(
                    peekHeight = peekHeight,
                    screenHeight = screenHeight,
                    viewState = viewState.value,
                    navController = navController,
                    viewModel = viewModel,
                )
                if (showSendToCarSuccess) {
                    ModalBottomSheet(
                        onDismissRequest = {
                            viewModel.setShowSendToCar(false)
                        },
                        sheetState = modalSheetState,
                        containerColor = AppTheme.colors.tertiary15,
                        dragHandle = {},
                    ) {
                        SendToCarSuccess(onDismiss = { viewModel.setShowSendToCar(false) })
                    }
                }
            },
            content = {
                MapScreenView(
                    currentLocation = currentLocation,
                    viewModel = viewModel,
                )
            },
            modifier = modifier.fillMaxSize(),
        )

        CurrentLocationRequest(
            onCurrentLocation = { location ->
                viewModel.currentLatLng = LocationUtil.getPosition(location)
                currentLocation = location
                viewModel.loadPage()
            },
            onLocationError = {},
        )
    }
}

@Composable
fun FindStationsBottomSheetContent(
    peekHeight: Double,
    screenHeight: Int,
    viewState: FindStationsBottomSheetState,
    navController: NavController,
    viewModel: FindStationViewModel,
    modifier: Modifier = Modifier,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier =
            modifier
                .fillMaxWidth()
                .heightIn(
                    min = peekHeight.dp,
                    max = (0.9 * screenHeight).dp,
                ).padding(all = 8.dp),
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_drag_indicator),
            contentDescription = stringResource(id = R.string.content_drag),
            modifier =
                Modifier
                    .width(28.dp)
                    .height(4.dp),
            colorFilter = ColorFilter.tint(AppTheme.colors.outline01),
        )
        val title =
            if (viewState is FindStationsBottomSheetState.ShowStationDetail) {
                stringResource(id = R.string.station_detail_title)
            } else {
                stringResource(id = R.string.nearby_stations)
            }
        StationHeader(
            title = title,
        ) {
            if (viewState is FindStationsBottomSheetState.ShowStationDetail) {
                viewModel.onBackToStationList()
            } else {
                navController.popBackStack()
            }
        }
        val searchText = remember { mutableStateOf(TextFieldValue("")) }
        when (viewState) {
            is FindStationsBottomSheetState.Loading -> {
                ShowProgressIndicator(dialogState = true)
            }
            is FindStationsBottomSheetState.EmptyStations -> {
                EmptyStationsContent(
                    isH2Vehicle = viewModel.isHydrogenFuelVehicle(),
                    navController = navController,
                    searchText = searchText,
                    modifier =
                        Modifier
                            .padding(horizontal = 8.dp),
                ) { geocoder, searchText ->
                    viewModel.fetchStationsForSearchedLocation(
                        searchText = searchText,
                        geocoder = geocoder,
                    )
                }
            }
            is FindStationsBottomSheetState.LoadAllNearbyStations -> {
                StationsListView(
                    isH2Vehicle = viewModel.isHydrogenFuelVehicle(),
                    navController = navController,
                    uiModel = viewState.uiModel,
                    searchText = searchText,
                    onStationDetailNavigation = { stationItem ->
                        viewModel.onShowStationDetail(stationItem)
                    },
                ) { geocoder, searchText ->
                    viewModel.fetchStationsForSearchedLocation(
                        searchText = searchText,
                        geocoder = geocoder,
                    )
                }
            }
            is FindStationsBottomSheetState.ShowStationDetail -> {
                StationDetailView(
                    uiModel = viewState.stationItem,
                    canShowSendToCar = viewModel.canShowSendToCar(),
                    onSendToCar = { viewModel.sendPOIToCar(it) },
                ) {
                    viewModel.onBackToStationList()
                }
            }
            else -> {
                // Do nothing
            }
        }
    }
}

@Composable
fun SendToCarSuccess(onDismiss: () -> Unit) {
    Column(
        modifier =
            Modifier
                .fillMaxHeight(.4f)
                .fillMaxWidth()
                .background(AppTheme.colors.tile03)
                .padding(horizontal = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(24.dp))
        InfoIcon()
        Spacer(modifier = Modifier.height(16.dp))
        OAHeadline1TextView(
            text = stringResource(id = R.string.destination_sent_title),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary03,
        )
        Spacer(modifier = Modifier.height(8.dp))
        OACallOut1TextView(
            text = stringResource(id = R.string.destination_address_sent),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary05,
        )
        Spacer(modifier = Modifier.weight(1f))
        PrimaryButton02(
            modifier = Modifier,
            text = stringResource(id = R.string.ok_label),
            click = {
                onDismiss.invoke()
            },
        )
        Spacer(modifier = Modifier.height(24.dp))
    }
}

@Composable
fun InfoIcon() {
    Box(
        modifier =
            Modifier
                .width(48.dp),
    ) {
        Surface(
            shape = CircleShape,
            color = AppTheme.colors.secondary02,
            modifier =
                Modifier
                    .size(size = 48.dp)
                    .align(Alignment.CenterStart),
        ) {
            Image(
                modifier =
                    Modifier
                        .padding(
                            all = 12.dp,
                        ),
                painter = painterResource(id = R.drawable.ic_info_secondary),
                colorFilter = ColorFilter.tint(AppTheme.colors.secondary01),
                contentDescription = getIconContentDescription(icon = R.drawable.ic_info_secondary),
            )
        }
    }
}

@Preview
@Composable
fun PreviewSendToCarSuccess() {
    Box(modifier = Modifier.fillMaxSize()) {
        SendToCarSuccess(onDismiss = {})
    }
}
