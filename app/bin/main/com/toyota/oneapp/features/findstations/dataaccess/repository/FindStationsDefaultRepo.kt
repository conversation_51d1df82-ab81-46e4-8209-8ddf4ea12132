/*
 *  Created by sudhan.ram on 07/10/24, 11:16 am
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 07/10/24, 11:14 am
 *
 */

package com.toyota.oneapp.features.findstations.dataaccess.repository

import com.toyota.oneapp.features.findstations.dataaccess.servermodel.NearestStationsListResponse
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationsListResponse
import com.toyota.oneapp.features.findstations.dataaccess.service.FindStationsAPI
import com.toyota.oneapp.features.findstations.domain.repo.FindStationsRepo
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class FindStationsDefaultRepo
    @Inject
    constructor(
        private val findStationsAPI: FindStationsAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext),
        FindStationsRepo {
        override suspend fun fetchStationList(
            latitude: Double,
            longitude: Double,
            vin: String,
            region: String,
            brand: String,
            radius: Int,
        ): Resource<StationsListResponse?> =
            makeApiCall {
                findStationsAPI.fetchStationList(
                    latitude = latitude,
                    longitude = longitude,
                    vin = vin,
                    region = region,
                    brand = brand,
                    radius = radius,
                )
            }

        override suspend fun fetchNearestStationList(
            vehicleInfo: VehicleInfo,
            radius: Int,
            latitude: Double,
            longitude: Double,
        ): Resource<NearestStationsListResponse?> =
            makeApiCall {
                findStationsAPI.fetchNearestStationList(
                    headers =
                        hashMapOf(
                            "X-BRAND" to vehicleInfo.brand,
                            "X-VIN" to vehicleInfo.vin,
                            "region" to vehicleInfo.region,
                            "fuelType" to vehicleInfo.fuelType,
                            "radius" to radius.toString(),
                            "latitude" to latitude.toString(),
                            "longitude" to longitude.toString(),
                        ),
                )
            }
    }
