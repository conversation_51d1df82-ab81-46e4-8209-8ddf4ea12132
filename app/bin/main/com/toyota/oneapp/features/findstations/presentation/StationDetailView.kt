/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.findstations.presentation

import android.content.Intent
import android.net.Uri
import androidx.activity.compose.BackHandler
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.Divider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.findstations.domain.model.StationUIModel
import com.toyota.oneapp.util.ToyUtil

@Composable
fun StationDetailView(
    uiModel: StationUIModel,
    modifier: Modifier = Modifier,
    onSendToCar: (StationUIModel) -> Unit,
    canShowSendToCar: Boolean,
    onBackNavigation: () -> Unit,
) {
    BackHandler {
        onBackNavigation()
    }

    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 8.dp),
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .wrapContentHeight(),
        ) {
            OABody4TextView(
                text = uiModel.stationName,
                color = AppTheme.colors.tertiary03,
                modifier =
                    Modifier
                        .weight(0.8f),
            )

            OACallOut1TextView(
                text = uiModel.distance,
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.End,
                modifier =
                    Modifier
                        .weight(0.2f),
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        OACallOut1TextView(
            text = uiModel.addressLine1,
            color = AppTheme.colors.tertiary05,
        )

        OACallOut1TextView(
            text = uiModel.addressLine2,
            color = AppTheme.colors.tertiary05,
        )

        uiModel.availability?.let {
            OACallOut1TextView(
                text = stringResource(id = it),
                color = AppTheme.colors.secondary01,
                maxLines = 1,
            )
        }

        Spacer(modifier = Modifier.height(12.dp))

        Divider(
            thickness = 1.dp,
            color = AppTheme.colors.tertiary10,
            modifier = Modifier.fillMaxWidth(),
        )

        StationDetailOptions(
            uiModel = uiModel,
            canShowSendToCar = canShowSendToCar,
            onSendToCar = onSendToCar,
        )
    }
}

@Composable
fun StationDetailOptions(
    uiModel: StationUIModel,
    onSendToCar: (StationUIModel) -> Unit,
    canShowSendToCar: Boolean,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current

    Column(modifier = modifier) {
        StationDetailItem(
            title = stringResource(id = R.string.directions_card),
            imageRes = R.drawable.ic_map_pin,
            modifier =
                Modifier
                    .testTagID(AccessibilityId.ID_DIRECTIONS_ITEM),
        ) {
            uiModel.stationPosition.apply {
                val gmmIntentUri =
                    Uri.parse("google.navigation:q=$latitude,$longitude")
                val mapIntent = Intent(Intent.ACTION_VIEW, gmmIntentUri)
                mapIntent.setPackage("com.google.android.apps.maps")
                context.startActivity(mapIntent)
            }
        }

        if (uiModel.website.isNotEmpty()) {
            StationDetailItem(
                title = stringResource(id = R.string.website_card),
                imageRes = R.drawable.ic_website,
                modifier =
                    Modifier
                        .testTagID(AccessibilityId.ID_WEBSITE_ITEM),
            ) {
                ToyUtil.openCustomChromeTab(
                    context,
                    uiModel.website,
                )
            }
        }

        if (uiModel.phoneNumber.isNotEmpty()) {
            StationDetailItem(
                title = stringResource(id = R.string.call_charge_station),
                imageRes = R.drawable.ic_baseline_call_24,
                modifier =
                    Modifier
                        .testTagID(AccessibilityId.ID_CALL_STATION_ITEM),
            ) {
                val intent = Intent(Intent.ACTION_DIAL)
                intent.setData(Uri.parse("tel:${uiModel.phoneNumber}"))
                context.startActivity(intent)
            }
        }

        if (canShowSendToCar) {
            StationDetailItem(
                title = stringResource(R.string.share_poi_send),
                imageRes = R.drawable.map_car,
                modifier =
                    Modifier
                        .testTagID(AccessibilityId.ID_SEND_TO_CAR_ITEM),
                onClick = {
                    onSendToCar(uiModel)
                },
            )
        }
    }
}

@Composable
private fun StationDetailItem(
    title: String,
    @DrawableRes imageRes: Int,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .clickable {
                    onClick()
                }
                .padding(vertical = 20.dp),
    ) {
        Image(
            painter = painterResource(id = imageRes),
            contentDescription = title,
            colorFilter = ColorFilter.tint(AppTheme.colors.tertiary03),
            modifier =
                Modifier
                    .size(20.dp)
                    .align(Alignment.CenterVertically),
        )

        Spacer(modifier = Modifier.width(16.dp))

        OABody3TextView(
            text = title,
            color = AppTheme.colors.tertiary03,
        )
    }

    Divider(
        thickness = 1.dp,
        color = AppTheme.colors.tertiary10,
        modifier = Modifier.fillMaxWidth(),
    )
}
