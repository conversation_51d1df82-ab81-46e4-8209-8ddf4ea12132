/*
 *  Created by sudhan.ram on 07/10/24, 11:15 am
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 07/10/24, 11:14 am
 *
 */

package com.toyota.oneapp.features.findstations.application

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.features.findstations.domain.model.StationListUIModel
import com.toyota.oneapp.features.findstations.domain.model.StationUIModel

sealed class FindStationsBottomSheetState {
    object Init : FindStationsBottomSheetState()

    object Loading : FindStationsBottomSheetState()

    class LoadAllNearbyStations(
        val uiModel: StationListUIModel,
    ) : FindStationsBottomSheetState()

    object EmptyStations : FindStationsBottomSheetState()

    class ShowStationDetail(
        val stationItem: StationUIModel,
    ) : FindStationsBottomSheetState()
}

sealed class FindStationsMapState {
    object Init : FindStationsMapState()

    object ShowVehicleLocationMapIcons : FindStationsMapState()

    class UpdateMapWithMarkers(
        val vehicleLocation: LatLng?,
        val focusLocation: LatLng?,
        val stations: List<StationUIModel>,
    ) : FindStationsMapState()

    class MoveToSelectedStationMarker(
        val vehicleLocation: LatLng?,
        val stationItem: StationUIModel,
    ) : FindStationsMapState()
}
