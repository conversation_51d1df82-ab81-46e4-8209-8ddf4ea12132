/*
 *  Created by sudhan.ram on 07/10/24, 11:16 am
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 07/10/24, 11:14 am
 *
 */

package com.toyota.oneapp.features.findstations.dataaccess.service

import com.toyota.oneapp.features.findstations.dataaccess.servermodel.NearestStationsListResponse
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationsListResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.HeaderMap
import retrofit2.http.Query

interface FindStationsAPI {
    @GET("/charging/v2/locations")
    suspend fun fetchStationList(
        @Header("x-latitude") latitude: Double,
        @Header("x-longitude") longitude: Double,
        @Header("X-VIN") vin: String,
        @Header("X-REGION") region: String,
        @Header("X-BRAND") brand: String,
        @Query("radius") radius: Int,
        @Query("limit") limit: Int = 40,
    ): Response<StationsListResponse?>

    @GET("/oneapi/v2/electric/nearest-fuel-stations")
    suspend fun fetchNearestStationList(
        @HeaderMap headers: Map<String, String>,
    ): Response<NearestStationsListResponse?>
}
