/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.findstations.application

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.findstations.domain.model.SendToCarState
import com.toyota.oneapp.features.findstations.domain.model.StationListUIModel
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface FindStationsUseCase {
    fun getVehicleLocationFromElectricStatus(response: ElectricStatusResponse?): LatLng?

    fun fetchStationList(
        vehicleInfo: VehicleInfo,
        positionInfo: LatLng?,
    ): Flow<StationListUIModel?>

    fun fetchNearestStationListForPHEV(
        vehicleInfo: VehicleInfo,
        positionInfo: LatLng?,
    ): Flow<StationListUIModel?>

    fun fetchHydrogenStationList(
        vehicleInfo: VehicleInfo,
        positionInfo: LatLng?,
    ): Flow<StationListUIModel?>

    fun sendPOIToCar(
        text: String,
        guid: String,
    ): Flow<SendToCarState>
}
