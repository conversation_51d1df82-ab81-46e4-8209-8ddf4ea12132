/*
 *  Created by sudhan.ram on 07/10/24, 11:16 am
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 07/10/24, 11:14 am
 *
 */

package com.toyota.oneapp.features.findstations.di

import com.toyota.oneapp.features.findstations.application.FindStationsLogic
import com.toyota.oneapp.features.findstations.application.FindStationsUseCase
import com.toyota.oneapp.features.findstations.dataaccess.repository.FindStationsDefaultRepo
import com.toyota.oneapp.features.findstations.domain.repo.FindStationsRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class FindStationsModule {
    @Binds
    abstract fun bindFindStationsRepo(repo: FindStationsDefaultRepo): FindStationsRepo

    @Binds
    abstract fun bindFindStationsUseCase(logic: FindStationsLogic): FindStationsUseCase
}
