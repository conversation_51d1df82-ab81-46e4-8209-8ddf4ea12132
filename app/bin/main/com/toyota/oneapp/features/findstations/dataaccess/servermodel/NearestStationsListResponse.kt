/*
 *  Created by sudhan.ram on 07/10/24, 11:16 am
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 07/10/24, 11:14 am
 *
 */

package com.toyota.oneapp.features.findstations.dataaccess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.network.models.BaseResponse

data class NearestStationsListResponse(
    @SerializedName("payload") val payload: NearestStationsListPayload?,
) : BaseResponse()

data class NearestStationsListPayload(
    @SerializedName("results") val results: Result?,
    @SerializedName("fuelStations") val fuelStations: List<FuelStation>?,
)

data class Result(
    @SerializedName("totalResults") val totalResults: Int?,
)

data class FuelStation(
    @SerializedName("id") val id: Int?,
    @SerializedName("fuelType") val fuelType: String?,
    @SerializedName("stationName") val stationName: String?,
    @SerializedName("stationPhone") val stationPhone: String?,
    @SerializedName("streetAddress") val streetAddress: String?,
    @SerializedName("city") val city: String?,
    @SerializedName("state") val state: String?,
    @SerializedName("zip") val zip: String?,
    @SerializedName("country") val country: String?,
    @SerializedName("latitude") val latitude: Double?,
    @SerializedName("longitude") val longitude: Double?,
    @SerializedName("availability") val availability: String?,
    @SerializedName("pressures") val pressures: List<String>?,
    @SerializedName("evChargeLevel1Count") val chargeLevel1: Int = 0,
    @SerializedName("evChargeLevel2Count") val chargeLevel2: Int = 0,
    @SerializedName("hyStatusLink") val hyStatusLink: String?,
    @SerializedName("networkWebSite") val website: String?,
    @SerializedName("status") val status: String?,
)
