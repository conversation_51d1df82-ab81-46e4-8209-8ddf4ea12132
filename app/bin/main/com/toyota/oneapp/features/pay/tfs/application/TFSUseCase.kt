package com.toyota.oneapp.features.pay.tfs.application

import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AccessTokenResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthenticateResponseModel
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

abstract class TFSUseCase {
    abstract suspend fun isFinanceServiceAvailable(vehicleInfo: VehicleInfo): Flow<TFSAvailabilityState>

    abstract suspend fun isFinanceUnderMaintenance(): Flow<FinancialServiceState>

    abstract suspend fun eligibilityCheckForTFS(vehicleInfo: VehicleInfo): Flow<EligibilityState>

    abstract suspend fun isCheckSecondaryUser(vehicleInfo: VehicleInfo): Boolean

    abstract suspend fun callAuthenticate(
        submitDevicePrint: Boolean,
        isRelinkUser: Boolean,
    ): Flow<LinkAccountState>

    abstract suspend fun callAuthorize(tfsSessionIdToken: String): Flow<LinkAccountState>

    abstract suspend fun handleTFSTokens(tokenResponse: AccessTokenResponseModel?): Flow<Boolean>

    abstract suspend fun handleErrors(errorMessages: String): Flow<LinkAccountState>

    abstract suspend fun accountSummary(): Flow<AccountSummaryState>

    abstract suspend fun getPaymentHistory(
        accountNumber: String?,
        ucid: String?,
    ): Flow<PaymentHistoryState>

    abstract suspend fun submitDevicePrint(authenticateResponseModel: AuthenticateResponseModel): Flow<LinkAccountState>

    abstract fun clearTFSTokens()
}
