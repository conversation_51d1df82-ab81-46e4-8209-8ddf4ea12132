package com.toyota.oneapp.features.pay.tfs.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.pay.tfs.application.AccountSummaryState
import com.toyota.oneapp.features.pay.tfs.application.LinkAccountState
import com.toyota.oneapp.features.pay.tfs.application.PaymentHistoryState
import com.toyota.oneapp.features.pay.tfs.application.TFSAvailabilityState
import com.toyota.oneapp.features.pay.tfs.application.TFSLogic
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class TFSViewModel
    @Inject
    constructor(
        private val tfsLogic: TFSLogic,
        val applicationData: ApplicationData,
        val analyticsLogger: AnalyticsLogger,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : BaseViewModel() {
        val vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicleState().value

        private val _tfsAvailabilityState =
            MutableStateFlow<TFSAvailabilityState>(
                TFSAvailabilityState.Loading,
            )
        private val _tfsAccountSummaryState =
            MutableStateFlow<AccountSummaryState>(
                AccountSummaryState.Loading,
            )
        private val _linkAccountState = MutableStateFlow<LinkAccountState>(LinkAccountState.Loading)
        private val _paymentHistoryState =
            MutableStateFlow<PaymentHistoryState>(
                PaymentHistoryState.Loading,
            )
        val tfsAvailabilityState: StateFlow<TFSAvailabilityState> = _tfsAvailabilityState.asStateFlow()
        val tfsAccountSummaryState: StateFlow<AccountSummaryState> = _tfsAccountSummaryState.asStateFlow()
        val linkAccountState: StateFlow<LinkAccountState> = _linkAccountState.asStateFlow()
        val paymentHistoryState: StateFlow<PaymentHistoryState> = _paymentHistoryState.asStateFlow()

        init {
            checkFeatureFlagForTFS()
        }

        fun checkFeatureFlagForTFS() {
            viewModelScope.launch {
                vehicleInfo?.let {
                    tfsLogic.isFinanceServiceAvailable(vehicleInfo).collect {
                        _tfsAvailabilityState.value = it
                        if (it is TFSAvailabilityState.Success) {
                            if (it.tfsAvailabilityResponse.isTFSFeatureAvailable && it.tfsAvailabilityResponse.isEligibleForTFS) {
                                authenticate()
                            }
                        }
                    }
                }
            }
        }

        fun isDarkMode(): Boolean = oneAppPreferenceModel.isDarkModeEnabled()

        fun authenticate(
            submitDevicePrint: Boolean = false,
            isRelinkUser: Boolean = false,
        ) {
            viewModelScope.launch {
                _linkAccountState.value = LinkAccountState.Loading
                if (isRelinkUser) { // relink account
                    oneAppPreferenceModel.setTfsUnLinkUser(false)
                }
                tfsLogic.callAuthenticate(submitDevicePrint, isRelinkUser).collect {
                    _linkAccountState.value = it
                    if (it is LinkAccountState.GotoSummaryScreen) {
                        accountSummaryDetails()
                    }
                }
            }
        }

        fun getSelectedVehicle(): VehicleInfo {
            vehicleInfo?.let {
                return it
            } ?: kotlin.run {
                return getSelectedVehicle()
            }
        }

        fun authorizeUser() {
            viewModelScope.launch {
                tfsLogic.callAuthorize(oneAppPreferenceModel.getAuthenticateIdToken()).collect {
                    _linkAccountState.value = it
                    if (it is LinkAccountState.GotoSummaryScreen) {
                        accountSummaryDetails()
                    }
                }
            }
        }

        fun submitElectronicConsent() {
            viewModelScope.launch {
                _tfsAccountSummaryState.value = AccountSummaryState.Loading
                tfsLogic.submitElectronicConsent().collect {
                    if (it is LinkAccountState.GotoSummaryScreen) {
                        accountSummaryDetails()
                    } else {
                        _tfsAccountSummaryState.value =
                            AccountSummaryState.Error(
                                ToyotaConstants.EMPTY_STRING,
                                ToyotaConstants.EMPTY_STRING,
                            )
                    }
                }
            }
        }

        fun accountSummaryDetails() {
            viewModelScope.launch {
                tfsLogic.accountSummary().collect {
                    _tfsAccountSummaryState.value = it
                    if (it is AccountSummaryState.Success) {
                        paymentHistory(
                            accountNumber = it.accountSummary?.fullAccountNumber,
                            ucid = it.accountSummary?.customer?.ucid,
                        )
                    }
                }
            }
        }

        fun paymentHistory(
            accountNumber: String?,
            ucid: String?,
        ) {
            viewModelScope.launch {
                tfsLogic.getPaymentHistory(accountNumber = accountNumber, ucid = ucid).collect {
                    _paymentHistoryState.value = it
                }
            }
        }

        fun logFirebaseEventWithParameter(event: String) {
            analyticsLogger.logEventWithParameter(AnalyticsEventParam.BOTTOM_NAV_PAY_TAP, event)
        }

        fun getTfsUnLinkUserStatus(): Boolean {
            return oneAppPreferenceModel.getTfsUnLinkUser()
        }

        fun getTfsAuthIdToken(): String {
            return oneAppPreferenceModel.getAuthenticateIdToken()
        }

        fun updateStateOfLinkAccount() {
            viewModelScope.launch {
                _linkAccountState.value = LinkAccountState.Error(userNotFound = true)
            }
        }

        fun updateAccountSummaryState(state: AccountSummaryState = AccountSummaryState.Loading) {
            viewModelScope.launch {
                _tfsAccountSummaryState.value = state
            }
        }

        fun updateLinkAccountState(state: LinkAccountState = LinkAccountState.Loading) {
            viewModelScope.launch {
                _linkAccountState.value = state
            }
        }
    }
