/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.application

import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletSetupIntent

sealed class WalletSetupIntentState {
    object Loading : WalletSetupIntentState()

    data class Success(
        val response: WalletSetupIntent? = null,
    ) : WalletSetupIntentState()

    data class Error(val errorCode: String?, val errorMessage: String?) : WalletSetupIntentState()
}
