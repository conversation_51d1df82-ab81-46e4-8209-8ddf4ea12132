package com.toyota.oneapp.features.pay.tfs

import com.toyota.oneapp.features.pay.tfs.application.TFSLogic
import com.toyota.oneapp.features.pay.tfs.application.TFSUseCase
import com.toyota.oneapp.features.pay.tfs.dataaccess.repository.TFSDefaultRepository
import com.toyota.oneapp.features.pay.tfs.domain.TFSRepository
import com.toyota.oneapp.features.pay.tfs.presentation.utils.TFSNetworkProviderHelperImpl
import com.toyota.oneapp.features.pay.tfs.presentation.utils.TFSTokenProviderHelper
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class TFSModule {
    @Binds
    abstract fun provideTFSRepository(tfsRepository: TFSDefaultRepository): TFSRepository

    @Binds
    abstract fun provideTFSUseCase(tfsLogic: TFSLogic): TFSUseCase

    @Binds
    abstract fun provideTFSTokenProviderHelper(tfsNetworkProviderHelperImpl: TFSNetworkProviderHelperImpl): TFSTokenProviderHelper
}
