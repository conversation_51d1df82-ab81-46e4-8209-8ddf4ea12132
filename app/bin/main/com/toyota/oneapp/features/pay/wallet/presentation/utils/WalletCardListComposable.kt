/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.presentation.utils

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import com.google.gson.Gson
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.pay.wallet.application.WalletCardData
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.CardTransactions
import com.toyota.oneapp.features.pay.wallet.presentation.SubscriptionCardStack
import com.toyota.oneapp.features.pay.wallet.presentation.WalletViewModel
import com.toyota.oneapp.features.pay.wallet.presentation.evtransactions.NoTransactions
import com.toyota.oneapp.features.pay.wallet.presentation.evtransactions.TransactionCard
import com.toyota.oneapp.features.pay.wallet.presentation.evwallethome.BottomCards

@Composable
fun WalletCard(
    modifier: Modifier = Modifier,
    card: WalletCardData,
    navController: NavController,
) {
    Card(
        modifier
            .fillMaxWidth()
            .height(220.dp)
            .padding(16.dp)
            .clickable {
                val gson = Gson()
                val jsonString =
                    gson.toJson(
                        WalletCardData(
                            cardName = card.cardName,
                            cardNumber = card.cardNumber,
                            isDefault = card.isDefault,
                            cardId = card.cardId,
                            brand = card.brand,
                            expiry = card.expiry,
                        ),
                    )

                navController.navigate("CardTransactionScreen/$jsonString")
            },
        shape = RoundedCornerShape(16.dp),
    ) {
        CardBGImage(brand = card.cardName)
        Column(
            modifier = Modifier.padding(5.dp),
            verticalArrangement = Arrangement.SpaceBetween,
        ) {
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(10.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top,
            ) {
                OACallOut1TextView(
                    text = card.brand,
                    color = Color.White,
                )
                Row {
                    repeat(4) {
                        OACallOut1TextView(
                            text = stringResource(R.string.removeVehicleDot),
                            color = Color.White,
                        )
                    }
                    Spacer(modifier = Modifier.width(3.dp))
                    OACallOut1TextView(
                        text = card.cardNumber,
                        color = Color.White,
                    )
                }
            }
            Row(
                modifier =
                    Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(bottom = 16.dp),
            ) {
                DefaultCardButton()
            }
        }
    }
}

@Composable
fun DefaultCardButton() {
    Box(
        modifier =
            Modifier
                .background(AppTheme.colors.success02, RoundedCornerShape(12.dp))
                .padding(horizontal = 8.dp, vertical = 4.dp),
        contentAlignment = Alignment.Center,
    ) {
        Row {
            Box(
                modifier =
                    Modifier
                        .padding(
                            top = 2.dp,
                            end = 4.dp,
                        ),
            ) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = null,
                    tint = AppTheme.colors.success01,
                    modifier =
                        Modifier
                            .size(16.dp)
                            .border(1.dp, AppTheme.colors.success01, CircleShape)
                            .padding(2.dp),
                )
            }
            Row(
                modifier = Modifier,
            ) {
                Text(
                    text = stringResource(R.string.ev_wallet_default),
                    color = AppTheme.colors.tertiary03,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                )
            }
        }
    }
}

@Composable
fun WalletCardStack(
    cards: List<WalletCardData>,
    navController: NavHostController,
    transactionsCount: Int,
    isHomeScreen: Boolean = false,
    transactions: List<CardTransactions>? = null,
) {
    val mainCard by remember { mutableStateOf<WalletCardData?>(null) }

    LazyColumn(
        modifier =
            Modifier
                .fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(5.dp),
    ) {
        item {
            if (mainCard != null) {
                mainCard?.let {
                    WalletCard(
                        card = it,
                        navController = navController,
                    )
                }
            } else {
                StackWalletCards(cards = cards, navController = navController)
            }
        }
        if (isHomeScreen) {
            if (cards.isEmpty()) {
                item {
                    NoWalletStack {
                        navController.navigate(OAScreen.AddEVCardScreen.route)
                    }
                }
            }
            item {
                BottomCards(transactionsCount, navController)
            }
        } else {
            item {
                Text(
                    text = "Transactions",
                    style = AppTheme.fontStyles.subHeadline1,
                    modifier =
                        Modifier
                            .padding(16.dp),
                    color = AppTheme.colors.tertiary03,
                )
            }
            if (transactions.isNullOrEmpty()) {
                item {
                    NoTransactions()
                }
            } else {
                items(transactions.size) {
                    TransactionCard(transaction = transactions[it])
                }
            }
        }
    }
}

@Composable
fun StackWalletCards(
    cards: List<WalletCardData>,
    navController: NavController,
) {
    // Separate the default card from the rest
    val nonDefaultCards = cards.filter { !it.isDefault }
    val defaultCard = cards.find { it.isDefault }

    // Combine non-default cards with the default card at the end
    val sortedCards =
        if (defaultCard != null) {
            nonDefaultCards + defaultCard
        } else {
            nonDefaultCards
        }

    val heightPerCard = 80 // Height of each card in dp
    val additionalSpace = 100 // Extra space for the last two cards
    val spacerHeight = (sortedCards.size * heightPerCard) + additionalSpace

    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(16.dp),
    ) {
        sortedCards.forEachIndexed { index, card ->
            WalletCard(
                navController = navController,
                card = card,
                modifier =
                    Modifier
                        .offset(y = (index * 60).dp),
            )
        }
        Spacer(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(spacerHeight.dp),
        )
    }
}

@Composable
fun CardBGImage(brand: String) {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .fillMaxHeight(),
    ) {
        var cardBG: Int = R.drawable.amex_ev_card
        if (brand.isNotEmpty()) {
            if (brand.contains(WalletCards.AMEX.cardName)) {
                cardBG = R.drawable.amex_ev_card
            } else if (brand.contains(WalletCards.DISCOVER.cardName)) {
                cardBG = R.drawable.discover_ev_card
            } else if (brand.contains(WalletCards.JCB.cardName)) {
                cardBG = R.drawable.jcb_ev_card
            } else if (brand.contains(WalletCards.UNION.cardName)) {
                cardBG = R.drawable.unionpay_ev_card
            } else if (brand.contains(WalletCards.VISA.cardName)) {
                cardBG = R.drawable.visa_ev_card
            } else if (brand.contains(WalletCards.MASTERCARD.cardName)) {
                cardBG = R.drawable.mastercard_ev_card
            }
        }
        Image(
            painter = painterResource(cardBG),
            contentDescription = stringResource(R.string.ev_wallet_card),
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth,
        )
    }
}

@Composable
fun TransactionWalletCard(
    cardName: String,
    cardNumber: String,
    isDefault: Boolean = false,
    cardId: String,
    expiry: String,
    viewModel: WalletViewModel,
) {
    Card(
        modifier =
            Modifier
                .fillMaxWidth()
                .height(220.dp)
                .padding(16.dp),
        shape = RoundedCornerShape(16.dp),
    ) {
        CardBGImage(brand = cardName)
        Column(
            modifier = Modifier.padding(5.dp),
            verticalArrangement = Arrangement.SpaceBetween,
        ) {
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(10.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top,
            ) {
                Column {
                    OACallOut1TextView(
                        text = viewModel.formatBrandName(cardName),
                        color = Color.White,
                    )
                    OACallOut1TextView(
                        text = "Exp ${formatExpDate(expiry)}",
                        color = Color.White,
                    )
                }
                Row {
                    repeat(4) {
                        OACallOut1TextView(
                            text = stringResource(R.string.removeVehicleDot),
                            color = Color.White,
                        )
                    }
                    Spacer(modifier = Modifier.width(3.dp))
                    OACallOut1TextView(
                        text = cardNumber,
                        color = Color.White,
                    )
                }
            }
            Row(
                modifier =
                    Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(bottom = 16.dp),
            ) {
                if (isDefault) {
                    Box(
                        modifier =
                            Modifier
                                .clickable {
                                    viewModel.setDefaultCard(cardId, isDefault = true)
                                },
                    ) {
                        DefaultCardButton()
                    }
                } else {
                    MakeDefaultButton(
                        viewModel = viewModel,
                        cardId = cardId,
                    )
                }
            }
        }
    }
}

@Composable
fun MakeDefaultButton(
    viewModel: WalletViewModel,
    cardId: String,
) {
    Box(
        modifier =
            Modifier
                .background(Color.White, RoundedCornerShape(12.dp))
                .padding(horizontal = 8.dp, vertical = 4.dp)
                .clickable {
                    viewModel.setDefaultCard(cardId)
                },
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = stringResource(R.string.ev_wallet_make_default),
            color = Color(0xFF1E88E5),
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
        )
    }
}

fun formatExpDate(date: String): String = date.substring(0, 2) + "/" + date.substring(2)

@Composable
fun NoWalletStack(onClick: () -> Unit) {
    SubscriptionCardStack(
        headline = stringResource(R.string.ev_wallet_add_card),
        onClick = onClick,
    )
}

@Preview
@Composable
private fun NoWalletStackPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode = themeMode) {
        NoWalletStack {}
    }
}
