package com.toyota.oneapp.features.pay.wallet.presentation

import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.pay.wallet.application.CardTransactionState
import com.toyota.oneapp.features.pay.wallet.application.CollectEVWalletState
import com.toyota.oneapp.features.pay.wallet.application.DeleteCardState
import com.toyota.oneapp.features.pay.wallet.application.SetDefaultCardState
import com.toyota.oneapp.features.pay.wallet.application.WalletCardData
import com.toyota.oneapp.features.pay.wallet.application.WalletFFState
import com.toyota.oneapp.features.pay.wallet.application.WalletLogic
import com.toyota.oneapp.features.pay.wallet.application.WalletTransactionState
import com.toyota.oneapp.features.pay.wallet.presentation.utils.WalletCards
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class WalletViewModel
    @Inject
    constructor(
        private val walletLogic: WalletLogic,
        applicationData: ApplicationData,
        val analyticsLogger: AnalyticsLogger,
        oneAppPreferenceModel: OneAppPreferenceModel,
    ) : BaseViewModel() {
        val preferenceModel: OneAppPreferenceModel = oneAppPreferenceModel
        val vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()
        val guid: String = preferenceModel.getGuid()

        private val _evWalletState =
            MutableStateFlow<CollectEVWalletState>(
                CollectEVWalletState.Loading,
            )
        val evWalletState: StateFlow<CollectEVWalletState> = _evWalletState.asStateFlow()
        private val _walletPresenceState = MutableStateFlow<WalletFFState>(WalletFFState.Loading)
        val walletPresenceState: StateFlow<WalletFFState> = _walletPresenceState.asStateFlow()

        private val _walletTransactionState =
            MutableStateFlow<WalletTransactionState>(
                WalletTransactionState.Loading,
            )
        val walletTransactionState: StateFlow<WalletTransactionState> = _walletTransactionState.asStateFlow()

        private val _cardTransactionState =
            MutableStateFlow<CardTransactionState>(
                CardTransactionState.Loading,
            )
        val cardTransactionState: StateFlow<CardTransactionState> = _cardTransactionState.asStateFlow()

        private val _setDefaultCardState =
            MutableStateFlow<SetDefaultCardState>(
                SetDefaultCardState.StandBy,
            )
        val setDefaultCardState: StateFlow<SetDefaultCardState> = _setDefaultCardState.asStateFlow()

        private val _deleteCardState = MutableStateFlow<DeleteCardState>(DeleteCardState.StandBy())
        val deleteCardState: StateFlow<DeleteCardState> = _deleteCardState

        init {
            viewModelScope.launch {
                applicationData.getSelectedVehicleState().collectLatest { vehicle ->
                    vehicle?.let {
                        checkWalletFF(vehicle)
                    }
                }
            }
        }

        fun checkWalletFF(vehicle: VehicleInfo) {
            viewModelScope.launch {
                walletLogic.isWalletPresent(vehicle).collect { resource ->
                    _walletPresenceState.value = resource
                }
            }
        }

        fun collectEVWallet() {
            viewModelScope.launch {
                walletLogic.getEVWallet().collect { resource ->
                    _evWalletState.value = resource
                }
            }
        }

        fun logFirebaseEventWithParameter(event: String) {
            analyticsLogger.logEventWithParameter(AnalyticsEventParam.BOTTOM_NAV_PAY_TAP, event)
        }

        fun checkWallet() {
            viewModelScope.launch {
                walletLogic.fetchWallet().collect { resource ->
                    _walletPresenceState.value = resource
                }
            }
        }

        fun checkWalletTransactions() {
            viewModelScope.launch {
                walletLogic.getWalletTransactions().collect { resource ->
                    _walletTransactionState.value = resource
                }
            }
        }

        fun checkCardTransactions(
            last4: String,
            expiry: String,
        ) {
            viewModelScope.launch {
                walletLogic
                    .getCardTransactions(
                        last4 = last4,
                        expiry = expiry,
                    ).collect { resource ->
                        _cardTransactionState.value = resource
                    }
            }
        }

        fun setDefaultCard(
            paymentMethodId: String,
            isDefault: Boolean = false,
        ) {
            viewModelScope.launch {
                if (isDefault) {
                    _setDefaultCardState.value = SetDefaultCardState.IsDefault
                } else {
                    _setDefaultCardState.value = SetDefaultCardState.Loading
                    walletLogic
                        .setEvDefaultCard(
                            paymentMethod = paymentMethodId,
                        ).collect { resource ->
                            _setDefaultCardState.value = resource
                        }
                }
            }
        }

        fun loadDeletion() {
            viewModelScope.launch {
                _deleteCardState.value = DeleteCardState.Loading()
            }
        }

        fun deleteCard(
            paymentMethodId: String,
            isDefault: Boolean = false,
        ) {
            viewModelScope.launch {
                walletLogic
                    .deleteEVCard(
                        paymentMethod = paymentMethodId,
                        isDefault = isDefault,
                    ).collect { resource ->
                        _deleteCardState.value = resource
                    }
            }
        }

        fun confirmDeletion() {
            viewModelScope.launch {
                _deleteCardState.value = DeleteCardState.Confirm()
            }
        }

        fun cancelDeletion() {
            viewModelScope.launch {
                _deleteCardState.value = DeleteCardState.StandBy()
            }
        }

        fun getSingleCard(card: String?): WalletCardData {
            var gson = Gson()
            return gson.fromJson(card, WalletCardData::class.java)
        }

        fun formatBrandName(brand: String): String {
            if (brand.contains(WalletCards.AMEX.cardName)) {
                return "American Express"
            } else if (brand.contains(WalletCards.VISA.cardName)) {
                return "Visa"
            } else if (brand.contains(WalletCards.MASTERCARD.cardName)) {
                return "MasterCard"
            } else if (brand.contains(WalletCards.JCB.cardName)) {
                return "Jcb"
            } else if (brand.contains(WalletCards.DISCOVER.cardName)) {
                return "Discover"
            } else if (brand.contains(WalletCards.UNION.cardName)) {
                return "Union"
            } else {
                return ""
            }
        }

        fun formatExpiry(
            expMonth: Int,
            expYear: Int,
        ): String {
            if (expMonth < 10) {
                val expMonthFormatted = "0$expMonth"
                return "$expMonthFormatted$expYear"
            } else {
                return "$expMonth$expYear"
            }
        }
    }
