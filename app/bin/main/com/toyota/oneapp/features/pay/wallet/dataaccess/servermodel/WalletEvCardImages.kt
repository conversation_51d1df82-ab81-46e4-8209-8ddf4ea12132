package com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel

import com.toyota.oneapp.features.pay.wallet.domain.model.WalletEvCardLogoResponse

data class WalletEvCardImages(
    val payload: Payload,
    val status: Status,
)

data class Payload(
    val channelConfig: ChannelConfig,
    val images: Images,
)

data class ChannelConfig(
    val publicKey: String,
)

data class Images(
    val wallet: Wallet,
)

data class Wallet(
    val amex: CardDetails,
    val default: CardDetails,
    val discover: CardDetails,
    val jcb: CardDetails,
    val mastercard: CardDetails,
    val unionpay: CardDetails,
    val visa: CardDetails,
)

data class CardDetails(
    val large: String,
    val small: String,
)

fun WalletEvCardImages.toUIModel(): WalletEvCardLogoResponse {
    return WalletEvCardLogoResponse(
        wallet = payload.images.wallet,
    )
}
