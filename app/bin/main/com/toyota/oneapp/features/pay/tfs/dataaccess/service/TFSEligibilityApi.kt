package com.toyota.oneapp.features.pay.tfs.dataaccess.service

import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.BannerResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.FinancialServiceEligibilityModel
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Headers

interface TFSEligibilityApi {
    @Headers("CONTENT-TYPE: application/json")
    @GET("/oneapi/v1/banners")
    suspend fun banner(): Response<BannerResponseModel>

    @GET("/oneapi/v2/customer/eligibility")
    suspend fun financialServiceEligibilityCheck(
        @Header("vin") vin: String,
    ): Response<FinancialServiceEligibilityModel?>
}
