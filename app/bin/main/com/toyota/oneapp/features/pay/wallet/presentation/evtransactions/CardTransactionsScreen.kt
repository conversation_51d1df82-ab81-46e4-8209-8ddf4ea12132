/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.presentation.evtransactions

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.Scaffold
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OAAppBar
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.pay.wallet.application.CardTransactionState
import com.toyota.oneapp.features.pay.wallet.application.DeleteCardState
import com.toyota.oneapp.features.pay.wallet.application.SetDefaultCardState
import com.toyota.oneapp.features.pay.wallet.application.WalletCardData
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.CardTransactions
import com.toyota.oneapp.features.pay.wallet.presentation.WalletViewModel
import com.toyota.oneapp.features.pay.wallet.presentation.utils.BottomSheetContent
import com.toyota.oneapp.features.pay.wallet.presentation.utils.DeleteEVCardBottomSheet
import com.toyota.oneapp.features.pay.wallet.presentation.utils.TransactionWalletCard
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun CardTransactionsScreen(
    navController: NavHostController,
    viewModel: WalletViewModel = hiltViewModel<WalletViewModel>(),
    card: String?,
) {
    BackHandler {
        navController.navigate(OAScreen.Pay.route)
    }
    var mainCard = viewModel.getSingleCard(card)
    viewModel.checkCardTransactions(
        last4 = mainCard.cardNumber,
        expiry = mainCard.expiry,
    )

    val sheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            confirmValueChange = { false },
        )
    val scope = rememberCoroutineScope()

    val cardState = viewModel.cardTransactionState.collectAsState().value
    val defaultState = viewModel.setDefaultCardState.collectAsState().value
    val deleteCardState = viewModel.deleteCardState.collectAsState().value

    when (defaultState) {
        is SetDefaultCardState.Success ->
            {
                mainCard.isDefault = true
            }
        else -> {}
    }

    when (cardState) {
        is CardTransactionState.Loading -> {
            ShowProgressIndicator(dialogState = true)
        } is CardTransactionState.Success -> {
            when (deleteCardState) {
                is DeleteCardState.StandBy -> {
                    CardState(
                        isDefault = mainCard.isDefault,
                        card = mainCard,
                        viewModel = viewModel,
                        navController = navController,
                    )
                } is DeleteCardState.Confirm -> {
                    DeleteCardSegmentConfirm(
                        scope,
                        sheetState,
                        mainCard,
                        viewModel,
                        navController,
                    )
                } is DeleteCardState.Loading -> {
                    ShowProgressIndicator(dialogState = true)
                    CardState(
                        isDefault = mainCard.isDefault,
                        card = mainCard,
                        viewModel = viewModel,
                        navController = navController,
                    )
                } is DeleteCardState.Success -> {
                    DeleteCardSegmentSuccess(
                        scope,
                        sheetState,
                        navController,
                        mainCard,
                        viewModel,
                    )
                } is DeleteCardState.Error -> {
                    var defaultCardError = false
                    if (deleteCardState.errorMessage.equals("defaultcarderror")) {
                        defaultCardError = true
                    }
                    DeleteCardSegmentError(
                        scope,
                        sheetState,
                        navController,
                        mainCard,
                        viewModel,
                        defaultCardError,
                    )
                }
            }
        }
        else -> {}
    }
}

@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun DeleteCardSegmentConfirm(
    scope: CoroutineScope,
    sheetState: ModalBottomSheetState,
    mainCard: WalletCardData,
    viewModel: WalletViewModel,
    navController: NavHostController,
) {
    scope.launch { sheetState.show() }
    ModalBottomSheetLayout(
        sheetState = sheetState,
        modifier = Modifier.background(Color.Transparent),
        sheetBackgroundColor = AppTheme.colors.tertiary15,
        sheetContent = {
            DeleteEVCardBottomSheet(
                onCancelClick = {
                    scope.launch {
                        viewModel.cancelDeletion()
                        sheetState.hide()
                    }
                },
                onDoneClick = {
                    scope.launch {
                        viewModel.loadDeletion()
                        viewModel.deleteCard(mainCard.cardId, mainCard.isDefault)
                        sheetState.hide()
                    }
                },
            )
        },
    ) {
        CardState(
            isDefault = mainCard.isDefault,
            card = mainCard,
            viewModel = viewModel,
            navController = navController,
        )
    }
}

@SuppressLint("CoroutineCreationDuringComposition")
@Composable
private fun DeleteCardSegmentSuccess(
    scope: CoroutineScope,
    sheetState: ModalBottomSheetState,
    navController: NavHostController,
    mainCard: WalletCardData,
    viewModel: WalletViewModel,
) {
    scope.launch { sheetState.show() }
    ModalBottomSheetLayout(
        sheetState = sheetState,
        modifier =
            Modifier.background(Color.Transparent),
        sheetBackgroundColor = AppTheme.colors.tertiary15,
        sheetContent = {
            BottomSheetContent(
                onDoneClick = {
                    scope.launch {
                        navController.popBackStack()
                        sheetState.hide()
                    }
                },
                title = stringResource(id = R.string.success),
                description = "This card has been deleted",
                image = R.drawable.ic_baseline_done_primary_24,
            )
        },
    ) {
        CardState(
            isDefault = mainCard.isDefault,
            card = mainCard,
            viewModel = viewModel,
            navController = navController,
        )
    }
}

@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun DeleteCardSegmentError(
    scope: CoroutineScope,
    sheetState: ModalBottomSheetState,
    navController: NavHostController,
    mainCard: WalletCardData,
    viewModel: WalletViewModel,
    defaultCardError: Boolean,
) {
    var errorDescription = "An Error has occurred with deleting this card"
    if (defaultCardError) {
        errorDescription = stringResource(R.string.wallet_something_went_wrong)
    }
    scope.launch { sheetState.show() }
    ModalBottomSheetLayout(
        sheetState = sheetState,
        modifier =
            Modifier
                .background(Color.Transparent),
        sheetBackgroundColor = AppTheme.colors.tertiary15,
        sheetContent = {
            BottomSheetContent(
                onDoneClick = {
                    scope.launch {
                        navController.popBackStack()
                        sheetState.hide()
                    }
                },
                title = stringResource(R.string.software_error),
                description = errorDescription,
                image = R.drawable.ic_alert_no_background,
            )
        },
    ) {
        CardState(
            isDefault = mainCard.isDefault,
            card = mainCard,
            viewModel = viewModel,
            navController = navController,
        )
    }
}

@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun CardState(
    isDefault: Boolean,
    card: WalletCardData,
    viewModel: WalletViewModel,
    navController: NavHostController,
) {
    val scope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState(initialValue = ModalBottomSheetValue.Hidden)

    when (val cardState = viewModel.cardTransactionState.collectAsState().value) {
        is CardTransactionState.Loading -> {
            ShowProgressIndicator(dialogState = true)
        }
        is CardTransactionState.Success -> {
            val transactions = cardState.transactions
            when (viewModel.setDefaultCardState.collectAsState().value) {
                is SetDefaultCardState.StandBy -> {
                    CardScreenLayout(
                        isDefault = isDefault,
                        card = card,
                        viewModel = viewModel,
                        navController = navController,
                        transactions = transactions,
                    )
                } is SetDefaultCardState.Loading -> {
                    ShowProgressIndicator(dialogState = true)
                    CardScreenLayout(
                        isDefault = isDefault,
                        card = card,
                        viewModel = viewModel,
                        navController = navController,
                        transactions = transactions,
                    )
                } is SetDefaultCardState.Success -> {
                    scope.launch { sheetState.show() }
                    ModalBottomSheetLayout(
                        sheetState = sheetState,
                        modifier =
                            Modifier
                                .background(Color.Transparent),
                        sheetBackgroundColor = AppTheme.colors.tertiary15,
                        sheetContent = {
                            BottomSheetContent(
                                onDoneClick = { scope.launch { sheetState.hide() } },
                                title = stringResource(id = R.string.success),
                                description = "The ${card.brand} card ending in ${card.cardNumber} was set as default to your wallet.",
                                image = R.drawable.ic_baseline_done_primary_24,
                            )
                        },
                    ) {
                        CardScreenLayout(
                            isDefault = true,
                            card = card,
                            viewModel = viewModel,
                            navController = navController,
                            transactions = transactions,
                        )
                    }
                } is SetDefaultCardState.IsDefault -> {
                    scope.launch { sheetState.show() }
                    ModalBottomSheetLayout(
                        sheetState = sheetState,
                        modifier =
                            Modifier
                                .background(Color.Transparent),
                        sheetBackgroundColor = AppTheme.colors.tertiary15,
                        sheetContent = {
                            BottomSheetContent(
                                onDoneClick = { scope.launch { sheetState.hide() } },
                                title = stringResource(id = R.string.ev_card_default_title),
                                description = "The ${card.brand} card ending in ${card.cardNumber} is already set to default",
                                image = R.drawable.ic_baseline_done_primary_24,
                            )
                        },
                    ) {
                        CardScreenLayout(
                            isDefault = true,
                            card = card,
                            viewModel = viewModel,
                            navController = navController,
                            transactions = transactions,
                        )
                    }
                }
                else -> {}
            }
        }
        else -> {}
    }
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun CardScreenLayout(
    isDefault: Boolean,
    card: WalletCardData,
    viewModel: WalletViewModel,
    navController: NavHostController,
    transactions: List<CardTransactions>?,
) {
    Scaffold(
        topBar = {
            OAAppBar(
                modifier =
                    Modifier
                        .padding(start = 16.dp, top = 9.dp, end = 16.dp),
                title = card.brand,
                onBack = {
                    navController.popBackStack()
                },
                actionWidget = {
                    Box(
                        modifier =
                            Modifier
                                .size(40.dp)
                                .wrapContentSize(Alignment.Center)
                                .clickable {
                                    viewModel.confirmDeletion()
                                },
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_trash),
                            contentDescription = stringResource(R.string.delete_card),
                            modifier =
                                Modifier
                                    .size(12.dp),
                            colorFilter = ColorFilter.tint(AppTheme.colors.button02a),
                        )
                    }
                },
                testTagId = AccessibilityId.ID_EV_WALLET_TRANSACTION_SCREEN_BACK_BTN,
            )
        },
        backgroundColor = AppTheme.colors.tertiary12,
        content = {
            LazyColumn(
                modifier =
                    Modifier
                        .fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(5.dp),
            ) {
                item {
                    TransactionWalletCard(
                        cardName = card.cardName,
                        cardNumber = card.cardNumber,
                        cardId = card.cardId,
                        expiry = card.expiry,
                        isDefault = isDefault,
                        viewModel = viewModel,
                    )
                }
                if (!transactions.isNullOrEmpty()) {
                    item {
                        OASubHeadLine1TextView(
                            text = stringResource(R.string.transactions),
                            color = AppTheme.colors.tertiary03,
                            modifier =
                                Modifier
                                    .padding(16.dp),
                        )
                    }

                    items(transactions.size) {
                        TransactionCard(transaction = transactions[it])
                    }
                }
            }
        },
    )
}
