/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.application

import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.Status

sealed class DeleteCardState {
    data class StandBy(
        val status: Status? = null,
    ) : DeleteCardState()

    data class Confirm(
        val status: Status? = null,
    ) : DeleteCardState()

    data class Loading(
        val status: Status? = null,
    ) : DeleteCardState()

    data class Success(
        val response: Unit? = null,
    ) : DeleteCardState()

    data class Error(
        val errorCode: String?,
        val errorMessage: String?,
    ) : DeleteCardState()
}
