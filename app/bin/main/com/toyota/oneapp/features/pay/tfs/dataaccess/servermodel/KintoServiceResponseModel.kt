package com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel

import com.toyota.oneapp.features.pay.tfs.domain.model.KintoModel

data class KintoServiceResponseModel(
    val status: String?,
    val customer: ArrayList<Customer>,
    val errors: ArrayList<Errors>?,
    val error: String,
)

data class Customer(
    val upid: String,
    val idVal: String,
    val Service: ArrayList<ServiceDTO>,
)

data class ServiceDTO(
    val serviceId: String,
    val serviceTyp: String,
)

fun KintoServiceResponseModel.toUIModel(upid: String?): KintoModel {
    var customerKintoData: Customer? = null
    var serviceData: ServiceDTO? = null
    customer.forEach {
        if (it.upid == upid) {
            customerKintoData = it
        }
    }

    customerKintoData?.Service?.forEach {
        if (it.serviceTyp == "Kinto Payment") {
            serviceData = it
        }
    }

    return KintoModel(
        serviceId = serviceData?.serviceId,
        ssn = extractLast4Digits(customerKintoData?.idVal),
    )
}

fun extractLast4Digits(str: String?): String {
    return if (str == null) {
        ""
    } else {
        if (str.length <= 4) {
            str
        } else {
            str.substring(str.length - 4)
        }
    }
}
