/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.presentation.addevwaletcard

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.Scaffold
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import apptentive.com.android.util.InternalUseOnly
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.NavTitleSection
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.pay.wallet.application.AddEvCardState
import com.toyota.oneapp.features.pay.wallet.application.PubKeySetupState
import com.toyota.oneapp.features.pay.wallet.presentation.AddEVWalletCardViewModel
import com.toyota.oneapp.features.pay.wallet.presentation.utils.BottomSheetContent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class, ExperimentalComposeUiApi::class, InternalUseOnly::class)
@SuppressLint("UnusedMaterialScaffoldPaddingParameter", "CoroutineCreationDuringComposition")
@Composable
fun AddEVWalletCardScreen(
    navController: NavHostController,
    viewModel: AddEVWalletCardViewModel = hiltViewModel<AddEVWalletCardViewModel>(),
) {
    BackHandler {
        navController.navigate(OAScreen.Pay.route)
    }

    val sheetState = rememberModalBottomSheetState(initialValue = ModalBottomSheetValue.Hidden)
    val scope = rememberCoroutineScope()
    val keyboardController = LocalSoftwareKeyboardController.current

    val pubKeyState = viewModel.pubKeySetupState.collectAsState().value
    val addCardState = viewModel.addEVCardState.collectAsState().value

    when (pubKeyState) {
        is PubKeySetupState.Loading -> {
            ShowProgressIndicator(dialogState = true)
            viewModel.getPubKey()
        }
        else -> {}
    }

    when (addCardState) {
        is AddEvCardState.StandBy -> {
            Scaffold(
                backgroundColor = AppTheme.colors.tertiary12,
                content = {
                    Column(
                        modifier =
                            Modifier
                                .verticalScroll(rememberScrollState())
                                .background(AppTheme.colors.tertiary12),
                    ) {
                        NavTitleSection(
                            navController,
                            stringResource(id = R.string.add_card),
                            stringResource(id = R.string.add_card),
                        )
                        AddCardForm()
                    }
                },
            )
        } is AddEvCardState.Loading -> {
            AddCardLoadingBlock(
                navController,
                stringResource(id = R.string.add_card),
            )
        }
        is AddEvCardState.Success -> {
            val last4 = addCardState.last4
            AddCardSuccessBlock(
                scope,
                sheetState,
                keyboardController,
                navController,
                last4,
            )
        } is AddEvCardState.Error -> {
            AddCardErrorBlock(
                scope = scope,
                sheetState = sheetState,
                navController = navController,
                viewModel = viewModel,
            )
        }
    }
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun AddCardLoadingBlock(
    navController: NavHostController,
    addCardTitle: String,
) {
    ShowProgressIndicator(dialogState = true)
    Scaffold(
        backgroundColor = AppTheme.colors.tertiary12,
        content = {
            Column(
                modifier =
                    Modifier
                        .verticalScroll(rememberScrollState())
                        .background(AppTheme.colors.tertiary12),
            ) {
                NavTitleSection(
                    navController,
                    addCardTitle,
                    addCardTitle,
                )
                AddCardForm()
            }
        },
    )
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter", "CoroutineCreationDuringComposition")
@OptIn(ExperimentalMaterialApi::class)
@Composable
fun AddCardErrorBlock(
    scope: CoroutineScope,
    sheetState: ModalBottomSheetState,
    navController: NavHostController,
    viewModel: AddEVWalletCardViewModel,
) {
    scope.launch { sheetState.show() }
    ModalBottomSheetLayout(
        sheetState = sheetState,
        modifier =
            Modifier
                .background(Color.Transparent),
        sheetBackgroundColor = AppTheme.colors.tertiary15,
        sheetContent = {
            BottomSheetContent(
                onDoneClick = {
                    scope.launch {
                        sheetState.hide()
                    }
                },
                title = stringResource(id = R.string.software_error),
                description = viewModel.addCardError,
                image = R.drawable.ic_alert_no_background,
            )
        },
    ) {
        Scaffold(
            backgroundColor = AppTheme.colors.tertiary12,
            content = {
                Column(
                    modifier =
                        Modifier
                            .verticalScroll(rememberScrollState())
                            .background(AppTheme.colors.tertiary12),
                ) {
                    NavTitleSection(
                        navController,
                        stringResource(id = R.string.add_card),
                        "Add EV Card",
                    )
                    AddCardForm()
                }
            },
        )
    }
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter", "CoroutineCreationDuringComposition")
@OptIn(ExperimentalComposeUiApi::class, ExperimentalMaterialApi::class)
@Composable
fun AddCardSuccessBlock(
    scope: CoroutineScope,
    sheetState: ModalBottomSheetState,
    keyboardController: SoftwareKeyboardController?,
    navController: NavHostController,
    last4: String?,
) {
    scope.launch {
        sheetState.show()
    }
    ModalBottomSheetLayout(
        sheetState = sheetState,
        modifier =
            Modifier
                .background(Color.Transparent),
        sheetBackgroundColor = AppTheme.colors.tertiary15,
        sheetContent = {
            BottomSheetContent(
                onDoneClick = {
                    keyboardController?.hide()
                    scope.launch {
                        navController.popBackStack()
                        sheetState.hide()
                    }
                },
                title = stringResource(id = R.string.Login_success),
                description = "The card ending in $last4 was added to your wallet",
                image = R.drawable.ic_baseline_done_primary_24,
            )
        },
    ) {
        Scaffold(
            backgroundColor = AppTheme.colors.tertiary12,
            content = {
                Column(
                    modifier =
                        Modifier
                            .verticalScroll(rememberScrollState())
                            .background(AppTheme.colors.tertiary12),
                ) {
                    NavTitleSection(
                        navController,
                        stringResource(id = R.string.add_card),
                        "Add EV Card",
                    )
                    AddCardForm()
                }
            },
        )
    }
}
