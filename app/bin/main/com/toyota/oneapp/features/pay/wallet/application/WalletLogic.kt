package com.toyota.oneapp.features.pay.wallet.application

import apptentive.com.android.util.InternalUseOnly
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.EVWalletTransactions
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.toCardTransactionsList
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.toUIModel
import com.toyota.oneapp.features.pay.wallet.domain.model.WalletDetailEvCardResponse
import com.toyota.oneapp.features.pay.wallet.domain.model.WalletDetailResponse
import com.toyota.oneapp.features.pay.wallet.domain.model.WalletEvCard
import com.toyota.oneapp.features.pay.wallet.domain.model.WalletEvCardLogoResponse
import com.toyota.oneapp.features.pay.wallet.domain.repository.WalletRepository
import com.toyota.oneapp.features.pay.wallet.presentation.utils.WalletCards
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.NetworkStatus
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class WalletLogic
    @Inject
    constructor(
        private val repository: WalletRepository,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val analyticsLogger: AnalyticsLogger,
    ) : WalletUseCase() {
        override suspend fun isWalletPresent(vehicleInfo: VehicleInfo): Flow<WalletFFState> =
            flow {
                if (vehicleInfo.isEvVehicle) {
                    if (vehicleInfo.isFeatureEnabled(Feature.WALLET)) {
                        fetchWallet().collect {
                            emit(it)
                        }
                    } else {
                        isWalletEnableWithSubscription(vehicleInfo).collect {
                            emit(it)
                        }
                    }
                } else {
                    isWalletEnableWithSubscription(vehicleInfo).collect {
                        emit(it)
                    }
                }
            }

        override suspend fun isWalletEnableWithSubscription(vehicleInfo: VehicleInfo): Flow<WalletFFState> =
            flow {
                if (vehicleInfo.isFeatureEnabled(Feature.PAID_SUBSCRIPTION) && vehicleInfo.isPrimarySubscriber) {
                    emit(
                        WalletFFState.Success(
                            isWalletWithSubscription = true,
                            isEVVehicle =
                                vehicleInfo.isEvVehicle &&
                                    vehicleInfo.isFeatureEnabled(
                                        Feature.WALLET,
                                    ),
                        ),
                    )
                } else {
                    emit(WalletFFState.NoWalletFeatureAvailable)
                }
            }

        override fun logFirebaseEventWithParameter(event: String) {
            analyticsLogger.logEventWithParameter(
                paramValue = AnalyticsEventParam.BOTTOM_NAV_PAY_TAP,
                event = event,
            )
        }

        override suspend fun fetchWallet(): Flow<WalletFFState> =
            flow {
                val response = repository.fetchWalletDetails()
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        val walletDetailResponse = it.data?.payload
                        if (walletDetailResponse?.paymentMethods != null && walletDetailResponse.paymentMethods.isNotEmpty()) {
                            it.data?.payload?.paymentMethods?.forEach { paymentMethod ->
                                if (paymentMethod.default) {
                                    fetchWalletImages(paymentMethod.card.brand).collect { images ->
                                        if (images is WalletCardImagesState.Success) {
                                            evCardLogo(paymentMethod.card.brand, images.walletImages).collect { walletEvCardInfo ->
                                                if (walletEvCardInfo != null) {
                                                    emit(
                                                        WalletFFState.Success(
                                                            walletDetails =
                                                                WalletDetailEvCardResponse(
                                                                    walletDetail = paymentMethod.toUIModel(),
                                                                    evCardImages = walletEvCardInfo,
                                                                ),
                                                            isEVVehicle = true,
                                                        ),
                                                    )
                                                }
                                            }
                                        } else {
                                            if (images is WalletCardImagesState.Error) {
                                                emit(
                                                    WalletFFState.Success(
                                                        walletDetails =
                                                            WalletDetailEvCardResponse(
                                                                walletDetail = paymentMethod.toUIModel(),
                                                                evCardImages = WalletEvCard(),
                                                            ),
                                                        isEVVehicle = true,
                                                    ),
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            emit(
                                WalletFFState.Success(
                                    walletDetails =
                                        WalletDetailEvCardResponse(
                                            walletDetail = WalletDetailResponse(),
                                            evCardImages = WalletEvCard(),
                                        ),
                                    isEVVehicle = true,
                                ),
                            )
                        }
                    } else if (it.status == NetworkStatus.FAILED) {
                        emit(
                            WalletFFState.Success(
                                walletDetails =
                                    WalletDetailEvCardResponse(
                                        walletDetail = WalletDetailResponse(),
                                        evCardImages = WalletEvCard(),
                                    ),
                                isEVVehicle = true,
                            ),
                        )
                    }
                }
            }

        override suspend fun fetchWalletImages(brand: String): Flow<WalletCardImagesState> =
            flow {
                val response = repository.fetchEvWalletCardImages()
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        it.data?.toUIModel()?.let { walletImages ->
                            emit(
                                WalletCardImagesState.Success(walletImages = walletImages),
                            )
                        }
                    } else {
                        emit(
                            WalletCardImagesState.Error(
                                errorCode = it.responseCode,
                                errorMessage = it.message,
                            ),
                        )
                    }
                }
            }

        override fun getWalletTransactions(): Flow<WalletTransactionState> =
            flow {
                val response = repository.fetchWalletTransactions()
                response.let {
                    val transactions = it.data
                    if (it.status == NetworkStatus.SUCCESS && transactions != null) {
                        val data: EVWalletTransactions = transactions
                        emit(
                            WalletTransactionState.Success(
                                transactions = data.payload.transactions?.toCardTransactionsList(),
                            ),
                        )
                    } else {
                        emit(
                            WalletTransactionState.Error(
                                errorCode = it.responseCode,
                                errorMessage = it.message,
                            ),
                        )
                    }
                }
            }

        override fun getEVWallet(): Flow<CollectEVWalletState> =
            flow {
                var defaultCard: WalletDetailResponse? = null
                var cardList: MutableList<WalletDetailResponse?> = emptyList<WalletDetailResponse>().toMutableList()
                val response = repository.fetchWalletDetails()
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        it.data?.payload?.paymentMethods?.forEach { walletPaymentMethod ->
                            if (walletPaymentMethod.default) {
                                defaultCard = walletPaymentMethod.toUIModel()
                                cardList.add(defaultCard)
                            } else {
                                cardList.add(walletPaymentMethod.toUIModel())
                            }
                        }
                        emit(
                            CollectEVWalletState.Success(
                                defaultCard = defaultCard,
                                evWallet = cardList,
                            ),
                        )
                    } else {
                        emit(
                            CollectEVWalletState.Error(
                                errorCode = it.responseCode,
                                errorMessage = it.message,
                            ),
                        )
                    }
                }
            }

        override fun getCardTransactions(
            last4: String,
            expiry: String,
        ): Flow<CardTransactionState> =
            flow {
                val response = repository.fetchCardTransactions(last4 = last4, expiry = expiry)
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        emit(
                            CardTransactionState.Success(
                                transactions = it.data?.payload?.transactions?.toCardTransactionsList(),
                            ),
                        )
                    } else {
                        emit(
                            CardTransactionState.Error(
                                errorCode = it.responseCode,
                                errorMessage = it.message,
                            ),
                        )
                    }
                }
            }

        override fun fetchClientSecret(): Flow<WalletSetupIntentState> =
            flow {
                val response = repository.fetchWalletSetupIntent()
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        emit(
                            WalletSetupIntentState.Success(response = it.data!!),
                        )
                    } else {
                        emit(
                            WalletSetupIntentState.Error(
                                errorCode = it.responseCode,
                                errorMessage = it.message,
                            ),
                        )
                    }
                }
            }

        @OptIn(InternalUseOnly::class)
        override fun fetchPubKey(): Flow<PubKeySetupState> =
            flow {
                val response = repository.fetchEvWalletCardImages()
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        emit(
                            PubKeySetupState.Success(response = it.data),
                        )
                    } else {
                        emit(
                            PubKeySetupState.Error(
                                errorCode = it.responseCode,
                                errorMessage = it.message,
                            ),
                        )
                    }
                }
            }

        override fun deleteEVCard(
            paymentMethod: String,
            isDefault: Boolean,
        ): Flow<DeleteCardState> =
            flow {
                if (isDefault) {
                    emit(
                        DeleteCardState.Error(
                            errorCode = null,
                            errorMessage = DEFAULT_CARD_ERROR,
                        ),
                    )
                } else {
                    val response = repository.deleteEvCard(paymentMethod)
                    response.let {
                        if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                            emit(
                                DeleteCardState.Success(response = it.data!!),
                            )
                        } else {
                            emit(
                                DeleteCardState.Error(
                                    errorCode = it.responseCode,
                                    errorMessage = it.message,
                                ),
                            )
                        }
                    }
                }
            }

        override fun setEvDefaultCard(paymentMethod: String): Flow<SetDefaultCardState> =
            flow {
                val response = repository.setDefaultCard(paymentMethod)
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        emit(
                            SetDefaultCardState.Success(response = it.data!!),
                        )
                    } else {
                        emit(
                            SetDefaultCardState.Error(
                                errorCode = it.responseCode,
                                errorMessage = it.message,
                            ),
                        )
                    }
                }
            }

        override suspend fun evCardLogo(
            brand: String,
            walletImages: WalletEvCardLogoResponse?,
        ): Flow<WalletEvCard?> {
            val isDarkMode = oneAppPreferenceModel.isDarkModeEnabled()
            return flow {
                walletImages?.let {
                    when {
                        brand.contains(WalletCards.AMEX.cardName) -> {
                            emit(
                                WalletEvCard(
                                    logo = walletImages.wallet.amex.small,
                                    cardImage =
                                        if (isDarkMode) {
                                            R.drawable.american_express_dark_mode
                                        } else {
                                            R.drawable.american_express_light_mode
                                        },
                                ),
                            )
                        }
                        brand.contains(WalletCards.DISCOVER.cardName) -> {
                            emit(
                                WalletEvCard(
                                    logo = walletImages.wallet.discover.small,
                                    cardImage =
                                        if (isDarkMode) {
                                            R.drawable.discover_dark_mode
                                        } else {
                                            R.drawable.discover_light_mode
                                        },
                                ),
                            )
                        }
                        brand.contains(WalletCards.VISA.cardName) -> {
                            emit(
                                WalletEvCard(
                                    logo = walletImages.wallet.visa.small,
                                    cardImage =
                                        if (isDarkMode) {
                                            R.drawable.visa_dark_mode
                                        } else {
                                            R.drawable.ev_wallet_first
                                        },
                                ),
                            )
                        }
                        brand.contains(WalletCards.MASTERCARD.cardName) -> {
                            emit(
                                WalletEvCard(
                                    logo = walletImages.wallet.mastercard.small,
                                    cardImage =
                                        if (isDarkMode) {
                                            R.drawable.master_card_dark_mode
                                        } else {
                                            R.drawable.master_card_light_mode
                                        },
                                ),
                            )
                        }
                        brand.contains(WalletCards.JCB.cardName) -> {
                            emit(
                                WalletEvCard(
                                    logo = walletImages.wallet.jcb.small,
                                    cardImage =
                                        if (isDarkMode) {
                                            R.drawable.jcb_dark_mode
                                        } else {
                                            R.drawable.jcb_light_mode
                                        },
                                ),
                            )
                        }
                        brand.contains(WalletCards.UNION.cardName) -> {
                            emit(
                                WalletEvCard(
                                    logo = walletImages.wallet.unionpay.small,
                                    cardImage =
                                        if (isDarkMode) {
                                            R.drawable.china_union_pay_dark
                                        } else {
                                            R.drawable.china_union_pay_light
                                        },
                                ),
                            )
                        }
                        else -> {
                            emit(
                                WalletEvCard(
                                    logo = walletImages.wallet.default.small,
                                    cardImage =
                                        if (isDarkMode) {
                                            R.drawable.visa_dark_mode
                                        } else {
                                            R.drawable.ev_wallet_first
                                        },
                                ),
                            )
                        }
                    }
                } ?: run {
                    fetchWalletImages(brand)
                }
            }
        }

        companion object {
            const val DEFAULT_CARD_ERROR = "defaultcarderror"
        }
    }
