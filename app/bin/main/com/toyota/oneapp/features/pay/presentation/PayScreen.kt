package com.toyota.oneapp.features.pay.presentation

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.BaseCompose
import com.toyota.oneapp.features.core.composable.EmptyBottomNavState
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.LocalBottomSheetState
import com.toyota.oneapp.features.pay.tfs.application.TFSAvailabilityState
import com.toyota.oneapp.features.pay.tfs.presentation.TFSScreen
import com.toyota.oneapp.features.pay.tfs.presentation.TFSViewModel
import com.toyota.oneapp.features.pay.wallet.application.WalletFFState
import com.toyota.oneapp.features.pay.wallet.presentation.WalletScreen
import com.toyota.oneapp.features.pay.wallet.presentation.WalletViewModel

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun PayScreenWithFeatures(
    walletViewModel: WalletViewModel,
    tfsViewModel: TFSViewModel,
    bottomSheet: LocalBottomSheetState,
    navController: NavController,
) {
    BaseCompose {
        BoxWithConstraints(modifier = Modifier.fillMaxWidth()) {
            Column(
                modifier = Modifier.verticalScroll(rememberScrollState()),
            ) {
                WalletScreen(walletViewModel, navController)
                Spacer(modifier = Modifier.height(8.dp))
                TFSScreen(tfsViewModel, bottomSheet)
            }
        }
    }
}

@Composable
fun PayPageIcon(
    icon: Int,
    contentDescription: String,
) {
    Box(
        modifier =
            Modifier
                .size(48.dp)
                .background(color = (AppTheme.colors.button02b), CircleShape)
                .clip(CircleShape),
    ) {
        Image(
            modifier =
                Modifier
                    .align(Alignment.Center)
                    .size(22.dp),
            painter = painterResource(icon),
            contentDescription = contentDescription,
        )
    }
}

@Composable
fun PayScreen(
    navController: NavHostController,
    walletViewModel: WalletViewModel = hiltViewModel<WalletViewModel>(),
    tfsViewModel: TFSViewModel = hiltViewModel<TFSViewModel>(),
    bottomSheetState: LocalBottomSheetState = LocalBottomSheet.current,
) {
    val walletState = walletViewModel.walletPresenceState.collectAsState().value
    val tfsState = tfsViewModel.tfsAvailabilityState.collectAsState().value

    if (walletState == WalletFFState.NoWalletFeatureAvailable && tfsState == TFSAvailabilityState.NoTFSFeatureAvailable) {
        EmptyBottomNavState(resId = R.drawable.ic_finance)
    } else {
        PayScreenWithFeatures(
            walletViewModel = walletViewModel,
            tfsViewModel = tfsViewModel,
            bottomSheet = bottomSheetState,
            navController,
        )
    }
}
