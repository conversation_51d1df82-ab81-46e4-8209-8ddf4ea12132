package com.toyota.oneapp.features.pay.tfs.dataaccess.service
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AccessTokenResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthorizeUserResponseModel
import com.toyota.oneapp.features.pay.tfs.presentation.utils.HeaderApiConstants
import retrofit2.Response
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.Header
import retrofit2.http.POST

interface TFSAuthorizeApi {
    @FormUrlEncoded
    @POST("/auth/oauth2/realms/root/realms/oneapp/authorize")
    suspend fun authorize(
        @Header("content-type") contentType: String? = HeaderApiConstants.FORM_URL_ENCODE_CONTENT_TYPE_VALUE,
        @Header("accept-api-version") acceptApiVersion: String? = HeaderApiConstants.ACCEPT_API_VERSION,
        @Header("cookie") ssoSession: String?,
        @Field("response_type") responseType: String?,
        @Field("scope") scope: String?,
        @Field("client_id") clientID: String?,
        @Field("csrf") csrf: String?,
        @Field("decision") decision: String?,
        @Field("save_consent") saveContent: Int?,
        @Field("redirect_uri") redirectUri: String?,
        @Field("code_challenge") codeChallange: String?,
        @Field("code_challenge_method") codeChallangeMethod: String?,
    ): Response<AuthorizeUserResponseModel>?

    @FormUrlEncoded
    @POST("/auth/oauth2/realms/root/realms/oneapp/access_token")
    suspend fun accessToken(
        @Header("content-type") contentType: String? = HeaderApiConstants.FORM_URL_ENCODE_CONTENT_TYPE_VALUE,
        @Header("accept-api-version") acceptApiVersion: String? = HeaderApiConstants.ACCEPT_API_VERSION,
        @Field("grant_type") grantType: String?,
        @Field("code") code: String?,
        @Field("client_id") clientID: String?,
        @Field("client_secret") clientSecret: String?,
        @Field("code_verifier") codeVerifier: String?,
        @Field("redirect_uri") redirectUri: String?,
    ): Response<AccessTokenResponseModel>

    @FormUrlEncoded
    @POST("/auth/oauth2/realms/root/realms/oneapp/access_token")
    suspend fun refreshToken(
        @Header("content-type") contentType: String? = HeaderApiConstants.FORM_URL_ENCODE_CONTENT_TYPE_VALUE,
        @Header("accept-api-version") acceptApiVersion: String? = HeaderApiConstants.ACCEPT_API_VERSION,
        @Field("grant_type") grantType: String?,
        @Field("client_id") clientID: String?,
        @Field("client_secret") clientSecret: String?,
        @Field("refresh_token") refreshToken: String?,
    ): Response<AccessTokenResponseModel>
}
