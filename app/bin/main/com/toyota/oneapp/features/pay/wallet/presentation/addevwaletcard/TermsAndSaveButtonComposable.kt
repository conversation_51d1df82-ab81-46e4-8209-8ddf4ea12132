/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.presentation.addevwaletcard

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun TermsAndSaveButton(
    brand: String,
    enable: Boolean,
    onClick: ((<PERSON>olean?) -> Unit)? = null,
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = stringResource(R.string.finance_agree_text, brand),
            fontSize = 12.sp,
            color = Color.Gray,
            modifier = Modifier.padding(16.dp),
        )
        Button(
            onClick = {
                onClick?.invoke(true)
            },
            enabled = enable,
            colors =
                ButtonDefaults.textButtonColors(
                    backgroundColor = AppTheme.colors.button02a,
                ),
            shape = CircleShape,
            modifier =
                Modifier
                    .padding(bottom = 16.dp)
                    .size(192.dp, 52.dp)
                    .zIndex(1f)
                    .alpha(if (enable) 1.0f else 0.5f),
        ) {
            OACallOut2TextView(
                stringResource(R.string.Common_save),
                color = AppTheme.colors.primaryButton01,
            )
        }
    }
}
