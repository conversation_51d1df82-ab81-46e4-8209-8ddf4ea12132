package com.toyota.oneapp.features.pay.tfs.application

import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.BannerPayload

sealed class TFSAvailabilityState {
    object Loading : TFSAvailabilityState()

    object NoTFSFeatureAvailable : TFSAvailabilityState()

    data class Success(
        val tfsAvailabilityResponse: TFSAvailabilityResponse,
    ) : TFSAvailabilityState()

    data class Error(val errorCode: String?, val errorMessage: String?) : TFSAvailabilityState()
}

data class TFSAvailabilityResponse(
    val isTFSFeatureAvailable: Boolean = false,
    val isEligibleForTFS: Boolean = false,
    val isUnderMaintenance: Boolean = false,
    val underMaintenance: BannerPayload = BannerPayload(),
)
