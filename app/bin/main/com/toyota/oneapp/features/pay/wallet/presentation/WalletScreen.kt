@file:OptIn(ExperimentalGlideComposeApi::class)

package com.toyota.oneapp.features.pay.wallet.presentation

import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.composable.PayComposableShimmer
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.pay.presentation.PayPageIcon
import com.toyota.oneapp.features.pay.wallet.application.WalletFFState
import com.toyota.oneapp.features.pay.wallet.domain.model.WalletDetailResponse
import com.toyota.oneapp.features.pay.wallet.domain.model.WalletEvCard
import com.toyota.oneapp.ui.payment.PaymentMethodsActivity
import com.toyota.oneapp.util.NavigationUtil
import com.toyota.oneapp.util.ToyotaConstants

@Composable
fun WalletScreen(
    viewModel: WalletViewModel,
    navController: NavController,
) {
    val context = LocalContext.current
    val walletState = viewModel.walletPresenceState.collectAsState().value

    PayComposableShimmer(
        isLoading = (walletState == WalletFFState.Loading),
        contentAfterLoading = {
            when (walletState) {
                is WalletFFState.Success -> {
                    val walletDetail = walletState.walletDetails
                    if (walletState.isEVVehicle && walletDetail != null) {
                        val walletDetailsData = walletDetail.walletDetail.paymentMethodDefault
                        if (walletDetailsData) {
                            EvWalletCard(
                                walletDetail.walletDetail,
                                walletDetail.evCardImages,
                                onCardClicked = {
                                    NavigationUtil.navigateToWalletHome(context, navController)
                                },
                            )
                        } else {
                            WalletSubscriptionCard(
                                onCardClicked = {
                                    viewModel.logFirebaseEventWithParameter(AnalyticsEventParam.PAY_TAB_WALLET)
                                    navController.navigate(OAScreen.AddEVCardScreen.route)
                                },
                            )
                        }
                    } else {
                        WalletSubscriptionCard(
                            onCardClicked = {
                                viewModel.logFirebaseEventWithParameter(AnalyticsEventParam.PAY_TAB_WALLET)
                                context.startActivity(
                                    Intent(context, PaymentMethodsActivity::class.java),
                                )
                            },
                        )
                    }
                }

                else -> {
                    // Do nothing
                }
            }
        },
    )
}

@Composable
fun WalletTitle() {
    Row(
        modifier =
            Modifier.padding(
                top = 12.dp,
                start = 16.dp,
            ),
    ) {
        PayPageIcon(R.drawable.ic_wallet, contentDescription = stringResource(R.string.wallet_icon))
        Column(
            modifier =
                Modifier.padding(
                    top = 12.dp,
                    start = 12.dp,
                ),
        ) {
            OABody4TextView(
                text = stringResource(R.string.wallet),
                color = AppTheme.colors.tertiary03,
            )
        }
    }
}

@Composable
fun WalletSubscriptionCard(onCardClicked: () -> Unit) {
    Card(
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(
                    top = 16.dp,
                    start = 16.dp,
                    end = 16.dp,
                ),
    ) {
        Column(modifier = Modifier.background(color = AppTheme.colors.tile01)) {
            WalletTitle()
            Spacer(modifier = Modifier.height(30.dp))
            Box(
                modifier =
                    Modifier
                        .padding(16.dp)
                        .fillMaxWidth()
                        .testTagID(AccessibilityId.ID_REGULAR_WALLET),
                contentAlignment = Alignment.Center,
            ) {
                SubscriptionCardStack(
                    headline = stringResource(R.string.ev_wallet_add_card),
                    onClick = onCardClicked,
                )
            }
        }
    }
}

@Composable
fun SubscriptionCardStack(
    headline: String = stringResource(R.string.manage_payment),
    onClick: () -> Unit = {},
) {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .offset(y = (-30).dp)
                .clickable { onClick() },
    ) {
        Image(
            painter = painterResource(R.drawable.wallet_card3),
            contentDescription = stringResource(R.string.wallet_subscription_card_three),
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth,
        )
    }
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .offset(y = (-15).dp),
    ) {
        Image(
            painter = painterResource(R.drawable.wallet_card2),
            contentDescription = stringResource(R.string.wallet_subscription_card_two),
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth,
        )
    }
    Box(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Image(
            painter = painterResource(R.drawable.wallet_card1),
            contentDescription = stringResource(R.string.wallet_subscription_card_one),
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth,
        )
    }
    Box(
        modifier = Modifier.padding(start = 66.dp, end = 66.dp),
        contentAlignment = Alignment.Center,
    ) {
        OACallOut2TextView(
            text = headline,
            color = Color.White,
            textAlign = TextAlign.Center,
            maxLines = 5,
        )
    }
}

@Composable
fun EvWalletCard(
    walletDetails: WalletDetailResponse,
    walletEvCard: WalletEvCard,
    onCardClicked: () -> Unit,
) {
    Card(
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(
                    top = 16.dp,
                    start = 16.dp,
                    end = 16.dp,
                ),
    ) {
        Column(modifier = Modifier.background(color = AppTheme.colors.tile01)) {
            WalletTitle()
            Spacer(modifier = Modifier.height(12.dp))
            Box(
                modifier = @Suppress("ktlint:standard:no-consecutive-comments")
                Modifier
                    .testTagID(AccessibilityId.ID_WALLET_EV_CARD)
                    .clickable { onCardClicked() },
            ) {
                LoadImage(url = walletEvCard.logo, cardImage = walletEvCard.cardImage)
                Box {
                    Row(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(start = 32.dp, end = 32.dp, top = 10.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.Top,
                    ) {
                        OACallOut1TextView(
                            text = stringResource(R.string.default_card_wallet),
                            color = Color.White,
                        )
                        Row {
                            repeat(4) {
                                OACallOut1TextView(
                                    text = stringResource(R.string.removeVehicleDot),
                                    color = Color.White,
                                )
                            }
                            Spacer(modifier = Modifier.width(3.dp))
                            OACallOut1TextView(
                                text = walletDetails.card?.last4 ?: ToyotaConstants.EMPTY_STRING,
                                color = Color.White,
                            )
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
private fun LoadImage(
    url: String?,
    cardImage: Int?,
) {
    Box(
        modifier =
            Modifier
                .padding(
                    start = 16.dp,
                    end = 16.dp,
                    bottom = 16.dp,
                ).fillMaxWidth(),
        contentAlignment = Alignment.Center,
    ) {
        Box(
            modifier = Modifier.fillMaxWidth(),
        ) {
            if (cardImage != null) {
                Image(
                    painter = painterResource(cardImage),
                    contentDescription = stringResource(R.string.ev_wallet_card),
                    modifier = Modifier.fillMaxWidth(),
                    contentScale = ContentScale.FillWidth,
                )
            }
        }
        Box(
            modifier = Modifier.padding(start = 66.dp, end = 66.dp),
            contentAlignment = Alignment.Center,
        ) {
            GlideImage(
                model = url ?: ToyotaConstants.EMPTY_STRING,
                contentDescription = stringResource(R.string.ev_wallet_card_logo),
                modifier =
                    Modifier
                        .width(85.dp)
                        .height(50.dp),
            )
        }
    }
}

@Preview
@Composable
private fun SubscriptionCardStackPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode = themeMode) {
        SubscriptionCardStack()
    }
}
