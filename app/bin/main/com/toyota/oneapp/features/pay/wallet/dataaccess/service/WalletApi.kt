package com.toyota.oneapp.features.pay.wallet.dataaccess.service

import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.DefaultCard
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.EVWalletTransactions
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.EvWalletCardTransactions
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletEvCardImages
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletResponse
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletSetupIntent
import com.toyota.oneapp.util.ToyotaConstants
import retrofit2.Response
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface WalletApi {
    @GET("/oneapi/v1/payment/wallet")
    suspend fun getWallet(): Response<WalletResponse?>

    @GET("/oneapi/v1/payment/config")
    suspend fun walletImages(): Response<WalletEvCardImages?>

    @GET("/oneapi/v1/payment/wallet/transactions")
    suspend fun getCardTransactions(
        @Query("last4") last4: String,
        @Query("expiry") expiry: String,
        @Query("status") status: String = ToyotaConstants.SUCCEEDED,
    ): Response<EvWalletCardTransactions?>

    @POST("/oneapi/v1/payment/wallet/payment-method")
    suspend fun walletSetupIntent(): Response<WalletSetupIntent?>

    @GET("/oneapi/v1/payment/wallet/transactions")
    suspend fun getWalletTransactions(
        @Query("status") status: String = ToyotaConstants.SUCCEEDED,
    ): Response<EVWalletTransactions?>

    @DELETE("/oneapi/v1/payment/wallet/payment-method/{paymentMethodId}")
    suspend fun deleteCard(
        @Path("paymentMethodId") paymentMethodId: String,
    ): Response<Unit?>

    @POST("/oneapi/v1/payment/wallet/payment-method/{paymentMethodId}/default")
    suspend fun setDefaultCard(
        @Path("paymentMethodId") paymentMethodId: String,
    ): Response<DefaultCard?>
}
