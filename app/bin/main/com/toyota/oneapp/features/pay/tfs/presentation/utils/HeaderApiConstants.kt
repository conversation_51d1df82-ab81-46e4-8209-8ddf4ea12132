package com.toyota.oneapp.features.pay.tfs.presentation.utils

object HeaderApiConstants {
    const val HEADER_CONTENT_TYPE_VALUE = "application/json"
    const val HEADER_X_CHANNEL_ID_VALUE = "OneApp"
    const val HEADER_ACCEPT_API_VERSION_VALUE = "resource=2.0"
    const val FORM_URL_ENCODE_CONTENT_TYPE_VALUE = "application/x-www-form-urlencoded;charset=utf-8"
    const val AUTH_INDEX_TYPE_VALUE = "service"
    const val AUTH_INDEX_VALUE_VALUE = "oneTID"
    const val REFRESH_TOKEN_GRANT_VALUE = "refresh_token"
    const val ACCEPT_API_VERSION = "resource=2.0"
    const val CLIENT_ID_VALUE = "oneapp"
    const val HEADER_UPSTREAM_TRACE_ID_VALUE = "1234542"

    const val USER_NOT_FOUND = "user not found"
    const val USER_UNVERIFIED = "user unverified"
    const val TOKEN_HAS_EXPIRED = "token has expired"
    const val EXPIRED_TOKEN = "expired token"
    const val NO_EMAIL_ID_FOUND = "NO EMAIL ID FOUND"
    const val INVALID_ID_TOKEN = "INVALID ID TOKEN"

    const val UNABLE_TO_LOCATE = "UNABLE TO LOCATE"
    const val SESSION_TIMEOUT = "session timeout"
    const val LOCKED = "locked"
    const val K_TOKENID = "tokenId"
    const val K_EXCEED_MAX_LIMIT = "EXCEEDED MAX ATTEMPTS"
    const val K_VERIFICATION_FAILED = "ACCESS CODE VERIFICATION FAILED"
    const val K_ONE_TIME_ACCESS_CODE = "ONE TIME ACCESS CODE"
    const val K_REMEMBER_THIS_DEVICE = "REMEMBER THIS DEVICE"

    const val RESPONSE_TYPE = "code"
    const val SCOPE = "openid profile"
    const val DECISION = "Allow"
    const val REDIRECT_URI = "com.toyota.oneappenterprise://callback"
    const val CODE_CHALLENGE = "j3wKnK2Fa_mc2tgdqa6GtUfCYjdWSA5S23JKTTtPF8Y"
    const val CODE_CHALLENGE_METHOD = "S256"
    const val AUTHORIZATION_CODE = "authorization_code"
    const val REFRESH_TOKEN = "refresh_token"
    const val ACCESS_TOKEN_REDIRECT_URI = "com.toyota.oneappenterprise://callback"
    const val CODE_VERIFIER = "ZpJiIM_G0SE9WlxzS69Cq0mQh8uyFaeEbILlW8tHs62SmEE6n7Nke0XJGx_F4OduTI4"
    const val LOCATION = "location"
    const val CONTAINS_CODE = "code="
    const val VALID_AUTHORIZE_RESPONSE_CODE = 302
    const val FINANCIAL_SERVICES = "financialServices"
}
