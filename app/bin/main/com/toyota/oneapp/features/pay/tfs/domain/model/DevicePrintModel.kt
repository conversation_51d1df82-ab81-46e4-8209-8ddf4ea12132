package com.toyota.oneapp.features.pay.tfs.domain.model

import org.forgerock.android.auth.ui.entity.Geolocation

data class ScreenData(
    val screenWidth: Int,
    val screenHeight: Int,
    val screenColourDepth: Int,
)

data class TimezoneData(
    val timezone: String,
)

data class PluginData(
    val installedPlugins: String,
)

data class FontData(
    val installedFonts: String,
)

data class DevicePrintModel(
    val screen: ScreenData,
    val timezone: String,
    val plugins: PluginData,
    val fonts: FontData,
    val userAgent: String,
    val appName: String,
    val appCodeName: String,
    val appVersion: String,
    val buildID: String,
    val platform: String,
    val oscpu: String,
    val product: String,
    val productSub: String,
    val language: String,
    val geolocation: Geolocation?,
)
