/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.application

import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.DefaultCard

sealed class SetDefaultCardState {
    object StandBy : SetDefaultCardState()

    object Loading : SetDefaultCardState()

    data class Success(
        val response: DefaultCard? = null,
    ) : SetDefaultCardState()

    object IsDefault : SetDefaultCardState()

    data class Error(
        val errorCode: String?,
        val errorMessage: String?,
    ) : SetDefaultCardState()
}
