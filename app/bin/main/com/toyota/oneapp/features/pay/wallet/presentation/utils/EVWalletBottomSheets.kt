/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.presentation.utils

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun DeleteEVCardBottomSheet(
    onCancelClick: () -> Unit,
    onDoneClick: () -> Unit,
) {
    Column(
        modifier =
            Modifier
                .fillMaxWidth() // Apply contentPadding here
                .padding(16.dp),
        // Additional padding
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Box(
            modifier =
                Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(AppTheme.colors.primary02)
                    .wrapContentSize(Alignment.Center),
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_alert_no_background),
                contentDescription = "Subscriptions Navigation Button",
                modifier =
                    Modifier
                        .size(32.dp),
                colorFilter = ColorFilter.tint(AppTheme.colors.error01),
            )
        }
        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = stringResource(R.string.remove_card),
            fontWeight = FontWeight.Bold,
            fontSize = 24.sp,
            color = AppTheme.colors.tertiary00,
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = stringResource(id = R.string.remove_card_subheading),
            fontSize = 16.sp,
            color = AppTheme.colors.tertiary00,
            modifier = Modifier.padding(horizontal = 16.dp),
            textAlign = TextAlign.Center,
        )

        Spacer(modifier = Modifier.height(32.dp))

        Button(
            onClick = onCancelClick,
            colors =
                ButtonDefaults.textButtonColors(
                    backgroundColor = AppTheme.colors.tertiary15,
                ),
            shape = CircleShape,
            modifier =
                Modifier
                    .padding(bottom = 16.dp)
                    .size(192.dp, 52.dp)
                    .zIndex(1f),
        ) {
            OACallOut2TextView(
                stringResource(id = R.string.Common_cancel),
                color = AppTheme.colors.tertiary00,
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        Button(
            onClick = onDoneClick,
            colors =
                ButtonDefaults.textButtonColors(
                    backgroundColor = AppTheme.colors.button02a,
                ),
            shape = CircleShape,
            modifier =
                Modifier
                    .padding(bottom = 16.dp)
                    .size(192.dp, 52.dp)
                    .zIndex(1f),
        ) {
            OACallOut2TextView(
                stringResource(R.string.ev_wallet_yes_remove),
                color = AppTheme.colors.primaryButton01,
            )
        }
    }
}

@Composable
fun BottomSheetContent(
    onDoneClick: () -> Unit,
    title: String,
    description: String,
    image: Int,
) {
    Column(
        modifier =
            Modifier
                .fillMaxWidth() // Apply contentPadding here
                .padding(16.dp),
        // Additional padding
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        val imageColor: Color =
            if (image == R.drawable.ic_baseline_done_primary_24) {
                AppTheme.colors.button03d
            } else {
                AppTheme.colors.error01
            }

        Image(
            painter = painterResource(id = image),
            contentDescription = "Subscriptions Navigation Button",
            modifier =
                Modifier
                    .size(32.dp),
            colorFilter = ColorFilter.tint(imageColor),
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Default Text
        Text(
            text = title,
            fontWeight = FontWeight.Bold,
            fontSize = 24.sp,
            color = AppTheme.colors.tertiary00,
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Description Text
        Text(
            text = description,
            fontSize = 16.sp,
            color = AppTheme.colors.tertiary00,
            modifier = Modifier.padding(horizontal = 16.dp),
            textAlign = TextAlign.Center,
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Done Button
        Button(
            onClick = onDoneClick,
            colors =
                ButtonDefaults.textButtonColors(
                    backgroundColor = AppTheme.colors.tertiary00,
                ),
            shape = CircleShape,
            modifier =
                Modifier
                    .padding(bottom = 16.dp)
                    .size(192.dp, 52.dp)
                    .zIndex(1f),
        ) {
            OACallOut2TextView(
                stringResource(R.string.done_label),
                color = AppTheme.colors.primaryButton01,
            )
        }
    }
}
