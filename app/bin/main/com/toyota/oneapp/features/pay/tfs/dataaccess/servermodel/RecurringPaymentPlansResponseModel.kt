package com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel

import com.toyota.oneapp.features.pay.tfs.domain.model.RecurringPaymentPlans
import com.toyota.oneapp.features.pay.tfs.domain.model.RecurringPlansModel

data class RecurringPaymentPlansResponseModel(
    val recurringpaymentplans: ArrayList<RecurringPaymentPlanDTO>?,
    val error: String?,
)

data class RecurringPaymentPlanDTO(
    val id: String?,
    val accountId: String?,
    val channel: String?,
    val subAccountType: String?,
    val paymentCategory: String?,
    val paymentMethodTypec: String?,
    val paymentMethodId: String?,
    val paymentGatewayName: String?,
    val achAccountNumberMask: String?,
    val frequency: String?,
    val startDate: String?,
    val endDate: String?,
    val amount: String?,
    val status: String?,
    val period: String?,
    val occurrences: Int,
    val upid: String?,
)

fun RecurringPaymentPlansResponseModel.toUIModel(): RecurringPaymentPlans {
    val plans = ArrayList<RecurringPlansModel>()
    recurringpaymentplans?.forEach {
        plans.add(
            RecurringPlansModel(
                recurringPlanId = it.id,
                status = it.status,
                frequency = it.frequency,
                period = it.period,
                paymentMethodID = it.paymentMethodId,
                postDate = it.startDate,
                paymentAmount = it.amount,
                achAccountNumberMask = it.achAccountNumberMask,
            ),
        )
    }
    return RecurringPaymentPlans(
        plans = plans,
    )
}
