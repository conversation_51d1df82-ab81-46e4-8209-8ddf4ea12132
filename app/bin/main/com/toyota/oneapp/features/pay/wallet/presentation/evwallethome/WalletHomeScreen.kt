/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.presentation.evwallethome

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OAAppBar
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.pay.wallet.application.CollectEVWalletState
import com.toyota.oneapp.features.pay.wallet.application.WalletCardData
import com.toyota.oneapp.features.pay.wallet.application.WalletTransactionState
import com.toyota.oneapp.features.pay.wallet.presentation.WalletViewModel
import com.toyota.oneapp.features.pay.wallet.presentation.utils.WalletCardStack

@Composable
fun WalletHomeScreen(
    navController: NavHostController,
    viewModel: WalletViewModel = hiltViewModel<WalletViewModel>(),
) {
    BackHandler {
        navController.navigate(OAScreen.Pay.route)
    }
    LaunchedEffect(Unit) {
        viewModel.checkWalletTransactions()
        viewModel.collectEVWallet()
    }
    Scaffold(
        topBar = {
            OAAppBar(
                modifier =
                    Modifier
                        .padding(start = 16.dp, top = 9.dp, end = 16.dp),
                title = stringResource(id = R.string.wallet),
                onBack = {
                    navController.popBackStack()
                },
                actionWidget = {
                    Box(
                        modifier =
                            Modifier
                                .size(40.dp)
                                .clip(CircleShape)
                                .background(AppTheme.colors.button01b)
                                .wrapContentSize(Alignment.Center)
                                .clickable {
                                    navController.navigate(OAScreen.AddEVCardScreen.route)
                                },
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_plus),
                            contentDescription = stringResource(R.string.add_card),
                            modifier =
                                Modifier
                                    .size(12.dp),
                            colorFilter = ColorFilter.tint(AppTheme.colors.button01a),
                        )
                    }
                },
                testTagId = AccessibilityId.ID_EV_WALLET_HOME_BACK_BTN,
            )
        },
        backgroundColor = AppTheme.colors.tertiary12,
        content = { paddingValues ->
            Column(
                modifier =
                    Modifier
                        .padding(paddingValues)
                        .background(AppTheme.colors.tertiary12)
                        .testTagID(AccessibilityId.ID_EV_WALLET_HOME),
            ) {
                val walletState = viewModel.evWalletState.collectAsState().value
                val transactionState = viewModel.walletTransactionState.collectAsState().value
                when (walletState) {
                    is CollectEVWalletState.Loading -> {
                        ShowProgressIndicator(dialogState = true)
                    }
                    is CollectEVWalletState.Success -> {
                        val fullWallet = walletState.evWallet
                        val cardListing: MutableList<WalletCardData> = emptyList<WalletCardData>().toMutableList()
                        fullWallet?.forEach {
                            it
                                ?.card
                                ?.last4
                                ?.let { cardNumber ->
                                    it.id?.let { cardId ->
                                        val expiry =
                                            viewModel.formatExpiry(
                                                expMonth = it.card.expiryMonth,
                                                expYear = it.card.expiryYear,
                                            )
                                        WalletCardData(
                                            cardName = it.card.brand,
                                            cardNumber = cardNumber,
                                            isDefault = it.paymentMethodDefault,
                                            brand =
                                                viewModel.formatBrandName(
                                                    it.card.brand,
                                                ),
                                            cardId = cardId,
                                            expiry = expiry,
                                        )
                                    }
                                }?.let { walletCards ->
                                    cardListing.add(walletCards)
                                }
                        }
                        var transactionCount = 0
                        when (transactionState) {
                            is WalletTransactionState.Success -> {
                                if (!transactionState.transactions.isNullOrEmpty()) {
                                    transactionCount = transactionState.transactions.size
                                }
                                WalletCardStack(
                                    cards = cardListing,
                                    navController = navController,
                                    transactionsCount = transactionCount,
                                    isHomeScreen = true,
                                )
                            } is WalletTransactionState.Error -> {
                                WalletCardStack(
                                    cards = cardListing,
                                    navController = navController,
                                    transactionsCount = 0,
                                    isHomeScreen = true,
                                )
                            }
                            else -> {}
                        }
                    }
                    else -> {}
                }
            }
        },
    )
}

@Composable
fun BottomCards(
    transactions: Int,
    navController: NavHostController,
) {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .align(Alignment.BottomCenter),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Card(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(75.dp)
                        .clickable {
                            navController.navigate(OAScreen.WalletTransactionsScreen.route)
                        },
                elevation = 4.dp,
                backgroundColor = AppTheme.colors.primaryButton01,
                shape = RoundedCornerShape(8.dp),
            ) {
                Row(
                    modifier = Modifier.padding(8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Box(
                        modifier =
                            Modifier
                                .size(40.dp)
                                .clip(CircleShape)
                                .background(AppTheme.colors.button03d)
                                .wrapContentSize(Alignment.Center),
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_charge_flash),
                            contentDescription = "Charge Stations Button",
                            modifier =
                                Modifier
                                    .size(16.dp)
                                    .clip(CircleShape)
                                    .background(AppTheme.colors.button03d),
                            colorFilter = ColorFilter.tint(AppTheme.colors.button01a),
                        )
                    }
                    Column {
                        Text(
                            text = stringResource(id = R.string.charging_stations),
                            fontSize = 16.sp,
                            color = AppTheme.colors.tertiary03,
                            modifier = Modifier.padding(start = 8.dp),
                            style = AppTheme.fontStyles.body4,
                        )
                        val transactionCount: String =
                            if (transactions > 0) {
                                "$transactions Transactions"
                            } else {
                                stringResource(id = R.string.no_transactions_found)
                            }
                        Text(
                            text = transactionCount,
                            modifier = Modifier.padding(start = 8.dp),
                            style = AppTheme.fontStyles.tabLabel02,
                            color = AppTheme.colors.tertiary05,
                        )
                    }
                }
            }
            ManageSubscriptionCard()
        }
    }
}
