package com.toyota.oneapp.features.pay.tfs.presentation.utils

import android.net.Uri
import com.auth0.android.jwt.JWT
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthenticateResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.service.TFSAuthenticateApi
import com.toyota.oneapp.features.pay.tfs.dataaccess.service.TFSAuthorizeApi
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.Lazy
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import javax.inject.Inject

class TFSNetworkProviderHelperImpl
    @Inject
    internal constructor(
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val tfsAuthorizeApi: TFS<PERSON>uthorizeApi,
        private val tfsAuthenticateApi: Lazy<TFSAuthenticateApi>,
        private val applicationData: Lazy<ApplicationData>,
    ) : TFSTokenProviderHelper {
        override fun getTFSIdToken(): String {
            return oneAppPreferenceModel.getTFSIdToken()
        }

        override fun getTFSRefreshToken(): String {
            return oneAppPreferenceModel.getTFSRefreshToken()
        }

        override fun newTfsIdTokenNeeded(): Boolean {
            return try {
                val expTime = JWT(oneAppPreferenceModel.getTFSIdToken()).getClaim("exp").asLong()
                var expireDate: LocalDateTime? = null
                expTime?.let {
                    expireDate =
                        Instant.ofEpochSecond(it)
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime()
                }
                expireDate?.isBefore(LocalDateTime.now()) ?: false
            } catch (e: Exception) {
                false
            }
        }

        override fun getNewTfsIdToken(): Flow<String> {
            return flow {
                if (!newRefreshTokenNeeded()) {
                    val refreshTokenResponse =
                        tfsAuthorizeApi.refreshToken(
                            grantType = HeaderApiConstants.REFRESH_TOKEN,
                            clientID = BuildConfig.TFS_CLIENT_ID,
                            clientSecret = BuildConfig.TFS_CLIENT_SECRET,
                            refreshToken = oneAppPreferenceModel.getTFSRefreshToken(),
                        )
                    refreshTokenResponse.let { newTokens ->
                        if (newTokens.isSuccessful) {
                            newTokens.body()?.let {
                                oneAppPreferenceModel.setTFSAccessToken(it.access_token)
                                oneAppPreferenceModel.setTFSRefreshToken(it.refresh_token)
                                oneAppPreferenceModel.setTFSIdToken(it.id_token)
                                oneAppPreferenceModel.setTFSExpireIn(it.expires_in)
                                emit(it.id_token)
                            }
                        } else {
                            emit(ToyotaConstants.EMPTY_STRING)
                        }
                    }
                } else {
                    // call tfs refresh token expired flow: get tmna id_token and call regular authenticate api and update all token
                    val authenticateResponseModel = AuthenticateResponseModel()
                    applicationData.get().getSelectedVehicle()?.let {
                        val authResponse =
                            tfsAuthenticateApi.get().authenticate(
                                brandType = TFSProperties.isBrandLFSOrTFS(it.brand),
                                tmssguid = null,
                                body = authenticateResponseModel,
                            )
                        if (authResponse.isSuccessful && authResponse.body() != null) {
                            val authorizeApiResponse =
                                tfsAuthorizeApi.authorize(
                                    clientID = BuildConfig.TFS_CLIENT_ID,
                                    scope = HeaderApiConstants.SCOPE,
                                    responseType = HeaderApiConstants.RESPONSE_TYPE,
                                    csrf = authResponse.body()?.tokenId,
                                    saveContent = 1,
                                    redirectUri = HeaderApiConstants.REDIRECT_URI,
                                    codeChallange = HeaderApiConstants.CODE_CHALLENGE,
                                    codeChallangeMethod = HeaderApiConstants.CODE_CHALLENGE_METHOD,
                                    decision = HeaderApiConstants.DECISION,
                                    ssoSession = "SSOSESSION=${authResponse.body()?.tokenId}",
                                )
                            authorizeApiResponse?.let {
                                if (it.isSuccessful) {
                                    // no need to do anything here
                                } else {
                                    val isValidResponse = it.code() == HeaderApiConstants.VALID_AUTHORIZE_RESPONSE_CODE
                                    if (isValidResponse) {
                                        val location = it.headers().get(HeaderApiConstants.LOCATION).toString()
                                        val uri = Uri.parse(location)
                                        if (location.contains(HeaderApiConstants.CONTAINS_CODE)) {
                                            val accessTokenResponse =
                                                tfsAuthorizeApi.accessToken(
                                                    grantType = HeaderApiConstants.AUTHORIZATION_CODE,
                                                    code =
                                                        uri.getQueryParameter(
                                                            HeaderApiConstants.RESPONSE_TYPE,
                                                        ),
                                                    clientID = BuildConfig.TFS_CLIENT_ID,
                                                    redirectUri = HeaderApiConstants.ACCESS_TOKEN_REDIRECT_URI,
                                                    codeVerifier = HeaderApiConstants.CODE_VERIFIER,
                                                    clientSecret = BuildConfig.TFS_CLIENT_SECRET,
                                                )
                                            if (accessTokenResponse.isSuccessful && accessTokenResponse.body() != null) {
                                                accessTokenResponse.body()?.let { newToken ->
                                                    oneAppPreferenceModel.setTFSAccessToken(
                                                        newToken.access_token,
                                                    )
                                                    oneAppPreferenceModel.setTFSRefreshToken(
                                                        newToken.refresh_token,
                                                    )
                                                    oneAppPreferenceModel.setTFSIdToken(
                                                        newToken.id_token,
                                                    )
                                                    oneAppPreferenceModel.setTFSExpireIn(
                                                        newToken.expires_in,
                                                    )
                                                    emit(newToken.id_token)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        override fun newRefreshTokenNeeded(): Boolean {
            return try {
                val expTime = JWT(oneAppPreferenceModel.getTFSRefreshToken()).getClaim("exp").asLong()
                var isExpired = false
                // var expireDate :LocalDateTime?=null
                expTime?.let {
                    val expireDate =
                        Instant.ofEpochSecond(it)
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime()
                    isExpired = expireDate.isBefore(LocalDateTime.now())
                }
                isExpired
            } catch (_: Exception) {
                true
            }
        }
    }
