/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel

import com.eclipsesource.v8.Platform
import com.google.gson.annotations.SerializedName

data class EbocRequest(
    @SerializedName("ebaFlag") var ebaFlag: String = "Y",
    @SerializedName("ocFlag") var ocFlag: String = "Y",
    @SerializedName("browserVersion") var browserVersion: String? = Platform.ANDROID,
    @SerializedName("ocTimeStamp") var ocTimeStamp: String? = null,
    @SerializedName("ebaTimeStamp") var ebaTimeStamp: String? = null,
    @SerializedName("browserTimeStamp") var browserTimeStamp: String? = null,
    @SerializedName("clientInfo") var clientInfo: String? = "${Platform.ANDROID}  ${android.os.Build.VERSION.RELEASE}",
)
