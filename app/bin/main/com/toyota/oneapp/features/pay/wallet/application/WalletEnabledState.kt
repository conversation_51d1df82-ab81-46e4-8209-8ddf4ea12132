package com.toyota.oneapp.features.pay.wallet.application

sealed class WalletEnableSubscriptionState {
    object Loading : WalletEnableSubscriptionState()

    data class Success(
        val isWalletSubscriptionAvailable: Boolean = false,
        val isEvVehicle: Boolean = false,
    ) : WalletEnableSubscriptionState()

    data class Error(val errorCode: String?, val errorMessage: Int) : WalletEnableSubscriptionState()
}
