/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.application

import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletEvCardImages

sealed class PubKeySetupState {
    object Loading : PubKeySetupState()

    data class Success(
        val response: WalletEvCardImages? = null,
    ) : PubKeySetupState()

    data class Error(
        val errorCode: String?,
        val errorMessage: String?,
    ) : PubKeySetupState()
}
