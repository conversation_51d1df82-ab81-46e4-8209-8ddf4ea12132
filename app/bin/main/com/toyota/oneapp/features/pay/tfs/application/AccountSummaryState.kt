package com.toyota.oneapp.features.pay.tfs.application

import com.toyota.oneapp.features.pay.tfs.domain.model.AccountModel

sealed class AccountSummaryState {
    object Loading : AccountSummaryState()

    data class Success(
        val accountSummary: AccountModel?,
    ) : AccountSummaryState()

    object InactiveVehicleAccount : AccountSummaryState()

    object AccountCannotBeAdded : AccountSummaryState()

    object ShowEBOCAgreementDialog : AccountSummaryState()

    object AccountNotFound : AccountSummaryState()

    object AccountClosed : AccountSummaryState()

    data class Error(val errorCode: String?, val errorMessage: String?) : AccountSummaryState()
}
