package com.toyota.oneapp.features.pay.tfs.domain

import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AccessTokenRequestModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AccessTokenResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AccountSummaryResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthenticateResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthorizeRequestModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthorizeUserResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.BannerResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.EbocRequest
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.FinancialServiceEligibilityModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.KintoServiceResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.PaymentHistoryResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.RecurringPaymentPlansResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.RefreshTokenRequestModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.ServerTimeStampResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import retrofit2.Response
import kotlin.coroutines.CoroutineContext

abstract class TFSRepository(
    errorParser: ErrorMessageParser,
    ioContext: CoroutineContext,
) : BaseRepository(errorParser, ioContext) {
    abstract suspend fun fetchBanner(): Resource<BannerResponseModel?>

    abstract suspend fun fetchTfsEligibility(vin: String): Resource<FinancialServiceEligibilityModel?>

    abstract suspend fun fetchAuthenticate(
        brandType: String,
        tmssguid: String?,
        body: AuthenticateResponseModel?,
    ): Resource<AuthenticateResponseModel?>

    abstract suspend fun callAuthorizeUserApi(
        tfsSessionIdToken: String?,
        clientID: String?,
        body: AuthorizeRequestModel,
    ): Response<AuthorizeUserResponseModel>?

    abstract suspend fun callAccessToken(body: AccessTokenRequestModel): Resource<AccessTokenResponseModel?>

    abstract suspend fun callAccountSummary(brandId: String?): Resource<AccountSummaryResponseModel?>

    abstract suspend fun callPaymentHistory(
        brandId: String?,
        accountNumber: String?,
    ): Resource<PaymentHistoryResponseModel?>

    abstract suspend fun callKintoService(
        brandId: String?,
        ucId: String?,
    ): Resource<KintoServiceResponseModel?>

    abstract suspend fun callRecurringPlans(
        brandId: String?,
        kintoServiceId: String?,
    ): Resource<RecurringPaymentPlansResponseModel?>

    abstract suspend fun callRefreshToken(body: RefreshTokenRequestModel): Resource<AccessTokenResponseModel?>

    abstract suspend fun getServerTimeStamp(brandId: String?): Resource<ServerTimeStampResponse?>

    abstract suspend fun submitElectronicConsent(
        body: EbocRequest,
        brandId: String?,
    ): Resource<ServerTimeStampResponse?>
}
