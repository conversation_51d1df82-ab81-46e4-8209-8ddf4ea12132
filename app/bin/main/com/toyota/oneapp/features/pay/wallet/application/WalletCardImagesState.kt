package com.toyota.oneapp.features.pay.wallet.application

import com.toyota.oneapp.features.pay.wallet.domain.model.WalletEvCardLogoResponse

sealed class WalletCardImagesState {
    object Loading : WalletCardImagesState()

    data class Success(
        val walletImages: WalletEvCardLogoResponse,
    ) : WalletCardImagesState()

    data class Error(val errorCode: String?, val errorMessage: String?) : WalletCardImagesState()
}
