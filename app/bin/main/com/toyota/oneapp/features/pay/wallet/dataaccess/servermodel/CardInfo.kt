/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel

data class CardInfo(
    var creditCardNumber: String = "",
    var firstName: String = "",
    var lastName: String = "",
    var expDate: String = "",
    var ccvCode: String = "",
    var billingAddress: String = "",
    var city: String = "",
    var state: String = "",
    var zipCode: String = "",
    var country: String = "",
)

fun CardInfo.isAllFieldsFilled(): Boolean =
    listOf(
        creditCardNumber,
        firstName,
        lastName,
        expDate,
        ccvCode,
        billingAddress,
        city,
        state,
        zipCode,
        country,
    ).all { it.isNotEmpty() } &&
        isValidExpDate(expDate)

private fun isValidExpDate(expDate: String): Boolean {
    if (expDate.isEmpty()) return false

    // Remove any non-digit characters
    val cleanDate = expDate.replace(Regex("[^0-9]"), "")

    // Check if we have 6 digits (MMYYYY)
    if (cleanDate.length != 6) return false

    // Extract month and year
    val month = cleanDate.substring(0, 2).toIntOrNull() ?: return false
    val year = cleanDate.substring(2).toIntOrNull() ?: return false

    // Validate month is between 1 and 12
    if (month < 1 || month > 12) return false

    // Validate year is not in the past
    val currentYear =
        java.time.LocalDate
            .now()
            .year
    if (year < currentYear) return false

    return true
}
