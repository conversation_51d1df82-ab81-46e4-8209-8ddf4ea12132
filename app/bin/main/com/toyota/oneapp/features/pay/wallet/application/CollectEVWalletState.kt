/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.application

import com.toyota.oneapp.features.pay.wallet.domain.model.WalletDetailResponse

sealed class CollectEVWalletState {
    object Loading : CollectEVWalletState()

    data class Success(
        val defaultCard: WalletDetailResponse? = null,
        val evWallet: MutableList<WalletDetailResponse?>? = null,
    ) : CollectEVWalletState()

    data class Error(val errorCode: String?, val errorMessage: String?) : CollectEVWalletState()
}
