/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.presentation.evwallethome

import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.ui.payment.PaymentMethodsActivity

@Composable
fun ManageSubscriptionCard() {
    val context = LocalContext.current
    Card(
        modifier =
            Modifier
                .fillMaxWidth()
                .height(75.dp)
                .clickable {
                    context.startActivity(
                        Intent(context, PaymentMethodsActivity::class.java),
                    )
                },
        backgroundColor = AppTheme.colors.primaryButton01,
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Box(
                modifier =
                    Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(AppTheme.colors.button03b)
                        .wrapContentSize(Alignment.Center),
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_document),
                    contentDescription = "Subscriptions Navigation Button",
                    modifier =
                        Modifier
                            .size(12.dp),
                    colorFilter = ColorFilter.tint(AppTheme.colors.button01a),
                )
            }
            Column {
                Text(
                    text = stringResource(id = R.string.Subscription_subscriptions),
                    modifier = Modifier.padding(start = 8.dp),
                    color = AppTheme.colors.tertiary03,
                    style = AppTheme.fontStyles.body4,
                )
                Text(
                    text = stringResource(id = R.string.manage_payments),
                    modifier = Modifier.padding(start = 8.dp),
                    style = AppTheme.fontStyles.tabLabel02,
                    color = AppTheme.colors.tertiary05,
                )
            }
        }
    }
}
