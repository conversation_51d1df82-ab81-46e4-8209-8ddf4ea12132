/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.presentation

import androidx.activity.ComponentActivity
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import com.stripe.android.createPaymentMethod
import com.stripe.android.exception.CardException
import com.stripe.android.model.PaymentMethodCreateParams
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.pay.wallet.application.AddEvCardState
import com.toyota.oneapp.features.pay.wallet.application.PubKeySetupState
import com.toyota.oneapp.features.pay.wallet.application.WalletLogic
import com.toyota.oneapp.features.pay.wallet.application.WalletSetupIntentState
import com.toyota.oneapp.features.pay.wallet.dataaccess.repository.StripeOAPaymentMethod
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.CardInfo
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.io.IOException
import java.lang.IllegalArgumentException
import java.lang.NumberFormatException
import javax.inject.Inject

@HiltViewModel
class AddEVWalletCardViewModel
    @Inject
    constructor(
        private val walletLogic: WalletLogic,
        private val oaPaymentMethod: StripeOAPaymentMethod,
        applicationData: ApplicationData,
    ) : BaseViewModel() {
        var addCardError: String = ""
        val vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()

        private val _cardInfo = mutableStateOf(CardInfo())
        val cardInfo: State<CardInfo> get() = _cardInfo

        private val _pubKeySetupState = MutableStateFlow<PubKeySetupState>(PubKeySetupState.Loading)
        val pubKeySetupState: StateFlow<PubKeySetupState> = _pubKeySetupState

        private val _walletSetupIntentState =
            MutableStateFlow<WalletSetupIntentState>(
                WalletSetupIntentState.Loading,
            )
        val walletSetupIntentState: StateFlow<WalletSetupIntentState> = _walletSetupIntentState

        private val _addEVCardState = MutableStateFlow<AddEvCardState>(AddEvCardState.StandBy)
        val addEVCardState: StateFlow<AddEvCardState> = _addEVCardState

        // Class-level constant for regex pattern
        private val nonDigitRegex = Regex("[^0-9]")

        // Custom exception classes for card validation issues
        class CardExpiryFormatException(
            message: String,
        ) : IllegalArgumentException(message)

        fun updateCardInfo(update: (CardInfo) -> CardInfo) {
            _cardInfo.value = update(_cardInfo.value)
        }

        fun collectPubKey(): String? {
            return when (pubKeySetupState.value) {
                is PubKeySetupState.Success -> {
                    (pubKeySetupState.value as PubKeySetupState.Success)
                        .response
                        ?.payload
                        ?.channelConfig
                        ?.publicKey
                } else -> {
                    return ""
                }
            }
        }

        fun confirmPaymentMethod(
            activity: ComponentActivity,
            cardNumber: String,
            cvc: String,
            expiry: String,
            pubKey: String,
        ) {
            viewModelScope.launch {
                try {
                    val paymentSetup = oaPaymentMethod.initPaymentMethod(pubKey = pubKey)
                    savePaymentLoading()
                    fetchClientSecret()

                    when (val state = walletSetupIntentState.value) {
                        is WalletSetupIntentState.Success -> {
                            val clientSecret =
                                state.response
                                    ?.payload
                                    ?.setupIntent
                                    ?.clientSecret
                                    ?: throw IllegalArgumentException("Invalid client secret")

                            val paymentMethod =
                                PaymentMethodCreateParams.create(
                                    PaymentMethodCreateParams.Card
                                        .Builder()
                                        .setNumber(cardNumber)
                                        .setCvc(cvc)
                                        .setExpiryMonth(getExpiryMonth(expiry))
                                        .setExpiryYear(getExpiryYear(expiry))
                                        .build(),
                                )

                            val paymentMethodCreate = paymentSetup.createPaymentMethod(paymentMethod)
                            val paymentId = paymentMethodCreate.id.toString()

                            oaPaymentMethod.confirmPaymentSetup(
                                stripe = paymentSetup,
                                clientSecret = clientSecret,
                                paymentId = paymentId,
                                activity = activity,
                            )

                            savePaymentSuccess(cardNumber)
                        }
                        is WalletSetupIntentState.Error -> {
                            addCardError = state.errorMessage.toString()
                            savePaymentError()
                        }
                        else -> {
                            // Handle loading state
                        }
                    }
                } catch (e: CardException) {
                    addCardError = e.message.toString()
                    savePaymentError()
                } catch (e: CardExpiryFormatException) {
                    addCardError = e.message.toString()
                    savePaymentError()
                } catch (e: IllegalArgumentException) {
                    addCardError = e.message ?: "Invalid input parameters"
                    savePaymentError()
                } catch (e: IOException) {
                    addCardError = e.message ?: "Network error occurred"
                    savePaymentError()
                } catch (e: SecurityException) {
                    addCardError = "Security validation failed"
                    savePaymentError()
                } catch (e: Throwable) {
                    // We still need a fallback, but it's more specific than Exception
                    addCardError = "Unexpected error: ${e.message ?: "Unknown error"}"
                    savePaymentError()
                }
            }
        }

        private fun getExpiryMonth(expiry: String): Int =
            try {
                val cleanDate = expiry.replace(nonDigitRegex, "")
                val month = cleanDate.take(2).toInt()
                if (month < 1 || month > 12) {
                    throw CardExpiryFormatException("Month must be between 1 and 12")
                }
                month
            } catch (e: NumberFormatException) {
                throw CardExpiryFormatException("Invalid expiry month format")
            }

        fun getExpiryYear(expiry: String): Int =
            try {
                val cleanDate = expiry.replace(nonDigitRegex, "")
                cleanDate.takeLast(4).toInt()
            } catch (e: NumberFormatException) {
                throw CardExpiryFormatException("Invalid expiry year format")
            }

        suspend fun fetchClientSecret() {
            walletLogic.fetchClientSecret().collect { resource ->
                _walletSetupIntentState.value = resource
            }
        }

        fun savePaymentLoading() {
            _addEVCardState.value = AddEvCardState.Loading()
        }

        fun savePaymentSuccess(cardNumber: String) {
            _addEVCardState.value =
                AddEvCardState.Success(
                    last4 = cardNumber.substring(cardNumber.length - 4),
                )
        }

        fun savePaymentError() {
            _addEVCardState.value =
                AddEvCardState.Error(
                    errorCode = null,
                    errorMessage = null,
                )
        }

        fun getPubKey() {
            viewModelScope.launch {
                walletLogic.fetchPubKey().collectLatest { resource ->
                    _pubKeySetupState.value = resource
                }
            }
        }
    }
