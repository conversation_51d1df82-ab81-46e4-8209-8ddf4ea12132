package com.toyota.oneapp.features.pay.tfs.presentation

import android.app.Activity
import android.os.Bundle
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.pay.presentation.PayPageIcon
import com.toyota.oneapp.features.pay.tfs.domain.model.AccountModel
import com.toyota.oneapp.features.pay.tfs.presentation.utils.TFSProperties
import com.toyota.oneapp.ui.flutter.ACCOUNTS
import com.toyota.oneapp.ui.flutter.DashboardFlutterActivity
import com.toyota.oneapp.ui.flutter.GO_TO_ACCOUNT_OPTIONS
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants

@Composable
fun TFSHeaderView(
    viewModel: TFSViewModel,
    showMenu: Boolean = true,
    account: AccountModel?,
) {
    val context = LocalContext.current
    val tfsResponse =
        rememberLauncherForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                if (viewModel.getTfsUnLinkUserStatus()) {
                    viewModel.authenticate()
                } else {
                    viewModel.updateAccountSummaryState()
                    viewModel.accountSummaryDetails()
                }
            }
        }
    Row(
        modifier =
            Modifier.padding(
                top = 12.dp,
                start = 16.dp,
            ),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        PayPageIcon(R.drawable.ic_money, contentDescription = stringResource(R.string.money_icon))
        Column(
            modifier =
                Modifier.padding(
                    top = 12.dp,
                    start = 12.dp,
                ),
        ) {
            Row {
                OABody4TextView(
                    text =
                        stringResource(
                            id = TFSProperties.tfsTitle(viewModel.getSelectedVehicle().brand),
                        ),
                    color = AppTheme.colors.tertiary03,
                    modifier = Modifier.weight(1f, true),
                )
                if (showMenu) {
                    Image(
                        modifier =
                            Modifier
                                .testTagID(AccessibilityId.ID_TFS_OPTIONS_ICON)
                                .padding(
                                    top = 12.dp,
                                    start = 12.dp,
                                    end = 16.dp,
                                )
                                .clickable {
                                    val bundleValue = Bundle()
                                    bundleValue.putSerializable(ACCOUNTS, account)
                                    val intent =
                                        DashboardFlutterActivity.createIntent(
                                            context,
                                            bundleValue,
                                            GO_TO_ACCOUNT_OPTIONS,
                                        )
                                    tfsResponse.launch(intent)
                                },
                        painter = painterResource(getMenuIcon(viewModel)),
                        contentDescription = null,
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ShowGenericCard(viewModel: TFSViewModel) {
    val context = LocalContext.current
    TFSCard(
        TfsCardData(
            viewModel = viewModel,
            title = stringResource(R.string.flexible_financing_options),
            cardContent =
                stringResource(
                    R.string.flexible_financing_options_content,
                ),
            buttonText = stringResource(R.string.learn_more),
            click = {
                ToyUtil.openCustomChromeTab(
                    context,
                    TFSProperties.learnMoreUrl(viewModel.getSelectedVehicle().brand),
                )
            },
            layoutID = AccessibilityId.ID_TFS_LEARN_MORE_CTA,
            bodyContentTestTagId = AccessibilityId.ID_TFS_LEARN_MORE_BODY_TEXT,
            titleTestTagId = AccessibilityId.ID_TFS_LEARN_MORE_TITLE_TEXT,
        ),
    )
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ShowErrorCard(
    viewModel: TFSViewModel,
    click: () -> Unit,
) {
    TFSCard(
        TfsCardData(
            viewModel = viewModel,
            title = ToyotaConstants.EMPTY_STRING,
            cardContent = "${stringResource(R.string.textLooksLikeServerError)} ${stringResource(
                R.string.please_try_again,
            )}",
            buttonText = stringResource(R.string.tap_to_refresh),
            click = click,
            layoutID = AccessibilityId.ID_TFS_ERROR_CARD_CTA,
            bodyContentTestTagId = AccessibilityId.ID_TFS_ERROR_CARD_BODY_TEXT,
            titleTestTagId = ToyotaConstants.EMPTY_STRING,
        ),
    )
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ShowUnderMaintenanceCard(
    viewModel: TFSViewModel,
    title: String,
    content: String,
) {
    TFSCard(
        TfsCardData(
            viewModel = viewModel,
            title = title,
            cardContent = content,
            buttonText = ToyotaConstants.EMPTY_STRING,
            click = {},
            showButton = false,
            layoutID = AccessibilityId.ID_TFS_UNDER_MAINTENANCE,
            bodyContentTestTagId = AccessibilityId.ID_TFS_UNDER_MAINTENANCE_BODY_TEXT,
            titleTestTagId = ToyotaConstants.EMPTY_STRING,
        ),
    )
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ShowConsentCard(
    viewModel: TFSViewModel,
    click: () -> Unit,
) {
    TFSCard(
        TfsCardData(
            viewModel = viewModel,
            title = ToyotaConstants.EMPTY_STRING,
            cardContent = stringResource(R.string.tfs_consent_card_title),
            buttonText = stringResource(R.string.continue_button),
            click = {
                click()
            },
            layoutID = AccessibilityId.ID_TFS_LEARN_MORE_CTA,
            bodyContentTestTagId = AccessibilityId.ID_TFS_LEARN_MORE_BODY_TEXT,
            titleTestTagId = AccessibilityId.ID_TFS_LEARN_MORE_TITLE_TEXT,
        ),
    )
}

@Composable
fun TfsDialogHeader(brand: String) {
    Row(
        modifier =
            Modifier.padding(
                top = 12.dp,
                start = 40.dp,
            ),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        PayPageIcon(R.drawable.ic_money, contentDescription = stringResource(R.string.money_icon))
        OABody4TextView(
            text =
                stringResource(
                    id = TFSProperties.tfsTitle(brand),
                ),
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier.weight(1f, true).padding(
                    top = 12.dp,
                    start = 12.dp,
                ),
        )
    }
}

fun getMenuIcon(viewModel: TFSViewModel): Int {
    return if (viewModel.isDarkMode()) {
        R.drawable.ic_menu_dark_mode
    } else {
        R.drawable.ic_menu_light_mode
    }
}
