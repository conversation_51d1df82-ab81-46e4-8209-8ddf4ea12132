@file:OptIn(ExperimentalMaterialApi::class)

package com.toyota.oneapp.features.pay.tfs.presentation

import android.app.Activity
import android.content.Context
import android.os.Bundle
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.commonapicalls.domain.model.DialogData
import com.toyota.oneapp.features.core.composable.OAAlertDialog
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine2TextView
import com.toyota.oneapp.features.core.composable.PayComposableShimmer
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.AppTheme.colors
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.LocalBottomSheetState
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import com.toyota.oneapp.features.dashboard.util.DashboardConstants
import com.toyota.oneapp.features.pay.tfs.application.LinkAccountState
import com.toyota.oneapp.features.pay.tfs.application.TFSAvailabilityState
import com.toyota.oneapp.features.pay.tfs.presentation.utils.TFSProperties
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.flutter.DashboardFlutterActivity
import com.toyota.oneapp.ui.flutter.GO_TO_REQUEST_OTP
import com.toyota.oneapp.ui.flutter.LINK_ACCOUNT
import com.toyota.oneapp.ui.flutter.REQUEST_OTP
import com.toyota.oneapp.util.ToyUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun TFSScreen(
    viewModel: TFSViewModel,
    bottomSheet: LocalBottomSheetState = LocalBottomSheet.current,
) {
    val context = LocalContext.current
    val tfsAvailabilityState = viewModel.tfsAvailabilityState.collectAsState().value
    PayComposableShimmer(isLoading = (tfsAvailabilityState == TFSAvailabilityState.Loading), contentAfterLoading = {
        when (tfsAvailabilityState) {
            is TFSAvailabilityState.Success -> {
                val checkEligibility = tfsAvailabilityState.tfsAvailabilityResponse
                if (checkEligibility.isTFSFeatureAvailable) {
                    if (checkEligibility.isEligibleForTFS) {
                        TFSLinkAccountScreen(viewModel, bottomSheet)
                    } else { // not eligible card
                        ShowGenericCard(viewModel = viewModel)
                    }
                } else if (checkEligibility.isUnderMaintenance) {
                    val translations = tfsAvailabilityState.tfsAvailabilityResponse.underMaintenance.translation
                    if (translations?.title != null && translations.subTitle != null) {
                        ShowUnderMaintenanceCard(
                            viewModel,
                            title = translations.title,
                            content = translations.subTitle,
                        )
                    } else {
                        ShowGenericCard(viewModel = viewModel)
                    }
                }
            }
            is TFSAvailabilityState.Error -> {
                ShowErrorCard(viewModel = viewModel, click = {
                    viewModel.checkFeatureFlagForTFS()
                })
            }
            else -> {}
        }
    })
}

@ExperimentalMaterialApi
@Composable
fun TFSLinkAccountScreen(
    viewModel: TFSViewModel,
    bottomSheet: LocalBottomSheetState,
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val linkAccountState = viewModel.linkAccountState.collectAsState().value
    PayComposableShimmer(isLoading = (linkAccountState == LinkAccountState.Loading), contentAfterLoading = {
        when (linkAccountState) {
            is LinkAccountState.GotoSummaryScreen -> {
                AccountSummaryScreen(viewModel)
            }
            is LinkAccountState.RequestOTP -> { // requestMFA
                ShowAccessAccountCard(viewModel, true)
                val tfsResponse =
                    rememberLauncherForActivityResult(
                        ActivityResultContracts.StartActivityForResult(),
                    ) { result ->
                        if (result.resultCode == Activity.RESULT_OK) {
                            viewModel.updateLinkAccountState()
                            viewModel.authorizeUser()
                        }
                    }
                val bundleValue = Bundle()
                bundleValue.putSerializable(REQUEST_OTP, linkAccountState.authenticateResponseModel)
                val intent =
                    DashboardFlutterActivity.createIntent(
                        context,
                        bundleValue,
                        GO_TO_REQUEST_OTP,
                    )
                SideEffect {
                    tfsResponse.launch(intent)
                }
            }
            is LinkAccountState.RequestDevicePrint -> {
                ShowAccessAccountCard(viewModel, true)
            }
            is LinkAccountState.ReLinkAccount -> {
                LinkAccountScreen(viewModel = viewModel, onClickCard = {
                    viewModel.authenticate(isRelinkUser = true)
                })
            }
            is LinkAccountState.Error -> {
                if (linkAccountState.userNotFound) {
                    LinkAccountScreen(viewModel = viewModel, onClickCard = {
                        coroutineScope.launchPrimaryBottomSheetAction(
                            bottomSheet,
                            screen_id = DashboardConstants.ID_LINK_ACCOUNT,
                        ) { sheetState ->
                            ShowConfirmationBottomSheet(
                                sheetState,
                                viewModel,
                            )
                        }
                    })
                } else if (linkAccountState.oneAppTokenExpired) {
                    ShowAccessAccountCard(viewModel, relink = true)
                } else if (linkAccountState.accountLocked) {
                    TfsAccountLockedScreens(viewModel = viewModel, click = {
                        viewModel.authenticate()
                    }, testTagId = AccessibilityId.ID_TFS_ACCOUNT_LOCKED)
                } else if (linkAccountState.userUnVerified) {
                    TfsAccountLockedScreens(viewModel = viewModel, click = {
                        viewModel.authenticate()
                    }, isUnVerifiedUser = true, testTagId = AccessibilityId.ID_TFS_ACCOUNT_UNVERIFIED)
                } else if (linkAccountState.serverError) {
                    ShowErrorCard(viewModel = viewModel, click = {
                        viewModel.authenticate()
                    })
                }
            }
            else -> {}
        }
    })
}

@Composable
fun LinkAccountScreen(
    viewModel: TFSViewModel,
    onClickCard: () -> Unit,
) {
    TFSCard(
        TfsCardData(
            viewModel = viewModel,
            title = stringResource(R.string.access_linking_required),
            cardContent = stringResource(TFSProperties.tfsLinkAccountContent(BuildConfig.APP_BRAND)),
            buttonText = stringResource(R.string.link_account),
            click = onClickCard,
            layoutID = AccessibilityId.ID_TFS_LINK_ACCOUNT_CTA,
            bodyContentTestTagId = AccessibilityId.ID_TFS_LINK_ACCOUNT_BODY_TEXT,
            titleTestTagId = AccessibilityId.ID_TFS_LINK_ACCOUNT_TITLE_TEXT,
        ),
    )
}

@Composable
fun ShowAccessAccountCard(
    viewModel: TFSViewModel,
    submitDevicePrint: Boolean = false,
    relink: Boolean = false,
) {
    TFSCard(
        TfsCardData(
            viewModel = viewModel,
            title = stringResource(R.string.access_account),
            cardContent =
                stringResource(
                    TFSProperties.tfsAccessAccountContent(viewModel.getSelectedVehicle().brand),
                ),
            buttonText = stringResource(R.string.continue_with_account),
            click = {
                viewModel.authenticate(submitDevicePrint = submitDevicePrint, isRelinkUser = relink)
            },
            layoutID = AccessibilityId.ID_TFS_ACCESS_ACCOUNT_CONTINUE_CTA,
            bodyContentTestTagId = AccessibilityId.ID_TFS_ACCESS_ACCOUNT_BODY_TEXT,
            titleTestTagId = AccessibilityId.ID_TFS_ACCESS_ACCOUNT_TITLE_TEXT,
        ),
    )
}

@ExperimentalMaterialApi
@Composable
fun TFSCard(tfsCardData: TfsCardData) {
    TFSCardHeader(tfsCardData.viewModel) {
        OASubHeadLine2TextView(
            text = tfsCardData.title,
            color = AppTheme.colors.tertiary03,
            textAlign = TextAlign.Center,
            modifier =
                Modifier.testTagID(
                    tfsCardData.titleTestTagId,
                ),
        )
        Spacer(modifier = Modifier.height(8.dp))
        OACallOut1TextView(
            text = tfsCardData.cardContent,
            color = AppTheme.colors.tertiary05,
            textAlign = TextAlign.Center,
            modifier =
                Modifier.testTagID(
                    tfsCardData.bodyContentTestTagId,
                ),
        )
        Spacer(modifier = Modifier.height(40.dp))
        if (tfsCardData.showButton) {
            Button(
                onClick = tfsCardData.click,
                colors =
                    ButtonDefaults.textButtonColors(
                        backgroundColor = AppTheme.colors.button02a,
                    ),
                shape = CircleShape,
                modifier =
                    Modifier
                        .padding(bottom = 16.dp)
                        .size(192.dp, 52.dp)
                        .zIndex(1f)
                        .testTagID(
                            tfsCardData.layoutID,
                        ),
            ) {
                OACallOut2TextView(
                    tfsCardData.buttonText,
                    color = AppTheme.colors.primaryButton01,
                )
            }
        }
    }
}

@ExperimentalMaterialApi
@Composable
fun TFSCardHeader(
    viewModel: TFSViewModel,
    body: @Composable () -> Unit,
) {
    Card(
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(
                    start = 16.dp,
                    end = 16.dp,
                    bottom = 16.dp,
                ),
    ) {
        Column(modifier = Modifier.background(color = AppTheme.colors.tile01)) {
            TFSHeaderView(viewModel = viewModel, showMenu = false, account = null)
            Box(
                modifier =
                    Modifier
                        .padding(top = 16.dp, start = 16.dp, end = 16.dp, bottom = 16.dp)
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(10.dp))
                        .background(color = AppTheme.colors.tile02),
                contentAlignment = Alignment.Center,
            ) {
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(top = 16.dp),
                ) {
                    body()
                }
            }
        }
    }
}

@Composable
fun ShowConsentBottomSheet(
    viewModel: TFSViewModel,
    modalBottomSheetState: ModalBottomSheetState,
    coroutineScope: CoroutineScope,
) {
    OAAlertDialog(
        DialogData(
            imageId = R.drawable.ic_small_alert,
            imageBgColor = colors.primary02,
            title = stringResource(R.string.disclosure),
            primaryButtonText = stringResource(id = R.string.tfs_i_accept),
            primaryOnClick = {
                viewModel.submitElectronicConsent()
                coroutineScope.launch {
                    modalBottomSheetState.hide()
                }
            },
            secondaryButtonText = stringResource(id = R.string.cancel),
            secondaryOnClick = {
                coroutineScope.launch {
                    modalBottomSheetState.hide()
                }
            },
            annotatedView = {
                ShowDisclosureContentForAccountSummery(viewModel.getSelectedVehicle())
            },
            header = {
                TfsDialogHeader(viewModel.getSelectedVehicle().brand)
            },
        ),
    )
}

@Composable
fun ShowConfirmationBottomSheet(
    sheetState: ModalBottomSheetState,
    viewModel: TFSViewModel,
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val tfsResponse =
        rememberLauncherForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                viewModel.updateStateOfLinkAccount()
                if (viewModel.getTfsAuthIdToken().isNotEmpty()) {
                    viewModel.authorizeUser()
                }
            }
        }
    OAAlertDialog(
        DialogData(
            imageId = R.drawable.ic_small_alert,
            imageBgColor = colors.primary02,
            title = stringResource(R.string.disclosure),
            primaryButtonText = stringResource(id = R.string.tfs_i_accept),
            primaryOnClick = {
                coroutineScope.launch { sheetState.hide() }
                val bundleValue = Bundle()
                val intent =
                    DashboardFlutterActivity.createIntent(
                        context,
                        bundleValue,
                        LINK_ACCOUNT,
                    )
                tfsResponse.launch(intent)
            },
            secondaryButtonText = stringResource(id = R.string.cancel),
            secondaryOnClick = {
                coroutineScope.launch { sheetState.hide() }
            },
            annotatedView = {
                ShowDisclosureContent(viewModel.getSelectedVehicle())
            },
            header = {
                TfsDialogHeader(viewModel.getSelectedVehicle().brand)
            },
        ),
    )
}

@Composable
fun ShowDisclosureContentForAccountSummery(vehicleInfo: VehicleInfo) {
    val content =
        DisclosureContent(
            disclosureContent = stringResource(R.string.tfs_disclosure_alert_content),
            onlinePolicyUrl = TFSProperties.urlPolicyAgreementTfsLfs(vehicleInfo.brand),
            privacyPolicyUrl = TFSProperties.urlPrivacyPolicyTfsLfs(vehicleInfo.brand),
            electronicCommunicationsUrl = TFSProperties.getTfsEboc(brand = vehicleInfo.brand),
            onlinePoliciesAgreement = stringResource(R.string.online_policies_agreement),
            privacyPolicy = stringResource(R.string.privacy_policy),
            electronicCommunicationsAgreement =
                stringResource(
                    R.string.electronic_communications_agreement,
                ),
            includingThe = stringResource(R.string.including_the),
            and = stringResource(R.string.and),
        )

    ShowDisclosureContent(content)
}

@Composable
fun ShowDisclosureContent(vehicleInfo: VehicleInfo) {
    val content =
        DisclosureContent(
            disclosureContent =
                if (vehicleInfo.brand == TFSProperties.TOYOTA) {
                    stringResource(R.string.disclosure_alert_content)
                } else {
                    stringResource(R.string.disclosure_alert_lexus_content)
                },
            onlinePolicyUrl = TFSProperties.urlPolicyAgreementTfsLfs(vehicleInfo.brand),
            privacyPolicyUrl = TFSProperties.urlPrivacyPolicyTfsLfs(vehicleInfo.brand),
            electronicCommunicationsUrl = TFSProperties.getTfsEboc(brand = vehicleInfo.brand),
            onlinePoliciesAgreement = stringResource(R.string.online_policies_agreement),
            privacyPolicy = stringResource(R.string.privacy_policy),
            electronicCommunicationsAgreement =
                stringResource(
                    R.string.electronic_communications_agreement,
                ),
            includingThe = stringResource(R.string.including_the),
            and = stringResource(R.string.and),
        )

    ShowDisclosureContent(content)
}

@Composable
fun ShowDisclosureContent(content: DisclosureContent) {
    val annotatedString =
        buildAnnotatedString {
            styles(
                SpanStyle(color = AppTheme.colors.tertiary05, fontWeight = FontWeight.Normal),
                content.disclosureContent,
            )
            append(" ")

            pushStringAnnotation(
                tag = TFSProperties.ONLINE_POLICY,
                annotation = content.onlinePolicyUrl,
            )
            styles(
                style = SpanStyle(color = AppTheme.colors.tertiary03, fontWeight = FontWeight.Bold),
                content.onlinePoliciesAgreement,
            )
            append(" ")
            pop()

            styles(
                SpanStyle(color = AppTheme.colors.tertiary05, fontWeight = FontWeight.Normal),
                content.includingThe,
            )
            append(" ")

            pushStringAnnotation(
                tag = TFSProperties.PRIVACY_POLICY,
                annotation = content.privacyPolicyUrl,
            )
            styles(
                SpanStyle(color = AppTheme.colors.tertiary03, fontWeight = FontWeight.Bold),
                content.privacyPolicy,
            )
            append(" ")
            pop()

            styles(
                SpanStyle(color = AppTheme.colors.tertiary05, fontWeight = FontWeight.Normal),
                content.and,
            )
            append(" ")

            pushStringAnnotation(
                tag = TFSProperties.ELECTRONIC_COMMUNICATIONS,
                annotation = content.electronicCommunicationsUrl,
            )
            styles(
                SpanStyle(color = AppTheme.colors.tertiary03, fontWeight = FontWeight.Bold),
                content.electronicCommunicationsAgreement,
            )
            append(" ")
            pop()
        }

    EboConsentClickableText(annotatedString)
}

@Composable
fun EboConsentClickableText(annotatedString: AnnotatedString) {
    val context = LocalContext.current
    ClickableText(
        modifier = Modifier.padding(horizontal = 16.dp),
        text = annotatedString,
        style =
            AppTheme.fontStyles.callout1.copy(
                textAlign = TextAlign.Center,
            ),
        onClick = { offset ->
            annotatedString.getStringAnnotations(
                tag = TFSProperties.PRIVACY_POLICY,
                start = offset,
                end = offset,
            ).firstOrNull()?.let {
                openExternalPage(it.item, context)
            }

            annotatedString.getStringAnnotations(
                tag = TFSProperties.ELECTRONIC_COMMUNICATIONS,
                start = offset,
                end = offset,
            ).firstOrNull()?.let {
                openExternalPage(it.item, context)
            }
            annotatedString.getStringAnnotations(
                tag = TFSProperties.ONLINE_POLICY,
                start = offset,
                end = offset,
            ).firstOrNull()?.let {
                openExternalPage(it.item, context)
            }
        },
    )
}

private fun openExternalPage(
    item: String,
    context: Context,
) {
    if (item.contains(".pdf")) {
        ToyUtil.openBrowser(context, ToyUtil.openPDF(item), null)
    } else {
        ToyUtil.openCustomChromeTab(context, item)
    }
}

fun AnnotatedString.Builder.styles(
    style: SpanStyle,
    text: String,
) {
    withStyle(style = style) {
        append(text)
    }
}

data class TfsCardData(
    val viewModel: TFSViewModel,
    val title: String,
    val cardContent: String,
    val buttonText: String,
    val click: () -> Unit,
    val layoutID: String,
    val bodyContentTestTagId: String,
    val titleTestTagId: String,
    val showButton: Boolean = true,
)

data class DisclosureContent(
    val disclosureContent: String,
    val onlinePolicyUrl: String,
    val privacyPolicyUrl: String,
    val electronicCommunicationsUrl: String,
    val onlinePoliciesAgreement: String,
    val privacyPolicy: String,
    val electronicCommunicationsAgreement: String,
    val includingThe: String,
    val and: String,
)
