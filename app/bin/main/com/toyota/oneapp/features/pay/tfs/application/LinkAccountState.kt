package com.toyota.oneapp.features.pay.tfs.application

import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthenticateResponseModel
import com.toyota.oneapp.util.ToyotaConstants

sealed class LinkAccountState {
    object Loading : LinkAccountState()

    object GotoSummaryScreen : LinkAccountState()

    object ReLinkAccount : LinkAccountState()

    data class RequestOTP(val authenticateResponseModel: AuthenticateResponseModel) : LinkAccountState()

    object RequestDevicePrint : LinkAccountState() // access Account

    data class Error(
        val serverError: Boolean = false,
        val oneAppTokenExpired: Boolean = false,
        val userUnVerified: Boolean = false,
        val userNotFound: Boolean = false,
        val accountLocked: Boolean = false,
        val sessionTimeOut: Boolean = false,
        val newOtpSent: Boolean = false,
        val errorMessage: String = ToyotaConstants.EMPTY_STRING,
    ) : LinkAccountState()
}
