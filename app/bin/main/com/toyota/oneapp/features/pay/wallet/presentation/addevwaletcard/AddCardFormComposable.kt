/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.presentation.addevwaletcard

import androidx.activity.ComponentActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.Text
import androidx.compose.material.TextFieldDefaults
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.isAllFieldsFilled
import com.toyota.oneapp.features.pay.wallet.presentation.AddEVWalletCardViewModel
import com.toyota.oneapp.util.ToyotaConstants

@Composable
fun AddCardForm(viewModel: AddEVWalletCardViewModel = hiltViewModel<AddEVWalletCardViewModel>()) {
    val activity = LocalContext.current as ComponentActivity
    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .padding(16.dp)
                .background(AppTheme.colors.tertiary12),
    ) {
        CardInfoSection(
            creditCardNumber = viewModel.cardInfo.value.creditCardNumber,
            creditCardNumberChanged = { newCreditCardNumber ->
                viewModel.updateCardInfo { it.copy(creditCardNumber = newCreditCardNumber) }
            },
            firstName = viewModel.cardInfo.value.firstName,
            firstNameChanged = { newFirstName ->
                viewModel.updateCardInfo { it.copy(firstName = newFirstName) }
            },
            lastName = viewModel.cardInfo.value.lastName,
            lastNameChanged = { newLastName ->
                viewModel.updateCardInfo { it.copy(lastName = newLastName) }
            },
            expDateChanged = { newExpDate ->
                viewModel.updateCardInfo { it.copy(expDate = newExpDate) }
            },
            ccvCode = viewModel.cardInfo.value.ccvCode,
            ccvCodeChanged = { newCCV ->
                viewModel.updateCardInfo { it.copy(ccvCode = newCCV) }
            },
        )
        Spacer(modifier = Modifier.height(16.dp))
        BillingAddressSection(
            billingAddress = viewModel.cardInfo.value.billingAddress,
            billingAddressChanged = { newBillingAddress ->
                viewModel.updateCardInfo { it.copy(billingAddress = newBillingAddress) }
            },
            city = viewModel.cardInfo.value.city,
            cityChanged = { newCity ->
                viewModel.updateCardInfo { it.copy(city = newCity) }
            },
            state = viewModel.cardInfo.value.state,
            stateChanged = { newState ->
                viewModel.updateCardInfo { it.copy(state = newState) }
            },
            zipCode = viewModel.cardInfo.value.zipCode,
            zipCodeChanged = { newZipCode ->
                viewModel.updateCardInfo { it.copy(zipCode = newZipCode) }
            },
            country = viewModel.cardInfo.value.country,
            countryChanged = { newCountry ->
                viewModel.updateCardInfo { it.copy(country = newCountry) }
            },
        )
        Spacer(modifier = Modifier.height(16.dp))
        val brandId =
            when (BuildConfig.FLAVOR_brand) {
                ToyotaConstants.LEXUS_FLAVOR_BRAND -> R.string.Common_lexus
                ToyotaConstants.SUBARU_FLAVOR_BRAND -> R.string.Common_Subaru
                else -> {
                    R.string.Common_toyota
                }
            }
        TermsAndSaveButton(
            brand = stringResource(id = brandId),
            enable = viewModel.cardInfo.value.isAllFieldsFilled(),
        ) {
            it?.let {
                viewModel.collectPubKey()?.let { pubKey ->
                    viewModel.confirmPaymentMethod(
                        activity = activity,
                        viewModel.cardInfo.value.creditCardNumber,
                        viewModel.cardInfo.value.ccvCode,
                        viewModel.cardInfo.value.expDate,
                        pubKey,
                    )
                }
            }
        }
    }
}

@Composable
fun CardInfoSection(
    creditCardNumber: String,
    creditCardNumberChanged: (String) -> Unit,
    firstName: String,
    firstNameChanged: (String) -> Unit,
    lastName: String,
    lastNameChanged: (String) -> Unit,
    expDateChanged: (String) -> Unit,
    ccvCode: String,
    ccvCodeChanged: (String) -> Unit,
) {
    Card(
        shape = RoundedCornerShape(8.dp),
        modifier =
            Modifier
                .fillMaxWidth(),
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tertiary12,
    ) {
        Column(
            modifier =
                Modifier
                    .padding(16.dp)
                    .background(AppTheme.colors.tertiary12),
        ) {
            SectionTitle(icon = {
                Icon(
                    imageVector = ImageVector.vectorResource(id = R.drawable.ic_user),
                    contentDescription =
                        stringResource(
                            id = R.string.add_ev_wallet_credit_card_info,
                        ),
                )
            }, title = stringResource(id = R.string.add_ev_wallet_credit_card_info))
            Spacer(modifier = Modifier.height(16.dp))
            CustomOutlinedTextField(
                label = stringResource(id = R.string.add_ev_wallet_credit_card_number),
                keyboardType = KeyboardType.Number,
                trailingIcon = ImageVector.vectorResource(R.drawable.ic_payment),
                onValueChanged = creditCardNumberChanged,
                value = creditCardNumber,
            )
            Spacer(modifier = Modifier.height(8.dp))
            Row {
                Column(
                    modifier =
                        Modifier
                            .weight(1f)
                            .padding(end = 4.dp),
                ) {
                    CustomOutlinedTextField(
                        label = stringResource(id = R.string.add_ev_wallet_first_name),
                        onValueChanged = firstNameChanged,
                        value = firstName,
                    )
                }
                Column(
                    modifier =
                        Modifier
                            .weight(1f)
                            .padding(end = 4.dp),
                ) {
                    CustomOutlinedTextField(
                        label = stringResource(id = R.string.add_ev_wallet_last_name),
                        onValueChanged = lastNameChanged,
                        value = lastName,
                    )
                }
            }
            Spacer(modifier = Modifier.height(8.dp))
            Row {
                Column(
                    modifier =
                        Modifier
                            .weight(1f)
                            .padding(end = 4.dp),
                ) {
                    AnimatedDateInputField(onDateChange = expDateChanged)
                }
                Column(
                    modifier =
                        Modifier
                            .weight(1f)
                            .padding(end = 4.dp),
                ) {
                    CustomOutlinedTextField(
                        label = stringResource(id = R.string.add_ev_wallet_cvv_code),
                        keyboardType = KeyboardType.Number,
                        onValueChanged = ccvCodeChanged,
                        value = ccvCode,
                    )
                }
            }
        }
    }
}

@Composable
fun BillingAddressSection(
    billingAddress: String,
    billingAddressChanged: (String) -> Unit,
    city: String,
    cityChanged: (String) -> Unit,
    state: String,
    stateChanged: (String) -> Unit,
    zipCode: String,
    zipCodeChanged: (String) -> Unit,
    country: String,
    countryChanged: (String) -> Unit,
) {
    Card(
        shape = RoundedCornerShape(8.dp),
        modifier =
            Modifier
                .fillMaxWidth()
                .background(AppTheme.colors.tertiary12),
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tertiary12,
    ) {
        Column(
            modifier =
                Modifier
                    .padding(16.dp)
                    .background(AppTheme.colors.tertiary12),
        ) {
            SectionTitle(icon = {
                Icon(
                    imageVector = Icons.Default.Home,
                    contentDescription = stringResource(id = R.string.add_ev_wallet_billing_address),
                )
            }, title = stringResource(id = R.string.add_ev_wallet_billing_address))
            Spacer(modifier = Modifier.height(16.dp))
            CustomOutlinedTextField(
                label = stringResource(id = R.string.add_ev_wallet_billing_address),
                onValueChanged = billingAddressChanged,
                value = billingAddress,
            )
            Spacer(modifier = Modifier.height(8.dp))
            Row {
                CustomOutlinedTextField(
                    label = stringResource(id = R.string.add_ev_wallet_city),
                    modifier =
                        Modifier
                            .weight(1f)
                            .padding(end = 4.dp),
                    onValueChanged = cityChanged,
                    value = city,
                )
                CustomOutlinedTextField(
                    label = stringResource(id = R.string.add_ev_wallet_state),
                    modifier =
                        Modifier
                            .weight(1f)
                            .padding(start = 4.dp),
                    onValueChanged = stateChanged,
                    value = state,
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Row {
                CustomOutlinedTextField(
                    label = stringResource(id = R.string.add_ev_wallet_zip_code),
                    keyboardType = KeyboardType.Number,
                    modifier =
                        Modifier
                            .weight(1f)
                            .padding(end = 4.dp),
                    onValueChanged = zipCodeChanged,
                    value = zipCode,
                )
                CustomOutlinedTextField(
                    label = stringResource(id = R.string.add_ev_wallet_country),
                    modifier =
                        Modifier
                            .weight(1f)
                            .padding(start = 4.dp),
                    onValueChanged = countryChanged,
                    value = country,
                )
            }
        }
    }
}

@Composable
fun AnimatedDateInputField(
    modifier: Modifier = Modifier,
    onDateChange: (String) -> Unit,
) {
    var textFieldValue by remember { mutableStateOf(TextFieldValue()) }
    var isError by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }

    fun validateAndFormatDate(input: String): String {
        val cleanInput = input.replace(Regex("[^0-9]"), "")
        if (cleanInput.isEmpty()) return ""

        // Format as MM/YYYY
        return when {
            cleanInput.length <= 2 -> cleanInput
            else -> {
                val month = cleanInput.take(2)
                val year = cleanInput.drop(2).take(4)
                "$month/$year"
            }
        }
    }

    fun validateDate(
        month: String,
        year: String,
    ): Boolean {
        val monthNum = month.toIntOrNull() ?: return false
        val yearNum = year.toIntOrNull() ?: return false
        val currentYear =
            java.time.LocalDate
                .now()
                .year

        when {
            monthNum !in 1..12 -> {
                errorMessage = "Month must be between 01-12"
                return false
            }
            year.length != 4 -> {
                errorMessage = "Year must be 4 digits (YYYY)"
                return false
            }
            yearNum < currentYear -> {
                errorMessage = "Card is expired"
                return false
            }
        }
        return true
    }

    Column {
        OutlinedTextField(
            value = textFieldValue,
            onValueChange = { newValue ->
                val formatted = validateAndFormatDate(newValue.text)
                val newSelection =
                    when {
                        // Handle backspace
                        newValue.text.length < textFieldValue.text.length -> newValue.selection
                        // Handle adding slash
                        formatted.length > textFieldValue.text.length && formatted.contains("/") ->
                            TextRange(formatted.length)
                        else -> newValue.selection
                    }

                textFieldValue =
                    TextFieldValue(
                        text = formatted,
                        selection = newSelection,
                    )

                // Validate only if we have a complete date
                if (formatted.contains("/")) {
                    val parts = formatted.split("/")
                    if (parts.size == 2) {
                        val month = parts[0]
                        val year = parts[1]
                        isError = !validateDate(month, year)
                        if (!isError) {
                            // Only send valid dates to parent
                            onDateChange("$month$year")
                        }
                    }
                }
            },
            label = {
                Text(
                    text = stringResource(id = R.string.add_ev_wallet_expiration_date),
                    style = AppTheme.fontStyles.tabLabel02,
                    color = if (isError) AppTheme.colors.tertiary10 else AppTheme.colors.tertiary05,
                )
            },
            placeholder = {
                Text(
                    text = "MM/YYYY",
                    style = AppTheme.fontStyles.tabLabel02,
                    color = AppTheme.colors.tertiary05,
                )
            },
            isError = isError,
            keyboardOptions =
                KeyboardOptions(
                    keyboardType = KeyboardType.Number,
                    imeAction = ImeAction.Next,
                ),
            colors =
                TextFieldDefaults.outlinedTextFieldColors(
                    focusedBorderColor = if (isError) AppTheme.colors.tertiary10 else Color.Gray,
                    unfocusedBorderColor = if (isError) AppTheme.colors.tertiary10 else Color.Gray,
                    textColor = AppTheme.colors.tertiary00,
                    errorBorderColor = AppTheme.colors.tertiary10,
                ),
            modifier = modifier.fillMaxWidth(),
            singleLine = true,
        )

        if (isError && errorMessage.isNotEmpty()) {
            Text(
                text = errorMessage,
                color = AppTheme.colors.tertiary10,
                style = AppTheme.fontStyles.caption1,
                modifier = Modifier.padding(start = 16.dp, top = 4.dp),
            )
        }
    }
}

@Composable
fun CustomOutlinedTextField(
    label: String,
    modifier: Modifier = Modifier,
    keyboardType: KeyboardType = KeyboardType.Text,
    trailingIcon: ImageVector? = null,
    onValueChanged: (String) -> Unit,
    value: String,
) {
    OutlinedTextField(
        value = value,
        onValueChange = onValueChanged,
        label = {
            Text(
                text = label,
                style = AppTheme.fontStyles.tabLabel02,
                color = AppTheme.colors.tertiary05,
            )
        },
        modifier =
            modifier
                .fillMaxWidth()
                .background(Color.Transparent),
        keyboardOptions = KeyboardOptions(keyboardType = keyboardType, imeAction = ImeAction.Next),
        trailingIcon = {
            trailingIcon?.let {
                Icon(imageVector = it, contentDescription = null)
            }
        },
        colors =
            TextFieldDefaults.outlinedTextFieldColors(
                focusedBorderColor = Color.Gray,
                unfocusedBorderColor = Color.Gray,
                textColor = AppTheme.colors.tertiary00,
            ),
    )
}
