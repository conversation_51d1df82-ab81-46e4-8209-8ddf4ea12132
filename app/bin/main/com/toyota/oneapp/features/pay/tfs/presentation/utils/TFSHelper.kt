package com.toyota.oneapp.features.pay.tfs.presentation.utils

import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.ToyotaConstants
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.Locale

object TFSHelper {
    private val currentDate: LocalDateTime = LocalDateTime.now(ZoneOffset.UTC)
    private val dtfInput: DateTimeFormatter =
        DateTimeFormatter.ofPattern(
            "EEE MMM dd HH:mm:ss zzz yyyy",
            Locale.ENGLISH,
        )
    private const val dateFormatWithSlash = "MM/dd/yyyy"
    private const val DATE_FORMAT_WITH_DASHES = "yyyy-MM-dd"

    fun formatStringDateToShortMonthDayYear(date: String?): String {
        val dateTime: Date
        var convertedFormat: String = ToyotaConstants.EMPTY_STRING
        if (!date.isNullOrEmpty()) {
            if (date.contains("/")) {
                dateTime = SimpleDateFormat(
                    dateFormatWithSlash,
                    AppLanguageUtils.getCurrentLocale(),
                ).parse(
                    date,
                ) ?: Date()
                convertedFormat =
                    dateTime.let {
                        SimpleDateFormat(
                            "MMM dd, yyyy",
                            AppLanguageUtils.getCurrentLocale(),
                        ).format(it)
                    }
            } else {
                dateTime = SimpleDateFormat(
                    DATE_FORMAT_WITH_DASHES,
                    AppLanguageUtils.getCurrentLocale(),
                ).parse(
                    date,
                ) ?: Date()
                convertedFormat =
                    dateTime.let {
                        SimpleDateFormat(
                            "MMM dd, yyyy",
                            AppLanguageUtils.getCurrentLocale(),
                        ).format(it)
                    }
            }
        }
        return convertedFormat
    }

    private fun dateDue(paymentDueDate: String): String {
        return if (paymentDueDate.isNotEmpty()) {
            val formattedDate = formatStringDateWithDashesPatternInDateTime(paymentDueDate)
            formattedDate?.toString() ?: ToyotaConstants.EMPTY_STRING
        } else {
            ToyotaConstants.EMPTY_STRING
        }
    }

    private fun formatStringDateWithDashesPatternInDateTime(date: String): Date? {
        return if (date.isNotEmpty()) {
            val pattern = if (date.contains("/")) dateFormatWithSlash else DATE_FORMAT_WITH_DASHES
            try {
                SimpleDateFormat(pattern, AppLanguageUtils.getCurrentLocale()).parse(date)
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }

    fun hasPastDueDate(paymentDueDate: String): Boolean {
        val dueDate = dateDue(paymentDueDate)
        return dueDate.isNotEmpty() && beforeInSameTimeZone(dueDate)
    }

    private fun beforeInSameTimeZone(date: String): Boolean {
        return try {
            val paymentDueDate: LocalDateTime =
                LocalDateTime
                    .parse(date, dtfInput)

            paymentDueDate.isBefore(currentDate)
        } catch (e: Exception) {
            false
        }
    }

    fun firstTodayOrFutureScheduledPayment(
        status: String,
        postDate: String,
    ): Boolean {
        return (
            (status.uppercase(Locale.getDefault()) == "SCHEDULED") &&
                todayOrFutureDatePayment(
                    postDate,
                )
        )
    }

    private fun todayOrFutureDatePayment(date: String): Boolean {
        return (
            todayDate(date) ||
                futureDate(date)
        )
    }

    private fun todayDate(date: String): Boolean {
        return try {
            val dateTime =
                LocalDateTime
                    .parse(dateDue(date))

            sameDay(dateTime)
        } catch (e: Exception) {
            false
        }
    }

    private fun sameDay(date: LocalDateTime): Boolean {
        return (date.year == currentDate.year) &&
            (date.month == currentDate.month) &&
            (date.dayOfMonth == currentDate.dayOfMonth)
    }

    private fun futureDate(date: String): Boolean {
        return try {
            val paymentDueDate: LocalDateTime =
                LocalDateTime
                    .parse(dateDue(date), dtfInput)

            paymentDueDate.isAfter(currentDate)
        } catch (e: Exception) {
            false
        }
    }
}
