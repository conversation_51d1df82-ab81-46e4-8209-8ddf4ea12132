package com.toyota.oneapp.features.pay.tfs.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine2TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.pay.tfs.presentation.utils.TFSProperties
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.util.ToyUtil

@Composable
fun TfsAccountLockedScreens(
    viewModel: TFSViewModel,
    click: () -> Unit,
    isUnVerifiedUser: Boolean = false,
    testTagId: String,
) {
    Card(
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(
                    start = 16.dp,
                    end = 16.dp,
                    bottom = 16.dp,
                ),
    ) {
        Column(modifier = Modifier.background(color = AppTheme.colors.tile01)) {
            TFSHeaderView(viewModel = viewModel, showMenu = false, account = null)
            Box(
                modifier =
                    Modifier
                        .padding(top = 16.dp, start = 16.dp, end = 16.dp, bottom = 16.dp)
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(10.dp))
                        .background(color = AppTheme.colors.tile02),
                contentAlignment = Alignment.Center,
            ) {
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(top = 16.dp),
                ) {
                    OASubHeadLine2TextView(
                        text =
                            if (isUnVerifiedUser) {
                                stringResource(R.string.account_unverified)
                            } else {
                                stringResource(R.string.account_locked)
                            },
                        color = AppTheme.colors.tertiary03,
                        textAlign = TextAlign.Center,
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    if (isUnVerifiedUser) {
                        AccountUnverified(vehicleInfo = viewModel.getSelectedVehicle())
                    } else {
                        AccountLocked(vehicleInfo = viewModel.getSelectedVehicle())
                    }
                    Spacer(modifier = Modifier.height(20.dp))
                    Button(
                        onClick = click,
                        colors =
                            ButtonDefaults.textButtonColors(
                                backgroundColor = AppTheme.colors.button02a,
                            ),
                        shape = CircleShape,
                        modifier =
                            Modifier
                                .padding(bottom = 16.dp)
                                .size(192.dp, 52.dp)
                                .zIndex(1f).testTagID(testTagId),
                    ) {
                        OACallOut2TextView(
                            stringResource(R.string.tap_to_refresh),
                            color = AppTheme.colors.primaryButton01,
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun AccountUnverified(vehicleInfo: VehicleInfo) {
    val context = LocalContext.current
    val annotatedString =
        buildAnnotatedString {
            withStyle(
                style = SpanStyle(color = AppTheme.colors.tertiary05, fontWeight = FontWeight.Normal),
            ) {
                append(stringResource(R.string.please_visit))
                append(" ")
            }

            pushStringAnnotation(
                tag = TFSProperties.websitePaymentUrl,
                annotation = TFSProperties.websiteLoginURL(vehicleInfo.brand),
            )
            withStyle(
                style = SpanStyle(color = AppTheme.colors.tertiary03, fontWeight = FontWeight.Bold),
            ) {
                if (vehicleInfo.brand == TFSProperties.TOYOTA) {
                    append(TFSProperties.websiteToyotaPaymentUrl)
                } else {
                    append(TFSProperties.websiteLexusPaymentUrl)
                }
                append(" ")
            }
            pop()

            withStyle(
                style = SpanStyle(color = AppTheme.colors.tertiary05, fontWeight = FontWeight.Normal),
            ) {
                append(stringResource(R.string.verify_your_account))
                append(" ")
            }
        }

    ClickableText(text = annotatedString, style = AppTheme.fontStyles.callout1, onClick = { offset ->
        annotatedString.getStringAnnotations(
            tag = TFSProperties.websitePaymentUrl,
            start = offset,
            end = offset,
        ).firstOrNull()?.let {
            ToyUtil.openCustomChromeTab(context, it.item)
        }
    })
}

@Composable
fun AccountLocked(vehicleInfo: VehicleInfo) {
    val context = LocalContext.current
    val annotatedString =
        buildAnnotatedString {
            withStyle(
                style = SpanStyle(color = AppTheme.colors.tertiary05, fontWeight = FontWeight.Normal),
            ) {
                if (vehicleInfo.brand == TFSProperties.TOYOTA) {
                    append(stringResource(R.string.toyota_registered_user))
                } else {
                    append(stringResource(R.string.lexus_registered_user))
                }
                append(" ")
            }

            pushStringAnnotation(
                tag = TFSProperties.onlineSelfSevice,
                annotation = TFSProperties.websiteForgotUserURL(vehicleInfo.brand),
            )
            withStyle(
                style = SpanStyle(color = AppTheme.colors.tertiary03, fontWeight = FontWeight.Bold),
            ) {
                append(stringResource(R.string.online_self_service))
                append(" ")
            }
            pop()

            withStyle(
                style = SpanStyle(color = AppTheme.colors.tertiary05, fontWeight = FontWeight.Normal),
            ) {
                if (vehicleInfo.brand == TFSProperties.TOYOTA) {
                    append(stringResource(R.string.unlock_toyota_account_content))
                } else {
                    append(stringResource(R.string.unlock_lexus_account_content))
                }
                append(" ")
            }

            pushStringAnnotation(
                tag = TFSProperties.onlineRegistration,
                annotation = TFSProperties.onlineRegistrationURL(vehicleInfo.brand),
            )
            withStyle(
                style = SpanStyle(color = AppTheme.colors.tertiary03, fontWeight = FontWeight.Bold),
            ) {
                append(stringResource(R.string.online_registration))
                append(" ")
            }
            pop()

            withStyle(
                style = SpanStyle(color = AppTheme.colors.tertiary05, fontWeight = FontWeight.Normal),
            ) {
                append(stringResource(R.string.create_an_account))
                append(" ")
            }
        }

    ClickableText(text = annotatedString, style = AppTheme.fontStyles.callout1, onClick = { offset ->
        annotatedString.getStringAnnotations(
            tag = TFSProperties.onlineSelfSevice,
            start = offset,
            end = offset,
        ).firstOrNull()?.let {
            ToyUtil.openCustomChromeTab(context, it.item)
        }

        annotatedString.getStringAnnotations(
            tag = TFSProperties.onlineRegistration,
            start = offset,
            end = offset,
        ).firstOrNull()?.let {
            ToyUtil.openCustomChromeTab(context, it.item)
        }
    })
}
