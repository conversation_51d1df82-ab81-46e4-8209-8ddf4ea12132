package com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel

import java.io.Serializable

data class AuthenticateResponseModel(
    val tokenId: String? = null,
    val authId: String? = null,
    val template: String? = null,
    val stage: String? = null,
    val header: String? = null,
    val callbacks: List<AuthenticateCallback>? = null,
) : Serializable

data class AuthenticateCallback(
    val type: String?,
    val output: List<AuthenticateInoutput>?,
    val input: List<AuthenticateInoutput>?,
) : Serializable

data class AuthenticateInoutput(
    val name: String?,
    val value: Any?,
    val valueList: List<Any>? = null,
    val valueInt: Int? = null,
) : Serializable
