package com.toyota.oneapp.features.pay.tfs.dataaccess.service

import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AccountSummaryResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.EbocRequest
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.KintoServiceResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.PaymentHistoryResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.RecurringPaymentPlansResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.ServerTimeStampResponse
import com.toyota.oneapp.features.pay.tfs.domain.model.AccountNumberModel
import com.toyota.oneapp.features.pay.tfs.domain.model.UCIDNumber
import com.toyota.oneapp.features.pay.tfs.presentation.utils.HeaderApiConstants.HEADER_UPSTREAM_TRACE_ID_VALUE
import com.toyota.oneapp.features.pay.tfs.presentation.utils.HeaderApiConstants.HEADER_X_CHANNEL_ID_VALUE
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Query

interface TFSApi {
    @GET("/digital-self-services/expapi/v1/accounts/accountSummary")
    suspend fun getAccountSummary(
        @Header("X-Brand-Id") brandId: String?,
        @Header("x-channel-id") xChannelId: String? = HEADER_X_CHANNEL_ID_VALUE,
        @Header("X-Upstream-Trace-Id") upStreamTraceId: String? = HEADER_UPSTREAM_TRACE_ID_VALUE,
    ): Response<AccountSummaryResponseModel?>

    @POST("/digital-self-services/expapi/v7/payments/paymenthub")
    suspend fun getPaymentHistory(
        @Header("X-Brand-Id") brandId: String?,
        @Header("x-channel-id") xChannelId: String? = HEADER_X_CHANNEL_ID_VALUE,
        @Header("X-Upstream-Trace-Id") upStreamTraceId: String? = HEADER_UPSTREAM_TRACE_ID_VALUE,
        @Body fullAccountNumber: AccountNumberModel?,
    ): Response<PaymentHistoryResponseModel?>

    @POST("/digital-self-services/expapi/v7/payments/kintoId")
    suspend fun getKintoId(
        @Header("X-Brand-Id") brandId: String?,
        @Header("x-channel-id") xChannelId: String? = HEADER_X_CHANNEL_ID_VALUE,
        @Header("X-Upstream-Trace-Id") upStreamTraceId: String? = HEADER_UPSTREAM_TRACE_ID_VALUE,
        @Body upid: UCIDNumber?,
    ): Response<KintoServiceResponseModel?>

    @GET("/digital-self-services/expapi/v7/payments/getRecurringSchedulePaymentsById")
    suspend fun getRecurringPaymentPlans(
        @Query("kintoServiceId") kintoServiceId: String?,
        @Header("X-Brand-Id") brandId: String?,
        @Header("x-channel-id") xChannelId: String? = HEADER_X_CHANNEL_ID_VALUE,
        @Header("X-Upstream-Trace-Id") upStreamTraceId: String? = HEADER_UPSTREAM_TRACE_ID_VALUE,
    ): Response<RecurringPaymentPlansResponseModel?>

    @GET("/digital-self-services/expapi/v1/profile/accounts/getTimestamp")
    suspend fun getServerTimeStamp(
        @Header("X-Brand-Id") brandId: String?,
        @Header("x-channel-id") xChannelId: String? = HEADER_X_CHANNEL_ID_VALUE,
        @Header("X-Upstream-Trace-Id") upStreamTraceId: String? = HEADER_UPSTREAM_TRACE_ID_VALUE,
    ): Response<ServerTimeStampResponse?>

    @POST("/digital-self-services/expapi/v1/accounts/agreement")
    suspend fun submitElectronicConsent(
        @Header("X-Brand-Id") brandId: String?,
        @Header("x-channel-id") xChannelId: String? = HEADER_X_CHANNEL_ID_VALUE,
        @Header("X-Upstream-Trace-Id") upStreamTraceId: String? = HEADER_UPSTREAM_TRACE_ID_VALUE,
        @Body request: EbocRequest,
    ): Response<ServerTimeStampResponse?>
}
