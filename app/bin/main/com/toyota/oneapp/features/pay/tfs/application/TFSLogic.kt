package com.toyota.oneapp.features.pay.tfs.application

import android.net.Uri
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AccessTokenRequestModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AccessTokenResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthenticateResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthorizeRequestModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.EbocRequest
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.getFirstAccount
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.toUIModel
import com.toyota.oneapp.features.pay.tfs.domain.TFSRepository
import com.toyota.oneapp.features.pay.tfs.presentation.utils.HeaderApiConstants
import com.toyota.oneapp.features.pay.tfs.presentation.utils.TFSProperties
import com.toyota.oneapp.features.pay.tfs.presentation.utils.TFSProperties.authCallBacks
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.NetworkStatus
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Locale
import javax.inject.Inject

class TFSLogic
    @Inject
    constructor(
        private val repository: TFSRepository,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val applicationData: ApplicationData,
    ) : TFSUseCase() {
        var authResponse: Resource<AuthenticateResponseModel?>? = null

        override suspend fun isFinanceServiceAvailable(vehicleInfo: VehicleInfo): Flow<TFSAvailabilityState> {
            return flow {
                val isTFSFeatureAvailable =
                    vehicleInfo.isFeatureEnabled(
                        Feature.TFS_FINANCIAL_SERVICES_V2,
                    )
                val isTFSFeatureUnderMaintenance =
                    vehicleInfo.isFeatureMaintenance(
                        Feature.TFS_FINANCIAL_SERVICES_V2,
                    )
                if (isTFSFeatureAvailable) {
                    if (!isCheckSecondaryUser(vehicleInfo)) {
                        eligibilityCheckForTFS(vehicleInfo).collect { eligibility ->
                            if (eligibility is EligibilityState.Success) {
                                emit(
                                    TFSAvailabilityState.Success(
                                        tfsAvailabilityResponse =
                                            TFSAvailabilityResponse(
                                                isTFSFeatureAvailable = true,
                                                isEligibleForTFS = eligibility.isEligible,
                                            ),
                                    ),
                                )
                            } else if (eligibility is EligibilityState.Error) {
                                emit(
                                    TFSAvailabilityState.Error(
                                        errorCode = eligibility.errorCode,
                                        errorMessage = eligibility.errorMessage,
                                    ),
                                )
                            }
                        }
                    } else {
                        emit(
                            TFSAvailabilityState.Success(
                                tfsAvailabilityResponse =
                                    TFSAvailabilityResponse(
                                        isTFSFeatureAvailable = true,
                                        isEligibleForTFS = false,
                                    ),
                            ),
                        )
                    }
                } else if (isTFSFeatureUnderMaintenance) {
                    isFinanceUnderMaintenance().collect {
                        if (it is FinancialServiceState.UnderMaintenance) {
                            emit(
                                TFSAvailabilityState.Success(
                                    tfsAvailabilityResponse =
                                        TFSAvailabilityResponse(
                                            isTFSFeatureAvailable = false,
                                            isEligibleForTFS = false,
                                            isUnderMaintenance = true,
                                            underMaintenance = it.maintenanceState,
                                        ),
                                ),
                            )
                        } else {
                            emit(
                                TFSAvailabilityState.Success(
                                    tfsAvailabilityResponse =
                                        TFSAvailabilityResponse(
                                            isTFSFeatureAvailable = true,
                                            isEligibleForTFS = false,
                                        ),
                                ),
                            )
                        }
                    }
                } else {
                    emit(
                        TFSAvailabilityState.NoTFSFeatureAvailable,
                    )
                }
            }
        }

        override suspend fun isFinanceUnderMaintenance(): Flow<FinancialServiceState> {
            return flow {
                val response = repository.fetchBanner()
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        it.data?.payload?.let { banners ->
                            if (banners.isNotEmpty()) {
                                banners.forEach { bannerPayload ->
                                    if (bannerPayload.featureName == "financialServicesV2") {
                                        when (bannerPayload.status) {
                                            "1" -> {
                                                emit(
                                                    FinancialServiceState.FinanceAvailable,
                                                )
                                            }
                                            "2" -> { // under maintainenace
                                                emit(
                                                    FinancialServiceState.UnderMaintenance(
                                                        maintenanceState = bannerPayload,
                                                    ),
                                                )
                                            }
                                            else -> {
                                                emit(
                                                    FinancialServiceState.FinanceServiceDisabled,
                                                )
                                            }
                                        }
                                    }
                                }
                            } else {
                                emit(
                                    FinancialServiceState.FinanceServiceDisabled,
                                )
                            }
                        } ?: run {
                            emit(
                                FinancialServiceState.FinanceServiceDisabled,
                            )
                        }
                    } else {
                        emit(
                            FinancialServiceState.Error(
                                errorMessage = it.error?.message,
                                errorCode = it.error?.code.toString(),
                            ),
                        )
                    }
                }
            }
        }

        override suspend fun eligibilityCheckForTFS(vehicleInfo: VehicleInfo): Flow<EligibilityState> {
            return flow {
                vehicleInfo.vin?.let { vin ->
                    val response = repository.fetchTfsEligibility(vin)
                    response.let {
                        if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                            it.data?.payload?.let { eligibilityResponse ->
                                emit(
                                    EligibilityState.Success(isEligible = eligibilityResponse.eligible),
                                )
                            }
                        } else {
                            emit(
                                EligibilityState.Error(
                                    errorCode = it.error?.responseCode,
                                    errorMessage = it.error?.message,
                                ),
                            )
                        }
                    }
                }
            }
        }

        override suspend fun isCheckSecondaryUser(vehicleInfo: VehicleInfo): Boolean {
            var isRemoteUser = false
            if (!vehicleInfo.isPrimarySubscriber && vehicleInfo.isRemoteUser) {
                isRemoteUser = true
            }
            return isRemoteUser
        }

        override suspend fun callAuthenticate(
            submitDevicePrint: Boolean,
            isRelinkUser: Boolean,
        ): Flow<LinkAccountState> {
            var authenticateResponseModel = AuthenticateResponseModel()
            return flow {
                val tfsIdToken = oneAppPreferenceModel.getTFSIdToken()
                val getUnlinkUserStatus = oneAppPreferenceModel.getTfsUnLinkUser()
                if (getUnlinkUserStatus && tfsIdToken.isEmpty()) { // relink Account
                    emit(LinkAccountState.ReLinkAccount)
                } else {
                    applicationData.getSelectedVehicle()?.let { selectedVehicle ->
                        if (submitDevicePrint && authResponse?.data != null) {
                            authenticateResponseModel =
                                AuthenticateResponseModel(
                                    stage = authResponse?.data?.stage,
                                    tokenId = authResponse?.data?.tokenId,
                                    template = authResponse?.data?.template,
                                    authId = authResponse?.data?.authId,
                                    header = authResponse?.data?.header,
                                    callbacks = authCallBacks(authResponse?.data?.callbacks),
                                )
                            authResponse =
                                repository.fetchAuthenticate(
                                    brandType = TFSProperties.isBrandLFSOrTFS(selectedVehicle.brand),
                                    tmssguid = null,
                                    body = authenticateResponseModel,
                                )
                        } else if (tfsIdToken.isNotEmpty() && !isRelinkUser) {
                            emit(LinkAccountState.GotoSummaryScreen)
                        } else {
                            authResponse =
                                repository.fetchAuthenticate(
                                    brandType = TFSProperties.isBrandLFSOrTFS(selectedVehicle.brand),
                                    tmssguid = null,
                                    body = authenticateResponseModel,
                                )
                        }

                        authResponse?.let {
                            if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                                if (it.data?.tokenId != null) {
                                    callAuthorize(it.data?.tokenId!!).collect { linkAccountState ->
                                        emit(linkAccountState)
                                    }
                                } else if (it.data?.tokenId == null) { // access account
                                    clearTFSTokens()
                                    if (TFSProperties.extractOtpEmail(authResponse?.data?.callbacks)) {
                                        // navigate to flutter otp screen
                                        authenticateResponseModel =
                                            AuthenticateResponseModel(
                                                stage = authResponse?.data?.stage,
                                                tokenId = authResponse?.data?.tokenId,
                                                template = authResponse?.data?.template,
                                                authId = authResponse?.data?.authId,
                                                header = authResponse?.data?.header,
                                                callbacks = authResponse?.data?.callbacks,
                                            )
                                        emit(
                                            LinkAccountState.RequestOTP(
                                                authenticateResponseModel = authenticateResponseModel,
                                            ),
                                        )
                                    } else {
                                        if (isRelinkUser) {
                                            submitDevicePrint(
                                                authenticateResponseModel =
                                                    AuthenticateResponseModel(
                                                        stage = authResponse?.data?.stage,
                                                        tokenId = authResponse?.data?.tokenId,
                                                        template = authResponse?.data?.template,
                                                        authId = authResponse?.data?.authId,
                                                        header = authResponse?.data?.header,
                                                        callbacks =
                                                            authCallBacks(
                                                                authResponse?.data?.callbacks,
                                                            ),
                                                    ),
                                            ).collect { submitDevicePrintState ->
                                                emit(submitDevicePrintState)
                                            }
                                        } else {
                                            emit(LinkAccountState.RequestDevicePrint)
                                        }
                                    }
                                } else {
                                    // no state
                                }
                            } else if (it.status == NetworkStatus.FAILED && it.error != null) {
                                handleErrors(it.error!!.message).collect { errorState ->
                                    emit(errorState)
                                }
                            }
                        }
                    }
                }
            }
        }

        override suspend fun callAuthorize(tfsSessionIdToken: String): Flow<LinkAccountState> {
            val authenticateRequestData =
                AuthorizeRequestModel(
                    response_type = HeaderApiConstants.RESPONSE_TYPE,
                    scope = HeaderApiConstants.SCOPE,
                    client_id = BuildConfig.TFS_CLIENT_ID,
                    csrf = tfsSessionIdToken,
                    decision = HeaderApiConstants.DECISION,
                    save_consent = 1,
                    redirect_uri = HeaderApiConstants.REDIRECT_URI,
                    code_challenge = HeaderApiConstants.CODE_CHALLENGE,
                    code_challenge_method = HeaderApiConstants.CODE_CHALLENGE_METHOD,
                )
            return flow {
                val response =
                    repository.callAuthorizeUserApi(
                        tfsSessionIdToken = tfsSessionIdToken,
                        clientID = TFSProperties.clientId,
                        body = authenticateRequestData,
                    )
                response?.let {
                    if (it.isSuccessful) {
                        // no need to do anything here
                    } else {
                        val isValidResponse = it.code() == HeaderApiConstants.VALID_AUTHORIZE_RESPONSE_CODE
                        if (isValidResponse) {
                            val location = it.headers().get(HeaderApiConstants.LOCATION).toString()
                            val uri = Uri.parse(location)
                            if (location.contains(HeaderApiConstants.CONTAINS_CODE)) {
                                val accessTokenRequestModel =
                                    AccessTokenRequestModel(
                                        grant_type = HeaderApiConstants.AUTHORIZATION_CODE,
                                        code = uri.getQueryParameter(HeaderApiConstants.RESPONSE_TYPE),
                                        client_id = BuildConfig.TFS_CLIENT_ID,
                                        redirect_uri = HeaderApiConstants.ACCESS_TOKEN_REDIRECT_URI,
                                        code_verifier = HeaderApiConstants.CODE_VERIFIER,
                                        client_secret = BuildConfig.TFS_CLIENT_SECRET,
                                    )
                                val accessTokenResponse =
                                    repository.callAccessToken(
                                        body = accessTokenRequestModel,
                                    )
                                accessTokenResponse.let { accessToken ->
                                    if (accessToken.status == NetworkStatus.SUCCESS && accessToken.data != null) {
                                        handleTFSTokens(accessToken.data).collect { isTokenStored ->
                                            if (isTokenStored) {
                                                emit(LinkAccountState.GotoSummaryScreen)
                                            }
                                        }
                                    } else {
                                        emit(
                                            LinkAccountState.Error(
                                                serverError = true,
                                                errorMessage = accessToken.error?.message ?: ToyotaConstants.EMPTY_STRING,
                                            ),
                                        )
                                    }
                                }
                            } else {
                                emit(
                                    LinkAccountState.Error(
                                        serverError = true,
                                        errorMessage = ToyotaConstants.EMPTY_STRING,
                                    ),
                                )
                            }
                        } else {
                            emit(
                                LinkAccountState.Error(
                                    serverError = true,
                                    errorMessage = ToyotaConstants.EMPTY_STRING,
                                ),
                            )
                        }
                    }
                }
            }
        }

        override suspend fun handleTFSTokens(tokenResponse: AccessTokenResponseModel?): Flow<Boolean> {
            return flow {
                tokenResponse?.let {
                    oneAppPreferenceModel.setTFSAccessToken(it.access_token)
                    oneAppPreferenceModel.setTFSRefreshToken(it.refresh_token)
                    oneAppPreferenceModel.setTFSIdToken(it.id_token)
                    oneAppPreferenceModel.setTFSExpireIn(it.expires_in)
                    if (oneAppPreferenceModel.getTFSIdToken() == it.id_token) {
                        emit(true)
                    }
                } ?: run { emit(false) }
            }
        }

        override suspend fun handleErrors(errorMessages: String): Flow<LinkAccountState> {
            return flow {
                val errorMessage = errorMessages.lowercase(Locale.getDefault())
                if (errorMessage.contains(HeaderApiConstants.USER_NOT_FOUND)) {
                    emit(LinkAccountState.Error(userNotFound = true))
                } else if (errorMessage.contains(HeaderApiConstants.TOKEN_HAS_EXPIRED) ||
                    errorMessage.contains(
                        HeaderApiConstants.EXPIRED_TOKEN,
                    )
                ) {
                    emit(LinkAccountState.Error(oneAppTokenExpired = true))
                } else if (errorMessage.contains(HeaderApiConstants.LOCKED)) {
                    emit(LinkAccountState.Error(accountLocked = true))
                } else if (errorMessage.contains(HeaderApiConstants.SESSION_TIMEOUT)) {
                    emit(LinkAccountState.Error(sessionTimeOut = true))
                } else if (errorMessage.contains(HeaderApiConstants.USER_UNVERIFIED)) {
                    emit(LinkAccountState.Error(userUnVerified = true))
                } else {
                    emit(LinkAccountState.Error(serverError = true, errorMessage = errorMessage))
                }
            }
        }

        override suspend fun accountSummary(): Flow<AccountSummaryState> {
            val vin = applicationData.getSelectedVehicleState().value?.vin
            return flow {
                val response = repository.callAccountSummary(BuildConfig.APP_BRAND)
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        var accountSummaryData = it.data?.toUIModel(vin)
                        accountSummaryData?.let { accountSummary ->
                            if (accountSummary.accountNumber.isNotEmpty() && accountSummary.fullAccountNumber.isNotEmpty()) {
                                if (TFSProperties.OPEN.equals(accountSummary.status, true)) {
                                    emit(AccountSummaryState.Success(accountSummary = accountSummary))
                                } else {
                                    emit(AccountSummaryState.AccountClosed)
                                }
                            } else {
                                accountSummaryData = it.data?.getFirstAccount(vin)
                                accountSummaryData?.let { firstAccount ->
                                    if (firstAccount.accountNumber.isNotEmpty() && firstAccount.fullAccountNumber.isNotEmpty()) {
                                        emit(AccountSummaryState.AccountNotFound)
                                    } else {
                                        emit(AccountSummaryState.AccountNotFound)
                                    }
                                }
                            }
                        }
                    } else if (it.status == NetworkStatus.FAILED && it.error != null) {
                        emit(
                            AccountSummaryState.Error(
                                errorCode = it.error?.responseCode,
                                errorMessage = it.error?.message,
                            ),
                        )
                    }
                }
            }
        }

        override suspend fun getPaymentHistory(
            accountNumber: String?,
            ucid: String?,
        ): Flow<PaymentHistoryState> {
            val response =
                repository.callPaymentHistory(
                    brandId = BuildConfig.APP_BRAND,
                    accountNumber = accountNumber,
                )
            return flow {
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        emit(PaymentHistoryState.Success(it.data?.toUIModel()))
                    } else if (it.status == NetworkStatus.FAILED && it.error != null) {
                        emit(
                            PaymentHistoryState.Error(
                                errorCode = it.error?.code.toString(),
                                errorMessage = it.error?.message,
                            ),
                        )
                    }
                }
            }
        }

        override suspend fun submitDevicePrint(authenticateResponseModel: AuthenticateResponseModel): Flow<LinkAccountState> {
            return flow {
                applicationData.getSelectedVehicle()?.let { selectedVehicle ->
                    authResponse =
                        repository.fetchAuthenticate(
                            brandType = TFSProperties.isBrandLFSOrTFS(selectedVehicle.brand),
                            tmssguid = null,
                            body = authenticateResponseModel,
                        )
                    authResponse?.let {
                        if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                            if (it.data?.tokenId != null) {
                                callAuthorize(it.data?.tokenId!!).collect { linkAccountState ->
                                    emit(linkAccountState)
                                }
                            } else if (it.data?.tokenId == null) { // access account
                                clearTFSTokens()
                                if (TFSProperties.extractOtpEmail(authResponse?.data?.callbacks)) {
                                    // navigate to flutter otp screen
                                    emit(
                                        LinkAccountState.RequestOTP(
                                            authenticateResponseModel =
                                                AuthenticateResponseModel(
                                                    stage = authResponse?.data?.stage,
                                                    tokenId = authResponse?.data?.tokenId,
                                                    template = authResponse?.data?.template,
                                                    authId = authResponse?.data?.authId,
                                                    header = authResponse?.data?.header,
                                                    callbacks = authResponse?.data?.callbacks,
                                                ),
                                        ),
                                    )
                                }
                            }
                        } else if (it.status == NetworkStatus.FAILED && it.error != null) {
                            handleErrors(it.error!!.message).collect { errorState ->
                                emit(errorState)
                            }
                        }
                    }
                }
            }
        }

        override fun clearTFSTokens() {
            oneAppPreferenceModel.setTFSAccessToken(ToyotaConstants.EMPTY_STRING)
            oneAppPreferenceModel.setTFSRefreshToken(ToyotaConstants.EMPTY_STRING)
            oneAppPreferenceModel.setTFSIdToken(ToyotaConstants.EMPTY_STRING)
            oneAppPreferenceModel.setTFSExpireIn(0)
        }

        fun submitElectronicConsent(): Flow<LinkAccountState> {
            return flow {
                val timeStampResponse = repository.getServerTimeStamp(BuildConfig.APP_BRAND)
                timeStampResponse.data?.response?.let { serverTime ->

                    val agreementResponse =
                        repository.submitElectronicConsent(
                            EbocRequest(
                                ocTimeStamp = serverTime,
                                ebaTimeStamp = serverTime,
                                browserTimeStamp = serverTime,
                            ),
                            BuildConfig.APP_BRAND,
                        )

                    agreementResponse.data?.let {
                        emit(LinkAccountState.GotoSummaryScreen)
                    } ?: run {
                        emit(
                            LinkAccountState.Error(
                                serverError = true,
                                errorMessage = agreementResponse.message,
                            ),
                        )
                    }
                } ?: run {
                    emit(
                        LinkAccountState.Error(
                            serverError = true,
                            errorMessage = timeStampResponse.message,
                        ),
                    )
                }
            }
        }
    }
