/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.pay.wallet.domain.model.WalletDetailResponse

data class EvWalletCardTransactions(
    val payload: CardPayload,
    val status: Status,
)

data class CardPayload(
    val transactions: List<CardTransactionsResponse>?,
)

data class CardTransactionsResponse(
    @SerializedName("id")
    val id: String,
    @SerializedName("customer")
    val customer: CardCustomerResponse?,
    @SerializedName("merchant")
    val merchant: CardMerchantResponse?,
    @SerializedName("product")
    val product: CardProductResponse?,
    @SerializedName("amount")
    val amount: Int,
    @SerializedName("currency")
    val currency: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("paymentMethod")
    val paymentMethod: WalletDetailResponse,
)

data class CardProductResponse(val id: String)

data class CardMerchantResponse(val id: String)

data class CardCustomerResponse(val id: String)

data class CardTransactions(
    val id: String,
    val customerId: String,
    val merchantId: String,
    val productId: String,
    val amount: Int,
    val currency: String,
    val status: String,
    val description: String,
    val createdAt: String,
    val paymentMethod: WalletDetailResponse,
)

fun CardTransactionsResponse.toCardTransactions() =
    CardTransactions(
        id = id,
        customerId = customer?.id.orEmpty(),
        merchantId = merchant?.id.orEmpty(),
        productId = product?.id.orEmpty(),
        amount = amount,
        currency = currency,
        status = status,
        description = description,
        createdAt = createdAt,
        paymentMethod = paymentMethod,
    )

fun List<CardTransactionsResponse>.toCardTransactionsList() = map { it.toCardTransactions() }
