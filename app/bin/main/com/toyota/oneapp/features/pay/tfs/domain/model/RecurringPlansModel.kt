package com.toyota.oneapp.features.pay.tfs.domain.model

data class RecurringPaymentPlans(
    val plans: ArrayList<RecurringPlansModel>,
)

data class RecurringPlansModel(
    val recurringPlanId: String?,
    val status: String?,
    val frequency: String?,
    val period: String?,
    val paymentMethodID: String?,
    val postDate: String?,
    val paymentAmount: String?,
    val achAccountNumberMask: String?,
)
