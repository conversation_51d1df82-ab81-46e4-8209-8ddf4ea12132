package com.toyota.oneapp.features.pay.tfs.presentation.utils

import android.content.res.Resources
import android.os.Build
import com.eclipsesource.v8.Platform
import com.google.gson.Gson
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthenticateCallback
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthenticateInoutput
import com.toyota.oneapp.features.pay.tfs.domain.model.DevicePrintModel
import com.toyota.oneapp.features.pay.tfs.domain.model.FontData
import com.toyota.oneapp.features.pay.tfs.domain.model.PluginData
import com.toyota.oneapp.features.pay.tfs.domain.model.ScreenData
import com.toyota.oneapp.util.ToyotaConstants
import org.forgerock.android.auth.ui.FRNative
import org.forgerock.android.auth.ui.entity.Geolocation
import java.util.Locale
import java.util.TimeZone

object TFSProperties {
    const val appName = "ONEAPP"
    const val clientId = "oneapp"
    const val LEXUS = "L"
    const val TOYOTA = "T"
    const val ELECTRONIC_COMMUNICATIONS = "electronic_communications_agreement"
    const val ONLINE_POLICY = "online_policies_agreement"
    const val PRIVACY_POLICY = "privacy_policy"
    const val websitePaymentUrl = "WebsitePaymentUrl"
    const val onlineSelfSevice = "Online Self Service"
    const val onlineRegistration = "Online Registration"
    const val PRIVACY_POLICY_TITLE = "Privacy Policy"
    const val ONLINE_POLICY_TITLE = "Online Policies & Agreements"
    const val websiteToyotaPaymentUrl = "toyotafinancial.com"
    const val websiteLexusPaymentUrl = "lexusfinancial.com"
    const val ELECTRONIC_COMMUNICATIONS_TITLE = "Electronic Communications and Agreement"
    const val OPEN = "OPEN"
    const val openPdfUrl = "https://drive.google.com/viewerng/viewer?embedded=true&url="
    const val K_ONE_TIME_ACCESS_CODE = "ONE TIME ACCESS CODE"
    val ACCOUNT_SUMMERY_ERROR_CODE = listOf("EEF0497", "EEF0256", "EEF0255", "EEF0257", "EEF0603")

    fun definitionKey(brand: String): String {
        return if (brand.equals(TOYOTA, true)) {
            "API_myFS_BankAcctAdd_TFS_email"
        } else {
            "API_myFS_BankAcctAdd_LFS_email"
        }
    }

    fun isBrandLFSOrTFS(brand: String): String {
        return if (brand.equals(TOYOTA, true)) {
            "TFS"
        } else {
            "LFS"
        }
    }

    fun learnMoreUrl(brand: String): String {
        return if (brand.equals(TOYOTA, true)) {
            "https://www.toyotafinancial.com/us/en.html"
        } else {
            "https://www.lexusfinancial.com/us/en.html"
        }
    }

    fun tfsTitle(brand: String): Int {
        return if (brand.equals(TOYOTA, true)) {
            R.string.toyota_financial_services
        } else {
            R.string.lexus_financial_services
        }
    }

    fun tfsAccessAccountContent(brand: String): Int {
        return if (brand.equals(TOYOTA, true)) {
            R.string.click_below_to_toyota_access
        } else {
            R.string.click_below_to_lexus_access
        }
    }

    fun tfsLinkAccountContent(brand: String): Int {
        return if (brand.equals(TOYOTA, true)) {
            R.string.link_your_account_toyota
        } else {
            R.string.link_your_account_lexus
        }
    }

    private fun websitePaymentUrl(brand: String): String {
        return if (brand.equals(TOYOTA, true)) {
            "https://www.toyotafinancial.com"
        } else {
            "https://www.lexusfinancial.com"
        }
    }

    fun websiteLoginURL(brand: String): String {
        return "${websitePaymentUrl(brand)}/us/en/consumer-web/home/<USER>"
    }

    fun urlPrivacyPolicyTfsLfs(brand: String): String {
        return "${urlPolicyAgreementTfsLfs(brand)}#opp"
    }

    fun websiteForgotUserURL(brand: String): String = "${websitePaymentUrl(brand)}/us/en/consumer-web/self-service/forgot-credential"

    fun onlineRegistrationURL(brand: String): String = "${websitePaymentUrl(brand)}/us/en/consumer-web/nextgen-registration"

    fun leaseEndURL(brand: String) = "${websitePaymentUrl(brand)}/us/en/end_of_lease_options/your_option.html"

    fun faqUrl(brand: String) = "${websitePaymentUrl(brand)}/us/en/end_of_lease_options/faqs.html"

    fun urlPolicyAgreementTfsLfs(brand: String): String {
        return "${websitePaymentUrl(brand)}/us/en/online_policies_and_agreements.html"
    }

    fun getTfsEboc(brand: String): String {
        return "${websitePaymentUrl(brand)}/content/dam/dcx-app/t001/tfs/assets/pdfs/Electronic_Business_Agreement.pdf"
    }

    fun authCallBacks(callback: List<AuthenticateCallback>?): List<AuthenticateCallback> {
        val authenticateCallback: ArrayList<AuthenticateCallback> = arrayListOf()
        callback?.map { authCallBack ->
            authenticateCallback.add(
                AuthenticateCallback(
                    type = authCallBack.type,
                    output = authCallBack.output,
                    input =
                        inputAuthenticateInOutPut(authCallBack.input).ifEmpty {
                            null
                        },
                ),
            )
        }
        return authenticateCallback
    }

    private fun getDevicePrint(): String {
        val displayMetrics = Resources.getSystem().displayMetrics
        val screenHeight: Int = displayMetrics.heightPixels
        val screenWidth: Int = displayMetrics.widthPixels

        val geo =
            if (FRNative.currentLocation != null) {
                Geolocation(
                    latitude = FRNative.currentLocation?.latitude,
                    longitude = FRNative.currentLocation?.longitude,
                )
            } else {
                null
            }

        val devicePrint =
            DevicePrintModel(
                geolocation = geo,
                screen =
                    ScreenData(
                        screenHeight = screenHeight,
                        screenWidth = screenWidth,
                        screenColourDepth = 0,
                    ),
                platform = Platform.ANDROID,
                timezone = TimeZone.getDefault().getDisplayName(false, TimeZone.SHORT),
                language = "${FRNative.nativeLocale.language}-${FRNative.nativeLocale.country}",
                plugins = PluginData(installedPlugins = ToyotaConstants.EMPTY_STRING),
                fonts = FontData(installedFonts = ToyotaConstants.EMPTY_STRING),
                userAgent = Platform.ANDROID,
                appName = appName,
                appCodeName = Platform.ANDROID,
                appVersion = BuildConfig.VERSION_NAME,
                buildID = Build.ID,
                oscpu = BuildConfig.VERSION_NAME,
                product = ToyotaConstants.EMPTY_STRING,
                productSub = Build.MODEL,
            )

        return Gson().toJson(devicePrint)
    }

    private fun inputAuthenticateInOutPut(authenticateCallback: List<AuthenticateInoutput?>?): List<AuthenticateInoutput> {
        var authenticateInoutput: AuthenticateInoutput? = null
        authenticateCallback?.map { inputElement ->
            when (inputElement?.name) {
                "IDToken1" -> {
                    authenticateInoutput =
                        AuthenticateInoutput(
                            name = inputElement.name,
                            valueInt = -1,
                            value = "",
                            valueList = listOf(),
                        )
                }
                "IDToken2" -> {
                    authenticateInoutput =
                        AuthenticateInoutput(
                            name = inputElement.name,
                            valueInt = 0,
                            value = getDevicePrint(),
                            valueList = listOf(),
                        )
                }
                else -> {
                    authenticateInoutput =
                        AuthenticateInoutput(
                            name = inputElement?.name,
                            value = inputElement?.value,
                            valueInt = inputElement?.valueInt,
                            valueList = inputElement?.valueList,
                        )
                }
            }
        }
        return if (authenticateInoutput != null) {
            listOf(authenticateInoutput!!)
        } else {
            listOf()
        }
    }

    fun extractOtpEmail(callbacks: List<AuthenticateCallback>?): Boolean {
        var isRequestOTP = false
        callbacks?.forEach { element ->
            if (element.type == "TextOutputCallback") {
                element.output?.forEach { outputElement ->
                    if (outputElement.value is String &&
                        outputElement.value.uppercase(
                            Locale.getDefault(),
                        ).contains(K_ONE_TIME_ACCESS_CODE)
                    ) {
                        isRequestOTP = true
                    }
                }
            }
        }
        return isRequestOTP
    }
}
