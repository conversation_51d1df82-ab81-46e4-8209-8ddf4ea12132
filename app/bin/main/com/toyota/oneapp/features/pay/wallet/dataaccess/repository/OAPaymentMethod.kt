/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.dataaccess.repository

import android.content.Context
import androidx.activity.ComponentActivity
import com.stripe.android.PaymentConfiguration
import com.stripe.android.Stripe
import com.stripe.android.model.ConfirmSetupIntentParams
import javax.inject.Inject

interface OAPaymentMethod {
    fun initPaymentMethod(pubKey: String): Stripe

    fun confirmPaymentSetup(
        stripe: Stripe,
        clientSecret: String,
        paymentId: String,
        activity: ComponentActivity,
    )
}

class StripeOAPaymentMethod
    @Inject
    constructor(
        val context: Context,
    ) : OAPaymentMethod {
        override fun initPaymentMethod(pubKey: String): Stripe {
            PaymentConfiguration.init(context, pubKey)
            return Stripe(context, PaymentConfiguration.getInstance(context).publishableKey)
        }

        override fun confirmPaymentSetup(
            stripe: Stripe,
            clientSecret: String,
            paymentId: String,
            activity: ComponentActivity,
        ) {
            val confirmSetupIntentParams =
                ConfirmSetupIntentParams.create(
                    paymentMethodId = paymentId,
                    clientSecret = clientSecret,
                )

            stripe.confirmSetupIntent(
                activity = activity,
                confirmSetupIntentParams = confirmSetupIntentParams,
            )
        }
    }
