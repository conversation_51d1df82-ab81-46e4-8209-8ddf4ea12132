package com.toyota.oneapp.features.pay.wallet.application

import com.toyota.oneapp.features.pay.wallet.domain.model.WalletDetailEvCardResponse

sealed class WalletDetailState {
    object Loading : WalletDetailState()

    data class Success(
        val walletDetails: WalletDetailEvCardResponse,
    ) : WalletDetailState()

    data class Error(val errorCode: String?, val errorMessage: String?) : WalletDetailState()
}
