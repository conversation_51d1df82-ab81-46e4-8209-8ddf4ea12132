package com.toyota.oneapp.features.pay.wallet.application

import com.toyota.oneapp.features.pay.wallet.domain.model.WalletDetailEvCardResponse

sealed class WalletFFState {
    object Loading : WalletFFState()

    object NoWalletFeatureAvailable : WalletFFState()

    data class Success(
        val walletDetails: WalletDetailEvCardResponse? = null,
        val isEVVehicle: Boolean = false,
        val isWalletWithSubscription: Boolean = false,
        val noWallet: Boolean = false,
    ) : WalletFFState()

    data class Error(val errorCode: String?, val errorMessage: Int) : WalletFFState()
}
