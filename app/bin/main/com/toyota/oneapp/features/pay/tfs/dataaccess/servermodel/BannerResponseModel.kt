package com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel

import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.Status

data class BannerResponseModel(
    val status: Status,
    val payload: List<BannerPayload>?,
    val code: Int,
)

data class BannerPayload(
    val featureName: String? = null,
    val status: String? = null,
    val translation: Translation? = null,
)

data class Translation(
    val language: String? = null,
    val title: String? = null,
    val subTitle: String? = null,
    val category: String? = null,
    val image: String? = null,
    val link: String? = null,
    val linkText: String? = null,
    val linkTarget: String? = null,
)
