package com.toyota.oneapp.features.pay.tfs.dataaccess.repository

import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AccessTokenRequestModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AccessTokenResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AccountSummaryResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthenticateResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthorizeRequestModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthorizeUserResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.BannerResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.EbocRequest
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.FinancialServiceEligibilityModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.KintoServiceResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.PaymentHistoryResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.RecurringPaymentPlansResponseModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.RefreshTokenRequestModel
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.ServerTimeStampResponse
import com.toyota.oneapp.features.pay.tfs.dataaccess.service.TFSApi
import com.toyota.oneapp.features.pay.tfs.dataaccess.service.TFSAuthenticateApi
import com.toyota.oneapp.features.pay.tfs.dataaccess.service.TFSAuthorizeApi
import com.toyota.oneapp.features.pay.tfs.dataaccess.service.TFSEligibilityApi
import com.toyota.oneapp.features.pay.tfs.domain.TFSRepository
import com.toyota.oneapp.features.pay.tfs.domain.model.AccountNumberModel
import com.toyota.oneapp.features.pay.tfs.domain.model.UCIDNumber
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import retrofit2.Response
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class TFSDefaultRepository
    @Inject
    constructor(
        val service: TFSEligibilityApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
        val tfsAuthenticateApi: TFSAuthenticateApi,
        val authorizeApi: TFSAuthorizeApi,
        val tfsApi: TFSApi,
    ) : TFSRepository(errorParser, ioContext) {
        override suspend fun fetchBanner(): Resource<BannerResponseModel?> {
            return makeApiCall {
                service.banner()
            }
        }

        override suspend fun fetchTfsEligibility(vin: String): Resource<FinancialServiceEligibilityModel?> {
            return makeApiCall {
                service.financialServiceEligibilityCheck(vin)
            }
        }

        override suspend fun fetchAuthenticate(
            brandType: String,
            tmssguid: String?,
            body: AuthenticateResponseModel?,
        ): Resource<AuthenticateResponseModel?> {
            return makeApiCall {
                tfsAuthenticateApi.authenticate(
                    brandType = brandType,
                    tmssguid = tmssguid,
                    body = body,
                )
            }
        }

        override suspend fun callAuthorizeUserApi(
            tfsSessionIdToken: String?,
            clientID: String?,
            body: AuthorizeRequestModel,
        ): Response<AuthorizeUserResponseModel>? {
            return authorizeApi.authorize(
                clientID = body.client_id,
                scope = body.scope,
                responseType = body.response_type,
                csrf = body.csrf,
                saveContent = body.save_consent,
                redirectUri = body.redirect_uri,
                codeChallange = body.code_challenge,
                codeChallangeMethod = body.code_challenge_method,
                decision = body.decision,
                ssoSession = "SSOSESSION=$tfsSessionIdToken",
            )
        }

        override suspend fun callAccessToken(body: AccessTokenRequestModel): Resource<AccessTokenResponseModel?> {
            return makeApiCall {
                authorizeApi.accessToken(
                    grantType = body.grant_type,
                    code = body.code,
                    codeVerifier = body.code_verifier,
                    clientID = body.client_id,
                    clientSecret = body.client_secret,
                    redirectUri = body.redirect_uri,
                )
            }
        }

        override suspend fun callAccountSummary(brandId: String?): Resource<AccountSummaryResponseModel?> {
            return makeApiCall {
                tfsApi.getAccountSummary(brandId = brandId)
            }
        }

        override suspend fun callPaymentHistory(
            brandId: String?,
            accountNumber: String?,
        ): Resource<PaymentHistoryResponseModel?> {
            return makeApiCall {
                tfsApi.getPaymentHistory(
                    brandId = brandId,
                    fullAccountNumber = AccountNumberModel(accountNumber),
                )
            }
        }

        override suspend fun callKintoService(
            brandId: String?,
            ucId: String?,
        ): Resource<KintoServiceResponseModel?> {
            return makeApiCall {
                tfsApi.getKintoId(brandId = brandId, upid = UCIDNumber(upid = ucId))
            }
        }

        override suspend fun callRecurringPlans(
            brandId: String?,
            kintoServiceId: String?,
        ): Resource<RecurringPaymentPlansResponseModel?> {
            return makeApiCall {
                tfsApi.getRecurringPaymentPlans(kintoServiceId = kintoServiceId, brandId = brandId)
            }
        }

        override suspend fun callRefreshToken(body: RefreshTokenRequestModel): Resource<AccessTokenResponseModel?> {
            return makeApiCall {
                authorizeApi.refreshToken(
                    grantType = body.grant_type,
                    clientID = body.client_id,
                    clientSecret = body.client_secret,
                    refreshToken = body.refresh_token,
                )
            }
        }

        override suspend fun getServerTimeStamp(brandId: String?): Resource<ServerTimeStampResponse?> {
            return makeApiCall {
                tfsApi.getServerTimeStamp(brandId = brandId)
            }
        }

        override suspend fun submitElectronicConsent(
            body: EbocRequest,
            brandId: String?,
        ): Resource<ServerTimeStampResponse?> {
            return makeApiCall {
                tfsApi.submitElectronicConsent(request = body, brandId = brandId)
            }
        }
    }
