package com.toyota.oneapp.features.pay.tfs.application

import com.toyota.oneapp.features.pay.tfs.domain.model.AccountModel
import com.toyota.oneapp.features.pay.tfs.domain.model.PaymentHistoryTransactions
import com.toyota.oneapp.features.pay.tfs.domain.model.RecurringPaymentPlans

sealed class PaymentHistoryState {
    object Loading : PaymentHistoryState()

    data class Success(
        val paymentHistory: PaymentHistoryTransactions?,
    ) : PaymentHistoryState()

    data class Error(val errorCode: String?, val errorMessage: String?) : PaymentHistoryState()
}

sealed class KintoState {
    object Loading : KintoState()

    data class Success(
        val paymentHistory: AccountModel?,
    ) : KintoState()

    data class Error(val errorCode: String?, val errorMessage: String?) : KintoState()
}

sealed class RecurringPlansState {
    object Loading : RecurringPlansState()

    data class Success(
        val paymentHistory: RecurringPaymentPlans?,
    ) : RecurringPlansState()

    data class Error(val errorCode: String?, val errorMessage: String?) : RecurringPlansState()
}
