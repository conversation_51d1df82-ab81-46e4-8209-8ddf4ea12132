package com.toyota.oneapp.features.pay.tfs.presentation

import android.app.Activity
import android.os.Bundle
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.composable.OATextTitle2TextView
import com.toyota.oneapp.features.core.composable.PayComposableShimmer
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.launchSecondaryBottomSheetAction
import com.toyota.oneapp.features.pay.tfs.application.AccountSummaryState
import com.toyota.oneapp.features.pay.tfs.application.PaymentHistoryState
import com.toyota.oneapp.features.pay.tfs.domain.model.AccountModel
import com.toyota.oneapp.features.pay.tfs.presentation.utils.TFSHelper
import com.toyota.oneapp.features.pay.tfs.presentation.utils.TFSProperties
import com.toyota.oneapp.ui.flutter.ACCOUNTS
import com.toyota.oneapp.ui.flutter.DashboardFlutterActivity
import com.toyota.oneapp.ui.flutter.GO_TO_PAYMENTS
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.launch

@Composable
fun AccountSummaryScreen(viewModel: TFSViewModel) {
    val accountSummaryState = viewModel.tfsAccountSummaryState.collectAsState().value
    val coroutines = rememberCoroutineScope()
    val bottomSheetState = LocalBottomSheet.current
    bottomSheetState.secondarySheetShape.value =
        RoundedCornerShape(
            topStart = 16.dp,
            topEnd = 16.dp,
        )
    BackHandler {
        coroutines.launch {
            if (bottomSheetState.secondarySheetState.value.isVisible) {
                bottomSheetState.secondarySheetState.value.hide()
            }
        }
    }

    LaunchedEffect(accountSummaryState) {
        if (accountSummaryState is AccountSummaryState.Error) {
            coroutines.launchSecondaryBottomSheetAction(bottomSheetState) { state ->
                ShowConsentBottomSheet(viewModel, state, coroutineScope = coroutines)
            }
        }
    }

    PayComposableShimmer(
        isLoading = (accountSummaryState == AccountSummaryState.Loading),
        contentAfterLoading = {
            when (accountSummaryState) {
                is AccountSummaryState.Success -> {
                    accountSummaryState.accountSummary?.let {
                        TFSPaymentCard(it, viewModel)
                    }
                }

                is AccountSummaryState.Error -> {
                    if (TFSProperties.ACCOUNT_SUMMERY_ERROR_CODE.contains(
                            accountSummaryState.errorCode,
                        )
                    ) {
                        ShowConsentCard(
                            viewModel = viewModel,
                            click = {
                                coroutines.launchSecondaryBottomSheetAction(bottomSheetState) { state ->
                                    ShowConsentBottomSheet(viewModel, state, coroutineScope = coroutines)
                                }
                            },
                        )
                    } else {
                        ShowErrorCard(viewModel = viewModel, click = {
                            viewModel.accountSummaryDetails()
                        })
                    }
                }

                is AccountSummaryState.AccountClosed -> {
                    TFSNoAccountCard(
                        viewModel = viewModel,
                        cardContent = stringResource(R.string.account_closed),
                    )
                }

                is AccountSummaryState.AccountNotFound -> {
                    TFSNoAccountCard(
                        viewModel = viewModel,
                        cardContent = stringResource(R.string.account_not_found),
                    )
                }

                else -> {
                }
            }
        },
    )
}

@Composable
fun TFSPaymentCard(
    account: AccountModel,
    viewModel: TFSViewModel,
) {
    var accountNumberLastFour: String = ToyotaConstants.EMPTY_STRING
    var convertedDateFormat = ToyotaConstants.EMPTY_STRING
    if (account.accountNumber.isNotEmpty()) {
        accountNumberLastFour = account.accountNumber.substring(account.accountNumber.length - 4)
    }

    if (account.paymentdueDate?.isNotEmpty() == true) {
        convertedDateFormat = TFSHelper.formatStringDateToShortMonthDayYear(account.paymentdueDate)
    }
    val tfsResponse =
        rememberLauncherForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                viewModel.updateAccountSummaryState()
                viewModel.accountSummaryDetails()
            }
        }

    val context = LocalContext.current
    Card(
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(
                    start = 16.dp,
                    end = 16.dp,
                    bottom = 16.dp,
                ),
    ) {
        Column(modifier = Modifier.background(color = AppTheme.colors.tile01)) {
            TFSHeaderView(viewModel, account = account)
            Box(
                contentAlignment = Alignment.TopCenter,
            ) {
                Box(
                    modifier =
                        Modifier
                            .padding(top = 16.dp, start = 16.dp, end = 16.dp, bottom = 16.dp)
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(10.dp))
                            .background(color = AppTheme.colors.tile02),
                    contentAlignment = Alignment.Center,
                ) {
                    Column(
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.padding(top = 20.dp),
                    ) {
                        OATextTitle2TextView(
                            text = "\$${account.paymentAmount}",
                            color = AppTheme.colors.tertiary03,
                            textAlign = TextAlign.Center,
                            modifier =
                                Modifier.testTagID(
                                    AccessibilityId.ID_TFS_PAYMENT_AMOUNT_TEXT,
                                ),
                        )
                        Spacer(modifier = Modifier.height(10.dp))
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center,
                            modifier = Modifier.testTagID(AccessibilityId.ID_TFS_PAYMENT_DATE_TEXT),
                        ) {
                            OACallOut1TextView(
                                text = stringResource(R.string.due_on),
                                color = AppTheme.colors.tertiary05,
                                textAlign = TextAlign.Center,
                            )
                            Spacer(modifier = Modifier.width(5.dp))
                            OABody4TextView(
                                text = convertedDateFormat,
                                color = AppTheme.colors.tertiary05,
                            )
                        }
                        OACallOut1TextView(
                            text = "${stringResource(R.string.account_xx)}$accountNumberLastFour",
                            color = AppTheme.colors.tertiary05,
                            textAlign = TextAlign.Center,
                            modifier =
                                Modifier.testTagID(
                                    AccessibilityId.ID_TFS_ACCOUNT_NUMBER_TEXT,
                                ),
                        )
                        Spacer(modifier = Modifier.height(25.dp))
                        Button(
                            onClick = {
                                viewModel.logFirebaseEventWithParameter(
                                    AnalyticsEventParam.TFS_MAKE_A_PAYMENT_CTA,
                                )
                                val bundleValue = Bundle()
                                bundleValue.putSerializable(ACCOUNTS, account)
                                val intent =
                                    DashboardFlutterActivity.createIntent(
                                        context,
                                        bundleValue,
                                        GO_TO_PAYMENTS,
                                    )
                                tfsResponse.launch(intent)
                            },
                            colors =
                                ButtonDefaults.textButtonColors(
                                    backgroundColor = AppTheme.colors.button02a,
                                ),
                            shape = CircleShape,
                            modifier =
                                Modifier
                                    .padding(bottom = 16.dp)
                                    .size(192.dp, 52.dp)
                                    .zIndex(1f)
                                    .testTagID(AccessibilityId.ID_TFS_MAKE_A_PAYMENT_CTA),
                        ) {
                            OACallOut2TextView(
                                stringResource(R.string.make_a_payment),
                                color = AppTheme.colors.primaryButton01,
                            )
                        }
                    }
                }

                when (val paymentHistoryState = viewModel.paymentHistoryState.collectAsState().value) {
                    is PaymentHistoryState.Loading -> {}
                    is PaymentHistoryState.Success -> {
                        var isScheduled = false
                        val isHasPastDueDate =
                            account.pastDueAmount > 0.0 &&
                                TFSHelper.hasPastDueDate(
                                    account.paymentdueDate ?: ToyotaConstants.EMPTY_STRING,
                                )
                        var tagText: String? = null
                        var bgColor: Color? = null
                        var textColor: Color? = null
                        paymentHistoryState.paymentHistory?.transactionList?.forEach {
                            if (it.status != null && it.postDate != null) {
                                if (TFSHelper.firstTodayOrFutureScheduledPayment(
                                        it.status,
                                        it.postDate,
                                    )
                                ) {
                                    isScheduled = true
                                }
                            }
                        }

                        if (isHasPastDueDate) {
                            tagText = stringResource(R.string.past_due)
                            bgColor = AppTheme.colors.primaryLight02
                            textColor = AppTheme.colors.primary01
                        } else if (isScheduled) {
                            tagText = stringResource(R.string.scheduled)
                            bgColor = AppTheme.colors.primaryLightBlue
                            textColor = AppTheme.colors.button03b
                        }
                        tagText?.let {
                            Card(
                                modifier =
                                    Modifier
                                        .align(Alignment.TopCenter)
                                        .width(150.dp)
                                        .padding(2.dp)
                                        .height(34.dp)
                                        .align(Alignment.Center),
                                backgroundColor = bgColor ?: AppTheme.colors.primaryLightBlue,
                                shape = RoundedCornerShape(20.dp),
                                elevation = 0.dp,
                            ) {
                                Box(contentAlignment = Alignment.Center) {
                                    OACallOut1TextView(
                                        text = tagText,
                                        color = textColor ?: AppTheme.colors.button03b,
                                        textAlign = TextAlign.Center,
                                    )
                                }
                            }
                        }
                    }
                    // No need to show error UI as per In-Market
                    is PaymentHistoryState.Error -> {}
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun TFSNoAccountCard(
    viewModel: TFSViewModel,
    cardContent: String,
) {
    TFSCardHeader(viewModel) {
        Spacer(modifier = Modifier.height(8.dp))
        OACallOut1TextView(
            text = cardContent,
            color = AppTheme.colors.tertiary05,
            textAlign = TextAlign.Center,
        )
    }
}
