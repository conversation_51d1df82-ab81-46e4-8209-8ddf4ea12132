package com.toyota.oneapp.features.pay.tfs.domain.model

import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.CustomerDTO
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.VehicleDTO
import java.io.Serializable

data class AccountModel(
    val id: Int?,
    val encryptedAccountNumber: String,
    val status: String,
    val accountNumber: String,
    val accountType: String,
    val fullAccountNumber: String,
    val contractType: String,
    val monthlyPaymentAmount: String,
    val payoffAmount: String?,
    val financialAccountType: String,
    val paymentAmount: Double,
    val maturityDate: String,
    val paymentdueDate: String?,
    val financialAccountTypeDesc: String,
    val pastDueAmount: Double,
    val lateCharges: Double,
    val miscCharges: Double,
    val customer: CustomerDTO?,
    val vehicle: VehicleDTO?,
) : Serializable
