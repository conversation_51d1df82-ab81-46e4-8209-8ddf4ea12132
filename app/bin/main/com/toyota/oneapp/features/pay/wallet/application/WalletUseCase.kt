package com.toyota.oneapp.features.pay.wallet.application

import com.toyota.oneapp.features.pay.wallet.domain.model.WalletEvCard
import com.toyota.oneapp.features.pay.wallet.domain.model.WalletEvCardLogoResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

abstract class WalletUseCase {
    abstract suspend fun isWalletPresent(vehicleInfo: VehicleInfo): Flow<WalletFFState>

    abstract suspend fun isWalletEnableWithSubscription(vehicleInfo: VehicleInfo): Flow<WalletFFState>

    abstract suspend fun fetchWallet(): Flow<WalletFFState>

    abstract suspend fun fetchWalletImages(brand: String): Flow<WalletCardImagesState>

    abstract suspend fun evCardLogo(
        brand: String,
        walletImages: WalletEvCardLogoResponse?,
    ): Flow<WalletEvCard?>

    abstract fun getEVWallet(): Flow<CollectEVWalletState>

    abstract fun getWalletTransactions(): Flow<WalletTransactionState>

    abstract fun getCardTransactions(
        last4: String,
        expiry: String,
    ): Flow<CardTransactionState>

    abstract fun setEvDefaultCard(paymentMethod: String): Flow<SetDefaultCardState>

    abstract fun fetchClientSecret(): Flow<WalletSetupIntentState>

    abstract fun fetchPubKey(): Flow<PubKeySetupState>

    abstract fun deleteEVCard(
        paymentMethod: String,
        isDefault: Boolean,
    ): Flow<DeleteCardState>

    abstract fun logFirebaseEventWithParameter(event: String)
}
