package com.toyota.oneapp.features.pay.tfs.dataaccess.service

import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthenticateResponseModel
import com.toyota.oneapp.features.pay.tfs.presentation.utils.HeaderApiConstants
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Query

interface TFSAuthenticateApi {
    @POST("/auth/json/realms/root/realms/oneapp/authenticate")
    suspend fun authenticate(
        @Body body: AuthenticateResponseModel?,
        @Header("brandType") brandType: String,
        @Header("tmssguid") tmssguid: String?,
        @Header("Content-Type") contentType: String = HeaderApiConstants.HEADER_CONTENT_TYPE_VALUE,
        @Header("x-channel-id") xChannelId: String = HeaderApiConstants.HEADER_X_CHANNEL_ID_VALUE,
        @Query("authIndexType") authIndexType: String = HeaderApiConstants.AUTH_INDEX_TYPE_VALUE,
        @Query("authIndexValue") authIndexValue: String = HeaderApiConstants.AUTH_INDEX_VALUE_VALUE,
    ): Response<AuthenticateResponseModel>
}
// @Header("id_token") tmnaIdToken: String,
