package com.toyota.oneapp.features.pay.wallet.dataaccess.repository

import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.DefaultCard
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.EVWalletTransactions
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.EvWalletCardTransactions
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletEvCardImages
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletResponse
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletSetupIntent
import com.toyota.oneapp.features.pay.wallet.dataaccess.service.WalletApi
import com.toyota.oneapp.features.pay.wallet.domain.repository.WalletRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class WalletDefaultRepository
    @Inject
    constructor(
        val service: WalletApi,
        errorParser: <PERSON>rror<PERSON><PERSON>ageParser,
        ioContext: CoroutineContext,
    ) : WalletRepository(errorParser, ioContext) {
        override suspend fun fetchWalletDetails(): Resource<WalletResponse?> =
            makeApiCall {
                service.getWallet()
            }

        override suspend fun fetchEvWalletCardImages(): Resource<WalletEvCardImages?> =
            makeApiCall {
                service.walletImages()
            }

        override suspend fun fetchWalletSetupIntent(): Resource<WalletSetupIntent?> =
            makeApiCall {
                service.walletSetupIntent()
            }

        override suspend fun fetchWalletTransactions(): Resource<EVWalletTransactions?> =
            makeApiCall {
                service.getWalletTransactions()
            }

        override suspend fun fetchCardTransactions(
            last4: String,
            expiry: String,
        ): Resource<EvWalletCardTransactions?> =
            makeApiCall {
                service.getCardTransactions(
                    last4 = last4,
                    expiry = expiry,
                )
            }

        override suspend fun deleteEvCard(paymentMethodId: String): Resource<Unit?> =
            makeApiCall {
                service.deleteCard(paymentMethodId)
            }

        override suspend fun setDefaultCard(paymentMethodId: String): Resource<DefaultCard?> =
            makeApiCall {
                service.setDefaultCard(paymentMethodId)
            }
    }
