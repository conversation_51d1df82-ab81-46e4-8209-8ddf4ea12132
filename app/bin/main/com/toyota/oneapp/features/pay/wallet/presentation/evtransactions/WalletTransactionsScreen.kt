/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.presentation.evtransactions

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Card
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.NavTitleSection
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.date.DateParseHelper
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.pay.wallet.application.CollectEVWalletState
import com.toyota.oneapp.features.pay.wallet.application.WalletCardData
import com.toyota.oneapp.features.pay.wallet.application.WalletTransactionState
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.CardTransactions
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletCard
import com.toyota.oneapp.features.pay.wallet.domain.model.WalletDetailResponse
import com.toyota.oneapp.features.pay.wallet.presentation.WalletViewModel
import com.toyota.oneapp.features.pay.wallet.presentation.utils.WalletCardStack
import java.text.SimpleDateFormat
import java.util.Locale

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun WalletTransactionsScreen(
    navController: NavHostController,
    viewModel: WalletViewModel = hiltViewModel<WalletViewModel>(),
) {
    BackHandler {
        navController.navigate(OAScreen.Pay.route)
    }
    LaunchedEffect(Unit) {
        viewModel.checkWalletTransactions()
        viewModel.collectEVWallet()
    }
    Scaffold(
        backgroundColor = AppTheme.colors.tertiary12,
        content = {
            Column(
                modifier = Modifier.background(AppTheme.colors.tertiary15),
            ) {
                NavTitleSection(
                    navController,
                    stringResource(R.string.charging_stations),
                    stringResource(R.string.charging_stations),
                )
                WalletContent(viewModel, navController)
            }
        },
    )
}

@Composable
private fun WalletContent(
    viewModel: WalletViewModel,
    navController: NavHostController,
) {
    when (val walletState = viewModel.evWalletState.collectAsState().value) {
        is CollectEVWalletState.Loading -> {
            ShowProgressIndicator(dialogState = true)
        }

        is CollectEVWalletState.Success -> {
            walletState.defaultCard?.id?.let {
                walletState.defaultCard.card?.let { _ ->
                    Column(modifier = Modifier.fillMaxSize()) {
                        WalletCardSection(walletState.evWallet, viewModel, navController)
                    }
                }
            }
        }

        else -> {
            // Do Nothing.
        }
    }
}

@Composable
private fun WalletCardSection(
    walletList: List<WalletDetailResponse?>?,
    viewModel: WalletViewModel,
    navController: NavHostController,
) {
    val transactions =
        when (val state = viewModel.walletTransactionState.collectAsState().value) {
            is WalletTransactionState.Success ->
                state.transactions

            is WalletTransactionState.Loading,
            is WalletTransactionState.Error,
            -> null
        }
    Box(
        modifier =
            Modifier
                .fillMaxWidth(),
    ) {
        val cardListing = processWalletCards(walletList, viewModel)
        WalletCardStack(
            cards = cardListing,
            navController = navController,
            transactionsCount = transactions?.size ?: 0,
            transactions = transactions,
        )
    }
}

@Composable
private fun TransactionSection(viewModel: WalletViewModel) {
    val transactionState = viewModel.walletTransactionState.collectAsState().value
    when (transactionState) {
        is WalletTransactionState.Success -> {
            if (!transactionState.transactions.isNullOrEmpty()) {
                Text(
                    text = "Transactions",
                    style = AppTheme.fontStyles.subHeadline1,
                    modifier = Modifier.padding(16.dp),
                )
                LazyColumn(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    items(transactionState.transactions.size) { index ->
                        TransactionCard(
                            transaction = transactionState.transactions[index],
                        )
                    }
                }
            } else {
                NoTransactions()
            }
        }

        is WalletTransactionState.Error -> {
            NoTransactions()
        }

        else -> {
            // Do Nothing.
        }
    }
}

fun formatDate(dateString: String): String {
    val helper = DateParseHelper()
    val date = helper.parse(dateString) ?: return ""
    val outputFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
    return outputFormat.format(date)
}

fun formatCurrency(amount: Int): String {
    val decimalAmount = amount / 100.0
    return String.format(Locale.getDefault(), "%.2f", decimalAmount)
}

// Merchant ID to name mapping
private val merchantIdToName =
    mapOf(
        "622fdf8f901e9a45ffef3e27" to R.string.chargePointLowerCase,
        "67e44c00ed678f779a1bcea2" to R.string.ionna_uppercase,
        "66d9de63c1960700847d55eb" to R.string.ionna_uppercase,
    )

@Composable
fun TransactionCard(transaction: CardTransactions) {
    val last4 = transaction.paymentMethod.card?.last4 ?: ""
    val amount = formatCurrency(transaction.amount)
    val createdAt = transaction.createdAt

    // Try to get merchant name from description first (for charging stations)
    val merchantName =
        if (transaction.description.contains("CP Payment Session Id", ignoreCase = true)) {
            stringResource(R.string.chargePointLowerCase)
        } else if (transaction.description.contains(
                "IONNA Payment Session Id",
                ignoreCase = true,
            )
        ) {
            stringResource(R.string.ionna_uppercase)
        } else {
            // Fall back to merchant ID mapping
            stringResource(merchantIdToName[transaction.merchantId] ?: R.string.chargePointLowerCase)
        }

    Card(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(8.dp),
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tertiary15,
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Card image on the left
            Box(
                modifier =
                    Modifier
                        .size(60.dp),
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_american_express_logo),
                    contentDescription = "Card Brand",
                    modifier = Modifier.size(40.dp),
                )
            }

            // Transaction details
            Column(
                modifier = Modifier.fillMaxWidth(),
            ) {
                Text(
                    text = merchantName,
                    style = AppTheme.fontStyles.body1,
                    color = AppTheme.colors.tertiary03,
                    modifier = Modifier.padding(bottom = 4.dp),
                )
                Text(
                    text = "•••••$last4 • $$amount • ${formatDate(createdAt)}",
                    style = AppTheme.fontStyles.body1,
                    color = AppTheme.colors.tertiary03,
                )
            }
        }
    }
}

@Composable
fun NoTransactions() {
    Card(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(16.dp),
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tertiary15,
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Box(
                modifier =
                    Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(AppTheme.colors.primary02)
                        .wrapContentSize(Alignment.Center),
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_calendar),
                    contentDescription = "Charge Stations Button",
                    modifier =
                        Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                            .background(AppTheme.colors.primary02),
                    colorFilter = ColorFilter.tint(AppTheme.colors.primary01),
                )
            }
            Text(
                text = "No Transactions Found",
                textAlign = TextAlign.Center,
                style = AppTheme.fontStyles.headline1,
                color = AppTheme.colors.tertiary03,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun testNoTrans() {
    NoTransactions()
}

@Preview(showBackground = true)
@Composable
fun TransactionCardPreview() {
    Column {
        TransactionCard(
            CardTransactions(
                id = "",
                customerId = "",
                merchantId = "622fdf8f901e9a45ffef3e27",
                productId = "622fd310d23675799dd2a992",
                amount = 10,
                currency = "usd",
                status = "success",
                description = "CP Payment Session Id",
                createdAt = "2025-04-14T22:24:37.033Z",
                paymentMethod =
                    WalletDetailResponse(
                        id = "",
                        paymentMethodDefault = true,
                        type = "card",
                        card =
                            WalletCard(
                                brand = "amex",
                                country = "US",
                                expiryMonth = 7,
                                expiryYear = 2029,
                                funding = "credit",
                                last4 = "4004",
                            ),
                    ),
            ),
        )
        TransactionCard(
            CardTransactions(
                id = "",
                customerId = "",
                merchantId = "67e44c00ed678f779a1bcea2",
                productId = "67e449e12bd1fd1b12ecd6b4",
                amount = 45,
                currency = "usd",
                status = "success",
                description = "IONNA Payment Session Id",
                createdAt = "2025-04-14T18:01:43.83Z",
                paymentMethod =
                    WalletDetailResponse(
                        id = "",
                        paymentMethodDefault = true,
                        type = "card",
                        card =
                            WalletCard(
                                brand = "amex",
                                country = "US",
                                expiryMonth = 7,
                                expiryYear = 2029,
                                funding = "credit",
                                last4 = "4004",
                            ),
                    ),
            ),
        )
    }
}

private fun processWalletCards(
    walletList: List<WalletDetailResponse?>?,
    viewModel: WalletViewModel,
): List<WalletCardData> {
    val cardListing = mutableListOf<WalletCardData>()
    walletList?.forEach { walletdetails ->
        walletdetails
            ?.card
            ?.last4
            ?.let { cardNumber ->
                walletdetails.id?.let { cardId ->
                    val expiryMonth = walletdetails.card.expiryMonth
                    val expiryYear = walletdetails.card.expiryYear
                    val expiry =
                        viewModel.formatExpiry(
                            expMonth = expiryMonth,
                            expYear = expiryYear,
                        )
                    WalletCardData(
                        cardName = walletdetails.card.brand,
                        cardNumber = cardNumber,
                        isDefault = walletdetails.paymentMethodDefault,
                        brand =
                            viewModel.formatBrandName(
                                walletdetails.card.brand,
                            ),
                        cardId = cardId,
                        expiry = expiry,
                    )
                }
            }?.let { evWalletCard ->
                cardListing.add(
                    evWalletCard,
                )
            }
    }
    return cardListing.toList()
}
