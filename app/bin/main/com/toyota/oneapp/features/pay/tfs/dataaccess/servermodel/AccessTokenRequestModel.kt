package com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel

data class AccessTokenRequestModel(
    val grant_type: String,
    val code: String?,
    val client_id: String,
    val redirect_uri: String,
    val code_verifier: String,
    val client_secret: String,
)

data class AuthorizeApiError(val message: String? = null)

data class RefreshTokenRequestModel(
    val grant_type: String,
    val client_id: String,
    val client_secret: String,
    val refresh_token: String,
)
