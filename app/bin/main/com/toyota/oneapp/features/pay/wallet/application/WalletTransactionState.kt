/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.pay.wallet.application

import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.CardTransactions

sealed class WalletTransactionState {
    object Loading : WalletTransactionState()

    data class Success(
        val transactions: List<CardTransactions>? = null,
    ) : WalletTransactionState()

    data class Error(val errorCode: String?, val errorMessage: String?) : WalletTransactionState()
}
