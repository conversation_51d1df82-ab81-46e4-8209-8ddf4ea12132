package com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel

import com.toyota.oneapp.features.pay.tfs.domain.model.PaymentHistoryTransactions
import com.toyota.oneapp.features.pay.tfs.domain.model.Transactions

data class PaymentHistoryResponseModel(
    val billerAvailable: Boolean,
    val citiAvailable: Boolean,
    val kintoAvailable: Boolean,
    val payments: ArrayList<PaymentsDTO>,
    val wuAvailable: Boolean,
)

data class PaymentsDTO(
    val id: String?,
    val paymentAmount: String?,
    val postDate: String?,
    val createDate: String?,
    val frequency: String?,
    val type: String?,
    val status: String?,
    val channel: String?,
    val gateway: String?,
    val partyNumber: String?,
    val paymentCategory: String?,
    val recurringPaymentPlanId: String?,
    val confirmCode: String?,
    val paymentId: String?,
    val comments: String?,
    val bank: BankDTO?,
)

data class BankDTO(
    val id: String?,
    val type: String?,
    val accountNumber: String?,
    val routing: String?,
)

fun PaymentHistoryResponseModel.toUIModel(): PaymentHistoryTransactions {
    val transactionList = ArrayList<Transactions>()
    payments.forEach {
        transactionList.add(
            Transactions(
                id = it.id,
                paymentAmount = it.paymentAmount,
                postDate = it.postDate,
                status = it.status,
                confirmCode = it.confirmCode,
                frequency = it.frequency,
                channel = it.channel,
                paymentCategory = it.paymentCategory,
                type = it.type,
                paymentId = it.paymentId,
                recurringPaymentPlanId = it.recurringPaymentPlanId,
            ),
        )
    }
    return PaymentHistoryTransactions(transactionList = transactionList)
}
