package com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel

import com.toyota.oneapp.features.pay.wallet.domain.model.WalletDetailResponse

data class WalletResponse(
    val payload: WalletPayload?,
    val status: Status?,
)

data class WalletPayload(
    val paymentMethods: List<WalletPaymentMethod>?,
)

data class WalletPaymentMethod(
    val billingDetails: BillingDetails,
    val card: WalletCard,
    val createdAt: String,
    val default: Boolean,
    val deleted: Boolean,
    val id: String,
    val type: String,
    val updatedAt: String,
)

data class BillingDetails(
    val address: Address,
    val email: String,
    val name: String,
    val phone: String,
)

data class WalletCard(
    val brand: String,
    val country: String,
    val expiryMonth: Int,
    val expiryYear: Int,
    val funding: String,
    val last4: String,
)

data class Address(
    val city: String,
    val country: String,
    val line1: String,
    val line2: String,
    val postalCode: String,
    val state: String,
)

fun WalletPaymentMethod.toUIModel(): WalletDetailResponse {
    return WalletDetailResponse(
        id = id,
        paymentMethodDefault = default,
        type = type,
        card = card,
    )
}
