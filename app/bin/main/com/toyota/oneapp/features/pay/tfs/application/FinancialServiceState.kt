package com.toyota.oneapp.features.pay.tfs.application

import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.BannerPayload

sealed class FinancialServiceState {
    object Loading : FinancialServiceState()

    object FinanceAvailable : FinancialServiceState()

    object FinanceServiceDisabled : FinancialServiceState()

    data class UnderMaintenance(
        val maintenanceState: BannerPayload = BannerPayload(),
    ) : FinancialServiceState()

    data class Error(val errorCode: String?, val errorMessage: String?) : FinancialServiceState()
}
