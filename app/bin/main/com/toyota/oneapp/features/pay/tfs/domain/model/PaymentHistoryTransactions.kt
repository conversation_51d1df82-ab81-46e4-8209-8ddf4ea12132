package com.toyota.oneapp.features.pay.tfs.domain.model

data class PaymentHistoryTransactions(val transactionList: ArrayList<Transactions>)

data class Transactions(
    val id: String?,
    val paymentAmount: String?,
    val postDate: String?,
    val status: String?,
    val confirmCode: String?,
    val frequency: String?,
    val channel: String?,
    val paymentCategory: String?,
    val type: String?,
    val paymentId: String?,
    val recurringPaymentPlanId: String?,
)
