package com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel

import com.toyota.oneapp.features.pay.tfs.domain.model.AccountModel
import com.toyota.oneapp.util.ToyotaConstants
import java.io.Serializable

data class AccountSummaryResponseModel(
    val accounts: ArrayList<AccountDTO>?,
    val error: String,
    val errors: List<Errors>?,
)

data class AccountDTO(
    val encryptedAccountNumber: String = ToyotaConstants.EMPTY_STRING,
    val id: Int? = null,
    val status: String = ToyotaConstants.EMPTY_STRING,
    val accountNumber: String = ToyotaConstants.EMPTY_STRING,
    val accountType: String = ToyotaConstants.EMPTY_STRING,
    val fullAccountNumber: String = ToyotaConstants.EMPTY_STRING,
    val contractType: String = ToyotaConstants.EMPTY_STRING,
    val monthlyPaymentAmount: String = ToyotaConstants.EMPTY_STRING,
    val payoffAmount: String? = ToyotaConstants.EMPTY_STRING,
    val financialAccountType: String = ToyotaConstants.EMPTY_STRING,
    val paymentAmount: Double = 0.0,
    val maturityDate: String = ToyotaConstants.EMPTY_STRING,
    val paymentdueDate: String? = ToyotaConstants.EMPTY_STRING,
    val financialAccountTypeDesc: String = ToyotaConstants.EMPTY_STRING,
    val lastPaymentAmount: Double = 0.0,
    val pastDueAmount: Double = 0.0,
    val nextPaymentAmount: Double = 0.0,
    val lateCharges: Double = 0.0,
    val miscCharges: Double = 0.0,
    val currentBalance: String = ToyotaConstants.EMPTY_STRING,
    val customer: CustomerDTO? = null,
    val vehicle: VehicleDTO? = null,
)

data class CustomerDTO(
    val listOfCoborrower: List<CoBorrowerDTO>?,
    val ucid: String,
    val fullName: String,
    val lastName: String,
    val firstName: String,
    val customerType: String,
    val last4Ssn: String?,
    val middleName: String?,
    val mktgMail: String?,
    val borrowerName: String?,
    val locked: Boolean?,
    val username: String?,
    val guid: String,
    val accountManagementEmail: String?,
) : Serializable

data class VehicleDTO(
    val imagePath: String = ToyotaConstants.EMPTY_STRING,
    val year: String,
    val model: String,
    val make: String,
    val vin: String,
) : Serializable

data class CoBorrowerDTO(val name: String)

data class Errors(
    val code: String,
    val message: String,
)

fun AccountSummaryResponseModel.toUIModel(vin: String?): AccountModel {
    val accountsData = ArrayList<AccountDTO>()

    accounts?.forEach {
        accountsData.add(it)
    }

    var accountDetails = AccountDTO()
    accountsData.forEach {
        if (it.fullAccountNumber.isNotEmpty()) {
            if (it.vehicle?.vin == vin) {
                accountDetails = it
            }
        }
    }
    return AccountModel(
        id = accountDetails.id,
        encryptedAccountNumber = accountDetails.encryptedAccountNumber,
        status = accountDetails.status,
        accountNumber = accountDetails.accountNumber,
        accountType = accountDetails.accountType,
        fullAccountNumber = accountDetails.fullAccountNumber,
        contractType = accountDetails.contractType,
        monthlyPaymentAmount = accountDetails.monthlyPaymentAmount,
        payoffAmount = accountDetails.payoffAmount,
        financialAccountType = accountDetails.financialAccountType,
        paymentAmount = accountDetails.paymentAmount,
        maturityDate = accountDetails.maturityDate,
        paymentdueDate = accountDetails.paymentdueDate,
        financialAccountTypeDesc = accountDetails.financialAccountTypeDesc,
        pastDueAmount = accountDetails.pastDueAmount,
        lateCharges = accountDetails.lateCharges,
        miscCharges = accountDetails.miscCharges,
        customer = accountDetails.customer,
        vehicle = accountDetails.vehicle,
    )
}

fun AccountSummaryResponseModel.getFirstAccount(vin: String?): AccountModel {
    val accountsData = ArrayList<AccountDTO>()

    accounts?.forEach {
        accountsData.add(it)
    }
    val accountDetails: AccountDTO = accountsData.first()

    return AccountModel(
        id = accountDetails.id,
        encryptedAccountNumber = accountDetails.encryptedAccountNumber,
        status = accountDetails.status,
        accountNumber = accountDetails.accountNumber,
        accountType = accountDetails.accountType,
        fullAccountNumber = accountDetails.fullAccountNumber,
        contractType = accountDetails.contractType,
        monthlyPaymentAmount = accountDetails.monthlyPaymentAmount,
        payoffAmount = accountDetails.payoffAmount,
        financialAccountType = accountDetails.financialAccountType,
        paymentAmount = accountDetails.paymentAmount,
        maturityDate = accountDetails.maturityDate,
        paymentdueDate = accountDetails.paymentdueDate,
        financialAccountTypeDesc = accountDetails.financialAccountTypeDesc,
        pastDueAmount = accountDetails.pastDueAmount,
        lateCharges = accountDetails.lateCharges,
        miscCharges = accountDetails.miscCharges,
        customer = accountDetails.customer,
        vehicle = accountDetails.vehicle,
    )
}
