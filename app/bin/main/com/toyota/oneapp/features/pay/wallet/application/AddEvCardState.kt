/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.pay.wallet.application

import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.Status

sealed class AddEvCardState {
    data object StandBy : AddEvCardState()

    data class Loading(
        val status: Status? = null,
    ) : AddEvCardState()

    data class Success(
        val last4: String?,
    ) : AddEvCardState()

    data class Error(
        val errorCode: String?,
        val errorMessage: String?,
    ) : AddEvCardState()
}
