package com.toyota.oneapp.features.vehiclesoftware.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class SoftwareUpdatePayload(
    @SerializedName("latestAvailableUpdate") var latestAvailableUpdate: Boolean?,
    @SerializedName("previousAvailableUpdate") var previousAvailableUpdate: Boolean?,
    @SerializedName("latestUpdate") var latestUpdate: LatestUpdateResponse?,
    @SerializedName("previousUpdate") var previousUpdate: PreviousUpdate?,
    @SerializedName("update") var update: Boolean?,
    @SerializedName("help") var help: String?,
)
