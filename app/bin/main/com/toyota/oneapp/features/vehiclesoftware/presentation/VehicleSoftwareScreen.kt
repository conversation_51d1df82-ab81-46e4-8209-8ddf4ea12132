package com.toyota.oneapp.features.vehiclesoftware.presentation

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.ExpandableCard
import com.toyota.oneapp.features.core.composable.NavTitleSection
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OACaption1TextView
import com.toyota.oneapp.features.core.composable.OAHeadline1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme.colors
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.vehiclesoftware.application.VehicleSoftwareState
import com.toyota.oneapp.features.vehiclesoftware.domain.model.SoftwareUpdatesDetails
import kotlinx.coroutines.launch

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun VehicleSoftwareScreen(
    navHostController: NavHostController,
    modalBottomSheetState: ModalBottomSheetState? = null,
    vehicleSoftwareViewModel: VehicleSoftwareViewModel = hiltViewModel(),
) {
    BackHandler {
        navHostController.popBackStack()
    }
    val notificationStatus =
        navHostController.previousBackStackEntry?.savedStateHandle?.get<Int>(
            Constants.VEHICLE_SOFTWARE_NOTIFICATION_STATUS,
        )

    LaunchedEffect(key1 = Unit) {
        vehicleSoftwareViewModel.fetchSoftwareUpdates(notificationStatus)
    }
    val vehicleSoftwareState = vehicleSoftwareViewModel.vehicleSoftwareState.collectAsState()
    when (vehicleSoftwareState.value) {
        is VehicleSoftwareState.Success -> {
            val status = (vehicleSoftwareState.value as VehicleSoftwareState.Success)
            if (status.data?.isUpdate == true) {
                InformationLayout(
                    modalBottomSheetState,
                    R.drawable.ic_small_tic,
                    R.string.your_software_upto_date,
                    R.string.your_software_upto_date_description,
                    colors.secondary02,
                    navHostController,
                )
            } else {
                Scaffold(
                    content = {
                        Column(
                            Modifier
                                .padding(horizontal = 16.dp)
                                .fillMaxWidth()
                                .fillMaxHeight()
                                .background(colors.tertiary15)
                                .verticalScroll(rememberScrollState()),
                            horizontalAlignment = Alignment.CenterHorizontally,
                        ) {
                            NavTitleSection(
                                navHostController,
                                stringResource(id = R.string.vehicle_software),
                                AccessibilityId.ID_VEHICLE_SOFTWARE_BACK_ARROW_CTA,
                            )
                            VersionLayout(status.data)
                            status.data?.status?.let { status ->
                                StatusLayout(status = status)
                            }
                            status.data?.installDate?.let { installDate ->
                                InstallDateLayout(installDate)
                            }
                            status.data?.instructions?.let { instructions ->
                                InstructionLayout(instructions)
                            }
                            status.data?.descriptions?.let { descriptions ->
                                DescriptionLayout(descriptions)
                            }
                            status.data?.workingTime?.let { workingTime ->
                                WorkingTimeLayout(workingTime)
                            }
                            status.data?.previousUpdates?.let { previousUpdates ->
                                PreviousUpdateLayout(previousUpdates)
                            }
                        }
                    },
                )
            }
        }

        is VehicleSoftwareState.Loading -> {
            ShowProgressIndicator(dialogState = true)
        }
        is VehicleSoftwareState.Error -> {
            InformationLayout(
                modalBottomSheetState,
                R.drawable.ic_small_alert,
                R.string.software_updated_failed_title,
                R.string.software_updated_failed_description,
                colors.primary02,
                navHostController,
            )
        }
        else -> {
            LaunchedEffect(Unit) {
                navHostController.popBackStack()
            }
        }
    }
}

@Composable
fun VersionLayout(data: SoftwareUpdatesDetails?) {
    val circleColor = colors.success01
    data?.version?.let { version ->
        Spacer(modifier = Modifier.height(16.dp))
        Box(
            modifier =
                Modifier
                    .size(132.dp)
                    .clip(CircleShape)
                    .background(color = circleColor),
            contentAlignment = Alignment.Center,
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                OAHeadline1TextView(
                    text = version,
                    color = colors.button03a,
                )
                data.installDate?.let {
                    OACaption1TextView(
                        text = stringResource(id = R.string.installed),
                        color = colors.button03a,
                        textAlign = TextAlign.Center,
                    )
                }
            }
        }
        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
fun StatusLayout(status: String) {
    ExpandableCard(
        title = stringResource(id = R.string.VehicleStatus_vehicle),
        modifier =
            Modifier
                .testTagID(
                    AccessibilityId.ID_VEHICLE_SOFTWARE_STATUS_TITLE_TEXT,
                ),
    ) {
        OABody3TextView(
            text = status,
            color = colors.tertiary05,
            modifier =
                Modifier
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .testTagID(
                        AccessibilityId.ID_VEHICLE_SOFTWARE_STATUS_SUB_TITLE_TEXT,
                    ),
        )
    }
}

@Composable
fun InstructionLayout(instructions: String) {
    Spacer(modifier = Modifier.height(8.dp))
    ExpandableCard(
        title = stringResource(id = R.string.instruction),
        modifier =
            Modifier
                .testTagID(
                    AccessibilityId.ID_VEHICLE_SOFTWARE_INSTRUCTION_TITLE_TEXT,
                ),
    ) {
        OABody3TextView(
            text = instructions,
            color = colors.tertiary05,
            modifier =
                Modifier
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .testTagID(
                        AccessibilityId.ID_VEHICLE_SOFTWARE_INSTRUCTION_SUB_TITLE_TEXT,
                    ),
        )
    }
}

@Composable
fun InstallDateLayout(installDate: String) {
    ExpandableCard(
        title = stringResource(id = R.string.VehicleStatus_vehicle),
        modifier =
            Modifier
                .testTagID(
                    AccessibilityId.ID_VEHICLE_SOFTWARE_STATUS_TITLE_TEXT,
                ),
    ) {
        OABody3TextView(
            text = "${stringResource(id = R.string.installed)} $installDate",
            color = colors.tertiary05,
            modifier =
                Modifier
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .testTagID(
                        AccessibilityId.ID_VEHICLE_SOFTWARE_STATUS_SUB_TITLE_TEXT,
                    ),
        )
    }
}

@Composable
fun DescriptionLayout(descriptions: String) {
    Spacer(modifier = Modifier.height(8.dp))
    ExpandableCard(
        title = stringResource(id = R.string.what_s_new),
        modifier =
            Modifier
                .testTagID(
                    AccessibilityId.ID_VEHICLE_SOFTWARE_WHATS_NEW_TITLE_TEXT,
                ),
    ) {
        OABody3TextView(
            text = descriptions,
            color = colors.tertiary05,
            modifier =
                Modifier
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .testTagID(
                        AccessibilityId.ID_VEHICLE_SOFTWARE_WHATS_NEW_SUB_TITLE_TEXT,
                    ),
        )
    }
}

@Composable
fun WorkingTimeLayout(workingTime: String) {
    Spacer(modifier = Modifier.height(8.dp))
    ExpandableCard(
        title = stringResource(id = R.string.working_time),
        modifier =
            Modifier
                .testTagID(
                    AccessibilityId.ID_VEHICLE_SOFTWARE_WORKING_TIME_TITLE_TEXT,
                ),
    ) {
        OABody3TextView(
            text = workingTime,
            color = colors.tertiary05,
            modifier =
                Modifier
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .testTagID(
                        AccessibilityId.ID_VEHICLE_SOFTWARE_WORKING_TIME_SUB_TITLE_TEXT,
                    ),
        )
    }
}

@Composable
fun PreviousUpdateLayout(previousUpdates: String) {
    Spacer(modifier = Modifier.height(8.dp))
    ExpandableCard(
        title = stringResource(id = R.string.previous_updates),
        modifier =
            Modifier
                .testTagID(
                    AccessibilityId.ID_VEHICLE_SOFTWARE_PREVIOUS_UPDATE_TITLE_TEXT,
                ),
    ) {
        OABody3TextView(
            text = previousUpdates,
            color = colors.tertiary05,
            modifier =
                Modifier
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .testTagID(
                        AccessibilityId.ID_VEHICLE_SOFTWARE_PREVIOUS_UPDATE_SUB_TITLE_TEXT,
                    ),
        )
    }
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun InformationLayout(
    modalBottomSheetState: ModalBottomSheetState? = null,
    imageId: Int,
    title: Int,
    subTitle: Int,
    bgColor: Color,
    navHostController: NavHostController,
    modifier: Modifier = Modifier,
) {
    val coroutineScope = rememberCoroutineScope()

    Column(
        modifier = modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Surface(
            shape = CircleShape,
            color = bgColor,
            modifier =
                Modifier
                    .size(48.dp),
        ) {
            Image(
                modifier =
                    Modifier
                        .padding(12.dp),
                painter = painterResource(id = imageId),
                contentDescription = null,
            )
        }
        OASubHeadLine1TextView(
            modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 16.dp),
            textAlign = TextAlign.Center,
            text =
                stringResource(
                    title,
                ),
            color = colors.tertiary03,
        )
        OACallOut1TextView(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
            textAlign = TextAlign.Center,
            text =
                stringResource(
                    subTitle,
                ),
            color = colors.tertiary05,
        )
    }

    Box(modifier = Modifier.fillMaxSize()) {
        NavTitleSection(
            navHostController,
            stringResource(id = R.string.vehicle_software),
            AccessibilityId.ID_VEHICLE_SOFTWARE_BACK_ARROW_CTA,
        )
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.align(Alignment.BottomCenter),
        ) {
            PrimaryButton02(
                text = stringResource(id = R.string.Common_Back_to_Dashboard),
                modifier =
                    Modifier
                        .wrapContentWidth()
                        .wrapContentHeight()
                        .align(Alignment.CenterHorizontally),
                click = {
                    coroutineScope.launch {
                        modalBottomSheetState?.hide()
                    }
                },
            )
            Spacer(Modifier.height(32.h()))
        }
    }
}
