package com.toyota.oneapp.features.vehiclesoftware.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class SoftwareUpdatesAvailablePayload(
    @SerializedName("version") var version: String? = null,
    @SerializedName("status") var status: String? = null,
    @SerializedName("instructions") var instructions: String? = null,
    @SerializedName("descriptions") var descriptions: String? = null,
    @SerializedName("workingTime") var workingTime: String? = null,
)
