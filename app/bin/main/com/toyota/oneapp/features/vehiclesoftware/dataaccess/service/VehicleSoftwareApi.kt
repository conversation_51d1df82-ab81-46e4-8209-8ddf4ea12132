package com.toyota.oneapp.features.vehiclesoftware.dataaccess.service

import com.toyota.oneapp.features.vehiclesoftware.dataaccess.servermodel.SoftwareUpdateResponse
import com.toyota.oneapp.features.vehiclesoftware.dataaccess.servermodel.SoftwareUpdatesAvailableResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Query

interface VehicleSoftwareApi {
    @GET("/oa21mm/v1/ota/update/versions")
    suspend fun fetchSoftwareUpdates(
        @Header("vin") vin: String,
    ): Response<SoftwareUpdateResponse?>

    @GET("/oneapi/v1/ota/update")
    suspend fun fetchSoftwareUpdatesAvailable(
        @Header("vin") vin: String,
        @Query("version") version: String,
    ): Response<SoftwareUpdatesAvailableResponse?>
}
