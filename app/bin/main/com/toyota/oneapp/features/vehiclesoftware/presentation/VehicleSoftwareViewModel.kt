package com.toyota.oneapp.features.vehiclesoftware.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.vehicleinfo.domain.model.VehicleInfoConstance
import com.toyota.oneapp.features.vehiclesoftware.application.VehicleSoftwareState
import com.toyota.oneapp.features.vehiclesoftware.application.VehicleSoftwareUseCase
import com.toyota.oneapp.features.vehiclesoftware.domain.model.SoftwareUpdatesDetails
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class VehicleSoftwareViewModel
    @Inject
    constructor(
        private val vehicleSoftwareUseCase: VehicleSoftwareUseCase,
        applicationData: ApplicationData,
    ) : BaseViewModel() {
        private val _vehicleSoftwareState =
            MutableStateFlow<VehicleSoftwareState>(value = VehicleSoftwareState.Loading)
        val vehicleSoftwareState = _vehicleSoftwareState.asStateFlow()
        var vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()

        fun fetchSoftwareUpdates(softwareStatus: Int?) {
            if (vehicleInfo == null || softwareStatus == null) {
                _vehicleSoftwareState.value = VehicleSoftwareState.Initial
                return
            }

            viewModelScope.launch {
                _vehicleSoftwareState.value = VehicleSoftwareState.Loading

                when (softwareStatus) {
                    VehicleInfoConstance.SOFTWARE_ERROR_NOTIFICATION -> {
                        _vehicleSoftwareState.value = VehicleSoftwareState.Error
                    }

                    VehicleInfoConstance.CUSTOMER_ACTION_COMPLETE,
                    VehicleInfoConstance.SOFTWARE_UPDATE_AVAILABLE,
                    VehicleInfoConstance.SOFTWARE_UPDATE_PROGRESS,
                    -> {
                        vehicleSoftwareUseCase
                            .fetchSoftwareUpdates(softwareStatus, vehicleInfo!!)
                            .collect { details ->
                                _vehicleSoftwareState.value =
                                    if (details != null) {
                                        VehicleSoftwareState.Success(details)
                                    } else {
                                        VehicleSoftwareState.Error
                                    }
                            }
                    }

                    VehicleInfoConstance.SOFTWARE_UPDATE_COMPLETE -> {
                        _vehicleSoftwareState.value =
                            VehicleSoftwareState.Success(
                                SoftwareUpdatesDetails(isUpdate = true),
                            )
                    }

                    else -> {
                        _vehicleSoftwareState.value = VehicleSoftwareState.Initial
                    }
                }
            }
        }
    }
