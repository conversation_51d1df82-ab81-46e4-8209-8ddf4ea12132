package com.toyota.oneapp.features.vehiclesoftware.application

import com.toyota.oneapp.features.vehiclesoftware.domain.model.SoftwareUpdatesDetails

sealed class VehicleSoftwareState {
    data object Initial : VehicleSoftwareState()

    data object Loading : VehicleSoftwareState()

    class Success(
        val data: SoftwareUpdatesDetails?,
    ) : VehicleSoftwareState()

    data object Error : VehicleSoftwareState()
}
