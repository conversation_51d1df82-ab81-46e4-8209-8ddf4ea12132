package com.toyota.oneapp.features.vehiclesoftware.domain.repository

import com.toyota.oneapp.features.vehiclesoftware.dataaccess.servermodel.SoftwareUpdateResponse
import com.toyota.oneapp.features.vehiclesoftware.dataaccess.servermodel.SoftwareUpdatesAvailableResponse
import com.toyota.oneapp.network.Resource

interface VehicleSoftwareRepository {
    suspend fun fetchSoftwareUpdates(vin: String): Resource<SoftwareUpdateResponse?>

    suspend fun fetchSoftwareUpdatesAvailable(
        vin: String,
        version: String,
    ): Resource<SoftwareUpdatesAvailableResponse?>
}
