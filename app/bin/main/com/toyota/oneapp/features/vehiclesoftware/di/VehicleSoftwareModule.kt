package com.toyota.oneapp.features.vehiclesoftware.di

import com.toyota.oneapp.features.vehiclesoftware.application.VehicleSoftwareLogic
import com.toyota.oneapp.features.vehiclesoftware.application.VehicleSoftwareUseCase
import com.toyota.oneapp.features.vehiclesoftware.dataaccess.repository.VehicleSoftwareDefaultRepo
import com.toyota.oneapp.features.vehiclesoftware.domain.repository.VehicleSoftwareRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class VehicleSoftwareModule {
    @Binds
    abstract fun provideVehicleSoftwareRepository(vehicleSoftwareDefaultRepo: VehicleSoftwareDefaultRepo): VehicleSoftwareRepository

    @Binds
    abstract fun provideVehicleSoftwareLogic(vehicleSoftwareLogic: VehicleSoftwareLogic): VehicleSoftwareUseCase
}
