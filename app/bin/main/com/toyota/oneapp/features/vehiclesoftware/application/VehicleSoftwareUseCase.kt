package com.toyota.oneapp.features.vehiclesoftware.application

import com.toyota.oneapp.features.vehiclesoftware.domain.model.SoftwareUpdatesDetails
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface VehicleSoftwareUseCase {
    fun fetchSoftwareUpdates(
        softwareStatus: Int,
        vehicleInfo: VehicleInfo,
    ): Flow<SoftwareUpdatesDetails?>
}
