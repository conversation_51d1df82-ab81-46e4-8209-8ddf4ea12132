package com.toyota.oneapp.features.vehiclesoftware.dataaccess.repository

import com.toyota.oneapp.features.vehiclesoftware.dataaccess.servermodel.SoftwareUpdateResponse
import com.toyota.oneapp.features.vehiclesoftware.dataaccess.servermodel.SoftwareUpdatesAvailableResponse
import com.toyota.oneapp.features.vehiclesoftware.dataaccess.service.VehicleSoftwareApi
import com.toyota.oneapp.features.vehiclesoftware.domain.repository.VehicleSoftwareRepository
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class VehicleSoftwareDefaultRepo
    @Inject
    constructor(
        val service: VehicleSoftwareApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), VehicleSoftwareRepository {
        override suspend fun fetchSoftwareUpdates(vin: String): Resource<SoftwareUpdateResponse?> {
            return makeApiCall {
                service.fetchSoftwareUpdates(
                    vin = vin,
                )
            }
        }

        override suspend fun fetchSoftwareUpdatesAvailable(
            vin: String,
            version: String,
        ): Resource<SoftwareUpdatesAvailableResponse?> {
            return makeApiCall {
                service.fetchSoftwareUpdatesAvailable(
                    vin = vin,
                    version = version,
                )
            }
        }
    }
