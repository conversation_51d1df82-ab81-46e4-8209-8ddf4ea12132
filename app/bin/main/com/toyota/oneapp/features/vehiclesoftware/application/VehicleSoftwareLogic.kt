package com.toyota.oneapp.features.vehiclesoftware.application

import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.features.core.util.Constants.MILLI_SECOND
import com.toyota.oneapp.features.vehicleinfo.domain.model.VehicleInfoConstance
import com.toyota.oneapp.features.vehiclesoftware.dataaccess.servermodel.LatestUpdateResponse
import com.toyota.oneapp.features.vehiclesoftware.dataaccess.servermodel.SoftwareUpdatePayload
import com.toyota.oneapp.features.vehiclesoftware.dataaccess.servermodel.SoftwareUpdatesAvailablePayload
import com.toyota.oneapp.features.vehiclesoftware.domain.model.SoftwareUpdatesDetails
import com.toyota.oneapp.features.vehiclesoftware.domain.repository.VehicleSoftwareRepository
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.text.SimpleDateFormat
import java.util.Date
import javax.inject.Inject

class VehicleSoftwareLogic
    @Inject
    constructor(
        private val repository: VehicleSoftwareRepository,
        private val analyticsLogger: AnalyticsLogger,
    ) : VehicleSoftwareUseCase {
        override fun fetchSoftwareUpdates(
            softwareStatus: Int,
            vehicleInfo: VehicleInfo,
        ): Flow<SoftwareUpdatesDetails?> {
            return flow {
                val softwareUpdatesResponse = repository.fetchSoftwareUpdates(vehicleInfo.vin)

                if (softwareUpdatesResponse is Resource.Success) {
                    softwareUpdatesResponse.data?.payload?.let { softwareUpdate ->
                        analyticsLogger.logEvent(AnalyticsEvent.VEH_20TM_HISTORY_SUCCESSFUL)
                        when {
                            isSoftwareUpdateAvailable(softwareUpdate, softwareStatus) -> {
                                emit(processSoftwareUpdateAvailable(softwareUpdate, vehicleInfo))
                            }

                            isSoftwareCompleted(softwareStatus) -> {
                                emit(processSoftwareCompleted(softwareUpdate, vehicleInfo))
                            }

                            else -> {
                                emit(null)
                            }
                        }
                    } ?: run {
                        emit(null)
                    }
                } else {
                    analyticsLogger.logEvent(AnalyticsEvent.VEH_20TM_HISTORY_UNSUCCESSFUL)
                    emit(null)
                }
            }
        }

        private fun isSoftwareUpdateAvailable(
            softwareUpdatePayload: SoftwareUpdatePayload,
            softwareStatus: Int,
        ): Boolean {
            return softwareUpdatePayload.latestAvailableUpdate == true &&
                (
                    softwareStatus == VehicleInfoConstance.SOFTWARE_UPDATE_AVAILABLE ||
                        softwareStatus == VehicleInfoConstance.SOFTWARE_UPDATE_PROGRESS
                )
        }

        private fun isSoftwareCompleted(softwareStatus: Int): Boolean {
            return softwareStatus == VehicleInfoConstance.SOFTWARE_UPDATE_COMPLETE ||
                softwareStatus == VehicleInfoConstance.CUSTOMER_ACTION_COMPLETE
        }

        private fun processSoftwareCompleted(
            softwareUpdatePayload: SoftwareUpdatePayload,
            vehicleInfo: VehicleInfo,
        ): SoftwareUpdatesDetails? {
            return when {
                isSoftwareUpToDate(softwareUpdatePayload, vehicleInfo) -> {
                    SoftwareUpdatesDetails(isUpdate = true)
                }
                vehicleInfo.isCY17Plus -> {
                    softwareUpdatePayload.latestUpdate?.toUiModel()
                }
                else -> {
                    null
                }
            }
        }

        private suspend fun processSoftwareUpdateAvailable(
            softwareUpdatePayload: SoftwareUpdatePayload,
            vehicleInfo: VehicleInfo,
        ): SoftwareUpdatesDetails? {
            val softwareUpdatesAvailableResponse =
                repository.fetchSoftwareUpdatesAvailable(
                    vehicleInfo.vin,
                    softwareUpdatePayload.latestUpdate?.version.orEmpty(),
                )
            return if (softwareUpdatesAvailableResponse is Resource.Success) {
                analyticsLogger.logEvent(AnalyticsEvent.VEH_20TM_INFO_SUCCESSFUL)

                softwareUpdatesAvailableResponse.data?.payload?.toUIModel()
                    ?.apply {
                        this.previousUpdates =
                            getPreviousUpdateValue(
                                softwareUpdatePayload.previousUpdate?.description,
                            )
                    }
            } else {
                analyticsLogger.logEvent(AnalyticsEvent.VEH_20TM_INFO_UNSUCCESSFUL)
                null
            }
        }

        private fun isSoftwareUpToDate(
            softwareUpdatePayload: SoftwareUpdatePayload,
            vehicleInfo: VehicleInfo,
        ): Boolean {
            return !(
                softwareUpdatePayload.latestAvailableUpdate == true &&
                    softwareUpdatePayload.previousAvailableUpdate == true
            ) && !vehicleInfo.isCY17Plus
        }
    }

private fun getPreviousUpdateValue(data: String?): String? {
    return if (!data.isNullOrEmpty() && data.lowercase() != Constants.NOT_FOUND) {
        data
    } else {
        null
    }
}

fun getConvertedTimeStamps(date: String?): String {
    return date?.let {
        try {
            SimpleDateFormat("MMM dd, yyyy 'at' h:mma", AppLanguageUtils.getCurrentLocale())
                .format(Date(date.toLong() * MILLI_SECOND))
                .replace("AM", "am")
                .replace("PM", "pm")
        } catch (e: NumberFormatException) {
            ToyotaConstants.EMPTY_STRING
        }
    } ?: run {
        ToyotaConstants.EMPTY_STRING
    }
}

fun LatestUpdateResponse.toUiModel(): SoftwareUpdatesDetails {
    return SoftwareUpdatesDetails(
        version = this.version,
        installDate = getConvertedTimeStamps(this.installDate),
        descriptions = this.description,
    )
}

fun SoftwareUpdatesAvailablePayload?.toUIModel(): SoftwareUpdatesDetails {
    return SoftwareUpdatesDetails(
        version = this?.version,
        status = this?.status,
        instructions = this?.instructions,
        descriptions = this?.descriptions,
        workingTime = this?.workingTime,
    )
}
