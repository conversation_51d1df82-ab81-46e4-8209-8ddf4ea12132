/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.publiccharging.application

import com.toyota.oneapp.model.poi.LocationDetails

data class ChargeStationFavoriteUIState(
    val favoriteStations: Set<String> = emptySet(),
    val favoriteLocationDetails: List<LocationDetails> = emptyList(),
    val isFilteringFavorite: Boolean = false,
    val favoriteFetchState: ChargeStationFavoriteState = ChargeStationFavoriteState.Loading,
)
