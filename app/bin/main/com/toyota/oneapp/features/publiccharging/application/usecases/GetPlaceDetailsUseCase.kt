/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.google.android.libraries.places.api.model.Place
import com.google.android.libraries.places.api.net.FetchPlaceRequest
import com.google.android.libraries.places.api.net.PlacesClient
import com.toyota.oneapp.features.publiccharging.application.PublicChargingLogic
import com.toyota.oneapp.features.publiccharging.application.PublicChargingState
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import kotlin.coroutines.resume

class GetPlaceDetailsUseCase
    @Inject
    constructor(
        private val publicChargingLogToolic: PublicChargingLogic,
    ) {
        operator fun invoke(
            placeId: String,
            placesClient: PlacesClient,
            partnerFilters: List<Int>,
            plugFilters: List<Int>,
            vehicleInfo: VehicleInfo?,
        ): Flow<PublicChargingState> =
            flow {
                emit(PublicChargingState.Loading)

                try {
                    val placeDetails = getPlaceDetails(placeId, placesClient)
                    if (placeDetails == null) {
                        emit(PublicChargingState.EmptyStations)
                        return@flow
                    }

                    if (vehicleInfo == null) {
                        emit(PublicChargingState.EmptyStations)
                        return@flow
                    }

                    publicChargingLogToolic
                        .fetchNearByStations(
                            lat = placeDetails.latLng?.latitude,
                            long = placeDetails.latLng?.longitude,
                            vehicleInfo = vehicleInfo,
                            partnerFilters = partnerFilters,
                            plugFilters = plugFilters,
                        ).collect { stationsInfo ->
                            if (stationsInfo.chargeStationsInfoList.isEmpty()) {
                                emit(PublicChargingState.EmptyStations)
                            } else {
                                emit(PublicChargingState.Success(stationsInfo, placeDetails.latLng))
                            }
                        }
                } catch (e: Exception) {
                    emit(PublicChargingState.EmptyStations)
                }
            }

        private suspend fun getPlaceDetails(
            placeId: String,
            placesClient: PlacesClient,
        ): Place? =
            suspendCancellableCoroutine { continuation ->
                val placeFields =
                    listOf(
                        Place.Field.ADDRESS,
                        Place.Field.LAT_LNG,
                    )

                val request = FetchPlaceRequest.newInstance(placeId, placeFields)
                val task = placesClient.fetchPlace(request)

                task
                    .addOnSuccessListener { response ->
                        continuation.resume(response.place)
                    }.addOnFailureListener { exception ->
                        continuation.resume(null)
                    }

                continuation.invokeOnCancellation {
                    // No need to explicitly cancel the task as it will be handled by the Places API
                }
            }
    }
