/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.model.vehicle.VehicleInfo
import org.json.JSONObject
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

class PrepareUnlockPayloadUseCase
    @Inject
    constructor() {
        operator fun invoke(
            station: ChargeStationInfo,
            connectorId: String,
            evseUid: String,
            vehicleInfo: VehicleInfo?,
        ): String {
            LogTool.d("PrepareUnlockPayloadUseCase", "Preparing unlock payload for station: ${station.stationName}")

            val vehicleVin = vehicleInfo?.vin ?: "Unknown"

            // Find the EVSE and connector to get physical reference
            val evse = station.evEvses.find { it.uid == evseUid }
            val connector = evse?.connectors?.find { it.evId == connectorId }
            val physicalRef = evse?.physicalReference ?: "Not available"

            val payload =
                JSONObject().apply {
                    put("stationId", station.evId)
                    put("stationName", station.stationName)
                    put("connectorId", connectorId)
                    put("evseUid", evseUid)
                    put("physicalReference", physicalRef)
                    put("vehicleVin", vehicleVin)
                    put("timestamp", System.currentTimeMillis())
                    put("source", station.evEvSource)
                    put("isPartner", station.evIsPartner)
                    put("connector", connector)

                    // Add debug info section
                    put(
                        "debug_info",
                        JSONObject().apply {
                            put(
                                "evse_details",
                                JSONObject().apply {
                                    put("uid", evse?.uid)
                                    put("physical_reference", physicalRef)
                                    put("connector_count", evse?.connectors?.size ?: 0)
                                },
                            )
                            put(
                                "connector_details",
                                connector?.let { conn ->
                                    JSONObject().apply {
                                        put("id", conn.evId)
                                        put("standard", conn.evStandard)
                                    }
                                },
                            )
                            put(
                                "vehicle_info",
                                JSONObject().apply {
                                    put("vin", vehicleVin)
                                },
                            )
                        },
                    )
                }

            LogTool.d("PrepareUnlockPayloadUseCase", "Unlock payload prepared: ${payload.toString(2)}")
            return payload.toString(2)
        }
    }
