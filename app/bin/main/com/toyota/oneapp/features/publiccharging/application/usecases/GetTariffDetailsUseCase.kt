/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.features.publiccharging.domain.model.TariffDetails
import com.toyota.oneapp.features.publiccharging.domain.repo.TariffDetailsRepo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * Use case for retrieving and processing tariff details for charging stations
 */
class GetTariffDetailsUseCase
    @Inject
    constructor(
        private val tariffDetailsRepo: TariffDetailsRepo,
    ) {
        fun getTariffDetails(
            tariffId: List<String?>,
            station: ChargeStationInfo,
        ): Flow<Resource<List<TariffDetails?>>> =
            flow {
                emit(Resource.Loading(null))
                val result = tariffDetailsRepo.fetchTariffDetails(tariffId, station)
                emit(result)
            }

        fun getTariffIds(
            selectedConnectorId: String,
            station: ChargeStationInfo,
        ): Flow<Resource<List<String?>>> =
            flow {
                emit(Resource.Loading(null))
                val result = tariffDetailsRepo.fetchTariffIds(selectedConnectorId, station)
                emit(result)
            }
    }
