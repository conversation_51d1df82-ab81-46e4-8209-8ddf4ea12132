/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.publiccharging.presentation.model.ChargingSessionStatus
import com.toyota.oneapp.features.publiccharging.presentation.model.UIChargeSession
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.EVGO

class StopChargingButtonUseCase {
    data class StopButtonState(
        val shouldShowButton: Boolean,
        val isButtonEnabled: <PERSON><PERSON>an,
        val buttonText: String?,
    )

    operator fun invoke(session: UIChargeSession?): StopButtonState {
        val isEvgoStation =
            session
                ?.location
                ?.operator
                ?.name
                ?.trim()
                .equals(EVGO, ignoreCase = true)

        val chargingStatus =
            session
                ?.status
                ?.trim()
                .orEmpty()
                .uppercase()

        val chargingStatusEnum = ChargingSessionStatus.from(chargingStatus)

        val shouldShowButton =
            !session?.chargingId.isNullOrBlank() &&
                chargingStatusEnum !in listOf(ChargingSessionStatus.STOPPED, ChargingSessionStatus.REJECTED) &&
                chargingStatusEnum in listOf(ChargingSessionStatus.CHARGING, ChargingSessionStatus.ACTIVE) &&
                !isEvgoStation

        return StopButtonState(
            shouldShowButton = shouldShowButton,
            isButtonEnabled = shouldShowButton,
            buttonText = PublicChargingConstants.STOP_CHARGING_TEXT,
        )
    }
}
