/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.publiccharging.application.ChargeStationFavoriteState
import com.toyota.oneapp.features.publiccharging.application.SendToCarState
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.model.StationListInfo
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingRequest
import com.toyota.oneapp.features.publiccharging.presentation.model.StopChargingStatusUI
import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.model.poi.SendPOIToCarRequest
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface PublicChargingUseCase {
    fun fetchNearByStations(
        lat: Double?,
        long: Double?,
        vehicleInfo: VehicleInfo,
        partnerFilters: List<Int> = emptyList(),
        plugFilters: List<Int> = emptyList(),
    ): Flow<StationListInfo>

    fun startCharging(
        vehicleInfo: VehicleInfo,
        startChargingRequest: StartChargingRequest,
    ): Flow<StartChargingData?>

    fun stopCharging(
        vehicleInfo: VehicleInfo,
        stopChargingRequest: StopChargingRequest,
    ): Flow<StopChargingStatusUI?>

    fun updateFavoriteLocation(favList: List<LocationDetails>): Flow<ChargeStationFavoriteState>

    fun getFavoriteStation(): Flow<ChargeStationFavoriteState>

    fun sendToCar(sendPOIToCarRequest: SendPOIToCarRequest): Flow<SendToCarState>
}
