// * Copyright © 2024. Toyota Motors North America Inc * All rights reserved.

package com.toyota.oneapp.features.publiccharging.presentation.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Divider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.publiccharging.domain.model.StartTimeAndEnergy

@Composable
fun StartTimeAndEnergyWidget(startTimeAndEnergy: StartTimeAndEnergy) {
    Column(modifier = Modifier.fillMaxWidth().padding(horizontal = 10.dp)) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(vertical = 9.dp),
        ) {
            OABody3TextView(text = stringResource(R.string.ev_start_time), color = AppTheme.colors.tertiary03)
            OABody3TextView(text = startTimeAndEnergy.time, color = AppTheme.colors.tertiary03)
        }
        Divider(thickness = 1.dp, color = AppTheme.colors.tertiary10)
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(vertical = 9.dp),
        ) {
            OABody3TextView(text = stringResource(R.string.ev_energy), color = AppTheme.colors.tertiary03)
            OABody3TextView(
                text = startTimeAndEnergy.energyInKw,
                color = AppTheme.colors.tertiary03,
            )
        }
        Divider(thickness = 1.dp, color = AppTheme.colors.tertiary10)
        startTimeAndEnergy.currentCost?.let { currentCost ->
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(vertical = 9.dp),
            ) {
                OABody3TextView(
                    text = stringResource(R.string.current_cost),
                    color = AppTheme.colors.tertiary03,
                )
                OABody3TextView(
                    text = "${startTimeAndEnergy.currency}$currentCost",
                    color = AppTheme.colors.tertiary03,
                )
            }
            Divider(thickness = 1.dp, color = AppTheme.colors.tertiary10)
        }
        Spacer(modifier = Modifier.height(5.dp))
    }
}

@Preview(showBackground = true)
@Composable
fun StartTimeAndEnergyWidgetPreview() {
    StartTimeAndEnergyWidget(
        StartTimeAndEnergy(
            time = "1:00pm",
            energyInKw = "0 kWh",
        ),
    )
}
