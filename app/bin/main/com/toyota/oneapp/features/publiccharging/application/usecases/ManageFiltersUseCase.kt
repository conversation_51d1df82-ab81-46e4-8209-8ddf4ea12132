/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

data class FilterState(
    val partnerFilters: List<Int>,
    val plugFilters: List<Int>,
)

class ManageFiltersUseCase
    @Inject
    constructor() {
        private var currentFilters =
            FilterState(
                partnerFilters = emptyList(),
                plugFilters = emptyList(),
            )

        operator fun invoke(
            partnerTypeFilters: List<Int>,
            plugTypeFilters: List<Int>,
        ): Flow<FilterState> =
            flow {
                LogTool.d("ManageFiltersUseCase", "Updating filters - Partners: $partnerTypeFilters, Plugs: $plugTypeFilters")

                // Convert from SnapshotStateLists to regular Lists if needed
                currentFilters =
                    FilterState(
                        partnerFilters = partnerTypeFilters.toList(),
                        plugFilters = plugTypeFilters.toList(),
                    )

                emit(currentFilters)
            }

        fun getCurrentFilters(): FilterState = currentFilters
    }
