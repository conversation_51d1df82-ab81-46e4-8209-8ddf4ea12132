/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.presentation.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.publiccharging.application.usecases.StopChargingButtonUseCase
import com.toyota.oneapp.features.publiccharging.domain.model.ChargePointAddress
import com.toyota.oneapp.features.publiccharging.presentation.model.ChargingSessionStatus
import com.toyota.oneapp.features.publiccharging.presentation.model.UIChargeSession
import com.toyota.oneapp.features.publiccharging.presentation.model.UIElectricStatus
import com.toyota.oneapp.features.publiccharging.presentation.model.UIPositionInfo
import com.toyota.oneapp.features.publiccharging.presentation.model.UIRealtimeStatus
import com.toyota.oneapp.features.publiccharging.presentation.model.UIRemoteControlResult
import com.toyota.oneapp.features.publiccharging.presentation.state.ChargeSessionUiState
import com.toyota.oneapp.features.publiccharging.presentation.state.ElectricStatusUiState
import com.toyota.oneapp.features.publiccharging.presentation.util.SessionFormatter
import com.toyota.oneapp.util.ToyotaConstants

@Composable
fun PublicChargingStatusContent(
    sessionState: ChargeSessionUiState,
    electricStatusState: ElectricStatusUiState,
    onStopCharging: (String) -> Unit,
    onRefresh: () -> Unit,
    onBackPressed: () -> Unit,
) {
    val sessionData = (sessionState as? ChargeSessionUiState.Success)?.session

    val electricStatus = (electricStatusState as? ElectricStatusUiState.Success)?.status
    val statusEnum = ChargingSessionStatus.from(sessionData?.status)
    val isChargingStopped =
        statusEnum in
            setOf(
                ChargingSessionStatus.STOPPED,
                ChargingSessionStatus.COMPLETED,
                ChargingSessionStatus.REJECTED,
            )

    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .background(AppTheme.colors.tertiary15)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 15.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(8.dp))
        DragIcon()
        Spacer(modifier = Modifier.height(20.dp))
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .size(48.dp)
                        .clip(CircleShape)
                        .background(AppTheme.colors.button02d)
                        .clickable { onBackPressed() }
                        .testTagID(AccessibilityId.ID_PUBLIC_CHARGING_STATUS_BACK_BTN),
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_back_arrow),
                    contentDescription = stringResource(id = R.string.Common_back),
                    modifier =
                        Modifier.padding(
                            start = 19.dp,
                            end = 22.dp,
                            top = 17.dp,
                            bottom = 17.dp,
                        ),
                    tint = AppTheme.colors.button02a,
                )
            }
            Spacer(modifier = Modifier.weight(1f))
            OASubHeadLine3TextView(
                text = stringResource(R.string.charging_session_title),
                color = AppTheme.colors.tertiary03,
                modifier = Modifier,
                textAlign = TextAlign.Center,
            )
            Spacer(modifier = Modifier.weight(1f))
            Box(
                modifier =
                    Modifier
                        .size(48.dp),
            )
        }
        Spacer(modifier = Modifier.height(18.dp))
        ChargePercentageView(electricStatus)
        ChargePluggedStatusAndTimeWidget(electricStatus, sessionData)
        Spacer(modifier = Modifier.height(12.dp))

        val address = sessionData?.location
        if (!address?.operator?.name.isNullOrBlank() ||
            !address?.name.isNullOrBlank() ||
            !address?.address.isNullOrBlank()
        ) {
            ChargePointAddressTileWidget(
                address =
                    ChargePointAddress(
                        partnerName = address?.operator?.name,
                        stationName = address?.name,
                        stationAddress = address?.address,
                        partnerInfo = null,
                    ),
            )
        }

        StartTimeAndEnergyWidget(
            startTimeAndEnergy = SessionFormatter.createStartTimeAndEnergy(sessionData),
        )
        StatusCTA(sessionData, onStopCharging)
        if (isChargingStopped) {
            StoppedChargingMessage()
        }
        Spacer(modifier = Modifier.weight(1f))

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
                Modifier
                    .wrapContentHeight()
                    .padding(horizontal = 15.dp),
        ) {
            RefreshIconWidget(onRefresh = onRefresh)
            OACallOut1TextView(
                text =
                    stringResource(
                        R.string.last_updated_time,
                        SessionFormatter.formatLastUpdatedTime(sessionData?.lastUpdated),
                    ),
                color = AppTheme.colors.tertiary05,
            )
            Spacer(modifier = Modifier.height(12.dp))
        }
    }
}

@Composable
private fun StatusCTA(
    session: UIChargeSession?,
    onStopCharging: (String) -> Unit,
) {
    val stopChargingButtonUseCase = StopChargingButtonUseCase()
    val stopButtonState = stopChargingButtonUseCase(session)

    Spacer(modifier = Modifier.height(12.dp))

    if (stopButtonState.shouldShowButton) {
        PrimaryButton02(
            text = stopButtonState.buttonText ?: stringResource(R.string.stop_charging),
            modifier = Modifier.fillMaxWidth(),
            click = {
                session?.chargingId?.let { chargingId ->
                    onStopCharging(chargingId)
                }
            },
            enabled = stopButtonState.isButtonEnabled,
        )
    } else {
        OACallOut1TextView(
            text = stringResource(R.string.stop_charging_info),
            color = AppTheme.colors.tertiary05,
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
private fun StoppedChargingMessage() {
    OACallOut1TextView(
        text = stringResource(R.string.charging_ended),
        color = AppTheme.colors.tertiary05,
        textAlign = TextAlign.Center,
    )
    Spacer(modifier = Modifier.height(12.dp))
}

@Composable
private fun DragIcon() {
    Image(
        painterResource(R.drawable.ic_drag),
        contentDescription = stringResource(R.string.swipeDownIconDescription),
        contentScale = ContentScale.FillWidth,
        modifier =
            Modifier
                .width(30.dp)
                .height(10.dp),
        colorFilter = ColorFilter.tint(AppTheme.colors.outline01),
    )
}

@Composable
fun ChargePercentageView(electricStatus: UIElectricStatus?) {
    val lowBatteryPercentage = 30
    val percentage = electricStatus?.chargeRemainingAmount?.toString() ?: return
    val isBatteryLow = electricStatus.chargeRemainingAmount <= lowBatteryPercentage
    val isCharging = electricStatus.isCharging
    val progress = (percentage.toFloatOrNull() ?: ToyotaConstants.ZERO_FLOAT) / 100f

    ChargingPercentage(percentage, isBatteryLow, isCharging)
    Spacer(modifier = Modifier.height(8.dp))
    ChargingProgress(isBatteryLow, progress)
}

@Preview(showBackground = true)
@Composable
fun PublicChargingStatusContentPreview() {
    val mockElectricStatus =
        UIElectricStatus(
            plugStatus = 56,
            chargeRemainingAmount = 180,
            evDistance = 160.0,
            evDistanceAC = 150.0,
            evDistanceUnit = "mi",
            remainingChargeTime = 40,
            isCharging = true,
            isPluggedIn = true,
            position = UIPositionInfo(37.7749, -122.4194, "2025-05-07T11:59:00Z"),
            realtimeStatus = UIRealtimeStatus(status = 1, result = 1),
            remoteControl = UIRemoteControlResult(status = 1, result = 1, errorCode = null),
            vehicleStatus = emptyList(),
        )

    PublicChargingStatusContent(
        sessionState = ChargeSessionUiState.Success(mockSession),
        electricStatusState = ElectricStatusUiState.Success(mockElectricStatus),
        onStopCharging = { _ -> },
        onRefresh = {},
        onBackPressed = {},
    )
}
