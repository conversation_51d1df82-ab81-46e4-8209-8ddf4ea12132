/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.pay.wallet.application.CollectEVWalletState
import com.toyota.oneapp.features.pay.wallet.application.WalletUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

private const val TAG = "FetchDefaultPaymentMethodUseCase"

class FetchDefaultPaymentMethodUseCase
    @Inject
    constructor(
        private val walletLogic: WalletUseCase,
    ) {
        operator fun invoke(): Flow<String?> =
            walletLogic.getEVWallet().map {
                when (it) {
                    is CollectEVWalletState.Success -> {
                        // First try to get the default card
                        val defaultCard = it.defaultCard
                        if (defaultCard != null && !defaultCard.id.isNullOrEmpty()) {
                            LogTool.d(TAG, "Default payment method found: ${defaultCard.id}")
                            defaultCard.id
                        } else if (it.evWallet?.isNotEmpty() == true) {
                            // If no default card but we have cards, use the first one
                            val firstCard = it.evWallet.firstOrNull()
                            if (firstCard != null && !firstCard.id.isNullOrEmpty()) {
                                LogTool.d(TAG, "Using first available payment method: ${firstCard.id}")
                                firstCard.id
                            } else {
                                LogTool.d(TAG, "No valid payment methods found in wallet")
                                null
                            }
                        } else {
                            LogTool.d(TAG, "No payment methods found in wallet")
                            null
                        }
                    }
                    is CollectEVWalletState.Error -> {
                        LogTool.e(TAG, "Error fetching wallet: ${it.errorMessage}")
                        null
                    }
                    is CollectEVWalletState.Loading -> {
                        LogTool.d(TAG, "Wallet data is loading...")
                        null
                    }
                }
            }
    }
