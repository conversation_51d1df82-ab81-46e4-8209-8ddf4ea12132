/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.publiccharging.presentation.util

import com.toyota.oneapp.features.publiccharging.domain.model.StartTimeAndEnergy
import com.toyota.oneapp.features.publiccharging.presentation.model.UIChargeSession
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.THREE_DEC_CONVERSION
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.getDateNameAsStringFormats
import toyotaone.commonlib.log.LogTool
import java.util.Locale

object SessionFormatter {
    private const val TAG = "SessionFormatter"

    fun createStartTimeAndEnergy(uiSession: UIChargeSession?): StartTimeAndEnergy {
        LogTool.d(TAG, "Creating StartTimeAndEnergy with uiSession: $uiSession")
        val formattedTime =
            uiSession?.startDatetime?.let {
                getDateNameAsStringFormats(
                    date = it,
                    inputFormat = DateUtil.YYYY_MM_DD_HH_MM_SS_Z_LITERAL_UTC,
                    outputFormat = DateUtil.HH_MMA,
                    isUTC = true,
                    inputLocale = Locale.US,
                )
            } ?: ToyotaConstants.EMPTY_STRING

        val formattedEnergy =
            uiSession?.kwh?.let {
                "${THREE_DEC_CONVERSION.format(it)} kWh"
            } ?: PublicChargingConstants.ENERGY_IN_KW_INIT

        val formattedCost = uiSession?.totalCost?.takeIf { it > 0.0 }?.toString()
        val formattedCurrency =
            uiSession?.currency?.takeIf { it == PublicChargingConstants.USD }?.let {
                PublicChargingConstants.DOLLAR
            }

        return StartTimeAndEnergy(
            time = formattedTime,
            energyInKw = formattedEnergy,
            currentCost = formattedCost,
            currency = formattedCurrency,
        )
    }

    fun formatLastUpdatedTime(isoUtcTime: String?): String =
        isoUtcTime?.let {
            getDateNameAsStringFormats(
                date = it,
                inputFormat = DateUtil.YYYY_MM_DD_HH_MM_SS_Z_LITERAL_UTC,
                outputFormat = DateUtil.MMM_DD_AT_HH_MMA,
                isUTC = true,
                inputLocale = Locale.US,
            )
        } ?: ToyotaConstants.EMPTY_STRING
}
