/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.google.android.libraries.places.api.model.AutocompleteSessionToken
import com.google.android.libraries.places.api.net.FindAutocompletePredictionsRequest
import com.google.android.libraries.places.api.net.PlacesClient
import com.toyota.oneapp.ui.destinations.PlaceInfo
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject

class GetPlacePredictionsUseCase
    @Inject
    constructor() {
        @OptIn(ExperimentalCoroutinesApi::class)
        operator fun invoke(
            query: String,
            placesClient: PlacesClient,
        ): Flow<List<PlaceInfo>> =
            flow {
                if (query.length < 3) {
                    emit(emptyList())
                    return@flow
                }

                try {
                    val token = AutocompleteSessionToken.newInstance()
                    val request =
                        FindAutocompletePredictionsRequest
                            .builder()
                            .setSessionToken(token)
                            .setCountries("US", "CA", "MX")
                            .setQuery(query)
                            .build()

                    val predictions =
                        suspendCancellableCoroutine<List<PlaceInfo>> { continuation ->
                            val task = placesClient.findAutocompletePredictions(request)

                            task
                                .addOnSuccessListener { response ->
                                    val placeInfoList =
                                        response.autocompletePredictions.map { prediction ->
                                            PlaceInfo(
                                                heading = prediction.getPrimaryText(null).toString(),
                                                description = prediction.getSecondaryText(null).toString(),
                                                placeId = prediction.placeId,
                                            )
                                        }
                                    continuation.resume(placeInfoList) { }
                                }.addOnFailureListener { exception ->
                                    continuation.resume(emptyList()) { }
                                }
                        }

                    emit(predictions)
                } catch (e: Exception) {
                    emit(emptyList())
                }
            }
    }
