/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.publiccharging.presentation

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.presentation.utils.toStringResource
import kotlinx.coroutines.launch

private const val TAG = "FilterMenu"

@SuppressLint("UnrememberedMutableState")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilterMenu(
    viewModel: PublicChargingViewModel,
    onFilterChanged: (partnerTypes: List<Int>, plugTypes: List<Int>) -> Unit = { _, _ -> },
) {
    // Store string resource values in local variables
    val partnersString = stringResource(R.string.partners)
    val plugTypesString = stringResource(R.string.plug_types)
    val clearAllString = stringResource(R.string.ev_filter_clear_all)

    val selectedFilter by remember { derivedStateOf { viewModel.selectedFilter } }
    val selectedPartnerFilters by remember { derivedStateOf { viewModel.selectedPartnerFilters } }
    val selectedPlugFilters by remember { derivedStateOf { viewModel.selectedPlugFilters } }

    val bottomSheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()
    selectedFilter?.let { filterName ->
        ModalBottomSheet(
            onDismissRequest = { viewModel.updateSelectedFilter(null) },
            sheetState = bottomSheetState,
            dragHandle = null,
        ) {
            FilterScreen(
                filterMenuData = filterName,
                selectedFilters =
                    when (filterName.filterType) {
                        partnersString -> selectedPartnerFilters
                        plugTypesString -> selectedPlugFilters
                        else -> mutableStateListOf()
                    },
                onFilterSelectionChanged = { newSelections ->
                    when (filterName.filterType) {
                        partnersString -> {
                            selectedPartnerFilters.clear()
                            selectedPartnerFilters.addAll(newSelections)
                        }

                        plugTypesString -> {
                            selectedPlugFilters.clear()
                            selectedPlugFilters.addAll(newSelections)
                        }
                    }

                    // Make copies of the lists to avoid passing mutable state directly
                    val partnerFiltersCopy = selectedPartnerFilters.toList()
                    val plugFiltersCopy = selectedPlugFilters.toList()
                    scope.launch {
                        bottomSheetState.hide()
                    }
                    viewModel.updateSelectedFilter(null)
                    viewModel.updateSelectedPartnerFilters(selectedPartnerFilters)
                    viewModel.updateSelectedPlugFilters(selectedPlugFilters)
                    onFilterChanged(partnerFiltersCopy, plugFiltersCopy)
                },
                onClearAll = {
                    filterName.filterType.takeIf { it == clearAllString }?.let {
                        Log.d(TAG, "Clearing all filters")
                        selectedPartnerFilters.clear()
                        selectedPlugFilters.clear()
                        viewModel.updateSelectedFilter(null)
                        viewModel.updateSelectedPartnerFilters(selectedPartnerFilters)
                        viewModel.updateSelectedPlugFilters(selectedPlugFilters)
                        viewModel.clearFavoriteFilter()
                        viewModel.clearPnCFilter()
                        onFilterChanged(emptyList(), emptyList())
                    }
                },
                onClose = {
                    viewModel.updateSelectedFilter(null)
                },
            )
        }
    }
    val favoriteStationsState by viewModel.favoriteStationsState.collectAsState()
    val pncStationsState by viewModel.pncStationsState.collectAsState()

    val favoriteFilterText = stringResource(R.string.favorites)
    val pncFilterText = stringResource(R.string.plug_and_charge)

    val filterList = ArrayList<FilterMenuData>()
    val partnerList = ArrayList<FilterData>()
    val partners =
        mutableListOf(
            FilterData(R.string.chargePointLowerCase),
            FilterData(R.string.partnerFilterEVConnect),
            FilterData(R.string.partnerFilterEVgo),
            FilterData(R.string.partnerFilterFLONetwork),
            FilterData(R.string.partnerFilterGreenlots),
            FilterData(R.string.partnerFilterShellRecharge),
        )
    viewModel.getEnabledChargingNetworks().forEach { chargingNetwork: ChargingNetworkType ->
        when (chargingNetwork) {
            ChargingNetworkType.IONNA,
            ChargingNetworkType.TESLA,
            ->
                partners.add(
                    FilterData(chargingNetwork.toStringResource()),
                )
            ChargingNetworkType.PLUG_AND_CHARGE,
            ->
                filterList.add(
                    FilterMenuData(pncFilterText, ArrayList()),
                )
        }
    }
    // Sort partners alphabetically by their string resource values
    partnerList.addAll(partners.sortedBy { it.filterName })

    val plugList =
        arrayListOf(
            FilterData(
                R.string.j1772,
                R.drawable.ic_charger_level_2,
            ),
            FilterData(
                R.string.ccs1,
                R.drawable.ic_charger_dc_fast,
            ),
            FilterData(
                R.string.nacs,
                R.drawable.ic_charger_nacs,
            ),
            FilterData(
                R.string.chademo,
                R.drawable.ic_charger_dc_fast,
            ),
        )
    filterList.add(
        FilterMenuData(partnersString, partnerList),
    )

    filterList.add(
        FilterMenuData(plugTypesString, plugList),
    )
    filterList.add(
        FilterMenuData(favoriteFilterText, ArrayList()),
    )
    filterList.add(
        FilterMenuData(clearAllString, ArrayList()),
    )

    LazyRow(
        modifier = Modifier.padding(bottom = 8.dp),
    ) {
        items(filterList) { item ->
            StationFilterChipComposable(
                filterMenuData = item,
                selectedFilterItems =
                    item.filterType.allSelectedFilterItems(
                        selectedPartnerFilters,
                        selectedPlugFilters,
                    ),
                hasActiveFilters =
                    item.filterType.hasActiveFilters(
                        isPartnersFilterActive = selectedPartnerFilters.isNotEmpty(),
                        isPlugTypeFiltersActive = selectedPlugFilters.isNotEmpty(),
                        isFavouritesActive = favoriteStationsState.isFilteringFavorite,
                        isPnCFilterActive = pncStationsState.isFilteringPnc,
                    ),
                onClick = {
                    Log.d(TAG, "Filter chip clicked: ${item.filterType}")
                    when (item.filterType) {
                        favoriteFilterText -> viewModel.toggleFavoriteFilter()
                        pncFilterText -> viewModel.togglePnCFilter()
                        else -> viewModel.updateSelectedFilter(item)
                    }
                },
            )
        }
    }
}

@Composable
private fun String.hasActiveFilters(
    isPartnersFilterActive: Boolean,
    isPlugTypeFiltersActive: Boolean,
    isFavouritesActive: Boolean,
    isPnCFilterActive: Boolean,
): Boolean =
    when (this) {
        stringResource(R.string.partners) -> isPartnersFilterActive
        stringResource(R.string.plug_types) -> isPlugTypeFiltersActive
        stringResource(R.string.favorites) -> isFavouritesActive
        stringResource(R.string.plug_and_charge) -> isPnCFilterActive
        else -> false
    }

@Composable
private fun String.allSelectedFilterItems(
    selectedPartnersList: List<Int>,
    selectedPlugTypeList: List<Int>,
): List<Int> =
    when (this) {
        stringResource(R.string.partners) -> {
            selectedPartnersList
        }

        stringResource(R.string.plug_types) -> {
            selectedPlugTypeList
        }

        else -> emptyList()
    }

data class FilterMenuData(
    val filterType: String,
    val filterData: ArrayList<FilterData>,
)

data class FilterData(
    val filterName: Int,
    val filterImageRes: Int? = null,
) {
    var isSelected: Boolean = false
}
