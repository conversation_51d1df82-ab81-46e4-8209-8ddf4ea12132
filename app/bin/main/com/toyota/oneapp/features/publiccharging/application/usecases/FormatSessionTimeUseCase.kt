package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.publiccharging.domain.model.ChargeDuration
import com.toyota.oneapp.features.publiccharging.domain.model.Session
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.DAYS_STRING
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.HOURS_PER_DAY
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.INVALID_TIME
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.MINUTES_PER_HOUR
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.MIN_STRING
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.ONE_DAY
import java.time.DateTimeException
import java.time.Duration
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject

class FormatSessionTimeUseCase
    @Inject
    constructor() {
        operator fun invoke(
            session: Session,
            remainingMinutes: Int? = null,
        ): String {
            // Check for invalid time values
            if (remainingMinutes == INVALID_TIME) {
                return ""
            }

            // If remainingMinutes is provided, format that instead of calculating duration
            if (remainingMinutes != null) {
                val duration =
                    ChargeDuration(
                        hours = remainingMinutes / MINUTES_PER_HOUR,
                        minutes = remainingMinutes % MINUTES_PER_HOUR,
                    )
                return formatDuration(duration)
            }

            // Otherwise calculate duration from session timestamps
            return try {
                val duration = calculateDuration(session)
                formatDuration(duration)
            } catch (e: DateTimeException) {
                "" // Return empty string for invalid timestamps to match test expectations
            }
        }

        private fun calculateDuration(session: Session): ChargeDuration {
            val startTime = parseDateTime(session.data.startDatetime)
            val lastUpdated = parseDateTime(session.data.lastUpdated)

            val durationInMinutes = calculateMinutesBetween(startTime, lastUpdated)

            // Check for invalid time
            if (durationInMinutes == INVALID_TIME) {
                return ChargeDuration()
            }

            var remaining = durationInMinutes
            val minutes = remaining % MINUTES_PER_HOUR
            remaining /= MINUTES_PER_HOUR
            val hours = remaining % HOURS_PER_DAY
            val days = remaining / HOURS_PER_DAY

            return ChargeDuration(
                days = days,
                hours = hours,
                minutes = minutes,
            )
        }

        private fun parseDateTime(dateTimeString: String): LocalDateTime =
            LocalDateTime.parse(
                dateTimeString,
                DateTimeFormatter.ISO_DATE_TIME,
            )

        private fun calculateMinutesBetween(
            start: LocalDateTime,
            end: LocalDateTime,
        ): Int {
            val minutes = Duration.between(start, end).toMinutes().toInt()
            return if (minutes < 0 || minutes >= INVALID_TIME) INVALID_TIME else minutes
        }

        private fun formatDuration(duration: ChargeDuration): String {
            if (isDurationEmpty(duration)) {
                return ""
            }

            val parts =
                buildList {
                    formatDays(duration.days)?.let { add(it) }
                    formatHours(duration.hours)?.let { add(it) }
                    formatMinutes(duration.minutes, isDurationEmpty(duration.copy(minutes = 0)))?.let { add(it) }
                }

            return parts.joinToString(" ")
        }

        private fun isDurationEmpty(duration: ChargeDuration): Boolean = duration.days == 0 && duration.hours == 0 && duration.minutes == 0

        private fun formatDays(days: Int): String? =
            when {
                days <= 0 -> null
                days == 1 -> ONE_DAY
                else -> "$days $DAYS_STRING"
            }

        private fun formatHours(hours: Int): String? =
            when {
                hours <= 0 -> null
                else -> "$hours hr"
            }

        private fun formatMinutes(
            minutes: Int,
            showZeroMinutes: Boolean,
        ): String? =
            when {
                minutes > 0 || showZeroMinutes -> "$minutes $MIN_STRING"
                else -> null
            }
    }
