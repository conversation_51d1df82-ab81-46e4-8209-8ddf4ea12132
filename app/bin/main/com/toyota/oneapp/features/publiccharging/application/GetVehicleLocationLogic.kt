/*
 *
 *  Copyright © 2025. Toyota Motors North America Inc
 *  All rights reserved.
 * /
 *
 *
 */

package com.toyota.oneapp.features.publiccharging.application

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.findstations.application.toLatLng
import com.toyota.oneapp.features.publiccharging.application.usecases.GetVehicleLocationUseCase
import javax.inject.Inject

class GetVehicleLocationLogic
    @Inject
    constructor() : GetVehicleLocationUseCase {
        override fun getVehicleLocationFromElectricStatus(response: ElectricStatusResponse?): LatLng? =
            response?.payload?.positionInfo?.toLatLng()
    }
