package com.toyota.oneapp.features.publiccharging.dataaccess.service

import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationsListResponse
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeSessionData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingRequest
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Query

interface PublicChargingApi {
    @GET("/charging/v2/locations")
    suspend fun fetchAllNearbyStations(
        @Header("x-brand") brand: String,
        @Header("fuelType") fuelType: String,
        @Query("radius") radius: Int,
        @Header("x-region") region: String,
        @Header("x-latitude") latitude: Double?,
        @Header("x-longitude") longitude: Double?,
        @Header("x-vin") vin: String,
        @Query("connector") connector: String? = null,
        @Query("displayMode") displayMode: String? = null,
        @Query("operatorname") operatorname: String? = null,
    ): Response<StationsListResponse?>

    @POST("/charging/v2/charger/start")
    suspend fun startCharging(
        @Header("x-make") xMake: String,
        @Header("x-vin") xVin: String,
        @Body startChargingRequest: StartChargingRequest,
    ): Response<StartChargingData?>

    @PUT("/charging/v2/charger/stop")
    suspend fun stopCharging(
        @Header("x-make") xMake: String,
        @Header("x-vin") xVin: String,
        @Body stopChargingRequest: StopChargingRequest,
    ): Response<StopChargingData?>

    @GET("/charging/v2/charger/session")
    suspend fun fetchChargeSession(
        @Header("x-make") xMake: String,
        @Header("x-vin") xVin: String,
    ): Response<ChargeSessionData?>
}
