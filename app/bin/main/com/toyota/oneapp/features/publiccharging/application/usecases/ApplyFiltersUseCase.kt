/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.features.publiccharging.application.PublicChargingState
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

class ApplyFiltersUseCase
    @Inject
    constructor(
        private val getStationsWithFiltersUseCase: GetStationsWithFiltersUseCase,
    ) {
        operator fun invoke(
            positionInfo: LatLng?,
            partnerTypeFilters: List<Int>,
            plugTypeFilters: List<Int>,
            vehicleInfo: VehicleInfo?,
        ): Flow<PublicChargingState> {
            LogTool.d("ApplyFiltersUseCase", "Applying filters - Position: $positionInfo")
            LogTool.d("ApplyFiltersUseCase", "Partner filters: $partnerTypeFilters")
            LogTool.d("ApplyFiltersUseCase", "Plug filters: $plugTypeFilters")

            return getStationsWithFiltersUseCase(
                positionInfo = positionInfo,
                partnerFilters = partnerTypeFilters,
                plugFilters = plugTypeFilters,
                vehicleInfo = vehicleInfo,
            )
        }
    }
