/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.publiccharging.application.PublicChargingState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class SetInitialLoadingStateUseCase
    @Inject
    constructor() {
        operator fun invoke(): Flow<PublicChargingState> =
            flow {
                emit(PublicChargingState.Loading)
            }
    }
