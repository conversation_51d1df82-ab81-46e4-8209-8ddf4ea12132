/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.publiccharging.application

import com.toyota.oneapp.model.poi.StcLocationResponse

sealed class SendToCarState {
    data object Init : SendToCarState()

    data class Success(
        val data: StcLocationResponse?,
    ) : SendToCarState()

    data class Error(
        val message: String,
    ) : SendToCarState()
}
