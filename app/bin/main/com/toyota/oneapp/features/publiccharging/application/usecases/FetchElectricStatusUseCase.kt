/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.core.commonapicalls.domain.repository.CommonApiRepository
import com.toyota.oneapp.features.publiccharging.presentation.model.UIElectricStatus
import com.toyota.oneapp.features.publiccharging.presentation.model.toUIModel
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

class FetchElectricStatusUseCase
    @Inject
    constructor(
        private val commonApiRepository: CommonApiRepository,
    ) {
        companion object {
            private val TAG = FetchElectricStatusUseCase::class.java.simpleName
            private const val POLLING_INTERVAL_MS = 5000L
        }

        internal var pollingIntervalMs: Long = POLLING_INTERVAL_MS
        var polling = true

        operator fun invoke(
            vehicleInfo: VehicleInfo?,
            deviceId: String,
        ): Flow<UIElectricStatus?> =
            flow {
                if (vehicleInfo == null) {
                    return@flow
                }
                while (polling) {
                    val appRequestNo =
                        postAppRequestAndGetNumber(
                            requestId = ToyotaConstants.EMPTY_STRING,
                            vehicleInfo.vin,
                            vehicleInfo.brand,
                            vehicleInfo.generation,
                            deviceId,
                        ) ?: continue
                    while (polling) {
                        val response =
                            commonApiRepository.getEvRealTimeStatus(
                                brand = vehicleInfo.brand,
                                vin = vehicleInfo.vin,
                                generation = vehicleInfo.generation,
                                appRequestNo = appRequestNo,
                            )

                        when (response) {
                            is Resource.Success -> {
                                val uiModel = response.data?.toUIModel()
                                emit(uiModel)
                            }

                            else -> {
                                Throwable(response.message)
                                break // re-post needed
                            }
                        }

                        delay(pollingIntervalMs)
                    }
                }
            }

        private suspend fun postAppRequestAndGetNumber(
            requestId: String,
            vin: String,
            brand: String,
            generation: String,
            deviceId: String,
        ): String? {
            val postResponse =
                commonApiRepository.postEvRealTimeStatus(
                    requestId = requestId,
                    vin = vin,
                    generation = generation,
                    brand = brand,
                    deviceId = deviceId,
                )

            return when (postResponse) {
                is Resource.Success -> postResponse.data?.payload?.appRequestNo

                is Resource.Failure -> {
                    LogTool.e(TAG, "Failed to get appRequestNo: ${postResponse.message}")
                    null
                }

                else -> {
                    LogTool.e(TAG, "Unknown error while getting appRequestNo")
                    null
                }
            }
        }
    }
