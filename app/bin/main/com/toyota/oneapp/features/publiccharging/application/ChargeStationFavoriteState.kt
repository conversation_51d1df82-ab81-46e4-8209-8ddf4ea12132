/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.publiccharging.application

import com.toyota.oneapp.model.poi.LocationDetails

sealed class ChargeStationFavoriteState {
    data object Loading : ChargeStationFavoriteState()

    data class Success(
        val data: List<LocationDetails?>,
    ) : ChargeStationFavoriteState()

    data class Error(
        val message: String,
    ) : ChargeStationFavoriteState()
}
