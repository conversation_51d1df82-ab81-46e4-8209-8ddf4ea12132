/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingRequest
import com.toyota.oneapp.features.publiccharging.presentation.model.StopChargingStatusUI
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

class StopChargingUseCase
    @Inject
    constructor(
        private val publicChargingUseCase: PublicChargingUseCase,
    ) {
        companion object {
            private val TAG = StopChargingUseCase::class.java.simpleName
        }

        operator fun invoke(
            chargingId: String,
            vehicleInfo: VehicleInfo?,
        ): Flow<StopChargingStatusUI?> {
            LogTool.d(TAG, "Stopping charging session with ID: $chargingId")

            return if (vehicleInfo == null) {
                LogTool.e(TAG, "Vehicle info is null")
                flow { emit(null) }
            } else {
                val request = StopChargingRequest(chargingId = chargingId)
                publicChargingUseCase.stopCharging(
                    vehicleInfo = vehicleInfo,
                    stopChargingRequest = request,
                )
            }
        }
    }
