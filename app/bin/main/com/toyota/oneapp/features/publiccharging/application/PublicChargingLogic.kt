/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application

import com.google.android.gms.maps.model.LatLng
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.oneapp.R
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentdatetime.toTime
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Connectors
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Elements
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Evses
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.PriceComponents
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Station
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.TariffAltText
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.TariffInfo
import com.toyota.oneapp.features.findstations.domain.model.MarkerInfo
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.FCV_RADIUS
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.PHEV_RADIUS
import com.toyota.oneapp.features.publiccharging.application.usecases.PublicChargingUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.RemoveDisabledChargingNetworksFromChargeStationsInfoUseCase
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.features.publiccharging.domain.model.Coordinates
import com.toyota.oneapp.features.publiccharging.domain.model.EvConnector
import com.toyota.oneapp.features.publiccharging.domain.model.EvConnectorDetails
import com.toyota.oneapp.features.publiccharging.domain.model.EvConnectorSum
import com.toyota.oneapp.features.publiccharging.domain.model.EvEVSE
import com.toyota.oneapp.features.publiccharging.domain.model.EvOpeningTimes
import com.toyota.oneapp.features.publiccharging.domain.model.EvOperator
import com.toyota.oneapp.features.publiccharging.domain.model.EvPriceComponent
import com.toyota.oneapp.features.publiccharging.domain.model.EvPriceElement
import com.toyota.oneapp.features.publiccharging.domain.model.EvTariffAltText
import com.toyota.oneapp.features.publiccharging.domain.model.EvTariffInfo
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.model.StationListInfo
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.repo.PublicChargingRepo
import com.toyota.oneapp.features.publiccharging.presentation.model.StopChargingStatusUI
import com.toyota.oneapp.features.publiccharging.presentation.model.toUiModel
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.DAILY_OPEN_24_HOURS
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.DAY_OPEN_24_HOURS
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.WEEKLY_OPEN_24_HOURS
import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.model.poi.SendPOIToCarRequest
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.model.vehicle.VehicleInfo.FUELTYPE_HYDROGENFUELCELL
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.network.api.repository.LocationRepository
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.toLocationDetails
import io.grpc.stub.StreamObserver
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class PublicChargingLogic
    @Inject
    constructor(
        private val publicChargingRepo: PublicChargingRepo,
        private val locationRepository: LocationRepository,
        private val userProfileAPIManager: UserProfileAPIManager,
        private val removeDisabledChargingNetworksFromChargeStationsInfo: RemoveDisabledChargingNetworksFromChargeStationsInfoUseCase,
    ) : PublicChargingUseCase {
        override fun fetchNearByStations(
            lat: Double?,
            long: Double?,
            vehicleInfo: VehicleInfo,
            partnerFilters: List<Int>,
            plugFilters: List<Int>,
        ): Flow<StationListInfo> {
            return flow {
                if (lat == null || long == null) {
                    emit(StationListInfo())
                    return@flow
                }

                if (vehicleInfo.isFeatureEnabled(Feature.EV_PUBLIC_CHARGING_CONTROL)) {
                    // Convert filter IDs to API-compatible strings
                    val operatorName = getPartnerType(partnerFilters)

                    val connectorTypes = getConnectorType(plugFilters)

                    val response =
                        publicChargingRepo.fetchNearbyStations(
                            brand = vehicleInfo.brand,
                            fuelType = vehicleInfo.fuelType,
                            radius =
                                if (vehicleInfo.fuelType == FUELTYPE_HYDROGENFUELCELL) {
                                    FCV_RADIUS
                                } else {
                                    PHEV_RADIUS
                                },
                            region = vehicleInfo.region,
                            latitude = lat,
                            longitude = long,
                            vin = vehicleInfo.vin,
                            connector = connectorTypes,
                            operatorname = operatorName,
                        )

                    val totalStations = response.data?.payload?.totalRecords ?: 0

                    response.data?.payload?.stations?.let {
                        emit(
                            StationListInfo(
                                chargeStationsInfoList =
                                    removeDisabledChargingNetworksFromChargeStationsInfo(
                                        chargeStationsInfo = it.toUiModel(),
                                    ),
                                totalStations = totalStations,
                            ),
                        )
                    } ?: emit(StationListInfo())
                }
            }
        }

        private fun getPartnerType(partnerFilters: List<Int>): String? =
            if (partnerFilters.isNotEmpty()) {
                val convertedTypes =
                    partnerFilters
                        .mapNotNull {
                            val type = getPartnerTypeForId(it)
                            type.ifEmpty { null }
                        }.joinToString(",")
                convertedTypes.ifEmpty { null }
            } else {
                null
            }

        private fun getConnectorType(plugFilters: List<Int>): String? =
            if (plugFilters.isNotEmpty()) {
                val convertedTypes =
                    plugFilters
                        .mapNotNull {
                            val type = getConnectorTypeForId(it)
                            type.ifEmpty { null }
                        }.joinToString(",")
                convertedTypes.ifEmpty { null }
            } else {
                null
            }

        // Helper method to convert filter ID to partner type string
        private fun getPartnerTypeForId(filterId: Int): String =
            when (filterId) {
                R.string.partnerFilterEVConnect -> PublicChargingConstants.EV_CONNECT
                R.string.partnerFilterEVgo -> PublicChargingConstants.EV_GO
                R.string.partnerFilterFLONetwork ->
                    "${PublicChargingConstants.FLO_NETWORK},${PublicChargingConstants.FLO_US_NETWORK}"
                R.string.partnerFilterGreenlots -> PublicChargingConstants.GREENLOTS
                R.string.partnerFilterShellRecharge -> PublicChargingConstants.SHELL_RECHARGE
                R.string.partnerFilterIONNA -> PublicChargingConstants.IONNA
                R.string.chargePointLowerCase -> PublicChargingConstants.CHARGE_POINT
                R.string.tesla -> PublicChargingConstants.TESLA
                else -> ToyotaConstants.EMPTY_STRING
            }

        // Helper method to convert filter ID to connector type string
        private fun getConnectorTypeForId(filterId: Int): String =
            when (filterId) {
                R.string.j1772 -> PublicChargingConstants.J1772
                R.string.ccs1, R.string.chademo -> "${PublicChargingConstants.CCS1},${PublicChargingConstants.CHADEMO}"
                R.string.nacs -> PublicChargingConstants.NACS
                else -> ToyotaConstants.EMPTY_STRING
            }

        override fun startCharging(
            vehicleInfo: VehicleInfo,
            startChargingRequest: StartChargingRequest,
        ): Flow<StartChargingData?> =
            flow {
                val result: Resource<StartChargingData?> =
                    publicChargingRepo.startCharging(
                        make = vehicleInfo.make,
                        vin = vehicleInfo.vin,
                        startChargingRequest = startChargingRequest,
                    )
                emit(
                    when (result) {
                        is Resource.Success -> result.data
                        else -> null
                    },
                )
            }

        override fun stopCharging(
            vehicleInfo: VehicleInfo,
            stopChargingRequest: StopChargingRequest,
        ): Flow<StopChargingStatusUI?> =
            flow {
                val response =
                    publicChargingRepo.stopCharging(
                        make = vehicleInfo.make,
                        vin = vehicleInfo.vin,
                        stopChargingRequest = stopChargingRequest,
                    )
                val uiModel =
                    when (response) {
                        is Resource.Success -> response.data?.toUiModel()
                        else -> null
                    }
                emit(uiModel)
            }

        override fun updateFavoriteLocation(favList: List<LocationDetails>): Flow<ChargeStationFavoriteState> =
            callbackFlow {
                trySend(ChargeStationFavoriteState.Loading)
                userProfileAPIManager.updateFavouriteLocation(
                    ArrayList(favList),
                    object : StreamObserver<ProfileServiceServer.UpdateUserProfileResponse> {
                        override fun onNext(value: ProfileServiceServer.UpdateUserProfileResponse) {
                            trySend(ChargeStationFavoriteState.Success(favList))
                            close()
                        }

                        override fun onError(t: Throwable?) {
                            trySend(ChargeStationFavoriteState.Error(t?.message ?: "Unknown error"))
                            close()
                        }

                        override fun onCompleted() {
                            close()
                        }
                    },
                )
                awaitClose()
            }

        override fun getFavoriteStation(): Flow<ChargeStationFavoriteState> =
            callbackFlow {
                trySend(ChargeStationFavoriteState.Loading)

                val callback =
                    object : StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                        override fun onNext(value: ProfileServiceServer.GetUserProfileResponse) {
                            val favoriteList =
                                value.userProfile.commonVehicleSettings.navigationSettings.favorites.valueList
                                    .map { it.toLocationDetails() }

                            trySend(ChargeStationFavoriteState.Success(favoriteList))
                        }

                        override fun onError(t: Throwable) {
                            trySend(ChargeStationFavoriteState.Error(t.message ?: "Unknown error"))
                            close()
                        }

                        override fun onCompleted() {
                            close()
                        }
                    }

                userProfileAPIManager.getUserProfile(callback)

                awaitClose()
            }

        override fun sendToCar(sendPOIToCarRequest: SendPOIToCarRequest): Flow<SendToCarState> =
            flow {
                emit(SendToCarState.Init)
                when (val response = locationRepository.sendPOIToCar(sendPOIToCarRequest)) {
                    is Resource.Success -> {
                        emit(SendToCarState.Success(response.data?.payload))
                    }

                    is Resource.Failure -> {
                        emit(SendToCarState.Error(response.error?.message ?: "Unknown Error"))
                    }

                    else -> {
                        // do nothing
                    }
                }
            }
    }

private fun stationName(station: Station): String? {
    val name =
        when {
            !station.operator?.name.isNullOrEmpty() -> station.operator?.name
            !station.owner?.name.isNullOrEmpty() -> station.owner?.name
            !station.partnerInfo
                ?.firstOrNull()
                ?.name
                .isNullOrEmpty() -> station.partnerInfo?.first()?.name

            else -> station.evSource.orEmpty()
        }
    return name
}

fun List<Station>.toUiModel(): List<ChargeStationInfo> {
    val data = ArrayList<ChargeStationInfo>()
    this.forEach {
        data.add(
            ChargeStationInfo(
                stationName = stationName(it),
                addressLine1 = it.name,
                addressLine2 = "${it.address}, ${it.city}",
                markerInfo = it.geometry?.coordinates.getMarkerInfo(it.name.orEmpty()),
                is24hoursOpen = it.openingTimes?.timing.is24hoursTiming(),
                evId = it.id,
                evName = it.name,
                evConnectorTypes = it.evConnectorTypes,
                evTariffInfo = mapTariffInfo(it.partnerInfo?.firstOrNull()?.tariffInfo ?: emptyList()),
                evOperator =
                    EvOperator(
                        name = it.operator?.name,
                        website = it.operator?.website,
                    ),
                evPhoneNumber = it.phoneNumber,
                evPlaceId = it.placeId,
                evPostalCode = it.postalCode,
                evProvince = it.province,
                evAddress = it.address,
                evCity = it.city,
                evStatusCode = it.statusCode,
                evStatusSum = it.statusSum,
                evTimeZone = it.timeZone,
                evEvses = mapEves(it.partnerInfo?.firstOrNull()?.evses ?: emptyList()),
                evEvDcFastNum = it.evDCFastNum,
                evEvLevel1EvseNum = it.evLevel1,
                evEvLevel2EvseNum = it.evLevel2,
                evIsPartner = it.isPartner == true,
                evEvSource = it.evSource.orEmpty(),
                evOpeningTimes =
                    EvOpeningTimes(
                        evRegularHour = it.openingTimes?.regularHour,
                        evTiming = it.openingTimes?.timing,
                    ),
                evConnectorSum =
                    EvConnectorSum(
                        evCcs1 =
                            EvConnectorDetails(
                                it.connectorSum?.ccs1?.total,
                                it.connectorSum?.ccs1?.active,
                            ),
                        evJ1772 =
                            EvConnectorDetails(
                                it.connectorSum?.j1772?.total,
                                it.connectorSum?.j1772?.active,
                            ),
                        evNacs =
                            EvConnectorDetails(
                                it.connectorSum?.nacs?.total,
                                it.connectorSum?.nacs?.active,
                            ),
                        evChademo =
                            EvConnectorDetails(
                                it.connectorSum?.chademo?.total,
                                it.connectorSum?.chademo?.active,
                            ),
                    ),
                partnerInfoId = it.partnerInfo?.firstOrNull()?.id,
                evIsPnC = it.isPnC(),
            ),
        )
    }
    return data
}

fun EvConnectorSum.getPlugValue(): List<List<String>> {
    val plugType = ArrayList<ArrayList<String>>()

    fun addPlug(
        plugTypeName: String,
        connector: EvConnectorDetails?,
    ) {
        connector?.takeIf { it.total != null && it.total != 0 }?.let {
            val plugValue = arrayListOf(plugTypeName, "${it.active ?: 0}/${it.total}")
            plugType.add(plugValue)
        }
    }
    addPlug(PublicChargingConstants.CCS1, evCcs1)
    addPlug(PublicChargingConstants.J1772, evJ1772)
    addPlug(PublicChargingConstants.CHADEMO, evChademo)
    addPlug(PublicChargingConstants.NACS, evNacs)
    return plugType
}

fun List<Double>?.getMarkerInfo(stationName: String): MarkerInfo? {
    if (this != null && this.size >= 2) {
        return MarkerInfo(
            LatLng(this[1], this[0]),
            stationName,
        )
    }
    return null
}

fun String?.is24hoursTiming(): Boolean = this in setOf(DAY_OPEN_24_HOURS, DAILY_OPEN_24_HOURS, WEEKLY_OPEN_24_HOURS)

fun Station.isPnC(): Boolean =
    partnerInfo?.flatMap { it.evses }
        ?.flatMap { it.connectors }
        ?.any { it.pnc == true } ?: false

private fun mapPriceElements(elements: ArrayList<Elements>?): List<EvPriceElement>? =
    elements?.map { elem ->
        EvPriceElement(
            evPriceComponents = mapPriceComponents(elem.priceComponents),
        )
    }

private fun mapTariffAltText(elements: ArrayList<TariffAltText>): List<EvTariffAltText> =
    elements.map { elem ->
        EvTariffAltText(
            language = elem.language,
            text = elem.text,
        )
    }

private fun mapTariffInfo(tariff: List<TariffInfo>): List<EvTariffInfo> =
    tariff.map {
        EvTariffInfo(
            evCurrency = it.currency,
            evElements = mapPriceElements(it.elements),
            evId = it.id,
            evPartnerName = it.partnerName,
            evTariffAltURL = it.tariffAltUrl,
            evTariffAltText = mapTariffAltText(it.tariffAltText),
        )
    }

private fun mapPriceComponents(components: ArrayList<PriceComponents>): List<EvPriceComponent> =
    components.map { component ->
        EvPriceComponent(
            evPrice = component.price,
            evStepSize = component.stepSize,
            evType = component.type,
        )
    }

private fun mapEves(evse: List<Evses>): List<EvEVSE> =
    evse.map { evseItem ->
        val evseObj =
            EvEVSE(
                uid = evseItem.uid,
                evseId = evseItem.evseId,
                status = evseItem.status,
                capabilities = evseItem.capabilities,
                connectors = emptyList(),
                coordinates =
                    Coordinates(
                        evLatitude = evseItem.coordinates?.latitude,
                        evLongitude = evseItem.coordinates?.longitude,
                    ),
                floorLevel = evseItem.floorLevel,
                physicalReference = evseItem.physicalReference,
                lastUpdated = evseItem.lastUpdated,
                openingTimes =
                    evseItem.lastUpdated?.let {
                        EvOpeningTimes(
                            evRegularHour = it.toTime(),
                            evTiming = it.toTime(),
                        )
                    },
            )

        val connectors = mapConnectors(evseItem.connectors, evseObj)

        // Create a copy of the EVSE with the connectors set
        evseObj.copy(connectors = connectors)
    }

private fun mapConnectors(
    con: List<Connectors>,
    parent: EvEVSE,
): List<EvConnector> =
    con.map { connector ->
        EvConnector(
            evAmperage = connector.amperage,
            evChargerLevel = connector.chargerLevel,
            evChargerType = connector.chargerType,
            evFormat = connector.format,
            evId = connector.id,
            evLastUpdated = connector.lastUpdated,
            evMaxPower = connector.maxPower,
            evPowerType = connector.powerType,
            evStandard = connector.standard,
            evTariffId = connector.tariffId,
            evVoltage = connector.voltage,
            evseParent = parent,
        )
    }
