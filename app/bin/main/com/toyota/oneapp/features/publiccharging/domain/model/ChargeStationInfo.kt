/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.domain.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.R
import com.toyota.oneapp.features.findstations.domain.model.MarkerInfo
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants
import kotlinx.android.parcel.Parcelize
import java.util.UUID

@Parcelize
data class ChargeStationInfo(
    val stationName: String?,
    val addressLine1: String?,
    val addressLine2: String?,
    val evConnectorSum: EvConnectorSum?,
    val is24hoursOpen: Boolean = false,
    val markerInfo: MarkerInfo?,
    val evId: String? = null,
    val evName: String? = null,
    val evConnectorTypes: List<String>? = null,
    val evTariffInfo: List<EvTariffInfo> = emptyList(),
    val evOperator: EvOperator? = null,
    val evPhoneNumber: String? = null,
    val evPlaceId: String? = null,
    val evPostalCode: String? = null,
    val evProvince: String? = null,
    val evAddress: String? = null,
    val evCity: String? = null,
    val evStatusCode: String? = null,
    val evStatusSum: String? = null,
    val evTimeZone: String? = null,
    val evEvses: List<EvEVSE> = emptyList(),
    val evEvDcFastNum: Int? = null,
    val evEvLevel1EvseNum: Int? = null,
    val evEvLevel2EvseNum: Int? = null,
    val evIsPartner: Boolean = false,
    val evEvSource: String = "",
    val evOpeningTimes: EvOpeningTimes? = null,
    val partnerInfoId: String? = null,
    val evIsPnC: Boolean = false,
) : Parcelable

@Parcelize
data class EvConnectorSum(
    val evCcs1: EvConnectorDetails?,
    val evChademo: EvConnectorDetails?,
    val evJ1772: EvConnectorDetails?,
    val evNacs: EvConnectorDetails?,
) : Parcelable

@Parcelize
data class EvConnectorDetails(
    val total: Int?,
    val active: Int?,
) : Parcelable

@Parcelize
data class EvTariffInfo(
    val evCurrency: String? = null,
    val evElements: List<EvPriceElement>? = null,
    val evId: String? = null,
    val evPartnerName: String? = null,
    val evTariffAltURL: String? = null,
    val evTariffAltText: List<EvTariffAltText>? = null,
) : Parcelable

@Parcelize
data class EvTariffAltText(
    var language: String? = null,
    var text: String? = null,
) : Parcelable

@Parcelize
data class EvOperator(
    var name: String? = null,
    var website: String? = null,
) : Parcelable

@Parcelize
data class EvPriceElement(
    val evId: UUID = UUID.randomUUID(),
    val evPriceComponents: List<EvPriceComponent>? = null,
) : Parcelable

@Parcelize
data class EvPriceComponent(
    val evId: UUID = UUID.randomUUID(),
    val evPrice: Double? = null,
    val evStepSize: Int? = null,
    val evType: String? = null,
) : Parcelable

@Parcelize
data class EvEVSE(
    val uid: String? = null,
    @SerializedName("evse_id") val evseId: String? = null,
    val status: String? = null,
    val capabilities: List<String?>? = null,
    val connectors: List<EvConnector>? = null,
    val coordinates: Coordinates? = null,
    @SerializedName("floor_level") val floorLevel: String? = null,
    @SerializedName("parking_restrictions") val parkingRestrictions: List<String?>? = null,
    @SerializedName("physical_reference") val physicalReference: String? = null,
    @SerializedName("last_updated") val lastUpdated: String? = null,
    @SerializedName("opening_times") val openingTimes: EvOpeningTimes? = null,
) : Parcelable

@Parcelize
data class EvConnector(
    val evAmperage: Int? = null,
    val evChargerLevel: Int? = null,
    val evChargerType: String? = null,
    val evFormat: String? = null,
    val evId: String? = null,
    val evLastUpdated: String? = null,
    val evMaxPower: Double? = null,
    val evPowerType: String? = null,
    val evStandard: String? = null,
    val evTariffId: String? = null,
    val evVoltage: Int? = null,
    val evseParent: EvEVSE? = null,
) : Parcelable {
    fun mapStandard(): String =
        when (evStandard) {
            PublicChargingConstants.J1772, PublicChargingConstants.IEC_62196_T1 -> PublicChargingConstants.J1772
            PublicChargingConstants.CCS1, PublicChargingConstants.IEC_62196_T1_COMBO -> PublicChargingConstants.CCS1
            PublicChargingConstants.CHADEMO -> PublicChargingConstants.CHADEMO
            PublicChargingConstants.TESLA_S, PublicChargingConstants.NACS -> PublicChargingConstants.NACS
            else -> evStandard ?: "Unknown"
        }

    fun mapStandardIcon(): Int =
        when (evStandard) {
            PublicChargingConstants.J1772, PublicChargingConstants.IEC_62196_T1 -> R.drawable.ic_charger_level_2
            PublicChargingConstants.CCS1, PublicChargingConstants.IEC_62196_T1_COMBO, PublicChargingConstants.CHADEMO,
            -> R.drawable.ic_charger_dc_fast
            PublicChargingConstants.TESLA_S, PublicChargingConstants.NACS -> R.drawable.ic_charger_nacs
            else -> R.drawable.ic_charger_level_2
        }
}

@Parcelize
data class EvOpeningTimes(
    val evRegularHour: String? = null,
    val evTiming: String? = null,
) : Parcelable

@Parcelize
data class Coordinates(
    val evLatitude: String? = null,
    val evLongitude: String? = null,
) : Parcelable

data class StationListInfo(
    val chargeStationsInfoList: List<ChargeStationInfo> = emptyList(),
    val totalStations: Int = 0,
)
