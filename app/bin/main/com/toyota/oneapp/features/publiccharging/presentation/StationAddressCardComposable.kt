/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.presentation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.google.accompanist.flowlayout.FlowRow
import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.DrawableImage
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.spacer.HorizontalSpacer
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.entrollment.util.Constance
import com.toyota.oneapp.features.findstations.application.distanceBetween
import com.toyota.oneapp.features.findstations.domain.model.MarkerInfo
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.MOCK_EVGO_STATION_NAME
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.MOCK_TESLA_STATION_NAME

@Composable
fun StationAddressCardComposable(
    data: ChargeStationInfo,
    selectedLocation: LatLng?,
) {
    Column(
        modifier =
            Modifier
                .padding(horizontal = 16.dp),
    ) {
        Row(
            horizontalArrangement = Arrangement.End,
        ) {
            FlowRow(
                modifier = Modifier.weight(1f),
            ) {
                data.stationName?.let { stationName ->
                    OABody4TextView(
                        text = stationName,
                        color = AppTheme.colors.tertiary03,
                        modifier = Modifier.weight(0.8f),
                    )
                }
                if (data.evIsPnC) {
                    HorizontalSpacer(size = 8)
                    DrawableImage(
                        drawableId = R.drawable.pnc_label,
                        contentDescriptionId = R.string.plug_and_charge,
                    )
                }
            }
            Spacer(modifier = Modifier.width(8.dp))
            if (selectedLocation != null && data.markerInfo?.stationPosition != null) {
                OACallOut1TextView(
                    text = selectedLocation.distanceBetween(data.markerInfo.stationPosition, false),
                    color = AppTheme.colors.tertiary05,
                    maxLines = 1,
                )
            }
        }
        if (data.evEvSource == Constance.TESLA) {
            Spacer(modifier = Modifier.height(8.dp))
            OACallOut1TextView(
                text = stringResource(R.string.tesla_charge_station_disclaimer),
                color = AppTheme.colors.tertiary05,
                maxLines = 2,
            )
        }
        Spacer(modifier = Modifier.height(8.dp))
        data.addressLine1?.let { addressLine1 ->
            OACallOut1TextView(
                text = addressLine1,
                color = AppTheme.colors.tertiary05,
                maxLines = 1,
            )
        }
        data.addressLine2?.let { addressLine2 ->
            OACallOut1TextView(
                text = addressLine2,
                color = AppTheme.colors.tertiary05,
                maxLines = 1,
            )
        }
        if (data.is24hoursOpen) {
            OACallOut1TextView(
                text = stringResource(R.string.open_24_hours),
                color = AppTheme.colors.secondary01,
            )
        }
    }
}

@Preview(showBackground = true, name = "Station Address Card", heightDp = 152)
@Composable
fun StationAddressCardPreview() {
    val sampleStationPosition = LatLng(37.7749, -122.4194) // San Francisco coordinates
    val sampleChargeStationInfo =
        ChargeStationInfo(
            stationName = MOCK_TESLA_STATION_NAME,
            addressLine1 = "123 Electric Avenue",
            addressLine2 = "San Francisco, CA 94105",
            evConnectorSum = null,
            is24hoursOpen = true,
            markerInfo =
                MarkerInfo(
                    stationPosition = sampleStationPosition,
                    stationName = MOCK_TESLA_STATION_NAME,
                ),
            evId = "station1",
            evName = MOCK_TESLA_STATION_NAME,
            evTariffInfo = emptyList(),
            evPhoneNumber = null,
            evPlaceId = null,
            evPostalCode = null,
            evProvince = null,
            evAddress = null,
            evCity = null,
            evStatusCode = null,
            evStatusSum = null,
            evTimeZone = null,
            evEvses = emptyList(),
            evEvDcFastNum = null,
            evEvLevel1EvseNum = null,
            evEvLevel2EvseNum = null,
            evIsPartner = false,
            evEvSource = "",
            evOpeningTimes = null,
            partnerInfoId = null,
        )

    StationAddressCardComposable(
        data = sampleChargeStationInfo,
        selectedLocation = LatLng(37.7739, -122.4312),
    )
}

@Preview(showBackground = true, name = "Station Address Card - 24/7", heightDp = 152)
@Composable
fun StationAddressCard24HourPreview() {
    val sampleStationPosition = LatLng(37.7749, -122.4194)
    val sampleChargeStationInfo =
        ChargeStationInfo(
            stationName = MOCK_EVGO_STATION_NAME,
            addressLine1 = "456 Charging Boulevard",
            addressLine2 = "San Francisco, CA 94105",
            evConnectorSum = null,
            is24hoursOpen = true,
            markerInfo =
                MarkerInfo(
                    stationPosition = sampleStationPosition,
                    stationName = MOCK_EVGO_STATION_NAME,
                ),
            evId = "station2",
            evName = MOCK_EVGO_STATION_NAME,
            evTariffInfo = emptyList(),
            evPhoneNumber = null,
            evPlaceId = null,
            evPostalCode = null,
            evProvince = null,
            evAddress = null,
            evCity = null,
            evStatusCode = null,
            evStatusSum = null,
            evTimeZone = null,
            evEvses = emptyList(),
            evEvDcFastNum = null,
            evEvLevel1EvseNum = null,
            evEvLevel2EvseNum = null,
            evIsPartner = true,
            evEvSource = "evgo",
            evOpeningTimes = null,
            partnerInfoId = null,
        )

    StationAddressCardComposable(
        data = sampleChargeStationInfo,
        selectedLocation = null,
    )
}

@Preview(showBackground = true, name = "Station Address Card - Minimal", heightDp = 152)
@Composable
fun StationAddressCardMinimalPreview() {
    val sampleChargeStationInfo =
        ChargeStationInfo(
            stationName = "ChargePoint Station",
            addressLine1 = "789 Power Street",
            addressLine2 = "789 Power Street",
            evConnectorSum = null,
            is24hoursOpen = true,
            markerInfo = null,
            evId = "station3",
            evName = "ChargePoint Station",
            evTariffInfo = emptyList(),
            evPhoneNumber = null,
            evPlaceId = null,
            evPostalCode = null,
            evProvince = null,
            evAddress = null,
            evCity = null,
            evStatusCode = null,
            evStatusSum = null,
            evTimeZone = null,
            evEvses = emptyList(),
            evEvDcFastNum = null,
            evEvLevel1EvseNum = null,
            evEvLevel2EvseNum = null,
            evIsPartner = false,
            evEvSource = "",
            evOpeningTimes = null,
            partnerInfoId = null,
        )

    StationAddressCardComposable(
        data = sampleChargeStationInfo,
        selectedLocation = null,
    )
}
