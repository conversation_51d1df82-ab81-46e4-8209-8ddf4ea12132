/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingRequest
import com.toyota.oneapp.features.publiccharging.presentation.model.StartChargingStatusUI
import com.toyota.oneapp.features.publiccharging.presentation.model.toUIModel
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

class StartChargingUseCase
    @Inject
    constructor(
        private val publicChargingUseCase: PublicChargingUseCase,
    ) {
        companion object {
            private val TAG = StartChargingUseCase::class.java.simpleName
        }

        operator fun invoke(
            station: ChargeStationInfo,
            connectorId: String,
            evseUid: String,
            vehicleInfo: VehicleInfo?,
            paymentMethodId: String,
        ): Flow<StartChargingStatusUI?> =
            flow {
                if (vehicleInfo == null) {
                    emit(null)
                    return@flow
                }

                // Find the EVSE to get physical reference
                val evse = station.evEvses.find { it.uid == evseUid }
                val physicalRef = evse?.physicalReference ?: "Not available"

                val normalizedPartnerName = normalizePartnerName(station.evEvSource)

                val request =
                    StartChargingRequest(
                        connectorId = connectorId,
                        evseUid = evseUid,
                        locationId = station.partnerInfoId ?: station.evId ?: "",
                        partnerName = normalizedPartnerName,
                        paymentMethod = paymentMethodId,
                    )

                logStartChargingDetails(
                    station,
                    connectorId,
                    evseUid,
                    vehicleInfo,
                    request,
                    physicalRef,
                )

                publicChargingUseCase
                    .startCharging(
                        vehicleInfo = vehicleInfo,
                        startChargingRequest = request,
                    ).collect { result ->
                        val uiModel = result?.toUIModel()
                        emit(uiModel)
                    }
            }

        private fun logStartChargingDetails(
            station: ChargeStationInfo,
            connectorId: String,
            evseUid: String,
            vehicleInfo: VehicleInfo,
            request: StartChargingRequest,
            physicalRef: String,
        ) {
            LogTool.d(TAG, "****** START CHARGING REQUEST ******")
            LogTool.d(TAG, "Station Name: ${station.stationName}")
            LogTool.d(TAG, "EVSE UID: $evseUid, Connector ID: $connectorId")
            LogTool.d(TAG, "Partner Info ID: ${station.partnerInfoId}")
            LogTool.d(TAG, "Request JSON: $request")
            LogTool.d(TAG, "Vehicle VIN: ${vehicleInfo.vin}")
            LogTool.d(TAG, "Physical Reference: $physicalRef")
            LogTool.d(TAG, "************************************")
        }

        private fun normalizePartnerName(partnerName: String?): String {
            if (partnerName.isNullOrBlank()) return ToyotaConstants.EMPTY_STRING

            val lowercased = partnerName.lowercase().trim()
            return when (lowercased) {
                "chargepoint" -> "chargepoint"
                "evgo" -> "evgo"
                "electrify america" -> "electrifyamerica"
                "electrifyamerica" -> "electrifyamerica"
                "flo" -> "flo"
                else -> lowercased
            }
        }
    }
