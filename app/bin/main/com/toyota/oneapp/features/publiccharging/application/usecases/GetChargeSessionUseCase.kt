/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.publiccharging.domain.repo.PublicChargingRepo
import com.toyota.oneapp.features.publiccharging.presentation.model.UIChargeSession
import com.toyota.oneapp.features.publiccharging.presentation.model.toUIModel
import com.toyota.oneapp.features.publiccharging.presentation.util.SessionFormatter
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class GetChargeSessionUseCase
    @Inject
    constructor(
        private val publicChargingRepo: PublicChargingRepo,
    ) {
        companion object {
            private const val POLLING_INTERVAL_MS = 15_000L
            private const val DEFAULT_TIMEOUT_MS = 180_000L
        }

        internal var pollingIntervalMs: Long = POLLING_INTERVAL_MS
        internal var timeoutMs: Long = DEFAULT_TIMEOUT_MS

        operator fun invoke(
            make: String,
            vin: String,
        ): Flow<UIChargeSession?> =
            flow {
                val startTime = System.currentTimeMillis()

                while (true) {
                    val elapsed = System.currentTimeMillis() - startTime

                    if (elapsed >= timeoutMs) {
                        break
                    }

                    when (val result = publicChargingRepo.fetchChargeSession(make, vin)) {
                        is Resource.Success -> {
                            val session = result.data?.payload?.session
                            val sessionDataExists = session?.data?.status?.isNotEmpty() == true

                            if (session != null && sessionDataExists) {
                                val uiModel = session.toUIModel()
                                val formatted = SessionFormatter.createStartTimeAndEnergy(uiModel)
                                val finalUiModel = uiModel.copy(startTimeAndEnergy = formatted)
                                emit(finalUiModel)
                            }
                        }

                        else -> {
                            // do nothing
                        }
                    }
                    delay(pollingIntervalMs)
                }
            }
    }
