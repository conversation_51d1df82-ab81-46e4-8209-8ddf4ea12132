/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.features.publiccharging.application.PublicChargingState
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

private const val TAG = "GetStationsWithFiltersUseCase"

class GetStationsWithFiltersUseCase
    @Inject
    constructor(
        private val publicChargingUseCase: PublicChargingUseCase,
    ) {
        operator fun invoke(
            positionInfo: LatLng?,
            partnerFilters: List<Int>,
            plugFilters: List<Int>,
            vehicleInfo: VehicleInfo?,
        ): Flow<PublicChargingState> =
            flow {
                LogTool.d(TAG, "Position: $positionInfo")
                LogTool.d(TAG, "Partner filters: $partnerFilters")
                LogTool.d(TAG, "Plug filters: $plugFilters")

                if (positionInfo == null) {
                    LogTool.e(TAG, "Cannot fetch stations: position is null")
                    emit(PublicChargingState.EmptyStations)
                    return@flow
                }

                if (vehicleInfo == null) {
                    LogTool.d(TAG, "No vehicle info available")
                    emit(PublicChargingState.EmptyStations)
                    return@flow
                }

                LogTool.d(TAG, "Vehicle info available: ${vehicleInfo.vin}")
                LogTool.d(
                    TAG,
                    "Vehicle feature enabled: ${vehicleInfo.isFeatureEnabled(Feature.EV_PUBLIC_CHARGING_CONTROL)}",
                )

                emit(PublicChargingState.Loading)

                publicChargingUseCase
                    .fetchNearByStations(
                        lat = positionInfo.latitude,
                        long = positionInfo.longitude,
                        vehicleInfo = vehicleInfo,
                        partnerFilters = partnerFilters,
                        plugFilters = plugFilters,
                    ).catch {
                        LogTool.e(TAG, "Error fetching stations: ${it.message}")
                        emit(PublicChargingState.EmptyStations)
                    }.collect { stationsInfo ->
                        LogTool.d(TAG, "Fetched ${stationsInfo.chargeStationsInfoList.size} stations")
                        if (stationsInfo.chargeStationsInfoList.isEmpty()) {
                            LogTool.d(TAG, "Empty stations result")
                            emit(PublicChargingState.EmptyStations)
                        } else {
                            LogTool.d(TAG, "Stations found")
                            emit(PublicChargingState.Success(stationsInfo, positionInfo))
                        }
                    }
            }
    }
