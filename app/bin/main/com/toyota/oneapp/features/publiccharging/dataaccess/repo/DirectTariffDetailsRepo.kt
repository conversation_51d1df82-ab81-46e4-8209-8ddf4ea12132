/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.dataaccess.repo

import com.toyota.oneapp.R
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.features.publiccharging.domain.model.EvPriceElement
import com.toyota.oneapp.features.publiccharging.domain.model.PriceComponent
import com.toyota.oneapp.features.publiccharging.domain.model.PriceComponentType
import com.toyota.oneapp.features.publiccharging.domain.model.TariffDetails
import com.toyota.oneapp.features.publiccharging.domain.model.TimeRestrictions
import com.toyota.oneapp.features.publiccharging.domain.repo.TariffDetailsRepo
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.OPEN_247
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.util.ToyotaConstants
import java.util.UUID
import javax.inject.Inject

/**
 * Repository implementation that directly extracts tariff information from connector data
 * instead of making separate API calls. This implementation eliminates the need for
 * mock data outside of Preview mode.
 */
class DirectTariffDetailsRepo
    @Inject
    constructor() : TariffDetailsRepo {
        override suspend fun fetchTariffIds(
            selectedConnectorId: String,
            station: ChargeStationInfo,
        ): Resource<List<String?>> {
            val tariffList = mutableListOf<String?>()

            station.evEvses.forEachIndexed { _, evEVSE ->
                evEVSE.connectors?.forEach {
                    if (it.evStandard == selectedConnectorId && !tariffList.contains(it.evTariffId)) {
                        tariffList.add(it.evTariffId)
                    }
                }
            }

            return Resource.Success(tariffList)
        }

        override suspend fun fetchTariffDetails(
            tariffId: List<String?>,
            station: ChargeStationInfo,
        ): Resource<List<TariffDetails?>> {
            val tariffDetails = mutableListOf<TariffDetails>()
            tariffId.forEach { id ->
                for (tariffInfo in station.evTariffInfo) {
                    if (tariffInfo.evId == id) {
                        val priceComponents = createPricingComponentsFromTariffId(tariffInfo.evElements)
                        tariffDetails.add(
                            TariffDetails(
                                partnerId = tariffInfo.evId ?: ToyotaConstants.EMPTY_STRING,
                                partnerName = tariffInfo.evPartnerName ?: ToyotaConstants.EMPTY_STRING,
                                currency = tariffInfo.evCurrency ?: ToyotaConstants.US_CURRENCY_CODE,
                                priceComponents = priceComponents,
                                timeRestrictions =
                                    TimeRestrictions(
                                        availability = OPEN_247,
                                        minDuration = null,
                                        maxDuration = null,
                                    ),
                                tariffAltText = tariffInfo.evTariffAltText,
                            ),
                        )
                        break
                    }
                }
            }

            return Resource.Success(tariffDetails)
        }

        /**
         * Create pricing components based on the tariff ID pattern
         */
        private fun createPricingComponentsFromTariffId(evPriceElement: List<EvPriceElement>?): List<PriceComponent> {
            val priceComponent = mutableListOf<PriceComponent>()
            evPriceElement?.forEach { priceElement ->
                priceElement.evPriceComponents?.forEach { component ->
                    priceComponent.add(
                        PriceComponent(
                            id = UUID.randomUUID(),
                            type = getPriceType(component.evType ?: ToyotaConstants.EMPTY_STRING),
                            price = component.evPrice ?: 0.0,
                            unit = getUnit(component.evType ?: ToyotaConstants.EMPTY_STRING),
                        ),
                    )
                }
            }

            return priceComponent
        }

        private fun getPriceType(type: String): PriceComponentType =
            when (type) {
                PublicChargingConstants.ENERGY -> PriceComponentType.ENERGY
                PublicChargingConstants.FLAT -> PriceComponentType.FLAT
                PublicChargingConstants.PARKING_TIME -> PriceComponentType.PARKING
                PublicChargingConstants.TIME -> PriceComponentType.TIME
                PublicChargingConstants.SESSION -> PriceComponentType.SESSION
                else -> PriceComponentType.UNKNOWN
            }

        private fun getUnit(type: String): Int =
            when (type) {
                PublicChargingConstants.ENERGY, PublicChargingConstants.FLAT -> R.string.tariff_energy_unit
                PublicChargingConstants.PARKING_TIME, PublicChargingConstants.TIME -> R.string.tariff_time_unit
                PublicChargingConstants.SESSION -> R.string.tariff_session_unit
                else -> R.string.empty_string
            }
    }
