/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.google.android.gms.maps.model.LatLng
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

private const val TAG = "SetLastPositionUseCase"

class SetLastPositionUseCase
    @Inject
    constructor() {
        private var lastPosition: LatLng? = null

        operator fun invoke(position: LatLng?): Flow<LatLng?> =
            flow {
                LogTool.d(TAG, "Setting last position to: $position")
                lastPosition = position
                emit(lastPosition)
            }

        fun getLastPosition(): LatLng? = lastPosition
    }
