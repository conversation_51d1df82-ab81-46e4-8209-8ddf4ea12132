package com.toyota.oneapp.features.publiccharging.application

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.features.publiccharging.domain.model.StationListInfo

sealed class PublicChargingState {
    data object Init : PublicChargingState()

    data object Loading : PublicChargingState()

    object ShowVehicleLocationMapIconsInPublicCharging : PublicChargingState()

    class Success(
        val data: StationListInfo,
        val selectedLatLng: LatLng?,
    ) : PublicChargingState()

    data object EmptyStations : PublicChargingState()

    data object NoFavoriteStation : PublicChargingState()

    data class Error(
        val message: String,
    ) : PublicChargingState()
}
