/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.features.publiccharging.application.PublicChargingState
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

private const val TAG = "GetStationsForLocationUseCase"

class GetStationsForLocationUseCase
    @Inject
    constructor(
        private val publicChargingUseCase: PublicChargingUseCase,
    ) {
        operator fun invoke(
            position: LatLng,
            partnerFilters: List<Int>,
            plugFilters: List<Int>,
            vehicleInfo: VehicleInfo?,
        ): Flow<PublicChargingState> =
            flow {
                if (vehicleInfo == null) {
                    LogTool.e(TAG, "No vehicle info available")
                    emit(PublicChargingState.EmptyStations)
                    return@flow
                }

                // Delegate to the LogToolic class to fetch stations
                publicChargingUseCase
                    .fetchNearByStations(
                        lat = position.latitude,
                        long = position.longitude,
                        vehicleInfo = vehicleInfo,
                        partnerFilters = partnerFilters,
                        plugFilters = plugFilters,
                    ).catch {
                        LogTool.e(TAG, "Error searching for location: ${it.message}")
                        emit(PublicChargingState.EmptyStations)
                    }.collect { stationsInfo ->
                        if (stationsInfo.chargeStationsInfoList.isEmpty()) {
                            emit(PublicChargingState.EmptyStations)
                        } else {
                            emit(PublicChargingState.Success(stationsInfo, position))
                        }
                    }
            }
    }
