package com.toyota.oneapp.features.publiccharging.dataaccess.repo

import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationsListResponse
import com.toyota.oneapp.features.publiccharging.dataaccess.service.PublicChargingApi
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeSessionData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.repo.PublicChargingRepo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class PublicChargingDefaultRepo
    @Inject
    constructor(
        private val publicChargingApi: PublicChargingApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext),
        PublicChargingRepo {
        override suspend fun fetchNearbyStations(
            brand: String,
            fuelType: String,
            radius: Int,
            region: String,
            latitude: Double?,
            longitude: Double?,
            vin: String,
            connector: String?,
            operatorname: String?,
        ): Resource<StationsListResponse?> =
            makeApiCall {
                publicChargingApi.fetchAllNearbyStations(
                    brand = brand,
                    fuelType = fuelType,
                    radius = radius,
                    region = region,
                    latitude = latitude,
                    longitude = longitude,
                    vin = vin,
                    connector = connector,
                    operatorname = operatorname,
                )
            }

        override suspend fun startCharging(
            make: String,
            vin: String,
            startChargingRequest: StartChargingRequest,
        ): Resource<StartChargingData?> =
            makeApiCall {
                publicChargingApi.startCharging(
                    xMake = make,
                    xVin = vin,
                    startChargingRequest = startChargingRequest,
                )
            }

        override suspend fun stopCharging(
            make: String,
            vin: String,
            stopChargingRequest: StopChargingRequest,
        ): Resource<StopChargingData?> =
            makeApiCall {
                publicChargingApi.stopCharging(
                    xMake = make,
                    xVin = vin,
                    stopChargingRequest = stopChargingRequest,
                )
            }

        override suspend fun fetchChargeSession(
            make: String,
            vin: String,
        ): Resource<ChargeSessionData?> =
            makeApiCall {
                publicChargingApi.fetchChargeSession(
                    xMake = make,
                    xVin = vin,
                )
            }
    }
