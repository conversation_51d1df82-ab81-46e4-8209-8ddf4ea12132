/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.application.logic

import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetDisabledChargingNetworkNamesUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.RemoveDisabledChargingNetworksFromChargeStationsInfoUseCase
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import javax.inject.Inject

class RemoveDisabledChargingNetworksFromChargeStationsInfoLogic
    @Inject
    constructor(
        private val getDisabledChargingNetworkNames: GetDisabledChargingNetworkNamesUseCase,
    ) : RemoveDisabledChargingNetworksFromChargeStationsInfoUseCase {
        override fun invoke(chargeStationsInfo: List<ChargeStationInfo>): List<ChargeStationInfo> =
            getDisabledChargingNetworkNames()
                .takeIf { it.isNotEmpty() }
                ?.let(chargeStationsInfo::removeDisabledNetworks)
                ?: chargeStationsInfo
    }

private fun List<ChargeStationInfo>.removeDisabledNetworks(disabledNetworkNames: List<String>): List<ChargeStationInfo> =
    filterNot { station: ChargeStationInfo ->
        station.stationName?.lowercase() in disabledNetworkNames
    }
