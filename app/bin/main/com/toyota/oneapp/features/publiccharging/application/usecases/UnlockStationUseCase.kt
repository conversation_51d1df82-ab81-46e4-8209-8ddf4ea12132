/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import org.json.JSONObject
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

class UnlockStationUseCase
    @Inject
    constructor() {
        companion object {
            private val TAG = UnlockStationUseCase::class.java.simpleName
        }

        /**
         * Simulates unlocking a station and returns the result.
         * Note: Currently there is no separate unlock API - this is part of the start charging flow.
         * This method maintains the existing interface for the debug screen.
         */
        operator fun invoke(
            station: ChargeStationInfo,
            connectorId: String,
            evseUid: String,
        ): String {
            LogTool.d(TAG, "Unlocking station: ${station.stationName}")
            LogTool.d(TAG, "Connector ID: $connectorId, EVSE UID: $evseUid")

            // Find the EVSE to get physical reference
            val evse = station.evEvses.find { it.uid == evseUid }
            val physicalRef = evse?.physicalReference ?: "Not available"

            // In the real API, unlocking is part of the startCharging flow
            // For debug purposes, we'll return a message indicating this behavior
            val responseJson =
                JSONObject().apply {
                    put("message", "Note: This is a precheck for the start charging flow")
                    put("apiCall", "startCharging")
                    put("connectorId", connectorId)
                    put("evseUid", evseUid)
                    put("physicalReference", physicalRef)
                    put("stationId", station.evId)
                    put("stationName", station.stationName)

                    // Add debug info section
                    put(
                        "debug_info",
                        JSONObject().apply {
                            put(
                                "evse_details",
                                JSONObject().apply {
                                    put("uid", evse?.uid)
                                    put("physical_reference", physicalRef)
                                    put("connector_count", evse?.connectors?.size ?: 0)
                                },
                            )
                            put(
                                "station_details",
                                JSONObject().apply {
                                    put("id", station.evId)
                                    put("name", station.stationName)
                                    put("source", station.evEvSource)
                                    put("is_partner", station.evIsPartner)
                                },
                            )
                        },
                    )
                }

            LogTool.d(TAG, "Unlock response: ${responseJson.toString(2)}")
            return responseJson.toString(2)
        }
    }
