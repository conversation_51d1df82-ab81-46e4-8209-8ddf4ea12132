/*
 *  Created by sudhan.ram on 29/08/24, 5:41 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementlist.application

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementPayloadResponse
import com.toyota.oneapp.features.announcementcenter.announcementlist.domain.AnnouncementListUIModel
import kotlinx.coroutines.flow.Flow

interface AnnouncementListUseCase {
    fun loadAnnouncementTabs(announcements: List<AnnouncementPayloadResponse>): Flow<MutableList<AnnouncementListUIModel>>
}
