/*
 *  Created by sudhan.ram on 29/08/24, 5:38 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 1:44 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementMessageResponse

enum class AnnouncementType {
    SUBSCRIPTION,
    OTA,
    OTHERS,
}

data class AnnouncementDetailUIModel(
    val type: AnnouncementType,
    val title: String,
    val contentHeader: String,
    val messageContent: String,
    val imageUrl: String?,
    val dismissTextButton: String,
    val primaryButton: String,
    val secondaryButton: String?,
    val dialogUIModel: DismissDialogUIModel,
    val messageResponse: AnnouncementMessageResponse,
)

data class DismissDialogUIModel(
    val alertMessage: String,
    val primaryButtonText: String,
    val secondaryButtonText: String,
)
