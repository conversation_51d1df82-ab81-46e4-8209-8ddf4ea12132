/*
 *  Created by sudhan.ram on 29/08/24, 5:42 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementlist.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementPayloadResponse
import com.toyota.oneapp.features.announcementcenter.announcementlist.application.AnnouncementListUseCase
import com.toyota.oneapp.features.announcementcenter.announcementlist.domain.AnnouncementListUIModel
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class AnnouncementListViewModel
    @Inject
    constructor(
        private val announcementCenterUseCase: AnnouncementListUseCase,
        private val dispatcherProvider: DispatcherProvider,
    ) : BaseViewModel() {
        private val tabWidgets = MutableStateFlow<MutableList<AnnouncementListUIModel>?>(value = null)
        val tabWidgetsState = tabWidgets.asStateFlow()

        fun initAnnouncementCenter(announcementPayloadList: List<AnnouncementPayloadResponse>) {
            viewModelScope.launch(dispatcherProvider.main()) {
                loadTabWidgets(announcementPayloadList)
            }
        }

        private suspend fun loadTabWidgets(announcementPayloadList: List<AnnouncementPayloadResponse>) {
            announcementCenterUseCase
                .loadAnnouncementTabs(announcementPayloadList)
                .flowOn(dispatcherProvider.main())
                .collect {
                    if (it.isNotEmpty()) {
                        tabWidgets.value = it
                    }
                }
        }
    }
