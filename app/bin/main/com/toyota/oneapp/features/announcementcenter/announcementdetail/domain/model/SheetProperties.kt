/*
 *  Created by sudhan.ram on 29/08/24, 5:38 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model

import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Shape

data class SheetProperties
    @OptIn(ExperimentalMaterialApi::class)
    constructor(
        val sheetShape: Shape,
        val sheetContent: @Composable () -> Unit,
        val sheetState: ModalBottomSheetState,
    )
