/*
 *  Created by sudhan.ram on 29/08/24, 5:38 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.dataaccess.service

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementsResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.PUT

interface AnnouncementDetailAPI {
    @PUT("/oneapi/v1/acknowledge/message")
    suspend fun dismissAnnouncement(
        @Header("VIN") vin: String,
        @Header("MESSAGE-CATEGORY") messageCategory: String,
        @Header("MESSAGEID") messageId: String,
    ): Response<BaseResponse>

    @PUT("/oneapi/v1/customer/ota")
    suspend fun updateOTA(
        @Header("X-BRAND") brand: String,
        @Header("VIN") vin: String,
    ): Response<BaseResponse>

    @GET("/oneapi/v1/vehicle-alerts")
    suspend fun getVehicleAlert(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
        @Header("X-REGION") region: String,
        @Header("INCLUDE-RENEWAL") includeRenewal: String,
    ): Response<AnnouncementsResponse>
}
