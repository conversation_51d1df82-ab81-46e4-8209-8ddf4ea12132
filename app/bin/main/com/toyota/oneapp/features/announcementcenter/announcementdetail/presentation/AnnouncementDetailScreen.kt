/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.presentation

import android.content.Intent
import android.net.Uri
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.Text
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.toyota.oneapp.R
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementMessageResponse
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.NavigationState
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.ResultState
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model.AnnouncementDetailUIModel
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model.AnnouncementType
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model.SheetProperties
import com.toyota.oneapp.features.announcementcenter.widgets.AnnouncementCenterBottomSheet
import com.toyota.oneapp.features.core.composable.OAButtonTextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.subscription.presentation.SubscriptionListScreen
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.scheduledmaint.ScheduleMaintenanceMainActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun AnnouncementDetailScreen(
    bottomSheetState: ModalBottomSheetState,
    vehicleInfo: VehicleInfo?,
    messageResponse: AnnouncementMessageResponse?,
    modifier: Modifier = Modifier,
    viewModel: AnnouncementDetailViewModel = hiltViewModel(),
) {
    val coroutineScope = rememberCoroutineScope()
    val content: @Composable (() -> Unit) = { Text("NULL") }
    var customSheetShape by remember { mutableStateOf(RoundedCornerShape(0.dp)) }
    var customSheetContent by remember { mutableStateOf(content) }
    val resultState = viewModel.resultState.collectAsState().value
    val navigationState = viewModel.navigationState.collectAsState().value

    val customSheetState = rememberModalBottomSheetState(ModalBottomSheetValue.Hidden)
    viewModel.vehicleInfo = vehicleInfo

    LaunchedEffect(customSheetState) {
        snapshotFlow { customSheetState.isVisible }.collect { isVisible ->
            if (!isVisible) {
                // Sheet is not visible
                viewModel.resetNavigation()
            }
        }
    }

    ResultStateHandler(
        resultState = resultState,
        viewModel = viewModel,
        messageResponse = messageResponse,
        onLoadAnnouncementDetail = { state ->
            viewModel.updateAnnouncementDetailUiData(messageResponse)
            LoadAnnouncementDetail(
                uiModel = state.uiModel,
                announcementDetailSheetState = bottomSheetState,
                sheetProperties =
                    SheetProperties(
                        sheetShape = customSheetShape,
                        sheetContent = customSheetContent,
                        sheetState = customSheetState,
                    ),
                viewModel = viewModel,
                modifier = modifier,
            )
        },
        onUpdateOTASuccess = {
            viewModel.dismissLoading()
            viewModel.getUpdatedAnnouncement(vehicleInfo)
        },
    )

    NavigationStateHandler(
        navigationState = navigationState,
        onLaunchDismissBottomSheet = { state ->
            customSheetShape = RoundedCornerShape(topStart = 30.dp, topEnd = 30.dp)
            customSheetContent = {
                DismissAnnouncementDialog(
                    bottomSheetState = customSheetState,
                    uiModel = state.dialogUIModel,
                ) {
                    viewModel.dismissAnnouncement(vehicleInfo, state.messageResponse)
                }
            }
            showBottomSheetDialog(
                coroutineScope = coroutineScope,
                modalBottomSheetState = customSheetState,
            )
        },
        onNavigateToSubscription = {
            customSheetShape = RoundedCornerShape(0.dp)
            customSheetContent = {
                SubscriptionListScreen(
                    bottomSheetState = customSheetState,
                )
            }
            showBottomSheetDialog(
                coroutineScope = coroutineScope,
                modalBottomSheetState = customSheetState,
            )
        },
    )

    BackHandler(enabled = (bottomSheetState.isVisible || customSheetState.isVisible)) {
        coroutineScope.launch {
            if (!customSheetState.isVisible) {
                bottomSheetState.hide()
            } else {
                customSheetState.hide()
            }
        }
    }
}

@Composable
private fun ResultStateHandler(
    resultState: ResultState,
    viewModel: AnnouncementDetailViewModel,
    messageResponse: AnnouncementMessageResponse?,
    onLoadAnnouncementDetail: @Composable (ResultState.LoadAnnouncementDetail) -> Unit,
    onUpdateOTASuccess: () -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current

    when (resultState) {
        is ResultState.Init -> {
            viewModel.updateAnnouncementDetailUiData(messageResponse)
        }

        is ResultState.Loading -> {
            ShowProgressIndicator(dialogState = true)
        }

        is ResultState.LoadAnnouncementDetail -> {
            onLoadAnnouncementDetail(resultState)
        }

        is ResultState.DismissAnnouncementSuccess -> {
            viewModel.dismissLoading()
            LaunchedEffect(key1 = Unit) {
                coroutineScope.launch {
                    (context as OADashboardActivity).finish()
                    context.startActivity(context.intent)
                }
            }
        }

        is ResultState.UpdateOTASuccess -> {
            onUpdateOTASuccess()
        }

        is ResultState.RefreshUI -> {
            viewModel.dismissLoading()
            viewModel.updateAnnouncementDetailUiData(resultState.messageResponse)
        }

        is ResultState.Error -> {
            viewModel.dismissLoading()
            // handle error
        }

        else -> {
            /** Do nothing **/
        }
    }
}

@Composable
private fun NavigationStateHandler(
    navigationState: NavigationState?,
    onLaunchDismissBottomSheet: @Composable (NavigationState.LaunchDismissBottomSheet) -> Unit,
    onNavigateToSubscription: () -> Unit,
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    when (navigationState) {
        is NavigationState.LaunchDismissBottomSheet -> {
            onLaunchDismissBottomSheet(navigationState)
        }

        is NavigationState.RedirectToDashboard -> {
            LaunchedEffect(key1 = Unit) {
                coroutineScope.launch {
                    (context as OADashboardActivity).finish()
                    context.startActivity(context.intent)
                }
            }
        }

        is NavigationState.LaunchPhoneDialer -> {
            val intent = Intent(Intent.ACTION_DIAL)
            intent.setData(Uri.parse("tel:${navigationState.phoneNumber}"))
            context.startActivity(intent)
        }

        is NavigationState.NavigateToScheduleService -> {
            context.startActivity(Intent(context, ScheduleMaintenanceMainActivity::class.java))
        }

        is NavigationState.NavigateToSubscriptions -> {
            onNavigateToSubscription()
        }

        else -> {
            /** Do nothing **/
        }
    }
}

@Composable
fun LoadAnnouncementDetail(
    uiModel: AnnouncementDetailUIModel,
    announcementDetailSheetState: ModalBottomSheetState,
    sheetProperties: SheetProperties,
    viewModel: AnnouncementDetailViewModel,
    modifier: Modifier = Modifier,
) {
    val coroutineScope = rememberCoroutineScope()
    Box(modifier = modifier.fillMaxSize()) {
        ModalBottomSheetLayout(
            sheetState = sheetProperties.sheetState,
            sheetShape = sheetProperties.sheetShape,
            sheetContent = { sheetProperties.sheetContent() },
        ) {
            AnnouncementCenterBottomSheet(
                title = uiModel.title,
                onBack = {
                    coroutineScope.launch {
                        if (announcementDetailSheetState.isVisible) {
                            announcementDetailSheetState.hide()
                        }
                    }
                },
            ) {
                Column {
                    Spacer(modifier = Modifier.height(24.dp))

                    Column(
                        modifier =
                            Modifier
                                .verticalScroll(rememberScrollState())
                                .weight(1f, fill = false),
                    ) {
                        AnnouncementContent(uiModel = uiModel)
                    }

                    AnnouncementDetailButtons(
                        uiModel = uiModel,
                        viewModel = viewModel,
                        modifier =
                            Modifier
                                .align(Alignment.CenterHorizontally),
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
fun AnnouncementContent(
    uiModel: AnnouncementDetailUIModel,
    modifier: Modifier = Modifier,
) {
    val imageTintColor =
        when (uiModel.type) {
            AnnouncementType.SUBSCRIPTION -> {
                null
            }

            AnnouncementType.OTA -> {
                ColorFilter.tint(AppTheme.colors.tertiary05)
            }

            else -> {
                ColorFilter.tint(AppTheme.colors.gradientThreeStart)
            }
        }
    Column(modifier = modifier) {
        if (uiModel.imageUrl?.isNotEmpty() == true) {
            GlideImage(
                model = uiModel.imageUrl,
                contentDescription = uiModel.type.name,
                contentScale = ContentScale.Fit,
            )
        } else {
            Image(
                painter = painterResource(id = uiModel.type.getImageRes()),
                contentDescription = uiModel.type.name,
                colorFilter = imageTintColor,
                modifier =
                    Modifier
                        .width(uiModel.type.getDimen())
                        .height(uiModel.type.getDimen())
                        .align(Alignment.CenterHorizontally),
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        OASubHeadLine1TextView(
            text = uiModel.contentHeader,
            color = AppTheme.colors.tertiary03,
            textAlign = TextAlign.Center,
            modifier =
                Modifier
                    .padding(all = 8.dp)
                    .align(Alignment.CenterHorizontally),
        )

        Spacer(modifier = Modifier.height(16.dp))

        OACallOut1TextView(
            text = uiModel.messageContent,
            color = AppTheme.colors.tertiary05,
            textAlign = TextAlign.Justify,
            modifier = Modifier.padding(horizontal = 24.dp),
        )

        Spacer(modifier = Modifier.height(16.dp))
    }
}

private fun AnnouncementType.getImageRes() =
    when (this) {
        AnnouncementType.SUBSCRIPTION -> {
            R.drawable.ic_exclamatory
        }

        AnnouncementType.OTA -> {
            R.drawable.ic_remove
        }

        else -> {
            R.drawable.ic_engine
        }
    }

private fun AnnouncementType.getDimen() =
    when (this) {
        AnnouncementType.SUBSCRIPTION -> {
            48.dp
        }

        else -> {
            120.dp
        }
    }

@Composable
private fun AnnouncementDetailButtons(
    uiModel: AnnouncementDetailUIModel,
    viewModel: AnnouncementDetailViewModel,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier =
            modifier
                .wrapContentSize()
                .padding(vertical = 8.dp),
    ) {
        Button(
            onClick = {
                viewModel.handleDismissCTAAction(uiModel)
            },
            modifier =
                Modifier
                    .height(40.dp)
                    .align(Alignment.CenterHorizontally)
                    .testTagID(AccessibilityId.ID_ANNOUNCEMENT_DISMISS_CTA),
            colors =
                ButtonDefaults.buttonColors(
                    backgroundColor = AppTheme.colors.tertiary15,
                    contentColor = AppTheme.colors.button02a,
                ),
            elevation =
                ButtonDefaults.elevation(
                    defaultElevation = 0.dp,
                    disabledElevation = 0.dp,
                ),
        ) {
            OAButtonTextView(
                text = uiModel.dismissTextButton,
                color = AppTheme.colors.button02a,
            )
        }

        PrimaryButton02(
            text = uiModel.primaryButton,
            modifier =
                Modifier
                    .align(Alignment.CenterHorizontally)
                    .testTagID(AccessibilityId.ID_ANNOUNCEMENT_DETAIL_PRIMARY_BUTTON),
        ) {
            viewModel.handlePrimaryCTANavigation(
                uiModel = uiModel,
            )
        }

        uiModel.secondaryButton?.let {
            if (it.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))

                PrimaryButton02(
                    text = it,
                    modifier =
                        Modifier
                            .align(Alignment.CenterHorizontally)
                            .testTagID(AccessibilityId.ID_ANNOUNCEMENT_DETAIL_SECONDARY_BUTTON),
                ) {
                    viewModel.updateOTA()
                }
            }
        }
    }
}

private fun showBottomSheetDialog(
    coroutineScope: CoroutineScope,
    modalBottomSheetState: ModalBottomSheetState,
) {
    coroutineScope.launch {
        if (!modalBottomSheetState.isVisible) {
            modalBottomSheetState.show()
        }
    }
}
