/*
 *  Created by sudhan.ram on 29/08/24, 5:39 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.repo

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementsResponse
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse

interface AnnouncementDetailRepository {
    suspend fun dismissAnnouncement(
        vin: String,
        messageCategory: String,
        messageId: String,
    ): Resource<BaseResponse?>

    suspend fun updateOTA(
        brand: String,
        vin: String,
    ): Resource<BaseResponse?>

    suspend fun getVehicleAlert(
        vin: String,
        brand: String,
        region: String,
        includeRenewal: String,
    ): Resource<AnnouncementsResponse?>
}
