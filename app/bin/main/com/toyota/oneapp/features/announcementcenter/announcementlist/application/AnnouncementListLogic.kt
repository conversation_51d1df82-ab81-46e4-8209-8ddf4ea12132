/*
 *  Created by sudhan.ram on 29/08/24, 5:41 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 5:30 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementlist.application

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementPayloadResponse
import com.toyota.oneapp.features.announcementcenter.announcementlist.domain.AnnouncementListUIModel
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class AnnouncementListLogic
    @Inject
    constructor(
        private val applicationData: ApplicationData,
    ) : AnnouncementListUseCase {
        companion object {
            private const val ID_PREFIX = "ID_TAB_"
        }

        override fun loadAnnouncementTabs(announcements: List<AnnouncementPayloadResponse>) =
            flow {
                val announcementTabs = mutableListOf<AnnouncementListUIModel>()
                applicationData.getVehicleList()?.forEach { vehicleInfo ->
                    announcementTabs.add(
                        AnnouncementListUIModel(
                            tabId = "$ID_PREFIX${vehicleInfo.modelName}",
                            tabTitle = vehicleInfo.modelName,
                            vehicleInfo = vehicleInfo,
                            messages =
                                announcements.filter { it.vin == vehicleInfo.vin }.run {
                                    if (isNotEmpty()) {
                                        first().messages
                                    } else {
                                        emptyList()
                                    }
                                },
                        ),
                    )
                }
                emit(announcementTabs)
            }
    }
