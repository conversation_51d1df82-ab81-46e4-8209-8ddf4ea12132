/*
 *  Created by sudhan.ram on 29/08/24, 5:38 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.di

import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.AnnouncementDetailLogic
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.AnnouncementDetailUseCase
import com.toyota.oneapp.features.announcementcenter.announcementdetail.dataaccess.repository.AnnouncementDetailDefaultRepo
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.repo.AnnouncementDetailRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class AnnouncementDetailModule {
    @Binds
    abstract fun bindAnnouncementDetailRepo(repo: AnnouncementDetailDefaultRepo): AnnouncementDetailRepository

    @Binds
    abstract fun bindAnnouncementDetailUseCase(logic: AnnouncementDetailLogic): AnnouncementDetailUseCase
}
