/*
 *  Created by sudhan.ram on 29/08/24, 5:37 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.application

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementMessageResponse
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementPayloadResponse
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model.AnnouncementDetailUIModel
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.coroutines.flow.Flow

interface AnnouncementDetailUseCase {
    fun loadAnnounceDetails(messageResponse: AnnouncementMessageResponse): Flow<AnnouncementDetailUIModel>

    fun dismissAnnouncement(
        vin: String,
        messageCategory: String,
        messageId: String,
    ): Flow<Resource<BaseResponse?>>

    fun updateOTA(
        brand: String,
        vin: String,
    ): Flow<Resource<BaseResponse?>>

    fun getUpdatedVehicleAlert(vehicleInfo: VehicleInfo): Flow<AnnouncementPayloadResponse?>
}
