/*
 *  Created by su<PERSON>n.ram on 29/08/24, 5:42 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.widgets

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId

@Composable
fun AnnouncementCenterBottomSheet(
    title: String,
    modifier: Modifier = Modifier,
    onBack: () -> Unit,
    content: @Composable () -> Unit,
) {
    Column(
        verticalArrangement = Arrangement.SpaceBetween,
        modifier =
            modifier
                .fillMaxSize()
                .background(AppTheme.colors.tertiary12),
    ) {
        Spacer(modifier = Modifier.height(8.dp))

        Image(
            painter = painterResource(id = R.drawable.ic_drag_indicator),
            contentDescription = stringResource(id = R.string.content_drag),
            modifier =
                Modifier
                    .width(28.dp)
                    .height(4.dp)
                    .align(Alignment.CenterHorizontally),
            colorFilter = ColorFilter.tint(AppTheme.colors.outline01),
        )

        Spacer(modifier = Modifier.height(8.dp))

        AnnouncementsAppBar(
            title = title,
            modifier =
                Modifier
                    .padding(horizontal = 16.dp),
        ) {
            onBack()
        }

        content()
    }
}

@Composable
fun AnnouncementsAppBar(
    title: String,
    modifier: Modifier = Modifier,
    onBack: () -> Unit,
) {
    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Box(
            modifier =
                Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(AppTheme.colors.button02d)
                    .testTagID(AccessibilityId.ID_ANNOUNCEMENT_DETAIL_BACK_BUTTON)
                    .clickable {
                        onBack()
                    },
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_back_arrow),
                contentDescription = stringResource(id = R.string.Common_back),
                modifier =
                    Modifier
                        .padding(
                            start = 19.dp,
                            end = 22.dp,
                            top = 17.dp,
                            bottom = 17.dp,
                        ),
                tint = AppTheme.colors.button02a,
            )
        }

        OASubHeadLine3TextView(
            text = title,
            color = AppTheme.colors.tertiary03,
            maxLines = 1,
            modifier =
                Modifier
                    .padding(
                        top = 6.dp,
                        start = if (title.length > 26) 48.dp else 0.dp,
                    )
                    .align(Alignment.TopCenter),
        )
    }
}
