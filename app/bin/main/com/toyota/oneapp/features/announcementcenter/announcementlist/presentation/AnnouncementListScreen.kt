/*
 *  Created by su<PERSON>n.ram on 29/08/24, 5:42 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementlist.presentation

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.ScrollableTabRow
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.google.accompanist.pager.ExperimentalPagerApi
import com.google.accompanist.pager.HorizontalPager
import com.google.accompanist.pager.rememberPagerState
import com.toyota.oneapp.R
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementMessageResponse
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementPayloadResponse
import com.toyota.oneapp.features.announcementcenter.announcementdetail.presentation.AnnouncementDetailScreen
import com.toyota.oneapp.features.announcementcenter.announcementlist.domain.AnnouncementListUIModel
import com.toyota.oneapp.features.announcementcenter.widgets.AnnouncementCenterBottomSheet
import com.toyota.oneapp.features.core.composable.OATabButton
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class, ExperimentalPagerApi::class)
@Composable
fun AnnouncementListScreen(
    bottomSheetState: ModalBottomSheetState,
    announcementPayloads: List<AnnouncementPayloadResponse>,
    modifier: Modifier = Modifier,
    viewModel: AnnouncementListViewModel = hiltViewModel(),
) {
    val coroutineScope = rememberCoroutineScope()
    val announcementDetailSheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            skipHalfExpanded = true,
        )
    var vehicleInfo: VehicleInfo? by remember { mutableStateOf(null) }
    var messageResponse: AnnouncementMessageResponse? by remember { mutableStateOf(null) }

    val tabWidgetsState by viewModel.tabWidgetsState.collectAsState()

    viewModel.initAnnouncementCenter(announcementPayloads)

    BackHandler(enabled = (bottomSheetState.isVisible)) {
        coroutineScope.launch {
            bottomSheetState.hide()
        }
    }

    Box(modifier = modifier.fillMaxSize()) {
        ModalBottomSheetLayout(
            sheetState = announcementDetailSheetState,
            sheetContent = {
                Box(modifier = Modifier.defaultMinSize(minHeight = 1.dp)) {
                    AnnouncementDetailScreen(
                        bottomSheetState = announcementDetailSheetState,
                        vehicleInfo = vehicleInfo,
                        messageResponse = messageResponse,
                    )
                }
            },
        ) {
            AnnouncementCenterBottomSheet(
                title = stringResource(id = R.string.announcements),
                onBack = {
                    coroutineScope.launch {
                        if (bottomSheetState.isVisible) {
                            bottomSheetState.hide()
                        }
                    }
                },
            ) {
                tabWidgetsState?.let { tabList ->
                    AnnouncementsScrollableTabContent(
                        tabList = tabList,
                    ) { vehicle, announcementMessageResponse ->
                        vehicleInfo = vehicle
                        messageResponse = announcementMessageResponse

                        coroutineScope.launch {
                            if (!announcementDetailSheetState.isVisible) {
                                announcementDetailSheetState.show()
                            }
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalPagerApi::class)
@Composable
private fun AnnouncementsScrollableTabContent(
    tabList: List<AnnouncementListUIModel>,
    modifier: Modifier = Modifier,
    onShowDetailBottomSheet: (VehicleInfo, AnnouncementMessageResponse) -> Unit,
) {
    val pagerState = rememberPagerState(initialPage = 0)
    val coroutineScope = rememberCoroutineScope()

    Column(modifier = modifier) {
        ScrollableTabRow(
            selectedTabIndex = pagerState.currentPage,
            backgroundColor = AppTheme.colors.tertiary12,
            indicator = {},
            divider = {},
            modifier = Modifier.padding(vertical = 8.dp),
        ) {
            tabList.forEachIndexed { index, announcementCenterTab ->
                OATabButton(
                    accessibilityID = announcementCenterTab.tabId,
                    text = announcementCenterTab.tabTitle,
                    isSelected = pagerState.currentPage == index,
                ) {
                    coroutineScope.launch {
                        pagerState.animateScrollToPage(index)
                    }
                }
            }
        }

        HorizontalPager(
            count = tabList.size,
            state = pagerState,
            modifier = Modifier.fillMaxWidth(),
        ) {
            AnnouncementListViewPagerLayout(uiModel = tabList[pagerState.currentPage]) {
                onShowDetailBottomSheet(
                    tabList[pagerState.currentPage].vehicleInfo,
                    it,
                )
            }
        }
    }
}
