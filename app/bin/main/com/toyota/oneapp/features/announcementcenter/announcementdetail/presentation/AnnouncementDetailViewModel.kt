/*
 *  Created by su<PERSON>n.ram on 29/08/24, 5:39 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementMessageResponse
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.AnnouncementDetailLogic.Companion.CTA_ADD_SUBSCRIPTION
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.AnnouncementDetailLogic.Companion.CTA_DEALER
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.AnnouncementDetailLogic.Companion.CTA_OTA
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.AnnouncementDetailLogic.Companion.CTA_PHONE
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.AnnouncementDetailLogic.Companion.STATUS_COMPLETED
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.AnnouncementDetailLogic.Companion.STATUS_FAILED
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.AnnouncementDetailLogic.Companion.STATUS_SCHEDULED
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.AnnouncementDetailUseCase
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.NavigationState
import com.toyota.oneapp.features.announcementcenter.announcementdetail.application.ResultState
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model.AnnouncementDetailUIModel
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class AnnouncementDetailViewModel
    @Inject
    constructor(
        private val announcementDetailUseCase: AnnouncementDetailUseCase,
        private val dispatcherProvider: DispatcherProvider,
    ) : BaseViewModel() {
        private val _resultState =
            MutableStateFlow<ResultState>(
                value = ResultState.Idle,
            )
        val resultState = _resultState.asStateFlow()

        private val _navigationState = MutableStateFlow<NavigationState?>(null)
        val navigationState = _navigationState.asStateFlow()

        var vehicleInfo: VehicleInfo? = null

        init {
            _resultState.value = ResultState.Init
        }

        fun updateAnnouncementDetailUiData(messageResponse: AnnouncementMessageResponse?) {
            messageResponse?.let { message ->
                viewModelScope.launch(dispatcherProvider.main()) {
                    announcementDetailUseCase.loadAnnounceDetails(message).collect {
                        _resultState.value = ResultState.LoadAnnouncementDetail(it)
                    }
                }
            }
        }

        fun dismissLoading() {
            _resultState.value = ResultState.Idle
        }

        /** Call this method to avoid showing the bottom sheet navigation to and fro
         to AnnouncementDetail screen **/
        fun resetNavigation() {
            _navigationState.value = NavigationState.Nothing
        }

        fun dismissAnnouncement(
            vehicleInfo: VehicleInfo?,
            messageResponse: AnnouncementMessageResponse,
        ) {
            viewModelScope.launch(dispatcherProvider.main()) {
                _resultState.value = ResultState.Loading
                announcementDetailUseCase
                    .dismissAnnouncement(
                        vin = vehicleInfo?.vin.orEmpty(),
                        messageCategory = messageResponse.messageCategory.orEmpty(),
                        messageId = messageResponse.messageId.orEmpty(),
                    ).flowOn(dispatcherProvider.io())
                    .collect { resource ->
                        when (resource) {
                            is Resource.Success -> {
                                resource.data?.let {
                                    _resultState.value = ResultState.DismissAnnouncementSuccess(true)
                                }
                            }

                            is Resource.Failure -> {
                                _resultState.value =
                                    ResultState.Error(
                                        errorCode = resource.error?.responseCode,
                                        errorMessage = resource.message,
                                    )
                            }

                            else -> {
                                // Do nothing }
                            }
                        }
                    }
            }
        }

        fun updateOTA() {
            viewModelScope.launch(dispatcherProvider.main()) {
                _resultState.value = ResultState.Loading
                announcementDetailUseCase
                    .updateOTA(
                        brand = vehicleInfo?.brand.orEmpty(),
                        vin = vehicleInfo?.vin.orEmpty(),
                    ).flowOn(dispatcherProvider.io())
                    .collect { resource ->
                        when (resource) {
                            is Resource.Success -> {
                                _resultState.value = ResultState.UpdateOTASuccess(true)
                            }

                            is Resource.Failure -> {
                                _resultState.value =
                                    ResultState.Error(
                                        errorCode = resource.error?.responseCode,
                                        errorMessage = resource.message,
                                    )
                            }

                            else -> {
                                // Do nothing
                            }
                        }
                    }
            }
        }

        fun getUpdatedAnnouncement(vehicleInfo: VehicleInfo?) {
            viewModelScope.launch(dispatcherProvider.main()) {
                _resultState.value = ResultState.Loading
                vehicleInfo?.let { vehicle ->
                    announcementDetailUseCase
                        .getUpdatedVehicleAlert(vehicle)
                        .flowOn(dispatcherProvider.io())
                        .collect { payload ->
                            if (payload != null) {
                                _resultState.value = ResultState.RefreshUI(payload.messages.first())
                            } else {
                                _resultState.value = ResultState.Error()
                            }
                        }
                }
            }
        }

        fun handleDismissCTAAction(uiModel: AnnouncementDetailUIModel) {
            _navigationState.value =
                NavigationState.LaunchDismissBottomSheet(
                    dialogUIModel = uiModel.dialogUIModel,
                    messageResponse = uiModel.messageResponse,
                )
        }

        fun handlePrimaryCTANavigation(uiModel: AnnouncementDetailUIModel) {
            uiModel.messageResponse.apply {
                when (cta) {
                    CTA_OTA -> {
                        if (status == STATUS_SCHEDULED || status == STATUS_COMPLETED || status == STATUS_FAILED) {
                            _navigationState.value = NavigationState.RedirectToDashboard
                        } else {
                            updateOTA()
                        }
                    }
                    CTA_DEALER -> {
                        _navigationState.value = NavigationState.NavigateToScheduleService
                    }
                    CTA_PHONE -> {
                        _navigationState.value = NavigationState.LaunchPhoneDialer(phone.orEmpty())
                    }
                    CTA_ADD_SUBSCRIPTION -> {
                        _navigationState.value = NavigationState.NavigateToSubscriptions
                    }
                    else -> {
                        // Do nothing
                    }
                }
            }
        }
    }
