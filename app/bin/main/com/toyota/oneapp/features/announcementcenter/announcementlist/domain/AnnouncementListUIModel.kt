/*
 *  Created by sudhan.ram on 29/08/24, 5:41 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementlist.domain

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementMessageResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo

data class AnnouncementListUIModel(
    val tabId: String,
    val tabTitle: String,
    val vehicleInfo: VehicleInfo,
    val messages: List<AnnouncementMessageResponse>,
)
