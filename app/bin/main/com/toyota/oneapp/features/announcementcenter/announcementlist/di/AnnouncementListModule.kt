/*
 *  Created by sudhan.ram on 29/08/24, 5:41 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementlist.di

import com.toyota.oneapp.features.announcementcenter.announcementlist.application.AnnouncementListLogic
import com.toyota.oneapp.features.announcementcenter.announcementlist.application.AnnouncementListUseCase
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class AnnouncementListModule {
    @Binds
    abstract fun bindAnnouncementCenterUseCase(logic: AnnouncementListLogic): AnnouncementListUseCase
}
