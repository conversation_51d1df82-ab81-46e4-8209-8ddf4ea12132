/*
 *  Created by su<PERSON>n.ram on 29/08/24, 5:39 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model.DismissDialogUIModel
import com.toyota.oneapp.features.core.composable.OAButtonTextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun DismissAnnouncementDialog(
    bottomSheetState: ModalBottomSheetState,
    uiModel: DismissDialogUIModel,
    modifier: Modifier = Modifier,
    onConfirmClicked: () -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()

    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .background(AppTheme.colors.tertiary15)
                .padding(all = 16.dp),
    ) {
        Surface(
            shape = CircleShape,
            color = AppTheme.colors.primary02,
            modifier =
                Modifier
                    .size(72.dp)
                    .padding(12.dp)
                    .align(Alignment.CenterHorizontally),
        ) {
            Image(
                modifier =
                    Modifier
                        .padding(12.dp),
                painter = painterResource(id = R.drawable.ic_car),
                colorFilter = ColorFilter.tint(AppTheme.colors.primary01),
                contentDescription = stringResource(id = R.string.announcements),
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        OACallOut1TextView(
            text = uiModel.alertMessage,
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary05,
        )

        Spacer(modifier = Modifier.height(24.dp))

        Button(
            onClick = {
                coroutineScope.launch {
                    if (bottomSheetState.isVisible) {
                        bottomSheetState.hide()
                    }
                }
            },
            modifier =
                Modifier
                    .height(40.dp)
                    .align(Alignment.CenterHorizontally)
                    .testTagID(AccessibilityId.ID_DISMISS_DIALOG_SECONDARY_CTA),
            colors =
                ButtonDefaults.buttonColors(
                    backgroundColor = AppTheme.colors.tertiary15,
                    contentColor = AppTheme.colors.button02a,
                ),
            elevation =
                ButtonDefaults.elevation(
                    defaultElevation = 0.dp,
                    disabledElevation = 0.dp,
                ),
        ) {
            OAButtonTextView(
                text = uiModel.secondaryButtonText,
                color = AppTheme.colors.button02a,
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        PrimaryButton02(
            click = onConfirmClicked,
            text = uiModel.primaryButtonText,
            modifier =
                Modifier
                    .align(Alignment.CenterHorizontally)
                    .testTagID(AccessibilityId.ID_DISMISS_DIALOG_PRIMARY_BUTTON),
        )
    }
}
