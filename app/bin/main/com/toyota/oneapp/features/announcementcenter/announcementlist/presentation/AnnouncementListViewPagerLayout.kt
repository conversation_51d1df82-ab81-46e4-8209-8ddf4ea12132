/*
 *  Created by sudhan.ram on 29/08/24, 5:42 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementlist.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementMessageResponse
import com.toyota.oneapp.features.announcementcenter.announcementlist.domain.AnnouncementListUIModel
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun AnnouncementListViewPagerLayout(
    uiModel: AnnouncementListUIModel,
    modifier: Modifier = Modifier,
    onShowDetailBottomSheet: (message: AnnouncementMessageResponse) -> Unit,
) {
    if (uiModel.messages.isEmpty()) {
        EmptyAnnouncements()
    } else {
        LazyColumn(
            modifier =
                modifier
                    .fillMaxSize(),
        ) {
            items(uiModel.messages) {
                MessageCard(
                    message = it,
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(all = 16.dp)
                            .testTagID(it.cardTitle.orEmpty()),
                ) {
                    onShowDetailBottomSheet(it)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun MessageCard(
    message: AnnouncementMessageResponse,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Card(
        backgroundColor = AppTheme.colors.tile03,
        shape = RoundedCornerShape(8.dp),
        elevation = 4.dp,
        modifier = modifier,
        onClick = onClick,
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(all = 16.dp),
        ) {
            Row {
                Image(
                    painter = painterResource(id = R.drawable.ic_announcement_center),
                    contentDescription = stringResource(id = R.string.announcements),
                    modifier =
                        Modifier
                            .width(48.dp)
                            .height(48.dp),
                )

                Spacer(modifier = Modifier.width(16.dp))

                OABody4TextView(
                    text = message.cardTitle.orEmpty(),
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .align(Alignment.CenterVertically),
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            OACallOut1TextView(
                text = message.cardMessage.orEmpty(),
                color = AppTheme.colors.tertiary05,
            )
        }
    }
}

@Composable
fun EmptyAnnouncements(modifier: Modifier = Modifier) {
    Box(
        modifier =
            modifier
                .fillMaxSize(),
    ) {
        OACallOut1TextView(
            text = stringResource(id = R.string.no_announcements),
            color = AppTheme.colors.tertiary05,
            modifier =
                Modifier
                    .align(Alignment.Center),
        )
    }
}
