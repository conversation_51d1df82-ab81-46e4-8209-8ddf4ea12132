/*
 *  Created by sudhan.ram on 29/08/24, 5:37 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.application

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementMessageResponse
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model.AnnouncementDetailUIModel
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model.DismissDialogUIModel

sealed class ResultState {
    object Idle : ResultState()

    object Loading : ResultState()

    object Init : ResultState()

    class LoadAnnouncementDetail(
        val uiModel: AnnouncementDetailUIModel,
    ) : ResultState()

    class DismissAnnouncementSuccess(
        val isAcknowledged: Boolean,
    ) : ResultState()

    class UpdateOTASuccess(
        val isOTAUpdated: Boolean,
    ) : ResultState()

    class RefreshUI(
        val messageResponse: AnnouncementMessageResponse?,
    ) : ResultState()

    class Error(
        val errorCode: String? = null,
        val errorMessage: String? = null,
    ) : ResultState()
}

sealed class NavigationState {
    object Nothing : NavigationState()

    class LaunchDismissBottomSheet(
        val dialogUIModel: DismissDialogUIModel,
        val messageResponse: AnnouncementMessageResponse,
    ) : NavigationState()

    object RedirectToDashboard : NavigationState()

    class LaunchPhoneDialer(
        val phoneNumber: String,
    ) : NavigationState()

    object NavigateToScheduleService : NavigationState()

    object NavigateToSubscriptions : NavigationState()
}
