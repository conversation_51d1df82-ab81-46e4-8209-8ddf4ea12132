/*
 *  Created by sudhan.ram on 29/08/24, 5:38 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 12:54 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.dataaccess.repository

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementsResponse
import com.toyota.oneapp.features.announcementcenter.announcementdetail.dataaccess.service.AnnouncementDetailAPI
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.repo.AnnouncementDetailRepository
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class AnnouncementDetailDefaultRepo
    @Inject
    constructor(
        val service: AnnouncementDetailAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext),
        AnnouncementDetailRepository {
        override suspend fun dismissAnnouncement(
            vin: String,
            messageCategory: String,
            messageId: String,
        ): Resource<BaseResponse?> =
            makeApiCall {
                service.dismissAnnouncement(vin, messageCategory, messageId)
            }

        override suspend fun updateOTA(
            brand: String,
            vin: String,
        ): Resource<BaseResponse?> =
            makeApiCall {
                service.updateOTA(brand, vin)
            }

        override suspend fun getVehicleAlert(
            vin: String,
            brand: String,
            region: String,
            includeRenewal: String,
        ): Resource<AnnouncementsResponse?> =
            makeApiCall {
                service.getVehicleAlert(
                    vin = vin,
                    brand = brand,
                    region = region,
                    includeRenewal = includeRenewal,
                )
            }
    }
