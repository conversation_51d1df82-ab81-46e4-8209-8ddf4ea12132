/*
 *  Created by sudhan.ram on 29/08/24, 5:37 pm
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 29/08/24, 5:28 pm
 *
 */

package com.toyota.oneapp.features.announcementcenter.announcementdetail.application

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementMessageResponse
import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AnnouncementPayloadResponse
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model.AnnouncementDetailUIModel
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model.AnnouncementType
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.model.DismissDialogUIModel
import com.toyota.oneapp.features.announcementcenter.announcementdetail.domain.repo.AnnouncementDetailRepository
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class AnnouncementDetailLogic
    @Inject
    constructor(
        private val repository: AnnouncementDetailRepository,
    ) : AnnouncementDetailUseCase {
        companion object {
            const val CTA_DEALER = 1
            const val CTA_PHONE = 2
            const val CTA_ADD_SUBSCRIPTION = 3
            const val CTA_OTA = 4
            const val STATUS_SCHEDULED = 1
            const val STATUS_COMPLETED = 2
            const val STATUS_FAILED = 3
        }

        override fun loadAnnounceDetails(messageResponse: AnnouncementMessageResponse) =
            flow {
                val uiModel =
                    AnnouncementDetailUIModel(
                        type =
                            when {
                                messageResponse.cta == CTA_ADD_SUBSCRIPTION -> {
                                    AnnouncementType.SUBSCRIPTION
                                }
                                messageResponse.cta == CTA_OTA &&
                                    messageResponse.status == STATUS_FAILED -> {
                                    AnnouncementType.OTA
                                }
                                else -> {
                                    AnnouncementType.OTHERS
                                }
                            },
                        title = messageResponse.title.orEmpty(),
                        contentHeader = messageResponse.header.orEmpty(),
                        messageContent = messageResponse.body.orEmpty(),
                        imageUrl = messageResponse.image,
                        dismissTextButton = messageResponse.dismissText.orEmpty(),
                        primaryButton = messageResponse.ctaText.orEmpty(),
                        secondaryButton =
                            if (checkSecondaryButton(messageResponse)) {
                                messageResponse.secondaryCtaText
                            } else {
                                null
                            },
                        dialogUIModel =
                            DismissDialogUIModel(
                                alertMessage = messageResponse.alertMessage.orEmpty(),
                                primaryButtonText = messageResponse.alertPositiveButtonText.orEmpty(),
                                secondaryButtonText = messageResponse.alertNegativeButtonText.orEmpty(),
                            ),
                        messageResponse = messageResponse,
                    )

                emit(uiModel)
            }

        private fun checkSecondaryButton(messageResponse: AnnouncementMessageResponse): Boolean =
            messageResponse.run {
                cta == CTA_OTA && status == STATUS_FAILED
            }

        override fun dismissAnnouncement(
            vin: String,
            messageCategory: String,
            messageId: String,
        ): Flow<Resource<BaseResponse?>> =
            flow {
                val response = repository.dismissAnnouncement(vin, messageCategory, messageId)
                emit(response)
            }

        override fun updateOTA(
            brand: String,
            vin: String,
        ): Flow<Resource<BaseResponse?>> =
            flow {
                val response = repository.updateOTA(brand, vin)
                emit(response)
            }

        override fun getUpdatedVehicleAlert(vehicleInfo: VehicleInfo): Flow<AnnouncementPayloadResponse?> =
            flow {
                val response =
                    repository.getVehicleAlert(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        region = vehicleInfo.region,
                        includeRenewal = vehicleInfo.isSubscriptionExpirationStatus.toString(),
                    )

                emit(response.data?.payload)
            }
    }
