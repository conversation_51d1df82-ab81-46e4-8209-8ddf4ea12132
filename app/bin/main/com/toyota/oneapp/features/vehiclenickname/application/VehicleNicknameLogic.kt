package com.toyota.oneapp.features.vehiclenickname.application

import com.toyota.oneapp.features.vehiclenickname.dataacess.servermodel.UpdateNicknameRequest
import com.toyota.oneapp.features.vehiclenickname.domain.repo.VehicleNicknameRepo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class VehicleNicknameLogic
    @Inject
    constructor(
        private val repository: VehicleNicknameRepo,
    ) : VehicleNicknameUseCase {
        override suspend fun updateVehicleNicknameForCY17(
            nickName: String,
            guid: String,
            brand: String,
            vin: String,
        ): Flow<Resource<BaseResponse?>> {
            return flow {
                val requestBody = UpdateNicknameRequest(nickName, guid, vin)
                val response =
                    repository.updateNicknameForCY17(
                        brand = brand,
                        vin = vin,
                        requestBody = requestBody,
                    )

                emit(response)
            }
        }

        override suspend fun updateVehicleNicknameForCY17Plus(
            nickName: String,
            guid: String,
            brand: String,
            vin: String,
        ): Flow<Resource<BaseResponse?>> {
            return flow {
                val requestBody = UpdateNicknameRequest(nickName, guid, vin)
                val response =
                    repository.updateNicknameForCY17Plus(
                        brand = brand,
                        requestBody = requestBody,
                    )

                emit(response)
            }
        }
    }
