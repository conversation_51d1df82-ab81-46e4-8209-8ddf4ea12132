package com.toyota.oneapp.features.vehiclenickname.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.vehiclenickname.application.VehicleNicknameState
import com.toyota.oneapp.features.vehiclenickname.application.VehicleNicknameUseCase
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.dataprovider.NetworkDataProvider
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class VehicleNicknameViewModel
    @Inject
    constructor(
        private val analyticsLogger: AnalyticsLogger,
        private val vehicleNicknameUseCase: VehicleNicknameUseCase,
        val applicationData: ApplicationData,
        private val dispatcherProvider: DispatcherProvider,
        private val provider: NetworkDataProvider,
    ) : BaseViewModel() {
        private val _uiState =
            MutableStateFlow<VehicleNicknameState>(
                value = VehicleNicknameState.Idle,
            )
        val uiState = _uiState.asStateFlow()

        init {
            analyticsLogger.logEvent(AnalyticsEvent.VIEW_VEHICLE_NICKNAME)
        }

        fun updateNickname(updatedNickname: String) {
            viewModelScope.launch(dispatcherProvider.main()) {
                _uiState.emit(VehicleNicknameState.Loading)
                applicationData.getSelectedVehicle()?.apply {
                    if (isCY17) {
                        vehicleNicknameUseCase.updateVehicleNicknameForCY17(
                            nickName = updatedNickname,
                            guid = provider.guid(),
                            brand = brand,
                            vin = vin,
                        ).flowOn(dispatcherProvider.io())
                            .collect { resource ->
                                when (resource) {
                                    is Resource.Success -> {
                                        resource.data?.let {
                                            analyticsLogger.logEvent(AnalyticsEvent.EDIT_VEHICLE_NAME)
                                            applicationData.getSelectedVehicle()?.let {
                                                it.nickName = updatedNickname
                                                applicationData.setSelectedVehicleNickName(it.nickName)
                                                _uiState.value = (
                                                    VehicleNicknameState.UpdateNicknameForCY17Success(
                                                        it,
                                                    )
                                                )
                                            }
                                        }
                                    }
                                    is Resource.Failure -> {
                                        _uiState.value = (
                                            VehicleNicknameState.Error(
                                                resource.error?.responseCode,
                                                resource.message,
                                            )
                                        )
                                    }
                                    else -> {}
                                }
                            }
                    } else {
                        vehicleNicknameUseCase.updateVehicleNicknameForCY17Plus(
                            nickName = updatedNickname,
                            guid = provider.guid(),
                            brand = brand,
                            vin = vin,
                        ).flowOn(dispatcherProvider.io())
                            .collect { resource ->
                                when (resource) {
                                    is Resource.Success -> {
                                        resource.data?.let {
                                            applicationData.getSelectedVehicle()?.let {
                                                it.nickName = updatedNickname
                                                applicationData.setSelectedVehicleNickName(it.nickName)
                                                _uiState.emit(
                                                    VehicleNicknameState.UpdateNicknameForCY17PlusSuccess(
                                                        it,
                                                    ),
                                                )
                                            }
                                        }
                                    }
                                    is Resource.Failure -> {
                                        _uiState.value = (
                                            VehicleNicknameState.Error(
                                                resource.error?.responseCode,
                                                resource.message,
                                            )
                                        )
                                    }
                                    else -> {}
                                }
                            }
                    }
                }
            }
        }

        fun onResultConsumed() {
            _uiState.value = VehicleNicknameState.Idle
        }
    }
