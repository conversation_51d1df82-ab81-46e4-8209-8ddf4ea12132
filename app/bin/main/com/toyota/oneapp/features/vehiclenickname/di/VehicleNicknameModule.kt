package com.toyota.oneapp.features.vehiclenickname.di

import com.toyota.oneapp.features.vehiclenickname.application.VehicleNicknameLogic
import com.toyota.oneapp.features.vehiclenickname.application.VehicleNicknameUseCase
import com.toyota.oneapp.features.vehiclenickname.dataacess.repository.VehicleNicknameDefaultRepo
import com.toyota.oneapp.features.vehiclenickname.domain.repo.VehicleNicknameRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class VehicleNicknameModule {
    @Binds
    abstract fun bindVehicleNicknameRepo(vehicleNicknameDefaultRepo: VehicleNicknameDefaultRepo): VehicleNicknameRepo

    @Binds
    abstract fun bindVehicleNicknameUseCase(vehicleNicknameLogic: VehicleNicknameLogic): VehicleNicknameUseCase
}
