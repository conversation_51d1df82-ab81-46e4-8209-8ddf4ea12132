package com.toyota.oneapp.features.vehiclenickname.presentation

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.Constants.MAX_NICKNAME_CHARACTER_ALLOWED
import com.toyota.oneapp.features.vehiclenickname.application.VehicleNicknameState

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun VehicleNicknameScreen(
    navHostController: NavHostController,
    viewModel: VehicleNicknameViewModel = hiltViewModel(),
) {
    val vehicleInfo = viewModel.applicationData.getSelectedVehicle()

    val uiState = viewModel.uiState.collectAsState().value
    when (uiState) {
        is VehicleNicknameState.Loading -> {
            ShowProgressIndicator(
                true,
            )
        }
        is VehicleNicknameState.UpdateNicknameForCY17Success,
        is VehicleNicknameState.UpdateNicknameForCY17PlusSuccess,
        -> {
            navHostController.popBackStack()
            viewModel.onResultConsumed()
        }
        is VehicleNicknameState.Error -> {
            // TODO handle error
        }
        else -> {}
    }

    val inputValue =
        vehicleInfo?.nickName?.let {
            if (it.isEmpty()) {
                remember { mutableStateOf(TextFieldValue()) }
            } else {
                remember { mutableStateOf(TextFieldValue(it)) }
            }
        } ?: remember { mutableStateOf(TextFieldValue("")) }

    Scaffold {
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(AppTheme.colors.tertiary15)
                    .padding(16.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight(),
            ) {
                Surface(
                    shape = CircleShape,
                    color = AppTheme.colors.button02d,
                    modifier =
                        Modifier
                            .size(48.dp)
                            .clickable {
                                navHostController.popBackStack()
                            },
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_circle_close),
                        contentDescription = null,
                        modifier =
                            Modifier
                                .layoutId(AccessibilityId.ID_CLOSE_NICKNAME)
                                .padding(12.dp),
                        tint = AppTheme.colors.button02a,
                    )
                }

                Text(
                    text = stringResource(id = R.string.vehicle_nickname_title),
                    textAlign = TextAlign.Center,
                    style = AppTheme.fontStyles.subHeadline3,
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .wrapContentHeight()
                            .align(Alignment.Center)
                            .layoutId(AccessibilityId.ID_UPDATE_NICKNAME_TITLE),
                )
            }

            Spacer(modifier = Modifier.height(40.dp))

            viewModel.applicationData.getSelectedVehicle()?.let { vehicleInfo ->
                Text(
                    text = vehicleInfo.modelName,
                    style = AppTheme.fontStyles.headline1,
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .align(Alignment.CenterHorizontally)
                            .layoutId(AccessibilityId.ID_MODEL_NAME),
                )
            }

            OutlinedTextField(
                shape = RoundedCornerShape(8.dp),
                value = inputValue.value,
                onValueChange = {
                    if (it.text.length <= MAX_NICKNAME_CHARACTER_ALLOWED) {
                        inputValue.value = it
                    } else {
                        inputValue.value = TextFieldValue(it.text.take(MAX_NICKNAME_CHARACTER_ALLOWED))
                    }
                },
                placeholder = {
                    Text(
                        text = stringResource(id = R.string.enter_nickname),
                        style = AppTheme.fontStyles.body3,
                        color = AppTheme.colors.tertiary07,
                    )
                },
                colors =
                    TextFieldDefaults.outlinedTextFieldColors(
                        textColor = AppTheme.colors.tertiary03,
                        unfocusedBorderColor = AppTheme.colors.tertiary10,
                        focusedBorderColor = AppTheme.colors.tertiary10,
                    ),
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(16.dp)
                        .testTagID(AccessibilityId.ID_VEHICLE_NICKNAME_EDIT_TEXT_FIELD),
            )

            LazyColumn(modifier = Modifier.weight(1f)) {}

            Button(
                shape = RoundedCornerShape(48.dp),
                enabled = inputValue.value.text.isNotEmpty(),
                colors =
                    ButtonDefaults.buttonColors(
                        contentColor = AppTheme.colors.primaryButton01,
                        backgroundColor = AppTheme.colors.primaryButton02,
                    ),
                contentPadding = PaddingValues(vertical = 16.dp, horizontal = 60.dp),
                modifier =
                    Modifier
                        .align(Alignment.CenterHorizontally)
                        .layoutId(AccessibilityId.ID_SAVE_NICKNAME),
                onClick = {
                    viewModel.updateNickname(inputValue.value.text.trim())
                },
            ) {
                Text(
                    text = stringResource(id = R.string.Common_save),
                    style = AppTheme.fontStyles.buttonLink1,
                )
            }
        }
    }
}
