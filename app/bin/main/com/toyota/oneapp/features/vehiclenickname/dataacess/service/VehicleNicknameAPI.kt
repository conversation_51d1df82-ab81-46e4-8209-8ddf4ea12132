package com.toyota.oneapp.features.vehiclenickname.dataacess.service

import com.toyota.oneapp.features.vehiclenickname.dataacess.servermodel.UpdateNicknameRequest
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.PUT

interface VehicleNicknameAPI {
    @Headers("Content-Type: application/json")
    @PUT("/oneapi/v1/legacy/oneaccount/vehicles")
    suspend fun updateVehicleNicknameCY17(
        @Header("X-BRAND") brand: String,
        @Header("VIN") vin: String,
        @Body body: UpdateNicknameRequest,
    ): Response<BaseResponse>

    @Headers("Content-Type: application/json", "Accept-Encoding: deflate")
    @PUT("/oneapi/v1/vehicle-association/vehicle")
    suspend fun updateVehicleNicknameCY17Plus(
        @Header("X-BRAND") brand: String,
        @Header("DATETIME") dateTime: Long,
        @Body body: UpdateNicknameRequest,
    ): Response<BaseResponse>
}
