package com.toyota.oneapp.features.vehiclenickname.domain.repo

import com.toyota.oneapp.features.vehiclenickname.dataacess.servermodel.UpdateNicknameRequest
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse

interface VehicleNicknameRepo {
    suspend fun updateNicknameForCY17(
        brand: String,
        vin: String,
        requestBody: UpdateNicknameRequest,
    ): Resource<BaseResponse?>

    suspend fun updateNicknameForCY17Plus(
        brand: String,
        requestBody: UpdateNicknameRequest,
    ): Resource<BaseResponse?>
}
