package com.toyota.oneapp.features.vehiclenickname.application

import com.toyota.oneapp.model.vehicle.VehicleInfo

sealed class VehicleNicknameState {
    object Loading : VehicleNicknameState()

    class UpdateNicknameForCY17Success(val data: VehicleInfo) : VehicleNicknameState()

    class UpdateNicknameForCY17PlusSuccess(val data: VehicleInfo) : VehicleNicknameState()

    class Error(val errorCode: String?, val errorMessage: String?) : VehicleNicknameState()

    object Idle : VehicleNicknameState()
}
