package com.toyota.oneapp.features.vehiclenickname.application

import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.coroutines.flow.Flow

interface VehicleNicknameUseCase {
    suspend fun updateVehicleNicknameForCY17(
        nickName: String,
        guid: String,
        brand: String,
        vin: String,
    ): Flow<Resource<BaseResponse?>>

    suspend fun updateVehicleNicknameForCY17Plus(
        nickName: String,
        guid: String,
        brand: String,
        vin: String,
    ): Flow<Resource<BaseResponse?>>
}
