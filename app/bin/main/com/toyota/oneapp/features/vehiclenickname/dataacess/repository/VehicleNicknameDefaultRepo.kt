package com.toyota.oneapp.features.vehiclenickname.dataacess.repository

import com.toyota.oneapp.features.vehiclenickname.dataacess.servermodel.UpdateNicknameRequest
import com.toyota.oneapp.features.vehiclenickname.dataacess.service.VehicleNicknameAPI
import com.toyota.oneapp.features.vehiclenickname.domain.repo.VehicleNicknameRepo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class VehicleNicknameDefaultRepo
    @Inject
    constructor(
        val service: VehicleNicknameAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), VehicleNicknameRepo {
        override suspend fun updateNicknameForCY17(
            brand: String,
            vin: String,
            requestBody: UpdateNicknameRequest,
        ): Resource<BaseResponse?> {
            return makeApiCall {
                service.updateVehicleNicknameCY17(
                    brand = brand,
                    vin = vin,
                    body = requestBody,
                )
            }
        }

        override suspend fun updateNicknameForCY17Plus(
            brand: String,
            requestBody: UpdateNicknameRequest,
        ): Resource<BaseResponse?> {
            return makeApiCall {
                service.updateVehicleNicknameCY17Plus(
                    brand = brand,
                    dateTime = System.currentTimeMillis(),
                    body = requestBody,
                )
            }
        }
    }
