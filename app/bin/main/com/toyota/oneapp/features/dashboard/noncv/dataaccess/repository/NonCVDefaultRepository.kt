package com.toyota.oneapp.features.dashboard.noncv.dataaccess.repository

import com.toyota.oneapp.features.dashboard.noncv.dataaccess.model.ServiceHistoryResponse
import com.toyota.oneapp.features.dashboard.noncv.dataaccess.service.NonCVApi
import com.toyota.oneapp.features.dashboard.noncv.domain.repository.NonCVRepository
import com.toyota.oneapp.features.find.domain.model.PreferredDealer
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class NonCVDefaultRepository
    @Inject
    constructor(
        val service: NonCVApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : NonCVRepository(errorParser, ioContext) {
        override suspend fun fetchPreferredDealer(
            brand: String,
            vin: String,
            region: String,
        ): Resource<PreferredDealer?> {
            return makeApiCall {
                service.fetchPreferredDealer(
                    vin = vin,
                    brand = brand,
                    region = region,
                )
            }
        }

        override suspend fun fetchServiceHistory(
            brand: String,
            vin: String,
        ): Resource<ServiceHistoryResponse?> {
            return makeApiCall {
                service.fetchServiceHistory(
                    vin = vin,
                    brand = brand,
                )
            }
        }
    }
