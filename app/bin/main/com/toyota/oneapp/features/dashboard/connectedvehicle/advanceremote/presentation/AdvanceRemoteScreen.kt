package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation

import android.annotation.SuppressLint
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.BottomSheetScaffoldState
import androidx.compose.material.Card
import androidx.compose.material.Scaffold
import androidx.compose.material.SnackbarHost
import androidx.compose.material.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.NavHostController
import com.google.gson.Gson
import com.toyota.oneapp.R
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application.AdvanceRemoteState
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteItem
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteProgress
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension.handleIntent
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.helper.AutoFixPopUpDialog
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.helper.RemoteComposableCircleItem
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.helper.RemoteProgressBar
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.application.DigitalKeyState
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyEntityState
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.ComposableDigitalKey
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DigitalKeyDownloadViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DigitalKeyWithRemoteCommandLayout
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DownloadDigitalKeyNotice
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.dashboard.util.DashboardConstants
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.guestdriver.presentation.GuestDriverLaunchWidget
import com.toyota.oneapp.features.vehiclestatus.presentation.OnLifecycleEvent
import com.toyota.oneapp.model.remote.DigitalKeyPushPayload
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.ToyotaConstants.Companion.DK_SILENT_SDK_PUSH_JSON
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import toyotaone.commonlib.toast.ToastUtil

@SuppressLint(
    "StateFlowValueCalledInComposition",
    "UnusedMaterialScaffoldPaddingParameter",
    "CoroutineCreationDuringComposition",
)
@Composable
fun AdvanceRemoteScreen(
    bottomSheetState: BottomSheetScaffoldState,
    isRemoteParkNoAdvanceRemote: Boolean,
    isRemoteStateActive: Boolean,
    digitalKeyStatus: State<DigitalKeyEntityState>,
    navHostController: NavHostController,
) {
    val viewModel = hiltViewModel<AdvanceRemoteViewModel>()

    val digitalKeyDownloadViewModel = hiltViewModel<DigitalKeyDownloadViewModel>()

    val remoteListState = viewModel.remoteCommandsState.collectAsState()

    val timerText = viewModel.timerState.collectAsState()

    val autoFixDialog = viewModel.autoFixDialog.collectAsState()

    val showToast = viewModel.showRemoteToast.collectAsState()

    val isGuestDriverAvailable = viewModel.guestDriver.collectAsState()

    val snackBarHostState = remember { SnackbarHostState() }

    val context = LocalContext.current

    val activity = LocalContext.current as FragmentActivity

    val isDigitalKeyExists = viewModel.isDkCapable.collectAsState()

    val guestDriverRemoteShare = viewModel.guestDriverRemoteShare.collectAsState()

    val coroutineScope = rememberCoroutineScope()

    val dashboardViewModel = (LocalContext.current as OADashboardActivity).dashboardViewModel

    val isRemoteOnlyUser = viewModel.remoteOnlyUser.collectAsState()

    val remoteProgress = viewModel.remoteCommandProgress.collectAsState().value

    LaunchedEffect(Unit) {
        digitalKeyDownloadViewModel.showRevokeToastEvent.collect {
            ToastUtil.show(
                context as Activity,
                context.getString(R.string.dk_luk_revoke_message),
                R.drawable.ic_toast_sign,
            )
        }
    }

    val broadcastReceiver =
        object : BroadcastReceiver() {
            override fun onReceive(
                context: Context?,
                intent: Intent?,
            ) {
                when (intent?.action) {
                    ToyotaConstants.REMOTE_SILENCE_ACTION -> {
                        intent.let { viewModel.handleIntent(it) }
                    }

                    DigitalMopKeyUtils.ACTION_DK_BLE_LINK_STATUS_CHANGE,
                    DigitalMopKeyUtils.ACTION_DK_DOWNLOAD_FINISH,
                    -> {
                        digitalKeyDownloadViewModel.provideDigitalKeyStatus()
                    }

                    DigitalMopKeyUtils.ACTION_SHARE_DK_SYNC -> {
                        digitalKeyDownloadViewModel.syncKeys()
                    }

                    ToyotaConstants.REMOTE_SILENT_DK_SDK_PUSH -> {
                        val payloadJson = intent.getStringExtra(DK_SILENT_SDK_PUSH_JSON) ?: return
                        val payload = Gson().fromJson(payloadJson, DigitalKeyPushPayload::class.java)
                        digitalKeyDownloadViewModel.onRevokePushReceived(payload)
                    }
                    ToyotaConstants.REMOTE_SILENT_PUSH_ACTION -> {
                        context?.let { dashboardViewModel.syncDigitalKeyStatus() }
                        if (digitalKeyDownloadViewModel.isSecondaryVehicle()) {
                            digitalKeyDownloadViewModel.removeSharedKeyCurrentVehicle()
                        }
                    }
                }
            }
        }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_CREATE -> {
                digitalKeyDownloadViewModel.digitalKeyInitialize(context)
            }

            Lifecycle.Event.ON_START -> {
                digitalKeyDownloadViewModel.provideDigitalKeyStatus()
                digitalKeyDownloadViewModel.registerReceiver(
                    context = context,
                    broadcastReceiver = broadcastReceiver,
                )
            }

            Lifecycle.Event.ON_STOP -> {
                digitalKeyDownloadViewModel.unregisterReceiver(
                    context = context,
                    broadcastReceiver = broadcastReceiver,
                )
            }

            else -> {}
        }
    }

    autoFixDialog.value.takeIf { it }?.let {
        AutoFixPopUpDialog(
            userResponse = { isUserAllowedAutoFix ->
                handleDialogResponse(
                    isUserAllowedAutoFix,
                    viewModel,
                    context,
                )
            },
        )
    }

    LaunchedEffect(Unit) {
        viewModel.remoteCommandsEventFlow.collectLatest { event ->
            navigateClimateScreen(event, navHostController, context)
        }
    }

    LaunchedEffect(Unit) {
        viewModel.isDkCommandExecuteFailed.collectLatest { isFailed ->
            isFailed.takeIf { it }?.let {
                ToastUtil.show(
                    activity,
                    context.getString(R.string.Notification_default_error),
                    R.drawable.toast_remove,
                )
            }
        }
    }

    LocalBroadcastManager.getInstance(context).registerReceiver(
        broadcastReceiver,
        IntentFilter(ToyotaConstants.REMOTE_SILENCE_ACTION),
    )

    (guestDriverRemoteShare.value && bottomSheetState.bottomSheetState.isExpanded).takeIf { it }?.also {
        coroutineScope.launch {
            bottomSheetState.bottomSheetState.collapse()
            dashboardViewModel.onRefresh()
        }
        viewModel.guestDriverRemoteShareFlow.value = false
    }

    showToast.value.takeIf { it.isNotEmpty() }?.let {
        val message = getToastMessage(showToast)

        Toast
            .makeText(
                LocalContext.current,
                message,
                Toast.LENGTH_SHORT,
            ).show()

        viewModel.showToastFlow.value = ToyotaConstants.EMPTY_STRING
    }

    Scaffold(
        snackbarHost = { SnackbarHost(hostState = snackBarHostState) },
        content = {
            when (remoteListState.value) {
                is AdvanceRemoteState.Loading ->
                    OASubHeadLine3TextView(
                        text = context.getString(R.string.Login_loading),
                        color = AppTheme.colors.tertiary03,
                        modifier = Modifier.padding(top = 60.dp),
                    )

                is AdvanceRemoteState.Success -> {
                    val filteredRemoteList =
                        remoteItems(
                            remoteListState,
                            isRemoteParkNoAdvanceRemote,
                        )
                    Column(
                        modifier =
                            Modifier
                                .wrapContentHeight()
                                .fillMaxWidth(),
                    ) {
                        // Evaluates if RemotePark is a vehicle feature but Advance Remote is
                        // not active otherwise check if Digital Key exists with the expected logic
                        val remoteNotActiveOrDigitalKeyExists = !isRemoteParkNoAdvanceRemote && isDigitalKeyExists.value

                        RemoteProgressBar(
                            remoteProgress =
                                RemoteProgress(
                                    title = remoteProgress.title,
                                    progressState = remoteProgress.progressState,
                                    isRemoteStop = remoteProgress.isRemoteStop,
                                ),
                        )

                        DigitalKeyWithRemoteCommandLayout(
                            digitalKeyEntityState = digitalKeyStatus.value,
                            activity = activity,
                            remoteList = filteredRemoteList as ArrayList<RemoteItem>,
                            viewModel = viewModel,
                            digitalKeyDownloadViewModel = digitalKeyDownloadViewModel,
                            isDigitalKeyExists = remoteNotActiveOrDigitalKeyExists,
                            timerText = timerText.value,
                            advanceRemoteBottomSheetScaffoldState = bottomSheetState,
                            isGuestDriverAvailable = isGuestDriverAvailable,
                        )
                    }
                }

                is AdvanceRemoteState.Error -> {
                    val error =
                        (remoteListState.value as AdvanceRemoteState.Error).errorMessage ?: context.getString(
                            R.string.no_data_available,
                        )
                    OASubHeadLine3TextView(
                        text = error,
                        color = AppTheme.colors.tertiary03,
                        modifier =
                            Modifier
                                .padding(top = 60.dp)
                                .fillMaxWidth(),
                        textAlign = TextAlign.Center,
                    )
                }

                is AdvanceRemoteState.NoRemoteCommands -> {
                    isRemoteOnlyUser.value.takeIf { it }?.let {
                        OABody4TextView(
                            text = stringResource(id = R.string.secondaryDriver),
                            color = AppTheme.colors.tertiary03,
                            textAlign = TextAlign.Center,
                            modifier =
                                Modifier
                                    .padding(top = 30.dp)
                                    .fillMaxWidth(),
                        )
                    }

                    (isGuestDriverAvailable.value && isRemoteStateActive).takeIf { it }?.also {
                        Box(
                            modifier =
                                Modifier
                                    .height(152.h()),
                        ) {
                            GuestDriverLaunchWidget()
                        }
                    }
                }

                else ->
                    OASubHeadLine3TextView(
                        text = context.getString(R.string.no_data_available),
                        color = AppTheme.colors.tertiary03,
                        modifier =
                            Modifier
                                .padding(top = 60.dp)
                                .fillMaxWidth(),
                        textAlign = TextAlign.Center,
                    )
            }
        },
        backgroundColor = AppTheme.colors.tile01,
    )
}

private fun navigateClimateScreen(
    event: RemoteCommandsEvent,
    navHostController: NavHostController,
    context: Context,
) {
    if (event is RemoteCommandsEvent.ClimateClicked) {
        navHostController.navigate(OAScreen.ClimateScreen.route)
    }
}

@Composable
private fun getToastMessage(showToast: State<String>): String {
    val message =
        if (showToast.value == DashboardConstants.NA) {
            stringResource(id = R.string.generic_error)
        } else {
            showToast.value
        }
    return message
}

private fun remoteItems(
    remoteListState: State<AdvanceRemoteState>,
    isRemoteParkNoAdvanceRemote: Boolean,
): List<RemoteItem> {
    val remoteList = (remoteListState.value as AdvanceRemoteState.Success).data.value as ArrayList<RemoteItem>
    val filteredRemoteList =
        if (isRemoteParkNoAdvanceRemote) {
            remoteList.filter { remoteItem ->
                remoteItem.remoteCommandType == RemoteCommandType.Park
            }
        } else {
            remoteList
        }
    return filteredRemoteList
}

fun handleDialogResponse(
    userAllowedAutoFix: Boolean,
    viewModel: AdvanceRemoteViewModel,
    context: Context,
) {
    viewModel.autoFixDialogFlow.value = false
    if (userAllowedAutoFix) {
        viewModel.onEvent(
            RemoteCommandsEvent.CommandClicked(
                RemoteCommandType.DoorLock,
                shortPress = false,
                autoFixEnabled = true,
                context = context,
            ),
        )
    }
}

@Composable
fun RemoteCommandsWidget(
    openSheet: (DigitalKeyState) -> Unit,
    activity: FragmentActivity,
    remoteList: ArrayList<RemoteItem>,
    viewModel: AdvanceRemoteViewModel,
    digitalKeyDownloadViewModel: DigitalKeyDownloadViewModel,
    digitalKeyEntityState: DigitalKeyEntityState,
    isDigitalKeyExists: Boolean,
    advanceRemoteBottomSheetScaffoldState: BottomSheetScaffoldState,
    timerText: String,
    isGuestDriverAvailable: State<Boolean>,
    advanceCoroutineScope: CoroutineScope,
    advanceBottomSheetState: BottomSheetScaffoldState,
) {
    val phoneVerificationContract =
        rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            digitalKeyDownloadViewModel.phoneVerificationResult(
                result.resultCode,
                true,
                activity = activity,
            )
        }

    val isDKConnected = digitalKeyEntityState.isConnected

    val isRemoteOnlyUser = viewModel.remoteOnlyUser.collectAsState()

    val isSecondaryDkAccepted = digitalKeyDownloadViewModel.isSecondaryDkAccepted.collectAsState()

    val primaryRemoteItems: List<RemoteItem> =
        remoteList.filter {
            it.type == RemoteType.PRIMARY
        }
    val secondaryRemoteItems: List<RemoteItem> =
        remoteList.filter {
            it.type == RemoteType.SECONDARY
        }

    val context = LocalContext.current

    val isSecondaryVehicle = digitalKeyDownloadViewModel.isSecondaryVehicle()

    val bottomSheetState = advanceRemoteBottomSheetScaffoldState.bottomSheetState

    LaunchedEffect(Unit, isSecondaryDkAccepted.value) {
        isSecondaryDkAccepted.value.let {
            if (it) {
                advanceCoroutineScope.launch {
                    if (bottomSheetState.isCollapsed) {
                        bottomSheetState.expand()
                        digitalKeyDownloadViewModel.startAutoDownload(
                            activity,
                            context,
                            true,
                            phoneVerificationContract,
                            advanceCoroutineScope,
                            advanceBottomSheetState,
                            openSheet,
                        )
                    }
                }
            }
        }
    }

    Box(
        modifier =
            Modifier
                .background(AppTheme.colors.tile01)
                .fillMaxHeight(),
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Box {
                LoadPrimaryRemote(primaryRemoteItems, processClick = { remote, shortPress ->
                    performClickOperation(
                        remote,
                        shortPress,
                        isDKConnected,
                        viewModel,
                        context,
                    )
                }, isDKConnected, timerText)
            }

            if (isDigitalKeyExists && !isRemoteOnlyUser.value || isSecondaryVehicle) {
                val isExpanded = advanceRemoteBottomSheetScaffoldState.bottomSheetState.isExpanded

                if (isExpanded) {
                    Card(
                        modifier =
                            Modifier
                                .height(100.dp)
                                .fillMaxWidth()
                                .padding(horizontal = 30.dp),
                        elevation = 3.dp,
                        shape = RoundedCornerShape(corner = CornerSize(10.dp)),
                        backgroundColor = AppTheme.colors.tertiary15,
                    ) {
                        ComposableDigitalKey(
                            openSheet = openSheet,
                            viewModel = digitalKeyDownloadViewModel,
                            activity = activity,
                            digitalKeyEntityState = digitalKeyEntityState,
                            advanceCoroutineScope = advanceCoroutineScope,
                            advanceBottomSheetState = advanceBottomSheetState,
                        )
                        Spacer(modifier = Modifier.height(24.h()))
                    }
                } else {
                    if (!isSecondaryVehicle && digitalKeyDownloadViewModel.getVinKeyInfo()?.keyInfoId == null) {
                        DownloadDigitalKeyNotice(
                            digitalKeyDownloadViewModel = digitalKeyDownloadViewModel,
                            activity = activity,
                            phoneVerificationLauncher = phoneVerificationContract,
                            openSheet = openSheet,
                            advanceRemoteBottomSheetScaffoldState = advanceRemoteBottomSheetScaffoldState,
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(24.h()))
            if (secondaryRemoteItems.isNotEmpty()) {
                LoadSecondaryRemote(secondaryRemoteItems, processClick = { remote, shortPress ->
                    viewModel.onEvent(
                        RemoteCommandsEvent.CommandClicked(
                            value = remote,
                            shortPress = shortPress,
                            context = context,
                        ),
                    )
                }, isDKConnected)
            }

            if (isRemoteOnlyUser.value) {
                Spacer(modifier = Modifier.weight(1f))
                OABody4TextView(
                    text = stringResource(id = R.string.secondaryDriver),
                    color = AppTheme.colors.tertiary03,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 10.dp),
                )
                Spacer(modifier = Modifier.height(40.dp))
            }

            if (isGuestDriverAvailable.value) {
                Spacer(modifier = Modifier.weight(1f))
                GuestDriverLaunchWidget()
                Spacer(modifier = Modifier.height(40.dp))
            }
        }
    }
}

/**
 * perform click operation, if digital key is connected then perform DK Lock/Unlock,
 * else perform advance remote lock/unlock
 */
fun performClickOperation(
    remote: RemoteCommandType,
    shortPress: Boolean,
    isConnected: Boolean,
    viewModel: AdvanceRemoteViewModel,
    context: Context,
) {
    viewModel.onEvent(
        RemoteCommandsEvent.CommandClicked(
            value = remote,
            shortPress = shortPress,
            isDkConnected = isConnected,
            context = context,
        ),
    )
}

@Composable
fun LoadPrimaryRemote(
    primaryRemoteItems: List<RemoteItem>,
    processClick: (remote: RemoteCommandType, shortPress: Boolean) -> Unit,
    isConnected: Boolean,
    timerText: String,
) {
    LazyRow(
        modifier =
            Modifier
                .padding(vertical = 10.dp, horizontal = 16.dp)
                .wrapContentHeight()
                .fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
    ) {
        itemsIndexed(primaryRemoteItems) { _, item ->
            Box(modifier = Modifier.padding(horizontal = 8.dp)) {
                RemoteComposableCircleItem(
                    remoteItem = item,
                    commandClick = { remote: RemoteCommandType, shortPress: Boolean ->
                        processClick(remote, shortPress)
                    },
                    isConnected = isConnected,
                    timerText = timerText,
                )
            }
        }
    }
}

@Composable
fun LoadSecondaryRemote(
    secondaryRemoteItems: List<RemoteItem>,
    processClick: (RemoteCommandType, Boolean) -> Unit,
    isConnected: Boolean,
) {
    val rowOneItemsMaxLength = 3
    val rowTwoItemsMaxLength = 8

    val rowOneItems =
        if (secondaryRemoteItems.size >= rowOneItemsMaxLength) {
            secondaryRemoteItems.subList(0, rowOneItemsMaxLength)
        } else {
            secondaryRemoteItems.subList(0, secondaryRemoteItems.size)
        }

    val rowTwoItems =
        if (secondaryRemoteItems.size >= rowTwoItemsMaxLength) {
            secondaryRemoteItems.subList(rowOneItemsMaxLength, rowTwoItemsMaxLength)
        } else if (secondaryRemoteItems.size > rowOneItemsMaxLength) {
            secondaryRemoteItems.subList(rowOneItemsMaxLength, secondaryRemoteItems.size)
        } else {
            listOf()
        }

    Column {
        if (rowOneItems.isNotEmpty()) {
            LazyRow(
                modifier =
                    Modifier
                        .padding(horizontal = 16.dp)
                        .fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
            ) {
                itemsIndexed(rowOneItems) { _, item ->
                    Box(contentAlignment = Alignment.Center) {
                        RemoteComposableCircleItem(
                            remoteItem = item,
                            commandClick = { remote: RemoteCommandType, shortPress: Boolean ->
                                processClick(remote, shortPress)
                            },
                            isConnected = isConnected,
                        )
                    }
                }
            }
        }
        if (rowTwoItems.isNotEmpty()) {
            Spacer(Modifier.height(18.dp))
            LazyRow(
                modifier =
                    Modifier
                        .padding(horizontal = 16.dp)
                        .fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
            ) {
                itemsIndexed(rowTwoItems) { _, item ->
                    Box(contentAlignment = Alignment.Center) {
                        RemoteComposableCircleItem(
                            remoteItem = item,
                            commandClick = { remote: RemoteCommandType, shortPress: Boolean ->
                                processClick(remote, shortPress)
                            },
                            isConnected = isConnected,
                        )
                    }
                }
            }
        }
    }
}
