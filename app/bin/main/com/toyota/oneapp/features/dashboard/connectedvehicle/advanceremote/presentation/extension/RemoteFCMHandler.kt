package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension

import android.content.Intent
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.ProgressState
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.AdvanceRemoteViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.utils.RemoteCommandAutoFix
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.utils.RemoteCommandStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.utils.RemoteConstants
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.utils.RemoteFCMStatus
import com.toyota.oneapp.features.dashboard.util.ApptentiveEvent
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import toyotaone.commonlib.log.LogTool

fun AdvanceRemoteViewModel.handleIntent(intent: Intent) {
    LogTool.d("REMOTE", "Received push intent with action: ${intent.action}")

    val silentPushVin = intent.getStringExtra(RemoteConstants.FCM_REMOTE_VIN) ?: ToyotaConstants.EMPTY_STRING
    val status = intent.getStringExtra(RemoteConstants.FCM_REMOTE_STATUS) ?: ToyotaConstants.EMPTY_STRING
    val category = intent.getStringExtra(RemoteConstants.FCM_REMOTE_CATEGORY) ?: ToyotaConstants.EMPTY_STRING

    if (vehicleInfo?.vin == silentPushVin &&
        status.isNotEmpty() &&
        category == ToyotaConstants.REMOTE_COMMAND &&
        ToyotaConstants.REMOTE_SILENCE_ACTION == intent.action
    ) {
        val remoteCommandType = intent.getStringExtra(RemoteConstants.FCM_REMOTE_COMMAND_TYPE) ?: ToyotaConstants.EMPTY_STRING

        when (status) {
            RemoteFCMStatus.REMOTE_STATUS_POPUP_REQUIRED.status -> {
                val autoFixPopUpId: Int = intent.getStringExtra(RemoteConstants.FCM_REMOTE_POPUP_ID)?.toInt() ?: -1
                if (autoFixPopUpId == RemoteCommandAutoFix.SHOW_POPUP.id) {
                    autoFixDialogFlow.value = true
                } else if (autoFixPopUpId == RemoteCommandAutoFix.SHOW_SPINNER_AND_DISABLE_ALL_COMMANDS.id) {
                    showDialogAndPrepareStartCommand()
                }
            }
            RemoteFCMStatus.REMOTE_STATUS_PROGRESS.status -> {
                disableAllCommands()
                updateProgressBar(
                    title =
                        getProgressTextFromFCM(
                            remoteCommandType,
                            vehicleInfo?.isTailgateLockUnlockCapable,
                        ),
                    progressState = ProgressState.IN_PROGRESS_75,
                    isRemoteStop = (
                        remoteCommandType == RemoteCommandStatus.ENGINE_STOP_1.command ||
                            remoteCommandType == RemoteCommandStatus.ENGINE_STOP_2.command
                    ),
                )
            }

            RemoteFCMStatus.REMOTE_STATUS_COMPLETED.status,
            RemoteFCMStatus.REMOTE_STATUS_ERRORS.status,
            RemoteFCMStatus.REMOTE_STATUS_TIMEOUT.status,
            RemoteFCMStatus.REMOTE_STATUS_TERMINATED.status,
            RemoteFCMStatus.REMOTE_STATUS_INTERRUPTED.status,
            -> {
                applicationData.updateThreeDShortActionStatus(status = false)
                updateProgressBar(
                    title =
                        getProgressTextFromFCM(
                            remoteCommandType,
                            vehicleInfo?.isTailgateLockUnlockCapable,
                        ),
                    progressState = ProgressState.COMPLETED,
                )
                when (remoteCommandType) {
                    RemoteCommandStatus.ENGINE_START_1.command, RemoteCommandStatus.ENGINE_START_2.command -> {
                        if (status == RemoteFCMStatus.REMOTE_STATUS_COMPLETED.status) {
                            viewModelScope.launch {
                                delay(4000L)
                                engageEvent(ApptentiveEvent.REMOTE_START_SUCCESS)
                            }
                        }
                        cancelJobAndGetEngineStatus(RemoteCommandType.EngineStart)
                    }

                    RemoteCommandStatus.ENGINE_STOP_1.command, RemoteCommandStatus.ENGINE_STOP_2.command -> {
                        cancelJobAndGetEngineStatus(RemoteCommandType.EngineStop)
                    }

                    RemoteCommandStatus.DOOR_LOCK_1.command, RemoteCommandStatus.DOOR_LOCK_2.command -> {
                        fetchOtherCommandsJob?.cancel()
                        if (status == RemoteFCMStatus.REMOTE_STATUS_COMPLETED.status) {
                            viewModelScope.launch {
                                delay(4000L)
                                engageEvent(ApptentiveEvent.REMOTE_LOCK_SUCCESS)
                            }
                        }
                        enableAllCommands()
                    }

                    RemoteCommandStatus.DOOR_UNLOCK_1.command, RemoteCommandStatus.DOOR_UNLOCK_2.command -> {
                        fetchOtherCommandsJob?.cancel()
                        if (status == RemoteFCMStatus.REMOTE_STATUS_COMPLETED.status) {
                            viewModelScope.launch {
                                delay(4000L)
                                engageEvent(ApptentiveEvent.REMOTE_UNLOCK_SUCCESS)
                            }
                        }
                        enableAllCommands()
                    }

                    else -> {
                        fetchOtherCommandsJob?.cancel()
                        enableAllCommands()
                    }
                }
            }
        }
    }
}

fun getProgressTextFromFCM(
    commandType: String,
    tailgateLockUnlockCapable: Boolean?,
): Int =
    when (commandType) {
        RemoteCommandStatus.ENGINE_START_1.command, RemoteCommandStatus.ENGINE_START_2.command -> R.string.remote_starting
        RemoteCommandStatus.ENGINE_STOP_1.command, RemoteCommandStatus.ENGINE_STOP_2.command -> R.string.remote_stopping
        RemoteCommandStatus.DOOR_LOCK_1.command, RemoteCommandStatus.DOOR_LOCK_2.command -> R.string.Dashboard_locking
        RemoteCommandStatus.DOOR_UNLOCK_1.command, RemoteCommandStatus.DOOR_UNLOCK_2.command -> R.string.Dashboard_unlocking
        RemoteCommandStatus.TRUNK_LOCK.command, RemoteCommandStatus.TRUNK_LOCK.command ->
            if (tailgateLockUnlockCapable ==
                true
            ) {
                R.string.tailgate_locking
            } else {
                R.string.trunk_locking
            }
        RemoteCommandStatus.TRUNK_UNLOCK.command, RemoteCommandStatus.TRUNK_UNLOCK.command ->
            if (tailgateLockUnlockCapable ==
                true
            ) {
                R.string.tailgate_un_locking
            } else {
                R.string.trunk_un_locking
            }
        RemoteCommandStatus.HAZARD_ON_1.command, RemoteCommandStatus.HAZARD_ON_2.command -> R.string.hazards_on
        RemoteCommandStatus.HORN_ON.command -> R.string.horn_on
        RemoteCommandStatus.LIGHTS_ON.command -> R.string.lights_on
        RemoteCommandStatus.BUZZER_ON.command, RemoteCommandStatus.BUZZER_ON_2.command -> R.string.buzzer_on
        else -> R.string.remote_connecting
    }
