package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension

import android.os.CountDownTimer
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application.AdvanceRemoteState
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.AdvanceRemoteViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.utils.TimeFormatExt.timeFormat
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.log.LogTool
import java.util.concurrent.TimeUnit

private const val TAG = "REMOTE_TIMER"

fun AdvanceRemoteViewModel.startCountDownTimer(timer: String) =
    viewModelScope.launch {
        if (timer.contains(":") && timer.first().toString().isNotEmpty() && timer.last().toString().isNotEmpty()) {
            try {
                val timerMinutes = TimeUnit.MINUTES.toMillis(timer.split(":").first().toLong())
                val timerSeconds = TimeUnit.SECONDS.toMillis(timer.split(":").last().toLong())
                val initialTotalTimeInMillis = timerMinutes + timerSeconds
                val timeLeft = mutableStateOf(initialTotalTimeInMillis)
                val timerText = mutableStateOf(timeLeft.value.timeFormat())
                countDownTimer =
                    object : CountDownTimer(timeLeft.value, countDownInterval) {
                        override fun onTick(currentTimeLeft: Long) {
                            timerText.value = currentTimeLeft.timeFormat()
                            timeLeft.value = currentTimeLeft
                            timerStateFlow.value = timerText.value
                        }

                        override fun onFinish() {
                            stopTimer()
                        }
                    }.start()

                // Todo - Need to refactor this update method
                remoteList.update {
                    remoteList.value.apply {
                        this.map { item ->
                            if (item.remoteCommandType == RemoteCommandType.EngineStart) {
                                item.apply {
                                    showProgress = false
                                    showTimer = true
                                    remoteCommandType = RemoteCommandType.EngineStop
                                    title = R.string.Dashboard_stop_engine
                                }
                            } else {
                                item.apply { disableCommand = false }
                            }
                        }
                    }
                }
                mRemoteCommandsState.value = AdvanceRemoteState.Success(remoteList)
            } catch (exception: Exception) {
                LogTool.d(TAG, exception.message)
            }
        } else {
            enableAllCommands()
        }
    }

fun AdvanceRemoteViewModel.stopTimer() =
    viewModelScope.launch {
        try {
            fetchEngineStatus3TimesIntervalJob?.cancel()
            countDownTimer?.cancel()
            timerStateFlow.value = ""
            remoteList.update {
                remoteList.value.toMutableList().apply {
                    this.map { item ->
                        if (item.remoteCommandType == RemoteCommandType.EngineStop) {
                            item.apply {
                                showProgress = false
                                showTimer = false
                                disableCommand = false
                                remoteCommandType = RemoteCommandType.EngineStart
                                title = R.string.Dashboard_start_engine
                            }
                        } else {
                            item.apply { disableCommand = false }
                        }
                    }
                }
            }
            mRemoteCommandsState.value = AdvanceRemoteState.Success(remoteList)
        } catch (exception: Exception) {
            LogTool.d(TAG, exception.message)
        }
    }
