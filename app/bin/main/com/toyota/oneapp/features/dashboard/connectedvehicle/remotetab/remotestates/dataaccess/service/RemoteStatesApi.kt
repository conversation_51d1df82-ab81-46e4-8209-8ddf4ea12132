package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.dataaccess.service

import com.toyota.oneapp.model.subscription.AssignRemoteUserRequest
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.PUT

interface RemoteStatesApi {
    @PUT("/oneapi/v1/subscription/remoteguid")
    suspend fun assignRemoteUser(
        @Header("GENERATION") generation: String,
        @Header("X-BRAND") brand: String,
        @Header("DATETIME") dateTime: String,
        @Body assignRemoteUserRequest: AssignRemoteUserRequest,
        @Header("VIN") vin: String,
    ): Response<BaseResponse?>
}
