package com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain

import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CASEvent
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CASFeedback
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CabinAwareness
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import kotlin.coroutines.CoroutineContext

abstract class CVRepository(
    errorParser: ErrorMessageParser,
    ioContext: CoroutineContext,
) : BaseRepository(errorParser, ioContext) {
    abstract suspend fun getCAS(vin: String): Resource<CabinAwareness?>

    abstract suspend fun updateCASFeedback(feedback: CASFeedback): Resource<BaseResponse?>

    abstract suspend fun updateCASEvent(casEvent: CASEvent): Resource<BaseResponse?>
}
