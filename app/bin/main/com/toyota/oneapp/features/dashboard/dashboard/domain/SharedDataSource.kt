package com.toyota.oneapp.features.dashboard.dashboard.domain

import com.toyota.oneapp.features.accountnotification.application.NotificationHistoryState
import com.toyota.oneapp.features.accountnotification.application.VehicleAlertState
import com.toyota.oneapp.features.core.commonapicalls.application.ElectricStatusState
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.dashboard.announcement.application.AnnouncementState
import com.toyota.oneapp.features.dashboard.dashboard.application.TelemetryState
import com.toyota.oneapp.features.subscription.application.SubscriptionState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SharedDataSource
    @Inject
    constructor() {
        private val telemetryState = MutableStateFlow<TelemetryState>(value = TelemetryState.Idle)

        private val electricStatusState =
            MutableStateFlow<ElectricStatusState>(
                value = ElectricStatusState.Idle,
            )

        private val subscriptionState =
            MutableStateFlow<SubscriptionState>(
                value = SubscriptionState.Idle,
            )

        private val notificationHistoryState =
            MutableStateFlow<NotificationHistoryState>(
                value = NotificationHistoryState.Idle,
            )
        private val vehicleAlertState =
            MutableStateFlow<VehicleAlertState>(
                value = VehicleAlertState.Idle,
            )

        private val fetchDataStateFlow = MutableStateFlow(value = FetchData.NONE)
        val fetchDataState = fetchDataStateFlow.asStateFlow()

        private val marketingBannerStateFlow =
            MutableStateFlow<AnnouncementState>(
                value = AnnouncementState.Idle,
            )

        fun setNotificationHistoryData(notificationHistoryState: NotificationHistoryState) {
            this.notificationHistoryState.value = notificationHistoryState
        }

        fun getNotificationHistoryState(): MutableStateFlow<NotificationHistoryState> = notificationHistoryState

        fun setVehicleAlertsData(vehicleAlertState: VehicleAlertState) {
            this.vehicleAlertState.value = vehicleAlertState
        }

        fun getVehicleAlertState(): MutableStateFlow<VehicleAlertState> = vehicleAlertState

        fun setTelemetryData(telemetryState: TelemetryState) {
            this.telemetryState.value = telemetryState
        }

        fun getTelemetryState(): MutableStateFlow<TelemetryState> {
            when (telemetryState.value) {
                is TelemetryState.Idle,
                is TelemetryState.Error,
                -> {
                    fetchDataStateFlow.value = FetchData.TELEMETRY
                }
                else -> {
                    // Do Nothing
                }
            }
            return telemetryState
        }

        fun setElectricStatusState(electricStatusState: ElectricStatusState) {
            this.electricStatusState.value = electricStatusState
        }

        fun getElectricStatusState(): MutableStateFlow<ElectricStatusState> {
            when (electricStatusState.value) {
                is ElectricStatusState.Idle,
                is ElectricStatusState.Error,
                -> {
                    fetchDataStateFlow.value = FetchData.ELECTRIC_STATUS
                }
                else -> {
                    // Do nothing
                }
            }
            return electricStatusState
        }

        fun saveRealTimeElectricStatus(electricStatusResponse: ElectricStatusResponse?) {
            electricStatusState.value = ElectricStatusState.Idle
            electricStatusResponse?.let {
                electricStatusState.value = ElectricStatusState.Success(it)
            } ?: run {
                electricStatusState.value = ElectricStatusState.Error()
            }
        }

        fun resetElectricStatusState() {
            electricStatusState.value = ElectricStatusState.Idle
        }

        fun setSubscriptionData(subscriptionState: SubscriptionState) {
            this.subscriptionState.value = subscriptionState
        }

        fun getSubscriptionState(): MutableStateFlow<SubscriptionState> {
            when (subscriptionState.value) {
                is SubscriptionState.Idle,
                is SubscriptionState.Error,
                -> {
                    fetchDataStateFlow.value = FetchData.SUBSCRIPTION
                }
                else -> {
                    // Do Nothing
                }
            }
            return subscriptionState
        }

        fun setMarketingBannerData(announcementState: AnnouncementState) {
            marketingBannerStateFlow.value = announcementState
        }

        fun getMarketingBannerState(): MutableStateFlow<AnnouncementState> = marketingBannerStateFlow

        fun resetMarketingBanner() {
            marketingBannerStateFlow.value = AnnouncementState.Idle
        }

        fun resetDataSource() {
            telemetryState.value = TelemetryState.Idle
            electricStatusState.value = ElectricStatusState.Idle
            subscriptionState.value = SubscriptionState.Idle
        }
    }

enum class FetchData {
    NONE,
    TELEMETRY,
    ELECTRIC_STATUS,
    SUBSCRIPTION,
}
