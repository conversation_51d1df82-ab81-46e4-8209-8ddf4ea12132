package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.presentation

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.composable.LoadVehicleImage
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.presentation.CASState
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.presentation.widgets.CasSnippet
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.RemoteStatesScreen
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardBottomSheetScreen

@Composable
fun RemoteTabScreen() {
    val coroutineScope = rememberCoroutineScope()
    val dashboardViewModel = (LocalContext.current as OADashboardActivity).dashboardViewModel

    val viewModel = (LocalContext.current as OADashboardActivity).connectedVehicleViewModel

    val vehicleImage = dashboardViewModel.imageData.collectAsState()

    val context = LocalContext.current

    val bottomSheet = LocalBottomSheet.current

    val cas = viewModel.casState.collectAsState()

    Column(
        modifier =
            Modifier
                .fillMaxHeight()
                .fillMaxSize()
                .background(AppTheme.colors.tertiary12)
                .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // TODO: Replace using subcomposelayout or similar to scale based on available space instead of screenHeight
        val screenHeightDp = LocalConfiguration.current.screenHeightDp.dp
        val topSpacerHeight =
            when {
                screenHeightDp > 707.dp -> 20.dp
                screenHeightDp in 667.dp..707.dp -> 3.dp
                else -> 0.dp
            }
        Spacer(modifier = Modifier.height(topSpacerHeight))
        LoadVehicleImage(
            url = vehicleImage.value,
            accessibilityId = AccessibilityId.ID_DASHBOARD_DISPLAY_IMAGE,
            contentDescription = R.string.dashboardVehicleImageDescription,
            isDashboardImage = true,
            modifier =
                Modifier.clickable {
                    dashboardViewModel.updateVehicleInfoAnalytics()
                    coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                        OADashboardBottomSheetScreen(bottomSheetState = it, true)
                    }
                },
        )
        Spacer(modifier = Modifier.height(5.dp))
        Button(
            onClick = {
                dashboardViewModel.updateVehicleInfoAnalytics()
                coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                    OADashboardBottomSheetScreen(bottomSheetState = it, true)
                }
            },
            colors = ButtonDefaults.buttonColors(AppTheme.colors.primaryButton01),
            shape = RoundedCornerShape(100.dp),
            border = BorderStroke(2.dp, AppTheme.colors.tertiary10),
            modifier =
                Modifier
                    .height(34.dp)
                    .width(72.dp)
                    .layoutId(AccessibilityId.ID_VEHICLE_INFO_CTA),
        ) {
            Text(
                text = context.getString(R.string.info),
                color = AppTheme.colors.primaryButton02,
            )
        }
        SubscriptionSnippetScreen()
        if (cas.value is CASState.Success) {
            val casData = (cas.value as CASState.Success).data
            CasSnippet(casData, onUpdateCasEvent = { eventId ->
                viewModel.updateCasAnalytics(AnalyticsEventParam.CAS_REMOTE_DETECTED_TILE)
                viewModel.updateCASEvent(eventId)
            })
        }
        Spacer(modifier = Modifier.height(24.dp))
        RemoteStatesScreen()
        Spacer(modifier = Modifier.height(dashboardViewModel.remoteSheetHeight))
    }
}
