package com.toyota.oneapp.features.dashboard.dashboard

import com.toyota.oneapp.features.dashboard.dashboard.application.DashboardLogic
import com.toyota.oneapp.features.dashboard.dashboard.application.DashboardUseCase
import com.toyota.oneapp.features.dashboard.dashboard.dataaccess.repository.DashboardDefaultRepo
import com.toyota.oneapp.features.dashboard.dashboard.domain.repository.DashboardRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class DashboardModule {
    @Binds
    abstract fun provideTopNavRepository(topNavDefaultRepository: DashboardDefaultRepo): DashboardRepository

    @Binds
    abstract fun provideTopNavLogic(dashboardLogic: DashboardLogic): DashboardUseCase
}
