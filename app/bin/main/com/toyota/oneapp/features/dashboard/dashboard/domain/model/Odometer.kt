package com.toyota.oneapp.features.dashboard.dashboard.domain.model

import com.toyota.oneapp.features.fuelwidget.application.UnitValue
import com.toyota.oneapp.model.vehicle.VehicleLocation

data class Telemetry(
    var lastUpdatedTime: String? = null,
    val displayDistanceToEmpty: Boolean = false,
    val odometer: String? = null,
    val distanceToEmpty: UnitValue,
    val fuelLevel: Int?,
    val vehicleLocation: VehicleLocation?,
)
