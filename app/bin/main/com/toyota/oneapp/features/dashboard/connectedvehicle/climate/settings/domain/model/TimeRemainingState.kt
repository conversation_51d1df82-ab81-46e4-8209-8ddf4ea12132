package com.toyota.oneapp.features.dashboard.connectedvehicle.climate.settings.domain.model

import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateState
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateType

data class TimeRemainingState(
    val type: ClimateType,
    val remainingTime: String,
    var isLayoutActionable: Boolean = true,
) : ClimateState()
