package com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.repository

import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CASEvent
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CASFeedback
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CabinAwareness
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.service.CVApi
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.CVRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class CVDefaultRepository
    @Inject
    constructor(
        private val cvaPi: CVApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : CVRepository(errorParser, ioContext) {
        override suspend fun getCAS(vin: String): Resource<CabinAwareness?> {
            return makeApiCall {
                cvaPi.getCAS(vin = vin)
            }
        }

        override suspend fun updateCASFeedback(feedback: CASFeedback): Resource<BaseResponse?> {
            return makeApiCall {
                cvaPi.updateCASFeedback(feedback = feedback)
            }
        }

        override suspend fun updateCASEvent(casEvent: CASEvent): Resource<BaseResponse?> {
            return makeApiCall {
                cvaPi.updateCASEvent(casEvent = casEvent)
            }
        }
    }
