package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation

import android.annotation.SuppressLint
import android.content.pm.PackageManager
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OACaption1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DigitalKeyShareViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.States
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.extension.updateRemoteStatesAnalyticsLogger
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.helper.RemoteStatesHelper
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.helper.RemoteStatesHelper.checkNotificationEnabled
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.helper.getRemoteStatesAccessibilityId
import com.toyota.oneapp.features.dashboard.dashboard.application.TelemetryState
import com.toyota.oneapp.features.dashboard.dashboard.presentation.LocalNavHostController
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.dashboard.util.DashboardConstants.ID_REMOTE_STATES_BOTTOM_SHEET
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import toyotaone.commonlib.permission.PermissionUtil

@SuppressLint("StateFlowValueCalledInComposition")
@Composable
fun RemoteStatesScreen() {
    val navHostController = LocalNavHostController.current
    val context = LocalContext.current

    val remoteStatesViewModel =
        hiltViewModel<RemoteStatesViewModel>(
            context as OADashboardActivity,
        )

    val digitalKeyShareViewModel = hiltViewModel<DigitalKeyShareViewModel>()

    val dashboardViewModel = context.dashboardViewModel

    val remoteStates = remoteStatesViewModel.remoteStates.collectAsState().value

    val topNavState =
        remoteStatesViewModel.sharedDataSource
            .getTelemetryState()
            .collectAsState()
            .value

    val coroutineScope = rememberCoroutineScope()

    val bottomSheet = LocalBottomSheet.current

    val lifecycleOwner = LocalLifecycleOwner.current

    val isRemoteActiveButNotificationDisabled =
        checkNotificationEnabled(
            context,
            remoteStates,
            digitalKeyShareViewModel.isSecondaryKey(),
        )

    var dashboardRefreshed = false

    LaunchedEffect(Unit) {
        remoteStatesViewModel.dashboardRefresh.collectLatest { performRefresh ->
            if (performRefresh) {
                dashboardViewModel.onRefresh()
                remoteStatesViewModel.mDashboardRefresh.value = false
            }
        }
    }

    LaunchedEffect(Unit) {
        remoteStatesViewModel.showProgress.collectLatest { performRefresh ->
            dashboardViewModel.updateProgressBar(performRefresh)
        }
    }

    DisposableEffect(Unit) {
        val observer =
            LifecycleEventObserver { _, event ->
                if (event == Lifecycle.Event.ON_RESUME &&
                    isRemoteActiveButNotificationDisabled.status == States.NOTIFICATIONS_DISABLED &&
                    !dashboardRefreshed
                ) {
                    if (PermissionUtil().checkNotificationPermissionStatus(context) == PackageManager.PERMISSION_GRANTED) {
                        // Refresh the dashboard to show the remote commands
                        dashboardViewModel.onRefresh()
                        dashboardRefreshed = true
                    }
                }
            }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    when (remoteStates.status) {
        States.HIDE -> {}
        States.REMOTE_ACTIVE ->
            checkNotificationEnabled(
                context,
                remoteStates,
                digitalKeyShareViewModel.isSecondaryKey(),
            )
        else -> {
            if (remoteStates.status == States.REMOTE_SHARED) {
                checkNotificationEnabled(
                    context,
                    remoteStates,
                    digitalKeyShareViewModel.isSecondaryKey(),
                )
            }
            remoteStatesViewModel.updateRemoteStatePopup(remoteStates = remoteStates)
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                remoteStates.title?.let { stringResource(id = it) }?.let {
                    OASubHeadLine1TextView(
                        text = it,
                        color = AppTheme.colors.tertiary03,
                        modifier = Modifier.padding(bottom = 8.dp),
                    )
                }
                remoteStates.subHeading?.let { stringResource(id = it) }?.let {
                    OACallOut1TextView(
                        text = it,
                        color = AppTheme.colors.tertiary05,
                        textAlign = TextAlign.Center,
                        modifier =
                            Modifier
                                .padding(bottom = 30.dp, start = 32.dp, end = 32.dp)
                                .fillMaxWidth(),
                    )
                }

                remoteStates.ctaSecondaryText?.let { context.getString(it) }?.let {
                    OACallOut1TextView(
                        text = it,
                        color = AppTheme.colors.tertiary05,
                        modifier =
                            Modifier
                                .testTagID(
                                    if (remoteStates.status == States.VEHICLE_STOLEN) {
                                        AccessibilityId.ID_RS_STOLEN_CONTACT_US_TEXT_BUTTON
                                    } else {
                                        ToyotaConstants.EMPTY_STRING
                                    },
                                ).padding(16.dp)
                                .clickable(onClick = {
                                    if (remoteStates.status == States.VEHICLE_STOLEN) {
                                        remoteStatesViewModel
                                            .getContactIntent()
                                            ?.let { contactData ->
                                                RemoteStatesHelper.contactSupport(
                                                    context,
                                                    region = contactData.region,
                                                    brand = contactData.brand,
                                                )
                                            }
                                    }
                                }),
                    )
                }

                if (remoteStates.ctaVisibility) {
                    remoteStates.ctaText?.let { context.getString(it) }?.let {
                        PrimaryButton02(
                            text = it,
                            modifier =
                                Modifier
                                    .wrapContentWidth()
                                    .wrapContentHeight()
                                    .testTagID(getRemoteStatesAccessibilityId(remoteStates.status)),
                            click = {
                                remoteStatesViewModel.updateRemoteStatesAnalyticsLogger(
                                    remoteStates.status,
                                )
                                when (remoteStates.status) {
                                    States.ACTIVATE_REMOTE, States.ACTIVATE_FAILED -> {
                                        coroutineScope.launch {
                                            remoteStatesViewModel.checkConditionToShowSOS(
                                                context,
                                                coroutineScope,
                                                bottomSheet,
                                            )
                                        }
                                    }
                                    States.SUBSCRIPTION_CANCELLED, States.SUBSCRIPTION_EXPIRED ->
                                        navHostController.navigate(
                                            OAScreen.Subscriptions.route,
                                        )
                                    States.ACTIVATION_ERROR -> {
                                        remoteStatesViewModel.getContactIntent()?.let { contactData ->
                                            RemoteStatesHelper.contactSupport(
                                                context,
                                                region = contactData.region,
                                                brand = contactData.brand,
                                            )
                                        }
                                    }
                                    States.NOTIFICATIONS_DISABLED ->
                                        PermissionUtil.openAppSettings(
                                            context,
                                        )
                                    else -> {
                                        when (remoteStates.status) {
                                            States.ACTIVATE_REMOTE_LMEX -> {
                                                coroutineScope.launch {
                                                    remoteStatesViewModel.checkConditionToShowSOS(
                                                        context,
                                                        coroutineScope,
                                                        bottomSheet,
                                                    )
                                                }
                                            }

                                            States.VEHICLE_STOLEN -> {
                                                coroutineScope.launchPrimaryBottomSheetAction(
                                                    bottomSheet,
                                                    ID_REMOTE_STATES_BOTTOM_SHEET,
                                                ) { state ->
                                                    RemoteStatesBottomSheet(state, bottomSheet)
                                                }
                                            }
                                            States.REMOTE_SHARED -> {
                                                remoteStatesViewModel.updateRemoteStatePopup(
                                                    remoteStates,
                                                )
                                                coroutineScope.launchPrimaryBottomSheetAction(
                                                    bottomSheet,
                                                    ID_REMOTE_STATES_BOTTOM_SHEET,
                                                ) { sheetState ->
                                                    RemoteStatesBottomSheet(sheetState, bottomSheet)
                                                }
                                            }
                                            else -> {
                                                remoteStatesViewModel.mDashboardRefresh.value = true
                                            }
                                        }
                                    }
                                }
                            },
                        )
                    }
                }

                if (topNavState is TelemetryState.Success) {
                    val lastUpdatedTime = topNavState.telemetryPayload?.lastUpdatedTime
                    if (!lastUpdatedTime.isNullOrEmpty()) {
                        OACaption1TextView(
                            text =
                                stringResource(
                                    id = R.string.VehicleStatus_last_updated,
                                    lastUpdatedTime,
                                ),
                            color = AppTheme.colors.tertiary05,
                            modifier = Modifier.padding(top = 20.dp, bottom = 16.dp),
                        )
                    }
                }
            }
        }
    }
}
