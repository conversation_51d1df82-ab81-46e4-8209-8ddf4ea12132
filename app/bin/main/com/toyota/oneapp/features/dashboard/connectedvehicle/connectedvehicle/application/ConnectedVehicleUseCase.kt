package com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.application

import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CASEvent
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CASFeedback
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.DashboardTab
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.model.CabinAlert
import kotlinx.coroutines.flow.Flow

interface ConnectedVehicleUseCase {
    fun loadTabWidget(): Flow<MutableList<DashboardTab>>

    // Get the cas data from API and show the UI State
    fun getCASData(vin: String): Flow<CabinAlert?>

    // Update the user feedback
    fun updateCASFeedback(casFeedback: CASFeedback): Flow<Boolean>

    // Update the cas event for BackEnd analytics purpose
    fun updateCASEvent(casEvent: CASEvent): Flow<Boolean>
}
