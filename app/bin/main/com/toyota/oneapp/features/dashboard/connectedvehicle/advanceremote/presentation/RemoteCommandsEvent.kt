package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation

import android.content.Context
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType

sealed class RemoteCommandsEvent {
    data class CommandClicked(
        val value: RemoteCommandType,
        val shortPress: <PERSON><PERSON><PERSON>,
        val autoFixEnabled: <PERSON>olean = false,
        val isDkConnected: Boolean = false,
        val context: Context? = null,
    ) : RemoteCommandsEvent()

    object ClimateClicked : RemoteCommandsEvent()
}
