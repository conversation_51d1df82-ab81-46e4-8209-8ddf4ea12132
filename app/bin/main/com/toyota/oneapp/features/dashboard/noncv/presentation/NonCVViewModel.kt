package com.toyota.oneapp.features.dashboard.noncv.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.dashboard.noncv.application.NonCVState
import com.toyota.oneapp.features.dashboard.noncv.application.NonCVUseCase
import com.toyota.oneapp.features.dashboard.noncv.domain.model.NonCVType
import com.toyota.oneapp.ui.BaseFragmentViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class NonCVViewModel
    @Inject
    constructor(
        private val nonCVUseCase: NonCVUseCase,
        applicationData: ApplicationData,
        val analyticsLogger: AnalyticsLogger,
    ) : BaseFragmentViewModel() {
        // Remote Commands State
        private val nonCVState = MutableStateFlow<NonCVState>(value = NonCVState.Loading)
        val nonCVStateFlow = nonCVState.asStateFlow()

        init {
            viewModelScope.launch {
                applicationData.getSelectedVehicleState().collect {
                    nonCVState.value = NonCVState.Loading
                    if (it != null) {
                        nonCVUseCase.getNonCVFeatures(it).collect { it1 ->
                            nonCVState.value = NonCVState.Success(it1)
                        }
                    }
                }
            }
        }

        fun updatedAnalyticsLog(type: NonCVType?) {
            when (type) {
                NonCVType.PREFERRED_DEALER ->
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.DASHBOARD_CARD.eventName,
                        AnalyticsEventParam.NC_PREFERRED_DEALER,
                    )
                NonCVType.MAINTENANCE_SCHEDULE ->
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.DASHBOARD_CARD.eventName,
                        AnalyticsEventParam.NC_MAINTENANCE_SCHEDULE,
                    )
                NonCVType.SERVICE_HISTORY ->
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.DASHBOARD_CARD.eventName,
                        AnalyticsEventParam.NC_SERVICE_HISTORY,
                    )
                NonCVType.APPOINTMENTS ->
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.DASHBOARD_CARD.eventName,
                        AnalyticsEventParam.NC_APPOINTMENTS,
                    )

                else -> {}
            }
        }
    }
