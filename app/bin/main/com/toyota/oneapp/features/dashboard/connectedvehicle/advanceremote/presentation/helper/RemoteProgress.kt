package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.helper

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ProgressIndicatorDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.LinearProgressWithShimmerIndicator
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.ProgressState
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteProgress

@Composable
fun RemoteProgressBar(remoteProgress: RemoteProgress) {
    var downloadProgress =
        when (remoteProgress.progressState) {
            ProgressState.STARTED -> 0.2f
            ProgressState.IN_PROGRESS -> 0.5f
            ProgressState.IN_PROGRESS_75 -> 0.7f
            else -> 0.1f
        }

    // To Increase the loader percentage if in-progress receives more than once.
    val inProgress75Count = remember { mutableStateOf(0) }

    if (remoteProgress.progressState == ProgressState.IN_PROGRESS_75) {
        inProgress75Count.value++

        if (inProgress75Count.value > 1) {
            val newProgress = downloadProgress + 0.1f
            downloadProgress = if (newProgress <= 0.9f) newProgress else 0.9f
        }
    } else {
        inProgress75Count.value = 0
    }

    val animatedProgress =
        animateFloatAsState(
            targetValue = downloadProgress,
            animationSpec = ProgressIndicatorDefaults.ProgressAnimationSpec,
            label = "",
        ).value

    if (remoteProgress.progressState in
        listOf(
            ProgressState.STARTED,
            ProgressState.IN_PROGRESS,
            ProgressState.IN_PROGRESS_75,
        )
    ) {
        val modifier =
            Modifier
                .fillMaxWidth()

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = modifier,
        ) {
            OACallOut1TextView(
                text =
                    stringResource(
                        id = if (remoteProgress.title != 0) remoteProgress.title else R.string.remote_connecting,
                    ),
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Center,
                modifier = Modifier.testTagID(AccessibilityId.REMOTE_PROGRESS_TEXT),
            )
            Spacer(modifier = Modifier.height(4.dp))
            Box(
                modifier = Modifier.clip(RoundedCornerShape(50)),
            ) {
                LinearProgressWithShimmerIndicator(
                    progress = animatedProgress,
                    progressColor = if (remoteProgress.isRemoteStop) AppTheme.colors.primary01 else AppTheme.colors.secondary01,
                    modifier =
                        Modifier
                            .layoutId(AccessibilityId.REMOTE_PROGRESS_VIEW)
                            .width(104.dp)
                            .height(6.dp),
                    clipShape = RoundedCornerShape(50),
                )
            }
        }
    }
}
