package com.toyota.oneapp.features.dashboard.dashboard.presentation

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavHostController
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.presentation.ConnectedVehicleScreen
import com.toyota.oneapp.features.dashboard.noncv.presentation.NonCVScreen

@SuppressLint("CompositionLocalNaming")
val LocalNavHostController =
    compositionLocalOf<NavHostController> {
        error("No NavHostController found!")
    }

@SuppressLint("StateFlowValueCalledInComposition")
@Composable
fun OADashboardScreen(navHostController: NavHostController) {
    CompositionLocalProvider(LocalNavHostController provides navHostController) {
        val dashboardViewModel = (LocalContext.current as OADashboardActivity).dashboardViewModel
        val isNonConnectedVehicle = dashboardViewModel.isNonConnectedVehicle.collectAsState()
        if (isNonConnectedVehicle.value) {
            NonCVScreen()
        } else {
            ConnectedVehicleScreen(navHostController)
        }
    }
}
