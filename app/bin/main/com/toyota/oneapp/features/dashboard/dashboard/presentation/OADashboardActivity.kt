package com.toyota.oneapp.features.dashboard.dashboard.presentation

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.bluetooth.BluetoothManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.graphics.toArgb
import androidx.core.view.WindowCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import apptentive.com.android.feedback.Apptentive
import apptentive.com.android.feedback.ApptentiveActivityInfo
import com.google.android.gms.maps.MapsInitializer
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.ToyotaApplication
import com.toyota.oneapp.component.receiver.BluetoothStateReceiver
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyManageViewModel
import com.toyota.oneapp.fcm.ToyotaFCMService
import com.toyota.oneapp.features.accountnotification.presentation.AccountNotificationViewModel
import com.toyota.oneapp.features.chargeassist.view.helper.ColdAppLaunchCounter
import com.toyota.oneapp.features.chargeassist.view.helper.provideColdAppLaunchCounter
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.presentation.ConnectedVehicleViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DigitalKeyDownloadViewModel
import com.toyota.oneapp.features.vehicleswitcher.presentation.VehicleSwitcherViewModel
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.services.UserProfileTransferService
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.flutter.DashboardFlutterActivity
import com.toyota.oneapp.ui.flutter.GO_TO_DASHBOARD
import com.toyota.oneapp.ui.flutter.VehicleRemoved
import com.toyota.oneapp.util.RootUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.xcapp.XcappActivity
import com.toyota.oneapp.xcapp.manager.XcappManagerProvider
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.disposables.Disposable
import org.json.JSONObject
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.eventbus.RxBus
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.permission.PermissionUtil
import javax.inject.Inject

@AndroidEntryPoint
class OADashboardActivity :
    UiBaseActivity(),
    ApptentiveActivityInfo {
    private lateinit var coldAppLaunchCounter: ColdAppLaunchCounter
    private lateinit var disposable: Disposable
    private var isAppInForeGround = false

    companion object {
        const val REFRESH_DASHBOARD = "DashboardRefresh"
        const val UPDATE_DK_VEHICLE = "updateDkVehicle"
        const val NEW_VEHICLE_ADDED_KEY = "NewVehicle"
        var isReturningFromSettings = false

        @JvmStatic
        fun createIntent(
            context: Context,
            bundle: Bundle? = null,
        ): Intent {
            val intent = Intent(context, OADashboardActivity::class.java)
            intent.putExtra(GO_TO_DASHBOARD, true)
            if (bundle != null) {
                intent.putExtras(bundle)
            }
            return intent
        }
    }

    val dashboardViewModel: DashboardViewModel by viewModels()
    val accountNotificationViewModel: AccountNotificationViewModel by viewModels()
    val connectedVehicleViewModel: ConnectedVehicleViewModel by viewModels()
    val digitalKeyDownloadViewModel: DigitalKeyDownloadViewModel by viewModels()
    val digitalKeyManageViewModel: DigitalKeyManageViewModel by viewModels()

    @Inject
    lateinit var applicationData: ApplicationData

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @Inject
    lateinit var preferenceModel: OneAppPreferenceModel

    @OptIn(ExperimentalMaterialApi::class)
    val vehicleSwitcherViewModel: VehicleSwitcherViewModel by viewModels()

    // Checks for required permissions for BLE connection and starts service when granted.
    // Enable Profile Sync-up setup
    @SuppressLint("NewApi") // this request is launched only after checking the api level
    private val locationPermissionRequest =
        registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions(),
        ) { permissions ->
            when {
                permissions.getOrDefault(Manifest.permission.ACCESS_BACKGROUND_LOCATION, false) -> {
                    checkPermissionsWithOverlay()
                }

                else -> {
                    // No location access granted.
                    isReturningFromSettings = true
                }
            }
        }

    override fun onNewIntent(intent: Intent) {
        handlePushNotifications(intent)
        super.onNewIntent(intent)
    }

    private val notificationPermissionLauncher =
        registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions(),
        ) { enableProfileSyncUp() }

    private val notificationReceiver =
        object : BroadcastReceiver() {
            override fun onReceive(
                context: Context?,
                intent: Intent?,
            ) {
                val category = intent?.getStringExtra(ToyotaFCMService.CATEGORY)
                if (ToyotaFCMService.VEHICLE_REMOVED == category || ToyotaFCMService.VEHICLE_ASSOCIATION == category) {
                    dashboardViewModel.onRefresh()
                }
                accountNotificationViewModel.refreshNotificationHistory()
                if (intent?.action == ToyotaConstants.REMOTE_SILENT_PUSH_ACTION) {
                    digitalKeyManageViewModel.getSharedKeys()
                    digitalKeyDownloadViewModel.provideDigitalKeyStatus()
                }
            }
        }

    @OptIn(ExperimentalMaterialApi::class)
    @SuppressLint("UnusedMaterialScaffoldPaddingParameter")
    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        MapsInitializer.initialize(applicationContext)

        handlePushNotifications(intent)
        handleDeepLink(intent.extras)

        val isRefresh = intent?.getBooleanExtra(REFRESH_DASHBOARD, false)
        if (isRefresh == true) {
            dashboardViewModel.sharedDataSource.resetDataSource()
            dashboardViewModel.onRefresh()
        }
        coldAppLaunchCounter = provideColdAppLaunchCounter(applicationContext)
        coldAppLaunchCounter.resetAnnouncementFlag()

        setContent {
            val isDarkMode = AppTheme.darkMode.collectAsState().value
            if (isDarkMode) {
                window.statusBarColor = AppTheme.colors.statusBarColorDark.toArgb()
                WindowCompat
                    .getInsetsController(window, window.decorView)
                    .isAppearanceLightStatusBars = false
            } else {
                window.statusBarColor = AppTheme.colors.statusBarColorLight.toArgb()
                WindowCompat
                    .getInsetsController(window, window.decorView)
                    .isAppearanceLightStatusBars = true
            }
            OADashboardActivityScreen()
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            checkNotificationPermission()
        } else {
            enableProfileSyncUp()
        }

        disposable =
            RxBus.get().toFlowable().subscribe { event ->
                when (event) {
                    is VehicleRemoved -> {
                        // if the app in background, broadcast receiver will be unregistered and remove vehicle will not be updated.
                        if (!isAppInForeGround) {
                            dashboardViewModel.onRefresh()
                            accountNotificationViewModel.refreshNotificationHistory()
                        }
                    }
                }
            }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun checkNotificationPermission() {
        if (PermissionUtil().checkNotificationPermissionStatus(this@OADashboardActivity) != PackageManager.PERMISSION_GRANTED) {
            notificationPermissionLauncher.launch(
                arrayOf(Manifest.permission.POST_NOTIFICATIONS),
            )
        } else {
            enableProfileSyncUp()
        }
    }

    private fun handlePushNotifications(intent: Intent) {
        val vin = intent.getStringExtra(ToyotaFCMService.VIN)
        vin?.let {
            dashboardViewModel.setVin(it)
        }

        val category = intent.getStringExtra(ToyotaFCMService.CATEGORY)

        when {
            ToyotaFCMService.OTA_UPDATES_21MM.equals(category, true) ||
                ToyotaFCMService.TM_UPDATE_CATEGORY.equals(category, true) ||
                ToyotaFCMService.TM_INSTALL_CATEGORY.equals(category, true) ||
                ToyotaFCMService.TM_RELEASE_CATEGORY.equals(category, true) -> {
                dashboardViewModel.setGoToVehicleSoftwareScreen(true)
                dashboardViewModel.deepLinkHandle(category, null)
            }
            ToyotaFCMService.EVGO.equals(category, true) -> {
                dashboardViewModel.deepLinkHandle(category, null)
            }
            ToyotaFCMService.CHARGE_ASSIST_NOTIFICATION.equals(category, true) -> {
                dashboardViewModel.deepLinkHandle(Constants.DEEP_LINK_CHARGE_INFO, null)
            }
        }
        intent.removeExtra(ToyotaFCMService.CATEGORY)
        intent.removeExtra(ToyotaFCMService.VIN)
        if (category.toString().lowercase() == Constants.NotificationCAS) {
            connectedVehicleViewModel.setNavigateStatusTab(true)
            connectedVehicleViewModel.updateCasIsTriggeredFromExternalSource()
        }
    }

    private fun handleDeepLink(arguments: Bundle?) {
        // 21mm Feature start
        val dashBoardRoute: String? = arguments?.getString(ToyotaConstants.DASHBOARD_ROUTE)
        if (!dashBoardRoute.isNullOrBlank()) {
            dashboardViewModel.deepLinkHandle(dashBoardRoute, arguments)
            intent.removeExtra(ToyotaConstants.DASHBOARD_ROUTE)
        }
        val isDeepLinkRouting: Boolean =
            arguments?.getBoolean(
                ToyotaConstants.IS_DEEP_LINK_ROUTING,
                false,
            ) == true
        if (isDeepLinkRouting) {
            if (dashboardViewModel.getSelectedVehicleState().value?.let { it.generation.isNullOrEmpty() } == true) {
                DialogUtil.showDialog(
                    this,
                    null,
                    getString(R.string.DeepLink_Not_Supported_Message),
                    getString(R.string.Common_ok),
                )
                return
            }

            val deepLinkPath =
                arguments?.getString(ToyotaConstants.BRANCH_REFERRING_PARAM)?.let {
                    try {
                        JSONObject(it).optString(ToyotaConstants.BRANCH_DEEPLINK_PATH_KEY)
                    } catch (e: Exception) {
                        LogTool.w("Json Parsing error", e.localizedMessage)
                        null
                    }
                }

            dashboardViewModel.deepLinkHandle(deepLinkPath, arguments)
            intent.removeExtra(ToyotaConstants.IS_DEEP_LINK_ROUTING)
            intent.removeExtra(ToyotaConstants.BRANCH_REFERRING_PARAM)
        }
    }

    override fun onStart() {
        super.onStart()
        BluetoothStateReceiver.register(applicationContext)
        LocalBroadcastManager.getInstance(this).registerReceiver(
            notificationReceiver,
            IntentFilter().apply {
                addAction(ToyotaConstants.ACTION_NOTIFICATION_RECEIVED)
                addAction(ToyotaConstants.VEHICLE_DE_ASSOCIATION_ACTION)
                addAction(ToyotaConstants.REMOTE_SILENT_PUSH_ACTION)
            },
        )
    }

    override fun onStop() {
        super.onStop()
        isAppInForeGround = false
        LocalBroadcastManager.getInstance(this).unregisterReceiver(notificationReceiver)
    }

    override fun getApptentiveActivityInfo(): Activity? = this

    override fun onResume() {
        if (!PermissionUtil.hasBluetoothPermissions(applicationContext)) {
            UserProfileTransferService.stopService(this)
        }
        isAppInForeGround = true
        super.onResume()
        Apptentive.registerApptentiveActivityInfoCallback(this)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == PermissionUtil.BLUETOOTH_PERMISSIONS_REQUEST_CODE &&
            grantResults.sum() == PackageManager.PERMISSION_GRANTED
        ) {
            scanHuBleServices()
        }
    }

    fun signOut() {
        performLogout()
    }

    private fun enableProfileSyncUp() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (PermissionUtil.checkBluetoothRuntimePermissions(this)) {
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Bluetooth runtime permissions granted")
                scanHuBleServices()
            }
        } else {
            scanHuBleServices()
        }
    }

    private fun scanHuBleServices() {
        if (checkBleScanEnabled()) {
            checkPermissionsBasic(onGranted = {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    checkPermissionsWithOverlay()
                }
                (application as ToyotaApplication).confirmAutoLaunchPermissions()
                UserProfileTransferService.startService(applicationContext)
            })
        }
    }

    private fun checkBleScanEnabled(): Boolean {
        val btManager = getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager?
        val btAdapter = btManager?.adapter
        return btManager != null &&
            !RootUtil.checkEmulator() &&
            btAdapter != null &&
            btAdapter.isEnabled
    }

    private fun checkPermissionsBasic(onGranted: () -> Unit) {
        if (this.checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED ||
            this.checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED
        ) {
            PermissionUtil.checkLocationPermissions(this) { permission ->
                if (permission.granted) {
                    onGranted()
                } else if (!permission.shouldShowRequestPermissionRationale) {
                    backGroundLocationDialog(onConfirm = {
                        showForceAllowPermissionDialog()
                    }, onCancel = { showForceAllowPermissionDialog() })
                }
            }
        } else {
            onGranted()
        }
    }

    private fun backGroundLocationDialog(
        onConfirm: () -> Unit,
        onCancel: () -> Unit,
    ) {
        val backgroundLocationBuilder: MaterialAlertDialogBuilder =
            MaterialAlertDialogBuilder(this)
                .setTitle(getString(R.string.Background_Location_Permission_Request_Dialog_Title))
                .setMessage(
                    String.format(
                        getString(R.string.Background_Location_Permission_Request_Dialog_Message),
                        getString(R.string.app_name),
                    ),
                ).setPositiveButton(
                    R.string.Common_confirm,
                ) { _, _ ->
                    onConfirm()
                }.setNegativeButton(
                    R.string.Common_later,
                ) { _, _ ->
                    onCancel()
                }.setNeutralButton(
                    R.string.deeplink_verification_don_ask_me,
                ) { _, _ -> preferenceModel.setBackgroundLocationPermissionShown() }
                .setCancelable(false)

        if (!preferenceModel.isBackgroundLocationPermissionShown()) backgroundLocationBuilder.show()
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    fun checkPermissionsWithOverlay() {
        if (checkSelfPermission(Manifest.permission.ACCESS_BACKGROUND_LOCATION)
            != PackageManager.PERMISSION_GRANTED
        ) {
            backGroundLocationDialog(onConfirm = {
                PermissionUtil.checkLocationPermissions(this) { permission ->
                    if (permission.granted) {
                        locationPermissionRequest.launch(
                            arrayOf(Manifest.permission.ACCESS_BACKGROUND_LOCATION),
                        )
                    } else if (!permission.shouldShowRequestPermissionRationale) {
                        showForceAllowPermissionDialog()
                    }
                }
            }, onCancel = {})
        } else {
            checkOverlayPermission()
        }
    }

    private fun checkOverlayPermission(): Boolean {
        var isGranted = true
        if (!Settings.canDrawOverlays(this)) {
            DialogUtil.showDialog(
                this,
                null,
                getString(R.string.Common_auto_launch_permission_msg),
                activityContext.getString(toyotaone.commonlib.R.string.Common_ok),
                activityContext.getString(toyotaone.commonlib.R.string.Common_cancel),
                object : OnCusDialogInterface {
                    override fun onConfirmClick() {
                        intentToOverlaySettings()
                    }

                    override fun onCancelClick() {
                        // no cancel event
                    }
                },
                false,
            )
            isGranted = false
        }
        return isGranted
    }

    private fun intentToOverlaySettings() {
        try {
            val intent =
                Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:" + application.packageName),
                )
            startActivity(intent)
        } catch (e: java.lang.Exception) {
            LogTool.e(DashboardFlutterActivity::class.java.simpleName, e.localizedMessage, e)
        }
    }

    fun navigateToAppSuite(vehicle: VehicleInfo) {
        if (vehicle.isXcappEnabled) {
            setVehicleToXCAPP(vehicle)
            startActivity(Intent(this, XcappActivity::class.java))
        }
    }

    private fun setVehicleToXCAPP(vehicle: VehicleInfo) {
        if (vehicle.isXcappEnabled && XcappManagerProvider.isXcappInitialized()) {
            val vin = vehicle.vin
            val region = vehicle.region
            val brand =
                if (vehicle.brand.startsWith("T", true)) {
                    getString(R.string.Common_toyota).uppercase()
                } else {
                    getString(
                        R.string.Common_lexus,
                    ).uppercase()
                }
            if (vin != null && region != null) {
                val xcappManager = XcappManagerProvider.xcappManager
                xcappManager.setVehicle(vin, brand, region)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::disposable.isInitialized) {
            disposable.dispose()
        }
    }
}
