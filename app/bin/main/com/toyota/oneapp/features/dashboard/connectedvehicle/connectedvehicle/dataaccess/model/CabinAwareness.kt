package com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.model.CasEvent
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.model.CasPageDestination

data class CabinAwareness(
    @SerializedName("payload") var payload: CAS?,
)

data class CAS(
    @SerializedName("event") var event: Event? = null,
    @SerializedName("feedbackTile") var feedbackTile: FeedbackTile? = null,
)

data class FeedbackTile(
    @SerializedName("pageTitle") var pageTitle: String? = null,
    @SerializedName("reasons") var reasons: ArrayList<String> = arrayListOf(),
    @SerializedName("disclaimer") var discliamer: String? = null,
    @SerializedName("positiveCTA") val positiveCTA: String? = null,
    @SerializedName("negativeCTA") val negativeCTA: String? = null,
    @SerializedName("selectionInLineError") val selectionInLineError: String? = null,
    @SerializedName("provideDetailsTitle") val provideDetailsTitle: String? = null,
    @SerializedName("provideDetailsInLineError") val provideDetailsInLineError: String? = null,
)

data class Event(
    @SerializedName("id") var id: String? = null,
    @SerializedName("title") var title: String? = null,
    @SerializedName("subTitle") var subTitle: String? = null,
    @SerializedName("active") var active: Boolean? = false,
    @SerializedName("userProvidedFeedback") var isUserProvidedFeedback: Boolean? = false,
    @SerializedName("tile") var details: Details? = null,
    @SerializedName("eventDetails") val eventDetails: EventDetails? = null,
)

data class Details(
    @SerializedName("pageTitle") var pageTitle: String? = null,
    @SerializedName("title") var title: String? = null,
    @SerializedName("description") var description: String? = null,
    @SerializedName("disclaimer") var discliamer: String? = null,
)

data class EventDetails(
    @SerializedName("header") val header: String? = null,
    @SerializedName("title") val title: String? = null,
    @SerializedName("description") val description: String? = null,
    @SerializedName("sections") val sections: ArrayList<EventSection> = arrayListOf(),
)

data class EventSection(
    @SerializedName("title") val title: String? = null,
    @SerializedName("description") val description: String? = null,
    @SerializedName("hyperLinkExist") val hyperLinkExist: Boolean? = null,
    @SerializedName("hyperLink") val reasons: ArrayList<EventSectionDetails> = arrayListOf(),
)

data class EventSectionDetails(
    @SerializedName("text") val text: String? = null,
    @SerializedName("falseDetection") val falseDetection: Boolean? = null,
    @SerializedName("ownersManual") val ownersManual: Boolean? = null,
    @SerializedName("notificationSettings") val notificationSettings: Boolean? = null,
)

data class CASFeedback(
    @SerializedName("eventViewed") var eventViewed: Boolean = true,
    @SerializedName("userAcknowledged") var userAcknowledged: Boolean = true,
    @SerializedName("eventId") var eventId: String? = null,
    @SerializedName("feedback") var feedback: FeedbackDetails? = FeedbackDetails(),
)

data class FeedbackDetails(
    @SerializedName("reason") var reason: String? = null,
    @SerializedName("details") var details: String? = null,
)

data class CASEvent(
    @SerializedName("userAcknowledged") var userAcknowledged: Boolean = false,
    @SerializedName("eventId") var eventId: String? = null,
    @SerializedName("eventViewed") var eventViewed: Boolean = false,
)

fun getPageDestination(reason: ArrayList<EventSectionDetails>): CasPageDestination? {
    return if (reason.firstOrNull()?.ownersManual == true) {
        CasPageDestination.OWNERS_MANUAL
    } else if (reason.firstOrNull()?.notificationSettings == true) {
        CasPageDestination.NOTIFICATION_SETTINGS
    } else if (reason.firstOrNull()?.falseDetection == true) {
        CasPageDestination.REPORT_FLOW
    } else {
        null
    }
}

fun getEvents(sections: ArrayList<EventSection>?): ArrayList<CasEvent> {
    val events: ArrayList<CasEvent> = arrayListOf()
    sections?.forEach { section ->
        events.add(
            CasEvent(
                title = section.title,
                description = section.description,
                isHyperLinkExists = section.hyperLinkExist ?: false,
                hyperLinkText = section.reasons.firstOrNull()?.text,
                pageDestination = getPageDestination(section.reasons),
            ),
        )
    }
    return events
}
