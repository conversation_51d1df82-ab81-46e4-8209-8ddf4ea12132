package com.toyota.oneapp.features.dashboard.dashboard.domain.repository

import com.toyota.oneapp.features.dashboard.dashboard.dataaccess.servermodel.TelemetryResponse
import com.toyota.oneapp.features.vehicleinfo.dataaccess.servermodel.SoftwareUpdateResponse
import com.toyota.oneapp.features.vehicleswitcher.dataaccess.servermodel.VehicleListResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import kotlin.coroutines.CoroutineContext

abstract class DashboardRepository(
    errorParser: ErrorMessageParser,
    ioContext: CoroutineContext,
) : BaseRepository(errorParser, ioContext) {
    abstract suspend fun vehiclesList(): Resource<VehicleListResponse?>

    abstract suspend fun fetchTelemetry(
        vin: String,
        generation: String,
        brand: String,
        region: String,
    ): Resource<TelemetryResponse?>

    abstract suspend fun fetchSoftwareUpdates(vin: String): Resource<SoftwareUpdateResponse?>
}
