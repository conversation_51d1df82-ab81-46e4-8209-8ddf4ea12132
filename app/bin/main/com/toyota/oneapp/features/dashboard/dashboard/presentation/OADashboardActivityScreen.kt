/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.dashboard.presentation

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.material.BottomSheetScaffoldState
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Scaffold
import androidx.compose.material.SnackbarHostState
import androidx.compose.material.rememberBottomSheetScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.google.gson.Gson
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyManageViewModel
import com.toyota.oneapp.features.bottomnavigation.domain.model.OABottomNavItem
import com.toyota.oneapp.features.bottomnavigation.presentation.OABottomNavigation
import com.toyota.oneapp.features.chargeassist.ChargeAssistRoute
import com.toyota.oneapp.features.chargeassist.view.helper.provideColdAppLaunchCounter
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_SUCCESSFUL_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.NOT_FOUND
import com.toyota.oneapp.features.chargeassist.view.screens.entrypoint1.ChargeAssistAnnouncementBottomSheet
import com.toyota.oneapp.features.chargeassist.view.screens.entrypoint2.getFinalStatus
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.chargeinfo.navigation.ChargeInfoRoute
import com.toyota.oneapp.features.core.composable.ShowSuccessSnackBar
import com.toyota.oneapp.features.core.navigation.OANavController
import com.toyota.oneapp.features.core.navigation.OANavigationGraph
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.OAThemeProvider
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.ModalBottomSheets
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import com.toyota.oneapp.features.core.util.launchSecondaryBottomSheetAction
import com.toyota.oneapp.features.dashboard.announcement.application.AnnouncementState
import com.toyota.oneapp.features.dashboard.announcement.presentation.AnnouncementScreen
import com.toyota.oneapp.features.dashboard.announcement.presentation.AnnouncementViewModel
import com.toyota.oneapp.features.dashboard.announcement.presentation.GetMarketingBannerLocation
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.AdvanceRemoteViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.utils.ShortCutActions
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.ConnectedVehicleTabs
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.presentation.ConnectedVehicleViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DigitalKeyShareViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotepark.presentation.RemoteParkViewModel
import com.toyota.oneapp.features.dashboard.dashboard.domain.model.DeepLinkState
import com.toyota.oneapp.features.dashboard.dashboard.presentation.helper.AdvanceRemoteBottomSheet
import com.toyota.oneapp.features.dashboard.util.DashboardConstants
import com.toyota.oneapp.features.topnav.presentation.TopNavScreen
import com.toyota.oneapp.features.vehiclestatus.presentation.OnLifecycleEvent
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.accountsettings.LegalSelectVehicleActivity
import com.toyota.oneapp.ui.accountsettings.LinkedAccountsActivity
import com.toyota.oneapp.ui.accountsettings.ResetPinActivity
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.vehiclesupport.toServiceDealer
import com.toyota.oneapp.ui.destinations.DestinationsActivity
import com.toyota.oneapp.ui.flutter.DashboardFlutterActivity
import com.toyota.oneapp.ui.flutter.GO_TO_APPOINTMENT_DETAIL
import com.toyota.oneapp.ui.flutter.GO_TO_EV_CHARGE_INFO_PAGE
import com.toyota.oneapp.ui.flutter.GO_TO_GUEST_DRIVER_DETAIL
import com.toyota.oneapp.ui.vinscan.QRScanActivity
import com.toyota.oneapp.util.IntentUtil
import com.toyota.oneapp.util.NavigationUtil
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import toyotaone.commonlib.permission.PermissionUtil

@SuppressLint("StateFlowValueCalledInComposition")
@ExperimentalMaterialApi
@Composable
fun OADashboardActivityScreen() {
    val dashboardViewModel = (LocalContext.current as OADashboardActivity).dashboardViewModel
    dashboardViewModel.updateDarkMode()

    val navHostController = rememberNavController()

    val digitalKeyShareViewModel = hiltViewModel<DigitalKeyShareViewModel>()

    val digitalKeyManageViewModel = hiltViewModel<DigitalKeyManageViewModel>()

    val announcementViewModel = hiltViewModel<AnnouncementViewModel>()

    val advanceRemoteViewModel = hiltViewModel<AdvanceRemoteViewModel>()

    val hasVehicle = dashboardViewModel.hasVehicle() || dashboardViewModel.isSecondaryVehicle()

    val context = LocalContext.current

    val remoteParkViewModel = hiltViewModel<RemoteParkViewModel>()

    val snackbarHostState = remember { SnackbarHostState() }
    var snackBarText by remember { mutableStateOf(0) }

    LaunchedEffect(Unit) {
        advanceRemoteViewModel.showSnackBar.collectLatest {
            if (it != 0) {
                snackBarText = it
                snackbarHostState.showSnackbar(ToyotaConstants.EMPTY_STRING)
                advanceRemoteViewModel.updateSnackBar(0)
            }
        }
    }

    val quickAction = dashboardViewModel.quickAction.value

    val notificationEnabled =
        PermissionUtil().checkNotificationPermissionStatus(context) == PackageManager.PERMISSION_GRANTED

    LaunchedEffect(quickAction) {
        if (containsShortCutAction(quickAction)) {
            val data =
                dashboardViewModel.getQuickActionNotSupportedMessage(
                    notificationEnabled,
                    dashboardViewModel.applicationData.getSelectedVehicle(),
                    quickAction,
                )
            if (data != 0) {
                snackBarText = data
                snackbarHostState.showSnackbar(ToyotaConstants.EMPTY_STRING)
            }
        }
    }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_CREATE -> {
                dashboardViewModel.syncDigitalKeyStatus()
            }

            else -> {}
        }
    }
    initRemoteParkingControl(
        hasVehicle,
        remoteParkViewModel.isRemoteParkEnabled(),
        digitalKeyShareViewModel.isSecondaryKey(),
        remoteParkViewModel,
    )

    val vehicleInfo: VehicleInfo? =
        dashboardViewModel.getSelectedVehicleState().collectAsState().value

    val isPendingInviteLeft = digitalKeyManageViewModel.isPendingInviteLeft.collectAsState()

    isPendingInviteLeft?.let {
        if (it.value) {
            digitalKeyManageViewModel.showPendingInviteTasks(
                activity = LocalContext.current as FragmentActivity,
            )
        }
    }

    OAThemeProvider(isDarkMode = AppTheme.darkMode.collectAsState().value) {
        val coroutineScope = rememberCoroutineScope()
        val connectedVehicleViewModel =
            (LocalContext.current as OADashboardActivity).connectedVehicleViewModel

        val advanceRemoteBottomSheetState = rememberBottomSheetScaffoldState()
        val navRoutes =
            when {
                vehicleInfo != null ->
                    listOf(
                        OABottomNavItem.Service.route,
                        OABottomNavItem.Pay.route,
                        OABottomNavItem.Home.route,
                        OABottomNavItem.Shop.route,
                        OABottomNavItem.Find.route,
                    )

                else -> emptyList()
            }

        val currentDestination =
            navHostController
                .currentBackStackEntryAsState()
                .value
                ?.destination
                ?.route
        val shouldEnableTopNav =
            shouldEnableTopNav(
                navRoutes,
                currentDestination ?: OABottomNavItem.Home.route,
            ) ||
                !hasVehicle
        val shouldEnableBottomNav =
            shouldEnableBottomNav(
                navRoutes,
                currentDestination ?: OABottomNavItem.Home.route,
            )
        val shouldEnableRemote =
            shouldEnableRemote(
                navRoutes,
                currentDestination ?: OABottomNavItem.Home.route,
            ) ||
                digitalKeyShareViewModel.isSecondaryKey()

        val showAdvanceRemoteScreen: Boolean =
            (navHostController.currentBackStackEntry?.destination?.route == OAScreen.Home.route) &&
                hasVehicle

        val bottomBar: @Composable (() -> Unit) = { OABottomNavigation(navHostController) }

        val topAppBar: @Composable (() -> Unit) = {
            TopNavScreen(
                navHostController = navHostController,
            )
        }

        val navContent: @Composable (() -> Unit) = {
            OANavigationGraph(
                navController = navHostController,
                analyticsLogger = dashboardViewModel.analyticsLogger,
            )

            HandleDeepLink(
                dashboardViewModel = dashboardViewModel,
                digitalKeyManageViewModel = digitalKeyManageViewModel,
                connectedVehicleViewModel = connectedVehicleViewModel,
                navHostController = navHostController,
            )

            AnnouncementBottomSheet(
                announcementViewModel = announcementViewModel,
                coroutineScope = coroutineScope,
                dashboardViewModel = dashboardViewModel,
            )
        }

        val oaDashboardContent: @Composable (() -> Unit) = {
            Scaffold(
                modifier = Modifier.background(AppTheme.colors.tertiary12),
                topBar = {
                    if (shouldEnableTopNav) {
                        topAppBar()
                    }
                },
                bottomBar = {
                    if (shouldEnableBottomNav) bottomBar()
                },
            ) { innerPadding ->
                Box(modifier = Modifier.padding(innerPadding)) {
                    navContent()
                }
            }

            if (showAdvanceRemoteScreen) {
                if (digitalKeyShareViewModel.isSecondaryKey()) {
                    digitalKeyShareViewModel.provideShareDigitalKeyStatus()
                }
                CompositionLocalProvider(OANavController provides navHostController) {
                    AdvanceRemoteBottomSheet(
                        shouldEnableRemote,
                        bottomBar = bottomBar,
                        topAppBar = topAppBar,
                        content = navContent,
                        bottomSheetScaffoldState = advanceRemoteBottomSheetState,
                        navHostController = navHostController,
                    )
                }
            }
        }

        ModalBottomSheets(content = oaDashboardContent)
        HandleBackNavigation(
            coroutineScope,
            advanceRemoteBottomSheetState,
            navHostController,
            dashboardViewModel,
            connectedVehicleViewModel,
        )
    }
    GetMarketingBannerLocation(context = context, dashboardViewModel, announcementViewModel)

    ShowSuccessSnackBar(
        snackbarHostState,
        message = ToyotaConstants.EMPTY_STRING,
        messageFromResource = snackBarText,
        icon = R.drawable.ic_close_white_transparent,
    )

    // CA Backend Flag
    val caBackEndFlag = vehicleInfo?.isFeatureEnabled(Feature.CHARGE_ASSIST)
    if (caBackEndFlag == true) {
        /*
        Charge Assist Bottom sheet contains view and logic
        which will ultimate trigger surfacing
         */
        val launchCounter = remember { provideColdAppLaunchCounter(context) }
        ChargeAssistAnnouncementBottomSheet(
            navHostController = navHostController,
            dashboardViewModel = dashboardViewModel,
            launchCounter,
        )
    }
}

fun initRemoteParkingControl(
    hasVehicle: Boolean,
    isRemoteParkEnabled: Boolean,
    isSecondaryKey: Boolean,
    remoteParkViewModel: RemoteParkViewModel,
) {
    if (hasVehicle && isRemoteParkEnabled && !isSecondaryKey) {
        remoteParkViewModel.initRemoteParkingControl()
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun HandleBackNavigation(
    coroutineScope: CoroutineScope,
    advanceRemoteBottomSheetState: BottomSheetScaffoldState,
    navHostController: NavHostController,
    dashboardViewModel: DashboardViewModel,
    connectedVehicleViewModel: ConnectedVehicleViewModel,
) {
    val context = LocalContext.current
    BackHandler {
        coroutineScope.launch {
            if (advanceRemoteBottomSheetState.bottomSheetState.isExpanded) {
                /**
                 * If the advance remote bottomsheet open in full size it will be display half of the screen while click
                 * the back button
                 */
                advanceRemoteBottomSheetState.bottomSheetState.collapse()
            } else if (navHostController.currentBackStackEntry?.destination?.route == OAScreen.Home.route) {
                /**
                 * Handled non connected vehicle back handler
                 */
                if (dashboardViewModel.isNonConnectedVehicle.value) {
                    (context as OADashboardActivity).finish()
                } else {
                    /**
                     * Handled connected vehicle tab button back handler
                     * Finish App when the current tab is Remote
                     */
                    if (connectedVehicleViewModel.getCurrentTab() == ConnectedVehicleTabs.REMOTE.ordinal) {
                        (context as OADashboardActivity).finish()
                    }
                }
            } else {
                /**
                 *If the current screen not in home screen moved to home screen
                 */
                navHostController.navigate(OAScreen.Home.route) {
                    popUpTo(navHostController.graph.id) {
                        inclusive = true
                    }
                }
            }
        }
    }
}

@SuppressLint("CoroutineCreationDuringComposition")
@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun HandleDeepLink(
    dashboardViewModel: DashboardViewModel,
    digitalKeyManageViewModel: DigitalKeyManageViewModel,
    connectedVehicleViewModel: ConnectedVehicleViewModel,
    navHostController: NavHostController,
) {
    val activity = LocalContext.current as FragmentActivity
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    val bottomSheet = LocalBottomSheet.current

    when (val dashboardState = dashboardViewModel.deepLinkStateFlow.collectAsState().value) {
        is DeepLinkState.NavigateToManageSubscription -> {
            navHostController.navigate(OAScreen.Subscriptions.route)
        }

        is DeepLinkState.NavigateToLastParked -> {
            navHostController.navigate(OAScreen.Find.route)
        }

        is DeepLinkState.NavigateToChargeInfo -> {
            navHostController.navigate(OAScreen.ChargeInfo.route)
        }

        is DeepLinkState.NavigateToChargeAssistEnrollmentStatusPath -> {
            val chargeAssistViewModel = hiltViewModel<ChargeAssistViewModel>()
            coroutineScope.launch {
                chargeAssistViewModel.chargeAssistVMHelper.handleChargeAssistEligibilityRouting()
                val routeToNavigateTo =
                    chargeAssistViewModel
                        .chargeAssistVMHelper.routeForEnrollmentStatusDeepLink.value
                navHostController.navigate(routeToNavigateTo)
            }
        }

        is DeepLinkState.NavigateToVehicleStatus -> {
            coroutineScope.launch {
                connectedVehicleViewModel.setNavigateStatusTab(true)
                connectedVehicleViewModel.updateCasIsTriggeredFromExternalSource()
            }
        }

        is DeepLinkState.NavigateToAccountSettings -> {
            coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                OADashboardBottomSheetScreen(bottomSheetState = it, false)
            }
        }

        is DeepLinkState.NavigateToVehicleInfo -> {
            coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                OADashboardBottomSheetScreen(bottomSheetState = it, true)
            }
        }

        is DeepLinkState.NavigateToGuestDriverSettings -> {
            context.startActivity(
                DashboardFlutterActivity.createIntent(context, null, GO_TO_GUEST_DRIVER_DETAIL),
            )
        }

        is DeepLinkState.NavigateToScheduleService -> {
            if (BuildConfig.DEALER_SERVICE_FLUTTER_DEPRECATION) {
                navHostController.navigate(OAScreen.DealerServiceAppointment.route)
            } else {
                context.startActivity(
                    DashboardFlutterActivity.createIntent(context, null, GO_TO_APPOINTMENT_DETAIL),
                )
            }
        }

        is DeepLinkState.NavigateToEVGO -> {
            val bundle = Bundle()
            val data = HashMap<String, Any?>()
            data["isChargeNow"] = false
            bundle.putString(
                ToyotaConstants.FLUTTER_DATA,
                Gson().toJson(data),
            )
            context.startActivity(
                DashboardFlutterActivity.createIntent(context, bundle, GO_TO_EV_CHARGE_INFO_PAGE),
            )
        }

        is DeepLinkState.NavigateToLinkedAccounts -> {
            context.startActivity(Intent(context, LinkedAccountsActivity::class.java))
        }

        is DeepLinkState.NavigateToPinReset -> {
            context.startActivity(Intent(context, ResetPinActivity::class.java))
        }

        is DeepLinkState.NavigateToPrivacyPortal -> {
            val intent =
                if (dashboardViewModel.applicationData.getVehicleList()?.size == 1) {
                    IntentUtil.getEditConsentIntent(
                        context,
                        dashboardViewModel.applicationData.getSelectedVehicle(),
                    )
                } else {
                    Intent(context, LegalSelectVehicleActivity::class.java)
                }
            context.startActivity(intent)
        }

        is DeepLinkState.NavigateToManageDigitalKey -> {
            digitalKeyManageViewModel.showPendingInviteTasks(activity)
        }

        is DeepLinkState.NavigateToSharePOI -> {
            val intent = Intent(context, DestinationsActivity::class.java)
            intent.putExtra(
                ToyotaConstants.DASHBOARD_ROUTE,
                stringResource(R.string.deep_link_share_poi),
            )
            intent.putExtra(
                ToyotaConstants.DASHBOARD_POI_DATA,
                dashboardState.bundle?.getString(Intent.EXTRA_TEXT),
            )
            context.startActivity(intent)
        }

        is DeepLinkState.NavigateToConnectByCode -> {
            val intent =
                Intent(context, QRScanActivity::class.java).apply {
                    putExtra(QRScanActivity.SHOW_QR_CODE, false)
                }
            context.startActivity(intent)
        }

        is DeepLinkState.NavigateToPreferredDealers -> {
            navigateToPreferredDealer(
                context = context,
                data = dashboardState.data,
                navHostController = navHostController,
            )
        }

        else -> {}
    }
    dashboardViewModel.resetDeepLinkStateFlow()
}

private fun navigateToPreferredDealer(
    context: Context,
    data: Bundle?,
    navHostController: NavHostController,
) {
    NavigationUtil.navigateToDealers(
        context = context,
        navController = navHostController,
        preferredDealer = data?.toServiceDealer(),
    )
}

private suspend fun handleChargeAssistEligibilityRouting(
    isEligibleForCAInfo: Boolean,
    chargeAssistViewModel: ChargeAssistViewModel,
    navHostController: NavHostController,
) {
    val userSystemZip = chargeAssistViewModel.chargeAssistVMHelper.profileInfoZipCode.filterNotNull().first()
    val enrollmentStatusInfo = chargeAssistViewModel.enrollmentStatusData.filterNotNull().first()
    val enrollmentStatus = getFinalStatus(enrollmentStatusInfo)

    when (enrollmentStatus) {
        in ENROLLMENT_SUCCESSFUL_STATUSES -> {
            // navigate to Charge Info
            navHostController.navigate(ChargeInfoRoute.ChargeInfoScreen.route)
        }
        NOT_FOUND -> {
            if (isEligibleForCAInfo) {
                // navigate to Charge Assist Learn More
                navHostController.navigate(
                    ChargeAssistRoute.LearnMoreScreen.route +
                        "/$userSystemZip/false",
                )
            }
        }
    }
}

@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun AnnouncementBottomSheet(
    announcementViewModel: AnnouncementViewModel,
    coroutineScope: CoroutineScope,
    dashboardViewModel: DashboardViewModel,
) {
    val bottomSheet = LocalBottomSheet.current
    val announcementState = announcementViewModel.announcementState.collectAsState()
    val selectedVehicleInfo = dashboardViewModel.getSelectedVehicleState().collectAsState()
    LaunchedEffect(bottomSheet.secondarySheetState.value) {
        snapshotFlow { bottomSheet.secondarySheetState.value.isVisible }.collect { isVisible ->
            if (!isVisible && announcementState.value is AnnouncementState.Success) {
                selectedVehicleInfo.value?.let {
                    announcementViewModel.storeAnnouncementClosedTime(it.vin)
                    announcementViewModel.updateAnnouncementStatus()
                }
            }
        }
    }

    when (announcementState.value) {
        is AnnouncementState.Success -> {
            val announcementList =
                MutableStateFlow(
                    (announcementState.value as AnnouncementState.Success).announcements,
                ).collectAsState()
            val selectedVehicle =
                (announcementState.value as AnnouncementState.Success).selectedVehicle
            if (announcementList.value.isNotEmpty() && !bottomSheet.secondarySheetState.value.isVisible) {
                coroutineScope.launchSecondaryBottomSheetAction(
                    bottomSheet,
                    screen_id = DashboardConstants.ID_ANNOUNCEMENT_BOTTOM_SHEET,
                ) { sheetState ->
                    AnnouncementScreen(
                        sheetState,
                        announcementList = announcementList.value,
                        selectedVehicle = selectedVehicle,
                        announcementViewModel,
                    )
                }
            }
        }

        else -> {}
    }
}

private fun shouldEnableTopNav(
    routeList: List<String>,
    currentRoute: String,
): Boolean = currentRoute != OABottomNavItem.Find.route && currentRoute in routeList

private fun shouldEnableBottomNav(
    routeList: List<String>,
    currentRoute: String,
): Boolean = currentRoute in routeList

private fun shouldEnableRemote(
    routeList: List<String>,
    currentRoute: String,
): Boolean = currentRoute in routeList

private fun containsShortCutAction(input: String?): Boolean {
    if (input.isNullOrEmpty()) {
        return false
    }
    for (enumValue in ShortCutActions.values()) {
        if (input.contains(enumValue.action)) {
            return true
        }
    }
    return false
}
