package com.toyota.oneapp.features.dashboard.dashboard.dataaccess.service

import com.toyota.oneapp.features.dashboard.dashboard.dataaccess.servermodel.TelemetryResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header

interface TelemetryApi {
    @GET("/oneapi/v2/telemetry")
    suspend fun fetchTelemetryApi(
        @Header("vin") vin: String?,
        @Header("generation") generation: String?,
        @Header("x-BRAND") brand: String?,
        @Header("x-region") region: String?,
    ): Response<TelemetryResponse?>
}
