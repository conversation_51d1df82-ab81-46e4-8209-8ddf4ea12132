package com.toyota.oneapp.features.dashboard.dashboard.application

import com.toyota.oneapp.features.dashboard.dashboard.domain.model.Telemetry

sealed class TelemetryState {
    object Idle : TelemetryState()

    object Loading : TelemetryState()

    object NotAvailable : TelemetryState()

    class Success(val telemetryPayload: Telemetry?) : TelemetryState()

    class Error(val errorCode: String? = null, val errorMessage: String? = null) : TelemetryState()
}
