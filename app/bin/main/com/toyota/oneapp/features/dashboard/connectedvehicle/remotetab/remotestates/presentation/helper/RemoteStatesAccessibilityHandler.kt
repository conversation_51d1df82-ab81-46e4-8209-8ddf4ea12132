package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.helper

import android.content.Context
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.PopupStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.RemotePopupStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.States
import com.toyota.oneapp.util.ToyotaConstants

fun getRemoteStatesAccessibilityId(status: States): String =
    when (status) {
        States.ACTIVATE_REMOTE -> AccessibilityId.ID_RS_AUTH_REQUIRED_ACTIVATE_BUTTON
        States.SUBSCRIPTION_CANCELLED -> AccessibilityId.ID_RS_SUBSCRIPTION_CANCELLED_RENEW_BUTTON
        States.ACTIVATE_FAILED -> AccessibilityId.ID_RS_ACTIVATE_FAILED_ACTIVATE_BUTTON
        States.ACTIVATION_PENDING -> AccessibilityId.ID_RS_ACTIVATE_PENDING_REFRESH_BUTTON
        States.ACTIVATION_ERROR -> AccessibilityId.ID_RS_ERROR_CONTACT_US_BUTTON
        States.SUBSCRIPTION_EXPIRED -> AccessibilityId.ID_RS_SUBSCRIPTION_EXPIRED_RENEW_BUTTON
        States.VEHICLE_STOLEN -> AccessibilityId.ID_RS_STOLEN_REACTIVATE_BUTTON
        States.NOTIFICATIONS_DISABLED -> AccessibilityId.ID_RS_NOTIFICATION_TURN_ON_BUTTON
        States.REMOTE_SHARED -> AccessibilityId.ID_RS_REMOTE_SHARE_ENABLED
        else -> ToyotaConstants.EMPTY_STRING
    }

fun getRemoteStatesPopupAccessibilityId(
    value: RemotePopupStates,
    context: Context,
): Any {
    val isCancelCTA = (
        value.ctaSecondaryText != 0 &&
            (
                value.ctaSecondaryText?.let { context.getString(it) } ==
                    context.getString(
                        R.string.Common_cancel,
                    )
            )
    )
    return when (value.popupStatus) {
        PopupStates.REMOTE_SHARED -> AccessibilityId.ID_RS_REMOTE_SHARE_SHEET_REMOVE_BUTTON
        PopupStates.VEHICLE_STOLEN ->
            if (isCancelCTA) {
                AccessibilityId.ID_RS_STOLEN_SHEET_CANCEL_BUTTON
            } else {
                AccessibilityId.ID_RS_STOLEN_SHEET_REACTIVATE_BUTTON
            }
        PopupStates.AUTH_REQUIRED_LMEX -> AccessibilityId.ID_RS_AUTH_REQ_SHEET_OKAY_BUTTON
        else -> ToyotaConstants.EMPTY_STRING
    }
}
