package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension

import com.toyota.oneapp.R
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application.AdvanceRemoteState
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.ProgressState
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteProgress
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.AdvanceRemoteViewModel
import kotlinx.coroutines.flow.update
import toyotaone.commonlib.log.LogTool

private const val TAG = "REMOTE_PROGRESS"

fun AdvanceRemoteViewModel.disableAllCommands() {
    try {
        remoteList.update {
            remoteList.value.toMutableList().apply {
                this.map { item ->
                    item.apply { disableCommand = true }
                }
            }
        }
        mRemoteCommandsState.value = AdvanceRemoteState.Success(remoteList)
    } catch (exception: Exception) {
        LogTool.d(TAG, exception.message)
    }
}

fun getProgressTextForBar(commandType: RemoteCommandType): Int =
    when (commandType) {
        RemoteCommandType.DoorLock -> R.string.Dashboard_locking
        RemoteCommandType.DoorUnlock -> R.string.Dashboard_unlocking
        RemoteCommandType.EngineStart -> R.string.remote_starting
        RemoteCommandType.EngineStop -> R.string.remote_stopping
        RemoteCommandType.TrunkLock -> R.string.trunk_locking
        RemoteCommandType.TrunkUnlock -> R.string.trunk_un_locking
        RemoteCommandType.TailgateLock -> R.string.tailgate_locking
        RemoteCommandType.TailgateUnlock -> R.string.tailgate_un_locking
        RemoteCommandType.Lights -> R.string.lights_on
        RemoteCommandType.Hazard -> R.string.hazards_on
        RemoteCommandType.Buzzer -> R.string.buzzer_on
        RemoteCommandType.Horn -> R.string.horn_on
        else -> R.string.remote_connecting
    }

fun AdvanceRemoteViewModel.enableAllCommands() {
    try {
        remoteList.update {
            remoteList.value.toMutableList().apply {
                this.map { item ->
                    item.apply { disableCommand = false }
                }
            }
        }
        mRemoteCommandsState.value = AdvanceRemoteState.Success(remoteList)
    } catch (exception: Exception) {
        LogTool.d(TAG, exception.message)
    }
}

fun AdvanceRemoteViewModel.showDialogAndPrepareStartCommand() {
    try {
        remoteList.update {
            remoteList.value.toMutableList().apply {
                this.map { item ->
                    item.apply { disableCommand = true }
                }
            }
        }
        mRemoteCommandsState.value = AdvanceRemoteState.Success(remoteList)
    } catch (exception: Exception) {
        LogTool.d(TAG, exception.message)
    }
}

fun AdvanceRemoteViewModel.updateProgressBar(
    title: Int,
    progressState: ProgressState = ProgressState.IDLE,
    isRemoteStop: Boolean = false,
) {
    mRemoteCommandProgressState.value =
        RemoteProgress(
            title = title,
            progressState = progressState,
            isRemoteStop = isRemoteStop,
        )
}
