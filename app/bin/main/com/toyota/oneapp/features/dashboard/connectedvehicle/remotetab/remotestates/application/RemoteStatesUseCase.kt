package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.application

import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.RemotePopupStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.RemoteStates
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.coroutines.flow.Flow

abstract class RemoteStatesUseCase {
    // POST API call to assign the remote user
    abstract fun assignRemoteUser(vehicleInfo: VehicleInfo): Flow<Resource<BaseResponse?>>

    // Check vehicle info remote display value and get remote states
    abstract fun getRemoteStates(vehicleInfo: VehicleInfo): RemoteStates

    // Check remote states and get pop up accordingly
    abstract fun updateRemoteStatePopup(remoteStates: RemoteStates): RemotePopupStates

    abstract fun isLMEXPhase2(selectedVehicle: VehicleInfo): <PERSON><PERSON><PERSON>
}
