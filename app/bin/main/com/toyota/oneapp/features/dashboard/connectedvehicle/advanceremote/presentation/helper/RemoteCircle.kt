package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.helper

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.toyota.oneapp.features.core.composable.OACaption1TextView
import com.toyota.oneapp.features.core.composable.OACaption2TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteItem
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteType
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.ftue.presentation.extension.w
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.delay

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun RemoteComposableCircleItem(
    remoteItem: RemoteItem,
    timerText: String = ToyotaConstants.EMPTY_STRING,
    commandClick: (RemoteCommandType, Boolean) -> Unit,
    isConnected: Boolean,
) {
    val interactionSource = remember { MutableInteractionSource() }
    val borderColor = remember { mutableStateOf(Color.Transparent) }
    val isClicked = remember { mutableStateOf(false) }
    val bluetoothBlue = Color(0xFF0082FC)
    val transparentBluetoothBlue = bluetoothBlue.copy(alpha = 0.5f)
    val context = LocalContext.current

    val haptic = LocalHapticFeedback.current

    var surfaceBorderColor = AppTheme.colors.tertiary15
    var textFontStyle = AppTheme.fontStyles.caption2
    val disabledIconTextColor = AppTheme.colors.tertiary07
    var iconModifierSize = 20.dp

    if (remoteItem.type == RemoteType.PRIMARY) {
        surfaceBorderColor = AppTheme.colors.tertiary10
        textFontStyle = AppTheme.fontStyles.caption2
        iconModifierSize = 24.dp
    }

    val surfaceBorder =
        if (remoteItem.remoteCommandType == RemoteCommandType.EngineStart || remoteItem.remoteCommandType == RemoteCommandType.EngineStop) {
            BorderStroke(
                2.dp,
                surfaceBorderColor,
            )
        } else {
            null
        }

    val textStyle = textFontStyle
    val iconModifier = Modifier.size(iconModifierSize)

    val iconColor =
        if (remoteItem.showTimer) {
            AppTheme.colors.primary01
        } else if (remoteItem.disableCommand) {
            disabledIconTextColor
        } else {
            AppTheme.colors.tertiary03
        }

    val textColor = if (remoteItem.disableCommand) disabledIconTextColor else AppTheme.colors.tertiary03

    if (remoteItem.type == RemoteType.PRIMARY) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
        ) {
            if (isClicked.value) {
                borderColor.value = transparentBluetoothBlue

                LaunchedEffect(isClicked.value) {
                    delay(200)
                    borderColor.value = Color.Transparent
                    isClicked.value = false
                }
            }
            Surface(
                color = AppTheme.colors.tertiary15,
                elevation = 2.dp,
                shape = CircleShape,
                border = if (isConnected) BorderStroke(4.dp, borderColor.value) else surfaceBorder,
            ) {
                Box(
                    modifier =
                        with(Modifier) {
                            val width = 88.w()
                            width(width)
                                .height(width)
                                .clip(CircleShape)
                                .background(AppTheme.colors.tertiary15)
                                .wrapContentSize(Alignment.Center)
                                .testTagID(getAccessibilityId(remoteItem.remoteCommandType))
                                .combinedClickable(
                                    interactionSource = interactionSource,
                                    indication = null,
                                    onClick = {
                                        if (!remoteItem.disableCommand || isConnected) {
                                            commandClick(remoteItem.remoteCommandType, true)
                                            if (isConnected) {
                                                borderColor.value = Color.Blue
                                                isClicked.value = true
                                            }
                                        }
                                    },
                                    onLongClick = {
                                        if (!remoteItem.disableCommand) {
                                            commandClick(remoteItem.remoteCommandType, false)
                                            haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                        }
                                    },
                                )
                        },
                ) {
                    Column(
                        modifier = Modifier.align(Alignment.Center),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center,
                    ) {
                        Icon(
                            painter = painterResource(id = remoteItem.iconPath),
                            contentDescription = context.getString(remoteItem.title),
                            modifier = iconModifier,
                            tint =
                                if (isConnected &&
                                    (
                                        remoteItem.remoteCommandType == RemoteCommandType.DoorLock ||
                                            remoteItem.remoteCommandType == RemoteCommandType.DoorUnlock
                                    )
                                ) {
                                    AppTheme.colors.secondary01
                                } else {
                                    iconColor
                                },
                        )
                        Text(
                            text =
                                context.getString(
                                    remoteItem.title,
                                ),
                            style = textStyle,
                            overflow = TextOverflow.Clip,
                            maxLines = 1,
                            color =
                                if (isConnected &&
                                    (
                                        remoteItem.remoteCommandType == RemoteCommandType.DoorLock ||
                                            remoteItem.remoteCommandType == RemoteCommandType.DoorUnlock
                                    )
                                ) {
                                    AppTheme.colors.secondary01
                                } else {
                                    textColor
                                },
                        )
                        if (remoteItem.showTimer) {
                            OACaption1TextView(
                                text = timerText,
                                color = AppTheme.colors.tertiary05,
                            )
                        }
                    }
                }
            }
        }
    } else {
        Column(
            verticalArrangement = Arrangement.spacedBy(4.h(), Alignment.Top),
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(vertical = 5.h()),
        ) {
            Surface(
                color = AppTheme.colors.tertiary15,
                elevation = 2.dp,
                shape = CircleShape,
                border = surfaceBorder,
            ) {
                Box(
                    modifier =
                        Modifier
                            .testTagID(getAccessibilityId(remoteItem.remoteCommandType))
                            .combinedClickable(
                                interactionSource = interactionSource,
                                indication = null,
                                onClick = {
                                    if (!remoteItem.disableCommand) {
                                        commandClick(remoteItem.remoteCommandType, true)
                                    }
                                },
                                onLongClick = {
                                    if (!remoteItem.disableCommand) {
                                        commandClick(remoteItem.remoteCommandType, false)
                                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                    }
                                },
                            ),
                ) {
                    Column(
                        verticalArrangement =
                            Arrangement.spacedBy(
                                8.h(),
                                Alignment.CenterVertically,
                            ),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier =
                            Modifier
                                .size(size = 68.w())
                                .clip(shape = CircleShape)
                                .background(AppTheme.colors.tertiary15)
                                .padding(
                                    horizontal = 5.w(),
                                    vertical = 11.h(),
                                ),
                    ) {
                        Icon(
                            painter = painterResource(id = remoteItem.iconPath),
                            contentDescription = context.getString(remoteItem.title),
                            tint =
                                if (isConnected &&
                                    (
                                        remoteItem.remoteCommandType == RemoteCommandType.DoorLock ||
                                            remoteItem.remoteCommandType == RemoteCommandType.DoorUnlock
                                    )
                                ) {
                                    AppTheme.colors.secondary01
                                } else {
                                    iconColor
                                },
                        )
                    }
                }
            }
            OACaption2TextView(
                text =
                    context.getString(
                        remoteItem.title,
                    ),
                color =
                    if (isConnected &&
                        (
                            remoteItem.remoteCommandType == RemoteCommandType.DoorLock ||
                                remoteItem.remoteCommandType == RemoteCommandType.DoorUnlock
                        )
                    ) {
                        AppTheme.colors.secondary01
                    } else {
                        textColor
                    },
                lineHeight = 16.sp,
                modifier =
                    Modifier
                        .width(width = 78.w()),
                textAlign = TextAlign.Center,
                maxLines = 2,
                overflow = TextOverflow.Clip,
            )
        }
    }
}

fun getAccessibilityId(remoteCommandType: RemoteCommandType): String =
    when (remoteCommandType) {
        RemoteCommandType.EngineStart -> AccessibilityId.ID_REMOTE_ENGINE_START_BUTTON
        RemoteCommandType.EngineStop -> AccessibilityId.ID_REMOTE_ENGINE_STOP_BUTTON
        RemoteCommandType.Buzzer -> AccessibilityId.ID_REMOTE_BUZZER_BUTTON
        RemoteCommandType.DoorLock -> AccessibilityId.ID_REMOTE_DOOR_LOCK_BUTTON
        RemoteCommandType.DoorUnlock -> AccessibilityId.ID_REMOTE_DOOR_UNLOCK_BUTTON
        RemoteCommandType.TrunkLock -> AccessibilityId.ID_REMOTE_TRUNK_LOCK_BUTTON
        RemoteCommandType.TrunkUnlock -> AccessibilityId.ID_REMOTE_TRUNK_UNLOCK_BUTTON
        RemoteCommandType.TailgateLock -> AccessibilityId.ID_REMOTE_TAILGATE_LOCK_BUTTON
        RemoteCommandType.TailgateUnlock -> AccessibilityId.ID_REMOTE_TAILGATE_UNLOCK_BUTTON
        RemoteCommandType.Lights -> AccessibilityId.ID_REMOTE_LIGHTS_BUTTON
        RemoteCommandType.Climate -> AccessibilityId.ID_REMOTE_CLIMATE_BUTTON
        RemoteCommandType.Hazard -> AccessibilityId.ID_REMOTE_HAZARD_BUTTON
        RemoteCommandType.Horn -> AccessibilityId.ID_REMOTE_HORN_BUTTON
        RemoteCommandType.Park -> AccessibilityId.ID_REMOTE_PARK_BUTTON
        else -> ""
    }
