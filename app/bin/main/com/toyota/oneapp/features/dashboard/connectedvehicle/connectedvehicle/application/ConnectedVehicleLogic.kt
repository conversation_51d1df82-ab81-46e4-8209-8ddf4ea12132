package com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.application

import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.application.util.toUiModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CASEvent
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CASFeedback
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.CVRepository
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.ConnectedVehicleTabs
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.DashboardTab
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.NetworkStatus
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class ConnectedVehicleLogic
    @Inject
    constructor(
        private val repository: CVRepository,
        val vehicleInfo: VehicleInfo,
    ) : ConnectedVehicleUseCase {
        override fun loadTabWidget() =
            flow {
                val tabWidgets = mutableListOf<DashboardTab>()
                if (vehicleInfo.isRemoteServiceEnabled) {
                    tabWidgets.add(
                        DashboardTab(
                            accessibilityID = AccessibilityId.ID_TAB_REMOTE,
                            text = R.string.tab_remote,
                            type = ConnectedVehicleTabs.REMOTE.name,
                        ),
                    )
                }

                if (vehicleInfo.isVehicleStatusEnabled) {
                    tabWidgets.add(
                        DashboardTab(
                            accessibilityID = AccessibilityId.ID_TAB_STATUS,
                            text = R.string.tab_status,
                            type = ConnectedVehicleTabs.STATUS.name,
                        ),
                    )
                }

                if (vehicleInfo.isVehicleHealthEnabled) {
                    tabWidgets.add(
                        DashboardTab(
                            accessibilityID = AccessibilityId.ID_TAB_HEALTH,
                            text = R.string.tab_health,
                            type = ConnectedVehicleTabs.HEALTH.name,
                        ),
                    )
                }
                emit(tabWidgets)
            }

        override fun getCASData(vin: String) =
            flow {
                val response = repository.getCAS(vin)
                emit(response.data?.toUiModel())
            }

        override fun updateCASFeedback(casFeedback: CASFeedback) =
            flow {
                val response = repository.updateCASFeedback(feedback = casFeedback)
                emit(response.status == NetworkStatus.SUCCESS)
            }

        override fun updateCASEvent(casEvent: CASEvent) =
            flow {
                val response = repository.updateCASEvent(casEvent = casEvent)
                emit(response.status == NetworkStatus.SUCCESS)
            }
    }
