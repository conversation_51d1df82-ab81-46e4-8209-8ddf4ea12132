package com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.service

import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CASEvent
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CASFeedback
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CabinAwareness
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.PUT

interface CVApi {
    @GET("/v1/vehicle/remote/cdas/status")
    suspend fun getCAS(
        @Header("VIN") vin: String,
    ): Response<CabinAwareness>

    @PUT("/v1/vehicle/remote/cdas/report")
    suspend fun updateCASFeedback(
        @Body feedback: CASFeedback,
    ): Response<BaseResponse?>

    @PUT("/v1/vehicle/remote/cdas/report")
    suspend fun updateCASEvent(
        @Body casEvent: CASEvent,
    ): Response<BaseResponse?>
}
