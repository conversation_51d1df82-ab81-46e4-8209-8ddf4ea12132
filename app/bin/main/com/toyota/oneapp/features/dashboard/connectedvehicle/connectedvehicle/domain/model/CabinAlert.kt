package com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.model

data class CabinAlert(
    val eventId: String? = null,
    val title: String? = null,
    val description: String? = null,
    val eventDetails: CasEventDetails? = null,
)

data class CasEventDetails(
    val appBarTitle: String? = null,
    val title: String? = null,
    val description: String? = null,
    val events: ArrayList<CasEvent>? = null,
    val reportFlowEvents: ReportFlowEvents? = null,
)

data class ReportFlowEvents(
    val eventId: String? = null,
    val reasons: ArrayList<String>? = null,
    val title: String? = null,
    val disclaimer: String? = null,
    val positiveCTA: String?,
    val negativeCTA: String?,
    val selectionInLineError: String?,
    val provideDetailsTitle: String?,
    val provideDetailsInLineError: String?,
)

data class CasEvent(
    val title: String? = null,
    val description: String? = null,
    val isHyperLinkExists: Boolean? = null,
    val hyperLinkText: String? = null,
    val pageDestination: CasPageDestination? = null,
)

enum class CasPageDestination {
    REPORT_FLOW,
    OWNERS_MANUAL,
    NOTIFICATION_SETTINGS,
}
