package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.di

import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application.AdvanceRemoteLogic
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application.AdvanceRemoteUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.dataaccess.repository.RemoteCommandsDefaultRepo
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.repository.RemoteCommandsRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class AdvanceRemoteModule {
    @Binds
    abstract fun bindAdvanceRemoteRepo(remoteCommandsDefaultRepository: RemoteCommandsDefaultRepo): RemoteCommandsRepo

    @Binds
    abstract fun provideAdvanceRemoteLogic(advanceRemoteLogic: AdvanceRemoteLogic): AdvanceRemoteUseCase
}
