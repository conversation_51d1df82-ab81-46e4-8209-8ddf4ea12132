package com.toyota.oneapp.features.dashboard.noncv.dataaccess.model

import com.google.gson.annotations.SerializedName

data class ServiceHistoryResponse(
    val payload: ServiceHistoryPayload?,
)

data class ServiceHistoryPayload(
    val serviceHistories: List<ServiceHistoryItem>?,
)

data class ServiceHistoryItem(
    @SerializedName("serviceHistoryId") val id: String?,
    var servicingDealer: DealerItem?,
    var mileage: String?,
    val unit: String?,
    val roNumber: String?,
    val serviceDate: String?,
    val operationsPerformed: ArrayList<String>?,
    val customerCreatedRecord: Boolean = false,
    val serviceProvider: String?,
    val notes: String?,
)

data class DealerItem(
    val servicingDealerCode: String?,
    var servicingDealerName: String?,
    val address: String?,
    val city: String?,
    val state: String?,
    val country: String?,
    val zip: String?,
    val latitude: Double?,
    val longitude: Double?,
    val phone: String?,
)
