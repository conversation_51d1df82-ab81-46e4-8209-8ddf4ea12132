package com.toyota.oneapp.features.dashboard.connectedvehicle.climate.settings.domain.model

import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateState
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateType

data class CustomClimateSettingsState(
    val type: ClimateType,
    var settingsEnabled: Boolean = true,
) : ClimateState()
