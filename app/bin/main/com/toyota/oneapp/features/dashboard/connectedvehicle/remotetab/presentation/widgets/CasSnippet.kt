package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.presentation.widgets

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.LocalBottomSheetState
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import com.toyota.oneapp.features.dashboard.connectedvehicle.cas.CheckVehicleFlow
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.model.CabinAlert
import com.toyota.oneapp.util.ToyotaConstants

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun CasSnippet(
    casData: CabinAlert,
    onUpdateCasEvent: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val interactionSource = remember { MutableInteractionSource() }
    val coroutineScope = rememberCoroutineScope()
    val bottomSheet: LocalBottomSheetState = LocalBottomSheet.current

    Column(
        modifier =
            modifier
                .padding(top = 10.dp).testTagID(AccessibilityId.ID_CAS_REMOTE_SCREEN_CARD_CTA),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Surface(
            shape = CircleShape,
            color = AppTheme.colors.primary02,
            modifier =
                Modifier
                    .size(296.dp, 48.dp)
                    .clickable(
                        interactionSource = interactionSource,
                        indication = null,
                        onClick = {
                            casData.eventId?.let { eventId ->
                                onUpdateCasEvent(eventId)
                            }

                            casData.eventDetails?.let { eventDetails ->
                                coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) { sheetState ->
                                    CheckVehicleFlow(
                                        sheetState,
                                        eventDetails,
                                    )
                                }
                            }
                        },
                    ),
        ) {
            Row(
                modifier = Modifier.padding(start = 20.dp, end = 14.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Box(
                    modifier = Modifier.size(20.71.dp, 24.dp),
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_caution),
                        contentDescription = ToyotaConstants.EMPTY_STRING,
                        modifier = Modifier.align(Alignment.Center),
                        tint = AppTheme.colors.primary01,
                    )
                }

                casData.title?.let {
                    OACallOut1TextView(
                        text = it,
                        color = AppTheme.colors.tertiary00,
                    )
                }
                Icon(
                    modifier = Modifier.size(24.dp).padding(5.dp),
                    painter = painterResource(id = R.drawable.ic_right_arrow),
                    tint = AppTheme.colors.tertiary00,
                    contentDescription = ToyotaConstants.EMPTY_STRING,
                )
            }
        }
    }
}
