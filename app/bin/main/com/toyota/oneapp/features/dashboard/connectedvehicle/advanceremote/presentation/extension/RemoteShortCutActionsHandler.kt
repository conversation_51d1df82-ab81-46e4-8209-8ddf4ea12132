package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension

import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.AdvanceRemoteViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.utils.ShortCutActions

fun AdvanceRemoteViewModel.handleShortCutActions(threeDShortAction: String) {
    if (!threeDShortAction.isNullOrEmpty()) {
        when (threeDShortAction) {
            ShortCutActions.SHORTCUT_START_ENGINE_ACTION.action -> {
                if (vehicleInfo?.isEStartStopCapable == true) {
                    applicationData.updateThreeDShortActionStatus(status = true)
                    checkEngineStatusAndStartEngine()
                }
            }
            ShortCutActions.SHORTCUT_DOOR_LOCK_ACTION.action -> {
                if (vehicleInfo?.isLockUnLockCapable == true) {
                    applicationData.updateThreeDShortActionStatus(status = true)
                    executeRemoteCommand(
                        commandType = RemoteCommandType.DoorLock,
                        autoFixEnabled = false,
                    )
                }
            }
            ShortCutActions.SHORTCUT_DOOR_UNLOCK_ACTION.action -> {
                if (vehicleInfo?.isLockUnLockCapable == true) {
                    applicationData.updateThreeDShortActionStatus(status = true)
                    executeRemoteCommand(
                        commandType = RemoteCommandType.DoorUnlock,
                        autoFixEnabled = false,
                    )
                }
            }
        }
    }
}
