package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.dataaccess.service

import com.toyota.oneapp.model.remote.EngineStatusResponse
import com.toyota.oneapp.model.remote.RemoteBaseResponse
import com.toyota.oneapp.model.remote.RemoteRequest
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

interface RemoteCommandsApi {
    @POST("/oneapi/v2/remote/ng86/command")
    suspend fun ng86remoteRequest(
        @Header("VIN") vin: String,
        @Body body: RemoteRequest,
    ): Response<RemoteBaseResponse?>

    @POST("/oneapi/v1/legacy/remote/command")
    suspend fun remoteRequestForPre17(
        @Header("X-BRAND") brand: String,
        @Header("VIN") vin: String,
        @Body body: RemoteRequest,
    ): Response<RemoteBaseResponse?>

    @POST("/oneapi/v1/global/remote/command")
    suspend fun remoteRequest(
        @Header("VIN") vin: String,
        @Body body: RemoteRequest,
    ): Response<RemoteBaseResponse?>

    @GET("/oneapi/v1/remote/ng86/engine-status")
    suspend fun getNG86RemoteEngineStatus(
        @Header("x-brand") brand: String,
        @Header("vin") vin: String,
    ): Response<EngineStatusResponse>

    @GET("/oneapi/v1/legacy/remote/engine-status")
    suspend fun getCY17RemoteEngineStatus(
        @Header("x-brand") brand: String,
        @Header("vin") vin: String,
    ): Response<EngineStatusResponse>

    @GET("/oneapi/v1/global/remote/engine-status")
    suspend fun getCY17PlusRemoteEngineStatus(
        @Header("x-brand") brand: String,
        @Header("vin") vin: String,
    ): Response<EngineStatusResponse>
}
