package com.toyota.oneapp.features.dashboard.connectedvehicle.climate.settings.domain.model

import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.constants.AirFlow
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateState
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateType

data class AirFlowState(
    val type: ClimateType,
    var blower: AirFlow = AirFlow.NONE,
    var isLayoutActionable: Boolean = true,
) : ClimateState()
