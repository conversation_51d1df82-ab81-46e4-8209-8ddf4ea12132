package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation

import android.content.Context
import android.os.CountDownTimer
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.apptentive.ApptentiveService
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application.AdvanceRemoteState
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application.AdvanceRemoteUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.ProgressState
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteItem
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteProgress
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension.disableAllCommands
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension.enableAllCommands
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension.getProgressTextForBar
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension.handleShortCutActions
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension.startCountDownTimer
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension.stopTimer
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension.updateProgressBar
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension.updatedAnalyticsLog
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.utils.EngineStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.application.DigitalKeyDownloadLogic
import com.toyota.oneapp.features.dashboard.util.DashboardConstants
import com.toyota.oneapp.model.remote.EngineStatusPayload
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.NetworkStatus
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseFragmentViewModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AdvanceRemoteViewModel
    @Inject
    constructor(
        private val advanceRemoteUseCase: AdvanceRemoteUseCase,
        private val digitalKeyDownloadLogic: DigitalKeyDownloadLogic,
        val applicationData: ApplicationData,
        val analyticsLogger: AnalyticsLogger,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        apptentiveService: ApptentiveService,
    ) : BaseFragmentViewModel(),
        ApptentiveService by apptentiveService {
        // Remote Commands State
        val mRemoteCommandsState =
            MutableStateFlow<AdvanceRemoteState>(value = AdvanceRemoteState.Loading)
        val remoteCommandsState = mRemoteCommandsState.asStateFlow()
        var vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()
        private val appData: ApplicationData = applicationData
        var remoteList: MutableStateFlow<MutableList<RemoteItem>> =
            MutableStateFlow(arrayListOf())

        // Timer
        var countDownTimer: CountDownTimer? = null
        val countDownInterval = 1000L
        private val engineStatusFetchIntervalDuration = 20000L
        private val remoteCommandsDismissWithDelay = 10000L
        private val startStopProgressDismissWithDelay = 20000L
        private val engineStatusFetchInterval = 4
        val timerStateFlow = MutableStateFlow(value = "")
        val timerState = timerStateFlow.asStateFlow()

        // AutoFix Dialog Interaction
        val autoFixDialogFlow = MutableStateFlow(value = false)
        val autoFixDialog = autoFixDialogFlow.asStateFlow()

        // Toast Interaction
        val showToastFlow = MutableStateFlow(value = "")
        val showRemoteToast = showToastFlow.asStateFlow()

        private val mIsDkCapable = MutableStateFlow(value = false)
        val isDkCapable = mIsDkCapable.asStateFlow()

        // Guest Screen State
        private val guestDriverFlow = MutableStateFlow(value = false)
        val guestDriver = guestDriverFlow.asStateFlow()

        private val remoteOnlyUserFlow = MutableStateFlow(value = false)
        val remoteOnlyUser = remoteOnlyUserFlow.asStateFlow()

        var fetchEngineStatus3TimesIntervalJob: Job? = null
        private var fetchEngineStatusJob: Job? = null
        var fetchOtherCommandsJob: Job? = null

        // To close the dialog and refresh the dashboard for remote share
        val guestDriverRemoteShareFlow = MutableStateFlow(value = false)
        val guestDriverRemoteShare = guestDriverRemoteShareFlow.asStateFlow()

        // Progress bar
        val mRemoteCommandProgressState =
            MutableStateFlow(value = RemoteProgress(progressState = ProgressState.IDLE))
        val remoteCommandProgress = mRemoteCommandProgressState.asStateFlow()

        private val mShowSnackBarValue = MutableStateFlow(0)
        val showSnackBar = mShowSnackBarValue.asStateFlow()

        private val _isDkCommandExecuteFailed = MutableStateFlow(value = false)
        val isDkCommandExecuteFailed = _isDkCommandExecuteFailed.asStateFlow()

        private val _remoteCommandsEventFlow = MutableSharedFlow<RemoteCommandsEvent>()
        val remoteCommandsEventFlow = _remoteCommandsEventFlow.asSharedFlow()

        init {
            viewModelScope.launch {
                applicationData.getSelectedVehicleState().collect { info ->
                    vehicleInfo = info
                    getRemoteCommands(info)
                    mIsDkCapable.value = info?.isFeatureEnabled(Feature.DIGITAL_KEY) == true
                    remoteOnlyUserFlow.value = info?.isRemoteOnlyUser == true
                    vehicleInfo?.let { checkGuestDriverAvailability(it) }
                }
            }
        }

        fun getRemoteCommands(vehicleInfo: VehicleInfo?) {
            viewModelScope.launch {
                if (vehicleInfo != null) {
                    advanceRemoteUseCase
                        .getRemoteCommands(vehicleInfo)
                        .collect { resource ->
                            if (resource.isNotEmpty()) {
                                remoteList.value = resource
                                mRemoteCommandsState.value = AdvanceRemoteState.Success(remoteList)

                                // 7 is the value for show remote commands
                                if (vehicleInfo.remoteDisplay == Constants.REMOTE_COMMANDS_ACTIVE) {
                                    appData.threeDShortAction?.let {
                                        handleShortCutActions(it)
                                        appData.threeDShortAction = null
                                    }
                                    if (resource.any { it.remoteCommandType == RemoteCommandType.EngineStart }) {
                                        fetchEngineStatus(
                                            RemoteCommandType.EngineStart,
                                        )
                                    }
                                }
                            } else {
                                mRemoteCommandsState.value = AdvanceRemoteState.NoRemoteCommands
                            }
                            appData.threeDShortAction = null
                        }
                }
            }
        }

        // Every 20 seconds need to trigger the EngineStatus API call for 1 minute.
        // By chance if fcm caught then fetchEngineStatus3TimesIntervalJob,fetchEngineStatusJob will be cancelled.
        private fun fetchEngineStatus3TimesInterval(commandType: RemoteCommandType) {
            fetchEngineStatus3TimesIntervalJob?.cancel()
            fetchEngineStatus3TimesIntervalJob =
                viewModelScope.launch {
                    repeat(engineStatusFetchInterval) {
                        delay(engineStatusFetchIntervalDuration)
                        fetchEngineStatus(commandType)
                    }
                }
        }

        fun fetchEngineStatus(commandType: RemoteCommandType) {
            if (vehicleInfo != null && vehicleInfo?.isEStartStopCapable == true) {
                if (fetchEngineStatusJob == null || fetchEngineStatusJob?.isActive != true) {
                    fetchEngineStatusJob =
                        viewModelScope.launch {
                            advanceRemoteUseCase
                                .getEngineStatusRequest(
                                    vehicleInfo!!,
                                ).collect { response ->
                                    if (response.status.value == NetworkStatus.SUCCESS.value && response.data?.payload != null) {
                                        response.data?.payload?.let { engineResponse ->

                                            when (engineResponse.status) {
                                                EngineStatus.SUCCESS.value -> {
                                                    handleEngineStatusSuccessResponse(
                                                        engineResponse = engineResponse,
                                                        commandType = commandType,
                                                    )
                                                }

                                                EngineStatus.RESPONSE_0.value, EngineStatus.RESPONSE_2.value ->
                                                    if (commandType == RemoteCommandType.EngineStop) {
                                                        stopTimer()
                                                    } else {
                                                        enableAllCommands()
                                                    }

                                                else ->
                                                    if (commandType == RemoteCommandType.EngineStop) {
                                                        stopTimer()
                                                    } else {
                                                        enableAllCommands()
                                                    }
                                            }
                                        }
                                    } else {
                                        enableAllCommands()
                                    }
                                }
                        }
                }
            }
        }

        private fun handleEngineStatusSuccessResponse(
            engineResponse: EngineStatusPayload,
            commandType: RemoteCommandType,
        ) {
            try {
                if (engineResponse.date.toString().isNotEmpty() && commandType == RemoteCommandType.EngineStart && vehicleInfo != null) {
                    fetchEngineStatus3TimesIntervalJob?.cancel()
                    val timer: String =
                        engineResponse.getEngineCountDownTimer(vehicleInfo!!)
                    if (timer.isNotEmpty()) {
                        startCountDownTimer(timer)
                    } else {
                        enableAllCommands()
                    }
                } else {
                    enableAllCommands()
                }
            } catch (exception: Exception) {
                if (commandType == RemoteCommandType.EngineStop) {
                    stopTimer()
                } else {
                    enableAllCommands()
                }
            }
        }

        /**
         * This function to perform digital key lock and unlock
         */

        private fun dkConnect(remoteCommandType: RemoteCommandType): Boolean {
            val isSuccess = digitalKeyDownloadLogic.dkConnect(remoteCommandType)
            _isDkCommandExecuteFailed.value = !isSuccess
            return isSuccess
        }

        fun onEvent(event: RemoteCommandsEvent) {
            when (event) {
                is RemoteCommandsEvent.CommandClicked -> {
                    updatedAnalyticsLog(event.value)
                    when {
                        event.shortPress -> {
                            when {
                                event.value == RemoteCommandType.Park -> launchRemotePark()
                                event.value == RemoteCommandType.EngineStart && (vehicleInfo?.isStartClimateApplicable == true) -> {
                                    event.context?.let { navigateClimateDetail(it) }
                                }
                                event.value == RemoteCommandType.DoorLock && event.isDkConnected -> {
                                    dkConnect(RemoteCommandType.REMOTE_DK_LOCK)
                                }
                                event.value == RemoteCommandType.DoorUnlock && event.isDkConnected -> {
                                    dkConnect(RemoteCommandType.REMOTE_DK_UNLOCK)
                                }
                                event.value == RemoteCommandType.Climate ->
                                    event.context?.let {
                                        navigateClimateDetail(
                                            it,
                                        )
                                    }
                                else -> {
                                    showToastFlow.value = event.context?.getString(
                                        R.string.Dashboard_long_press_prompt,
                                    ) ?: ToyotaConstants.EMPTY_STRING
                                }
                            }
                        }
                        !event.shortPress -> {
                            if (event.value == RemoteCommandType.Park) {
                                return
                            }
                            if (event.value == RemoteCommandType.EngineStart) {
                                if (vehicleInfo?.isEStartStopCapable == true) {
                                    executeRemoteCommand(
                                        commandType = event.value,
                                        autoFixEnabled = event.autoFixEnabled,
                                    )
                                }
                            } else if (event.value != RemoteCommandType.Climate) {
                                executeRemoteCommand(
                                    commandType = event.value,
                                    autoFixEnabled = event.autoFixEnabled,
                                )
                            }
                        }
                    }
                }
                else -> {}
            }
        }

        private fun navigateClimateDetail(context: Context) {
            viewModelScope.launch {
                _remoteCommandsEventFlow.emit(RemoteCommandsEvent.ClimateClicked)
            }
        }

        private fun launchRemotePark() {
            viewModelScope.launch {
                if (vehicleInfo != null) {
                    advanceRemoteUseCase.launchRemotePark()
                }
            }
        }

        fun executeRemoteCommand(
            commandType: RemoteCommandType,
            autoFixEnabled: Boolean,
        ) {
            viewModelScope.launch {
                updateProgressBar(
                    title = R.string.remote_sending,
                    progressState = ProgressState.STARTED,
                    isRemoteStop = (commandType == RemoteCommandType.EngineStop),
                )
                disableAllCommands()
                if (vehicleInfo != null) {
                    advanceRemoteUseCase
                        .postCommandRequest(
                            autoFixEnabled = autoFixEnabled,
                            commandType = commandType,
                            vehicleInfo = vehicleInfo!!,
                        ).collect { response ->
                            if (response.status.value == NetworkStatus.SUCCESS.value) {
                                updateProgressBar(
                                    title = getProgressTextForBar(commandType),
                                    progressState = ProgressState.IN_PROGRESS,
                                    isRemoteStop = (commandType == RemoteCommandType.EngineStop),
                                )
                                sendRequestSuccess(commandType)
                            } else {
                                applicationData.updateThreeDShortActionStatus(false)
                                updateProgressBar(
                                    title = getProgressTextForBar(commandType),
                                    progressState = ProgressState.IDLE,
                                )
                                showToastFlow.value =
                                    response.message.ifBlank {
                                        DashboardConstants.NA
                                    }
                                enableAllCommands()
                            }
                        }
                }
            }
        }

        fun checkEngineStatusAndStartEngine() {
            viewModelScope.launch {
                advanceRemoteUseCase
                    .getEngineStatusRequest(
                        vehicleInfo!!,
                    ).collect { response ->
                        if (response.status.value == NetworkStatus.SUCCESS.value && response.data?.payload != null) {
                            response.data?.payload?.let { engineResponse ->

                                when (engineResponse.status) {
                                    EngineStatus.SUCCESS.value -> {
                                        applicationData.updateThreeDShortActionStatus(status = false)
                                        updateSnackBar(R.string.startInProgress)
                                    }

                                    else -> {
                                        executeRemoteCommand(
                                            commandType = RemoteCommandType.EngineStart,
                                            autoFixEnabled = false,
                                        )
                                    }
                                }
                            }
                        }
                    }
            }
        }

        suspend fun sendRequestSuccess(commandType: RemoteCommandType) =
            when (commandType) {
                RemoteCommandType.EngineStart, RemoteCommandType.EngineStop -> {
                    fetchEngineStatus3TimesInterval(commandType)
                    cancelProgressAfterDelay(commandType)
                }

                else -> {
                    fetchOtherCommandsJob =
                        viewModelScope.launch {
                            delay(remoteCommandsDismissWithDelay)
                            applicationData.updateThreeDShortActionStatus(status = false)
                            enableAllCommands()
                            // If no fcm received after getting 200 response, need to hide the progress after 10 sec delay
                            updateProgressBar(
                                title = getProgressTextForBar(commandType),
                                progressState = ProgressState.IDLE,
                            )
                        }
                }
            }

        private suspend fun cancelProgressAfterDelay(commandType: RemoteCommandType) {
            delay(startStopProgressDismissWithDelay)
            applicationData.updateThreeDShortActionStatus(status = false)
            enableAllCommands()
            // If no fcm received after getting 200 response, need to hide the progress after 20 sec delay
            updateProgressBar(
                title = getProgressTextForBar(commandType),
                progressState = ProgressState.IDLE,
            )
        }

        fun cancelJobAndGetEngineStatus(commandType: RemoteCommandType) {
            fetchEngineStatus3TimesIntervalJob?.cancel()
            if (fetchEngineStatusJob == null || fetchEngineStatusJob?.isActive != true) {
                fetchEngineStatus(commandType)
            }
        }

        private fun checkGuestDriverAvailability(vehicleInfo: VehicleInfo) {
            if (!vehicleInfo.isRemoteOnlyUser && vehicleInfo.remoteDisplay == Constants.REMOTE_COMMANDS_ACTIVE) {
                guestDriverFlow.value = vehicleInfo.isGuestDriverCapable || vehicleInfo.isRemoteShareAvailable
            } else {
                guestDriverFlow.value = false
            }
        }

        // Primary user with remote shared
        fun isRemoteSharedUser(): Boolean =
            applicationData.getSelectedVehicle()?.isRemoteSharedUser(
                oneAppPreferenceModel.getGuid(),
            ) == true

        fun updateSnackBar(snackBarText: Int) {
            mShowSnackBarValue.value = snackBarText
        }

        public override fun onCleared() {
            super.onCleared()
            fetchEngineStatus3TimesIntervalJob?.cancel()
            fetchOtherCommandsJob?.cancel()
            fetchEngineStatusJob?.cancel()
            countDownTimer?.cancel()
        }
    }
