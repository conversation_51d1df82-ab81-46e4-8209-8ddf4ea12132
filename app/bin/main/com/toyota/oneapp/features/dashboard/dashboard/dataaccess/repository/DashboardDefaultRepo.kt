package com.toyota.oneapp.features.dashboard.dashboard.dataaccess.repository

import com.toyota.oneapp.features.core.commonapicalls.dataaccess.service.CommonApi
import com.toyota.oneapp.features.dashboard.dashboard.dataaccess.servermodel.TelemetryResponse
import com.toyota.oneapp.features.dashboard.dashboard.dataaccess.service.DashboardApi
import com.toyota.oneapp.features.dashboard.dashboard.dataaccess.service.TelemetryApi
import com.toyota.oneapp.features.dashboard.dashboard.domain.repository.DashboardRepository
import com.toyota.oneapp.features.vehicleinfo.dataaccess.servermodel.SoftwareUpdateResponse
import com.toyota.oneapp.features.vehicleswitcher.dataaccess.servermodel.VehicleListResponse
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class DashboardDefaultRepo
    @Inject
    constructor(
        private val telemetryApi: TelemetryApi,
        private val dashboardApi: Dashboard<PERSON>pi,
        private val commonApi: Common<PERSON><PERSON>,
        errorParser: <PERSON><PERSON>r<PERSON><PERSON>agePars<PERSON>,
        ioContext: CoroutineContext,
    ) : DashboardRepository(errorParser, ioContext) {
        override suspend fun vehiclesList(): Resource<VehicleListResponse?> {
            return makeApiCall {
                dashboardApi.getVehicleList()
            }
        }

        override suspend fun fetchTelemetry(
            vin: String,
            generation: String,
            brand: String,
            region: String,
        ): Resource<TelemetryResponse?> {
            return makeApiCall {
                telemetryApi.fetchTelemetryApi(
                    vin = vin,
                    generation = generation,
                    brand = brand,
                    region = region,
                )
            }
        }

        override suspend fun fetchSoftwareUpdates(vin: String): Resource<SoftwareUpdateResponse?> {
            return makeApiCall {
                commonApi.fetchSoftwareUpdates(
                    vin = vin,
                )
            }
        }
    }
