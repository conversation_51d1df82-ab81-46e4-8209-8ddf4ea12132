package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.application

import com.toyota.oneapp.R
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.features.core.util.Constants.REMOTE_COMMANDS_ACTIVE
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.PopupStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.RemotePopupStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.RemoteStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.States
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.repository.RemoteStatesRepository
import com.toyota.oneapp.model.subscription.AssignRemoteUserRequest
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class RemoteStatesLogic
    @Inject
    constructor(
        private val repository: RemoteStatesRepository,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : RemoteStatesUseCase() {
        override fun getRemoteStates(vehicleInfo: VehicleInfo): RemoteStates =
            when (vehicleInfo.remoteDisplay) {
                0 -> checkConditionsToShowRemoteCommands(vehicleInfo)
                1 ->
                    if (vehicleInfo.isNG86) {
                        updateStatesForGenNG86(vehicleInfo)
                    } else {
                        RemoteStates(
                            status =
                                if (vehicleInfo.isLexusBrand && vehicleInfo.isVehicleMexican) {
                                    States.ACTIVATE_REMOTE_LMEX
                                } else {
                                    States.ACTIVATE_REMOTE
                                },
                            title = R.string.RemoteActivation_active_remote,
                            subHeading = R.string.RemoteActivation_active_remote_sub_heading,
                            ctaText = R.string.RemoteActivation_activate_cta,
                        )
                    }
                2, 3 ->
                    RemoteStates(
                        status = States.SUBSCRIPTION_CANCELLED,
                        title = R.string.Subscription_cancelled,
                        subHeading = R.string.Subscription_cancelled_sub_heading,
                        ctaText = R.string.Subscription_renew_subscriptions,
                    )
                4 ->
                    if (vehicleInfo.isNG86) {
                        updateStatesForGenNG86(vehicleInfo)
                    } else {
                        RemoteStates(
                            status = States.ACTIVATE_FAILED,
                            title = R.string.RemoteActivation_failed_title,
                            subHeading = R.string.RemoteActivation_failed_note,
                            ctaText = R.string.RemoteActivation_activate_cta,
                        )
                    }
                5 ->
                    RemoteStates(
                        status = States.ACTIVATION_PENDING,
                        title = R.string.RemoteActivation_pending_title,
                        subHeading = R.string.RemoteActivation_pending_note,
                        ctaText = R.string.RemoteActivation_refresh_remote_functionality,
                    )
                6 ->
                    RemoteStates(
                        status = States.ACTIVATION_ERROR,
                        title = R.string.RemoteActivation_unknown_title,
                        subHeading = R.string.RemoteActivation_unknown_note,
                        ctaText = R.string.ManagePaidSubscription_Contact_Support,
                    )
                // TODO remove as it is a way for testing Remote Active or Not
//        7 -> RemoteStates(
//            status = if (vehicleInfo.isLexusBrand && vehicleInfo.isVehicleMexican) States.ACTIVATE_REMOTE_LMEX else States.ACTIVATE_REMOTE,
//            title = R.string.RemoteActivation_active_remote,
//            subHeading = R.string.RemoteActivation_active_remote_sub_heading,
//            ctaText = R.string.RemoteActivation_activate_cta
//        )
                7 -> checkConditionsToShowRemoteCommands(vehicleInfo)
                8, 9 ->
                    RemoteStates(
                        status = States.SUBSCRIPTION_EXPIRED,
                        title = R.string.Subscription_expired,
                        subHeading = R.string.Subscription_cancelled_sub_heading,
                        ctaText = R.string.Subscription_renew_subscriptions,
                    )
                10 ->
                    RemoteStates(
                        status = States.VEHICLE_STOLEN,
                        title = R.string.Vehicle_stolen,
                        subHeading = R.string.Vehicle_stolen_sub_heading,
                        ctaText = R.string.RemoteActivation_reactivate,
                        ctaSecondaryText = R.string.contact_us,
                    )
                else -> RemoteStates()
            }

        fun checkConditionsToShowRemoteCommands(vehicleInfo: VehicleInfo): RemoteStates =
            if (isRemoteSharedUser(vehicleInfo)) {
                RemoteStates(
                    status = States.REMOTE_SHARED,
                    title = R.string.Remote_shared_heading,
                    subHeading = R.string.Remote_shared_note,
                    ctaText = R.string.Remove_driver,
                )
            } else {
                if (vehicleInfo.remoteDisplay == REMOTE_COMMANDS_ACTIVE) {
                    RemoteStates(status = States.REMOTE_ACTIVE)
                } else {
                    RemoteStates()
                }
            }

        fun isRemoteSharedUser(vehicleInfo: VehicleInfo): Boolean {
            val guid = IDPData.preferenceModel?.getGuid()

            return (
                !vehicleInfo.remoteUserGuid.isNullOrEmpty() &&
                    vehicleInfo.remoteUserGuid != vehicleInfo.subscriberGuid
            ) &&
                guid == vehicleInfo.subscriberGuid
        }

        private fun updateStatesForGenNG86(vehicleInfo: VehicleInfo): RemoteStates {
            val subHeading =
                if (vehicleInfo.remoteDisplay == 1) {
                    R.string.RemoteActivation_needs_activate_note_ng86
                } else {
                    R.string.RemoteActivation_active_remote_sub_heading
                }

            return RemoteStates(
                status = States.ACTIVATE_REMOTE,
                title = R.string.RemoteActivation_active_remote,
                subHeading = subHeading,
                ctaText = R.string.RemoteActivation_activate_cta,
                ctaVisibility = false,
            )
        }

        override fun assignRemoteUser(vehicleInfo: VehicleInfo): Flow<Resource<BaseResponse?>> =
            flow {
                val response =
                    repository.assignRemoteUser(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        generation = vehicleInfo.generation,
                        dateTime = System.currentTimeMillis().toString(),
                        assignRemoteUserRequest =
                            AssignRemoteUserRequest(
                                vin = vehicleInfo.vin,
                                subscriberGuid = vehicleInfo.subscriberGuid,
                                remoteUserGuid = oneAppPreferenceModel.getGuid(),
                            ),
                    )
                emit(response)
            }

        override fun updateRemoteStatePopup(remoteStates: RemoteStates): RemotePopupStates =
            when (remoteStates.status) {
                States.ACTIVATE_REMOTE_LMEX ->
                    RemotePopupStates(
                        popupStatus = PopupStates.AUTH_REQUIRED_LMEX,
                        title = R.string.Verify_ownership,
                        subHeading = R.string.Verify_ownership_note,
                        ctaText = R.string.Common_ok,
                        popupIcon = R.drawable.ic_lexus_profile,
                    )
                States.VEHICLE_STOLEN ->
                    RemotePopupStates(
                        popupStatus = PopupStates.VEHICLE_STOLEN,
                        title = R.string.Vehicle_stolen_title,
                        subHeading = R.string.Vehicle_stolen_note,
                        ctaText = R.string.Common_confirm,
                        ctaSecondaryText = R.string.Common_cancel,
                    )
                States.REMOTE_SHARED ->
                    RemotePopupStates(
                        popupStatus = PopupStates.REMOTE_SHARED,
                        title = R.string.Remove_driver,
                        subHeading = R.string.Remote_shared_popup_note,
                        ctaText = R.string.remove,
                        ctaSecondaryText = R.string.Common_cancel,
                    )
                else -> RemotePopupStates()
            }

        override fun isLMEXPhase2(selectedVehicle: VehicleInfo): Boolean {
            val isDriveConnectCapable =
                selectedVehicle.vehicleCapabilities
                    .any { it.lowercase() == "drive connect" }
            return selectedVehicle.isLMEX && isDriveConnectCapable
        }
    }
