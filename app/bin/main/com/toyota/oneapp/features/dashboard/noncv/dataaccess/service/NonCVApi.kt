package com.toyota.oneapp.features.dashboard.noncv.dataaccess.service

import com.toyota.oneapp.features.dashboard.noncv.dataaccess.model.ServiceHistoryResponse
import com.toyota.oneapp.features.find.domain.model.PreferredDealer
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header

interface NonCVApi {
    @GET("/oneapi/v1/preferred-dealer")
    suspend fun fetchPreferredDealer(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
        @Header("X-REGION") region: String,
    ): Response<PreferredDealer?>

    @GET("/oneapi/v1/servicehistory/vehicle/summary")
    suspend fun fetchServiceHistory(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
    ): Response<ServiceHistoryResponse?>
}
