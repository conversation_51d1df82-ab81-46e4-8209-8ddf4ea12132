package com.toyota.oneapp.features.dashboard.noncv.di

import com.toyota.oneapp.features.dashboard.noncv.application.NonCVLogic
import com.toyota.oneapp.features.dashboard.noncv.application.NonCVUseCase
import com.toyota.oneapp.features.dashboard.noncv.dataaccess.repository.NonCVDefaultRepository
import com.toyota.oneapp.features.dashboard.noncv.domain.repository.NonCVRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class NonCVModule {
    @Binds
    abstract fun bindNonCVRepository(nonCVDefaultRepository: NonCVDefaultRepository): NonCVRepository

    @Binds
    abstract fun provideNonCVLogic(nonCVLogic: NonCVLogic): NonCVUseCase
}
