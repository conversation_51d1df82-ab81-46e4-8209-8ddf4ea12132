package com.toyota.oneapp.features.dashboard.dashboard.presentation.helper

import android.annotation.SuppressLint
import android.content.pm.PackageManager
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.BottomSheetScaffold
import androidx.compose.material.BottomSheetScaffoldState
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application.AdvanceRemoteState
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.ProgressState
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.AdvanceRemoteScreen
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.AdvanceRemoteViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.ConnectedVehicleTabs
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.presentation.ConnectedVehicleViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DigitalKeyDownloadViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DigitalKeyShareKeyLayout
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DigitalKeyShareViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.RemoteDisabledDkComposableLayout
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotepark.presentation.RemoteParkViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.States
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.RemoteStatesViewModel
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.ftue.presentation.extension.h
import kotlinx.coroutines.launch
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.permission.PermissionUtil

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@OptIn(ExperimentalMaterialApi::class)
@Composable
fun AdvanceRemoteBottomSheet(
    shouldShowNavBar: Boolean,
    bottomBar: @Composable () -> Unit,
    topAppBar: @Composable () -> Unit,
    content: @Composable () -> Unit,
    bottomSheetScaffoldState: BottomSheetScaffoldState,
    navHostController: NavHostController,
) {
    val dashboardViewModel = (LocalContext.current as OADashboardActivity).dashboardViewModel

    val viewModel =
        hiltViewModel<ConnectedVehicleViewModel>(
            LocalContext.current as OADashboardActivity,
        )

    val remoteStatesViewModel =
        hiltViewModel<RemoteStatesViewModel>(
            LocalContext.current as OADashboardActivity,
        )

    val currentTab by viewModel.currentTab.collectAsState()

    val coroutineScope = rememberCoroutineScope()

    val context = LocalContext.current

    val remoteStates = remoteStatesViewModel.remoteStates.collectAsState()

    val digitalKeyDownloadViewModel = hiltViewModel<DigitalKeyDownloadViewModel>()

    val digitalKeyStatus = digitalKeyDownloadViewModel.digitalKeyStatus.collectAsState()

    val digitalKeyShareViewModel = hiltViewModel<DigitalKeyShareViewModel>()

    val digitalKeyLukStatus = digitalKeyShareViewModel.digitalKeyStatus.collectAsState()

    val remoteParkViewModel = hiltViewModel<RemoteParkViewModel>()

    // Collecting the StateFlow as state in Compose
    val vehicleInfo = digitalKeyDownloadViewModel.vehicleInfo.collectAsState().value

    // Accessing the VIN from the collected state
    val vin = vehicleInfo?.vin

    val isNoticeDismissed =
        vehicleInfo?.let { vehicleInfo ->
            digitalKeyDownloadViewModel.noticeDismissedMap.collectAsState().value.filterKeys {
                it.startsWith(
                    vehicleInfo.vin,
                )
            }
        }

    val digitalKeyNoticeDismissed = digitalKeyDownloadViewModel.getPreferences(vin)

    val advanceRemoteViewModel =
        hiltViewModel<AdvanceRemoteViewModel>(
            LocalContext.current as OADashboardActivity,
        )
    val isGuestDriverAvailable = advanceRemoteViewModel.guestDriver.collectAsState()

    val isRemoteOnlyUser = advanceRemoteViewModel.remoteOnlyUser.collectAsState()

    val remoteActivatedForDk = viewModel.remoteActivatedForDk.collectAsState()

    val remoteActivated = viewModel.remoteActivated.collectAsState()

    val remoteListState = advanceRemoteViewModel.remoteCommandsState.collectAsState()
    val isRemoteParkEnabled = remoteParkViewModel.isRemoteParkEnabled()
    val notificationEnabled = PermissionUtil().checkNotificationPermissionStatus(context) == PackageManager.PERMISSION_GRANTED
    val isRemoteStateActive = remoteStates.value.status == States.REMOTE_ACTIVE
    val localRemoteActive = isRemoteStateActive && notificationEnabled
    val localRemoteIsNotActiveButRemotePark = !localRemoteActive && isRemoteParkEnabled

    val isSecondaryVehicle = digitalKeyShareViewModel.isSecondaryKey()

    val remoteProgressState =
        advanceRemoteViewModel.remoteCommandProgress
            .collectAsState()
            .value.progressState

    val remoteSharedUser = advanceRemoteViewModel.isRemoteSharedUser()

    // Logging each condition
    LogTool.d("AdvanceRemoteBottomSheet", "localRemoteActive REMOTE: $localRemoteActive")
    LogTool.d(
        "AdvanceRemoteBottomSheet",
        "currentTab REMOTE: ${currentTab == ConnectedVehicleTabs.REMOTE.ordinal}",
    )
    LogTool.d("AdvanceRemoteBottomSheet", "isRemoteParkEnabled: $isRemoteParkEnabled")
    LogTool.d(
        "AdvanceRemoteBottomSheet",
        "localRemoteIsNotActiveButRemotePark: $localRemoteIsNotActiveButRemotePark",
    )
    LogTool.d(
        "AdvanceRemoteBottomSheet",
        "isSecondaryKey: ${digitalKeyShareViewModel.isSecondaryKey()}",
    )
    LogTool.d(
        "AdvanceRemoteBottomSheet",
        "remoteActivatedForDk: ${remoteActivatedForDk.value}",
    )
    LogTool.d(
        "AdvanceRemoteBottomSheet",
        "remoteSharedUser: $remoteSharedUser",
    )

    LogTool.d(
        "AdvanceRemoteBottomSheet",
        "remoteActivated: $remoteActivated",
    )

    LaunchedEffect(Unit) {
        digitalKeyDownloadViewModel.checkKeyStatus()
    }

    var sheetPeekHeight =
        when {
            // User has Guest Driver but no Remote Commands
            currentTab == ConnectedVehicleTabs.REMOTE.ordinal &&
                isGuestDriverAvailable.value &&
                remoteListState.value is AdvanceRemoteState.NoRemoteCommands &&
                remoteStates.value.status == States.REMOTE_ACTIVE -> 152.h()
            currentTab == ConnectedVehicleTabs.REMOTE.ordinal &&
                isRemoteStateActive &&
                notificationEnabled &&
                remoteActivatedForDk.value -> {
                // User has remote active, allowed notifications and dismissed the Download Digital Key Notice
                if (digitalKeyDownloadViewModel.isDkFeatureEnabled() == true && !isRemoteOnlyUser.value) {
                    if (digitalKeyNoticeDismissed && isNoticeDismissed?.containsKey(vehicleInfo?.vin) == true ||
                        digitalKeyDownloadViewModel.getVinKeyInfo()?.keyInfoId != null
                    ) {
                        152.h()
                    } else {
                        if (remoteSharedUser) 152.h() else 222.h()
                    }
                } else {
                    152.h()
                }
            }
            currentTab == ConnectedVehicleTabs.REMOTE.ordinal && !isRemoteOnlyUser.value && !localRemoteActive && !isSecondaryVehicle -> {
                when {
                    digitalKeyDownloadViewModel.isDkFeatureEnabled() == true && (remoteSharedUser || remoteActivatedForDk.value) -> 152.h()
                    isRemoteParkEnabled -> 152.h()
                    else -> 0.dp
                }
            }

            // If the user only has Remote Park
            currentTab == ConnectedVehicleTabs.REMOTE.ordinal &&
                isRemoteParkEnabled &&
                localRemoteIsNotActiveButRemotePark &&
                !digitalKeyShareViewModel.isSecondaryKey() -> 152.dp

            currentTab == ConnectedVehicleTabs.REMOTE.ordinal && isRemoteOnlyUser.value && isSecondaryVehicle -> 152.h()

            // If the user has secondary  key
            isSecondaryVehicle -> 152.h()
            viewModel.isVehicleListEmpty -> 0.dp
            else -> 0.dp
        }

    val animatablePeekHeight = remember { Animatable(sheetPeekHeight.value) }
    if (remoteProgressState == ProgressState.IDLE || remoteProgressState == ProgressState.COMPLETED) {
        sheetPeekHeight = sheetPeekHeight
    } else {
        sheetPeekHeight += 50.h()
    }

    val animationDuration = if (sheetPeekHeight.value > 0) 500 else 0
    LaunchedEffect(key1 = sheetPeekHeight) {
        animatablePeekHeight.animateTo(
            sheetPeekHeight.value,
            animationSpec = tween(durationMillis = animationDuration),
        )
    }

    dashboardViewModel.remoteSheetHeight = sheetPeekHeight
    LogTool.d("AdvanceRemoteBottomSheet", "sheetPeekHeight: $sheetPeekHeight")
    Scaffold(
        modifier = Modifier.background(AppTheme.colors.tertiary12),
        bottomBar = {
            if (shouldShowNavBar) {
                bottomBar()
            }
        },
    ) { innerPadding ->
        Box(modifier = Modifier.padding(innerPadding)) {
            BottomSheetScaffold(
                scaffoldState = bottomSheetScaffoldState,
                sheetContent = {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        val arrowIcon =
                            if (bottomSheetScaffoldState.bottomSheetState.isExpanded) {
                                R.drawable.ic_dropdown
                            } else {
                                R.drawable.ic_upward_arrow_drag
                            }
                        val arrowIconContentDescription =
                            if (bottomSheetScaffoldState.bottomSheetState.isExpanded) {
                                R.string.swipe_down_arrow_icon_description
                            } else {
                                R.string.swipe_up_arrow_icon_description
                            }
                        Icon(
                            modifier =
                                Modifier
                                    .testTagID(
                                        if (bottomSheetScaffoldState.bottomSheetState.isExpanded) {
                                            AccessibilityId.ID_DASHBOARD_REMOTE_CLOSE_ICON_BUTTON
                                        } else {
                                            AccessibilityId.ID_DASHBOARD_REMOTE_OPEN_ICON_BUTTON
                                        },
                                    ).padding(vertical = 10.h())
                                    .clickable(
                                        interactionSource = remember { MutableInteractionSource() },
                                        indication = null,
                                        onClick = {
                                            coroutineScope.launch {
                                                if (bottomSheetScaffoldState.bottomSheetState.isExpanded) {
                                                    bottomSheetScaffoldState.bottomSheetState.collapse()
                                                } else {
                                                    bottomSheetScaffoldState.bottomSheetState.expand()
                                                }
                                            }
                                        },
                                    ),
                            painter =
                                painterResource(
                                    id = arrowIcon,
                                ),
                            contentDescription =
                                context.getString(
                                    arrowIconContentDescription,
                                ),
                            tint = AppTheme.colors.outline01,
                        )

                        when {
                            // Case 1
                            (localRemoteActive && !isSecondaryVehicle && !remoteSharedUser) ||
                                (isSecondaryVehicle && remoteActivated.value) -> {
                                if (digitalKeyDownloadViewModel.isDkFeatureEnabled() == true) {
                                    digitalKeyDownloadViewModel.provideDigitalKeyStatus()
                                }
                                AdvanceRemoteScreen(
                                    bottomSheetState = bottomSheetScaffoldState,
                                    isRemoteParkNoAdvanceRemote = false,
                                    isRemoteStateActive = isRemoteStateActive,
                                    digitalKeyStatus = digitalKeyStatus,
                                    navHostController = navHostController,
                                )
                            }
                            // Case 2
                            localRemoteIsNotActiveButRemotePark && !isSecondaryVehicle && !remoteSharedUser -> {
                                if (digitalKeyDownloadViewModel.isDkFeatureEnabled() == true) {
                                    digitalKeyDownloadViewModel.provideDigitalKeyStatus()
                                }
                                AdvanceRemoteScreen(
                                    bottomSheetState = bottomSheetScaffoldState,
                                    isRemoteParkNoAdvanceRemote = true,
                                    isRemoteStateActive = isRemoteStateActive,
                                    digitalKeyStatus = digitalKeyStatus,
                                    navHostController = navHostController,
                                )
                            }
                            // Case 3
                            isSecondaryVehicle && !remoteSharedUser && !remoteActivated.value -> {
                                digitalKeyShareViewModel.provideShareDigitalKeyStatus()
                                DigitalKeyShareKeyLayout(
                                    coroutineScope,
                                    bottomSheetScaffoldState,
                                    digitalKeyLukStatus,
                                )
                            }
                            // Case 4
                            isSecondaryVehicle && isRemoteOnlyUser.value && !remoteActivated.value -> {
                                digitalKeyDownloadViewModel.provideDigitalKeyStatus()
                                RemoteDisabledDkComposableLayout(
                                    coroutineScope,
                                    bottomSheetScaffoldState,
                                    digitalKeyStatus,
                                )
                            }
                            // Case 5
                            digitalKeyDownloadViewModel.isDkFeatureEnabled() == true &&
                                (remoteSharedUser || (!isRemoteOnlyUser.value && !localRemoteActive)) -> {
                                digitalKeyDownloadViewModel.provideDigitalKeyStatus()
                                RemoteDisabledDkComposableLayout(
                                    coroutineScope,
                                    bottomSheetScaffoldState,
                                    digitalKeyStatus,
                                )
                            }
                            else -> sheetPeekHeight = 0.dp
                        }
                    }
                },
                sheetPeekHeight = animatablePeekHeight.value.dp,
                sheetShape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
                sheetBackgroundColor = AppTheme.colors.tile01,
                sheetElevation = 8.dp,
            ) {
                Scaffold(
                    topBar = {
                        if (shouldShowNavBar) {
                            topAppBar()
                        }
                    },
                    content = { content() },
                )
            }
        }
    }
}
