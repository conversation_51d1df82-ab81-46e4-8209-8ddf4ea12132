package com.toyota.oneapp.features.dashboard.noncv.application

import com.toyota.oneapp.R
import com.toyota.oneapp.features.dashboard.noncv.domain.model.NonCV
import com.toyota.oneapp.features.dashboard.noncv.domain.model.NonCVType
import com.toyota.oneapp.features.dashboard.noncv.domain.repository.NonCVRepository
import com.toyota.oneapp.features.find.domain.model.toServiceDealer
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.NetworkStatus
import com.toyota.oneapp.ui.flutter.GO_TO_APPOINTMENTS
import com.toyota.oneapp.ui.flutter.GO_TO_MAINTENANCE_SCHEDULE
import com.toyota.oneapp.ui.flutter.GO_TO_SERVICE_HISTORY
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.toDate
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.text.ParseException
import javax.inject.Inject

internal const val GO_TO_PREFERRED_DEALER = "GO_TO_PREFERRED_DEALER"

class NonCVLogic
    @Inject
    constructor(
        private val repository: NonCVRepository,
        private val dateUtil: DateUtil,
    ) : NonCVUseCase() {
        override suspend fun getNonCVFeatures(vehicleInfo: VehicleInfo): Flow<ArrayList<NonCV>> {
            return flow {
                val nonCVItems = arrayListOf<NonCV>()

                getPreferredDealer(vehicleInfo).collect {
                    if (it != null) {
                        nonCVItems.add(it)
                    }
                }
                getMaintenanceSchedule(vehicleInfo).collect {
                    if (it != null) {
                        nonCVItems.add(it)
                    }
                }
                getServiceHistory(vehicleInfo).collect {
                    if (it != null) {
                        nonCVItems.add(it)
                    }
                }
                getAppointments(vehicleInfo).collect {
                    if (it != null) {
                        nonCVItems.add(it)
                    }
                }
                emit(nonCVItems)
            }
        }

        private fun getPreferredDealer(vehicleInfo: VehicleInfo): Flow<NonCV?> {
            return flow {
                val response =
                    repository.fetchPreferredDealer(
                        brand = vehicleInfo.brand,
                        vin = vehicleInfo.vin,
                        region = vehicleInfo.region,
                    )
                if (response.status == NetworkStatus.SUCCESS && response.data != null && !response.data!!.payload.isNullOrEmpty()) {
                    val dealerData = response.data!!.payload?.first()
                    val dealerItem =
                        dealerData?.dealerName?.let { dealerName ->
                            NonCV(
                                title = R.string.Preferred_Dealer,
                                subTitle = dealerName,
                                iconPath = R.drawable.ic_preferred_dealer,
                                nonCVType = NonCVType.PREFERRED_DEALER,
                                onclick = GO_TO_PREFERRED_DEALER,
                                preferredDealerPayloadData = dealerData.toServiceDealer(),
                            )
                        }
                    emit(dealerItem)
                } else {
                    emit(
                        NonCV(
                            title = R.string.Preferred_Dealer,
                            subTitle = ToyotaConstants.EMPTY_STRING,
                            iconPath = R.drawable.ic_preferred_dealer,
                            nonCVType = NonCVType.PREFERRED_DEALER,
                            onclick = GO_TO_PREFERRED_DEALER,
                        ),
                    )
                }
            }
        }

        private fun getMaintenanceSchedule(vehicleInfo: VehicleInfo): Flow<NonCV?> {
            return flow {
                val isMaintenanceScheduleEnabled =
                    vehicleInfo.isFeatureEnabled(
                        Feature.MAINTENANCE_TIMELINE,
                    )
                if (isMaintenanceScheduleEnabled) {
                    val subtitle = ToyotaConstants.EMPTY_STRING
                    val maintenanceItem =
                        NonCV(
                            title = R.string.Garage_maintenance_schedule,
                            subTitle = subtitle,
                            iconPath = R.drawable.ic_maintenance_schedule,
                            tickIcon = R.drawable.ic_check_icon,
                            nonCVType = NonCVType.MAINTENANCE_SCHEDULE,
                            onclick = GO_TO_MAINTENANCE_SCHEDULE,
                            maintenancePayloadData = null,
                        )
                    emit(maintenanceItem)
                } else {
                    emit(null)
                }
            }
        }

        private fun getServiceHistory(vehicleInfo: VehicleInfo): Flow<NonCV?> {
            return flow {
                val isServiceHistoryEnabled =
                    vehicleInfo.isFeatureEnabled(
                        Feature.SERVICE_HISTORY,
                    )

                if (isServiceHistoryEnabled) {
                    val response =
                        repository.fetchServiceHistory(
                            vin = vehicleInfo.vin,
                            brand = vehicleInfo.brand,
                        )
                    val isServiceHistoryNotEmpty = !response.data?.payload?.serviceHistories.isNullOrEmpty()
                    if (response.status == NetworkStatus.SUCCESS && response.data != null && isServiceHistoryNotEmpty) {
                        val dealerData = response.data?.payload?.serviceHistories?.first()
                        val subTitle =
                            dealerData?.serviceDate?.let {
                                try {
                                    dateUtil.formatMediumDate(
                                        it.toDate(),
                                    )
                                } catch (ex: ParseException) {
                                    ""
                                }
                            }.orEmpty()
                        emit(
                            NonCV(
                                title = R.string.Garage_service_history,
                                subTitle = subTitle,
                                iconPath = R.drawable.ic_service_history,
                                tickIcon = R.drawable.ic_check_icon,
                                nonCVType = NonCVType.SERVICE_HISTORY,
                                onclick = GO_TO_SERVICE_HISTORY,
                                serviceHistoryPayloadData = response.data?.payload,
                            ),
                        )
                    } else {
                        emit(
                            NonCV(
                                title = R.string.Garage_service_history,
                                subTitle = ToyotaConstants.EMPTY_STRING,
                                iconPath = R.drawable.ic_service_history,
                                tickIcon = R.drawable.ic_check_icon,
                                nonCVType = NonCVType.SERVICE_HISTORY,
                                onclick = GO_TO_SERVICE_HISTORY,
                                serviceHistoryPayloadData = response.data?.payload,
                            ),
                        )
                    }
                } else {
                    emit(null)
                }
            }
        }

        private fun getAppointments(vehicleInfo: VehicleInfo): Flow<NonCV?> {
            return flow {
                val isDealerAppointmentEnabled =
                    vehicleInfo.isFeatureEnabled(
                        Feature.DEALER_APPOINTMENT,
                    )

                if (isDealerAppointmentEnabled) {
                    val appointmentItem =
                        NonCV(
                            title = R.string.NonCV_Appointments,
                            subTitle = ToyotaConstants.EMPTY_STRING,
                            iconPath = R.drawable.ic_appointment_icon,
                            nonCVType = NonCVType.APPOINTMENTS,
                            tickIcon = R.drawable.ic_check_icon,
                            onclick = GO_TO_APPOINTMENTS,
                        )
                    emit(appointmentItem)
                } else {
                    emit(null)
                }
            }
        }
    }
