package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.dataaccess.service

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.DigitalKeyInviteRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.DigitalKeyInviteResponse
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.DigitalKeyStatusPayloadResponse
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.HsmUnlockGetResponse
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.HsmUnlockRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.HsmUnlockResponse
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.InstallKeyApiRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LukTokenRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LukTokenResponse
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusResponse
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.OwnerTokenRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.OwnerTokenResponse
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.PendingInvitesResponse
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.RotateTokenRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.RotateTokenResponse
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.VehicleIdResponse
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.HTTP
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT

interface Dk7RestServiceApi {
    @Headers("Content-Type: application/json")
    @POST("/oactp/v1/portal/savekey")
    suspend fun installDigitalKey(
        @Body body: InstallKeyApiRequest,
    ): Response<BaseResponse>

    @GET("/oactp/v1/digital-key/status")
    suspend fun getDigitalKeyStatus(
        @Header("vin") vin: String,
        @Header("deviceId") deviceId: String,
        @Header("keyInfoId") keyInfoId: String? = null,
    ): Response<ApiResponse<DigitalKeyStatusPayloadResponse>?>

    @POST("/oactp/v1/digital-key/ownertoken")
    suspend fun postOwnerToken(
        @Body request: OwnerTokenRequest,
    ): Response<OwnerTokenResponse>

    @GET("/oactp/v1/digital-key/vehicleid")
    suspend fun getVehicleId(
        @Header("vin") vin: String,
    ): Response<VehicleIdResponse?>

    @POST("/oactp/v1/digital-key/rotatetoken")
    suspend fun rotateToken(
        @Body request: RotateTokenRequest,
    ): Response<RotateTokenResponse>

    @HTTP(method = "DELETE", path = "/oactp/v1/digital-key/token", hasBody = true)
    suspend fun deleteDigitalKey(
        @Header("keyInfoId") keyInfoId: String?,
        @Header("vin") vin: String,
        @Header("phoneNo") phoneNo: String,
        @Header("deviceId") deviceId: String,
        @Header("type") type: String,
        @Header("inviteId") inviteId: String?,
    ): Response<BaseResponse?>

    @PUT("/oactp/v1/digital-key/status")
    suspend fun putDigitalKeyStatus(
        @Header("vin") vin: String,
        @Header("deviceId") deviceId: String,
        @Header("status") status: String,
    ): Response<BaseResponse>

    @POST("/oactp/v1/digital-key/luktoken")
    suspend fun postLukToken(
        @Body request: LukTokenRequest,
    ): Response<LukTokenResponse>

    @POST("/oactp/v1/digital-key/invite")
    suspend fun postDigitalKeyInvite(
        @Body request: DigitalKeyInviteRequest,
    ): Response<DigitalKeyInviteResponse>

    @GET("/oactp/v1/digital-key/luks")
    suspend fun getLuks(
        @Header("deviceId") deviceId: String,
        @Header("vin") vin: String,
    ): Response<LuksStatusResponse>

    @GET("/oactp/v1/digital-key/pendinginvite")
    suspend fun getPendingInvites(): Response<PendingInvitesResponse>

    @POST("/oactp/v1/digital-key/hsmunlock")
    suspend fun postHsmUnlock(
        @Body request: HsmUnlockRequest,
    ): Response<HsmUnlockResponse>

    @GET("/oactp/v1/digital-key/hsmunlock")
    suspend fun checkHsmUnlockStatus(
        @Header("deviceId") deviceId: String,
        @Header("vin") vin: String,
    ): Response<HsmUnlockGetResponse>
}
