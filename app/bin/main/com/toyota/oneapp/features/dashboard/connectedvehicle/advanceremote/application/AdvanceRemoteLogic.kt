package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application

import com.toyota.one_ui.widgets.Generation
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.IDPData.Companion.preferenceModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteItem
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.repository.RemoteCommandsRepo
import com.toyota.oneapp.model.remote.Command
import com.toyota.oneapp.model.remote.EngineStatusResponse
import com.toyota.oneapp.model.remote.RemoteBaseResponse
import com.toyota.oneapp.model.remote.RemoteRequest
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import jp.co.toyota.smart.remoteparkcontrollib.SdkFunction
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class AdvanceRemoteLogic
    @Inject
    constructor(
        private val analyticsLogger: AnalyticsLogger,
        private val repository: RemoteCommandsRepo,
    ) : AdvanceRemoteUseCase() {
        private val rpcSdk: SdkFunction by lazy { SdkFunction() }

        override suspend fun getRemoteCommands(vehicleInfo: VehicleInfo): Flow<ArrayList<RemoteItem>> =
            flow {
                val remoteCommands: ArrayList<RemoteItem> = arrayListOf()
                var hasPrimaryRemoteItems = false

                if (vehicleInfo.isLockUnLockCapable) {
                    remoteCommands.add(
                        RemoteItem(
                            title = R.string.Dashboard_lock,
                            type = RemoteType.PRIMARY,
                            iconPath = R.drawable.ic_remote_door_lock,
                            remoteCommandType = RemoteCommandType.DoorLock,
                        ),
                    )
                    remoteCommands.add(
                        RemoteItem(
                            title = R.string.Dashboard_unlock,
                            type = RemoteType.PRIMARY,
                            iconPath = R.drawable.ic_remote_unlock,
                            remoteCommandType = RemoteCommandType.DoorUnlock,
                        ),
                    )
                }

                if (vehicleInfo.isEStartStopCapable) {
                    if (vehicleInfo.isLockUnLockCapable) {
                        remoteCommands.add(
                            1,
                            RemoteItem(
                                title = R.string.Dashboard_start_engine,
                                type = RemoteType.PRIMARY,
                                iconPath = R.drawable.ic_remote_start,
                                remoteCommandType = RemoteCommandType.EngineStart,
                            ),
                        )
                    } else {
                        remoteCommands.add(
                            RemoteItem(
                                title = R.string.Dashboard_start_engine,
                                type = RemoteType.PRIMARY,
                                iconPath = R.drawable.ic_remote_start,
                                remoteCommandType = RemoteCommandType.EngineStart,
                            ),
                        )
                    }
                }

                if (vehicleInfo.isStartClimateApplicable) {
                    if (vehicleInfo.isLockUnLockCapable) {
                        remoteCommands.add(
                            1,
                            RemoteItem(
                                title = R.string.Dashboard_start_engine,
                                type = RemoteType.PRIMARY,
                                iconPath = R.drawable.ic_remote_start,
                                remoteCommandType = RemoteCommandType.EngineStart,
                            ),
                        )
                    } else {
                        remoteCommands.add(
                            RemoteItem(
                                title = R.string.Dashboard_start_engine,
                                type = RemoteType.PRIMARY,
                                iconPath = R.drawable.ic_remote_start,
                                remoteCommandType = RemoteCommandType.EngineStart,
                            ),
                        )
                    }
                }

                // Check if there are any remote items that are primary
                for (remoteCommand in remoteCommands) {
                    if (remoteCommand.type == RemoteType.PRIMARY) {
                        hasPrimaryRemoteItems = true
                        break
                    }
                }

                if (vehicleInfo.isLightsCapable) {
                    remoteCommands.add(
                        RemoteItem(
                            title = R.string.Dashboard_lights,
                            type = RemoteType.SECONDARY,
                            iconPath = R.drawable.ic_remote_lights,
                            remoteCommandType = RemoteCommandType.Lights,
                        ),
                    )
                }

                if (vehicleInfo.isRemoteParkEnabled) {
                    val parkRemoteType =
                        if (hasPrimaryRemoteItems && !vehicleInfo.isRemoteActivated) {
                            RemoteType.PRIMARY
                        } else if (hasPrimaryRemoteItems && vehicleInfo.isRemoteActivated) {
                            RemoteType.SECONDARY
                        } else {
                            RemoteType.PRIMARY
                        }

                    remoteCommands.add(
                        RemoteItem(
                            title = R.string.Dashboard_park,
                            type = parkRemoteType,
                            iconPath = R.drawable.ic_remote_park,
                            remoteCommandType = RemoteCommandType.Park,
                        ),
                    )
                }

                if (vehicleInfo.isHazardCapable) {
                    remoteCommands.add(
                        RemoteItem(
                            title = R.string.Dashboard_hazard,
                            type = RemoteType.SECONDARY,
                            iconPath = R.drawable.ic_remote_hazard,
                            remoteCommandType = RemoteCommandType.Hazard,
                        ),
                    )
                }

                if (vehicleInfo.isClimateCapable) {
                    remoteCommands.add(
                        RemoteItem(
                            title = R.string.Dashboard_climate,
                            type = RemoteType.SECONDARY,
                            iconPath = R.drawable.ic_remote_climate,
                            remoteCommandType = RemoteCommandType.Climate,
                        ),
                    )
                }

                if (vehicleInfo.isBuzzerCapable) {
                    remoteCommands.add(
                        RemoteItem(
                            title = R.string.Dashboard_buzzer_warning,
                            type = RemoteType.SECONDARY,
                            iconPath = R.drawable.ic_remote_buzzer,
                            remoteCommandType = RemoteCommandType.Buzzer,
                        ),
                    )
                }

                if (vehicleInfo.isTrunkLockUnlockCapable || vehicleInfo.isTailgateLockUnlockCapable) {
                    val lockTitle =
                        if (vehicleInfo.isTailgateLockUnlockCapable) {
                            R.string.Dashboard_lock
                        } else {
                            R.string.Dashboard_trunk_lock
                        }
                    val unLockTitle =
                        if (vehicleInfo.isTailgateLockUnlockCapable) {
                            R.string.Dashboard_unlock
                        } else {
                            R.string.Dashboard_trunk_unlock
                        }
                    val lockIcon =
                        if (vehicleInfo.isTailgateLockUnlockCapable) {
                            R.drawable.ic_remote_tailgate_lock
                        } else {
                            R.drawable.ic_remote_trunk_lock
                        }
                    val unLockIcon =
                        if (vehicleInfo.isTailgateLockUnlockCapable) {
                            R.drawable.ic_remote_tailgate_unlock
                        } else {
                            R.drawable.ic_new_remote_trunk_unlock
                        }
                    val lockType =
                        if (vehicleInfo.isTailgateLockUnlockCapable) {
                            RemoteCommandType.TailgateLock
                        } else {
                            RemoteCommandType.TrunkLock
                        }
                    val unlockType =
                        if (vehicleInfo.isTailgateLockUnlockCapable) {
                            RemoteCommandType.TailgateUnlock
                        } else {
                            RemoteCommandType.TrunkUnlock
                        }

                    remoteCommands.add(
                        RemoteItem(
                            title = lockTitle,
                            type = RemoteType.SECONDARY,
                            iconPath = lockIcon,
                            remoteCommandType = lockType,
                        ),
                    )
                    remoteCommands.add(
                        RemoteItem(
                            title = unLockTitle,
                            type = RemoteType.SECONDARY,
                            iconPath = unLockIcon,
                            remoteCommandType = unlockType,
                        ),
                    )
                }

                if (vehicleInfo.isHornCapable) {
                    remoteCommands.add(
                        RemoteItem(
                            title = R.string.Dashboard_horn,
                            type = RemoteType.SECONDARY,
                            iconPath = R.drawable.ic_remote_horn,
                            remoteCommandType = RemoteCommandType.Horn,
                        ),
                    )
                }

                // Only RemotePark Available
                val primaryRemoteItems: List<RemoteItem> =
                    remoteCommands.filter {
                        it.type == RemoteType.PRIMARY
                    }
                val secondaryRemoteItems: List<RemoteItem> =
                    remoteCommands.filter {
                        it.type == RemoteType.SECONDARY
                    }
                if (primaryRemoteItems.isEmpty() &&
                    secondaryRemoteItems.isEmpty() &&
                    vehicleInfo.isRemoteParkEnabled
                ) {
                    remoteCommands.add(
                        RemoteItem(
                            title = R.string.Dashboard_park,
                            type = RemoteType.PRIMARY,
                            iconPath = R.drawable.ic_remote_park,
                            remoteCommandType = RemoteCommandType.Park,
                        ),
                    )
                }
                emit(remoteCommands)
            }

        override fun postCommandRequest(
            autoFixEnabled: Boolean,
            commandType: RemoteCommandType,
            vehicleInfo: VehicleInfo,
        ): Flow<Resource<RemoteBaseResponse?>> =
            when (commandType) {
                RemoteCommandType.EngineStart ->
                    flow {
                        val requestBody =
                            getRemoteRequestBody(
                                autoFixEnabled = autoFixEnabled,
                                vehicleInfo = vehicleInfo,
                                command =
                                    if (vehicleInfo.generationType == Generation.CY17) {
                                        SeventeenRemoteCommand.ENGINE_START_17
                                    } else {
                                        RemoteCommand.ENGINE_START_17P
                                    },
                            )
                        sendRemoteCommandRequest(vehicleInfo = vehicleInfo, remoteRequest = requestBody).collect {
                            emit(it)
                        }
                    }

                RemoteCommandType.EngineStop ->
                    flow {
                        val requestBody =
                            getRemoteRequestBody(
                                autoFixEnabled = autoFixEnabled,
                                vehicleInfo = vehicleInfo,
                                command =
                                    if (vehicleInfo.generationType == Generation.CY17) {
                                        SeventeenRemoteCommand.ENGINE_STOP_17
                                    } else {
                                        RemoteCommand.ENGINE_STOP_17P
                                    },
                            )
                        sendRemoteCommandRequest(vehicleInfo = vehicleInfo, remoteRequest = requestBody).collect {
                            emit(it)
                        }
                    }

                RemoteCommandType.DoorLock ->
                    flow {
                        val requestBody =
                            getRemoteRequestBody(
                                autoFixEnabled = autoFixEnabled,
                                vehicleInfo = vehicleInfo,
                                command =
                                    if (vehicleInfo.generationType ==
                                        Generation.CY17
                                    ) {
                                        SeventeenRemoteCommand.DOOR_LOCK_17
                                    } else {
                                        RemoteCommand.DOOR_LOCK_17P
                                    },
                            )
                        sendRemoteCommandRequest(vehicleInfo = vehicleInfo, remoteRequest = requestBody).collect {
                            emit(it)
                        }
                    }

                RemoteCommandType.DoorUnlock ->
                    flow {
                        val requestBody =
                            getRemoteRequestBody(
                                autoFixEnabled = autoFixEnabled,
                                vehicleInfo = vehicleInfo,
                                command =
                                    if (vehicleInfo.generationType == Generation.CY17) {
                                        SeventeenRemoteCommand.DOOR_UNLOCK_17
                                    } else {
                                        RemoteCommand.DOOR_UNLOCK_17P
                                    },
                            )
                        sendRemoteCommandRequest(vehicleInfo = vehicleInfo, remoteRequest = requestBody).collect {
                            emit(it)
                        }
                    }

                RemoteCommandType.Lights ->
                    flow {
                        val requestBody =
                            getRemoteRequestBody(
                                autoFixEnabled = autoFixEnabled,
                                vehicleInfo = vehicleInfo,
                                command = RemoteCommand.HEAD_LIGHTS,
                            )
                        sendRemoteCommandRequest(vehicleInfo = vehicleInfo, remoteRequest = requestBody).collect {
                            emit(it)
                        }
                    }

                RemoteCommandType.Horn ->
                    flow {
                        val requestBody =
                            getRemoteRequestBody(
                                autoFixEnabled = autoFixEnabled,
                                vehicleInfo = vehicleInfo,
                                command = RemoteCommand.SOUND_HORN,
                            )
                        sendRemoteCommandRequest(vehicleInfo = vehicleInfo, remoteRequest = requestBody).collect {
                            emit(it)
                        }
                    }

                RemoteCommandType.TrunkLock, RemoteCommandType.TailgateLock ->
                    flow {
                        val requestBody =
                            getRemoteRequestBody(
                                autoFixEnabled = autoFixEnabled,
                                vehicleInfo = vehicleInfo,
                                command = RemoteCommand.TRUNK_LOCK,
                            )
                        sendRemoteCommandRequest(vehicleInfo = vehicleInfo, remoteRequest = requestBody).collect {
                            emit(it)
                        }
                    }

                RemoteCommandType.TrunkUnlock, RemoteCommandType.TailgateUnlock ->
                    flow {
                        val requestBody =
                            getRemoteRequestBody(
                                autoFixEnabled = autoFixEnabled,
                                vehicleInfo = vehicleInfo,
                                command = RemoteCommand.TRUNK_UNLOCK,
                            )
                        sendRemoteCommandRequest(vehicleInfo = vehicleInfo, remoteRequest = requestBody).collect {
                            emit(it)
                        }
                    }

                RemoteCommandType.Hazard ->
                    flow {
                        val requestBody =
                            getRemoteRequestBody(
                                autoFixEnabled = autoFixEnabled,
                                vehicleInfo = vehicleInfo,
                                command =
                                    if (vehicleInfo.generationType == Generation.CY17) {
                                        SeventeenRemoteCommand.HAZARD_ON_17
                                    } else {
                                        RemoteCommand.HAZARD_ON_17P
                                    },
                            )
                        sendRemoteCommandRequest(vehicleInfo = vehicleInfo, remoteRequest = requestBody).collect {
                            emit(it)
                        }
                    }

                RemoteCommandType.Buzzer ->
                    flow {
                        val requestBody =
                            getRemoteRequestBody(
                                autoFixEnabled = autoFixEnabled,
                                vehicleInfo = vehicleInfo,
                                command = RemoteCommand.BUZZER_WARNING,
                            )
                        sendRemoteCommandRequest(vehicleInfo = vehicleInfo, remoteRequest = requestBody).collect {
                            emit(it)
                        }
                    }

                else -> {
                    throw UnsupportedOperationException("Unknown Remote Commands")
                }
            }

        override fun getEngineStatusRequest(vehicleInfo: VehicleInfo): Flow<Resource<EngineStatusResponse?>> =
            when (vehicleInfo.generationType) {
                Generation.NG86 ->
                    flow {
                        val response =
                            repository.getNG86RemoteEngineStatus(
                                vin = vehicleInfo.vin,
                                brand = vehicleInfo.brand,
                            )
                        emit(response)
                    }

                Generation.CY17 ->
                    flow {
                        val response =
                            repository.getCY17RemoteEngineStatus(
                                vin = vehicleInfo.vin,
                                brand = vehicleInfo.brand,
                            )
                        emit(response)
                    }

                Generation.CY17PLUS ->
                    flow {
                        val response =
                            repository.getCY17PlusRemoteEngineStatus(
                                vin = vehicleInfo.vin,
                                brand = vehicleInfo.brand,
                            )
                        emit(response)
                    }
                else -> {
                    flow {
                        val response =
                            repository.getCY17PlusRemoteEngineStatus(
                                vin = vehicleInfo.vin,
                                brand = vehicleInfo.brand,
                            )
                        emit(response)
                    }
                }
            }

        override fun launchRemotePark() {
            analyticsLogger.logEvent(AnalyticsEvent.REMOTE_PARKING_CARD_LAUNCH)
            rpcSdk.launchFramework()
        }

        override suspend fun sendRemoteCommandRequest(
            vehicleInfo: VehicleInfo,
            remoteRequest: RemoteRequest,
        ): Flow<Resource<RemoteBaseResponse?>> =
            when (vehicleInfo.generationType) {
                Generation.NG86 ->
                    flow {
                        val response =
                            repository.ng86remoteRequest(
                                vin = vehicleInfo.vin,
                                bodyRequest = remoteRequest,
                            )
                        emit(
                            response,
                        )
                    }

                Generation.CY17 ->
                    flow {
                        val response =
                            repository.remoteRequestForPre17(
                                vin = vehicleInfo.vin,
                                bodyRequest = remoteRequest,
                                brand = vehicleInfo.brand,
                            )
                        emit(
                            response,
                        )
                    }

                Generation.CY17PLUS ->
                    flow {
                        val response =
                            repository.remoteRequest(
                                vin = vehicleInfo.vin,
                                bodyRequest = remoteRequest,
                            )
                        emit(
                            response,
                        )
                    }
                else ->
                    flow {
                        val response =
                            repository.remoteRequest(
                                vin = vehicleInfo.vin,
                                bodyRequest = remoteRequest,
                            )
                        emit(
                            response,
                        )
                    }
            }

        private fun getRemoteRequestBody(
            autoFixEnabled: Boolean,
            vehicleInfo: VehicleInfo,
            command: BaseRemoteCommand,
        ): RemoteRequest {
            val remoteCommand: RemoteCommand
            val requestCommand: Any
            val request = RemoteRequest()
            if (!(command is RemoteCommand || command is SeventeenRemoteCommand)) {
                return request
            }
            if (command is RemoteCommand) {
                remoteCommand = command
                request.trunkAttribute = remoteCommand.trunkAttribute
                request.beepCount = remoteCommand.beepCount
                requestCommand = remoteCommand.code
            } else {
                val temp17Command = command as SeventeenRemoteCommand
                requestCommand = Command(temp17Command.code, temp17Command.value)
            }
            request.command = requestCommand
            request.guid = preferenceModel?.getGuid()
            request.deviceId = preferenceModel?.getDeviceToken()
            request.autoFixPopup = autoFixEnabled
            val vin = vehicleInfo.vin
            request.vin = vin

            return request
        }
    }
