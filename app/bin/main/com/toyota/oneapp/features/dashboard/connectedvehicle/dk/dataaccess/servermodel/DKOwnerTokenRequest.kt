package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.dataaccess.servermodel

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class DKOwnerTokenRequest(
    @SerializedName("tokenExpirationDate")
    val tokenExpirationDate: String,
) : Parcelable

@Parcelize
data class DKOwnerTokenResponse(
    @SerializedName("tokenId")
    val tokenId: String,
    @SerializedName("tokenExpirationDate")
    val tokenExpirationDate: String,
    @SerializedName("tokenValue")
    val tokenValue: String,
    @SerializedName("vehicleId")
    val vehicleId: String,
    @SerializedName("lukInfo")
    val lukInfo: String,
) : Parcelable
