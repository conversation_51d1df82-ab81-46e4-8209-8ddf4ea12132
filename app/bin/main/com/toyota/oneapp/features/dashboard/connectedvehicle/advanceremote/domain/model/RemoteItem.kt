package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model

import com.toyota.oneapp.R

data class RemoteItem(
    val type: RemoteType,
    var title: Int = 0,
    val iconPath: Int,
    var showProgress: Boolean = false,
    var showTimer: <PERSON><PERSON><PERSON> = false,
    var disableCommand: Boolean = false,
    var remoteCommandType: RemoteCommandType,
    var progressText: Int = R.string.remote_connecting,
)

enum class RemoteType {
    PRIMARY,
    SECONDARY,
}

enum class RemoteCommandType {
    DoorLock,
    DoorUnlock,
    EngineStart,
    EngineStop,
    Buzzer,
    Climate,
    Horn,
    Hazard,
    TrunkLock,
    TrunkUnlock,
    TailgateLock,
    TailgateUnlock,
    Lights,
    Park,
    REMOTE_DK_LOCK,
    REMOTE_DK_UNLOCK,
}

data class RemoteProgress(
    var title: Int = 0,
    var progressState: ProgressState,
    var isRemoteStop: Boolean = false,
)

enum class ProgressState {
    STARTED,
    IN_PROGRESS,
    IN_PROGRESS_75,
    COMPLETED,
    IDLE,
}
