package com.toyota.oneapp.features.dashboard.dashboard.presentation

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.compose.rememberNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.navigation.OANavigationGraph
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class)
@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun OADashboardBottomSheetScreen(
    bottomSheetState: ModalBottomSheetState,
    goToVehicleInfo: Boolean = false,
) {
    val dashboardViewModel = (LocalContext.current as OADashboardActivity).dashboardViewModel

    val bottomSheetNavHostController = rememberNavController()
    val coroutineScope = rememberCoroutineScope()
    var isSheetOpened by remember { mutableStateOf(false) }

    BackHandler(bottomSheetState.isVisible) {
        val currentBackstack = bottomSheetNavHostController.currentBackStackEntry
        coroutineScope.launch {
            if (currentBackstack?.destination?.route == OAScreen.VehicleInfo.route) {
                bottomSheetState.hide()
            }
        }
    }

    LaunchedEffect(bottomSheetState.currentValue) {
        when (bottomSheetState.currentValue) {
            ModalBottomSheetValue.Hidden -> {
                if (isSheetOpened) {
                    isSheetOpened = false
                    bottomSheetNavHostController.popBackStack(OAScreen.VehicleInfo.route, false)
                }
            }

            ModalBottomSheetValue.Expanded -> {
                isSheetOpened = true
            }
            else -> {}
        }
    }
    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .background(AppTheme.colors.tertiary15),
    ) {
        if (goToVehicleInfo) {
            Spacer(modifier = Modifier.height(8.dp))

            Image(
                painter = painterResource(id = R.drawable.ic_drag_indicator),
                contentDescription = stringResource(id = R.string.content_drag),
                modifier =
                    Modifier
                        .width(28.dp)
                        .height(4.dp)
                        .align(Alignment.CenterHorizontally),
                colorFilter = ColorFilter.tint(AppTheme.colors.outline01),
            )

            Spacer(modifier = Modifier.height(8.dp))
        }

        Scaffold {
            OANavigationGraph(
                modalBottomSheetState = bottomSheetState,
                navController = bottomSheetNavHostController,
                analyticsLogger = dashboardViewModel.analyticsLogger,
                startDestination =
                    if (goToVehicleInfo) {
                        OAScreen.VehicleInfo.route
                    } else {
                        OAScreen.AccountNotification.route
                    },
            )
        }
    }
}
