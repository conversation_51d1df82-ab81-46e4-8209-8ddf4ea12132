package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.extension

import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.AdvanceRemoteViewModel

fun AdvanceRemoteViewModel.updatedAnalyticsLog(command: RemoteCommandType) {
    when (command) {
        RemoteCommandType.EngineStart ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.DASHBOARD_REMOTE_START,
            )
        RemoteCommandType.EngineStop ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.DASHBOARD_REMOTE_STOP,
            )
        RemoteCommandType.Buzzer ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.REMOTE_COMMANDS.eventName,
                AnalyticsEventParam.REMOTE_BUZZER,
            )
        RemoteCommandType.DoorLock ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.DASHBOARD_REMOTE_DOOR_LOCK,
            )
        RemoteCommandType.DoorUnlock ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.DASHBOARD_REMOTE_DOOR_UN_LOCK,
            )
        RemoteCommandType.TrunkLock ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.REMOTE_COMMANDS.eventName,
                AnalyticsEventParam.REMOTE_TRUNK_LOCK,
            )
        RemoteCommandType.TrunkUnlock ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.REMOTE_COMMANDS.eventName,
                AnalyticsEventParam.REMOTE_TRUNK_UN_LOCK,
            )
        RemoteCommandType.TailgateLock ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.REMOTE_COMMANDS.eventName,
                AnalyticsEventParam.REMOTE_TAILGATE_LOCK,
            )
        RemoteCommandType.TailgateUnlock ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.REMOTE_COMMANDS.eventName,
                AnalyticsEventParam.REMOTE_TAILGATE_UN_LOCK,
            )
        RemoteCommandType.Lights ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.REMOTE_COMMANDS.eventName,
                AnalyticsEventParam.REMOTE_LIGHTS,
            )
        RemoteCommandType.Climate ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.REMOTE_COMMANDS.eventName,
                AnalyticsEventParam.REMOTE_CLIMATE,
            )
        RemoteCommandType.Hazard ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.REMOTE_COMMANDS.eventName,
                AnalyticsEventParam.REMOTE_HAZARD,
            )
        RemoteCommandType.Horn ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.REMOTE_COMMANDS.eventName,
                AnalyticsEventParam.REMOTE_HORN,
            )
        else -> {}
    }
}
