package com.toyota.oneapp.features.dashboard.connectedvehicle.cas

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.RadioButton
import androidx.compose.material.RadioButtonDefaults
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OAFootNote1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CASFeedback
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.FeedbackDetails
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.model.ReportFlowEvents
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun CasReportScreen(
    sheetState: ModalBottomSheetState,
    reportEvents: ReportFlowEvents,
    modifier: Modifier = Modifier,
) {
    val viewModel = (LocalContext.current as OADashboardActivity).connectedVehicleViewModel
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var selectedOption by remember { mutableStateOf<String?>(null) }
    var otherOptionText by remember { mutableStateOf(ToyotaConstants.EMPTY_STRING) }
    val coroutineScope = rememberCoroutineScope()

    fun onReportClicked(
        option: String?,
        details: String,
        index: Int?,
    ) {
        index?.let {
            viewModel.updateCasAnalytics(
                AnalyticsEventParam.CAS_REPORT_CTA,
                getParamKeyForReason(it),
            )
        }
        val casFeedback =
            CASFeedback(
                eventId = reportEvents.eventId,
                feedback =
                    FeedbackDetails(
                        reason = option,
                        details = details,
                    ),
            )
        viewModel.updateCASFeedback(casFeedback)
    }

    fun onDismiss() {
        errorMessage = null
        selectedOption = null
        otherOptionText = ToyotaConstants.EMPTY_STRING
    }

    fun onCancelClicked() {
        viewModel.updateCasAnalytics(AnalyticsEventParam.CAS_REPORT_CANCEL_CTA)
        onDismiss()
        coroutineScope.launch { sheetState.hide() }
    }

    BackHandler {
        coroutineScope.launch {
            onCancelClicked()
        }
    }

    DisposableEffect(!sheetState.isVisible) {
        onDispose { onDismiss() }
    }

    Column(
        modifier =
            modifier
                .fillMaxSize()
                .background(AppTheme.colors.tertiary15),
        verticalArrangement = Arrangement.SpaceBetween,
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            CasSwipeDown()
            ReportTitle(title = reportEvents.title ?: ToyotaConstants.EMPTY_STRING)
            reportEvents.reasons?.let { reason ->
                ReportOptionsList(
                    options = reason,
                    selectedOption = selectedOption,
                    onOptionSelected = {
                        selectedOption = it
                        errorMessage = null
                    },
                )
            }
            if (selectedOption == reportEvents.reasons?.last()) {
                ReportTextField(
                    provideDetailsTitle = reportEvents.provideDetailsTitle,
                    value = otherOptionText,
                    onValueChange = {
                        otherOptionText = it
                        errorMessage = null
                    },
                    errorMessage = errorMessage,
                )
            }
            errorMessage?.let { errorMessage ->
                if (selectedOption.isNullOrEmpty()) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Start,
                    ) {
                        WarningMessage(errorMessage = errorMessage)
                    }
                }
            }
        }
        ReportFooter(
            onReportClicked = {
                if (selectedOption.isNullOrEmpty()) {
                    errorMessage = reportEvents.selectionInLineError
                } else if (selectedOption == reportEvents.reasons?.last() && otherOptionText.isEmpty()) {
                    errorMessage = reportEvents.provideDetailsInLineError
                } else {
                    val index = reportEvents.reasons?.indexOf(selectedOption)
                    onReportClicked(selectedOption, otherOptionText, index)
                }
            },
            onCancelClicked = {
                onCancelClicked()
            },
            reportEvents = reportEvents,
        )
    }
}

private fun getParamKeyForReason(reason: Int): String {
    return when (reason) {
        0 -> AnalyticsEventParam.CAS_REPORT_NOTHING_IN_VEHICLE
        1 -> AnalyticsEventParam.CAS_REPORT_UNWANTED_ALERT
        else -> AnalyticsEventParam.CAS_REPORT_OTHERS
    }
}

@Composable
fun ReportTextField(
    provideDetailsTitle: String?,
    value: String,
    onValueChange: (String) -> Unit,
    errorMessage: String?,
) {
    Column {
        OABody4TextView(
            text = provideDetailsTitle ?: ToyotaConstants.EMPTY_STRING,
            color = AppTheme.colors.tertiary03,
            modifier = Modifier.padding(horizontal = 16.dp),
        )
        OutlinedTextField(
            textStyle = AppTheme.fontStyles.callout1,
            value = value,
            onValueChange = onValueChange,
            colors =
                TextFieldDefaults.outlinedTextFieldColors(
                    textColor = AppTheme.colors.tertiary03,
                    unfocusedBorderColor = AppTheme.colors.tertiary10,
                    focusedBorderColor = AppTheme.colors.tertiary10,
                ),
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(horizontal = 16.dp),
        )
        if (!errorMessage.isNullOrEmpty()) {
            WarningMessage(errorMessage = errorMessage)
        }
    }
}

@Composable
private fun WarningMessage(errorMessage: String) {
    Row(
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(horizontal = 16.dp),
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ic_caution),
            contentDescription = ToyotaConstants.EMPTY_STRING,
            modifier = Modifier.size(14.dp),
            tint = AppTheme.colors.brand01,
        )
        Spacer(modifier = Modifier.width(4.dp))
        OACallOut1TextView(
            text = errorMessage,
            color = AppTheme.colors.brand01,
            modifier = Modifier.padding(bottom = 2.dp),
        )
    }
}

@Composable
fun ReportFooter(
    onReportClicked: () -> Unit,
    onCancelClicked: () -> Unit,
    reportEvents: ReportFlowEvents,
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        OACallOut1TextView(
            text = reportEvents.negativeCTA ?: ToyotaConstants.EMPTY_STRING,
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier
                    .clickable(
                        onClick = onCancelClicked,
                    )
                    .testTagID(AccessibilityId.ID_CAS_REPORT_CANCEL_CTA),
        )
        Spacer(modifier = Modifier.height(10.dp))
        PrimaryButton02(
            modifier =
                Modifier
                    .wrapContentWidth()
                    .wrapContentHeight()
                    .testTagID(AccessibilityId.ID_CAS_REPORT_REPORT_CTA),
            text = reportEvents.positiveCTA ?: ToyotaConstants.EMPTY_STRING,
            click = onReportClicked,
        )
        Spacer(modifier = Modifier.height(10.dp))
        OAFootNote1TextView(
            text = reportEvents.disclaimer ?: ToyotaConstants.EMPTY_STRING,
            color = AppTheme.colors.tertiary03,
            textAlign = TextAlign.Center,
        )
        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
fun ReportTitle(title: String) {
    OASubHeadLine3TextView(
        text = title,
        textAlign = TextAlign.Center,
        color = AppTheme.colors.tertiary03,
        modifier = Modifier.testTagID(AccessibilityId.ID_CAS_REPORT_APP_BAR_TEXT),
    )
    Spacer(modifier = Modifier.height(20.dp))
}

@Composable
fun ReportOptionsList(
    options: List<String>,
    selectedOption: String?,
    onOptionSelected: (String) -> Unit,
) {
    Column(
        modifier =
            Modifier
                .fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        options.forEach { option ->
            Row(
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.clickable { onOptionSelected(option) },
            ) {
                RadioButton(
                    selected = (option == selectedOption),
                    onClick = { onOptionSelected(option) },
                    colors =
                        RadioButtonDefaults.colors(
                            selectedColor = Color.Black,
                        ),
                )
                OACallOut1TextView(
                    text = option,
                    color = AppTheme.colors.tertiary03,
                    modifier = Modifier.padding(start = 8.dp),
                )
            }
        }
    }
}
