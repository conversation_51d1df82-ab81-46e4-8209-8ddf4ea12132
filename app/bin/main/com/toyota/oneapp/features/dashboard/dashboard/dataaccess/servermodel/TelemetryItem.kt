package com.toyota.oneapp.features.dashboard.dashboard.dataaccess.servermodel

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class TelemetryItem(
    val unit: String,
    @SerializedName("value")
    val _value: Double,
) : Parcelable {
    val value: Double
        get() = _value

    fun hasTirePressureWarning(): Boolean {
        return value < 30
    }

    fun convertThousandsWithComma(): String {
        return "%,d".format(this.value.toInt())
    }
}
