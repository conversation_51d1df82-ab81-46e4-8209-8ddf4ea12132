package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.helper

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import com.toyota.oneapp.R
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.RemoteActivateIntent
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.RemoteStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.States
import com.toyota.oneapp.ui.newdashboard.RemoteActivateActivity
import com.toyota.oneapp.ui.newdashboard.SendActivationCodeActivity
import com.toyota.oneapp.ui.remote.RemoteActivationActivity
import com.toyota.oneapp.util.ToyUtil
import toyotaone.commonlib.permission.PermissionUtil

object RemoteStatesHelper {
    fun navigatePage(
        remoteActivateIntent: RemoteActivateIntent,
        context: Context,
    ) {
        if (remoteActivateIntent.isSendActivationCodeActivity) {
            context.startActivity(Intent(context, SendActivationCodeActivity::class.java))
        } else if (remoteActivateIntent.isRemoteActivateActivity) {
            context.startActivity(Intent(context, RemoteActivateActivity::class.java))
        } else {
            context.startActivity(Intent(context, RemoteActivationActivity::class.java))
        }
    }

    fun contactSupport(
        context: Context,
        region: String,
        brand: String,
    ) {
        ToyUtil.contactSupport(
            context,
            region,
            brand,
        )
    }

    fun checkNotificationEnabled(
        context: Context,
        remoteStates: RemoteStates,
        isDkSecondaryVehicle: Boolean,
    ): RemoteStates {
        val isNotificationDisabled =
            PermissionUtil().checkNotificationPermissionStatus(context) !=
                PackageManager.PERMISSION_GRANTED &&
                !isDkSecondaryVehicle

        return if (isNotificationDisabled) {
            remoteStates.apply {
                status = States.NOTIFICATIONS_DISABLED
                title = R.string.Dashboard_notification_disabled_title
                subHeading = R.string.Dashboard_notification_disabled_note
                ctaText = R.string.Dashboard_turn_on_notifications
            }
        } else {
            remoteStates
        }
    }
}
