package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.features.core.util.LocalBottomSheetState
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.application.RemoteStatesUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.PopupStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.RemoteActivateIntent
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.RemoteContactIntent
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.RemotePopupStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.RemoteStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.States
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.extension.updateRemotePopupAnalyticsLogger
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.helper.RemoteStatesHelper
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.dashboard.util.DashboardConstants
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class RemoteStatesViewModel
    @Inject
    constructor(
        private val remoteStatesUseCase: RemoteStatesUseCase,
        val analyticsLogger: AnalyticsLogger,
        private val applicationData: ApplicationData,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        val sharedDataSource: SharedDataSource,
    ) : ViewModel() {
        private val _remoteStates = MutableStateFlow(value = RemoteStates())
        val remoteStates: StateFlow<RemoteStates> get() = _remoteStates

        private val _popupStates = MutableStateFlow(value = RemotePopupStates())
        val popupStates: StateFlow<RemotePopupStates> get() = _popupStates

        var vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()

        val mDashboardRefresh = MutableStateFlow(value = false)
        val dashboardRefresh: StateFlow<Boolean> get() = mDashboardRefresh

        private val _showProgress = MutableStateFlow(value = false)
        val showProgress: StateFlow<Boolean> get() = _showProgress

        init {
            viewModelScope.launch {
                applicationData.getSelectedVehicleState().collect {
                    it?.let {
                        vehicleInfo = it
                        _remoteStates.value = remoteStatesUseCase.getRemoteStates(it)
                    }
                }
            }
        }

        fun updateRemoteStatePopup(remoteStates: RemoteStates) {
            _popupStates.value = remoteStatesUseCase.updateRemoteStatePopup(remoteStates)
        }

        suspend fun popUpAction(
            popupStatus: RemotePopupStates,
            isCancelCTA: Boolean = false,
        ) {
            updateRemotePopupAnalyticsLogger(popupStatus.popupStatus, isCancelCTA = isCancelCTA)
            when (popupStatus.popupStatus) {
                PopupStates.REMOTE_SHARED ->
                    vehicleInfo?.let {
                        _showProgress.value = true
                        remoteStatesUseCase.assignRemoteUser(it).collect {
                            mDashboardRefresh.value = true
                        }
                    }
                else -> {
                    mDashboardRefresh.value = true
                }
            }
        }

        fun checkConditionToShowSOS(
            context: OADashboardActivity,
            coroutineScope: CoroutineScope,
            bottomSheet: LocalBottomSheetState,
        ) {
            applicationData.getSelectedVehicle()?.let { vehicleInfo ->
                if (shouldShowSOS(vehicleInfo)) {
                    updateRemoteStatePopup(
                        RemoteStates(status = States.ACTIVATE_REMOTE_LMEX),
                    )
                    coroutineScope.launchPrimaryBottomSheetAction(
                        bottomSheet,
                        DashboardConstants.ID_REMOTE_STATES_BOTTOM_SHEET,
                    ) { sheetState ->
                        RemoteStatesBottomSheet(sheetState, bottomSheet)
                    }
                } else {
                    RemoteStatesHelper.navigatePage(
                        checkActivationPage(),
                        context,
                    )
                }
            }
        }

        fun shouldShowSOS(vehicleInfo: VehicleInfo): Boolean =
            vehicleInfo.isLexusBrand && (isCY17OrLater(vehicleInfo) || isLMEXPhase2NotRequired(vehicleInfo))

        fun isCY17OrLater(vehicleInfo: VehicleInfo): Boolean = vehicleInfo.isCY17Plus

        fun isLMEXPhase2NotRequired(vehicleInfo: VehicleInfo): Boolean =
            vehicleInfo.is21MMVehicle &&
                vehicleInfo.isVehicleMexican &&
                !remoteStatesUseCase.isLMEXPhase2(vehicleInfo)

        fun checkActivationPage(): RemoteActivateIntent {
            val vehicleInfo = applicationData.getSelectedVehicle() ?: return RemoteActivateIntent()
            return when {
                (
                    vehicleInfo.isCY17Plus &&
                        !vehicleInfo.isLexusBrand &&
                        !IDPData
                            .getInstance(oneAppPreferenceModel)
                            .getPhoneNumber()
                            .isNullOrEmpty()
                ) ->
                    RemoteActivateIntent(isSendActivationCodeActivity = true)
                !vehicleInfo.is21MMVehicle ||
                    (
                        vehicleInfo.isVehicleMexican &&
                            !remoteStatesUseCase.isLMEXPhase2(
                                vehicleInfo,
                            )
                    ) -> RemoteActivateIntent(isRemoteActivateActivity = true)

                else -> RemoteActivateIntent(isRemoteActivationActivity = true)
            }
        }

        fun getContactIntent(): RemoteContactIntent? {
            vehicleInfo?.let {
                return RemoteContactIntent(
                    region = it.region,
                    brand = it.brand,
                )
            } ?: run {
                return null
            }
        }
    }
