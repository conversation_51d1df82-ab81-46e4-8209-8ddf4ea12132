package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.extension

import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.PopupStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.States
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.RemoteStatesViewModel

fun RemoteStatesViewModel.updateRemoteStatesAnalyticsLogger(status: States) {
    when (status) {
        States.NOTIFICATIONS_DISABLED ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.RS_NOTIFICATION_DISABLED,
            )
        States.SUBSCRIPTION_EXPIRED ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.RS_SUBSCRIPTION_EXPIRED,
            )
        States.SUBSCRIPTION_CANCELLED ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.RS_SUBSCRIPTION_CANCELLED,
            )
        States.ACTIVATE_REMOTE ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.RS_ACTIVATE_REMOTE,
            )
        States.ACTIVATION_PENDING ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.RS_ACTIVATE_REMOTE_PENDING,
            )
        States.ACTIVATE_FAILED ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.RS_UNABLE_TO_ACTIVATE_REMOTE,
            )
        States.ACTIVATION_ERROR ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.RS_REMOTE_ACTIVATION_ERROR,
            )
        States.VEHICLE_STOLEN ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.RS_VEHICLE_STOLEN,
            )
        States.REMOTE_SHARED ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.RS_REMOTE_SHARE_CTA,
            )
        else -> {}
    }
}

fun RemoteStatesViewModel.updateRemotePopupAnalyticsLogger(
    status: PopupStates,
    isCancelCTA: Boolean,
) {
    when (status) {
        PopupStates.AUTH_REQUIRED_LMEX ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.RS_REMOTE_LEXUS_AUTH_CTA,
            )
        PopupStates.REMOTE_SHARED ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                AnalyticsEventParam.RS_REMOTE_SHARE_POPUP_CTA,
            )
        PopupStates.VEHICLE_STOLEN ->
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.DASHBOARD_CARD.eventName,
                if (isCancelCTA) {
                    AnalyticsEventParam.RS_REMOTE_STOLEN_VEHICLE_POPUP_CANCEL_CTA
                } else {
                    AnalyticsEventParam.RS_REMOTE_STOLEN_VEHICLE_POPUP_CONFIRM_CTA
                },
            )
        else -> {}
    }
}
