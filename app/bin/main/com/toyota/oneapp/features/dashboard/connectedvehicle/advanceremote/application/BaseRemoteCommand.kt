package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application

import com.toyota.oneapp.model.remote.TrunkAttribute

interface BaseRemoteCommand

private const val RC_BUZZER_BEEP_COUNT = 10

enum class RemoteCommand constructor(
    val code: String,
    val value: Int,
    val trunkAttribute: TrunkAttribute? = null,
    val beepCount: Int? = null,
) : BaseRemoteCommand {
    ENGINE_START_17P("engine-start", 1),
    ENGINE_STOP_17P("engine-stop", 0),
    DOOR_LOCK_17P("door-lock", 1),
    DOOR_UNLOCK_17P("door-unlock", 0),
    HAZARD_ON_17P("hazard-on", 1),
    HAZARD_OFF_17P("hazard-off", 0),
    SOUND_HORN("sound-horn", 0),
    HEAD_LIGHTS("headlight-on", 0),
    BUZZER_WARNING("buzzer-warning", 0, beepCount = RC_BUZZER_BEEP_COUNT),
    TRUNK_LOCK("door-lock", 0, TrunkAttribute()),
    TRUNK_UNLOCK("door-unlock", 0, TrunkAttribute()),
}

enum class SeventeenRemoteCommand constructor(val code: String, val value: Int) : BaseRemoteCommand {
    ENGINE_START_17("RES", 1),
    ENGINE_STOP_17("RES", 2),
    DOOR_LOCK_17("DL", 1),
    DOOR_UNLOCK_17("DL", 2),
    HAZARD_ON_17("HZ", 2),
    HAZARD_OFF_17("HZ", 2),
}
