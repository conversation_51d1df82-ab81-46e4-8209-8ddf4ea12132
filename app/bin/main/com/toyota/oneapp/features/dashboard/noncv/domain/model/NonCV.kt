package com.toyota.oneapp.features.dashboard.noncv.domain.model

import com.toyota.oneapp.features.dashboard.noncv.dataaccess.model.ServiceHistoryPayload
import com.toyota.oneapp.features.dealerservice.domain.model.Dealer
import org.apache.commons.lang3.ObjectUtils

data class NonCV(
    val title: Int,
    val subTitle: String,
    val iconPath: Int,
    val tickIcon: Int? = null,
    val nonCVType: NonCVType? = null,
    val onclick: String,
    val maintenancePayloadData: ObjectUtils.Null? = null,
    val serviceHistoryPayloadData: ServiceHistoryPayload? = null,
    val preferredDealerPayloadData: Dealer? = null,
)

enum class NonCVType {
    PREFERRED_DEALER,
    MAINTENANCE_SCHEDULE,
    SERVICE_HISTORY,
    APPOINTMENTS,
}
