package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.dataaccess.servermodel

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class DkTokenResponse(
    @SerializedName("access_token")
    val accessToken: String,
    @SerializedName("token_type")
    val tokenType: String,
    @SerializedName("expires_in")
    val expires: Int,
    @SerializedName("scope")
    val scope: String,
    @SerializedName("role")
    val role: String,
    @SerializedName("authorities")
    val auth: ArrayList<String>,
    @SerializedName("jti")
    val jti: String,
) : Parcelable
