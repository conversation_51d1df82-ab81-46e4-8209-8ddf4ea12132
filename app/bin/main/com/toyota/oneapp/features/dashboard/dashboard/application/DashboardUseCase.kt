package com.toyota.oneapp.features.dashboard.dashboard.application

import com.toyota.oneapp.features.core.commonapicalls.application.ElectricStatusState
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface DashboardUseCase {
    suspend fun getVehicleList(selectedVehicle: VehicleInfo?): Flow<MutableList<VehicleInfo>?>

    suspend fun fetchTelemetry(vehicleInfo: VehicleInfo): Flow<TelemetryState>

    fun fetchElectricStatus(vehicleInfo: VehicleInfo): Flow<ElectricStatusState>

    suspend fun fetchSoftwareUpdates(vehicleInfo: VehicleInfo): Flow<SoftwareUpdateDeepLinkState>

    fun fetchProfileDetails(): Flow<ProfileInfoResponse?>
}
