package com.toyota.oneapp.features.dashboard.connectedvehicle.climate.settings.domain.model

import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateState
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateType

data class ClimateButtonState(
    val type: ClimateType,
    val ctaType: CTA,
    val isLayoutVisible: Boolean = true,
) : ClimateState()

enum class CTA {
    SAVE,
    START,
    STOP,
}
