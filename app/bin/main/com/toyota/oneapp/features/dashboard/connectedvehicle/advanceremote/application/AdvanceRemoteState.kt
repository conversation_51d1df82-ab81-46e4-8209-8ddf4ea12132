package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application

import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteItem
import kotlinx.coroutines.flow.MutableStateFlow

sealed class AdvanceRemoteState {
    object Loading : AdvanceRemoteState()

    object DigitalKeyDownload : AdvanceRemoteState()

    class Success(val data: MutableStateFlow<MutableList<RemoteItem>>) : AdvanceRemoteState()

    object NoRemoteCommands : AdvanceRemoteState()

    class Error(val errorCode: String?, val errorMessage: String?) : AdvanceRemoteState()
}
