package com.toyota.oneapp.features.dashboard.dashboard.presentation

import android.os.Bundle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.apptentive.ApptentiveService
import com.toyota.oneapp.digitalkey.DigitalKeyLocalData
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.fcm.ToyotaFCMService
import com.toyota.oneapp.features.core.commonapicalls.application.ElectricStatusState
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoResponse
import com.toyota.oneapp.features.core.presentation.OABaseViewModel
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.utils.ShortCutActions
import com.toyota.oneapp.features.dashboard.dashboard.application.DashboardUseCase
import com.toyota.oneapp.features.dashboard.dashboard.application.SoftwareUpdateDeepLinkState
import com.toyota.oneapp.features.dashboard.dashboard.application.TelemetryState
import com.toyota.oneapp.features.dashboard.dashboard.domain.FetchData
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.features.dashboard.dashboard.domain.model.DeepLinkState
import com.toyota.oneapp.features.dealerservice.domain.model.PREFERRED_DEALER_DATA_KEY
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.retryWhen
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@HiltViewModel
class DashboardViewModel
    @Inject
    constructor(
        val analyticsLogger: AnalyticsLogger,
        val applicationData: ApplicationData,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val dispatcherProvider: DispatcherProvider,
        private val dashboardUseCase: DashboardUseCase,
        private val digitalMopKeyUtils: DigitalMopKeyUtils,
        private val digitalKeyLocalData: DigitalKeyLocalData,
        val sharedDataSource: SharedDataSource,
        apptentiveService: ApptentiveService,
    ) : OABaseViewModel<DashboardEvent>(),
        ApptentiveService by apptentiveService {
        private val tag = "DashboardViewModel"

        var remoteSheetHeight: Dp = 0.dp

        private val _isRefreshing = MutableStateFlow(value = false)
        val isRefreshing = _isRefreshing.asStateFlow()

        val isPullDkRefreshFlow = MutableStateFlow(value = false)
        val isPullDkRefresh = isPullDkRefreshFlow.asStateFlow()

        private val imageDataFlow = MutableStateFlow(value = "")
        val imageData = imageDataFlow.asStateFlow()

        private val secondaryDkVehicleInfo: VehicleInfo? = digitalKeyLocalData.getVehicleInfo()

        val isNonConnectedVehicleFlow = MutableStateFlow(value = false)
        val isNonConnectedVehicle = isNonConnectedVehicleFlow.asStateFlow()

        private val _isMarketingBannerAnnouncementAvailable = MutableStateFlow(value = false)
        val isMarketingBannerAnnouncementAvailable = _isMarketingBannerAnnouncementAvailable.asStateFlow()

        private val _softwareUpdateFlow =
            MutableStateFlow<SoftwareUpdateDeepLinkState>(
                value = SoftwareUpdateDeepLinkState.IDLE,
            )
        val softwareUpdateFlow = _softwareUpdateFlow.asStateFlow()

        private val _deepLinkStateFlow =
            MutableStateFlow<DeepLinkState>(
                value = DeepLinkState.IDLE,
            )
        val deepLinkStateFlow = _deepLinkStateFlow.asStateFlow()

        private val _showDashboardProgress =
            MutableStateFlow(
                value = false,
            )
        val showDashboardProgress = _showDashboardProgress.asStateFlow()

        // Profile Data
        // Backing property to hold profile data retrieved from Api
        private val _profileData = MutableStateFlow<ProfileInfoResponse?>(null)
        val profileData: StateFlow<ProfileInfoResponse?> = _profileData

        private val quickActionValueFlow = MutableStateFlow("")
        val quickAction = quickActionValueFlow.asStateFlow()

        private var goToVehicleSoftwareScreen = false

        init {
            applicationData.mergeSecondaryVehicleIfNeeded(secondaryDkVehicleInfo)

            viewModelScope.launch(dispatcherProvider.main()) {
                applicationData.getSelectedVehicleState().collectLatest {
                    if (it != null) {
                        imageDataFlow.value = if (!it.image.isNullOrEmpty()) it.image else ToyotaConstants.EMPTY_STRING
                        loadTelemetry(it)
                    }
                    isNonConnectedVehicle()
                }
            }

            viewModelScope.launch(dispatcherProvider.main()) {
                applicationData.forceVinListRefresh.collect {
                    if (it) {
                        applicationData.resetForceRefreshFlag()
                        onRefresh()
                    }
                }
            }
            fetchData()
            fetchProfileData()
            applicationData.threeDShortAction?.let { setQuickAction(it) }
        }

        fun updateProgressBar(progress: Boolean) {
            _showDashboardProgress.value = progress
        }

        private fun fetchData() {
            viewModelScope.launch(dispatcherProvider.main()) {
                sharedDataSource.fetchDataState.collect { it ->
                    when (it) {
                        FetchData.TELEMETRY -> {
                            applicationData.getSelectedVehicle()?.let {
                                loadTelemetry(it)
                            } ?: let {
                                LogTool.d(
                                    tag,
                                    "Selected Vehicle is Null, Skip Calling Telemetry API.",
                                )
                            }
                        }

                        FetchData.ELECTRIC_STATUS -> {
                            loadElectricStatus()
                        }

                        else -> {}
                    }
                }
            }
        }

        fun syncDigitalKeyStatus() {
            applicationData.getSelectedVehicle()?.let {
                val isMaintenance = it.isFeatureMaintenance(Feature.DIGITAL_KEY)
                if (!isMaintenance) {
                    digitalMopKeyUtils.syncAllKeys(oneAppPreferenceModel)
                }
            }
        }

        override fun onRefresh() {
            onEvent(DashboardEvent.SwipeRefresh)
            applicationData.updateThreeDShortActionStatus(status = false)
            viewModelScope.launch {
                _isRefreshing.emit(true)
                // #1 Vin List
                getVehicleList(applicationData.getSelectedVehicle()).collect { vinListSuccess ->
                    if (vinListSuccess) {
                        applicationData.mergeSecondaryVehicleIfNeeded(secondaryDkVehicleInfo, shouldSelect = false)
                        applicationData.getSelectedVehicle()?.let {
                            if (it.isFeatureEnabled(Feature.DIGITAL_KEY)) {
                                isPullDkRefreshFlow.value = true
                            }
                            // #2 Telemetry
                            val telemetryRequest = async { loadTelemetry(it) }
                            telemetryRequest.await()
                        }
                    }
                }
                if (_showDashboardProgress.value) {
                    _showDashboardProgress.value = false
                }
                _isRefreshing.emit(false)
            }
        }

        override fun onEvent(event: DashboardEvent) {
            when (event) {
                is DashboardEvent.SwipeRefresh -> {
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.DASHBOARD_CARD.eventName,
                        AnalyticsEventParam.DASHBOARD_SWIPE_REFRESH,
                    )
                }
                is DashboardEvent.AccountNotification -> {
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.ACCOUNT_NOTIFICATION_GROUP.eventName,
                        AnalyticsEventParam.ACCOUNT_NOTIFICATION_ICON_TAP,
                    )
                }
                is DashboardEvent.VehicleInfo -> {
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.DASHBOARD_CARD.eventName,
                        AnalyticsEventParam.DASHBOARD_VEHICLE_INFO,
                    )
                }
                is DashboardEvent.VehicleSwitcher -> {
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.DASHBOARD_CARD.eventName,
                        AnalyticsEventParam.DASHBOARD_VEHICLE_SWITCHER_CHEVRON,
                    )
                }
            }
        }

        private fun isNonConnectedVehicle() {
            val isNonCvtVehicle = applicationData.getSelectedVehicle()?.isNonCvtVehicle == true
            val isVehicleGenerationEmpty = applicationData.getSelectedVehicle()?.generation.isNullOrEmpty()
            isNonConnectedVehicleFlow.value = isNonCvtVehicle || isVehicleGenerationEmpty
        }

        fun isSecondaryVehicle(): Boolean = digitalKeyLocalData.getVehicleInfo()?.isDigitalkey == true

        fun hasVehicle(): Boolean = applicationData.getVehicleListState().value?.isEmpty() != true

        fun getSelectedVehicleState(): StateFlow<VehicleInfo?> = applicationData.getSelectedVehicleState()

        fun updateVehicleInfoAnalytics() {
            onEvent(DashboardEvent.VehicleInfo)
        }

        fun updateDarkMode() {
            AppTheme.darkMode.value = oneAppPreferenceModel.isDarkModeEnabled()
        }

        private suspend fun loadTelemetry(vehicleInfo: VehicleInfo) {
            if (vehicleInfo.isNonCvtVehicle || vehicleInfo.isDigitalkey) {
                return
            }
            if (sharedDataSource.getTelemetryState().value != TelemetryState.Loading) {
                sharedDataSource.setTelemetryData(TelemetryState.Loading)
                viewModelScope.launch(dispatcherProvider.main()) {
                    dashboardUseCase.fetchTelemetry(vehicleInfo).flowOn(dispatcherProvider.io()).collect {
                        sharedDataSource.setTelemetryData(it)
                    }
                }
            }
        }

    /*
        Fetch profile data of current logged in user
     */
        fun fetchProfileData() {
            viewModelScope.launch {
                dashboardUseCase
                    .fetchProfileDetails()
                    .catch {
                        _profileData.value = null
                    }.collect { profileInfo ->
                        _profileData.value = profileInfo
                    }
            }
        }

        private suspend fun loadElectricStatus() {
            applicationData.getSelectedVehicleState().value?.let { vehicle ->
                if (sharedDataSource.getElectricStatusState().value != ElectricStatusState.Loading) {
                    sharedDataSource.setElectricStatusState(ElectricStatusState.Loading)
                    viewModelScope.launch(dispatcherProvider.main()) {
                        dashboardUseCase
                            .fetchElectricStatus(vehicle)
                            .flowOn(dispatcherProvider.io())
                            .collect {
                                sharedDataSource.setElectricStatusState(it)
                            }
                    }
                }
            }
        }

        private fun getVehicleList(vehicleInfo: VehicleInfo? = null): Flow<Boolean> =
            flow {
                dashboardUseCase
                    .getVehicleList(vehicleInfo)
                    .flowOn(dispatcherProvider.io())
                    .retryWhen { _, attempt ->
                        LogTool.w(tag, "Vin List Retry Attempt: $attempt")
                        delay(2000)
                        attempt < 3
                    }.catch {
                        LogTool.w(tag, "Vin List Failed: ${it.message}")
                        emit(false)
                    }.collect {
                        LogTool.d(tag, "Vin List Success, Size is : ${it?.size}")
                        if (it == null) {
                            sharedDataSource.resetDataSource()
                        }
                        emit(true)
                    }
            }

        fun getSoftwareUpdates(vehicleInfo: VehicleInfo) {
            viewModelScope.launch {
                dashboardUseCase.fetchSoftwareUpdates(vehicleInfo).collect {
                    _softwareUpdateFlow.value = it
                }
            }
        }

        fun resetSoftwareUpdateState() {
            _softwareUpdateFlow.value = SoftwareUpdateDeepLinkState.IDLE
        }

        fun setVin(vin: String) {
            if ((applicationData.getVehicleList()?.any { it.vin == vin } == true) && vin != applicationData.getSelectedVehicle()?.vin) {
                applicationData.getVehicleList()?.find { it.vin == vin }?.let {
                    applicationData.setSelectedVehicle(it)
                }
            }
        }

        fun deepLinkHandle(
            deepLinkPath: String?,
            bundle: Bundle?,
        ) {
            when (deepLinkPath) {
                Constants.DeepLinkManageSubscription,
                Constants.DeepLinkManageSubscriptions,
                Constants.DeepLinkSubscription,
                -> {
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEventParam.DEEPLINK,
                        AnalyticsEventParam.DEEPLINK_MANAGE_SUBSCRIPTION,
                    )
                    if (applicationData.getSelectedVehicle()?.isConnectedVehicle == true) {
                        _deepLinkStateFlow.value = DeepLinkState.NavigateToManageSubscription
                    }
                }

                Constants.DEEP_LINK_CHARGE_INFO -> {
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToChargeInfo
                }

                Constants.DEEP_LINK_CHARGE_ASSIST_ENROLLMENT_STATUS -> {
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToChargeAssistEnrollmentStatusPath
                }

                Constants.DeepLinkManageProfile -> {
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEventParam.DEEPLINK,
                        AnalyticsEventParam.DEEPLINK_PAYMENT_METHODS,
                    )
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToAccountSettings
                }

                Constants.DeepLinkManageGarage,
                ToyotaFCMService.OTA_UPDATES_21MM,
                ToyotaFCMService.TM_UPDATE_CATEGORY,
                ToyotaFCMService.TM_INSTALL_CATEGORY,
                ToyotaFCMService.TM_RELEASE_CATEGORY,
                -> {
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToVehicleInfo
                }

                Constants.DeepLinkManageGuestDriver -> {
                    val vehicle = applicationData.getSelectedVehicle()
                    if (vehicle?.remoteDisplay == Constants.REMOTE_COMMANDS_ACTIVE) {
                        _deepLinkStateFlow.value = DeepLinkState.NavigateToGuestDriverSettings
                    }
                }
                Constants.DeepLinkManageScheduleService -> {
                    if (getSelectedVehicleState().value?.isFeatureEnabled(Feature.SCHEDULE_MAINTENANCE) == true) {
                        _deepLinkStateFlow.value = DeepLinkState.NavigateToScheduleService
                    }
                }
                Constants.DeepLinkAccountLinking -> {
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToLinkedAccounts
                }
                Constants.DeepLinkPinReset -> {
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToPinReset
                }
                Constants.DeepLinkManagePrivacyPortal -> {
                    if (applicationData.getVehicleList()?.isNotEmpty() == true) {
                        _deepLinkStateFlow.value = DeepLinkState.NavigateToPrivacyPortal
                    }
                }
                Constants.DeepLinkManageDigitalKey -> {
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToManageDigitalKey
                }
                Constants.DEEP_LINK_SHARE_POI -> {
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToSharePOI(bundle)
                }
                Constants.DEEP_LINK_ACTIVATE -> {
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToConnectByCode
                }
                Constants.DeepLinkLastParked -> {
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToLastParked
                }
                ToyotaFCMService.EVGO -> {
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToEVGO
                }
                Constants.DeepLinkCAS -> {
                    _deepLinkStateFlow.value = DeepLinkState.NavigateToVehicleStatus
                }
                Constants.DEEP_LINK_PREFERRED_DEALER -> {
                    _deepLinkStateFlow.value =
                        DeepLinkState.NavigateToPreferredDealers(
                            data = bundle?.getBundle(PREFERRED_DEALER_DATA_KEY),
                        )
                }
                else -> {}
            }
        }

        private fun setQuickAction(value: String) {
            quickActionValueFlow.value = value
        }

        fun remoteFeatureCapable(
            vehicleInfo: VehicleInfo,
            quickAction: String,
        ): Boolean =
            when (quickAction) {
                ShortCutActions.SHORTCUT_START_ENGINE_ACTION.action -> vehicleInfo.isEStartStopCapable
                ShortCutActions.SHORTCUT_DOOR_LOCK_ACTION.action,
                ShortCutActions.SHORTCUT_DOOR_UNLOCK_ACTION.action,
                -> vehicleInfo.isLockUnLockCapable
                else -> false
            }

        fun getQuickActionNotSupportedMessage(
            notificationEnabled: Boolean,
            selectedVehicle: VehicleInfo?,
            quickAction: String,
        ): Int {
            return if (selectedVehicle?.remoteDisplay == 7) {
                if (!notificationEnabled) {
                    R.string.unableToPerformAction
                } else {
                    if (remoteFeatureCapable(selectedVehicle, quickAction)) 0 else R.string.featureNotSupported
                }
            } else {
                return R.string.unableToPerformAction
            }
        }

        fun resetDeepLinkStateFlow() {
            _deepLinkStateFlow.value = DeepLinkState.IDLE
        }

        fun isClimateSettingsAndScheduleAvailable(): Boolean =
            applicationData.getSelectedVehicle()?.isClimateSettingsAndScheduleCapable ?: false

        fun setGoToVehicleSoftwareScreen(value: Boolean) {
            goToVehicleSoftwareScreen = value
        }

        fun getGoToVehicleSoftwareScreen(): Boolean {
            return false // Disabled behavior to match iOS current implementation.
            // return goToVehicleSoftwareScreen
        }
    }
