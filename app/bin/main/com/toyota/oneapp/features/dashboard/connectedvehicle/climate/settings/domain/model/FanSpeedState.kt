package com.toyota.oneapp.features.dashboard.connectedvehicle.climate.settings.domain.model

import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateState
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateType

data class FanSpeedState(
    val type: ClimateType,
    var fanSpeed: Int = 0,
    var minSpeed: Int = 0,
    var maxSpeed: Int = 0,
    var isLayoutActionable: Boolean = true,
) : ClimateState()
