package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.dataaccess.repository

import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.dataaccess.service.RemoteCommandsApi
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.repository.RemoteCommandsRepo
import com.toyota.oneapp.model.remote.EngineStatusResponse
import com.toyota.oneapp.model.remote.RemoteBaseResponse
import com.toyota.oneapp.model.remote.RemoteRequest
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class RemoteCommandsDefaultRepo
    @Inject
    constructor(
        val service: RemoteCommandsApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : RemoteCommandsRepo(errorParser, ioContext) {
        override suspend fun ng86remoteRequest(
            vin: String,
            bodyRequest: RemoteRequest,
        ): Resource<RemoteBaseResponse?> {
            return makeApiCall {
                service.ng86remoteRequest(
                    vin = vin,
                    body = bodyRequest,
                )
            }
        }

        override suspend fun remoteRequest(
            vin: String,
            bodyRequest: RemoteRequest,
        ): Resource<RemoteBaseResponse?> {
            return makeApiCall {
                service.remoteRequest(
                    vin = vin,
                    body = bodyRequest,
                )
            }
        }

        override suspend fun remoteRequestForPre17(
            brand: String,
            vin: String,
            bodyRequest: RemoteRequest,
        ): Resource<RemoteBaseResponse?> {
            return makeApiCall {
                service.remoteRequestForPre17(
                    brand = brand,
                    vin = vin,
                    body = bodyRequest,
                )
            }
        }

        override suspend fun getNG86RemoteEngineStatus(
            brand: String,
            vin: String,
        ): Resource<EngineStatusResponse?> {
            return makeApiCall {
                service.getNG86RemoteEngineStatus(
                    brand = brand,
                    vin = vin,
                )
            }
        }

        override suspend fun getCY17RemoteEngineStatus(
            brand: String,
            vin: String,
        ): Resource<EngineStatusResponse?> {
            return makeApiCall {
                service.getCY17RemoteEngineStatus(
                    brand = brand,
                    vin = vin,
                )
            }
        }

        override suspend fun getCY17PlusRemoteEngineStatus(
            brand: String,
            vin: String,
        ): Resource<EngineStatusResponse?> {
            return makeApiCall {
                service.getCY17PlusRemoteEngineStatus(
                    brand = brand,
                    vin = vin,
                )
            }
        }
    }
