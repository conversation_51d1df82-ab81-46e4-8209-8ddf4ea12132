package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.helper

import androidx.compose.material.AlertDialog
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.window.DialogProperties
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun AutoFixPopUpDialog(userResponse: (Boolean) -> Unit) {
    val openDialog = remember { mutableStateOf(true) }

    val context = LocalContext.current

    if (openDialog.value) {
        AlertDialog(
            properties = DialogProperties(dismissOnClickOutside = false, dismissOnBackPress = false),
            onDismissRequest = { openDialog.value = false },
            title = {
                Text(
                    text = context.getString(R.string.RemoteCommand_unable_to_engine_start_command),
                    color = Color.White,
                )
            },
            text = {
                Text(
                    text = context.getString(R.string.RemoteCommand_door_not_locked),
                    color = Color.White,
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        openDialog.value = false
                        userResponse(true)
                    },
                ) {
                    Text(text = context.getString(R.string.Common_okay), color = Color.White)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        userResponse(false)
                        openDialog.value = false
                    },
                ) {
                    Text(text = context.getString(R.string.cancel), color = Color.White)
                }
            },
            backgroundColor = AppTheme.colors.tertiary03,
            contentColor = Color.White,
        )
    }
}
