package com.toyota.oneapp.features.dashboard.noncv.presentation

import android.os.Bundle
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import com.google.gson.Gson
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.LoadVehicleImage
import com.toyota.oneapp.features.core.composable.NonConnectedComposableShimmer
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.navigation.OANavController
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.LocalBottomSheetState
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import com.toyota.oneapp.features.dashboard.dashboard.presentation.DashboardViewModel
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardBottomSheetScreen
import com.toyota.oneapp.features.dashboard.noncv.application.GO_TO_PREFERRED_DEALER
import com.toyota.oneapp.features.dashboard.noncv.application.NonCVState
import com.toyota.oneapp.features.dashboard.noncv.domain.model.NonCV
import com.toyota.oneapp.features.dashboard.noncv.domain.model.NonCVType
import com.toyota.oneapp.ui.flutter.DashboardFlutterActivity
import com.toyota.oneapp.ui.flutter.GO_TO_APPOINTMENTS
import com.toyota.oneapp.ui.flutter.GO_TO_MAINTENANCE_SCHEDULE
import com.toyota.oneapp.ui.flutter.GO_TO_SERVICE_HISTORY
import com.toyota.oneapp.util.NavigationUtil
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.CoroutineScope

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun NonCVScreen(
    dashboardViewModel: DashboardViewModel = (LocalContext.current as OADashboardActivity).dashboardViewModel,
    viewModel: NonCVViewModel = hiltViewModel(),
    bottomSheetState: LocalBottomSheetState = LocalBottomSheet.current,
) {
    val coroutineScope = rememberCoroutineScope()
    val vehicleImage = dashboardViewModel.imageData.collectAsState()
    val refreshing by dashboardViewModel.isRefreshing.collectAsState()
    val pullRefreshState =
        rememberPullRefreshState(refreshing, {
            dashboardViewModel.onRefresh()
        })

    val uiState = viewModel.nonCVStateFlow.collectAsState()

    Box(
        Modifier
            .zIndex(-1f)
            .pullRefresh(pullRefreshState)
            .testTagID(
                AccessibilityId.ID_DASHBOARD_REFRESH,
            )
            .clipToBounds(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxHeight()
                    .verticalScroll(rememberScrollState())
                    .background(color = AppTheme.colors.tertiary12),
        ) {
            NonCVWidget(
                vehicleImage,
                coroutineScope,
                dashboardViewModel,
                bottomSheetState,
            )
            NonConnectedComposableShimmer(
                isLoading = (uiState.value == NonCVState.Loading),
                contentAfterLoading = {
                    val nonCVItems = (uiState.value as NonCVState.Success).data
                    nonCVItems.forEach {
                        NonCVItem(nonCVItem = it, viewModel)
                    }
                },
            )
        }
        PullRefreshIndicator(
            refreshing,
            pullRefreshState,
            Modifier.align(Alignment.TopCenter),
        )
    }
}

@Composable
fun NonCVWidget(
    vehicleImage: State<String>,
    coroutineScope: CoroutineScope,
    dashboardViewModel: DashboardViewModel = (LocalContext.current as OADashboardActivity).dashboardViewModel,
    bottomSheet: LocalBottomSheetState,
) {
    val context = LocalContext.current
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        LoadVehicleImage(
            url = vehicleImage.value,
            accessibilityId = AccessibilityId.ID_DASHBOARD_DISPLAY_IMAGE,
            contentDescription = R.string.dashboardVehicleImageDescription,
            modifier =
                Modifier.clickable {
                    dashboardViewModel.updateVehicleInfoAnalytics()
                    coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                        OADashboardBottomSheetScreen(bottomSheetState = it, true)
                    }
                },
        )
        Spacer(modifier = Modifier.height(13.35.dp))
        Button(
            onClick = {
                dashboardViewModel.updateVehicleInfoAnalytics()
                coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
                    OADashboardBottomSheetScreen(bottomSheetState = it, true)
                }
            },
            colors = ButtonDefaults.buttonColors(AppTheme.colors.primaryButton01),
            shape = RoundedCornerShape(100.dp),
            border = BorderStroke(2.dp, AppTheme.colors.tertiary10),
            modifier =
                Modifier
                    .height(34.dp)
                    .width(72.dp)
                    .layoutId(
                        AccessibilityId.ID_VEHICLE_INFO_CTA,
                    ),
        ) {
            Text(
                text = context.getString(R.string.info),
                color = AppTheme.colors.primaryButton02,
            )
        }
        Spacer(modifier = Modifier.height(63.dp))
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun NonCVItem(
    nonCVItem: NonCV,
    viewModel: NonCVViewModel,
) {
    val context = LocalContext.current
    val navHostController = OANavController.current
    Card(
        backgroundColor = AppTheme.colors.tile01,
        elevation = 8.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(bottom = 8.dp, start = 16.0.dp, end = 16.0.dp)
                .testTagID(getAccessibilityId(nonCVItem.nonCVType)),
        onClick = {
            viewModel.updatedAnalyticsLog(nonCVItem.nonCVType)
            val bundle = Bundle()
            when (nonCVItem.onclick) {
                GO_TO_MAINTENANCE_SCHEDULE -> {
                    val data = HashMap<String, String?>()
                    data["odometerValue"] = ToyotaConstants.EMPTY_STRING
                    data["odometerUnit"] = ToyotaConstants.EMPTY_STRING
                    bundle.putSerializable(
                        ToyotaConstants.FLUTTER_DATA,
                        data,
                    )
                    context.startActivity(
                        DashboardFlutterActivity.createIntent(
                            context,
                            bundle,
                            nonCVItem.onclick,
                        ),
                    )
                }

                GO_TO_APPOINTMENTS ->
                    context.startActivity(
                        DashboardFlutterActivity.createIntent(
                            context,
                            bundle,
                            nonCVItem.onclick,
                        ),
                    )

                GO_TO_SERVICE_HISTORY -> {
                    bundle.putSerializable(
                        ToyotaConstants.FLUTTER_DATA,
                        Gson().toJson(nonCVItem.serviceHistoryPayloadData),
                    )
                    context.startActivity(
                        DashboardFlutterActivity.createIntent(
                            context,
                            bundle,
                            nonCVItem.onclick,
                        ),
                    )
                }

                GO_TO_PREFERRED_DEALER -> {
                    NavigationUtil.navigateToDealers(
                        context = context,
                        navController = navHostController,
                        preferredDealer = nonCVItem.preferredDealerPayloadData,
                    )
                }
            }
        },
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .width(60.dp),
            ) {
                Surface(
                    shape = CircleShape,
                    color = AppTheme.colors.button02b,
                    modifier =
                        Modifier
                            .size(48.dp)
                            .align(Alignment.CenterStart),
                ) {
                    Image(
                        painter = painterResource(id = nonCVItem.iconPath),
                        contentDescription = stringResource(id = nonCVItem.title),
                        modifier =
                            Modifier
                                .background(color = AppTheme.colors.secondary02)
                                .padding(14.dp),
                    )
                }
                if (nonCVItem.tickIcon != null) {
                    Surface(
                        shape = CircleShape,
                        color = AppTheme.colors.tile02,
                        modifier =
                            Modifier
                                .size(24.dp)
                                .align(Alignment.BottomEnd),
                    ) {
                        Image(
                            modifier = Modifier.padding(4.dp),
                            painter = painterResource(id = nonCVItem.tickIcon),
                            contentDescription = null,
                        )
                    }
                }
            }
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight(),
            ) {
                val cardSubTitle =
                    when {
                        nonCVItem.nonCVType == NonCVType.MAINTENANCE_SCHEDULE && nonCVItem.subTitle.isEmpty() -> {
                            stringResource(
                                id = R.string.No_Schedules_Found,
                            )
                        }

                        nonCVItem.nonCVType == NonCVType.APPOINTMENTS && nonCVItem.subTitle.isEmpty() -> {
                            stringResource(
                                id = R.string.Upcoming_And_Past_Service,
                            )
                        }

                        nonCVItem.nonCVType == NonCVType.PREFERRED_DEALER && nonCVItem.subTitle.isEmpty() -> {
                            stringResource(
                                id = R.string.NoPreferredDealer,
                            )
                        }

                        nonCVItem.nonCVType == NonCVType.SERVICE_HISTORY -> {
                            if (nonCVItem.subTitle.isEmpty()) {
                                stringResource(
                                    id = R.string.NoServiceHistory,
                                )
                            } else {
                                stringResource(
                                    id = R.string.LastServiceDate,
                                    nonCVItem.subTitle,
                                )
                            }
                        }

                        else -> {
                            nonCVItem.subTitle
                        }
                    }

                OABody4TextView(
                    text = stringResource(id = nonCVItem.title),
                    color = AppTheme.colors.tertiary03,
                )

                Spacer(modifier = Modifier.height(2.dp))

                OACallOut1TextView(
                    text = cardSubTitle,
                    color = AppTheme.colors.tertiary05,
                )
            }
        }
    }
}

fun getAccessibilityId(nonCVType: NonCVType?): String {
    return when (nonCVType) {
        NonCVType.PREFERRED_DEALER -> AccessibilityId.ID_NC_PREFERRED_DEALER
        NonCVType.MAINTENANCE_SCHEDULE -> AccessibilityId.ID_NC_MAINTENANCE_SCHEDULE
        NonCVType.SERVICE_HISTORY -> AccessibilityId.ID_NC_SERVICE_HISTORY
        NonCVType.APPOINTMENTS -> AccessibilityId.ID_NC_APPOINTMENTS
        else -> ""
    }
}
