@file:OptIn(ExperimentalMaterialApi::class)

package com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.presentation

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.TabRow
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.features.core.composable.OATabButton
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.ConnectedVehicleTabs
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.DashboardTab
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.presentation.RemoteTabScreen
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.fuelwidget.presentation.FuelWidgetScreen
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.presentation.VehicleHealthScreen
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.presentation.VehicleHealthViewModel
import com.toyota.oneapp.features.vehiclestatus.presentation.VehicleStatusScreen
import com.toyota.oneapp.features.vehiclestatus.presentation.VehicleStatusViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

@SuppressLint("StateFlowValueCalledInComposition")
@Composable
fun ConnectedVehicleScreen(navHostController: NavHostController) {
    val viewModel =
        hiltViewModel<ConnectedVehicleViewModel>(
            LocalContext.current as OADashboardActivity,
        )

    val tabWidgetsState by viewModel.tabWidgetsState.collectAsState()
    val pageCount = tabWidgetsState?.size ?: 0
    val pagerState = rememberPagerState(initialPage = 0, pageCount = { pageCount })
    val coroutineScope = rememberCoroutineScope()

    val dashboardViewModel = (LocalContext.current as OADashboardActivity).dashboardViewModel
    val isSecondaryVehicle by viewModel.isSecondaryVehicle.collectAsState()

    val showProgress by dashboardViewModel.showDashboardProgress.collectAsState()

    val updateRemoteTab by viewModel.updateRemoteTab.collectAsState()

    val bottomSheet = LocalBottomSheet.current

    val navigateStatusTab by viewModel.navigateStatusTab.collectAsState()

    val pullRefreshDk = dashboardViewModel.isPullDkRefresh.collectAsState()

    val context = LocalContext.current

    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }.distinctUntilChanged().collect { currentPage ->
            viewModel.updateTab(currentPage)
        }
    }

    LaunchedEffect(key1 = Unit) {
        coroutineScope.launch {
            viewModel.applicationData.getSelectedVehicleState().collect {
                it?.let {
                    val currentTab = pagerState.currentPage
                    pagerState.animateScrollToPage(currentTab)
                }
            }
        }
    }

    LaunchedEffect(updateRemoteTab) {
        pagerState.animateScrollToPage(0)
        viewModel.updateRemoteTabFlow(false)
    }

    LaunchedEffect(pullRefreshDk.value) {
        if (pullRefreshDk.value) {
            dashboardViewModel.syncDigitalKeyStatus()
            dashboardViewModel.isPullDkRefreshFlow.value = false
        }
    }

    fun hideSheetIfVisible(sheetState: MutableState<ModalBottomSheetState>) {
        coroutineScope.launch {
            if (sheetState.value.isVisible) {
                sheetState.value.hide()
            }
        }
    }

    fun hidePrimarySheet() {
        hideSheetIfVisible(bottomSheet.primarySheetState)
    }

    fun hideSecondarySheet() {
        hideSheetIfVisible(bottomSheet.secondarySheetState)
    }

    LaunchedEffect(Unit) {
        viewModel.updatedCASFeedback.collectLatest { updated ->
            if (updated) {
                hidePrimarySheet()
                hideSecondarySheet()
                viewModel.setUpdatedCASFeedback(false) // reset the flag
                viewModel.getCAS(isFromFeedback = true) // get CAS and show the updated data in dashboard
            }
        }
    }

    LaunchedEffect(Unit) {
        viewModel.showCASProgress.collectLatest { showCasProgress ->
            dashboardViewModel.updateProgressBar(showCasProgress)
        }
    }

    if (navigateStatusTab) {
        LaunchedEffect(Unit) {
            coroutineScope.launch {
                viewModel.updateTab(1)
                pagerState.animateScrollToPage(1)
            }
        }
    }

    BackHandler(enabled = pagerState.currentPage != 0, onBack = {
        coroutineScope.launch {
            if (pagerState.currentPage != 0) {
                pagerState.animateScrollToPage(0)
            }
        }
    })

    if (showProgress) {
        ShowProgressIndicator(true)
    }

    // OAD01-13609 Do not show tabs and odometer if the vehicle is a shared key vehicle
    if (isSecondaryVehicle) {
        SecondaryVehicle()
    } else {
        PrimaryVehicle(pagerState, navHostController)
    }
}

@Composable
@OptIn(ExperimentalMaterialApi::class)
private fun PrimaryVehicle(
    pagerState: PagerState,
    navHostController: NavHostController,
) {
    val vehicleStatusViewModel =
        hiltViewModel<VehicleStatusViewModel>(
            LocalContext.current as OADashboardActivity,
        )
    val vehicleHealthViewModel = hiltViewModel<VehicleHealthViewModel>()
    val notifyHealthTab = vehicleHealthViewModel.notifyHealthTab.collectAsState()
    val notifyStatusTab = vehicleStatusViewModel.notifyStatusTab.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    val viewModel =
        hiltViewModel<ConnectedVehicleViewModel>(
            LocalContext.current as OADashboardActivity,
        )
    val tabWidgetsState by viewModel.tabWidgetsState.collectAsState()
    val dashboardViewModel = (LocalContext.current as OADashboardActivity).dashboardViewModel
    val refreshing = dashboardViewModel.isRefreshing.collectAsState()

    val pullRefreshState =
        rememberPullRefreshState(refreshing.value, {
            dashboardViewModel.onRefresh()
            vehicleStatusViewModel.onRefresh()
        })

    Column(Modifier.background(color = AppTheme.colors.tertiary12)) {
        FuelWidgetScreen(navHostController = navHostController)
        tabWidgetsState?.let { tabList ->
            TabRow(
                selectedTabIndex = pagerState.currentPage,
                backgroundColor = AppTheme.colors.tertiary12,
                indicator = {},
                divider = {},
                modifier = Modifier.padding(all = 8.dp),
            ) {
                tabList.forEachIndexed { index, tab ->
                    OATabButton(
                        accessibilityID = tab.accessibilityID,
                        text = stringResource(tab.text),
                        isSelected = pagerState.currentPage == index,
                        hasUpdates =
                            when (tab.type) {
                                ConnectedVehicleTabs.HEALTH.name -> notifyHealthTab.value
                                ConnectedVehicleTabs.STATUS.name -> notifyStatusTab.value
                                else -> false
                            },
                    ) {
                        coroutineScope.launch {
                            viewModel.updateTab(index)
                            pagerState.animateScrollToPage(index)
                        }
                    }
                }
            }
            Box(
                Modifier
                    .weight(1f)
                    .zIndex(-1f)
                    .pullRefresh(pullRefreshState)
                    .testTagID(
                        AccessibilityId.ID_DASHBOARD_REFRESH,
                    )
                    .clipToBounds(),
            ) {
                TabScreen(
                    tabList,
                    pagerState,
                    vehicleStatusViewModel,
                    vehicleHealthViewModel,
                )
                PullRefreshIndicator(
                    refreshing.value,
                    pullRefreshState,
                    Modifier.align(Alignment.TopCenter),
                )
            }
        }
    }
}

@Composable
@OptIn(ExperimentalMaterialApi::class)
private fun TabScreen(
    tabList: MutableList<DashboardTab>?,
    pagerState: PagerState,
    vehicleStatusViewModel: VehicleStatusViewModel,
    vehicleHealthViewModel: VehicleHealthViewModel,
) {
    if (tabList?.isNotEmpty() == true) {
        HorizontalPager(
//            count = tabList.size,
            state = pagerState,
            modifier = Modifier.fillMaxWidth(),
        ) { page ->
            val dashboardItem = tabList[page]
            when (dashboardItem.type) {
                ConnectedVehicleTabs.REMOTE.name -> {
                    RemoteTabScreen()
                }

                ConnectedVehicleTabs.STATUS.name -> {
                    VehicleStatusScreen(
                        vehicleStatusViewModel,
                    )
                }

                ConnectedVehicleTabs.HEALTH.name -> {
                    VehicleHealthScreen(
                        vehicleHealthViewModel,
                        LocalBottomSheet.current,
                    )
                }
            }
        }
    }
}

@Composable
private fun SecondaryVehicle() {
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(AppTheme.colors.tertiary12)
                .shadow(
                    elevation = 15.dp,
                    spotColor = Color(0x12000000),
                    ambientColor = Color(0x12000000),
                ),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        RemoteTabScreen()
    }
}
