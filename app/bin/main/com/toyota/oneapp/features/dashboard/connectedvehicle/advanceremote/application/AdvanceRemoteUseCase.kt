package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.application

import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteItem
import com.toyota.oneapp.model.remote.EngineStatusResponse
import com.toyota.oneapp.model.remote.RemoteBaseResponse
import com.toyota.oneapp.model.remote.RemoteRequest
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow

abstract class AdvanceRemoteUseCase {
    // Check All feature flags in the vehicleInfo and wrap into single list to render in the UI
    abstract suspend fun getRemoteCommands(vehicleInfo: VehicleInfo): Flow<ArrayList<RemoteItem>>

    // postCommandRequest() is for initiate the remote command post method
    abstract fun postCommandRequest(
        autoFixEnabled: Boolean,
        commandType: RemoteCommandType,
        vehicleInfo: VehicleInfo,
    ): Flow<Resource<RemoteBaseResponse?>>

    // sendRemoteCommandRequest() is inside postCommandRequest()
    // It checks generation and build request body to initiate API Call
    abstract suspend fun sendRemoteCommandRequest(
        vehicleInfo: VehicleInfo,
        remoteRequest: RemoteRequest,
    ): Flow<Resource<RemoteBaseResponse?>>

    // After getting success response from postCommandRequest() then trigger the GET engine status API to get updated value
    abstract fun getEngineStatusRequest(vehicleInfo: VehicleInfo): Flow<Resource<EngineStatusResponse?>>

    abstract fun launchRemotePark()
}
