package com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.application.util

import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.CabinAwareness
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.model.getEvents
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.model.CabinAlert
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.model.CasEventDetails
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.model.ReportFlowEvents

fun CabinAwareness.toUiModel(): CabinAlert? {
    return this.payload?.let { cas ->
        if (cas.event?.active == false) {
            null
        } else {
            val reportFlowEvents =
                ReportFlowEvents(
                    eventId = cas.event?.id,
                    title = cas.event?.title,
                    disclaimer = cas.feedbackTile?.discliamer,
                    reasons = cas.feedbackTile?.reasons,
                    positiveCTA = cas.feedbackTile?.positiveCTA,
                    negativeCTA = cas.feedbackTile?.negativeCTA,
                    selectionInLineError = cas.feedbackTile?.selectionInLineError,
                    provideDetailsTitle = cas.feedbackTile?.provideDetailsTitle,
                    provideDetailsInLineError = cas.feedbackTile?.provideDetailsInLineError,
                )

            val eventDetails =
                CasEventDetails(
                    appBarTitle = cas.event?.eventDetails?.header,
                    title = cas.event?.eventDetails?.title,
                    description = cas.event?.eventDetails?.description,
                    events = getEvents(cas.event?.eventDetails?.sections),
                    reportFlowEvents = reportFlowEvents,
                )

            CabinAlert(
                eventId = cas.event?.id,
                title = cas.event?.title,
                description = cas.event?.subTitle,
                eventDetails = eventDetails,
            )
        }
    }
}
