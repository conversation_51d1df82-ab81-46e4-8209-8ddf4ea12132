package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.presentation.utils

enum class RemoteFCMStatus(val status: String) {
    REMOTE_STATUS_PROGRESS("in_progress"),
    REMOTE_STATUS_COMPLETED("completed"),
    REMOTE_STATUS_TERMINATED("terminated"),
    REMOTE_STATUS_TIMEOUT("timeout"),
    REMOTE_STATUS_INTERRUPTED("interrupted"),
    REMOTE_STATUS_ERRORS("error"),
    REMOTE_STATUS_POPUP_REQUIRED("popup_required"),
}

enum class RemoteCommandStatus(val command: String) {
    ENGINE_START_1("EStart"),
    ENGINE_START_2("engine-start"),
    ENGINE_STOP_1("EStop"),
    ENGINE_STOP_2("engine-stop"),
    DOOR_LOCK_1("door-lock"),
    DOOR_LOCK_2("DLock"),
    DOOR_UNLOCK_1("door-unlock"),
    DOOR_UNLOCK_2("DUnlock"),
    HAZARD_ON_1("hazard-on"),
    HAZARD_ON_2("HZOn"),
    HAZARD_OFF_1("hazard-off"),
    HAZARD_OFF_2("HZOff"),
    BUZZER_ON("buzzer-on"),
    BUZZER_ON_2("buzzer-warning"),
    TRUNK_LOCK("trunk-lock"),
    TRUNK_UNLOCK("trunk-unlock"),
    HORN_ON("sound-horn"),
    LIGHTS_ON("headlight-on"),
}

enum class RemoteCommandAutoFix(val id: Int) {
    SHOW_POPUP(3),
    SHOW_SPINNER_AND_DISABLE_ALL_COMMANDS(4),
}
