package com.toyota.oneapp.features.dashboard.connectedvehicle.climate.settings.domain.model

import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.constants.AirCirculationFlow
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateState
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.modelcomponent.ClimateType

data class AirCirculationState(
    val type: ClimateType,
    var airCirculation: AirCirculationFlow = AirCirculationFlow.INSIDE,
    var isLayoutActionable: Boolean = true,
) : ClimateState()
