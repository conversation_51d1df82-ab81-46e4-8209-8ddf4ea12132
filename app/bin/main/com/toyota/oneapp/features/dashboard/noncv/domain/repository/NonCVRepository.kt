package com.toyota.oneapp.features.dashboard.noncv.domain.repository

import com.toyota.oneapp.features.dashboard.noncv.dataaccess.model.ServiceHistoryResponse
import com.toyota.oneapp.features.find.domain.model.PreferredDealer
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import kotlin.coroutines.CoroutineContext

abstract class NonCVRepository(
    errorParser: ErrorMessageParser,
    ioContext: CoroutineContext,
) : BaseRepository(errorParser, ioContext) {
    abstract suspend fun fetchPreferredDealer(
        brand: String,
        vin: String,
        region: String,
    ): Resource<PreferredDealer?>

    abstract suspend fun fetchServiceHistory(
        brand: String,
        vin: String,
    ): Resource<ServiceHistoryResponse?>
}
