package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.LocalBottomSheetState
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.model.PopupStates
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.extension.updateRemotePopupAnalyticsLogger
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.presentation.helper.getRemoteStatesPopupAccessibilityId
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun RemoteStatesBottomSheet(
    bottomSheetState: ModalBottomSheetState,
    bottomSheet: LocalBottomSheetState,
) {
    val context = LocalContext.current

    val viewModel =
        hiltViewModel<RemoteStatesViewModel>(
            context as OADashboardActivity,
        )

    val coroutineScope = rememberCoroutineScope()

    val popupState = viewModel.popupStates.collectAsState()

    Column(
        modifier =
            Modifier
                .height(337.dp)
                .background(color = AppTheme.colors.tile03),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(24.dp))
        Image(
            painter = painterResource(id = popupState.value.popupIcon),
            contentDescription = popupState.value.title?.let { stringResource(id = it) },
            modifier = Modifier.size(46.dp),
        )
        popupState.value.title?.let { stringResource(id = it) }?.let {
            OASubHeadLine1TextView(
                text = it,
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.padding(bottom = 8.dp, top = 16.dp),
            )
        }
        popupState.value.subHeading?.let { stringResource(id = it) }?.let {
            OACallOut1TextView(
                text = it,
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Center,
                modifier =
                    Modifier
                        .padding(bottom = 40.dp, start = 24.dp, end = 24.dp)
                        .fillMaxWidth(),
            )
        }
        if (popupState.value.ctaSecondaryText != 0) {
            OACallOut1TextView(
                text = stringResource(R.string.cancel),
                color = AppTheme.colors.tertiary05,
                modifier =
                    Modifier
                        .layoutId(getRemoteStatesPopupAccessibilityId(popupState.value, context))
                        .clickable(onClick = {
                            coroutineScope.launch {
                                bottomSheetState.hide()
                            }
                            if (popupState.value.popupStatus == PopupStates.VEHICLE_STOLEN) {
                                viewModel.updateRemotePopupAnalyticsLogger(
                                    popupState.value.popupStatus,
                                    isCancelCTA = true,
                                )
                            }
                        }),
            )
        }
        popupState.value.ctaText?.let { context.getString(it) }?.let {
            PrimaryButton02(
                text = it,
                modifier =
                    Modifier
                        .wrapContentWidth()
                        .wrapContentHeight()
                        .padding(top = 20.dp)
                        .layoutId(getRemoteStatesPopupAccessibilityId(popupState.value, context)),
                click = {
                    coroutineScope.launch {
                        bottomSheetState.hide()
                    }
                    when (popupState.value.popupStatus) {
                        PopupStates.VEHICLE_STOLEN -> {
                            viewModel.checkConditionToShowSOS(context, coroutineScope, bottomSheet)
                        }
                        else -> {
                            coroutineScope.launch {
                                viewModel.popUpAction(popupState.value)
                            }
                        }
                    }
                },
            )
        }
    }
}
