package com.toyota.oneapp.features.dashboard.connectedvehicle.cas

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun CasSwipeDown() {
    Column {
        Spacer(modifier = Modifier.height(8.dp))
        Image(
            painterResource(R.drawable.ic_drag),
            contentDescription = stringResource(R.string.swipeDownIconDescription),
            contentScale = ContentScale.FillWidth,
            modifier =
                Modifier
                    .width(30.dp)
                    .height(10.dp),
            colorFilter = ColorFilter.tint(AppTheme.colors.outline01),
        )
        Spacer(modifier = Modifier.height(20.dp))
    }
}
