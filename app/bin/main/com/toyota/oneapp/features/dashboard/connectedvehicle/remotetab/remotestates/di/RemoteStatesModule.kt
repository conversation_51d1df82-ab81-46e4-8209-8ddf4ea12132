package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.di

import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.application.RemoteStatesLogic
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.application.RemoteStatesUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.dataaccess.repository.RemoteStatesDefaultRepository
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.repository.RemoteStatesRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class RemoteStatesModule {
    @Binds
    abstract fun bindRemoteStatesRepository(remoteStatesDefaultRepository: RemoteStatesDefaultRepository): RemoteStatesRepository

    @Binds
    abstract fun provideAdvanceStatesLogic(remoteStatesLogic: RemoteStatesLogic): RemoteStatesUseCase
}
