package com.toyota.oneapp.features.dashboard.dashboard.application

import android.text.format.DateUtils
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.core.commonapicalls.application.ElectricStatusState
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.repository.CommonApiDefaultRepository
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoResponse
import com.toyota.oneapp.features.core.util.DateTimeUtil
import com.toyota.oneapp.features.core.util.DateTimeUtil.TODAY_AT
import com.toyota.oneapp.features.core.util.DateTimeUtil.YESTERDAY_AT
import com.toyota.oneapp.features.dashboard.dashboard.domain.model.Telemetry
import com.toyota.oneapp.features.dashboard.dashboard.domain.repository.DashboardRepository
import com.toyota.oneapp.features.dashboard.util.DashboardConstants
import com.toyota.oneapp.features.fuelwidget.application.UnitValue
import com.toyota.oneapp.features.fuelwidget.presentation.utils.FuelWidgetConstants
import com.toyota.oneapp.features.vehicleinfo.dataaccess.servermodel.toUIModel
import com.toyota.oneapp.features.vehicleswitcher.presentation.utils.PreferredVehicleStatus
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.io.IOException
import javax.inject.Inject

class DashboardLogic
    @Inject
    constructor(
        private val repository: DashboardRepository,
        private val commonApiRepository: CommonApiDefaultRepository,
        private val dateUtil: DateUtil,
        private val applicationData: ApplicationData,
        private val preferenceModel: OneAppPreferenceModel,
    ) : DashboardUseCase {
        override suspend fun getVehicleList(selectedVehicle: VehicleInfo?): Flow<MutableList<VehicleInfo>?> {
            return flow {
                val response = repository.vehiclesList()
                response.let vehicleResponse@{ vehicleResponse ->
                    when (vehicleResponse) {
                        is Resource.Failure -> {
                            throw IOException()
                        }

                        is Resource.Success -> {
                            vehicleResponse.data?.payload?.let { it ->
                                if (it.isNotEmpty()) {
                                    // update vin list
                                    applicationData.setVehicleList(it)
                                    var currentVehicleUpdated = false

                                    // set selected vehicle if exist in vin list
                                    if (selectedVehicle != null) {
                                        val listData =
                                            applicationData.getVehicleList()?.filter {
                                                    vehicleInfo ->
                                                vehicleInfo.vin == selectedVehicle.vin
                                            }
                                        listData?.firstOrNull()?.let {
                                            applicationData.setSelectedVehicle(it)
                                            currentVehicleUpdated = true
                                        }
                                    }

                                    // set preferred vehicle
                                    if (!currentVehicleUpdated) {
                                        applicationData.getVehicleList()?.forEach { vehicleInfo ->
                                            if (vehicleInfo.preferred == PreferredVehicleStatus.PREFERRED_VEHICLE.preferredVehicleStatus) {
                                                applicationData.setSelectedVehicle(vehicleInfo)
                                                currentVehicleUpdated = true
                                            }
                                        }
                                    }

                                    // set first vehicle as current vehicle
                                    if (!currentVehicleUpdated) {
                                        applicationData.setSelectedVehicle(it[0])
                                    }
                                    emit(it)
                                } else {
                                    applicationData.setVehicleList(it)
                                    applicationData.setSelectedVehicle(null)
                                    emit(null)
                                }
                            }
                        }

                        else -> {}
                    }
                }
            }
        }

        override suspend fun fetchTelemetry(vehicleInfo: VehicleInfo): Flow<TelemetryState> {
            return flow {
                if (vehicleInfo.isFeatureEnabled(Feature.TELEMETRY) && vehicleInfo.isConnectedVehicle) {
                    val response =
                        repository.fetchTelemetry(
                            vin = vehicleInfo.vin,
                            generation = vehicleInfo.generation,
                            brand = vehicleInfo.brand,
                            region = vehicleInfo.region,
                        )
                    if (response is Resource.Success) {
                        response.data?.payload?.let { it ->
                            val distanceToEmptyisNull = it.distanceToEmpty?.value == null
                            val emptyValue = FuelWidgetConstants.EMPTY_VALUE
                            val defaultUnit = FuelWidgetConstants.DEFAULT_UNIT
                            val telemetryPayload =
                                Telemetry(
                                    odometer =
                                        it.odometer?.let {
                                            "${it.convertThousandsWithComma()} ${it.unit}"
                                        } ?: run { DashboardConstants.NA },
                                    lastUpdatedTime = parseDate(it.lastTimestamp),
                                    distanceToEmpty =
                                        UnitValue(
                                            it.distanceToEmpty?.value?.toInt() ?: FuelWidgetConstants.STRING_ZERO,
                                            unit = if (distanceToEmptyisNull) emptyValue else it.distanceToEmpty?.unit ?: defaultUnit,
                                        ),
                                    fuelLevel = it.fuelLevel ?: 0,
                                    displayDistanceToEmpty =
                                        it.displayDistanceToEmpty == true ||
                                            vehicleInfo.isFeatureEnabled(
                                                Feature.EV_BATTERY,
                                            ),
                                    vehicleLocation = it.vehicleLocation,
                                )
                            emit(TelemetryState.Success(telemetryPayload))
                        } ?: emit(TelemetryState.NotAvailable)
                    } else {
                        emit(TelemetryState.Error())
                    }
                } else {
                    emit(TelemetryState.NotAvailable)
                }
            }
        }

        override fun fetchElectricStatus(vehicleInfo: VehicleInfo): Flow<ElectricStatusState> {
            return flow {
                val response =
                    commonApiRepository.fetchChargeManagementDetail(
                        vin = vehicleInfo.vin,
                        generation = vehicleInfo.generation,
                        brand = vehicleInfo.brand,
                    )
                if (response is Resource.Success) {
                    emit(ElectricStatusState.Success(response.data))
                } else {
                    emit(ElectricStatusState.Error())
                }
            }
        }

        override suspend fun fetchSoftwareUpdates(vehicleInfo: VehicleInfo): Flow<SoftwareUpdateDeepLinkState> {
            return flow {
                if (vehicleInfo.isFeatureEnabled(Feature.AUTO_DRIVE)) {
                    val response = repository.fetchSoftwareUpdates(vehicleInfo.vin)
                    if (response is Resource.Success) {
                        response.data?.toUIModel()?.let {
                            emit(SoftwareUpdateDeepLinkState.Success(it))
                        } ?: emit(SoftwareUpdateDeepLinkState.Error)
                    } else {
                        emit(SoftwareUpdateDeepLinkState.Error)
                    }
                } else {
                    emit(SoftwareUpdateDeepLinkState.Error)
                }
            }
        }

        private fun parseDate(date: String?): String {
            var updatedDate = ToyotaConstants.EMPTY_STRING
            dateUtil.getDateFromString(date)?.let {
                updatedDate =
                    when {
                        DateUtils.isToday(it.time) ->
                            "$TODAY_AT ${DateTimeUtil.todayTimeFormat.format(it)}"

                        DateUtils.isToday(it.time + DateUtils.DAY_IN_MILLIS) ->
                            "$YESTERDAY_AT ${DateTimeUtil.todayTimeFormat.format(it)}"

                        else -> {
                            DateTimeUtil.timeFormat_DateMonthYearWithTime.format(it)
                        }
                    }
            }
            return updatedDate
        }

        override fun fetchProfileDetails(): Flow<ProfileInfoResponse?> {
            return flow {
                applicationData.getSelectedVehicle()?.let { vehicleInfo ->
                    val response =
                        commonApiRepository.fetchProfileDetails(
                            brand = vehicleInfo.brand,
                            guid = preferenceModel.getGuid(),
                        )
                    // Add the error states
                    when (response) {
                        is Resource.Success -> {
                            response.data?.let {
                                emit(it)
                            }
                        }
                        else -> { /*left intentionally blank*/ }
                    }
                }
            }
        }
    }

fun String.ellipsize(): String {
    return if (this.length >= 16) "${this.substring(0, 16)}..." else this
}
