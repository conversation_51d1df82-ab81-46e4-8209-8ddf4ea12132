package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.dataaccess.repository

import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.dataaccess.service.RemoteStatesApi
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.domain.repository.RemoteStatesRepository
import com.toyota.oneapp.model.subscription.AssignRemoteUserRequest
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class RemoteStatesDefaultRepository
    @Inject
    constructor(
        val service: RemoteStatesApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : RemoteStatesRepository(errorParser, ioContext) {
        override suspend fun assignRemoteUser(
            vin: String,
            generation: String,
            brand: String,
            dateTime: String,
            assignRemoteUserRequest: AssignRemoteUserRequest,
        ): Resource<BaseResponse?> {
            return makeApiCall {
                service.assignRemoteUser(
                    vin = vin,
                    generation = generation,
                    brand = brand,
                    dateTime = dateTime,
                    assignRemoteUserRequest = assignRemoteUserRequest,
                )
            }
        }
    }
