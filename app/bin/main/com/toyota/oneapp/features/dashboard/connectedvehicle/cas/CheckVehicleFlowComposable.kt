package com.toyota.oneapp.features.dashboard.connectedvehicle.cas

import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.composable.ExpandableCard
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.OATextTitle2TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.LocalBottomSheetState
import com.toyota.oneapp.features.core.util.launchSecondaryBottomSheetAction
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.model.CasEventDetails
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.domain.model.CasPageDestination
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.glovebox.manualandwarranties.presentation.ManualsAndWarrantiesScreen
import com.toyota.oneapp.ui.accountsettings.NotificationSettingsActivity
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@Composable
fun CheckVehicleFlow(
    sheetState: ModalBottomSheetState,
    casEvent: CasEventDetails,
    modifier: Modifier = Modifier,
) {
    val coroutineScope = rememberCoroutineScope()

    val ownersManualSheet =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            skipHalfExpanded = true,
        )

    BackHandler {
        coroutineScope.launch {
            if (ownersManualSheet.isVisible) ownersManualSheet.hide()
            if (sheetState.isVisible) sheetState.hide()
        }
    }

    fun hideOwnersManual() {
        coroutineScope.launch {
            if (ownersManualSheet.isVisible) ownersManualSheet.hide()
        }
    }

    ModalBottomSheetLayout(
        modifier = modifier,
        sheetState = ownersManualSheet,
        sheetElevation = 8.dp,
        sheetShape = RoundedCornerShape(topEnd = 30.dp, topStart = 30.dp),
        sheetContent = {
            ManualsAndWarrantiesScreen(onBackClick = { hideOwnersManual() })
        },
    ) {
        Box(
            modifier =
                modifier
                    .fillMaxSize()
                    .background(AppTheme.colors.tertiary15),
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                CasSwipeDown()
                CASAppBar(title = casEvent.appBarTitle ?: ToyotaConstants.EMPTY_STRING, onBackPressed = {
                    coroutineScope.launch {
                        if (sheetState.isVisible) sheetState.hide()
                    }
                })
                Spacer(modifier = Modifier.height(20.dp))
                CabinAlertDetails(casEvent, coroutineScope, ownersManualSheet)
            }
        }
    }
}

@Composable
private fun CabinAlertDetails(
    casEvent: CasEventDetails,
    coroutineScope: CoroutineScope,
    ownersManualSheet: ModalBottomSheetState,
) {
    Column(
        Modifier.verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.Start,
    ) {
        OATextTitle2TextView(
            text = casEvent.title ?: ToyotaConstants.EMPTY_STRING,
            color = AppTheme.colors.tertiary03,
            textAlign = TextAlign.Start,
            modifier = Modifier.testTagID(AccessibilityId.ID_CAS_DETAIL_TITLE_TEXT),
        )
        Spacer(modifier = Modifier.height(20.dp))
        OACallOut1TextView(
            text = casEvent.description ?: ToyotaConstants.EMPTY_STRING,
            color = AppTheme.colors.tertiary05,
            modifier = Modifier.testTagID(AccessibilityId.ID_CAS_DETAIL_DESCRIPTION_TEXT),
        )
        Spacer(modifier = Modifier.height(20.dp))

        CasSection(
            casEvent = casEvent,
            coroutineScope = coroutineScope,
            ownersManualSheet = ownersManualSheet,
        )
        Spacer(modifier = Modifier.height(30.dp))
    }
}

@Composable
private fun CasSection(
    casEvent: CasEventDetails,
    coroutineScope: CoroutineScope,
    modifier: Modifier = Modifier,
    ownersManualSheet: ModalBottomSheetState,
) {
    val bottomSheet: LocalBottomSheetState = LocalBottomSheet.current
    val context = LocalContext.current
    val viewModel = (LocalContext.current as OADashboardActivity).connectedVehicleViewModel

    fun navigateScreen(pageDestination: CasPageDestination) {
        when (pageDestination) {
            CasPageDestination.REPORT_FLOW -> {
                viewModel.updateCasAnalytics(AnalyticsEventParam.CAS_FALSE_DETECTION_HYPERLINK)
                casEvent.reportFlowEvents?.let {
                    coroutineScope.launchSecondaryBottomSheetAction(
                        bottomSheet,
                    ) { sheetState ->
                        CasReportScreen(
                            sheetState = sheetState,
                            reportEvents = it,
                        )
                    }
                }
            }

            CasPageDestination.OWNERS_MANUAL -> {
                viewModel.updateCasAnalytics(AnalyticsEventParam.CAS_OWNERS_MANUAL_HYPERLINK)
                coroutineScope.launch {
                    if (!ownersManualSheet.isVisible) {
                        ownersManualSheet.show()
                    }
                }
            }

            CasPageDestination.NOTIFICATION_SETTINGS -> {
                viewModel.updateCasAnalytics(
                    AnalyticsEventParam.CAS_NOTIFICATION_SETTINGS_HYPERLINK,
                )
                context.startActivity(
                    Intent(
                        context,
                        NotificationSettingsActivity::class.java,
                    ),
                )
            }
        }
    }

    casEvent.events?.forEachIndexed { index, casData ->

        if (casData.title.isNotNullOrEmpty()) {
            ExpandableCard(
                title = casData.title,
                isDefaultExpanded = false,
                modifier = modifier.padding(vertical = 5.dp),
                changeCardBgColor = false,
                expandedLayout = {
                    if (casData.isHyperLinkExists == true &&
                        casData.description.isNotNullOrEmpty() &&
                        casData.hyperLinkText.isNotNullOrEmpty()
                    ) {
                        HyperlinkText(
                            description = casData.description,
                            hyperlinkText = casData.hyperLinkText,
                            onClick = {
                                casData.pageDestination?.let {
                                    navigateScreen(it)
                                }
                            },
                        )
                    } else {
                        OACallOut1TextView(
                            text = casData.description ?: ToyotaConstants.EMPTY_STRING,
                            color = AppTheme.colors.tertiary05,
                            modifier = Modifier.padding(horizontal = 14.dp, vertical = 4.dp),
                        )
                    }
                },
                onTileClicked = { isExpanded ->
                    isExpanded.takeIf { it }?.let {
                        viewModel.updateCasAnalytics(getEventParam(index))
                    }
                },
            )
        }
    }
}

fun getEventParam(index: Int): String {
    /**
     * Currently, the app assigns analytics based on a specific order.
     * In the future, when the API provides enum values, we can use those instead of relying on the order.
     * */
    return when (index) {
        0 -> AnalyticsEventParam.CAS_STOP_ALERT_DROP_DOWN
        1 -> AnalyticsEventParam.CAS_MUTE_ALERT_DROP_DOWN
        2 -> AnalyticsEventParam.CAS_FALSE_DETECTION_DROP_DOWN
        3 -> AnalyticsEventParam.CAS_OWNERS_MANUAL_DROP_DOWN
        else -> AnalyticsEventParam.CAS_NOTIFICATION_SETTINGS_DROP_DOWN
    }
}

@Composable
fun CASAppBar(
    title: String,
    onBackPressed: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Box(
            modifier =
                Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(AppTheme.colors.button02d)
                    .clickable {
                        onBackPressed()
                    },
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_back_arrow),
                contentDescription = stringResource(id = R.string.Common_back),
                modifier =
                    Modifier
                        .padding(
                            start = 19.dp,
                            end = 22.dp,
                            top = 17.dp,
                            bottom = 17.dp,
                        ),
                tint = AppTheme.colors.button02a,
            )
        }

        OASubHeadLine3TextView(
            text = title,
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier
                    .padding(top = 6.dp)
                    .align(Alignment.TopCenter)
                    .testTagID(AccessibilityId.ID_CAS_DETAIL_APP_BAR_TEXT),
        )
    }
}

@Composable
fun HyperlinkText(
    description: String,
    hyperlinkText: String,
    onClick: () -> Unit,
) {
    val annotatedString =
        buildAnnotatedString {
            append(description.substringBefore(hyperlinkText))
            pushStringAnnotation(
                tag = "URL",
                annotation = hyperlinkText,
            )
            withStyle(
                style =
                    SpanStyle(
                        fontWeight = FontWeight.Bold,
                        textDecoration = TextDecoration.Underline,
                    ),
            ) {
                append(hyperlinkText)
            }
            pop()
            append(description.substringAfter(hyperlinkText))
        }

    ClickableText(
        modifier = Modifier.padding(horizontal = 14.dp, vertical = 4.dp),
        text = annotatedString,
        style =
            AppTheme.fontStyles.callout1.copy(
                color = AppTheme.colors.tertiary05,
            ),
        onClick = { offset ->
            annotatedString
                .getStringAnnotations("URL", offset, offset)
                .firstOrNull()
                ?.let { annotation ->
                    if (annotation.item == hyperlinkText) {
                        onClick()
                    }
                }
        },
    )
}
