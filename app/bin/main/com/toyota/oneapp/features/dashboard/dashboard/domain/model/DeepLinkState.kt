package com.toyota.oneapp.features.dashboard.dashboard.domain.model

import android.os.Bundle

sealed class DeepLinkState {
    object IDLE : DeepLinkState()

    object NavigateToManageSubscription : DeepLinkState()

    object NavigateToAccountSettings : DeepLinkState()

    object NavigateToVehicleInfo : DeepLinkState()

    object NavigateToGuestDriverSettings : DeepLinkState()

    object NavigateToScheduleService : DeepLinkState()

    object NavigateToLinkedAccounts : DeepLinkState()

    object NavigateToPinReset : DeepLinkState()

    object NavigateToPrivacyPortal : DeepLinkState()

    object NavigateToManageDigitalKey : DeepLinkState()

    class NavigateToSharePOI(val bundle: Bundle?) : DeepLinkState()

    object NavigateToConnectByCode : DeepLinkState()

    object NavigateToLastParked : DeepLinkState()

    object NavigateToEVGO : DeepLinkState()

    object NavigateToVehicleStatus : DeepLinkState()

    object NavigateToChargeInfo : DeepLinkState()

    object NavigateToChargeAssistEnrollmentStatusPath : DeepLinkState()

    data class NavigateToPreferredDealers(val data: Bundle?) : DeepLinkState()
}
