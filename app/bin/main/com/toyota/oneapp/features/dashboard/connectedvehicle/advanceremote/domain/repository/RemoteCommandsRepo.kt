package com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.repository

import com.toyota.oneapp.model.remote.EngineStatusResponse
import com.toyota.oneapp.model.remote.RemoteBaseResponse
import com.toyota.oneapp.model.remote.RemoteRequest
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import kotlin.coroutines.CoroutineContext

abstract class RemoteCommandsRepo(
    errorParser: ErrorMessageParser,
    ioContext: CoroutineContext,
) : BaseRepository(errorParser, ioContext) {
    abstract suspend fun ng86remoteRequest(
        vin: String,
        bodyRequest: RemoteRequest,
    ): Resource<RemoteBaseResponse?>

    abstract suspend fun remoteRequest(
        vin: String,
        bodyRequest: RemoteRequest,
    ): Resource<RemoteBaseResponse?>

    abstract suspend fun remoteRequestForPre17(
        brand: String,
        vin: String,
        bodyRequest: RemoteRequest,
    ): Resource<RemoteBaseResponse?>

    abstract suspend fun getNG86RemoteEngineStatus(
        brand: String,
        vin: String,
    ): Resource<EngineStatusResponse?>

    abstract suspend fun getCY17RemoteEngineStatus(
        brand: String,
        vin: String,
    ): Resource<EngineStatusResponse?>

    abstract suspend fun getCY17PlusRemoteEngineStatus(
        brand: String,
        vin: String,
    ): Resource<EngineStatusResponse?>
}
