/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.trips.dataaccess.repository

import com.toyota.oneapp.features.trips.dataaccess.servermodel.request.GenericTripsPostRequestBody
import com.toyota.oneapp.features.trips.dataaccess.servermodel.response.EventsResponse
import com.toyota.oneapp.features.trips.dataaccess.servermodel.response.ScoreResponse
import com.toyota.oneapp.features.trips.dataaccess.servermodel.response.TripResponse
import com.toyota.oneapp.features.trips.dataaccess.service.TripsApi
import com.toyota.oneapp.features.trips.domain.repository.TripsRepository
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class TripsDefaultRepository
    @Inject
    constructor(
        val service: TripsApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext),
        TripsRepository {
        override suspend fun getDriverScore(
            vin: String,
            generation: String,
            brand: String,
        ): Resource<ScoreResponse?> =
            makeApiCall {
                service.getDriverScore(
                    vin = vin,
                    generation = generation,
                    brand = brand,
                )
            }

        override suspend fun getTrips(
            vin: String,
            generation: String,
            brand: String,
        ): Resource<TripResponse?> =
            makeApiCall {
                service.getTrips(
                    vin = vin,
                    generation = generation,
                    brand = brand,
                )
            }

        override suspend fun getTripEvents(
            brand: String,
            vin: String,
            tripId: String,
        ): Resource<EventsResponse?> =
            makeApiCall {
                service.getTripEvents(
                    brand = brand,
                    vin = vin,
                    tripId = tripId,
                )
            }

        override suspend fun clearHistory(
            vin: String,
            brand: String,
        ): Resource<BaseResponse?> =
            makeApiCall {
                service.postClearHistory(
                    vin = vin,
                    brand = brand,
                    body = GenericTripsPostRequestBody(products = listOf("recentTrips")),
                )
            }

        override suspend fun optIn(
            vin: String,
            brand: String,
        ): Resource<BaseResponse?> =
            makeApiCall {
                service.postOptIn(
                    vin = vin,
                    brand = brand,
                    body = GenericTripsPostRequestBody(products = listOf("driverscore"), userPreference = true),
                )
            }

        override suspend fun optOut(
            vin: String,
            brand: String,
        ): Resource<BaseResponse?> =
            makeApiCall {
                service.postOptOut(
                    vin = vin,
                    brand = brand,
                    body = GenericTripsPostRequestBody(products = listOf("driverscore"), userPreference = true),
                )
            }
    }
