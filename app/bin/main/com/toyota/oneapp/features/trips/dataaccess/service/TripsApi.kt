/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.trips.dataaccess.service

import com.toyota.oneapp.features.trips.dataaccess.servermodel.request.GenericTripsPostRequestBody
import com.toyota.oneapp.features.trips.dataaccess.servermodel.response.EventsResponse
import com.toyota.oneapp.features.trips.dataaccess.servermodel.response.ScoreResponse
import com.toyota.oneapp.features.trips.dataaccess.servermodel.response.TripResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

interface TripsApi {
    @GET("/oneapi/v3/canbus/score")
    suspend fun getDriverScore(
        @Header("VIN") vin: String,
        @Header("generation") generation: String,
        @Header("x-brand") brand: String,
    ): Response<ScoreResponse>

    @GET("/oneapi/v3/canbus/trip")
    suspend fun getTrips(
        @Header("vin") vin: String,
        @Header("generation") generation: String,
        @Header("x-brand") brand: String,
    ): Response<TripResponse>

    @GET("/oneapi/v1/canbus/trip/events")
    suspend fun getTripEvents(
        @Header("X-BRAND") brand: String,
        @Header("VIN") vin: String,
        @Header("tripId") tripId: String,
    ): Response<EventsResponse>

    @POST("/oneapi/v1/preference/clear-history")
    suspend fun postClearHistory(
        @Header("X-BRAND") brand: String,
        @Header("VIN") vin: String,
        @Body() body: GenericTripsPostRequestBody,
    ): Response<BaseResponse>

    @POST("/oneapi/v1/telemetry/product-registration")
    suspend fun postOptIn(
        @Header("X-BRAND") brand: String,
        @Header("VIN") vin: String,
        @Body() body: GenericTripsPostRequestBody,
    ): Response<BaseResponse>

    @POST("/oneapi/v1/telemetry/product-unregistration")
    suspend fun postOptOut(
        @Header("X-BRAND") brand: String,
        @Header("VIN") vin: String,
        @Body() body: GenericTripsPostRequestBody,
    ): Response<BaseResponse>
}
