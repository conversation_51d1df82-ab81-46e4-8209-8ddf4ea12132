/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.trips.dataaccess.servermodel.response

import com.google.gson.annotations.SerializedName

data class EventDetails(
    @SerializedName("latitude") val latitude: Double? = null,
    @SerializedName("longitude") val longitude: Double? = null,
    @SerializedName("severity") val severity: Double? = null,
    @SerializedName("timestamp") val timestamp: String? = null,
    @SerializedName("type") val type: String? = null,
) {
    val location: String
        get() = "$latitude,$longitude"
}
