/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.trips.dataaccess.servermodel.response

import com.google.gson.annotations.SerializedName

data class Events(
    @SerializedName("collisions") val collisions: List<EventDetails?>? = null,
    @SerializedName("harshAcceleration") val harshAcceleration: List<EventDetails?>? = null,
    @SerializedName("harshBrakes") val harshBrakes: List<EventDetails?>? = null,
    @SerializedName("harshCornering") val harshCornering: List<EventDetails?>? = null,
)
