/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.trips.dataaccess.servermodel.response

import com.google.gson.annotations.SerializedName

data class TripPayload(
    @SerializedName("accelerationScore") val accelerationScore: Double? = null,
    @SerializedName("brakingScore") val brakingScore: Double? = null,
    @SerializedName("corneringScore") val corneringScore: Double? = null,
    @SerializedName("distanceDriven") val distanceDriven: DistanceDriven? = null,
    @SerializedName("distanceOverSpeedLimit") val distanceOverSpeedLimit: DistanceOverSpeedLimit? = null,
    @SerializedName("drivingTimeInMinutes") val drivingTimeInMinutes: Int? = null,
    @SerializedName("end") val end: String? = null,
    @SerializedName("endDate") val endDate: String? = null,
    @SerializedName("endTime") val endTime: String? = null,
    @SerializedName("harshAccelerationCount") val harshAccelerationCount: Int? = null,
    @SerializedName("harshBrakingCount") val harshBrakingCount: Int? = null,
    @SerializedName("harshCorneringCount") val harshCorneringCount: Int? = null,
    @SerializedName("harshEventsCount") val harshEventsCount: Int? = null,
    @SerializedName("overSpeedingCount") val overSpeedingCount: Int? = null,
    @SerializedName("speedingScore") val speedingScore: Double? = null,
    @SerializedName("start") val start: String? = null,
    @SerializedName("startDate") val startDate: String? = null,
    @SerializedName("startTime") val startTime: String? = null,
    @SerializedName("tripId") val tripId: String? = null,
    @SerializedName("tripPath") val tripPath: TripPath? = null,
    @SerializedName("tripScore") val tripScore: Int? = null,
    @SerializedName("vin") val vin: String? = null,
)
