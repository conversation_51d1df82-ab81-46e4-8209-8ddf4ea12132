/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.trips.dataaccess.servermodel.response

import com.google.gson.annotations.SerializedName

data class ScorePayload(
    @SerializedName("days") val days: Int? = null,
    @SerializedName("displayDriverScore") val displayDriverScore: Boolean? = null,
    @SerializedName("harshAccelerationCount") val harshAccelerationCount: Int? = null,
    @SerializedName("harshBrakingCount") val harshBrakingCount: Int? = null,
    @SerializedName("harshCorneringCount") val harshCorneringCount: Int? = null,
    @SerializedName("optInEligible") val optInEligible: Boolean? = null,
    @SerializedName("optOutEligible") val optOutEligible: Boolean? = null,
    @SerializedName("score") val score: Int? = null,
    @SerializedName("vin") val vin: String? = null,
)
