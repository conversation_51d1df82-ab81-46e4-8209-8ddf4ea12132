package com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.repository

import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel.ManualAndWarrantiesResponse
import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel.ManualAndWarrantyDocumentUrlResponse
import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.service.ManualAndWarrantiesAPI
import com.toyota.oneapp.features.glovebox.manualandwarranties.domain.repo.ManualAndWarrantiesRepo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class ManualAndWarrantiesDefaultRepo
    @Inject
    constructor(
        private val manualAndWarrantiesAPI: ManualAndWarrantiesAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), ManualAndWarrantiesRepo {
        override suspend fun fetchManualAndWarrantiesHeading(
            vin: String,
            brand: String,
            model: String,
            modelYear: String,
        ): Resource<ManualAndWarrantiesResponse?> {
            return makeApiCall {
                manualAndWarrantiesAPI.fetchManualAndWarrantiesHeading(
                    vin,
                    brand,
                    model,
                    modelYear,
                )
            }
        }

        override suspend fun fetchManualAndWarrantyDocumentUrl(
            brand: String,
            documentId: String,
        ): Resource<ManualAndWarrantyDocumentUrlResponse?> {
            return makeApiCall {
                manualAndWarrantiesAPI.fetchManualAndWarrantyDocumentUrl(documentId, brand)
            }
        }
    }
