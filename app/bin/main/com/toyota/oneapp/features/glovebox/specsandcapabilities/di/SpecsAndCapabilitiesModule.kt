package com.toyota.oneapp.features.glovebox.specsandcapabilities.di

import com.toyota.oneapp.features.glovebox.specsandcapabilities.application.SpecsAndCapabilitiesLogic
import com.toyota.oneapp.features.glovebox.specsandcapabilities.application.SpecsAndCapabilitiesUseCase
import com.toyota.oneapp.features.glovebox.specsandcapabilities.dataaccess.repository.SpecsAndCapabilitiesDefaultRepo
import com.toyota.oneapp.features.glovebox.specsandcapabilities.domain.repo.SpecsAndCapabilitiesRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class SpecsAndCapabilitiesModule {
    @Binds
    abstract fun bindSpecsAndCapabilitiesRepo(repo: SpecsAndCapabilitiesDefaultRepo): SpecsAndCapabilitiesRepo

    @Binds
    abstract fun bindSpecsAndCapabilitiesUseCase(logic: SpecsAndCapabilitiesLogic): SpecsAndCapabilitiesUseCase
}
