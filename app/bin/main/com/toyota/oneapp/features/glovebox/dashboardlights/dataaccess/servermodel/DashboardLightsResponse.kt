package com.toyota.oneapp.features.glovebox.dashboardlights.dataaccess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.glovebox.dashboardlights.domain.model.DashboardLightUIModel
import com.toyota.oneapp.features.glovebox.dashboardlights.domain.model.DashboardLightsScreenUIModel

data class DashboardLightsResponse(
    @SerializedName("payload") val payload: DashboardLightsPayload?,
)

data class DashboardLightsPayload(
    @SerializedName("model") val model: String?,
    @SerializedName("modelYear") val modelYear: String?,
    @SerializedName("dashboardLights") val dashboardLights: List<DashboardLights>?,
)

data class DashboardLights(
    @SerializedName("dashboardLightTitle") val dashboardLightTitle: String?,
    @SerializedName("dashboardLightDisclaimer") val dashboardLightDisclaimer: String?,
    @SerializedName("dashboardLightDescription") val dashboardLightDescription: String?,
    @SerializedName("activeImageURL") val activeImageURL: String?,
    @SerializedName("inActiveImageURL") val inActiveImageURL: String?,
    @SerializedName("actualImageURL") val actualImageURL: String?,
    @SerializedName("rollOverImageURL") val rollOverImageURL: String?,
)

fun DashboardLightsResponse.toUIModel(): DashboardLightsScreenUIModel {
    val dashboardLightList = ArrayList<DashboardLightUIModel>()
    this.payload?.dashboardLights?.forEach { dashboardLight ->
        dashboardLightList.add(
            DashboardLightUIModel(
                title = dashboardLight.dashboardLightTitle.orEmpty(),
                icon = getIconUrl(dashboardLight),
                description = dashboardLight.dashboardLightDescription.orEmpty(),
            ),
        )
    }

    return DashboardLightsScreenUIModel(
        dashboardLights = dashboardLightList,
    )
}

private fun getIconUrl(dashboardLight: DashboardLights): String =
    dashboardLight.inActiveImageURL ?: dashboardLight.activeImageURL
        ?: dashboardLight.activeImageURL ?: dashboardLight.rollOverImageURL ?: ""
