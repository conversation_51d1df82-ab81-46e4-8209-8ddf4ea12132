package com.toyota.oneapp.features.glovebox.manualandwarranties.di

import com.toyota.oneapp.features.glovebox.manualandwarranties.application.ManualAndWarrantiesLogic
import com.toyota.oneapp.features.glovebox.manualandwarranties.application.ManualAndWarrantiesUseCase
import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.repository.ManualAndWarrantiesDefaultRepo
import com.toyota.oneapp.features.glovebox.manualandwarranties.domain.repo.ManualAndWarrantiesRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class ManualAndWarrantiesModule {
    @Binds
    abstract fun bindManualAndWarrantiesRepo(repo: ManualAndWarrantiesDefaultRepo): ManualAndWarrantiesRepo

    @Binds
    abstract fun bindManualAndWarrantiesUseCase(logic: ManualAndWarrantiesLogic): ManualAndWarrantiesUseCase
}
