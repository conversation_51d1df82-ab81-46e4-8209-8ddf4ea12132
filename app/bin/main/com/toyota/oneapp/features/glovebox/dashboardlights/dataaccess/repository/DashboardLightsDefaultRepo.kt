package com.toyota.oneapp.features.glovebox.dashboardlights.dataaccess.repository

import com.toyota.oneapp.features.glovebox.dashboardlights.dataaccess.servermodel.DashboardLightsResponse
import com.toyota.oneapp.features.glovebox.dashboardlights.dataaccess.service.DashboardLightsAPI
import com.toyota.oneapp.features.glovebox.dashboardlights.domain.repo.DashboardLightsRepo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class DashboardLightsDefaultRepo
    @Inject
    constructor(
        private val dashboardLightsAPI: DashboardLightsAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), DashboardLightsRepo {
        override suspend fun fetchDashboardLights(vin: String): Resource<DashboardLightsResponse?> {
            return makeApiCall {
                dashboardLightsAPI.fetchDashboardLights(vin)
            }
        }
    }
