package com.toyota.oneapp.features.glovebox.manualandwarranties.presentation

import androidx.lifecycle.viewModelScope
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.glovebox.manualandwarranties.application.ApiState
import com.toyota.oneapp.features.glovebox.manualandwarranties.application.ManualAndWarrantiesUseCase
import com.toyota.oneapp.features.glovebox.manualandwarranties.application.ViewState
import com.toyota.oneapp.ui.BaseFragmentViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class ManualsAndWarrantiesViewModel
    @Inject
    constructor(
        private val manualAndWarrantiesUseCase: ManualAndWarrantiesUseCase,
        private val applicationData: ApplicationData,
        private val dispatcherProvider: DispatcherProvider,
    ) : BaseFragmentViewModel() {
        private val _viewState =
            MutableStateFlow<ViewState>(
                value = ViewState.Nothing,
            )
        val viewState = _viewState.asStateFlow()

        private val _apiState = MutableStateFlow<ApiState>(value = ApiState.Nothing)
        val apiState = _apiState.asStateFlow()

        init {
            fetchAllManualsAndWarranties()
        }

        fun dismissProgress() {
            _apiState.value = ApiState.Nothing
        }

        private fun fetchAllManualsAndWarranties() {
            _viewState.value = ViewState.Init
            applicationData.getSelectedVehicleState().value?.let { vehicleInfo ->
                viewModelScope.launch(dispatcherProvider.main()) {
                    manualAndWarrantiesUseCase.fetchAllManualsAndWarranties(vehicleInfo)
                        .flowOn(dispatcherProvider.io())
                        .collect { uiMap ->
                            if (!uiMap.isNullOrEmpty()) {
                                _viewState.value = ViewState.LoadAllManualsAndWarranties(uiMap)
                            } else {
                                _viewState.value = ViewState.EmptyManualsAndWarranties
                            }
                        }
                }
            }
        }

        fun fetchDocumentUrl(documentId: String) {
            _apiState.value = ApiState.ShowProgress
            applicationData.getSelectedVehicleState().value?.let { vehicleInfo ->
                viewModelScope.launch(dispatcherProvider.main()) {
                    manualAndWarrantiesUseCase.fetchDocumentUrl(
                        brand = vehicleInfo.brand,
                        documentId = documentId,
                    )
                        .flowOn(dispatcherProvider.io())
                        .collect { documentUrl ->
                            if (documentUrl.isNotNullOrEmpty()) {
                                _apiState.value = ApiState.GetDocumentLinkSuccess(documentUrl)
                            } else {
                                _apiState.value = ApiState.Error()
                            }
                        }
                }
            }
        }
    }
