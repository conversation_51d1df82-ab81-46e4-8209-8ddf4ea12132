package com.toyota.oneapp.features.glovebox.dashboardlights.presentation

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.rememberNestedScrollInteropConnection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import coil.compose.AsyncImage
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.GloveBoxGridComposableShimmer
import com.toyota.oneapp.features.core.composable.OABody3HtmlTextView
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.composable.OABody4HtmlTextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.launchSecondaryBottomSheetAction
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.ftue.presentation.extension.w
import com.toyota.oneapp.features.glovebox.dashboardlights.application.DashboardLightsState
import com.toyota.oneapp.features.glovebox.dashboardlights.domain.model.DashboardLightUIModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@OptIn(ExperimentalComposeUiApi::class)
@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun DashboardLightsScreen(
    navHostController: NavHostController,
    modifier: Modifier = Modifier,
    viewModel: DashboardLightsViewModel = hiltViewModel(),
) {
    val searchText = remember { mutableStateOf(TextFieldValue("")) }
    val dashboardLightsState = viewModel.dashboardLightsState.collectAsState()

    Scaffold(
        backgroundColor = AppTheme.colors.tertiary15,
        modifier =
            modifier
                .fillMaxSize()
                .nestedScroll(rememberNestedScrollInteropConnection()),
    ) {
        Column(
            modifier =
                Modifier
                    .padding(horizontal = 16.dp, vertical = 8.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(AppTheme.colors.button02d)
                            .clickable {
                                navHostController.popBackStack()
                            }
                            .layoutId(AccessibilityId.ID_DASHBOARD_LIGHTS_BACK_BTN)
                            .testTagID(AccessibilityId.ID_DASHBOARD_LIGHTS_BACK_BTN),
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_back_arrow),
                        contentDescription = stringResource(id = R.string.Common_back),
                        modifier =
                            Modifier
                                .padding(
                                    start = 19.dp,
                                    end = 22.dp,
                                    top = 17.dp,
                                    bottom = 17.dp,
                                ),
                        tint = AppTheme.colors.button02a,
                    )
                }

                OASubHeadLine3TextView(
                    text = stringResource(id = R.string.warning_light_title),
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .padding(top = 6.dp)
                            .align(Alignment.TopCenter),
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            TextField(
                value = searchText.value,
                onValueChange = {
                    searchText.value = it
                    viewModel.filterDashboardLights(searchText.value.text)
                },
                shape = RoundedCornerShape(30.dp),
                placeholder = {
                    OABody3TextView(
                        text = stringResource(id = R.string.dashboard_light_search),
                        color = AppTheme.colors.tertiary07,
                    )
                },
                leadingIcon = {
                    Image(
                        painter = painterResource(id = R.drawable.ic_search),
                        contentDescription = stringResource(id = R.string.dashboard_light_search),
                        colorFilter = ColorFilter.tint(AppTheme.colors.tertiary00),
                        modifier =
                            Modifier
                                .size(24.dp),
                    )
                },
                trailingIcon = {
                    if (searchText.value.text.isNotEmpty()) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_remove),
                            contentDescription = stringResource(id = R.string.remove),
                            colorFilter = ColorFilter.tint(AppTheme.colors.tertiary07),
                            modifier =
                                Modifier
                                    .size(16.dp)
                                    .clickable {
                                        searchText.value = TextFieldValue("")
                                        viewModel.filterDashboardLights("")
                                    }
                                    .layoutId(AccessibilityId.ID_CLEAR_SEARCH_IMAGE_BUTTON)
                                    .testTagID(AccessibilityId.ID_CLEAR_SEARCH_IMAGE_BUTTON),
                        )
                    }
                },
                colors =
                    TextFieldDefaults.textFieldColors(
                        textColor = AppTheme.colors.tertiary03,
                        backgroundColor = AppTheme.colors.tile05,
                        cursorColor = AppTheme.colors.tertiary05,
                        unfocusedIndicatorColor = Color.Transparent,
                        focusedIndicatorColor = Color.Transparent,
                    ),
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(all = 16.dp)
                        .layoutId(AccessibilityId.ID_DASHBOARD_LIGHTS_SEARCH_TEXTFIELD)
                        .testTagID(AccessibilityId.ID_DASHBOARD_LIGHTS_SEARCH_TEXTFIELD),
            )

            GloveBoxGridComposableShimmer(
                isLoading = dashboardLightsState.value == DashboardLightsState.Loading,
                contentAfterLoading = {
                    when (dashboardLightsState.value) {
                        is DashboardLightsState.Success -> {
                            DashboardLightsContent(
                                tileList = (dashboardLightsState.value as DashboardLightsState.Success).tileList,
                            )
                        }
                        is DashboardLightsState.Error -> {
                            EmptyDashboardLightsScreen()
                        }
                        else -> {}
                    }
                },
            )
        }
    }
}

private const val CELL_COUNT = 2

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun DashboardLightsContent(
    tileList: List<DashboardLightUIModel>,
    modifier: Modifier = Modifier,
) {
    val coroutineScope = rememberCoroutineScope()
    val bottomSheet = LocalBottomSheet.current
    bottomSheet.secondarySheetShape.value =
        RoundedCornerShape(
            topStart = 30.dp,
            topEnd = 30.dp,
        )

    if (tileList.isNotEmpty()) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(CELL_COUNT),
            contentPadding = PaddingValues(horizontal = 8.dp),
            modifier = modifier,
        ) {
            items(tileList) { dashboardLight ->
                DashboardLightTile(
                    dashboardLightUIModel = dashboardLight,
                ) {
                    coroutineScope.launchSecondaryBottomSheetAction(bottomSheet) {
                        DashboardLightDetailBottomSheet(
                            modalBottomSheetState = it,
                            coroutineScope = coroutineScope,
                            uiModel = dashboardLight,
                        )
                    }
                }
            }
        }
    } else {
        EmptyDashboardLightsScreen()
    }
}

@OptIn(ExperimentalMaterialApi::class, ExperimentalGlideComposeApi::class)
@Composable
private fun DashboardLightTile(
    dashboardLightUIModel: DashboardLightUIModel,
    modifier: Modifier = Modifier,
    onTileClick: () -> Unit,
) {
    Box(
        modifier =
            modifier
                .padding(4.dp),
    ) {
        Card(
            shape = RoundedCornerShape(8.dp),
            backgroundColor = AppTheme.colors.tile02,
            elevation = 4.dp,
            onClick = onTileClick,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(169.dp),
        ) {
            Column(
                modifier =
                    Modifier
                        .padding(all = 16.dp),
            ) {
                Surface(
                    shape = CircleShape,
                    color = AppTheme.colors.button05b,
                    modifier =
                        Modifier
                            .size(48.dp)
                            .align(Alignment.Start),
                ) {
                    AsyncImage(
                        model = dashboardLightUIModel.icon,
                        contentDescription = dashboardLightUIModel.title,
                        contentScale = ContentScale.FillWidth,
                        placeholder = painterResource(id = R.drawable.ic_image_not_found),
                        modifier =
                            Modifier
                                .size(24.dp),
                    )
                }

                Spacer(modifier = Modifier.height(24.dp))

                OABody4HtmlTextView(
                    text = dashboardLightUIModel.title,
                    color = AppTheme.colors.tertiary03,
                    maxLines = 1,
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun DashboardLightDetailBottomSheet(
    modalBottomSheetState: ModalBottomSheetState,
    coroutineScope: CoroutineScope,
    uiModel: DashboardLightUIModel,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .background(AppTheme.colors.tertiary15)
                .padding(start = 16.dp, end = 16.dp, top = 32.dp),
    ) {
        Box(
            modifier =
                Modifier
                    .width(48.w())
                    .height(48.h())
                    .clip(CircleShape)
                    .background(AppTheme.colors.tertiary12)
                    .wrapContentSize(Alignment.Center)
                    .clickable {
                        coroutineScope.launch {
                            if (modalBottomSheetState.isVisible) modalBottomSheetState.hide()
                        }
                    }
                    .layoutId(AccessibilityId.ID_CLOSE_DASHBOARD_LIGHTS_DETAIL_BUTTON)
                    .testTagID(AccessibilityId.ID_CLOSE_DASHBOARD_LIGHTS_DETAIL_BUTTON),
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_close),
                contentDescription = stringResource(id = R.string.ftueCloseIconDescription),
                tint = AppTheme.colors.button02a,
            )
        }

        AsyncImage(
            model = uiModel.icon,
            placeholder = painterResource(id = R.drawable.ic_image_not_found),
            contentScale = ContentScale.FillWidth,
            contentDescription = uiModel.title,
            modifier =
                Modifier
                    .size(80.dp)
                    .align(Alignment.CenterHorizontally),
        )

        Spacer(modifier = Modifier.height(24.dp))

        OABody3HtmlTextView(
            text = uiModel.description,
            color = AppTheme.colors.tertiary03,
        )

        Spacer(modifier = Modifier.height(32.dp))
    }
}

@Composable
private fun EmptyDashboardLightsScreen(modifier: Modifier = Modifier) {
    Box(modifier = modifier.fillMaxSize()) {
        Column(
            modifier =
                Modifier
                    .wrapContentSize()
                    .align(Alignment.Center),
        ) {
            Surface(
                shape = CircleShape,
                color = AppTheme.colors.primary02,
                modifier =
                    Modifier
                        .size(48.dp)
                        .align(Alignment.CenterHorizontally),
            ) {
                Image(
                    modifier = Modifier.padding(12.dp),
                    painter = painterResource(id = R.drawable.ic_no_manuals),
                    colorFilter = ColorFilter.tint(AppTheme.colors.primary01),
                    contentDescription = stringResource(id = R.string.no_data_found),
                )
            }

            OASubHeadLine1TextView(
                text = stringResource(id = R.string.no_data_found),
                color = AppTheme.colors.tertiary03,
            )
        }
    }
}
