package com.toyota.oneapp.features.glovebox.dashboardlights.application

import com.toyota.oneapp.features.glovebox.dashboardlights.domain.model.DashboardLightUIModel

sealed class DashboardLightsState {
    object Nothing : DashboardLightsState()

    object Loading : DashboardLightsState()

    class Success(val tileList: List<DashboardLightUIModel>) : DashboardLightsState()

    class Error : DashboardLightsState()
}
