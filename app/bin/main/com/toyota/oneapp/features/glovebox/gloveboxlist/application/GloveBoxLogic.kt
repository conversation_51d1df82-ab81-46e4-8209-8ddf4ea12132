package com.toyota.oneapp.features.glovebox.gloveboxlist.application

import com.toyota.oneapp.R
import com.toyota.oneapp.core.BuildWrapper
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.glovebox.gloveboxlist.dataaccess.servermodel.toUIModel
import com.toyota.oneapp.features.glovebox.gloveboxlist.domain.model.GloveBoxSetting
import com.toyota.oneapp.features.glovebox.gloveboxlist.domain.repo.HowToVideosRepo
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject

class GloveBoxLogic
    @Inject
    constructor(
        private val preferenceModel: OneAppPreferenceModel,
        private val repository: HowToVideosRepo,
        private val buildWrapper: BuildWrapper,
    ) : GloveBoxUseCase {
        override fun loadLocalSettings(vehicleInfo: VehicleInfo): ArrayList<GloveBoxSetting> {
            val settings = ArrayList<GloveBoxSetting>()
            settings.add(
                GloveBoxSetting.SpecsCapabilites(
                    specCapabilitiesTitle =
                        if (buildWrapper.isSubaruApp()) {
                            R.string.AddVehicle_vehicle_capabilities
                        } else {
                            R.string.specs_capabilities
                        },
                    specCapabilitiesIconRes = R.drawable.ic_car_park_location,
                    specCapabilitiesIconDescription = R.string.specs_capabilities,
                    specCapabilitiesRoute = OAScreen.SpecsAndCapabilities.route,
                    specAccessibilityId = AccessibilityId.ID_SPECS_AND_CAPABILITIES_TILE,
                ),
            )
            if (vehicleInfo.isFeatureEnabled(Feature.OWNERS_MANUAL)) {
                settings.add(
                    GloveBoxSetting.ManualsAndWarranties(
                        manualsAndWarrantiesTitle = R.string.glovebox_manuals_warranties,
                        manualsAndWarrantiesIconRes = R.drawable.ic_manuals_and_warranties,
                        manualsAndWarrantiesIconDescription = R.string.glovebox_manuals_warranties,
                        manualsAndWarrantiesRoute = OAScreen.ManualsAndWarranties.route,
                        manualsAccessibilityId = AccessibilityId.ID_MANUALS_AND_WARRANTIES_TILE,
                    ),
                )
            }

            if (vehicleInfo.isFeatureEnabled(Feature.DASHBOARD_LIGHTS)) {
                settings.add(
                    GloveBoxSetting.DashboardLights(
                        dashboardLightsTitle = R.string.warning_light_title,
                        dashboardLightsIconRes = R.drawable.ic_dashboard_lights,
                        dashboardLightsIconDescription = R.string.warning_light_title,
                        dashboardLightsRoute = OAScreen.DashboardLights.route,
                        dashboardLightsAccessibilityId = AccessibilityId.ID_DASHBOARD_LIGHTS_TILE,
                    ),
                )
            }
            if (vehicleInfo.isFeatureEnabled(Feature.TOYOTA_FOR_FAMILIES) &&
                vehicleInfo.isUrlHasProtocol
            ) {
                settings.add(
                    GloveBoxSetting.ToyotaForFamilies(
                        ttfTitle = R.string.toyota_for_families,
                        ttfIconRes = R.drawable.ic_toyota_for_familes,
                        ttfIconDescription = R.string.toyota_for_families,
                        ttfUrl = vehicleInfo.ttfLinkUrl,
                        ttfAccessibilityId = AccessibilityId.ID_TTF_TILE,
                    ),
                )
            }
            if (vehicleInfo.isFeatureEnabled(Feature.PRO_X_SEATS) &&
                vehicleInfo.proXSeatsVideoLink != null
            ) {
                settings.add(
                    GloveBoxSetting.IsoDynamicSeats(
                        proSeatTitle = R.string.isodynamic_title,
                        proSeatIconRes = R.drawable.ic_pro_x_seat,
                        proSeatIconDescription = R.string.isodynamic_title,
                        proSeatUrl = vehicleInfo.proXSeatsVideoLink,
                        proSeatAccessibilityId = AccessibilityId.ID_PRO_SEATS_TILE,
                    ),
                )
            }
            return settings
        }

        override suspend fun fetchHowToVideos(vehicleInfo: VehicleInfo): Flow<GloveBoxSetting.HowToVideosSettings?> {
            return if (vehicleInfo.isFeatureEnabled(Feature.HOW_TO_VIDEOS)) {
                flow {
                    val response =
                        repository.fetchHowToVideos(
                            vin = vehicleInfo.vin,
                            appBrand = vehicleInfo.brand,
                            guid = preferenceModel.getGuid(),
                        )

                    if (response is Resource.Success) {
                        emit(
                            GloveBoxSetting.HowToVideosSettings(
                                howToVideoTitle = R.string.how_to_videos,
                                howToVideoIconRes = R.drawable.ic_how_to_videos,
                                howToVideoIconDescription = R.string.how_to_videos,
                                howToVideos = response.data?.toUIModel(),
                                howToVideoAccessibilityId = AccessibilityId.ID_HOW_TO_VIDEO_TILE,
                            ),
                        )
                    } else {
                        emit(null)
                    }
                }
            } else {
                flowOf(null)
            }
        }
    }
