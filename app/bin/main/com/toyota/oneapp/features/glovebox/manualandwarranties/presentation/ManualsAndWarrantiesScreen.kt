package com.toyota.oneapp.features.glovebox.manualandwarranties.presentation

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.rememberNestedScrollInteropConnection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.GloveBoxListComposableShimmer
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.glovebox.manualandwarranties.application.ApiState
import com.toyota.oneapp.features.glovebox.manualandwarranties.application.ViewState
import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel.OwnerManual

@OptIn(ExperimentalComposeUiApi::class)
@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun ManualsAndWarrantiesScreen(
    modifier: Modifier = Modifier,
    viewModel: ManualsAndWarrantiesViewModel = hiltViewModel(),
    onBackClick: () -> Unit,
) {
    val viewState = viewModel.viewState.collectAsState()
    val apiState = viewModel.apiState.collectAsState()

    when (apiState.value) {
        is ApiState.ShowProgress -> {
            ShowProgressIndicator(dialogState = true)
        }
        is ApiState.GetDocumentLinkSuccess -> {
            viewModel.dismissProgress()
            val intent =
                Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse((apiState.value as ApiState.GetDocumentLinkSuccess).docUrl),
                )
            if (intent.resolveActivity(LocalContext.current.packageManager) != null) {
                LocalContext.current.startActivity(intent)
            }
        }
        else -> {}
    }

    Scaffold(
        backgroundColor = AppTheme.colors.tertiary15,
        modifier =
            modifier
                .fillMaxSize()
                .nestedScroll(rememberNestedScrollInteropConnection()),
    ) {
        Column(
            modifier =
                Modifier
                    .padding(horizontal = 16.dp, vertical = 8.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(horizontal = 16.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(AppTheme.colors.button02d)
                            .clickable {
                                onBackClick()
                            }
                            .layoutId(AccessibilityId.ID_MANUALS_WARRANTIES_BACK_BTN)
                            .testTagID(AccessibilityId.ID_MANUALS_WARRANTIES_BACK_BTN),
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_back_arrow),
                        contentDescription = stringResource(id = R.string.Common_back),
                        modifier =
                            Modifier
                                .padding(
                                    start = 19.dp,
                                    end = 22.dp,
                                    top = 17.dp,
                                    bottom = 17.dp,
                                ),
                        tint = AppTheme.colors.button02a,
                    )
                }

                OASubHeadLine3TextView(
                    text = stringResource(id = R.string.glovebox_manuals_warranties),
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .padding(top = 6.dp)
                            .align(Alignment.TopCenter),
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            GloveBoxListComposableShimmer(
                isLoading = (viewState.value == ViewState.Init),
                contentAfterLoading = {
                    when (viewState.value) {
                        is ViewState.EmptyManualsAndWarranties -> {
                            EmptyManualsAndWarrantiesScreen()
                        }
                        is ViewState.LoadAllManualsAndWarranties -> {
                            ManualsAndWarrantiesListScreen(
                                viewModel = viewModel,
                                uiMap = (viewState.value as ViewState.LoadAllManualsAndWarranties).uiMap,
                            )
                        }
                        else -> {}
                    }
                },
            )
        }
    }
}

@Composable
private fun ManualsAndWarrantiesListScreen(
    viewModel: ManualsAndWarrantiesViewModel,
    uiMap: Map<String, List<OwnerManual>?>,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier,
    ) {
        uiMap.forEach { title, documentList ->
            item {
                documentList?.let {
                    Box(
                        modifier =
                            Modifier
                                .padding(vertical = 4.dp),
                    ) {
                        ItemCard(
                            viewModel = viewModel,
                            title = title,
                            documentList = it,
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun ItemCard(
    viewModel: ManualsAndWarrantiesViewModel,
    title: String,
    documentList: List<OwnerManual>,
    modifier: Modifier = Modifier,
) {
    Card(
        backgroundColor = AppTheme.colors.tile02,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(start = 16.dp, top = 16.dp, end = 16.dp),
        ) {
            OABody4TextView(
                text = title,
                color = AppTheme.colors.tertiary03,
            )

            Spacer(modifier = Modifier.height(8.dp))

            documentList.forEachIndexed { index, document ->
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .wrapContentHeight()
                            .clickable {
                                viewModel.fetchDocumentUrl(document.documentUrl.orEmpty())
                            },
                ) {
                    Row(
                        modifier =
                            Modifier
                                .padding(top = 8.dp, bottom = 16.dp),
                    ) {
                        OABody3TextView(
                            text = document.title.orEmpty(),
                            color = AppTheme.colors.tertiary03,
                            modifier =
                                Modifier
                                    .weight(3f),
                        )

                        Box(
                            modifier =
                                Modifier
                                    .weight(1f)
                                    .align(Alignment.CenterVertically),
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_ev_chevron_right),
                                contentDescription = stringResource(id = R.string.Common_back),
                                modifier =
                                    Modifier
                                        .size(16.dp)
                                        .align(Alignment.CenterEnd),
                                tint = AppTheme.colors.tertiary03,
                            )
                        }
                    }

                    if (index != documentList.lastIndex) {
                        Divider(
                            color = AppTheme.colors.tertiary07,
                            thickness = 1.dp,
                            modifier =
                                Modifier
                                    .align(Alignment.BottomStart),
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun EmptyManualsAndWarrantiesScreen(modifier: Modifier = Modifier) {
    Box(modifier = modifier.fillMaxSize()) {
        Column(
            modifier =
                Modifier
                    .wrapContentSize()
                    .align(Alignment.Center),
        ) {
            Surface(
                shape = CircleShape,
                color = AppTheme.colors.primary02,
                modifier =
                    Modifier
                        .size(48.dp)
                        .align(Alignment.CenterHorizontally),
            ) {
                Image(
                    modifier = Modifier.padding(12.dp),
                    painter = painterResource(id = R.drawable.ic_no_manuals),
                    colorFilter = ColorFilter.tint(AppTheme.colors.primary01),
                    contentDescription = stringResource(id = R.string.no_manuals_and_warranties),
                )
            }

            OASubHeadLine1TextView(
                text = stringResource(id = R.string.no_manuals_and_warranties),
                color = AppTheme.colors.tertiary03,
            )
        }
    }
}
