package com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel

import com.google.gson.annotations.SerializedName

data class ManualAndWarrantiesResponse(
    @SerializedName("payload") val payload: ManualAndWarrantiesPayload?,
)

data class ManualAndWarrantiesPayload(
    @SerializedName("model") val model: String?,
    @SerializedName("modelYear") val modelYear: String?,
    @SerializedName("make") val make: String?,
    @SerializedName("locale") val locale: String?,
    @SerializedName("translations") val translations: Translations?,
    @SerializedName("documents") val documents: Documents?,
    @SerializedName("appSubTitle") val appSubTitle: String?,
)

data class Documents(
    @SerializedName("omms") val omms: List<OwnerManual>?,
    @SerializedName("omnav") val omnav: List<OwnerManual>?,
    @SerializedName("om") val om: List<OwnerManual>?,
    @SerializedName("manuals") val manuals: List<OwnerManual>?,
)

data class OwnerManual(
    @SerializedName("summary") val summary: String?,
    @SerializedName("documentUrl") val documentUrl: String?,
    @SerializedName("title") val title: String?,
    @SerializedName("documentType") val documentType: String?,
    @SerializedName("pubNumber") val pubNumber: String?,
)

data class Translations(
    @SerializedName("omms") val omms: String?,
    @SerializedName("omnav") val omnav: String?,
    @SerializedName("om") val om: String?,
    @SerializedName("manuals") val manuals: String?,
)
