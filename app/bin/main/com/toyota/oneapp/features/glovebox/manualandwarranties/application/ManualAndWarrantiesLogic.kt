package com.toyota.oneapp.features.glovebox.manualandwarranties.application

import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel.ManualAndWarrantiesPayload
import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel.OwnerManual
import com.toyota.oneapp.features.glovebox.manualandwarranties.domain.repo.ManualAndWarrantiesRepo
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class ManualAndWarrantiesLogic
    @Inject
    constructor(
        private val repository: ManualAndWarrantiesRepo,
    ) : ManualAndWarrantiesUseCase {
        override suspend fun fetchAllManualsAndWarranties(vehicleInfo: VehicleInfo): Flow<Map<String, List<OwnerManual>?>?> {
            return flow {
                val response =
                    repository.fetchManualAndWarrantiesHeading(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        model = vehicleInfo.modelName,
                        modelYear = vehicleInfo.modelYear,
                    )

                if (response is Resource.Success) {
                    emit(mapToUIModel(response.data?.payload))
                } else {
                    emit(null)
                }
            }
        }

        private fun mapToUIModel(payload: ManualAndWarrantiesPayload?): Map<String, List<OwnerManual>?>? {
            if (payload == null) {
                return null
            }

            val manualsAndWarrantiesMap = LinkedHashMap<String, List<OwnerManual>?>()
            payload.documents?.apply {
                if (omms?.isNotEmpty() == true) {
                    manualsAndWarrantiesMap.put(
                        key = payload.translations?.omms.orEmpty(),
                        value = omms,
                    )
                }

                if (omnav?.isNotEmpty() == true) {
                    manualsAndWarrantiesMap.put(
                        key = payload.translations?.omnav.orEmpty(),
                        value = omnav,
                    )
                }

                if (om?.isNotEmpty() == true) {
                    manualsAndWarrantiesMap.put(
                        key = payload.translations?.om.orEmpty(),
                        value = om,
                    )
                }

                if (manuals?.isNotEmpty() == true) {
                    manualsAndWarrantiesMap.put(
                        key = payload.translations?.manuals.orEmpty(),
                        value = manuals,
                    )
                }
            }
            return manualsAndWarrantiesMap
        }

        override suspend fun fetchDocumentUrl(
            brand: String,
            documentId: String,
        ): Flow<String?> {
            return flow {
                val response =
                    repository.fetchManualAndWarrantyDocumentUrl(
                        brand = brand,
                        documentId = documentId,
                    )

                if (response is Resource.Success) {
                    emit(response.data?.payload?.pdfLink)
                } else {
                    emit(null)
                }
            }
        }
    }
