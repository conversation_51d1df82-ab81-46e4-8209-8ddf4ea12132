package com.toyota.oneapp.features.glovebox.dashboardlights.di

import com.toyota.oneapp.features.glovebox.dashboardlights.application.DashboardLightsLogic
import com.toyota.oneapp.features.glovebox.dashboardlights.application.DashboardLightsUseCase
import com.toyota.oneapp.features.glovebox.dashboardlights.dataaccess.repository.DashboardLightsDefaultRepo
import com.toyota.oneapp.features.glovebox.dashboardlights.domain.repo.DashboardLightsRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class DashboardLightsModule {
    @Binds
    abstract fun bindDashboardLightsRepo(repo: DashboardLightsDefaultRepo): DashboardLightsRepo

    @Binds
    abstract fun bindDashboardLightsUseCase(logic: DashboardLightsLogic): DashboardLightsUseCase
}
