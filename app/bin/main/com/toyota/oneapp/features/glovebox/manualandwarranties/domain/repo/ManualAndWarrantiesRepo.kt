package com.toyota.oneapp.features.glovebox.manualandwarranties.domain.repo

import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel.ManualAndWarrantiesResponse
import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel.ManualAndWarrantyDocumentUrlResponse
import com.toyota.oneapp.network.Resource

interface ManualAndWarrantiesRepo {
    suspend fun fetchManualAndWarrantiesHeading(
        vin: String,
        brand: String,
        model: String,
        modelYear: String,
    ): Resource<ManualAndWarrantiesResponse?>

    suspend fun fetchManualAndWarrantyDocumentUrl(
        brand: String,
        documentId: String,
    ): Resource<ManualAndWarrantyDocumentUrlResponse?>
}
