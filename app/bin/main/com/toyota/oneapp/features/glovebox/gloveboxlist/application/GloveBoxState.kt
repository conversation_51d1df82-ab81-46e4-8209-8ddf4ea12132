package com.toyota.oneapp.features.glovebox.gloveboxlist.application

import com.toyota.oneapp.features.glovebox.gloveboxlist.domain.model.GloveBoxSetting
import com.toyota.oneapp.model.vehicle.VehicleInfo

sealed class GloveBoxState {
    object Loading : GloveBoxState()

    class Success(
        val vehicleInfo: VehicleInfo,
        val gridItems: List<GloveBoxSetting>,
        val lastItem: GloveBoxSetting? = null,
    ) : GloveBoxState()

    class Error(val errorCode: String?, val errorMessage: String?) : GloveBoxState()
}
