package com.toyota.oneapp.features.glovebox.gloveboxlist.application

import com.toyota.oneapp.features.glovebox.gloveboxlist.domain.model.GloveBoxSetting
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface GloveBoxUseCase {
    suspend fun fetchHowToVideos(vehicleInfo: VehicleInfo): Flow<GloveBoxSetting.HowToVideosSettings?>

    fun loadLocalSettings(vehicleInfo: VehicleInfo): ArrayList<GloveBoxSetting>
}
