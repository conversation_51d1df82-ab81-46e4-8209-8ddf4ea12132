package com.toyota.oneapp.features.glovebox.gloveboxlist.di

import com.toyota.oneapp.features.glovebox.gloveboxlist.application.GloveBoxLogic
import com.toyota.oneapp.features.glovebox.gloveboxlist.application.GloveBoxUseCase
import com.toyota.oneapp.features.glovebox.gloveboxlist.dataaccess.repository.HowToVideosDefaultRepo
import com.toyota.oneapp.features.glovebox.gloveboxlist.domain.repo.HowToVideosRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class GloveBoxModule {
    @Binds
    abstract fun bindHowToVideoRepo(repo: HowToVideosDefaultRepo): HowToVideosRepo

    @Binds
    abstract fun bindGloveBoxUseCase(logic: GloveBoxLogic): GloveBoxUseCase
}
