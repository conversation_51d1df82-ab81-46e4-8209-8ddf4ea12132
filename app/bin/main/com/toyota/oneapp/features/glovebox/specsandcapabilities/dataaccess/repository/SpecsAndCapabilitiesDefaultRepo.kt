package com.toyota.oneapp.features.glovebox.specsandcapabilities.dataaccess.repository

import com.toyota.oneapp.features.glovebox.specsandcapabilities.dataaccess.servermodel.VehicleSpecsResponse
import com.toyota.oneapp.features.glovebox.specsandcapabilities.dataaccess.service.SpecsAndCapabilitiesAPI
import com.toyota.oneapp.features.glovebox.specsandcapabilities.domain.repo.SpecsAndCapabilitiesRepo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class SpecsAndCapabilitiesDefaultRepo
    @Inject
    constructor(
        private val specsAndCapabilitiesAPI: SpecsAndCapabilitiesAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), SpecsAndCapabilitiesRepo {
        override suspend fun fetchVehicleSpecs(vin: String): Resource<VehicleSpecsResponse?> {
            return makeApiCall {
                specsAndCapabilitiesAPI.fetchVehicleSpecs(vin)
            }
        }
    }
