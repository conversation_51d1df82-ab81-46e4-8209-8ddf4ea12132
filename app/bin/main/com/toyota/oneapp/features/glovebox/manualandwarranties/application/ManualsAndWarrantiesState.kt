package com.toyota.oneapp.features.glovebox.manualandwarranties.application

import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel.OwnerManual

sealed class ViewState {
    object Nothing : ViewState()

    object Init : ViewState()

    class LoadAllManualsAndWarranties(val uiMap: Map<String, List<OwnerManual>?>) : ViewState()

    object EmptyManualsAndWarranties : ViewState()
}

sealed class ApiState {
    object Nothing : ApiState()

    object ShowProgress : ApiState()

    class GetDocumentLinkSuccess(val docUrl: String) : ApiState()

    class Error() : ApiState()
}
