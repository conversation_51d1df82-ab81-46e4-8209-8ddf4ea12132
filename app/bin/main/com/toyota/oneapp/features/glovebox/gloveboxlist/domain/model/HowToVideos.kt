package com.toyota.oneapp.features.glovebox.gloveboxlist.domain.model

import android.os.Parcelable
import com.toyota.oneapp.features.glovebox.gloveboxlist.dataaccess.servermodel.Status
import kotlinx.parcelize.Parcelize

@Parcelize
data class HowToVideos(
    val status: Status?,
    val payload: ArrayList<HowToVideosPayload>?,
) : Parcelable

@Parcelize
data class HowToVideosPayload(
    val brand: String?,
    val createdDate: String?,
    val description: String?,
    val linkUrl: String?,
    val modelYear: List<String>?,
    val title: String?,
    val type: String?,
    val videoId: String?,
) : Parcelable
