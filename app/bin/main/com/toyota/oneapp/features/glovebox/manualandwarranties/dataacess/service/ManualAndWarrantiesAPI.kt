package com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.service

import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel.ManualAndWarrantiesResponse
import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel.ManualAndWarrantyDocumentUrlResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Path

interface ManualAndWarrantiesAPI {
    @GET("/oneapi/v2/manuals")
    suspend fun fetchManualAndWarrantiesHeading(
        @Header("VIN") vin: String,
        @Header("brand") brand: String,
        @Header("model") model: String,
        @Header("modelYear") modelYear: String,
    ): Response<ManualAndWarrantiesResponse?>

    @GET("/oneapi/v2/manual/pdf/{documentId}")
    suspend fun fetchManualAndWarrantyDocumentUrl(
        @Path("documentId") documnetId: String,
        @Header("brand") brand: String,
    ): Response<ManualAndWarrantyDocumentUrlResponse>
}
