package com.toyota.oneapp.features.glovebox.dashboardlights.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.glovebox.dashboardlights.application.DashboardLightsState
import com.toyota.oneapp.features.glovebox.dashboardlights.application.DashboardLightsUseCase
import com.toyota.oneapp.features.glovebox.dashboardlights.domain.model.DashboardLightUIModel
import com.toyota.oneapp.ui.BaseFragmentViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class DashboardLightsViewModel
    @Inject
    constructor(
        private val dashboardLightsUseCase: DashboardLightsUseCase,
        private val applicationData: ApplicationData,
        private val dispatcherProvider: DispatcherProvider,
    ) : BaseFragmentViewModel() {
        private val _dashboardLightsState =
            MutableStateFlow<DashboardLightsState>(
                value = DashboardLightsState.Nothing,
            )
        val dashboardLightsState = _dashboardLightsState.asStateFlow()

        private val allDashboardLights = ArrayList<DashboardLightUIModel>()

        init {
            loadAllDashboardLights()
        }

        private fun loadAllDashboardLights() {
            _dashboardLightsState.value = DashboardLightsState.Loading
            viewModelScope.launch(dispatcherProvider.main()) {
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    dashboardLightsUseCase.loadAllDashboardLightsTile(
                        vin = vehicle.vin,
                    )
                        .flowOn(dispatcherProvider.io())
                        .collect { uiModel ->
                            if (uiModel != null) {
                                allDashboardLights.addAll(uiModel.dashboardLights)
                                _dashboardLightsState.value =
                                    DashboardLightsState.Success(
                                        uiModel.dashboardLights,
                                    )
                            } else {
                                _dashboardLightsState.value = DashboardLightsState.Error()
                            }
                        }
                }
            }
        }

        fun filterDashboardLights(searchText: String) {
            if (searchText.isNotEmpty()) {
                val filteredList =
                    allDashboardLights.filter {
                        it.title.lowercase().contains(
                            searchText.lowercase(),
                        )
                    }
                _dashboardLightsState.value = DashboardLightsState.Success(filteredList)
            } else {
                _dashboardLightsState.value =
                    DashboardLightsState.Success(
                        allDashboardLights,
                    )
            }
        }
    }
