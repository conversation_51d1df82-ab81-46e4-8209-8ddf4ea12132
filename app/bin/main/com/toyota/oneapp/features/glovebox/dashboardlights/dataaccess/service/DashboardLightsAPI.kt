package com.toyota.oneapp.features.glovebox.dashboardlights.dataaccess.service

import com.toyota.oneapp.features.glovebox.dashboardlights.dataaccess.servermodel.DashboardLightsResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header

interface DashboardLightsAPI {
    @GET("/oneapi/v1/dashboardlights")
    suspend fun fetchDashboardLights(
        @Header("VIN") vin: String,
    ): Response<DashboardLightsResponse?>
}
