package com.toyota.oneapp.features.glovebox.gloveboxlist.dataaccess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.glovebox.gloveboxlist.domain.model.HowToVideos
import com.toyota.oneapp.features.glovebox.gloveboxlist.domain.model.HowToVideosPayload

data class HowToVideosResponse(
    @SerializedName("payload") val payload: List<Payload>?,
    @SerializedName("status") val status: Status?,
)

data class Payload(
    @SerializedName("brand") val brand: String?,
    @SerializedName("createdDate") val createdDate: String?,
    @SerializedName("description") val description: String?,
    @SerializedName("linkUrl") val linkUrl: String?,
    @SerializedName("modelYear") val modelYear: List<String>?,
    @SerializedName("modifiedDate") val modifiedDate: String?,
    @SerializedName("tags") val tags: List<String>?,
    @SerializedName("title") val title: String?,
    @SerializedName("type") val type: String?,
    @SerializedName("videoId") val videoId: String?,
)

fun HowToVideosResponse.toUIModel(): HowToVideos {
    val data = ArrayList<HowToVideosPayload>()
    payload?.forEach {
        data.add(
            HowToVideosPayload(
                brand = it.brand,
                createdDate = it.createdDate,
                description = it.description,
                linkUrl = it.linkUrl,
                modelYear = it.modelYear,
                title = it.title,
                type = it.type,
                videoId = it.videoId,
            ),
        )
    }
    return HowToVideos(status = status, payload = data)
}
