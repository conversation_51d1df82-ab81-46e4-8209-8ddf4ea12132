package com.toyota.oneapp.features.glovebox.gloveboxlist.dataaccess.service

import com.toyota.oneapp.features.glovebox.gloveboxlist.dataaccess.servermodel.HowToVideosResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header

interface GloveBoxApi {
    @GET("/oneapi/v1/videos")
    suspend fun fetchHowToVideos(
        @Header("vin") vin: String,
        @Header("X-APPBRAND") appBrand: String,
        @Header("X-GUID") guid: String?,
    ): Response<HowToVideosResponse?>
}
