package com.toyota.oneapp.features.glovebox.dashboardlights.application

import com.toyota.oneapp.features.glovebox.dashboardlights.dataaccess.servermodel.toUIModel
import com.toyota.oneapp.features.glovebox.dashboardlights.domain.model.DashboardLightsScreenUIModel
import com.toyota.oneapp.features.glovebox.dashboardlights.domain.repo.DashboardLightsRepo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class DashboardLightsLogic
    @Inject
    constructor(
        private val dashboardLightsRepo: DashboardLightsRepo,
    ) : DashboardLightsUseCase {
        override suspend fun loadAllDashboardLightsTile(vin: String): Flow<DashboardLightsScreenUIModel?> {
            return flow {
                val response = dashboardLightsRepo.fetchDashboardLights(vin)

                if (response is Resource.Success) {
                    emit(response.data?.toUIModel())
                } else {
                    emit(null)
                }
            }
        }
    }
