package com.toyota.oneapp.features.glovebox.specsandcapabilities.dataaccess.service

import com.toyota.oneapp.features.glovebox.specsandcapabilities.dataaccess.servermodel.VehicleSpecsResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header

interface SpecsAndCapabilitiesAPI {
    @GET("/oneapi/v1/vehicle/vehicle-spec")
    suspend fun fetchVehicleSpecs(
        @Header("VIN") vin: String,
    ): Response<VehicleSpecsResponse?>
}
