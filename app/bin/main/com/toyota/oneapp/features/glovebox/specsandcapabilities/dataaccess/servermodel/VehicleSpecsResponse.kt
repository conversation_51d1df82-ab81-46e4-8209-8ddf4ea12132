package com.toyota.oneapp.features.glovebox.specsandcapabilities.dataaccess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.glovebox.specsandcapabilities.domain.model.SpecsUIModel

data class VehicleSpecsResponse(
    @SerializedName("payload") val payload: VehicleSpecsPayload?,
)

data class VehicleSpecsPayload(
    @SerializedName("vehicleSpecifications") val vehicleSpecifications: SpecificationDetails?,
    @SerializedName("additionalDetails") val additionalDetails: SpecificationDetails?,
    @SerializedName("factoryInstalledEquipment") val factoryInstalledEquipment: SpecificationDetails?,
    @SerializedName("standardInstalledEquipment") val standardInstalledEquipment: SpecificationDetails?,
)

data class SpecificationDetails(
    @SerializedName("headerName") val headerName: String?,
    @SerializedName("dataItems") val dataItems: List<DataItem>?,
)

data class DataItem(
    @SerializedName("dataName") val dataName: String?,
    @SerializedName("dataValue") val dataValue: String?,
)

fun VehicleSpecsPayload.toUIModel(): Map<String, List<SpecsUIModel>> {
    val map = LinkedHashMap<String, List<SpecsUIModel>>()

    val vehicleSpecsList = ArrayList<SpecsUIModel>()
    vehicleSpecifications?.dataItems?.forEach { item ->
        vehicleSpecsList.add(
            SpecsUIModel(
                name = item.dataName.orEmpty(),
                value = item.dataValue.orEmpty(),
            ),
        )
    }

    val additionalDetailsList = ArrayList<SpecsUIModel>()
    additionalDetails?.dataItems?.forEach { item ->
        additionalDetailsList.add(
            SpecsUIModel(
                name = item.dataName.orEmpty(),
                value = item.dataValue.orEmpty(),
            ),
        )
    }

    val factoryInstalledList = ArrayList<SpecsUIModel>()
    factoryInstalledEquipment?.dataItems?.forEach { item ->
        factoryInstalledList.add(
            SpecsUIModel(
                name = item.dataName.orEmpty(),
                value = item.dataValue.orEmpty(),
            ),
        )
    }

    val standardInstalledList = ArrayList<SpecsUIModel>()
    standardInstalledEquipment?.dataItems?.forEach { item ->
        standardInstalledList.add(
            SpecsUIModel(
                name = item.dataName.orEmpty(),
                value = item.dataValue.orEmpty(),
            ),
        )
    }

    if (vehicleSpecsList.isNotEmpty()) {
        map.put(vehicleSpecifications?.headerName.orEmpty(), vehicleSpecsList)
    }

    if (additionalDetailsList.isNotEmpty()) {
        map.put(additionalDetails?.headerName.orEmpty(), additionalDetailsList)
    }

    if (factoryInstalledList.isNotEmpty()) {
        map.put(factoryInstalledEquipment?.headerName.orEmpty(), factoryInstalledList)
    }

    if (standardInstalledList.isNotEmpty()) {
        map.put(standardInstalledEquipment?.headerName.orEmpty(), standardInstalledList)
    }

    return map
}
