package com.toyota.oneapp.features.glovebox.manualandwarranties.application

import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.servermodel.OwnerManual
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface ManualAndWarrantiesUseCase {
    suspend fun fetchAllManualsAndWarranties(vehicleInfo: VehicleInfo): Flow<Map<String, List<OwnerManual>?>?>

    suspend fun fetchDocumentUrl(
        brand: String,
        documentId: String,
    ): Flow<String?>
}
