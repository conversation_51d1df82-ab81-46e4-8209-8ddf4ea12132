package com.toyota.oneapp.features.glovebox.gloveboxlist.domain.model

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes

sealed class GloveBoxSetting(
    @StringRes val title: Int,
    @DrawableRes val iconRes: Int,
    @StringRes val iconDescription: Int,
    val route: String?,
    val url: String?,
    val accessibilityId: String,
) {
    data class SpecsCapabilites(
        @StringRes val specCapabilitiesTitle: Int,
        @DrawableRes val specCapabilitiesIconRes: Int,
        @StringRes val specCapabilitiesIconDescription: Int,
        val specCapabilitiesRoute: String,
        val specAccessibilityId: String,
    ) : GloveBoxSetting(
            specCapabilitiesTitle,
            specCapabilitiesIconRes,
            specCapabilitiesIconDescription,
            specCapabilitiesRoute,
            null,
            specAccessibilityId,
        )

    data class ManualsAndWarranties(
        @StringRes val manualsAndWarrantiesTitle: Int,
        @DrawableRes val manualsAndWarrantiesIconRes: Int,
        @StringRes val manualsAndWarrantiesIconDescription: Int,
        val manualsAndWarrantiesRoute: String,
        val manualsAccessibilityId: String,
    ) : GloveBoxSetting(
            manualsAndWarrantiesTitle,
            manualsAndWarrantiesIconRes,
            manualsAndWarrantiesIconDescription,
            manualsAndWarrantiesRoute,
            null,
            manualsAccessibilityId,
        )

    data class DashboardLights(
        @StringRes val dashboardLightsTitle: Int,
        @DrawableRes val dashboardLightsIconRes: Int,
        @StringRes val dashboardLightsIconDescription: Int,
        val dashboardLightsRoute: String,
        val dashboardLightsAccessibilityId: String,
    ) : GloveBoxSetting(
            dashboardLightsTitle,
            dashboardLightsIconRes,
            dashboardLightsIconDescription,
            dashboardLightsRoute,
            null,
            dashboardLightsAccessibilityId,
        )

    data class ToyotaForFamilies(
        @StringRes val ttfTitle: Int,
        @DrawableRes val ttfIconRes: Int,
        @StringRes val ttfIconDescription: Int,
        val ttfUrl: String,
        val ttfAccessibilityId: String,
    ) : GloveBoxSetting(ttfTitle, ttfIconRes, ttfIconDescription, null, ttfUrl, ttfAccessibilityId)

    data class IsoDynamicSeats(
        @StringRes val proSeatTitle: Int,
        @DrawableRes val proSeatIconRes: Int,
        @StringRes val proSeatIconDescription: Int,
        val proSeatUrl: String,
        val proSeatAccessibilityId: String,
    ) : GloveBoxSetting(
            proSeatTitle,
            proSeatIconRes,
            proSeatIconDescription,
            null,
            proSeatUrl,
            proSeatAccessibilityId,
        )

    data class HowToVideosSettings(
        @StringRes val howToVideoTitle: Int,
        @DrawableRes val howToVideoIconRes: Int,
        @StringRes val howToVideoIconDescription: Int,
        val howToVideos: HowToVideos?,
        val howToVideoAccessibilityId: String,
    ) : GloveBoxSetting(
            howToVideoTitle,
            howToVideoIconRes,
            howToVideoIconDescription,
            null,
            null,
            howToVideoAccessibilityId,
        )
}
