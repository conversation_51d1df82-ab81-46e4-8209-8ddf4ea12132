package com.toyota.oneapp.features.glovebox.gloveboxlist.dataaccess.repository

import com.toyota.oneapp.features.glovebox.gloveboxlist.dataaccess.servermodel.HowToVideosResponse
import com.toyota.oneapp.features.glovebox.gloveboxlist.dataaccess.service.GloveBoxApi
import com.toyota.oneapp.features.glovebox.gloveboxlist.domain.repo.HowToVideosRepo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class HowToVideosDefaultRepo
    @Inject
    constructor(
        val service: GloveBoxApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), HowToVideosRepo {
        override suspend fun fetchHowToVideos(
            vin: String,
            appBrand: String,
            guid: String,
        ): Resource<HowToVideosResponse?> {
            return makeApiCall {
                service.fetchHowToVideos(
                    vin = vin,
                    appBrand = appBrand,
                    guid = guid,
                )
            }
        }
    }
