/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeassist.dataaccess.repository

import com.toyota.oneapp.features.chargeassist.dataaccess.model.addressupdateofficial.request.UpdateAddressOfficialRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.addressupdateofficial.response.UpdateAddressOfficialResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollment.request.EnrollmentRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollment.response.EnrollmentResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollmentcheck.EnrollmentCheckResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.processstatus.request.ProcessStatusRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.processstatus.response.ProcessStatusResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.programavailability.ProgramEligibilityModelPayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.toggle.request.ToggleRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.toggle.response.ToggleResponseModelPayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.unenrollment.request.UnenrollmentRequestBodyModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.unenrollment.response.UnenrollmentResponseModel
import com.toyota.oneapp.features.chargeassist.dataaccess.service.ChargeAssistApi
import com.toyota.oneapp.features.chargeassist.domain.repository.ChargeAssistRepository
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.ApiResponse
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class ChargeAssistDefaultRepo
    @Inject
    constructor(
        private val chargeAssistApi: ChargeAssistApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser = errorParser, ioContext = ioContext), ChargeAssistRepository {
        override suspend fun getChargeAssistEligibilityBasedOnZip(
            zipCode: String?,
        ): Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?> {
            return makeApiCall {
                chargeAssistApi.getChargeAssistEligibilityBasedOnZip(zipCode = zipCode)
            }
        }

        // Enrollment Status
        override suspend fun getEnrollmentStatus(
            email: String,
        ): Resource<
            ApiResponse<EnrollmentCheckResponsePayload?>?,
            > {
            return makeApiCall {
                chargeAssistApi.getEnrollmentStatus(
                    email = email,
                )
            }
        }

        override suspend fun enrollAUserIntoCA(
            vin: String,
            body: EnrollmentRequestModel?,
        ): Resource<ApiResponse<EnrollmentResponsePayload?>?> {
            return makeApiCall {
                chargeAssistApi.enrollAUserIntoCA(
                    vin = vin,
                    body = body,
                )
            }
        }

        override suspend fun processStatus(
            vin: String,
            body: ProcessStatusRequestModel?,
        ): Resource<ApiResponse<ProcessStatusResponsePayload?>?> {
            return makeApiCall {
                chargeAssistApi.processStatus(
                    vin = vin,
                    body = body,
                )
            }
        }

        override suspend fun updateToggleStatus(
            vin: String,
            body: ToggleRequestModel?,
        ): Resource<ApiResponse<ToggleResponseModelPayload?>?> {
            return makeApiCall {
                chargeAssistApi.setToggle(
                    vin = vin,
                    body = body,
                )
            }
        }

        override suspend fun unEnrollAUserFromCA(
            vin: String,
            body: UnenrollmentRequestBodyModel?,
        ): Resource<ApiResponse<UnenrollmentResponseModel>?> {
            return makeApiCall {
                chargeAssistApi.unEnrollAUserFromCA(
                    vin = vin,
                    body = body,
                )
            }
        }

        override suspend fun updateAddressOfficial(
            brand: String?,
            body: UpdateAddressOfficialRequestModel?,
        ): Resource<ApiResponse<UpdateAddressOfficialResponsePayload?>?> {
            return makeApiCall {
                chargeAssistApi.updateAddressOfficial(
                    brand = brand,
                    body = body,
                )
            }
        }
    }
