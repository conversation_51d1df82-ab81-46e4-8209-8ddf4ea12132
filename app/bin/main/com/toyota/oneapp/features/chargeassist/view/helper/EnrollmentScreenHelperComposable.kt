package com.toyota.oneapp.features.chargeassist.view.helper

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.features.chargeassist.ChargeAssistRoute
import com.toyota.oneapp.features.chargeassist.dataaccess.model.programavailability.ProgramEligibilityModelPayload
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistBottomSheetLogo
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLED
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_PENDING
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_SUCCESSFUL_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.UNENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ProgramNameAndDescriptionOrStatus
import com.toyota.oneapp.features.chargeassist.view.screens.ProgramSmartChargeLogo
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.core.composable.OABody1TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OACaption2TextView
import com.toyota.oneapp.features.core.composable.OAClickableButtonTextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.ApiResponse
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@Composable
fun ConditionalEnrollScreenButtons(
    enrollmentStatus: String,
    navController: NavController,
    unEnrollFromCABottomSheetState: ModalBottomSheetState,
    coroutineScope: CoroutineScope,
) {
    when (enrollmentStatus) {
        in ENROLLMENT_PENDING_STATUSES -> {
            // Pending
            PrimaryButton02(
                text = stringResource(id = R.string.Common_dismiss),
                click = { navController.popBackStack() },
                modifier = Modifier.padding(top = 84.dp),
            )
        }

        in ENROLLMENT_SUCCESSFUL_STATUSES, in UNENROLLMENT_PENDING_STATUSES -> {
            val isUnEnrollmentPending = enrollmentStatus in UNENROLLMENT_PENDING_STATUSES

            // Fully Enrolled
            OAClickableButtonTextView(
                text = stringResource(id = R.string.unenroll_text),
                color = if (isUnEnrollmentPending) AppTheme.colors.outline01 else AppTheme.colors.button02a,
                modifier = Modifier.padding(16.dp),
                onClick = {
                    if (!isUnEnrollmentPending) coroutineScope.launch { unEnrollFromCABottomSheetState.show() }
                },
            )

            PrimaryButton02(text = stringResource(id = R.string.Common_dismiss), click = {
                navController.popBackStack()
            }, modifier = Modifier)
        }
    }
}

@Composable
fun UnEnrollmentConfirmationBottomSheet(
    navController: NavController,
    bottomSheetState: ModalBottomSheetState,
    chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel(),
) {
    val coroutineScope = rememberCoroutineScope()
    ModalBottomSheetLayout(
        sheetBackgroundColor = AppTheme.colors.tertiary15,
        sheetShape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        sheetState = bottomSheetState,
        sheetContent = {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(486.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // Handle
                Box(
                    modifier =
                        Modifier
                            .padding(top = 16.dp)
                            .width(40.dp)
                            .height(4.dp)
                            .clip(RoundedCornerShape(2.dp))
                            .background(AppTheme.colors.tertiary10),
                )

                ChargeAssistBottomSheetLogo(modifier = Modifier.padding(top = 50.dp))

                // Title
                OASubHeadLine1TextView(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 32.dp)
                            .padding(top = 31.dp),
                    text = stringResource(R.string.are_you_sure_of_unenroll),
                    color = AppTheme.colors.tertiary03,
                    textAlign = TextAlign.Center,
                )

                // Description
                OACallOut1TextView(
                    text = stringResource(R.string.unenrollment_bottomsheet_message),
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 32.dp)
                            .padding(top = 8.dp),
                    color = AppTheme.colors.tertiary05,
                    textAlign = TextAlign.Center,
                )

                // Confirm Button
                PrimaryButton02(
                    text = "Confirm",
                    modifier =
                        Modifier
                            .padding(16.dp)
                            .padding(top = 70.dp)
                            .height(52.dp)
                            .wrapContentSize(),
                    click = {
                        coroutineScope.launch { bottomSheetState.hide() }
                        navController.navigate(OAScreen.ChargeInfo.route)
                        chargeAssistViewModel.unEnrollAUser()
                        // reset enrollment Failure visit count in case user wishes attempts enrollment again
                        chargeAssistViewModel.resetCounter()
                        chargeAssistViewModel.chargeAssistVMHelper.resetAllToggle()
                        // reset the backend toggle flag
                    },
                )
            }
        },
    ) {}
}

@Composable
fun BottomDescriptionCard(textDescription: String) {
    MergedBottomDescriptionCard(
        text = textDescription,
        contentDescription = stringResource(R.string.toggle_description_card),
        useFixedHeight = false,
    )
}

// merged bottom desc card

@Composable
fun MergedBottomDescriptionCard(
    text: String,
    contentDescription: String,
    useFixedHeight: Boolean = false,
) {
    Card(
        elevation = 0.dp,
        shape = RoundedCornerShape(size = 8.dp),
        modifier =
            Modifier
                .semantics { this.contentDescription = contentDescription }
                .wrapContentSize()
                .padding(16.dp),
    ) {
        Box(
            modifier =
                Modifier.background(AppTheme.colors.tile05),
        ) {
            Column(
                modifier =
                    Modifier
                        .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Box(
                    modifier = Modifier,
                ) {
                    Column(
                        modifier =
                            Modifier
                                .padding(start = 4.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        OABody1TextView(
                            textAlign = TextAlign.Center,
                            text = text,
                            color = AppTheme.colors.tertiary05,
                            modifier =
                                Modifier
                                    .semantics { this.contentDescription = contentDescription }
                                    .fillMaxWidth()
                                    .then(if (useFixedHeight) Modifier.height(46.dp) else Modifier),
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun UtilityProviderLinks(
    utilityProviderAcronym: String,
    utilityProviderLink: String?,
    weaveGridString: String,
    weaveGridLink: String?,
) {
    Row(
        modifier = Modifier.padding(bottom = 16.dp),
    ) {
        Text(
            text = "$utilityProviderAcronym ",
            color = AppTheme.colors.tertiary03,
            style = AppTheme.fontStyles.callout1,
        )
        HyperlinkText(utilityProviderLink, stringResource(R.string.terms_conditions), isUnderlined = true)
    }

    Row {
        Text(
            text = "$weaveGridString ",
            color = AppTheme.colors.tertiary03,
            style = AppTheme.fontStyles.callout1,
            modifier = Modifier.padding(bottom = 27.dp),
        )
        HyperlinkText(weaveGridLink, stringResource(R.string.terms_conditions), isUnderlined = true)
    }
}

// Bottom Description Card

@Composable
fun EnrollScreenBottomDescription(
    enrollmentStatus: String,
    chargeAssistViewModel: ChargeAssistViewModel,
    navController: NavController,
    unEnrollFromCABottomSheetState: ModalBottomSheetState,
    coroutineScope: CoroutineScope,
) {
    val state = chargeAssistViewModel.zipEligibilityStatusData.collectAsState()
    val payloadModel = state.value?.data?.payload?.firstOrNull()
    val aggregatorSupportEmail = payloadModel?.aggregatorSupportEmail
    val aggregatorWebsite = payloadModel?.aggregatorTAndC
    val aggregatorName = payloadModel?.aggregatorName
    val utilityProviderURL = payloadModel?.utilityProviderTAndC
    val utilityProviderAcronym = payloadModel?.utilityProviderAcronym
    val contactDescriptionText = stringResource(R.string.questions_email_weavegrid_at, aggregatorSupportEmail.orEmpty())

    Column(
        modifier =
            Modifier
                .padding(start = 16.dp, end = 16.dp, bottom = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        BottomDescriptionCard(contactDescriptionText)
        UtilityProviderLinks(
            utilityProviderAcronym = utilityProviderAcronym.orEmpty(),
            utilityProviderLink = utilityProviderURL.orEmpty(),
            weaveGridString = aggregatorName.orEmpty(),
            weaveGridLink = aggregatorWebsite.orEmpty(),
        )

        ConditionalEnrollScreenButtons(
            enrollmentStatus,
            navController,
            unEnrollFromCABottomSheetState,
            coroutineScope,
        )
    }
}

private const val EMPTY_STRING = ""

@Composable
fun MainCard(
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
    zipFromZipNotAvailableScreen: String?,
    currentEnrollmentStatus: String?,
    collectData: Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?,
) {
    val payloadModel = collectData?.data?.payload?.firstOrNull()
    val programName = payloadModel?.programName
    val programDescription = payloadModel?.shortDescription

    Card(
        contentColor = AppTheme.colors.tile01,
        modifier =
            Modifier
                .padding(vertical = 24.dp)
                .fillMaxWidth()
                .padding(16.dp)
                .background(
                    AppTheme.colors.tile01,
                    RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp),
                ),
    ) {
        Column(modifier = Modifier.background(AppTheme.colors.tile01)) {
            Row(
                modifier =
                    Modifier
                        .background(AppTheme.colors.tile01),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                ProgramSmartChargeLogo(collectData)
                Column(modifier = Modifier.padding(start = 8.dp)) {
                    ProgramNameAndDescriptionOrStatus(
                        name = programName,
                        description =
                            when (currentEnrollmentStatus) {
                                in ENROLLMENT_PENDING_STATUSES -> ENROLLMENT_PENDING
                                in ENROLLMENT_SUCCESSFUL_STATUSES -> ENROLLED
                                in UNENROLLMENT_PENDING_STATUSES -> EMPTY_STRING
                                else -> programDescription
                            },
                    )
                }
            }
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.Start),
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .background(AppTheme.colors.tile05)
                        .height(56.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                OACaption2TextView(
                    text = "Learn More",
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .wrapContentWidth()
                            .padding(start = 16.dp),
                    textAlign = TextAlign.Left,
                )
                Spacer(modifier = Modifier.weight(1f))
                IconButton(
                    onClick = {
                        navController.navigate(
                            ChargeAssistRoute.EnrollmentUtilityLearnMoreScreen.route +
                                "/$zipFromZipNotAvailableScreen",
                        )
                        chargeAssistViewModel.chargeAssistVMHelper.logAnalyticsEvent(
                            AnalyticsEvent.CHARGE_ASSIST_ENROLLMENT_PAGE_LEARN_MORE,
                        )
                    },
                    modifier = Modifier.padding(end = 8.dp),
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.KeyboardArrowRight,
                        contentDescription = "learn more arrow button",
                        tint = AppTheme.colors.tertiary00,
                    )
                }
            }
        }
    }
}
