/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeassist

import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import com.toyota.oneapp.features.chargeassist.ChargeAssistRoute.Companion.ZIP_CODE
import com.toyota.oneapp.features.chargeassist.view.screens.ApplicationSubmittedScreen
import com.toyota.oneapp.features.chargeassist.view.screens.EnrollUtilityProgramScreen
import com.toyota.oneapp.features.chargeassist.view.screens.EnrollmentUtilityLearnMoreScreen
import com.toyota.oneapp.features.chargeassist.view.screens.manualschedule.ManualScheduleScreen
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleViewModel
import com.toyota.oneapp.features.core.navigation.sharedViewModel
import com.toyota.oneapp.features.dashboard.dashboard.presentation.DashboardViewModel

fun NavGraphBuilder.addRemainingComposables(navController: NavHostController) {
    composable(
        route =
            ChargeAssistRoute.EnrollmentUtilityProgramScreen.route +
                "/{zip}/{enableEnrollmentFields}/{currentEnrollmentStatus}",
        arguments = listOf(NavArgs.zip, NavArgs.enableEnrollmentFields, NavArgs.currentEnrollmentStatus),
    ) { entry ->
        val zipFromZipNotAvailableScreen = entry.arguments?.getString(ZIP_CODE).orEmpty()
        val enableEnrollmentFields = entry.arguments?.getBoolean("enableEnrollmentFields") ?: false
        val currentEnrollmentStatus = entry.arguments?.getString("currentEnrollmentStatus") ?: false
        val chargeAssistViewModel =
            entry.sharedViewModel<ChargeAssistViewModel>(
                getBackStackEntry = { path ->
                    navController.getBackStackEntry(path)
                },
            )
        // User Account details should populate text fields in this page (Api call)
        val dashboardViewModel: DashboardViewModel = hiltViewModel()
        EnrollUtilityProgramScreen(
            navController,
            dashboardViewModel,
            chargeAssistViewModel,
            zipFromZipNotAvailableScreen,
            enableEnrollmentFields,
            currentEnrollmentStatus.toString(),
        )
    }

    composable(
        route = ChargeAssistRoute.EnrollmentUtilityLearnMoreScreen.route + "/{zip}",
        arguments = listOf(NavArgs.zip),
    ) { entry ->
        val zipFromZipNotAvailableScreen = entry.arguments?.getString(ZIP_CODE)
        val chargeAssistViewModel =
            entry.sharedViewModel<ChargeAssistViewModel>(
                getBackStackEntry = { path ->
                    navController.getBackStackEntry(path)
                },
            )
        EnrollmentUtilityLearnMoreScreen(navController, chargeAssistViewModel, zipFromZipNotAvailableScreen)
    }

    composable(
        route = ChargeAssistRoute.ApplicationSubmittedScreen.route,
        arguments = listOf(NavArgs.zip),
    ) { entry ->
        val chargeAssistViewModel =
            entry.sharedViewModel<ChargeAssistViewModel>(
                getBackStackEntry = { path ->
                    navController.getBackStackEntry(path)
                },
            )
        ApplicationSubmittedScreen(navController, chargeAssistViewModel = chargeAssistViewModel)
    }
    composable(
        route = ChargeAssistRoute.ManualScheduleScreen.route + "/{isClickable}/{isEnabled}/{isChargeAssistActive}",
        arguments =
            listOf(
                NavArgs.isClickable,
                NavArgs.isEnabled,
                NavArgs.isChargeAssistActive,
            ),
    ) { entry ->

        val chargeAssistViewModel =
            entry.sharedViewModel<ChargeAssistViewModel> { path ->
                navController.getBackStackEntry(path)
            }

        val chargeScheduleViewModel =
            entry.sharedViewModel<ChargeScheduleViewModel>(
                getBackStackEntry = { path ->
                    navController.getBackStackEntry(path)
                },
            )
        ManualScheduleScreen(
            navController,
            viewModel = chargeScheduleViewModel,
            chargeAssistViewModel = chargeAssistViewModel,
        )
    }
}
