package com.toyota.oneapp.features.chargeassist.view.screens.entrypoint1

import android.content.Context
import android.util.Log
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.Text
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import androidx.navigation.compose.currentBackStackEntryAsState
import apptentive.com.android.util.InternalUseOnly
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.features.chargeassist.ChargeAssistRoute
import com.toyota.oneapp.features.chargeassist.view.helper.ColdAppLaunchCounter
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.NOT_FOUND
import com.toyota.oneapp.features.chargeassist.view.screens.entrypoint2.getFinalStatus
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoResponse
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.OAThemeProvider
import com.toyota.oneapp.features.dashboard.dashboard.presentation.DashboardViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@Composable
fun ChargeAssistAnnouncementBottomSheet(
    navHostController: NavHostController,
    dashboardViewModel: DashboardViewModel,
    launchCounter: ColdAppLaunchCounter,
) {
    val scope = rememberCoroutineScope()
    val bottomSheetState =
        rememberModalBottomSheetState(
            skipHalfExpanded = true,
            initialValue = ModalBottomSheetValue.Hidden,
        )
    // Only show Announcement when user is on Dashboard Screen
    val currentRoute = navHostController.currentBackStackEntryAsState().value?.destination?.route
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()
    val profileData by dashboardViewModel.profileData.collectAsState()
    if (currentRoute != OAScreen.Home.route) return

    LaunchedEffect(Unit) {
        dashboardViewModel.fetchProfileData()
        chargeAssistViewModel.fetchEnrollmentStatus()
        chargeAssistViewModel.fetchZipEligibilityStatus()
    }

    if (profileData != null) {
        handlePopupAndNavLogic(
            bottomSheetState = bottomSheetState,
            scope = scope,
            profileData = profileData,
            navHostController,
            launchCounter = launchCounter,
            chargeAssistViewModel = chargeAssistViewModel,
        )
    }
}

@Composable
fun handlePopupAndNavLogic(
    bottomSheetState: ModalBottomSheetState,
    scope: CoroutineScope,
    profileData: ProfileInfoResponse?,
    navHostController: NavHostController,
    launchCounter: ColdAppLaunchCounter,
    chargeAssistViewModel: ChargeAssistViewModel,
) {
    val context = LocalContext.current

    // initialize values for announcement surfacing
    var showAnnouncement by remember { mutableStateOf(false) }

    // Ensure the bottom sheet shows whenever user navigates to dashboard screen
    LaunchedEffect(Unit) {
        // if cold app launch count is less than 2  and bottom sheet hasn't shown
        if ((launchCounter.getLaunchCount() < 2) && !launchCounter.hasShownAnnouncement()) {
            showAnnouncement = true
            launchCounter.incrementLaunchCount() // Increment the launch count once
            // launchCounter.setAnnouncementShown() // Mark the announcement as shown
        } else {
            showAnnouncement = false
        }
    }

    LaunchedEffect(bottomSheetState.currentValue) {
        when (bottomSheetState.currentValue) {
            // handle swipe hiding of announcement
            ModalBottomSheetValue.Hidden -> {
                bottomSheetState.hide()
            }

            ModalBottomSheetValue.Expanded -> { // left intentionally blank
            }

            ModalBottomSheetValue.HalfExpanded -> { // left intentionally blank
            }
        }
    }

    showBottomSheetBasedOnZipVehicleModelAndGeneration(
        bottomSheetState = bottomSheetState,
        chargeAssistViewModel = chargeAssistViewModel,
        launchCounter = launchCounter,
        showAnnouncement = showAnnouncement,
    )

    // set showAnnouncement to false when navigating away (Disposable Effect)
    DisposableEffect(Unit) {
        onDispose {
            showAnnouncement = false
        }
    }

    // Handle back press to close the bottom sheet
    BackHandler(enabled = bottomSheetState.isVisible) {
        scope.launch {
            bottomSheetState.hide()
            showAnnouncement = false // Reset to prevent reappearance
        }
    }

    announcementBottomSheetContent(bottomSheetState, scope, navHostController, profileData, context)
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun announcementBottomSheetContent(
    bottomSheetState: ModalBottomSheetState,
    scope: CoroutineScope,
    navHostController: NavHostController,
    profileData: ProfileInfoResponse?,
    context: Context,
) {
    OAThemeProvider(isDarkMode = AppTheme.darkMode.collectAsState().value) {
        ModalBottomSheetLayout(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(),
            // Ensures the bottom sheet can expand to 90% of the screen
            sheetElevation = 17.dp,
            sheetShape = RoundedCornerShape(topStart = 25.dp, topEnd = 25.dp),
            sheetState = bottomSheetState,
            sheetContent = {
                // Content for the bottom sheet
                Column(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(490.dp)
                            .background(AppTheme.colors.tertiary15),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    // Bottom Sheet Handle
                    Box(
                        modifier =
                            Modifier
                                .semantics {
                                    contentDescription = "Charge Assist Announcement handle"
                                }
                                .padding(16.dp)
                                .size(width = 40.dp, height = 4.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(AppTheme.colors.tertiary10),
                    )

                    // Announcements Text
                    OASubHeadLine3TextView(
                        text = stringResource(R.string.announcements),
                        color = AppTheme.colors.tertiary03,
                        modifier =
                            Modifier
                                .padding(horizontal = 16.dp)
                                .padding(bottom = 20.dp)
                                .semantics {
                                    contentDescription = context.getString(R.string.announcements)
                                },
                    )

                    // Image overlay on bottom sheet
                    val zipCode =
                        profileData
                            ?.payload
                            ?.customer
                            ?.addresses
                            ?.firstOrNull()
                            ?.zipCode
                    ImageWithOverlay(scope, bottomSheetState, navHostController, zipCode, context)
                }
            },
        ) {}
    }
}

@OptIn(InternalUseOnly::class)
@Composable
fun showBottomSheetBasedOnZipVehicleModelAndGeneration(
    bottomSheetState: ModalBottomSheetState,
    chargeAssistViewModel: ChargeAssistViewModel,
    launchCounter: ColdAppLaunchCounter,
    showAnnouncement: Boolean,
) {
    val currentEnrollmentStatusCollect by chargeAssistViewModel.enrollmentStatusData.collectAsState()
    val zipEligibilityStatusData by chargeAssistViewModel.zipEligibilityStatusData.collectAsState()
    val currentEnrollmentStatus = getFinalStatus(currentEnrollmentStatusCollect)

    // Check if zip is eligible only
    val isProfileZipEligible =
        zipEligibilityStatusData?.data?.payload?.isNotNullOrEmpty() == true &&
            currentEnrollmentStatus == NOT_FOUND

    LaunchedEffect(
        isProfileZipEligible,
        showAnnouncement,
        launchCounter.hasShownAnnouncement(),
    ) {
        // 20774, 20763 - Valid Zip
        // 30344 - Invalid Zip
        if (isProfileZipEligible &&
            showAnnouncement &&
            !launchCounter.hasShownAnnouncement()
        ) {
            bottomSheetState.show()
            Log.d("=== BottomSheetState", bottomSheetState.currentValue.toString())
            launchCounter.setAnnouncementShown()
        }
    }
}

@Composable
fun ImageWithOverlay(
    scope: CoroutineScope,
    bottomSheetState: ModalBottomSheetState,
    navController: NavController,
    zipCode: String?,
    context: Context,
) {
    Box(
        modifier =
            Modifier
                .wrapContentWidth()
                .padding(horizontal = 16.dp)
                .height(393.dp),
    ) {
        Image(
            painter = painterResource(id = R.drawable.charge_assist_announcement_img),
            contentDescription = stringResource(R.string.background_image_desc),
            modifier =
                Modifier
                    .padding(bottom = 25.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .fillMaxSize(),
            // Image fills the entire box
            contentScale = ContentScale.Crop,
        )

        Column(
            modifier =
                Modifier
                    .fillMaxSize(),
            // The column takes up the entire box size
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                modifier =
                    Modifier
                        .padding(top = 20.dp, bottom = 15.dp)
                        .semantics {
                            contentDescription =
                                context.getString(R.string.charge_assist_title_content_description)
                        },
                text = stringResource(R.string.charge_assist),
                color = Color.White,
                style = TextStyle(fontWeight = FontWeight.Bold),
            )
            Text(
                modifier =
                    Modifier
                        .padding(top = 5.dp)
                        .semantics {
                            contentDescription =
                                context.getString(R.string.enroll_to_personalize_your_home_charging)
                        },
                text =
                    stringResource(
                        R.string.enroll_to_personalize_your_home_charging_img_overlay_text,
                    ),
                textAlign = TextAlign.Center,
                color = Color.White,
                style =
                    TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight.SemiBold,
                    ),
            )
            Spacer(modifier = Modifier.height(16.dp))
            LearnMoreButton(
                scope,
                bottomSheetState,
                navController = navController,
                zipCode = zipCode,
            )
        }
    }
}

@Composable
fun LearnMoreButton(
    scope: CoroutineScope,
    bottomSheetState: ModalBottomSheetState,
    navController: NavController,
    zipCode: String?,
) {
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()
    PrimaryButton02(
        text = stringResource(R.string.learn_more_button_text),
        modifier =
            Modifier
                .semantics {
                    contentDescription = ""
                }
                .padding(bottom = 1.dp, top = 170.dp),
        click = {
            chargeAssistViewModel.chargeAssistVMHelper
                .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_LEARN_MORE_ANNOUNCEMENT_BUTTON)
            navController.navigate(ChargeAssistRoute.LearnMoreScreen.route + "/$zipCode/false")
            scope.launch {
                bottomSheetState.hide()
            }
        },
    )
}

@Preview(showBackground = true)
@Composable
fun ChargeAssistAnnouncementBottomSheet() {
    // Will attempt to create preview later
}
