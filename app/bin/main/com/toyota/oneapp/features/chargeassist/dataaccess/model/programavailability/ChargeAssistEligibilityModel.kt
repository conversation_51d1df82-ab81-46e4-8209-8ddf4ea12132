/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeassist.dataaccess.model.programavailability

import com.google.gson.annotations.SerializedName

data class ChargeAssistEligibilityModel(
    @SerializedName("message")
    val message: String? = "",
    @SerializedName("programEligibilityPayload")
    val programEligibilityPayload: List<ProgramEligibilityModelPayload?>? = listOf(),
)
