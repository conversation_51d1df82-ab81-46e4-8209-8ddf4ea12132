/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeassist.domain.repository

import com.toyota.oneapp.features.chargeassist.dataaccess.model.addressupdateofficial.request.UpdateAddressOfficialRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.addressupdateofficial.response.UpdateAddressOfficialResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollment.request.EnrollmentRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollment.response.EnrollmentResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollmentcheck.EnrollmentCheckResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.processstatus.request.ProcessStatusRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.processstatus.response.ProcessStatusResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.programavailability.ProgramEligibilityModelPayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.toggle.request.ToggleRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.toggle.response.ToggleResponseModelPayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.unenrollment.request.UnenrollmentRequestBodyModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.unenrollment.response.UnenrollmentResponseModel
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.ApiResponse

interface ChargeAssistRepository {
    suspend fun getChargeAssistEligibilityBasedOnZip(
        zipCode: String?,
    ): Resource<
        ApiResponse<List<ProgramEligibilityModelPayload?>?>?,
        >

    suspend fun getEnrollmentStatus(email: String): Resource<ApiResponse<EnrollmentCheckResponsePayload?>?>?

    suspend fun enrollAUserIntoCA(
        vin: String,
        body: EnrollmentRequestModel?,
    ): Resource<ApiResponse<EnrollmentResponsePayload?>?>

    suspend fun processStatus(
        vin: String,
        body: ProcessStatusRequestModel?,
    ): Resource<ApiResponse<ProcessStatusResponsePayload?>?>

    suspend fun updateToggleStatus(
        vin: String,
        body: ToggleRequestModel?,
    ): Resource<ApiResponse<ToggleResponseModelPayload?>?>?

    suspend fun unEnrollAUserFromCA(
        vin: String,
        body: UnenrollmentRequestBodyModel?,
    ): Resource<ApiResponse<UnenrollmentResponseModel>?>?

    suspend fun updateAddressOfficial(
        brand: String?,
        body: UpdateAddressOfficialRequestModel?,
    ): Resource<ApiResponse<UpdateAddressOfficialResponsePayload?>?>?
}
