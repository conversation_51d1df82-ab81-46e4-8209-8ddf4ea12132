package com.toyota.oneapp.features.chargeassist.view.screens

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.toyota.oneapp.features.chargeassist.view.helper.FullLearnMoreScreen
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel

@Composable
fun LearnMoreScreen(
    navController: NavController,
    zipCode: String?,
    showConsent: Boolean,
    chargeAssistViewModel: ChargeAssistViewModel,
) {
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .fillMaxHeight(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        FullLearnMoreScreen(navController, zipCode, showConsent, chargeAssistViewModel)
        BackHandler {
            // Navigate back to the previous screen
            navController.popBackStack()
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewLearnMoreScreen() {
    // Mock NavController (it won't perform actual navigation in the preview)
    val navController = rememberNavController()
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()

    // Call the LearnMoreScreen composable with mock data
    LearnMoreScreen(
        navController = navController,
        zipCode = "30344",
        showConsent = false,
        chargeAssistViewModel,
    )
}
