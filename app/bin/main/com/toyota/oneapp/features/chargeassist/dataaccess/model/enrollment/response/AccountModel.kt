package com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollment.response

import com.google.gson.annotations.SerializedName

data class AccountModel(
    @SerializedName("created_at")
    val createdAt: String? = "",
    @SerializedName("enrollment_date")
    val enrollmentDate: String? = "",
    @SerializedName("guid")
    val guid: String? = "",
    @SerializedName("vin_registrations")
    val vinRegistrations: List<Any?>? = listOf(),
)
