package com.toyota.oneapp.features.chargeassist.view.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeassist.ChargeAssistRoute
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistToggleSwitches.Companion.MANUAL_SCHEDULE_SWITCH
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.core.composable.CustomSwitch
import com.toyota.oneapp.features.core.composable.OAButtonTextView
import com.toyota.oneapp.features.core.composable.OAFootNote1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun ManualScheduleWidget(
    navHostController: NavHostController,
    modifier: Modifier = Modifier,
    manualScheduleCardParamsForCAToggle: ManualScheduleCardParamsForCAToggle? = null,
    showEntryPointSection: Boolean,
    chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel(),
    isChargeAssistActive: Boolean,
) {
    Card(
        backgroundColor = AppTheme.colors.tile01,
        shape = RoundedCornerShape(8.dp),
        elevation = 8.dp,
        modifier = modifier,
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(),
        ) {
            Row(
                modifier =
                    modifier
                        .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                ScheduleImage(modifier = Modifier.padding(start = 16.dp))

                Column(
                    modifier =
                        Modifier
                            .wrapContentWidth()
                            .padding(top = 16.dp, bottom = 16.dp)
                            .padding(start = 8.dp),
                ) {
                    OASubHeadLine1TextView(
                        text = stringResource(R.string.manual_scheduling),
                        color = AppTheme.colors.tertiary03,
                    )
                    OAFootNote1TextView(
                        modifier =
                            Modifier
                                .height(36.dp)
                                .widthIn(max = 188.dp),
                        text = stringResource(R.string.customize_your_own_charge_schedule_as_needed),
                        color = AppTheme.colors.tertiary05,
                        textAlign = TextAlign.Left,
                    )
                }

                Spacer(modifier = Modifier.weight(1f))
                CustomSwitch(
                    isEnabled = manualScheduleCardParamsForCAToggle?.isEnabled == true,
                    isClickable = manualScheduleCardParamsForCAToggle?.isClickable == true,
                    onCheckedChange = {
                        manualScheduleCardParamsForCAToggle?.onToggle?.invoke()
                    },
                    modifier = Modifier.padding(end = 16.dp),
                    shouldEnableCADisableColor = true,
                    testTagId = "",
                )
            }

            if (showEntryPointSection) {
                Box {
                    Row {
                        ManageScheduleEntryPoint(
                            navHostController,
                            chargeAssistViewModel.chargeAssistVMHelper.activeSwitch.value == MANUAL_SCHEDULE_SWITCH,
                            manualScheduleCardParamsForCAToggle?.isClickable == true,
                            isChargeAssistActive = isChargeAssistActive,
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ManageScheduleEntryPoint(
    navHostController: NavHostController,
    isEnabled: Boolean,
    isClickable: Boolean,
    isChargeAssistActive: Boolean,
) {
    Row(
        modifier =
            Modifier
                .background(AppTheme.colors.tile05)
                .padding(start = 16.dp)
                .fillMaxWidth()
                .height(56.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        OAButtonTextView(
            text = stringResource(R.string.manage_schedule),
            color = AppTheme.colors.tertiary03,
        )
        Spacer(modifier = Modifier.weight(1f)) // Pushes the button to the far right
        IconButton(
            onClick = {
                navHostController.navigate(
                    ChargeAssistRoute.ManualScheduleScreen.route + "/$isClickable/$isEnabled/$isChargeAssistActive",
                )
            },
        ) {
            Icon(
                modifier =
                    Modifier
                        .semantics { contentDescription = "Navigate to Manage Scheduling" },
                painter = painterResource(id = R.drawable.ic_arrow_right),
                contentDescription = "manage schedule entry arrow ",
                tint = AppTheme.colors.tertiary00,
            )
        }
    }
}

@Composable
fun ScheduleImage(modifier: Modifier = Modifier) {
    Box(
        modifier =
            modifier
                .size(48.dp),
        // Ensures the Box is the same size as the circle
        contentAlignment = Alignment.Center,
    ) {
        Image(
            colorFilter = ColorFilter.tint(AppTheme.colors.button02b),
            modifier =
                Modifier
                    .size(48.dp),
            painter = painterResource(id = R.drawable.circle),
            contentDescription = stringResource(R.string.schedule_image_description),
            contentScale = ContentScale.Fit,
        )

        Image(
            modifier =
                Modifier
                    .size(30.dp),
            painter = painterResource(id = R.drawable.ic_manual_schedule),
            contentDescription = "",
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewManualScheduleWidget() {
    val navController = rememberNavController()
    val manualScheduleCardParamsForCAToggle: ManualScheduleCardParamsForCAToggle =
        ManualScheduleCardParamsForCAToggle(
            isClickable = true,
            isEnabled = true,
            onToggle = {},
            isChargeAssistActive = false,
        )
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()

    ManualScheduleWidget(
        navController,
        manualScheduleCardParamsForCAToggle = manualScheduleCardParamsForCAToggle,
        showEntryPointSection = true,
        chargeAssistViewModel = chargeAssistViewModel,
        isChargeAssistActive = false,
    )
}
