/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeassist.view.viewmodel

import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.features.chargeassist.ChargeAssistRoute
import com.toyota.oneapp.features.chargeassist.application.ChargeAssistUseCase
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_FAILURE_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_SUCCESSFUL_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.NOT_FOUND
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.UNENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.UNENROLLMENT_SUCCESSFUL
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistToggleSwitches.Companion.ECO_CHARGE_SWITCH
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistToggleSwitches.Companion.MANUAL_SCHEDULE_SWITCH
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistWidgetNamesState.Companion.CHARGE_INFO_ENROLLMENT_FAILURE_WIDGET
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistWidgetNamesState.Companion.CHARGE_INFO_INVALID_ZIP_WIDGET
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistWidgetNamesState.Companion.SCHEDULE_SCREEN_ENROLLMENT_FAILURE
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistWidgetNamesState.Companion.SCHEDULE_SCREEN_INVALID_ZIP_WIDGET
import com.toyota.oneapp.features.chargeassist.view.screens.entrypoint2.getFinalStatus
import com.toyota.oneapp.features.chargeinfo.application.ChargeInfoState
import com.toyota.oneapp.features.chargeinfo.navigation.ChargeInfoRoute
import com.toyota.oneapp.features.chargeschedule.application.EcoSchduleCardState
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoResponse
import com.toyota.oneapp.features.core.util.DateTimeUtil
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject
import javax.inject.Singleton

const val MAX_FAILURE_CARD_SHOWS = 3
const val MAX_INVALID_ZIP_CARD_SHOWS = 2
const val CHARGE_ASSIST_SWITCH = "chargeAssistSwitch"

@Singleton
class ChargeAssistVMHelper
    @Inject
    constructor(
        val analyticsLogger: AnalyticsLogger,
        private val chargeAssistUseCase: ChargeAssistUseCase,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) {
        private val _routeForEnrollmentStatusDeepLink = MutableStateFlow("")
        val routeForEnrollmentStatusDeepLink: StateFlow<String> = _routeForEnrollmentStatusDeepLink

        private val _shouldCAEntryCardBeDisplayed = MutableStateFlow(false)
        val shouldCAEntryCardBeDisplayed: StateFlow<Boolean> = _shouldCAEntryCardBeDisplayed

        private val _isZipEnteredOnEligibilityScreenValid = MutableStateFlow<Boolean?>(null)
        val isZipEnteredOnEligibilityScreenValid: StateFlow<Boolean?> = _isZipEnteredOnEligibilityScreenValid

        private val _profileInfoData = MutableStateFlow<Resource<ProfileInfoResponse?>?>(null)
        val profileInfoData: StateFlow<Resource<ProfileInfoResponse?>?> = _profileInfoData

        private val _profileInfoZipCode = MutableStateFlow("")
        val profileInfoZipCode: StateFlow<String> = _profileInfoZipCode

        private val _shouldInvalidWidgetBeDisplayed = MutableStateFlow(false)
        val shouldInvalidWidgetBeDisplayed: StateFlow<Boolean> = _shouldInvalidWidgetBeDisplayed

        private val _shouldShowEnrollmentFailureWidgetBeDisplayed = MutableStateFlow(false)
        val shouldShowEnrollmentFailureWidgetBeDisplayed: StateFlow<Boolean> =
            _shouldShowEnrollmentFailureWidgetBeDisplayed

        private val _isEVPluggedIN = MutableStateFlow(false)
        val isEVPluggedIN: StateFlow<Boolean> = _isEVPluggedIN

        private val _isEVCharging = MutableStateFlow(false)
        val isEVCharging = _isEVCharging.asStateFlow()

        private val _chargingPercentage = MutableStateFlow(0)
        val chargingPercentage: StateFlow<Int?> = _chargingPercentage

        private val _chargeCompletionTime = MutableStateFlow("")
        val chargeCompletionTime: StateFlow<String> = _chargeCompletionTime

        private val _pluggedInTime = MutableStateFlow("")
        val pluggedInTime: StateFlow<String?> = _pluggedInTime

        // Custom Switch variable section
        private val _activeSwitch = MutableStateFlow(oneAppPreferenceModel.getActiveSwitch())
        val activeSwitch: StateFlow<String> = _activeSwitch.asStateFlow()

        private val _isChargeAssistActive = MutableStateFlow(false)
        val isChargeAssistActive: StateFlow<Boolean> = _isChargeAssistActive
        private var userHasToggledTheSwitch = false

        suspend fun checkEnrollmentStatusForAutoToggle() {
            // Check enrollmentStatus
            val enrollmentInfo =
                chargeAssistUseCase.fetchEnrollmentStatus().firstOrNull()

            // grab the current enrollment status
            val finalStatus = getFinalStatus(enrollmentInfo)

            if (finalStatus in ENROLLMENT_SUCCESSFUL_STATUSES &&
                !userHasToggledTheSwitch &&
                _activeSwitch.value.isEmpty()
            ) {
                _activeSwitch.value = CHARGE_ASSIST_SWITCH // set CA switch to on Automatically
                oneAppPreferenceModel.saveActiveSwitch(CHARGE_ASSIST_SWITCH)
            }
        }

        fun isEcoChargingEnabled(ecoState: EcoSchduleCardState) {
            if (_activeSwitch.value.isEmpty()) {
                _activeSwitch.value =
                    if (ecoState is EcoSchduleCardState.Success) {
                        if (ecoState.uiModel.optIn) ECO_CHARGE_SWITCH else MANUAL_SCHEDULE_SWITCH
                    } else {
                        MANUAL_SCHEDULE_SWITCH
                    }
            }
        }

        fun isChargeAssistActive() {
            val isCASwitchActive = _activeSwitch.value == CHARGE_ASSIST_SWITCH
            _isChargeAssistActive.value = isCASwitchActive
        }

        fun setActiveSwitchValue(switchId: String) {
            _activeSwitch.value = switchId
        }

        fun loadSavedActiveSwitch() {
            val loadActiveSwitch = oneAppPreferenceModel.getActiveSwitch()
            _activeSwitch.value = loadActiveSwitch
            isChargeAssistActive()
        }

        fun toggleSwitch(switchId: String) {
            // Toggle the switch state
            if (_activeSwitch.value == switchId) {
                // If the clicked switch is already active, turn it off

                setActiveSwitchValue("")
                oneAppPreferenceModel.saveActiveSwitch("") // Save the inactive state
            } else {
                // Set the clicked switch as active
                setActiveSwitchValue(switchId)
                oneAppPreferenceModel.saveActiveSwitch(switchId)
            }
            userHasToggledTheSwitch = true
            isChargeAssistActive()
        }

        fun resetAllToggle() {
            _activeSwitch.value = ""
            oneAppPreferenceModel.saveActiveSwitch("") // Save the inactive state
        }

        fun grabRealEVChargeDataRaw(
            chargeInfo: ChargeInfoState,
            processStatusEndTime: String?,
            pluggedInTime: String?,
        ) {
            when (chargeInfo) {
                is ChargeInfoState.ShowChargeInfo -> {
                    _isEVPluggedIN.value = chargeInfo.uiModel.evBatteryInfo?.isPlugConnected == true
                    _isEVCharging.value = chargeInfo.uiModel.evBatteryInfo?.isCharging == true
                    _chargingPercentage.value = chargeInfo.uiModel.evBatteryInfo?.chargingPercentage ?: 0
                    _chargeCompletionTime.value = processStatusEndTime ?: ""
                    _pluggedInTime.value = DateTimeUtil.extractedPluggedInTime(pluggedInTimeRaw = pluggedInTime)
                }

                else -> { // left intentionally blank
                }
            }
        }

        suspend fun loadZipValidity() {
            val userId = grabGuid()
            _isZipEnteredOnEligibilityScreenValid.value = oneAppPreferenceModel.loadZipValidity(userId.toString())
        }

        fun setZipEnteredOnEligibilityScreenValid(
            userId: String,
            value: Boolean?,
        ) {
            _isZipEnteredOnEligibilityScreenValid.value = value
            oneAppPreferenceModel.saveZipValidity(userId, value)
        }

        suspend fun grabGuid(): String? {
            var userId: String? = ""
            chargeAssistUseCase.fetchProfileInfo().collect { profileDataRaw ->
                userId =
                    profileDataRaw.data
                        ?.payload
                        ?.customer
                        ?.guid
            }
            return userId
        }

        suspend fun getFetchProfile() {
            chargeAssistUseCase.fetchProfileInfo().collect { profileInfoDataRaw ->
                _profileInfoData.value = profileInfoDataRaw
            }
        }

        suspend fun grabSystemZip() {
            chargeAssistUseCase.fetchProfileInfo().collect { profileInfoRaw ->
                _profileInfoZipCode.value =
                    profileInfoRaw.data
                        ?.payload
                        ?.customer
                        ?.addresses
                        ?.firstOrNull()
                        ?.zipCode
                        .toString()
            }
        }

        suspend fun resetZipEligibilityToNull() {
            // _isZipEnteredOnEligibilityScreenValid.value = null
            oneAppPreferenceModel.resetZipValidity(grabGuid().toString())
        }

        suspend fun determineEligibilityForEntryDisplay(
            systemZip: String,
            currentEnrollmentStatus: String,
            isEligibleForCAEnrollment: Boolean,
        ): Boolean {
            val zipCheck = systemZip.isNotNullOrEmpty() && isEligibleForCAEnrollment

            val isUnenrollmentPending = currentEnrollmentStatus in UNENROLLMENT_PENDING_STATUSES
            val isEligibleForDisplay =
                when {
                    currentEnrollmentStatus in ENROLLMENT_SUCCESSFUL_STATUSES -> false
                    currentEnrollmentStatus in ENROLLMENT_PENDING_STATUSES -> false
                    zipCheck && currentEnrollmentStatus == NOT_FOUND -> true
                    isEligibleForCAEnrollment && zipCheck && !isUnenrollmentPending -> true
                    checkInvalidZipVisitCountBelow2(isEligibleForCAEnrollment, currentEnrollmentStatus) -> true
                    currentEnrollmentStatus.equals(UNENROLLMENT_SUCCESSFUL, ignoreCase = true) -> true
                    else -> false
                }
            return isEligibleForDisplay
        }

        // helper function
        private fun checkInvalidZipVisitCountBelow2(
            isEligibleForCAEnrollment: Boolean,
            finalStatus: String,
        ): Boolean =
            isEligibleForCAEnrollment &&
                isZipEnteredOnEligibilityScreenValid.value == null &&
                finalStatus == NOT_FOUND &&
                oneAppPreferenceModel.getVisitCount() < 2

        suspend fun shouldCAEntryCardDisplay(
            currentEnrollmentStatus: String,
            isEligibleForCAEnrollment: Boolean,
            systemZip: String?,
        ) {
            val isEligibleForDisplay =
                determineEligibilityForEntryDisplay(
                    systemZip.orEmpty(),
                    currentEnrollmentStatus,
                    isEligibleForCAEnrollment,
                )
            if (isEligibleForDisplay) {
                _isChargeAssistActive.value = false
            }
            _shouldCAEntryCardBeDisplayed.value = isEligibleForDisplay
        }

        // determine if any widget should show based on widget name
        suspend fun shouldShowInvalidZipCard(
            enrollmentStatus: String,
            widgetName: String,
            systemZip: String?,
        ) {
            var hasIncrementedVisit = false
            chargeAssistUseCase
                .fetchProfileInfo()
                .collect {
                    val numberOfAllowedInvalidCardZipCardShows = MAX_INVALID_ZIP_CARD_SHOWS

                    val checkedWidgetName =
                        when {
                            isZipEnteredOnEligibilityScreenValid.value == null && enrollmentStatus == NOT_FOUND -> ""
                            isZipEnteredOnEligibilityScreenValid.value == false -> widgetName
                            else -> ""
                        }
                    val visitCountForZipInvalid = oneAppPreferenceModel.getVisitCount()

                    when (checkedWidgetName) {
                        CHARGE_INFO_INVALID_ZIP_WIDGET, SCHEDULE_SCREEN_INVALID_ZIP_WIDGET -> {
                            if (!hasIncrementedVisit && enrollmentStatus != "Unknown Status") {
                                oneAppPreferenceModel.incrementVisitCount()
                                hasIncrementedVisit = true

                                // Logic for invalidZip show
                                val isEligibleForCAEnrollment =
                                    chargeAssistUseCase.fetchUserEligibilityForCAEnrollmentAndEnrollmentStatus()
                                        .firstOrNull()?.first ?: false

                                val canShowInvalidZipWidget =
                                    visitCountForZipInvalid < numberOfAllowedInvalidCardZipCardShows

                                val ultimatelyShowTheInvalidZipWidget =
                                    isEligibleForCAEnrollment &&
                                        systemZip.isNullOrEmpty() &&
                                        enrollmentStatus == NOT_FOUND &&
                                        canShowInvalidZipWidget &&
                                        isZipEnteredOnEligibilityScreenValid.value == false

                                // display widget to user when very specific conditions are met
                                _shouldInvalidWidgetBeDisplayed.value = ultimatelyShowTheInvalidZipWidget
                            }
                        }
                    }
                }
        }

        fun shouldShowEnrollmentFailureCard(
            enrollmentStatus: String,
            widgetName: String,
        ) {
            val hasIncrementedVisit = false

            val numberOfAllowedFailureCardShows = MAX_FAILURE_CARD_SHOWS
            val checkedWidgetName =
                when (enrollmentStatus) {
                    in ENROLLMENT_FAILURE_STATUSES -> widgetName
                    !in ENROLLMENT_FAILURE_STATUSES -> ""
                    else -> ""
                }

            val visitCountForEnrollmentFailure = oneAppPreferenceModel.getVisitCountForEnrollmentFailure()

            when (checkedWidgetName) {
                CHARGE_INFO_ENROLLMENT_FAILURE_WIDGET, SCHEDULE_SCREEN_ENROLLMENT_FAILURE -> {
                    if (!hasIncrementedVisit && enrollmentStatus != "Unknown Status") {
                        oneAppPreferenceModel.incrementVisitCountForEnrollmentFailure()
                        //   Logic for EnrollmentFailure show
                        val ultimatelyShowTheFailureWidget =
                            enrollmentStatus in ENROLLMENT_FAILURE_STATUSES &&
                                visitCountForEnrollmentFailure <= numberOfAllowedFailureCardShows

                        _shouldShowEnrollmentFailureWidgetBeDisplayed.value = ultimatelyShowTheFailureWidget
                    }
                }
            }
        }

        suspend fun handleChargeAssistEligibilityRouting() {
            val isEligibleForCAEnrollment =
                chargeAssistUseCase
                    .fetchUserEligibilityForCAEnrollmentAndEnrollmentStatus().first().first
            grabSystemZip()
            val systemZip = profileInfoZipCode.value
            chargeAssistUseCase.fetchEnrollmentStatus().collect {
                val enrollmentStatus = getFinalStatus(it)

                when (enrollmentStatus) {
                    in ENROLLMENT_SUCCESSFUL_STATUSES -> {
                        // navigate to Charge Info
                        _routeForEnrollmentStatusDeepLink.value = ChargeInfoRoute.ChargeInfoScreen.route
                    }
                    NOT_FOUND -> {
                        if (isEligibleForCAEnrollment) {
                            // navigate to Charge Assist Learn More
                            _routeForEnrollmentStatusDeepLink.value =
                                ChargeAssistRoute.LearnMoreScreen.route + "/$systemZip/false"
                        }
                    }
                }
            }
        }

        fun fetchLegalConsentParagraph(
            utilityProviderTCWebsite: String?,
            utilityProviderName: String?,
            weaveGridTermsAndConditions: String?,
            privacyPolicyWebsite: String?,
        ): String {
            val officialParagraphWithLinks =
                """
                <!DOCTYPE html>
                  <html>
                  <body>
                  <p>
                     By checking this box, I agree to the
                     
                      <!--Link 1-->
                       <strong><a href="$utilityProviderTCWebsite">
                                      $utilityProviderName Terms and Conditions</a></strong>,
                      <!--Link 2-->
                      <strong><a href="$weaveGridTermsAndConditions">
                                      WeaveGrid Terms &amp Conditions</a></strong>, and                                   
                      <!--Link 3-->
                       <strong><a href="$privacyPolicyWebsite">
                                      WeaveGrid Privacy Policy</a></strong>.  
                                      
                     By submitting my mobile number, I agree to receive phone calls or text messages 
                     from WeaveGrid, and/or a representative of WeaveGrid. I understand that text messages may be required to receive 
                     service-related information. Consent to marketing is not a condition for participation. 
                     Messages may be sent using an automated technology (including texts/SMS). Reply HELP for help, and STOP to cancel. Message and data rates may apply.
                  </p>
                  </body>
                  </html>
                """.trimIndent()

            return officialParagraphWithLinks
        }

        // ==== Firebase Tags For Charge Assist====
        private val logTag = "AnalyticsLogger"

        fun logAnalyticsEvent(analyticsEvent: AnalyticsEvent) {
            analyticsLogger.logEvent(analyticsEvent)
            LogTool.d(logTag, "Logging event: ${analyticsEvent.name}")
        }
    }
