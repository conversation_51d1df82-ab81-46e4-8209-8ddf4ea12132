/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeassist.dataaccess.service

import com.toyota.oneapp.features.chargeassist.dataaccess.model.addressupdateofficial.request.UpdateAddressOfficialRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.addressupdateofficial.response.UpdateAddressOfficialResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollment.request.EnrollmentRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollment.response.EnrollmentResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollmentcheck.EnrollmentCheckResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.processstatus.request.ProcessStatusRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.processstatus.response.ProcessStatusResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.programavailability.ProgramEligibilityModelPayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.toggle.request.ToggleRequestModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.toggle.response.ToggleResponseModelPayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.unenrollment.request.UnenrollmentRequestBodyModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.unenrollment.response.UnenrollmentResponseModel
import com.toyota.oneapp.network.models.ApiResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Query

interface ChargeAssistApi {
    @GET("/charging/v2/driver/program-availability")
    suspend fun getChargeAssistEligibilityBasedOnZip(
        @Query("zipCode") zipCode: String?,
    ): Response<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>

    @GET("/charging/v2/driver/enrollment-check")
    suspend fun getEnrollmentStatus(
        @Header("x-email") email: String,
    ): Response<ApiResponse<EnrollmentCheckResponsePayload?>?>

    @POST("/charging/v2/driver/enrollment")
    suspend fun enrollAUserIntoCA(
        @Header("x-vin") vin: String,
        @Body body: EnrollmentRequestModel?,
    ): Response<ApiResponse<EnrollmentResponsePayload?>?>

    @POST("/charging/v2/driver/process-status")
    suspend fun processStatus(
        @Header("x-vin") vin: String,
        @Body body: ProcessStatusRequestModel?,
    ): Response<ApiResponse<ProcessStatusResponsePayload?>?>

    @POST("charging/v2/driver/enrollment/toggle")
    suspend fun setToggle(
        @Header("x-vin") vin: String,
        @Body body: ToggleRequestModel?,
    ): Response<ApiResponse<ToggleResponseModelPayload?>?>

    @POST("/charging/v2/driver/unenroll")
    suspend fun unEnrollAUserFromCA(
        @Header("x-vin") vin: String,
        @Body body: UnenrollmentRequestBodyModel?,
    ): Response<ApiResponse<UnenrollmentResponseModel>?>

    @PUT("/oneapi/v4/account")
    suspend fun updateAddressOfficial(
        @Header("X-BRAND") brand: String?,
        @Body body: UpdateAddressOfficialRequestModel?,
    ): Response<ApiResponse<UpdateAddressOfficialResponsePayload?>?>
}
