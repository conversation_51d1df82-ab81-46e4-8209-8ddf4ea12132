package com.toyota.oneapp.features.chargeassist.view.helper

import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.rememberNavController
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_SUCCESSFUL_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStatus
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistScheduleStates.Companion.FAILED
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistScheduleStates.Companion.IN_PROGRESS
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistScheduleStates.Companion.SUCCESS
import com.toyota.oneapp.features.chargeassist.view.screens.entrypoint2.ChargeAssistEntryTile
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.chargeinfo.presentation.ChargeInfoViewModel

fun getEnrollmentScheduleStates(
    triple: Triple<String?, String?, Boolean?>,
    isEVCharging: Boolean?,
    schedulePendingForOver5: Boolean?,
): ChargeAssistEnrollmentStatus {
    return when {
        // Case 1
        triple.first in ENROLLMENT_SUCCESSFUL_STATUSES &&
            triple.second == IN_PROGRESS &&
            triple.third == true &&
            schedulePendingForOver5 == true ->
            ChargeAssistEnrollmentStatus.ENROLLED_SCHEDULE_NOT_RETRIEVED_AFTER_5_PLUGGED_IN

        triple.first in ENROLLMENT_SUCCESSFUL_STATUSES &&
            triple.second == SUCCESS &&
            (triple.third == true || isEVCharging == true) ->
            ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_READY_AND_PLUGGED_IN

        triple.first in ENROLLMENT_SUCCESSFUL_STATUSES &&
            triple.second == SUCCESS &&
            triple.third == false ->
            ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_READY_AND_NOT_PLUGGED_IN

        triple.first in ENROLLMENT_SUCCESSFUL_STATUSES &&
            triple.second == IN_PROGRESS &&
            triple.third == true ->
            ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_PENDING_AND_PLUGGED_IN

        triple.first in ENROLLMENT_SUCCESSFUL_STATUSES &&
            triple.second == IN_PROGRESS &&
            triple.third == null ->
            ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_PENDING

        triple.first in ENROLLMENT_SUCCESSFUL_STATUSES &&
            (triple.second == FAILED || triple.second == null) &&
            triple.third != null -> // true or false
            ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_FAILED_AND_PLUGGED_IN

        triple.first in ENROLLMENT_SUCCESSFUL_STATUSES &&
            (triple.second == IN_PROGRESS || triple.second == null) &&
            triple.third == false ->
            ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_PENDING_AND_NOT_PLUGGED_IN
        else ->
            ChargeAssistEnrollmentStatus.NONE
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewChargeAssistEntryTile() {
    val navController = rememberNavController()
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()
    val chargeInfoViewModel: ChargeInfoViewModel = hiltViewModel()
    ChargeAssistEntryTile(navController, chargeInfoViewModel, chargeAssistViewModel)
}
