package com.toyota.oneapp.features.chargeassist.view.screens

import androidx.compose.material.ModalBottomSheetState
import androidx.compose.runtime.MutableState
import com.toyota.oneapp.features.chargeassist.dataaccess.model.programavailability.ProgramEligibilityModelPayload
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleUIState
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.ApiResponse

data class UserEnrollmentState(
    val userName: MutableState<String>,
    val userAddress: MutableState<String>,
    val userCity: MutableState<String>,
    val userState: MutableState<String>,
    val userZip: MutableState<String>,
    val userUtilityAccountNumber: MutableState<String>,
    val userPhoneNumber: MutableState<String>,
    val userEmail: MutableState<String>,
    val legalConsentConfirmed: MutableState<Boolean>,
    var isValidAccountLengthEntered: MutableState<Boolean>,
)

data class EnrollmentScreenData(
    val zipFromZipNotAvailableScreen: String?,
    val currentEnrollmentStatus: String?,
    val enableEnrollmentFields: Boolean,
    val unEnrollFromCABottomSheetState: ModalBottomSheetState,
    val userEnrollmentState: UserEnrollmentState,
    val collectZipProgramData: Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?,
)

data class CAEnabledLayoutDependencies(
    val bottomSheetStateCA: ModalBottomSheetState,
    val uiState: ChargeScheduleUIState,
)

sealed class ChargeAssistEnrollmentStates {
    companion object {
        const val NOT_FOUND = "Not Found"
        const val RECORD_CREATED = "RecordCreated"
        const val REGISTRATION_SUCCESSFUL = "RegistrationSuccessful"
        const val REGISTRATION_FAILED = "RegistrationFailed"
        const val AGGREGATOR_ENROLLMENT_SUCCESSFUL = "AggregatorEnrollmentSuccessful"
        private const val AGGREGATOR_ENROLLMENT_FAILED = "AggregatorEnrollmentFailed"

        // Unenrollment Pending
        const val UNENROLLMENT_IN_PROGRESS = "UnEnrollmentInProgress"
        const val AGGREGATOR_UNENROLLMENT_SUCCESSFUL = "AggregatorUnEnrollmentSuccessful"
        private const val UTILITY_PROVIDER_UNENROLLMENT_SUCCESSFUL = "UtilityProviderUnenrollmentSuccessful"

        const val UNENROLLMENT_SUCCESSFUL = "UnEnrollmentSuccessful"
        const val AGGREGATOR_UNENROLLMENT_FAILED = "AggregatorUnEnrollmentFailed"
        const val ENROLLMENT_SUCCESSFUL = "EnrollmentSuccessful"
        const val ENROLLMENT_FAILED = "EnrollmentFailed"

        // User enrollment status on Enroll Screen
        const val ENROLLMENT_PENDING = "Enrollment Pending"
        const val ENROLLED = "Enrolled"

        val ENROLLMENT_FAILURE_STATUSES = listOf(ENROLLMENT_FAILED, REGISTRATION_FAILED, AGGREGATOR_ENROLLMENT_FAILED)

        //
        val ENROLLMENT_SUCCESSFUL_STATUSES =
            listOf(
                ENROLLMENT_SUCCESSFUL,
                AGGREGATOR_UNENROLLMENT_FAILED,
            )

        val ENROLLMENT_PENDING_STATUSES =
            listOf(
                REGISTRATION_SUCCESSFUL,
                RECORD_CREATED,
                AGGREGATOR_ENROLLMENT_SUCCESSFUL,
                ENROLLMENT_PENDING,
            )

        // Unenrollment Pending
        val UNENROLLMENT_PENDING_STATUSES =
            listOf(
                UNENROLLMENT_IN_PROGRESS,
                UTILITY_PROVIDER_UNENROLLMENT_SUCCESSFUL,
                AGGREGATOR_UNENROLLMENT_SUCCESSFUL,
            )
    }
}

enum class ChargeAssistEnrollmentStatus {
    ENROLLED_WITH_SCHEDULE_READY_AND_PLUGGED_IN,
    ENROLLED_WITH_SCHEDULE_PENDING_AND_PLUGGED_IN,
    ENROLLED_WITH_SCHEDULE_READY_AND_NOT_PLUGGED_IN,
    ENROLLED_WITH_SCHEDULE_FAILED_AND_PLUGGED_IN,
    ENROLLED_SCHEDULE_NOT_RETRIEVED_AFTER_5_PLUGGED_IN,
    ENROLLED_WITH_SCHEDULE_PENDING_AND_NOT_PLUGGED_IN,
    ENROLLED_WITH_SCHEDULE_FAILED,
    ENROLLED_WITH_SCHEDULE_PENDING,
    ENROLLED_NULL_SCHEDULE_AND_NOT_PLUGGED_IN,
    NONE,
}

sealed class ChargeAssistScheduleStates {
    companion object {
        const val SUCCESS = "success"
        const val IN_PROGRESS = "in_progress"
        const val FAILED = "failed"
    }
}

object ChargeAssistEntryPointTextStates {
    const val ENROLL = "Enroll"
    const val ENTER = "Enter"
}

sealed class ChargeAssistToggleSwitches {
    companion object {
        const val CHARGE_ASSIST_SWITCH = "chargeAssistSwitch"
        const val ECO_CHARGE_SWITCH = "ecoChargingSwitch"
        const val MANUAL_SCHEDULE_SWITCH = "manualScheduleSwitch"
    }
}

data class ChargeAssistToggleCardParams(
    val isClickable: Boolean,
    val isEnabled: Boolean,
    val onToggle: () -> Unit = {},
    val isChargeAssistActive: Boolean,
    val sheetState: ModalBottomSheetState,
    val isZipFromEligibilityScreenValid: Boolean?,
    val fetchIsInProgressStatusOver5min: Boolean,
    val isEVPluggedIN: Boolean?,
)

data class EcoChargeCardParamsForCAToggle(
    val isClickable: Boolean,
    val isEnabled: Boolean,
    val onToggle: () -> Unit = {},
    val isChargeAssistActive: Boolean = false,
    val activeSwitch: String = "",
)

data class ManualScheduleCardParamsForCAToggle(
    val isClickable: Boolean,
    val isEnabled: Boolean,
    val onToggle: () -> Unit = {},
    val isChargeAssistActive: Boolean = false,
    val activeSwitch: String = "",
)

data class ChargeInfoDisplayCardState(
    val currentEnrollmentStatus: String,
    val isZipEnteredOnEligibilityScreenValid: Boolean?,
    val systemZip: String?,
    val isEligibleForCAEnrollment: Boolean,
    val processScheduleStatus: String?,
    val isEVPluggedIN: Boolean?,
    val isEVCharging: Boolean?,
    val fetchIsInProgressStatusOver5min: Boolean?,
)

data class ChargeInfoDisplayCardRenderState(
    val shouldCAEntryCardBEDisplayed: Boolean,
    val shouldShowInvalidZipCardBeDisplayed: Boolean,
    val shouldEnrollmentFailureCardBeDisplayed: Boolean,
)

sealed class ChargeAssistWidgetNamesState {
    companion object {
        const val CHARGE_INFO_INVALID_ZIP_WIDGET = "charge_info_invalid_zip_widget"
        const val CHARGE_INFO_ENROLLMENT_FAILURE_WIDGET = "enrollment_failure_widget"

        const val SCHEDULE_SCREEN_INVALID_ZIP_WIDGET = "invalid_zip_schedule_widget_on_schedule_screen"
        const val SCHEDULE_SCREEN_ENROLLMENT_FAILURE = "enrollment_failed_on_schedule_screen"
    }
}

sealed class ToggleSwitchState {
    companion object {
        const val MANAGE_SCHEDULE_ABBV = "MS"
        const val CHARGE_ASSIST_ABBV = "CA"
        const val ECO_CHARGING_ABBV = "EC"
    }
}
