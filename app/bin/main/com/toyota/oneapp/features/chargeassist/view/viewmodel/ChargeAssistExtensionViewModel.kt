/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeassist.view.viewmodel

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.features.chargeassist.application.ChargeAssistUseCase
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ChargeAssistExtensionViewModel
    @Inject
    constructor(
        val analyticsLogger: AnalyticsLogger,
        private val chargeAssistUseCase: ChargeAssistUseCase,
    ) : BaseViewModel() {
        private val _currentToggleStatus = MutableStateFlow(false)
        val currentToggleStatus: StateFlow<Boolean> = _currentToggleStatus

        private val _isConsentAccepted = MutableStateFlow(false)
        val isConsentAccepted: StateFlow<Boolean> = _isConsentAccepted

        private val _isConsentDeclined = MutableStateFlow(false)
        val isConsentDeclined: StateFlow<Boolean> = _isConsentDeclined

        fun resetConsentStatus() {
            _isConsentAccepted.value = false
        }

        fun isConsentAccepted() {
            viewModelScope.launch {
                chargeAssistUseCase.acceptChargeAssistConsent()
                    .collect { isConsentAcceptedInfo ->
                        _isConsentAccepted.value = isConsentAcceptedInfo
                    }
            }
        }

        fun isConsentDecline() {
            viewModelScope.launch {
                chargeAssistUseCase.declineChargeAssistConsent()
                    .collect { isConsentDeclineInfo ->
                        _isConsentDeclined.value = isConsentDeclineInfo
                    }
            }
        }

        fun updateToggleStatus(currentSwitchState: String) {
            viewModelScope.launch {
                chargeAssistUseCase.setToggleStatus(currentSwitchState).collect { status ->
                    _currentToggleStatus.value = status == "toggle_on"
                }
            }
        }
    }
