package com.toyota.oneapp.features.chargeassist.view.screens

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.google.gson.Gson
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrolllearnmoreresponse.EnrollLearnMoreResponseModel
import com.toyota.oneapp.features.chargeassist.dataaccess.model.programavailability.ProgramEligibilityModelPayload
import com.toyota.oneapp.features.chargeassist.view.helper.ChargeAssistBackButtonTitleHeader
import com.toyota.oneapp.features.chargeassist.view.helper.ExpandableQuestionAnswerCard
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OACaption1TextView
import com.toyota.oneapp.features.core.composable.OACaption2TextView
import com.toyota.oneapp.features.core.composable.OAHeadline1TextView
import com.toyota.oneapp.features.core.composable.shimmerEffect
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.ApiResponse
import kotlinx.coroutines.flow.StateFlow

private const val EMAIL_PATTERN = "[a-zA-Z0-9.-]+@[a-z]+\\.+[a-z]+"

@Composable
fun EnrollmentUtilityLearnMoreScreen(
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
    zipFromZipUnavailableScreen: String?,
) {
    LaunchedEffect(Unit) {
        chargeAssistViewModel.fetchZipEligibilityStatus()
        chargeAssistViewModel.fetchEligibilityForZipOnUnavailableScreen(zipFromZipUnavailableScreen)
    }

    val zipDataFlow: StateFlow<Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?> =
        if (zipFromZipUnavailableScreen.isNullOrEmpty() ||
            zipFromZipUnavailableScreen == "" || zipFromZipUnavailableScreen == "''"
        ) {
            chargeAssistViewModel.zipEligibilityStatusData
        } else {
            chargeAssistViewModel.zipEligibilityStatusDataForUnavailableScreenZip
        }

    val collectData by zipDataFlow.collectAsState()

    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        ChargeAssistBackButtonTitleHeader(navController, false)

        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            ProgramSmartChargeLogo(collectData)
            ProgramLearnMoreHeadline(collectData)
            EnrollExpandableQuestionAnswerList(collectData)
        }
    }

    // Handle back press
    BackHandler {
        // Navigate back to the previous screen
        navController.popBackStack()
    }
}

@Composable
fun LearnMoreExpandableItems(
    programDescription: EnrollLearnMoreResponseModel?,
    utilityProviderEmail: String,
) {
    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        LazyColumn(
            modifier =
                Modifier
                    .weight(1f) // This ensures LazyColumn takes only the space available, making it scrollable
                    .padding(top = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            item {
                ExpandableQuestionAnswerCard(
                    question = programDescription?.programDescription?.programIncentives?.title.toString(),
                    answer =
                        programDescription?.programDescription?.programIncentives?.bulletPoints?.joinToString(
                            separator = "",
                        ) { "$it\n" }?.trim(),
                )
            }
            item {
                val sentence = programDescription?.programDescription?.moreInformation?.details
                val link = programDescription?.programDescription?.moreInformation?.link
                ExpandableQuestionAnswerCard(
                    question = programDescription?.programDescription?.moreInformation?.title.toString(),
                    answerComposable = {
                        ClickableLinksParagraph("$sentence", mapOf("Link" to "$link"))
                    },
                )
            }

            item {
                val sentence = programDescription?.programDescription?.questions?.details
                val link = programDescription?.programDescription?.questions?.link

                ExpandableQuestionAnswerCard(
                    question = programDescription?.programDescription?.questions?.title.toString(),
                    answerComposable = {
                        ClickableLinksParagraph("$sentence", mapOf(utilityProviderEmail to utilityProviderEmail))
                    },
                )
            }
            item {
                DisclaimerText()
            }
        }
    }
}

@Composable
fun EnrollExpandableQuestionAnswerList(collectData: Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?) {
    val programDescriptionState = remember { mutableStateOf<EnrollLearnMoreResponseModel?>(null) }
    val utilityProviderEmail = remember { mutableStateOf("") }

    LaunchedEffect(collectData) {
        when (collectData) {
            is Resource.Success<*> -> {
                val gson = Gson()
                val pureJson =
                    collectData.data?.payload?.firstOrNull()?.programDescription?.replace("\\", "")
                programDescriptionState.value = gson.fromJson(pureJson, EnrollLearnMoreResponseModel::class.java)
                utilityProviderEmail.value = collectData.data?.payload?.firstOrNull()?.aggregatorSupportEmail.toString()
            }

            else -> { // resolved locally
            }
        }
    }

    LearnMoreExpandableItems(programDescription = programDescriptionState.value, utilityProviderEmail.value)
}

@Composable
fun DisclaimerText() {
    Box {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(top = 32.dp, bottom = 75.dp),
        ) {
            OACaption2TextView(
                modifier =
                    Modifier
                        .padding(start = 16.dp, bottom = 8.dp),
                text = stringResource(R.string.disclaimer),
                color = AppTheme.colors.tertiary03,
            )
            OACaption1TextView(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(140.dp)
                        .padding(horizontal = 16.dp),
                text = stringResource(R.string.enroll_utility_disclaimer),
                color = AppTheme.colors.tertiary05,
            )
        }
    }
}

@Composable
fun ProgramLearnMoreHeadline(collectData: Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?) {
    val programName = collectData?.data?.payload?.firstOrNull()?.programName

    when (collectData) {
        null, is Resource.Loading<*> -> {
            Box(
                modifier =
                    Modifier
                        .padding(start = 29.dp, end = 29.dp)
                        .fillMaxWidth()
                        .shimmerEffect()
                        .height(100.dp),
            )
        }

        is Resource.Success<*> -> {
            OAHeadline1TextView(
                modifier =
                    Modifier
                        .padding(start = 29.dp, end = 29.dp),
                textAlign = TextAlign.Center,
                text = "$programName",
                maxLine = 2,
                color = AppTheme.colors.tertiary03,
            )
        }

        else -> { // left intentionally blank
        }
    }
}

@Composable
fun ClickableLinksParagraph(
    paragraph: String,
    linksMap: Map<String, String>,
    legalConsent: Boolean = false,
) {
    val context = LocalContext.current
    val annotatedString =
        buildAnnotatedString {
            withStyle(
                style =
                    SpanStyle(
                        fontSize = if (legalConsent) 14.sp else 16.sp,
                        color = AppTheme.colors.tertiary05,
                    ),
            ) {
                append(paragraph)
            }

            linksMap.forEach { (text, url) ->
                val startIndex = paragraph.indexOf(text)
                if (startIndex >= 0) {
                    val endIndex = startIndex + text.length
                    addStyle(
                        style =
                            SpanStyle(
                                fontWeight = FontWeight.Bold,
                                color = AppTheme.colors.tertiary05,
                                textDecoration = TextDecoration.Underline,
                            ),
                        start = startIndex,
                        end = endIndex,
                    )
                    addStringAnnotation(
                        tag = "URL",
                        annotation = url,
                        start = startIndex,
                        end = endIndex,
                    )
                }
            }
        }

    DisplayClickableLinksBasedOnComponent(legalConsent, linksMap, context, paragraph, annotatedString = annotatedString)
}

@Composable
fun DisplayClickableLinksBasedOnComponent(
    legalConsent: Boolean,
    linksMap: Map<String, String?>,
    context: Context,
    paragraph: String,
    annotatedString: AnnotatedString,
) {
    if (legalConsent) {
        OACallOut1TextView(
            modifier =
                Modifier
                    .padding(top = 9.dp, start = 2.dp, bottom = 80.dp, end = 16.dp)
                    .fillMaxWidth()
                    .padding(end = 16.dp)
                    .wrapContentHeight()
                    .clickable {
                        linksMap.forEach { (_, url) ->
                            handleClickableLinkOrEmail(url, context)
                        }
                    }
                    .semantics {
                        contentDescription = paragraph
                        role = Role.Button
                    },
            text = annotatedString,
            color = AppTheme.colors.tertiary05,
        )
    } else {
        Text(
            text = annotatedString,
            modifier =
                Modifier
                    .padding(16.dp)
                    .fillMaxWidth()
                    .clickable {
                        linksMap.forEach { (_, url) ->
                            handleClickableLinkOrEmail(url, context)
                        }
                    }
                    .semantics {
                        contentDescription = paragraph
                        role = Role.Button
                    },
            style = AppTheme.fontStyles.body3,
            onTextLayout = { textLayoutResult ->
                textLayoutResult.getOffsetForPosition(Offset.Zero)
            },
        )
    }
}

fun handleClickableLinkOrEmail(
    url: String?,
    context: Context,
) {
    when {
        url.isNullOrEmpty() || url == "null" -> {
            // handle case when URL is empty or null
            Toast.makeText(context, "Link is invalid", Toast.LENGTH_SHORT).show()
        }
        isEmail(url) -> {
            // handle case when url is an email address
            val intent =
                Intent(Intent.ACTION_SENDTO).apply {
                    data = Uri.parse("mailto:$url") // Only email apps should handle this
                    putExtra(Intent.EXTRA_SUBJECT, "Subject") // Optional
                    putExtra(Intent.EXTRA_TEXT, "Body") // Optional
                }

            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
            } else {
                Toast.makeText(context, "No email app available.", Toast.LENGTH_SHORT).show()
            }
        }
        else -> {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))

            when (intent.resolveActivity(context.packageManager)) {
                null -> {
                    // no app can handle this link
                    Toast.makeText(
                        context,
                        "Link is not available link.",
                        Toast.LENGTH_SHORT,
                    ).show()
                }
                else -> {
                    context.startActivity(intent)
                }
            }
        }
    }
}

// function to check if the string is an email address
private fun isEmail(url: String): Boolean {
    val emailPattern = EMAIL_PATTERN
    return url.matches(emailPattern.toRegex())
}

@Preview(showBackground = true)
@Composable
fun PreviewEnrollmentUtilityLearnMoreScreen() {
    val navController = rememberNavController()
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()
    EnrollmentUtilityLearnMoreScreen(navController, chargeAssistViewModel, "88888")
}
