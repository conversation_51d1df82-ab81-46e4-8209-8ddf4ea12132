package com.toyota.oneapp.features.chargeassist.view.viewmodel

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.features.chargeassist.application.ChargeAssistUseCase
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollment.response.EnrollmentResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollmentcheck.EnrollmentCheckResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.processstatus.response.ProcessStatusResponsePayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.programavailability.ProgramEligibilityModelPayload
import com.toyota.oneapp.features.chargeassist.dataaccess.model.unenrollment.response.UnenrollmentResponseModel
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_FAILURE_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_SUCCESSFUL_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.NOT_FOUND
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.UNENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.UNENROLLMENT_SUCCESSFUL
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeInfoDisplayCardRenderState
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeInfoDisplayCardState
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoResponse
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

private const val FIVE_MINUTES: Long = 5 * 60 * 100

@HiltViewModel
class ChargeAssistViewModel
    @Inject
    constructor(
        val analyticsLogger: AnalyticsLogger,
        private val chargeAssistUseCase: ChargeAssistUseCase,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        val chargeAssistVMHelper: ChargeAssistVMHelper,
    ) : BaseViewModel() {
        private val _isCABackEndFlagOn = MutableStateFlow(false)
        val isCABackEndFlagOn: StateFlow<Boolean> = _isCABackEndFlagOn

        // Full Responses (data members)
        private var _enrollmentStatusData =
            MutableStateFlow<Resource<ApiResponse<EnrollmentCheckResponsePayload?>?>?>(null)
        val enrollmentStatusData: StateFlow<Resource<ApiResponse<EnrollmentCheckResponsePayload?>?>?> =
            _enrollmentStatusData

        private var _zipEligibilityStatusData =
            MutableStateFlow<Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?>(null)
        val zipEligibilityStatusData: StateFlow<Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?> =
            _zipEligibilityStatusData

        private var _zipEligibilityStatusDataForUnavailableScreenZip =
            MutableStateFlow<Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?>(null)
        val zipEligibilityStatusDataForUnavailableScreenZip:
            StateFlow<Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?> =
            _zipEligibilityStatusDataForUnavailableScreenZip

        private val _userEnrollmentStatusDataString = MutableStateFlow<String?>("")
        val userEnrollmentStatusDataString: StateFlow<String?> = _userEnrollmentStatusDataString.asStateFlow()

        private var _processStatusData =
            MutableStateFlow<Resource<ApiResponse<ProcessStatusResponsePayload?>?>?>(null)
        val processStatusData: StateFlow<Resource<ApiResponse<ProcessStatusResponsePayload?>?>?> = _processStatusData

        private var _userEnrollmentStatusData =
            MutableStateFlow<Resource<ApiResponse<EnrollmentResponsePayload?>?>?>(null)
        val userEnrollmentStatusData: StateFlow<Resource<ApiResponse<EnrollmentResponsePayload?>?>?> =
            _userEnrollmentStatusData

        // Minor Data (data members)
        private val _showEligibilityBottomSheet = MutableSharedFlow<Boolean>()
        val showEligibilityBottomSheet: SharedFlow<Boolean> = _showEligibilityBottomSheet.asSharedFlow()

        private val _message = MutableStateFlow<String?>("")
        val message: StateFlow<String?> = _message

        private val _isUserEligibilityForCAEnrollment = MutableStateFlow(false)
        val isUserEligibilityForCAEnrollment: StateFlow<Boolean> = _isUserEligibilityForCAEnrollment

        // Testing save of current eligibility of zip form Eligibility screen

        private val _isInProgressStatusOver5min = MutableStateFlow(false)
        val isInProgressStatusOver5min: StateFlow<Boolean> = _isInProgressStatusOver5min

        // Tracking visit counts of widgets on Charge Info and Charge Schedule
        private val _visitCountForZipInvalid = MutableStateFlow(0)
        val visitCountForZipInvalid: StateFlow<Int> = _visitCountForZipInvalid

        private val _visitCountForEnrollmentFail = MutableStateFlow(0)
        val visitCountForEnrollmentFail: StateFlow<Int> = _visitCountForEnrollmentFail

        private val _shouldShowDescription = MutableStateFlow(false)
        val shouldShowDescription: StateFlow<Boolean> = _shouldShowDescription

        private val _unEnrollmentStatusData = MutableStateFlow<ApiResponse<UnenrollmentResponseModel>?>(null)
        val unEnrollmentStatusData: StateFlow<ApiResponse<UnenrollmentResponseModel>?> = _unEnrollmentStatusData

        private val _profileInfoData = MutableStateFlow<Resource<ProfileInfoResponse?>?>(null)
        val profileInfoData: StateFlow<Resource<ProfileInfoResponse?>?> = _profileInfoData

        private val _chargeAssistEntryButtonData = MutableStateFlow<Pair<String, String>?>(null)
        val chargeAssistEntryButtonData: StateFlow<Pair<String, String>?> = _chargeAssistEntryButtonData

        private val _chargeAssistDisplayCardData =
            MutableStateFlow(
                ChargeInfoDisplayCardState(
                    currentEnrollmentStatus = "",
                    isZipEnteredOnEligibilityScreenValid = false,
                    systemZip = "",
                    isEligibleForCAEnrollment = false,
                    processScheduleStatus = "",
                    isEVPluggedIN = false,
                    isEVCharging = false,
                    fetchIsInProgressStatusOver5min = false,
                ),
            )
        val chargeAssistDisplayCardData: StateFlow<ChargeInfoDisplayCardState> get() =
            _chargeAssistDisplayCardData.asStateFlow()

        private val _chargeAssistDisplayCardRenderState =
            MutableStateFlow(
                ChargeInfoDisplayCardRenderState(
                    shouldCAEntryCardBEDisplayed = false,
                    shouldShowInvalidZipCardBeDisplayed = false,
                    shouldEnrollmentFailureCardBeDisplayed = false,
                ),
            )
        val chargeAssistDisplayCardRenderState: StateFlow<ChargeInfoDisplayCardRenderState> get() =
            _chargeAssistDisplayCardRenderState.asStateFlow()

        init {
            getChargeAssistBackEndFlag()
        }

        // Method to update the state
        fun updateChargeAssistDisplayCardData(newState: ChargeInfoDisplayCardState) {
            _chargeAssistDisplayCardData.value = newState
        }

        fun updateChargeAssistDisplayCardRenderState(newState: ChargeInfoDisplayCardRenderState) {
            _chargeAssistDisplayCardRenderState.value = newState
        }

        fun getChargeAssistEntryButtonData(isEligibilityForCAEnrollment: Boolean) {
            viewModelScope.launch {
                chargeAssistUseCase.fetchChargeAssistEntryButtonData(isEligibilityForCAEnrollment)
                    .collect { chargeAssistEntryButtonDataInfo ->
                        _chargeAssistEntryButtonData.value = chargeAssistEntryButtonDataInfo
                    }
            }
        }

        fun fetchUserProfile() {
            viewModelScope.launch {
                chargeAssistVMHelper.getFetchProfile()
            }
        }

        fun shouldShowDescription(
            enrollmentStatus: String,
            isEligibilityForCAEnrollment: Boolean?,
            systemZip: String?,
        ) {
            viewModelScope.launch {
                _shouldShowDescription.value =
                    shouldShowDescriptionLogic(
                        enrollmentStatus,
                        isEligibilityForCAEnrollment,
                        systemZip,
                    )
            }
        }

        private fun shouldShowDescriptionLogic(
            enrollmentStatus: String,
            isEligibilityForCAEnrollment: Boolean?,
            systemZip: String?,
        ): Boolean =
            when {
                // 1st Conditional: return false for these conditions
                enrollmentStatus.equals(UNENROLLMENT_SUCCESSFUL, ignoreCase = true) ||
                    enrollmentStatus in UNENROLLMENT_PENDING_STATUSES ||
                    enrollmentStatus in ENROLLMENT_SUCCESSFUL_STATUSES ||
                    enrollmentStatus in ENROLLMENT_PENDING_STATUSES ||
                    (
                        enrollmentStatus in ENROLLMENT_FAILURE_STATUSES &&
                            oneAppPreferenceModel.getVisitCountForEnrollmentFailure() <= 2
                    ) -> false

                // 2nd Conditional: return true for these conditions
                (isEligibilityForCAEnrollment == false && enrollmentStatus == NOT_FOUND) ||
                    isEligibilityForCAEnrollment == false ||
                    (
                        systemZip.isNullOrEmpty() &&
                            (
                                oneAppPreferenceModel.getVisitCount() >= 1 ||
                                    oneAppPreferenceModel.getVisitCount() >= 2
                            ) &&
                            chargeAssistVMHelper.isZipEnteredOnEligibilityScreenValid.value == false
                    ) -> true

                // default value
                else -> false
            }

        internal fun loadVisitCount() {
            viewModelScope.launch {
                flow {
                    emit(oneAppPreferenceModel.getVisitCount())
                }.catch {
                    _visitCountForZipInvalid.value = 0
                }.collect { visitCount ->
                    _visitCountForZipInvalid.value = visitCount
                }
            }

            viewModelScope.launch {
                flow {
                    emit(oneAppPreferenceModel.getVisitCountForEnrollmentFailure())
                }.catch {
                    _visitCountForEnrollmentFail.value = 0
                }.collect { enrollmentFailCount ->
                    _visitCountForEnrollmentFail.value = enrollmentFailCount
                }
            }
        }

        fun incrementVisitCount() {
            viewModelScope.launch {
                oneAppPreferenceModel.incrementVisitCount()
                _visitCountForZipInvalid.value =
                    oneAppPreferenceModel.getVisitCount() // Update state immediately
            }
        }

        fun incrementVisitCountForEnrollmentFailure() {
            viewModelScope.launch {
                oneAppPreferenceModel.incrementVisitCountForEnrollmentFailure()
                _visitCountForEnrollmentFail.value =
                    oneAppPreferenceModel.getVisitCountForEnrollmentFailure() // Update state immediately
            }
        }

        fun resetCounter() {
            viewModelScope.launch {
                oneAppPreferenceModel.resetVisitCount()
                _visitCountForZipInvalid.value = 0 // Reset UI state immediately
                _visitCountForEnrollmentFail.value = 0
                oneAppPreferenceModel.resetZipValidity(chargeAssistVMHelper.grabGuid().toString())
            }
        }

        fun isUserEligibleForCAEnrollmentAndEnrollmentStatus() {
            viewModelScope.launch {
                chargeAssistUseCase
                    .fetchUserEligibilityForCAEnrollmentAndEnrollmentStatus()
                    .catch {
                        _isUserEligibilityForCAEnrollment.value = false
                    }.collect {
                        _isUserEligibilityForCAEnrollment.value = it.first
                        _userEnrollmentStatusDataString.value = it.second
                    }
            }
        }

        fun fetchEnrollmentStatus() {
            viewModelScope.launch {
                chargeAssistUseCase
                    .fetchEnrollmentStatus()
                    .catch {
                        _enrollmentStatusData.value = null
                    }.collect {
                        when (it) {
                            is Resource.Failure<ApiResponse<EnrollmentCheckResponsePayload?>?> -> {
                                _enrollmentStatusData.value = null
                            }

                            is Resource.Success<ApiResponse<EnrollmentCheckResponsePayload?>?> -> {
                                _enrollmentStatusData.value = it
                            }

                            else -> { // left intentionally blank
                            }
                        }
                    }
            }
        }

        fun loadBaseData() {
            viewModelScope.launch {
                fetchProcessStatus()
                loadVisitCount()
                chargeAssistVMHelper.loadZipValidity()
                chargeAssistVMHelper.isChargeAssistActive()
            }
        }

        fun fetchEligibilityForZipOnUnavailableScreen(zip: String?) {
            _message.value = ""
            viewModelScope.launch {
                val userId = chargeAssistVMHelper.grabGuid()

                if (zip.isNullOrEmpty()) {
                    chargeAssistVMHelper.resetZipEligibilityToNull()
                    chargeAssistVMHelper.setZipEnteredOnEligibilityScreenValid(userId.toString(), null)
                } else {
                    chargeAssistUseCase
                        .fetchZNAZipEligibility(zip.toString())
                        .collect { zipEligibilityInfoForEnteredZip ->
                            when (zipEligibilityInfoForEnteredZip) {
                                is Resource.Success<*> -> {
                                    when {
                                        zipEligibilityInfoForEnteredZip.data?.payload?.isNotEmpty() == true -> {
                                            _zipEligibilityStatusDataForUnavailableScreenZip.value =
                                                zipEligibilityInfoForEnteredZip
                                            _message.value = "$zip is eligible for enrollment"
                                            _showEligibilityBottomSheet.emit(false)

                                            chargeAssistVMHelper
                                                .setZipEnteredOnEligibilityScreenValid(userId.toString(), true)
                                        }

                                        zipEligibilityInfoForEnteredZip.data?.payload?.isEmpty() == true -> {
                                            _zipEligibilityStatusDataForUnavailableScreenZip.value =
                                                zipEligibilityInfoForEnteredZip
                                            _message.value = "$zip is not eligible for enrollment"
                                            _showEligibilityBottomSheet.emit(true)

                                            chargeAssistVMHelper
                                                .setZipEnteredOnEligibilityScreenValid(userId.toString(), false)
                                        }

                                        zipEligibilityInfoForEnteredZip.data?.payload?.isEmpty() == null -> {
                                            _message.value =
                                                "Eligibility Check not available at this time. Try again later"
                                        } else -> {
                                            chargeAssistVMHelper.setZipEnteredOnEligibilityScreenValid(
                                                userId
                                                    .toString(),
                                                null,
                                            )
                                        }
                                    }
                                }

                                is Resource.Failure<*> -> { // left intentionally blank
                                    LogTool.d(
                                        "Eligibility for Unavailable Screen",
                                        "Something went wrong with the API call",
                                    )
                                    _message.value = "Problem with the API"
                                }

                                else -> { // left intentionally blank
                                }
                            }
                        }
                }
            }
        }

        fun fetchZipEligibilityStatus() {
            viewModelScope.launch {
                chargeAssistUseCase
                    .fetchZipEligibilityStatus()
                    .catch {
                        _zipEligibilityStatusData.value = null
                    }.collect {
                        _zipEligibilityStatusData.value = it
                    }
            }
        }

        fun fetchEnrollmentOfUserIntoChargeAssistResponse(
            address: String,
            city: String,
            state: String,
            zip: String,
            utilityAccountNumber: String,
        ) {
            viewModelScope.launch {
                chargeAssistUseCase
                    .enrollAUserIntoCA(address, city, state, zip, utilityAccountNumber)
                    .collect {
                        when (it) {
                            is Resource.Success<ApiResponse<EnrollmentResponsePayload?>?> -> {
                                _userEnrollmentStatusData.value = it
                            }

                            is Resource.Failure<ApiResponse<EnrollmentResponsePayload?>?> -> {
                                _userEnrollmentStatusData.value = it
                            }

                            else -> { // left intentionally blank
                            }
                        }
                    }
            }
        }

        fun unEnrollAUser() {
            viewModelScope.launch {
                chargeAssistUseCase.unEnrollAUserFromCA().collect {
                    _unEnrollmentStatusData.value = it?.data
                }
            }
        }

        fun fetchProcessStatus() {
            viewModelScope.launch {
                chargeAssistUseCase
                    .fetchProcessStatus()
                    .catch {
                        _processStatusData.value = null
                    }.collect {
                        _processStatusData.value = it
                    }
            }
        }

        fun fetchIsInProgressStatusOver5min(processString: String?) {
            viewModelScope.launch {
                chargeAssistUseCase
                    .fetchIsProgressStatusOver5Minutes(processString)
                    .catch {
                        _isInProgressStatusOver5min.value = false
                    }.collect {
                        _isInProgressStatusOver5min.value = it
                        if (it) {
                            delay(FIVE_MINUTES)
                            fetchIsInProgressStatusOver5min(processString)
                        }
                    }
            }
        }

        fun updateOfficialAddress(
            address: String,
            city: String,
            state: String,
            zip: String,
            userName: String,
        ) {
            viewModelScope.launch {
                chargeAssistUseCase
                    .updateAddressOfficial(
                        address,
                        city,
                        state,
                        zip,
                        userName,
                    ).collect()
            }
        }

        fun getChargeAssistBackEndFlag() {
            viewModelScope.launch {
                chargeAssistUseCase
                    .isCABackEndFlagOn()
                    .collect { isCABackEndFlagOnInfo ->
                        _isCABackEndFlagOn.value = isCABackEndFlagOnInfo
                    }
            }
        }

        fun shouldCAEntryCardDisplay(
            currentEnrollmentStatus: String,
            isEligibilityForCAEnrollment: Boolean,
            systemZip: String?,
        ) {
            viewModelScope.launch {
                chargeAssistVMHelper.shouldCAEntryCardDisplay(
                    currentEnrollmentStatus,
                    isEligibilityForCAEnrollment,
                    systemZip,
                )
            }
        }
        // Method to update the state
    }
