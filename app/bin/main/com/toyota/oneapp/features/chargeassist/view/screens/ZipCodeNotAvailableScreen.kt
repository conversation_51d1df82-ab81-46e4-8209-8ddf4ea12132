package com.toyota.oneapp.features.chargeassist.view.screens

import android.content.Context
import android.util.Log
import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.features.chargeassist.ChargeAssistRoute
import com.toyota.oneapp.features.chargeassist.view.helper.ChargeAssistBackButtonTitleHeader
import com.toyota.oneapp.features.chargeassist.view.helper.ChargeAssistCommonSheetContent
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.chargeinfo.navigation.ChargeInfoRoute
import com.toyota.oneapp.features.core.composable.OABody1TextView
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.composable.OAButtonTextView
import com.toyota.oneapp.features.core.composable.OAHeadline1TextView
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import kotlinx.coroutines.launch

const val ALLOWED_NUMBER_OF_DIGITS_FOR_ZIP = 5

@Composable
fun ZipCodeNotAvailableScreen(
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
) {
    val context = LocalContext.current
    val userEnteredZipCode = remember { mutableStateOf("") }
    val sheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            skipHalfExpanded = true,
        )
    val coroutineScope = rememberCoroutineScope()
    val isEnabled = remember { mutableStateOf(false) }
    val message by chargeAssistViewModel.message.collectAsState()
    val isZipEnteredOnEligibilityScreenValid by chargeAssistViewModel.chargeAssistVMHelper
        .isZipEnteredOnEligibilityScreenValid.collectAsState()

    // Track submission button click
    var hasSubmitted by remember { mutableStateOf(false) }
    // Get keyboard controller
    val keyboardController = LocalSoftwareKeyboardController.current

    LaunchedEffect(Unit) {
        chargeAssistViewModel.showEligibilityBottomSheet.collect { state ->
            if (hasSubmitted) {
                if (state) {
                    coroutineScope.launch {
                        sheetState.show()
                        keyboardController?.hide()
                    }
                } else if (!state) {
                    Toast.makeText(context, "ZIP is Eligible", Toast.LENGTH_LONG).show()
                    // pass zip to Consent screen
                    navController.navigate(ChargeAssistRoute.ConsentScreen.route + "/${userEnteredZipCode.value}")
                }
                hasSubmitted = false
            }
        }
    }

    Box {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            ChargeAssistBackButtonTitleHeader(
                navController,
                true,
                onBackClick = {
                    coroutineScope.launch {
                        chargeAssistViewModel.chargeAssistVMHelper.resetZipEligibilityToNull()
                    }
                },
            )
            ZipNotAvailableHeadlineAndDescription(context)

            ZipCodeEntryTextBox(userEnteredZipCode, isEnabled)

            Spacer(modifier = Modifier.weight(1f)) // This takes up remaining space
            SubmitButton(
                isButtonEnabled = isEnabled.value,
                // navController = navController,
                click = {
                    hasSubmitted = true
                    Log.d("ZIP_DEBUG", "ZIP Code Submitted: ${userEnteredZipCode.value}")
                    chargeAssistViewModel.fetchEligibilityForZipOnUnavailableScreen(
                        userEnteredZipCode.value,
                    )
                    chargeAssistViewModel.chargeAssistVMHelper
                        .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_ZIP_SUBMIT_BUTTON)
                },
            )
        }

        ZipNotAvailableBottomSheet(
            text = "$message",
            isZipEnteredOnEligibilityScreenValid = isZipEnteredOnEligibilityScreenValid,
            sheetState = sheetState,
            navController,
        )
    }

    BackHandler {
        if (sheetState.isVisible) {
            coroutineScope.launch { sheetState.hide() }
        } else {
            coroutineScope.launch {
                chargeAssistViewModel.chargeAssistVMHelper.resetZipEligibilityToNull()
            }
            navController.popBackStack() // Navigate back to the previous page
        }
    }
}

@Composable
fun ZipNotAvailableHeadlineAndDescription(context: Context) {
    OAHeadline1TextView(
        modifier =
            Modifier
                .semantics {
                    contentDescription =
                        context.getString(
                            R.string.please_enter_your_zip_code_to_check_program_text,
                        )
                }
                .padding(top = 15.dp, start = 16.dp, end = 16.dp, bottom = 32.dp)
                .fillMaxWidth()
                .wrapContentHeight(),
        text = stringResource(R.string.please_enter_your_zip_code_to_check_program_text),
        color = AppTheme.colors.tertiary03,
    )

    OABody1TextView(
        text = stringResource(R.string.your_charging_location_must_match_billing_address),
        color = AppTheme.colors.tertiary03,
        modifier =
            Modifier
                .semantics {
                    contentDescription =
                        context.getString(
                            R.string.your_charging_location_must_match_billing_address,
                        )
                }
                .padding(vertical = 16.dp)
                .padding(start = 16.dp, end = 18.dp)
                .fillMaxWidth()
                .height(46.dp),
    )
}

@Composable
fun ZipCodeEntryTextBox(
    userEnteredZipCode: MutableState<String>,
    isEnabled: MutableState<Boolean>,
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current

    Box(
        modifier =
            Modifier
                .wrapContentHeight()
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = {
                            // Hide the keyboard and clear focus when tapping outside
                            focusManager.clearFocus()
                            keyboardController?.hide()
                        },
                    )
                },
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            OutlinedTextField(
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                value = userEnteredZipCode.value,
                textStyle =
                    TextStyle(
                        color = AppTheme.colors.tertiary03,
                        fontSize = 16.sp,
                        fontFamily = FontFamily(Font(com.toyota.one_ui.R.font.toyotatype_regular)),
                    ),
                onValueChange = {
                    if (it.all { char -> char.isDigit() } && it.length <= 5) { // only digits can be entered
                        userEnteredZipCode.value = it
                        // Enable button when text entered is of a valid length
                        isEnabled.value =
                            userEnteredZipCode.value.length == ALLOWED_NUMBER_OF_DIGITS_FOR_ZIP
                    }
                },
                placeholder = {
                    OABody3TextView(
                        text = stringResource(R.string.zip_code),
                        color = AppTheme.colors.tertiary07,
                    )
                },
                modifier =
                    Modifier
                        .onFocusChanged { focusState ->
                            if (!focusState.isFocused) {
                                // hid keyboard when focus is lost
                                keyboardController?.hide()
                            }
                        }
                        .semantics { contentDescription = "Enter Zip code text box" }
                        .padding(top = 32.dp)
                        .padding(start = 16.dp, end = 16.dp)
                        .fillMaxWidth()
//                        .width(343.dp)
                        .height(56.dp),
                colors =
                    TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = AppTheme.colors.outline01,
                        unfocusedBorderColor = AppTheme.colors.outline01,
                        textColor = AppTheme.colors.tertiary07,
                        placeholderColor = AppTheme.colors.tertiary07,
                    ),
            )
        }
    }
}

@Composable
fun SubmitButton(
    modifier: Modifier = Modifier,
    isButtonEnabled: Boolean,
    click: () -> Unit,
) {
    val buttonEnableStatusText =
        if (isButtonEnabled) "Submit Button enabled" else "Submit Button disabled"

    Button(
        onClick = {
            click.invoke()
        },
        shape = RoundedCornerShape(100.dp),
        colors =
            ButtonDefaults.buttonColors(
                backgroundColor = AppTheme.colors.button02a,
                disabledBackgroundColor = AppTheme.colors.button02d,
            ),
        contentPadding = PaddingValues(vertical = 16.dp, horizontal = 60.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .semantics { contentDescription = buttonEnableStatusText }
                .padding(horizontal = 91.dp)
                .padding(bottom = 32.dp),
        enabled = isButtonEnabled,
    ) {
        OAButtonTextView(
            text = stringResource(R.string.submit),
            color = if (isButtonEnabled) AppTheme.colors.primaryButton01 else AppTheme.colors.button05a,
        )
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ZipNotAvailableBottomSheet(
    text: String,
    isZipEnteredOnEligibilityScreenValid: Boolean?,
    sheetState: ModalBottomSheetState,
    navController: NavController,
) {
    ModalBottomSheetLayout(
        sheetBackgroundColor = AppTheme.colors.tertiary15,
        modifier = Modifier.fillMaxWidth(),
        sheetShape =
            RoundedCornerShape(
                topStart = 16.dp,
                topEnd = 16.dp,
                bottomEnd = 0.dp,
                bottomStart = 0.dp,
            ),
        sheetState = sheetState,
        sheetContent = {
            // Bottom sheet content
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(486.79803.dp),
            ) {
                if (text.contains("is not eligible for enrollment")) {
                    showIneligibleZipBottomSheetContent(
                        navController,
                        isZipEnteredOnEligibilityScreenValid,
                    )
                } else if (text.contains("is eligible for enrollment")) {
                    // will not show bottom sheet
                    Toast.makeText(LocalContext.current, "Zip is Eligible", Toast.LENGTH_LONG)
                } else { // left intentionally blank
                }
            }
        },
    ) { /* main content - left intentionally blank*/ }
}

@Composable
fun showIneligibleZipBottomSheetContent(
    navController: NavController,
    isZipEnteredOnEligibilityScreenValid: Boolean?,
) {
    val previousRoute = navController.previousBackStackEntry?.destination?.route
    ChargeAssistCommonSheetContent(
        title = "Zip Code Not Eligible",
        descriptionTexts =
            listOf(
                stringResource(R.string.charge_assist_not_available_in_your_area),
            ),
        buttonText = stringResource(R.string.back_to_charge_info),
        buttonPaddingTop = 140.dp,
        onButtonClick = {
            when {
                previousRoute == OAScreen.ChargeInfo.route ||
                    previousRoute?.contains(ChargeAssistRoute.LearnMoreScreen.route) == true ||
                    previousRoute?.contains("charge_info") == true ||
                    previousRoute?.contains("charge_schedule_nested_route") == true -> {
                    navController.navigate(
                        ChargeInfoRoute.ChargeInfoScreen.route +
                            "?isZipFromEligibilityScreenValid=$isZipEnteredOnEligibilityScreenValid",
                    )
                }
            }
        },
    )
}

@Composable
fun ChargeAssistBottomSheetLogo(modifier: Modifier = Modifier) {
    Box(
        modifier =
            modifier
                .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
                .size(48.dp),
        contentAlignment = Alignment.Center,
    ) {
        Image(
            modifier =
                Modifier
                    .matchParentSize(),
            painter = painterResource(id = R.drawable.circle),
            contentDescription = "Circle image",
            contentScale = ContentScale.Crop,
            colorFilter = ColorFilter.tint(AppTheme.colors.primary02),
        )

        Image(
            modifier =
                Modifier
                    .size(32.dp),
            // Sizes the check image
            painter = painterResource(id = R.drawable.charge_assist_zip_unavailable_logo_home),
            contentDescription = "charge assist logo",
            colorFilter = ColorFilter.tint(AppTheme.colors.primary01),
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewZipCodeNotAvailableScreen() {
    val navController = rememberNavController()
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()
    ZipCodeNotAvailableScreen(navController = navController, chargeAssistViewModel)
}
