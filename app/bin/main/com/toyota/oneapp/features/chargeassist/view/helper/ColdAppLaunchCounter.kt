package com.toyota.oneapp.features.chargeassist.view.helper

import android.content.SharedPreferences

class ColdAppLaunchCounter(private val prefs: SharedPreferences) {
    // Keys for SharedPreferences
    companion object {
        const val LAUNCH_COUNT_KEY = "launch_count"
        const val ANNOUNCEMENT_SHOWN_KEY = "announcement_shown"
    }

    // keep track of launch count
    fun getLaunchCount(): Int {
        return prefs.getInt(LAUNCH_COUNT_KEY, 0)
    }

    fun incrementLaunchCount() {
        val currentCount = getLaunchCount()
        prefs.edit().putInt(LAUNCH_COUNT_KEY, currentCount + 1).apply()
    }

    // reset the launch count
    fun resetLaunchCount() {
        prefs.edit().putInt(LAUNCH_COUNT_KEY, 0).apply()
    }

    // Check if the announcement has been shown
    fun hasShownAnnouncement(): Bo<PERSON>an {
        return prefs.getBoolean(ANNOUNCEMENT_SHOWN_KEY, false)
    }

    // Set that the announcement has been shown
    fun setAnnouncementShown() {
        prefs.edit().putBoolean(ANNOUNCEMENT_SHOWN_KEY, true).apply()
    }

    // resets the announcement flag back to original value
    fun resetAnnouncementFlag() {
        prefs.edit().putBoolean(ANNOUNCEMENT_SHOWN_KEY, false).apply()
    }
}
