/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */
package com.toyota.oneapp.features.chargeassist.view.screens.entrypoint2

import android.content.Context
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.features.chargeassist.ChargeAssistRoute
import com.toyota.oneapp.features.chargeassist.view.helper.ChargeAssistCommonSheetContent
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.AGGREGATOR_UNENROLLMENT_FAILED
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_SUCCESSFUL_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.NOT_FOUND
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.UNENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.UNENROLLMENT_SUCCESSFUL
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistToggleCardParams
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistWidgetNamesState.Companion.SCHEDULE_SCREEN_ENROLLMENT_FAILURE
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistWidgetNamesState.Companion.SCHEDULE_SCREEN_INVALID_ZIP_WIDGET
import com.toyota.oneapp.features.chargeassist.view.viewmodel.CHARGE_ASSIST_SWITCH
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistExtensionViewModel
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.chargeinfo.presentation.ChargeInfoViewModel
import com.toyota.oneapp.features.core.composable.CustomSwitch
import com.toyota.oneapp.features.core.composable.OAButtonTextView
import com.toyota.oneapp.features.core.composable.OAFootNote1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dashboard.dashboard.presentation.DashboardViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@Composable
fun ChargeAssistScheduleCardComposable(
    chargeAssistToggleCardParams: ChargeAssistToggleCardParams,
    navController: NavHostController,
    chargeAssistViewModel: ChargeAssistViewModel,
    chargeInfoViewModel: ChargeInfoViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    val dashboardViewModel: DashboardViewModel = hiltViewModel()
    val isEligibleForCAEnrollment by chargeAssistViewModel.isUserEligibilityForCAEnrollment.collectAsState()
    val profileData by dashboardViewModel.profileData.collectAsState() // if this is null then the api is down
    val systemZip = profileData?.payload?.customer?.addresses?.getOrNull(0)?.zipCode
    val currentEnrollmentStatus by chargeAssistViewModel.userEnrollmentStatusDataString.collectAsState()
    val chargeAssistEntryButtonData by chargeAssistViewModel.chargeAssistEntryButtonData.collectAsState()
    val shouldCAEntryCardBEDisplayed by chargeAssistViewModel
        .chargeAssistVMHelper.shouldCAEntryCardBeDisplayed.collectAsState()
    val shouldShowInvalidZipCardBeDisplayed by chargeAssistViewModel.chargeAssistVMHelper
        .shouldInvalidWidgetBeDisplayed.collectAsState()
    val shouldEnrollmentFailureCardBeDisplayed by chargeAssistViewModel.chargeAssistVMHelper
        .shouldShowEnrollmentFailureWidgetBeDisplayed.collectAsState()

    // Real Data
    val processScheduleStatusCollect by chargeAssistViewModel.processStatusData.collectAsState()
    val processScheduleStatus = processScheduleStatusCollect?.data?.payload?.processStatus
    val fetchIsInProgressStatusOver5min by chargeAssistViewModel.isInProgressStatusOver5min.collectAsState()
    val pluggedInTime by chargeAssistViewModel.chargeAssistVMHelper.pluggedInTime.collectAsState()
    val isEVPluggedIN by chargeAssistViewModel.chargeAssistVMHelper.isEVPluggedIN.collectAsState()
    val isEVCharging by chargeAssistViewModel.chargeAssistVMHelper.isEVCharging.collectAsState()
    val processStatusEndTime = processScheduleStatusCollect?.data?.payload?.chargeEndTime

    LaunchedEffect(currentEnrollmentStatus, isEligibleForCAEnrollment) {
        chargeAssistViewModel.fetchUserProfile()
        chargeAssistViewModel.getChargeAssistEntryButtonData(isEligibleForCAEnrollment)
        // chargeAssistViewModel.isUserEligibleForCAEnrollmentAndEnrollmentStatus() // in Higher level screen
        chargeAssistViewModel.fetchIsInProgressStatusOver5min(processScheduleStatus)
        chargeAssistViewModel.loadBaseData()

        chargeAssistViewModel.chargeAssistVMHelper.shouldShowInvalidZipCard(
            enrollmentStatus = currentEnrollmentStatus.toString(),
            widgetName = SCHEDULE_SCREEN_INVALID_ZIP_WIDGET,
            systemZip = systemZip,
        )
        chargeAssistViewModel.chargeAssistVMHelper.shouldShowEnrollmentFailureCard(
            currentEnrollmentStatus.orEmpty(),
            SCHEDULE_SCREEN_ENROLLMENT_FAILURE,
        )
        chargeAssistViewModel.chargeAssistVMHelper.grabRealEVChargeDataRaw(
            chargeInfo = chargeInfoViewModel.chargeInfoState.value,
            processStatusEndTime = processStatusEndTime,
            pluggedInTime = pluggedInTime,
        )
    }

    when {
        shouldCAEntryCardBEDisplayed ->
            ScheduleChargeAssistEntry(
                navController,
                currentEnrollmentStatus.orEmpty(),
                context,
                chargeAssistEntryButtonData,
            )

        // Invalid Zip Card and disappear after 1 visit
        // Enrollment Failure Status Card and disappear after 2 visits
        shouldShowInvalidZipCardBeDisplayed || shouldEnrollmentFailureCardBeDisplayed ->
            ScheduleDescriptionOnlyCARD(
                navController,
                context,
                currentEnrollmentStatus.toString(),
                processScheduleStatus,
                isEVPluggedIN,
                isEVCharging,
                fetchIsInProgressStatusOver5min,
            )

        currentEnrollmentStatus in
            listOf(AGGREGATOR_UNENROLLMENT_FAILED) +
            UNENROLLMENT_PENDING_STATUSES + ENROLLMENT_SUCCESSFUL_STATUSES +
            ENROLLMENT_PENDING_STATUSES
        -> {
            ScheduleChargeAssistToggleCard(
                navController,
                chargeAssistToggleCardParams,
                systemZip,
                currentEnrollmentStatus,
                processScheduleStatus,
                isEVPluggedIN,
                isEVCharging,
            )
        }
    }
}

@Composable
fun ScheduleChargeAssistEntry(
    navController: NavHostController,
    currentEnrollmentStatus: String,
    context: Context,
    chargeAssistEntryButtonData: Pair<String, String>?,
) {
    Column {
        Card(
            shape = RoundedCornerShape(8.dp),
            elevation = 8.dp,
            modifier =
                Modifier
                    .background(AppTheme.colors.tile01)
                    .fillMaxWidth(),
            // .wrapContentHeight()
        ) {
            Column {
                ScheduleChargeAssistEntryContent(
                    navController,
                    context,
                    currentEnrollmentStatus,
                    chargeAssistEntryButtonData,
                )
                Box(
                    modifier =
                        Modifier
                            .background(AppTheme.colors.tile05)
                            .fillMaxWidth()
                            .height(56.dp),
                ) {
                    ScheduleLearnMoreEntry(navController)
                }
            }
        }
    }
}

@Composable
fun CAScheduleLogo(
    context: Context,
    currentEnrollmentStatus: String,
) {
    when (currentEnrollmentStatus) {
        "EnrollmentFailed", "RegistrationFailed", "AggregatorEnrollmentFailed" -> {
            Image(
                modifier =
                    Modifier
                        .semantics {
                            contentDescription = context.getString(R.string.charge_assist_logo)
                        }
                        .padding(end = 8.dp)
                        .padding(start = 16.dp)
                        .width(48.dp)
                        .height(48.dp),
                painter = painterResource(id = R.drawable.charge_assist_zip_unavailable_logo_home),
                contentDescription = "Charge Assist Logo",
                contentScale = ContentScale.Crop,
                colorFilter = ColorFilter.tint(AppTheme.colors.primary01),
            )
        }

        else -> {
            Image(
                modifier =
                    Modifier
                        .semantics {
                            contentDescription = context.getString(R.string.charge_assist_logo)
                        }
                        .padding(end = 8.dp)
                        .padding(start = 16.dp)
                        .width(48.dp)
                        .height(48.dp),
                painter = painterResource(id = R.drawable.charge_assist_logo_new_today),
                contentDescription = "Charge Assist Logo",
                contentScale = ContentScale.Crop,
                colorFilter = ColorFilter.tint(AppTheme.colors.secondary01),
            )
        }
    }
}

@Composable
fun ScheduleChargeAssistEntryContent(
    navController: NavHostController,
    context: Context,
    currentEnrollmentStatus: String,
    chargeAssistEntryButtonData: Pair<String, String>?,
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.Start),
        verticalAlignment = Alignment.CenterVertically,
        modifier =
            Modifier
                .semantics {
                    contentDescription = "Charge Assist Entry Tile"
                }
                .background(AppTheme.colors.tile01)
                .fillMaxWidth()
                .padding(top = 16.dp),
    ) {
        CAScheduleLogo(context, currentEnrollmentStatus)

        when {
            currentEnrollmentStatus.equals(NOT_FOUND, ignoreCase = true) ||
                currentEnrollmentStatus.equals(UNENROLLMENT_SUCCESSFUL, ignoreCase = true) -> {
                DisplayEnterEnrollCardContent(
                    navController,
                    chargeAssistEntryButtonData,
                    context,
                )
            }
        }
    }
}

@Composable
fun ScheduleDescriptionOnlyCARD(
    navController: NavHostController,
    context: Context,
    currentEnrollmentStatus: String,
    processStatus: String?,
    isEVPluggedIn: Boolean?,
    isEVCharging: Boolean?,
    fetchIsInProgressStatusOver5min: Boolean?,
) {
    val enrollmentDescriptionText =
        getEnrollmentStatusDescription(
            currentEnrollmentStatus,
            processStatus,
            isEVPluggedIN = isEVPluggedIn,
            isEVCharging = isEVCharging,
            fetchIsInProgressStatusOver5min,
        )

    Column {
        Card(
            shape = RoundedCornerShape(8.dp),
            elevation = 8.dp,
            modifier =
                Modifier
                    .background(AppTheme.colors.tile01)
                    .fillMaxWidth(),
        ) {
            Column {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.Start),
                    verticalAlignment = Alignment.CenterVertically,
                    modifier =
                        Modifier
                            .semantics {
                                contentDescription = "Charge Assist Entry Tile"
                            }
                            .background(AppTheme.colors.tile01)
                            .fillMaxWidth()
                            .padding(top = 16.dp),
                ) {
                    CAScheduleLogo(context, currentEnrollmentStatus)
                    DisplayContentAndDescription(enrollmentDescriptionText)
                }
                if (currentEnrollmentStatus !in
                    listOf(
                        "RegistrationFailed",
                        "AggregatorEnrollmentFailed",
                        "EnrollmentFailed",
                    )
                ) {
                    ScheduleLearnMoreEntry(navController)
                }
            }
        }
    }
}

@Composable
fun DisplayContentAndDescription(enrollmentStatusDescription: String) {
    Column(
        modifier =
            Modifier
                .fillMaxWidth(),
    ) {
        OASubHeadLine1TextView(
            modifier =
                Modifier
                    .padding()
                    .semantics { contentDescription = "Charge Assist Title" }
                    .height(27.dp),
            text = stringResource(R.string.charge_assist),
            color = AppTheme.colors.tertiary03,
        )

        OAFootNote1TextView(
            text = enrollmentStatusDescription,
            color = AppTheme.colors.tertiary05,
            textAlign = TextAlign.Left,
            modifier =
                Modifier
                    .wrapContentWidth()
                    .height(55.dp)
                    .semantics {
                        contentDescription = enrollmentStatusDescription
                    },
        )
    }
}

@Composable
fun DisplayEnterEnrollCardContent(
    navController: NavHostController,
    chargeAssistEntryButtonData: Pair<String, String>?,
    context: Context,
) {
    val (buttonText, route) = chargeAssistEntryButtonData ?: Pair("", "")
    Column(
        modifier = Modifier,
    ) {
        OASubHeadLine1TextView(
            modifier =
                Modifier
                    .padding()
                    .semantics { contentDescription = "Charge Assist Title" }
                    .height(27.dp),
            text = stringResource(R.string.charge_assist),
            color = AppTheme.colors.tertiary03,
        )

        if (buttonText == "Enroll") {
            Text(
                modifier =
                    Modifier
                        .padding(top = 2.dp)
                        .semantics { contentDescription = "A new program is available" },
                text = stringResource(R.string.program_is_available),
                style =
                    TextStyle(
                        fontSize = 12.sp,
                        lineHeight = 18.sp,
                        fontFamily = FontFamily(Font(com.toyota.one_ui.R.font.toyotatype_regular)),
                    ),
                fontWeight = FontWeight(PROGRAM_AVAILABLE_TEXT_WEIGHT),
                color = AppTheme.colors.tertiary05,
            )

            OAFootNote1TextView(
                text = stringResource(R.string.enroll_to_for_incentives_text),
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Left,
                modifier =
                    Modifier
                        .width(176.dp)
                        .height(55.dp)
                        .semantics {
                            contentDescription = context.getString(R.string.enroll_to_for_incentives_text)
                        },
            )
        } else if (buttonText == "Enter") {
            OAFootNote1TextView(
                text = stringResource(id = R.string.enter_zip_to_check_CA_program_availability),
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Left,
                modifier =
                    Modifier
                        .width(160.dp)
                        .height(55.dp)
                        .semantics {
                            contentDescription =
                                context.getString(
                                    R.string.enter_zip_to_check_CA_program_availability,
                                )
                        },
            )
        }
    }

    EnrollmentOutlinedButtonWithNav(navController, route, buttonText)
}

@Composable
fun EnrollmentOutlinedButtonWithNav(
    navController: NavHostController,
    route: String,
    buttonText: String,
) {
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()
    val buttonStyle =
        Modifier
            .padding(end = 6.dp)
            .semantics { contentDescription = "Charge Assist $buttonText Button" }
            .defaultMinSize(100.dp)
            .height(36.dp)
            .wrapContentWidth(Alignment.CenterHorizontally)

    val textStyle =
        TextStyle(
            fontSize = 14.sp,
            lineHeight = 20.sp,
            fontFamily = FontFamily(Font(com.toyota.one_ui.R.font.toyotatype_regular)),
            fontWeight = FontWeight(ENROLL_BUTTON_TEXT_WEIGHT),
            color = AppTheme.colors.tertiary03,
        )
    OutlinedButton(
        onClick = {
            when (buttonText) {
                "Enroll" ->
                    chargeAssistViewModel.chargeAssistVMHelper
                        .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_SCHEDULE_ENROLL)

                "Enter" ->
                    chargeAssistViewModel.chargeAssistVMHelper
                        .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_CHARGE_SCHEDULE_ENTER)
            }
            navController.navigate(route)
        },
        modifier = buttonStyle,
        border = BorderStroke(2.dp, AppTheme.colors.outline01),
        shape = RoundedCornerShape(100.dp),
        colors = ButtonDefaults.outlinedButtonColors(backgroundColor = Color.Transparent),
    ) {
        Text(
            text = buttonText,
            style = textStyle,
            maxLines = 1,
            softWrap = false,
            overflow = TextOverflow.Visible,
        )
    }
}

@Composable
fun ScheduleChargeAssistToggleCard(
    navController: NavHostController,
    chargeAssistToggleCardParams: ChargeAssistToggleCardParams,
    zip: String?,
    currentEnrollmentStatus: String?,
    currentProcessStatus: String?,
    isEVPluggedIn: Boolean? = false,
    isEVCharging: Boolean? = false,
) {
    val enrollmentStatusDescription: String
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()

    when (currentEnrollmentStatus) {
        in ENROLLMENT_SUCCESSFUL_STATUSES ->
            enrollmentStatusDescription =
                getEnrollmentStatusDescription(
                    currentEnrollmentStatus,
                    currentProcessStatus,
                    isEVPluggedIN = isEVPluggedIn,
                    isEVCharging = isEVCharging,
                    fetchIsInProgressStatusOver5min = chargeAssistToggleCardParams.fetchIsInProgressStatusOver5min,
                )

        else ->
            enrollmentStatusDescription =
                getEnrollmentStatusDescription(
                    currentEnrollmentStatus,
                    currentProcessStatus,
                    isEVPluggedIN = isEVPluggedIn,
                    isEVCharging = isEVCharging,
                )
    }

    MainToggleCardContent(
        currentEnrollmentStatus,
        enrollmentStatusDescription,
        chargeAssistToggleCardParams,
        chargeAssistViewModel,
        navController,
        zip,
    )
}

@Composable
fun MainToggleCardContent(
    currentEnrollmentStatus: String?,
    enrollmentStatusDescription: String,
    chargeAssistToggleCardParams: ChargeAssistToggleCardParams,
    chargeAssistViewModel: ChargeAssistViewModel,
    navController: NavHostController,
    zip: String?,
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    Card(
        backgroundColor = AppTheme.colors.tile01,
        shape = RoundedCornerShape(8.dp),
        elevation = 8.dp,
        modifier = Modifier.wrapContentHeight(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                CAScheduleLogo(context, currentEnrollmentStatus.toString())
                Column(
                    modifier =
                        Modifier
                            .wrapContentWidth()
                            .wrapContentHeight()
                            .padding(top = 16.dp, bottom = 16.dp)
                            .padding(start = 8.dp),
                ) {
                    OASubHeadLine1TextView(
                        text = stringResource(R.string.charge_assist),
                        color = AppTheme.colors.tertiary03,
                    )

                    // Status Description Text
                    OAFootNote1TextView(
                        modifier =
                            Modifier
                                .width(188.dp)
                                .height(36.dp),
                        text = enrollmentStatusDescription,
                        color = AppTheme.colors.tertiary05,
                        textAlign = TextAlign.Left,
                    )
                }
                Spacer(modifier = Modifier.weight(1f))
                DisplayPendingOrEnrollmentToggleCard(
                    currentEnrollmentStatus,
                    chargeAssistToggleCardParams,
                    chargeAssistViewModel,
                    coroutineScope,
                )
            }

            Box {
                Column {
                    // Show Account Details and LearnMore Entry
                    CAScheduleAccountDetails(navController, zip, currentEnrollmentStatus)
                    ScheduleLearnMoreEntry(navController)
                }
            }
        }
    }
}

@Composable
fun DisplayPendingOrEnrollmentToggleCard(
    currentEnrollmentStatus: String?,
    chargeAssistToggleCardParams: ChargeAssistToggleCardParams,
    chargeAssistViewModel: ChargeAssistViewModel,
    coroutineScope: CoroutineScope,
) {
    when (currentEnrollmentStatus) {
        in ENROLLMENT_PENDING_STATUSES, in UNENROLLMENT_PENDING_STATUSES -> {
            // Display the switch but keep it disabled
            CustomSwitch(
                modifier =
                    Modifier
                        .padding(8.dp)
                        .padding(end = 16.dp),
                onCheckedChange = {},
                isEnabled = false,
                isClickable = false,
                testTagId = "",
                shouldEnableCADisableColor = true,
            )
        }

        in ENROLLMENT_SUCCESSFUL_STATUSES -> {
            // Bottom sheet visibility state
            var showCAToggleOffBottomSheet by remember { mutableStateOf(false) }
            val chargeAssistExtensionViewModel: ChargeAssistExtensionViewModel = hiltViewModel()

            val toggleStatus by chargeAssistExtensionViewModel.currentToggleStatus.collectAsState()

            CustomSwitch(
                isEnabled =
                    chargeAssistToggleCardParams.isEnabled ||
                        toggleStatus,
                isClickable = chargeAssistToggleCardParams.isClickable,
                onCheckedChange = { checked ->
                    // Toggle and checked for new implementation
                    if (checked) {
                        chargeAssistViewModel.chargeAssistVMHelper.toggleSwitch(CHARGE_ASSIST_SWITCH)
                        chargeAssistViewModel.chargeAssistVMHelper.setActiveSwitchValue(CHARGE_ASSIST_SWITCH)
                        chargeAssistExtensionViewModel.updateToggleStatus("toggle_on")
                        chargeAssistViewModel.chargeAssistVMHelper
                            .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_TOGGLE_ON)
                    } else {
                        chargeAssistViewModel.chargeAssistVMHelper.toggleSwitch("")

                        chargeAssistExtensionViewModel.updateToggleStatus("toggle_off")
                        chargeAssistViewModel.chargeAssistVMHelper
                            .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_TOGGLE_OFF)
                        showCAToggleOffBottomSheet = true
                    }
                },
                modifier = Modifier.padding(end = 16.dp),
                shouldEnableCADisableColor = true,
                testTagId = "",
            )

            if (showCAToggleOffBottomSheet) {
                LaunchedEffect(Unit) {
                    coroutineScope.launch {
                        chargeAssistToggleCardParams.sheetState.show()
                    }
                }
                showCAToggleOffBottomSheet = false
            }
        }
    }
}

@Composable
fun ChargeAssistSwitchOffWithBottomSheetContent(
    coroutineScope: CoroutineScope,
    bottomSheetState: ModalBottomSheetState,
) {
    ChargeAssistCommonSheetContent(
        title = "Turning off Charge Assist",
        descriptionTexts =
            listOf(
                stringResource(R.string.switch_off_charge_assist_warning_text),
                stringResource(R.string.unenroll_from_managed_charging_program_text),
            ),
        buttonText = stringResource(R.string.Common_confirm),
        buttonPaddingTop = 70.dp,
        onButtonClick = {
            coroutineScope.launch { bottomSheetState.hide() }
        },
    )
}

@Composable
fun ChargeAssistSwitchOffWithBottomSheet(bottomSheetState: ModalBottomSheetState) {
    val coroutineScope = rememberCoroutineScope()
    ModalBottomSheetLayout(
        sheetBackgroundColor = AppTheme.colors.tertiary15,
        sheetShape =
            RoundedCornerShape(
                topStart = 16.dp,
                topEnd = 16.dp,
                bottomEnd = 0.dp,
                bottomStart = 0.dp,
            ),
        sheetState = bottomSheetState,
        sheetContent = {
            Box(
                modifier = Modifier.height(486.dp),
            ) {
                ChargeAssistSwitchOffWithBottomSheetContent(
                    coroutineScope = coroutineScope,
                    bottomSheetState = bottomSheetState,
                )
            }
        },
    ) {}
}

// ============ line duplication issue below
@Composable
fun CAScheduleAccountDetails(
    navController: NavHostController,
    zip: String?,
    currentUserEnrollmentStatus: String?,
) {
    ChargeAssistScheduleEntryTileComponent(
        title = "Account Details",
        onClick = {
            navController.navigate(
                ChargeAssistRoute.EnrollmentUtilityProgramScreen.route +
                    "/$zip/${false}/$currentUserEnrollmentStatus",
            )
        },
        contentDescription = "Account details arrow button",
    )
}

@Composable
fun ScheduleLearnMoreEntry(navController: NavHostController) {
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()

    ChargeAssistScheduleEntryTileComponent(
        title = "Learn More",
        onClick = {
            chargeAssistViewModel.chargeAssistVMHelper
                .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_SCHEDULE_LEARN_MORE)
            navController.navigate(ChargeAssistRoute.LearnMoreScreen.route + "/''/true")
        },
        contentDescription = "Navigate to Charge Assist Helper learn more screen",
    )
}
// ============ line duplication issue below

@Composable
fun ChargeAssistScheduleEntryTileComponent(
    title: String,
    onClick: () -> Unit,
    contentDescription: String,
    tileColor: Color = AppTheme.colors.tertiary03,
) {
    Box(
        modifier =
            Modifier
                .background(AppTheme.colors.tile05)
                .fillMaxWidth()
                .height(56.dp),
    ) {
        Row(
            modifier =
                Modifier
                    .background(AppTheme.colors.tile05)
                    .padding(start = 16.dp, end = 15.dp)
                    .fillMaxWidth()
                    .height(56.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            OAButtonTextView(
                text = title,
                color = tileColor,
                // modifier = Modifier.width(270.dp),
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Left,
            )

            IconButton(
                onClick = onClick,
                modifier =
                    Modifier
                        .size(24.dp),
            ) {
                Icon(
                    modifier =
                        Modifier.semantics {
                            this.contentDescription = contentDescription
                        },
                    painter = painterResource(id = R.drawable.ic_arrow_right),
                    contentDescription = contentDescription,
                    tint = AppTheme.colors.tertiary00,
                )
            }
        }
    }
}
