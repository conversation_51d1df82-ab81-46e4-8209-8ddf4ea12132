package com.toyota.oneapp.features.chargeassist.dataaccess.model.enrolllearnmoreresponse

import com.google.gson.annotations.SerializedName

data class ProgramDescriptionModel(
    @SerializedName("program-incentives")
    val programIncentives: ProgramIncentivesModel? = ProgramIncentivesModel(),
    @SerializedName("more-information")
    val moreInformation: MoreInformationModel? = MoreInformationModel(),
    @SerializedName("questions")
    val questions: QuestionsModel? = QuestionsModel(),
)
