/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeassist.dataaccess.model.programavailability

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.network.models.BaseResponse

data class ProgramEligibilityModelPayload(
    @SerializedName("program_name")
    val programName: String? = "",
    @SerializedName("zip_code")
    val zipCode: String? = "",
    @SerializedName("program_description")
    val programDescription: String? = "",
    @SerializedName("utility_provider_name")
    val utilityProviderName: String? = "",
    @SerializedName("utility_provider_acronym")
    val utilityProviderAcronym: String? = "",
    @SerializedName("aggregator_name")
    val aggregatorName: String? = "",
    @SerializedName("image_link")
    val imageLink: String? = "",
    @SerializedName("program_id")
    val programId: String? = "",
    @SerializedName("short_description")
    val shortDescription: String? = "",
    @SerializedName("utility_provider_account_number_length")
    val utilityProviderAccountNumberLength: String? = "",
    @SerializedName("utility_provider_T_and_C")
    val utilityProviderTAndC: String? = "",
    @SerializedName("aggregator_T_and_C")
    val aggregatorTAndC: String? = "",
    @SerializedName("aggregator_privacy_portal")
    val aggregatorPrivacyPortal: String? = "",
    @SerializedName("aggregator_support_email")
    val aggregatorSupportEmail: String? = "",
) : BaseResponse()
