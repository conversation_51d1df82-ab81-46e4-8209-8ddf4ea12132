package com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollmentcheck

import com.google.gson.annotations.SerializedName

data class VinStatuModel(
    @SerializedName("active")
    val active: String? = "",
    @SerializedName("enrollment_date")
    val enrollmentDate: String? = "",
    @SerializedName("program_id")
    val programId: String? = "",
    @SerializedName("program_name")
    val programName: String? = "",
    @SerializedName("status")
    val status: String? = "",
    @SerializedName("vin")
    val vin: String? = "",
)
