/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeassist

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.chargeinfo.navigation.ChargeInfoRoute
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleScreen
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleViewModel
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.ChargeScheduleRoute
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.ChargeScheduleRoute.Companion.ARG_IS_ELIGIBILITY_SCREEN_ZIP_VALID
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.ChargeScheduleRoute.Companion.ARG_IS_ELIGIBILITY_SCREEN_ZIP_VALID_QUERY_PARAM
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.ChargeScheduleRoute.Companion.ARG_IS_PLUGGED_IN
import com.toyota.oneapp.features.core.navigation.sharedViewModel

fun NavGraphBuilder.chargeAssistScheduleNavGraph(navController: NavHostController) {
    navigation(
        route = ChargeInfoRoute.ChargeScheduleNestedRoute.route,
        startDestination =
            ChargeScheduleRoute.ChargeScheduleScreen.route +
                "/$ARG_IS_PLUGGED_IN?$ARG_IS_ELIGIBILITY_SCREEN_ZIP_VALID_QUERY_PARAM",
    ) {
        composable(
            route =
                ChargeInfoRoute.ChargeScheduleNestedRoute.route +
                    "/$ARG_IS_PLUGGED_IN?$ARG_IS_ELIGIBILITY_SCREEN_ZIP_VALID_QUERY_PARAM",
            arguments =
                listOf(
                    navArgument(ARG_IS_PLUGGED_IN) {
                        type = NavType.BoolType
                        defaultValue = false
                    },
                    navArgument(ARG_IS_ELIGIBILITY_SCREEN_ZIP_VALID) {
                        type = NavType.StringType
                        nullable = true
                        defaultValue = null
                    },
                ),
        ) { entry ->
            val chargeScheduleViewModel =
                entry.sharedViewModel<ChargeScheduleViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            val chargeAssistViewModel =
                entry.sharedViewModel<ChargeAssistViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            val isZipFromEligibilityScreenValid =
                entry.arguments?.getString(ARG_IS_ELIGIBILITY_SCREEN_ZIP_VALID)?.let {
                    when (it) {
                        "true" -> true
                        "false" -> false
                        else -> null
                    }
                }

            ChargeScheduleScreen(
                navController = navController,
                viewModel = chargeScheduleViewModel,
                chargeAssistViewModel = chargeAssistViewModel,
                isZipFromEligibilityScreenValid = isZipFromEligibilityScreenValid,
            )
        }
    }
}
