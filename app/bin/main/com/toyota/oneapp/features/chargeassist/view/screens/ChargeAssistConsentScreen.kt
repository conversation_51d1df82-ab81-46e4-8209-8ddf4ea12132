package com.toyota.oneapp.features.chargeassist.view.screens

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.onClick
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.features.chargeassist.ChargeAssistRoute
import com.toyota.oneapp.features.chargeassist.view.helper.ChargeAssistBackButtonTitleHeader
import com.toyota.oneapp.features.chargeassist.view.helper.ExpandableQuestionAnswerCard
import com.toyota.oneapp.features.chargeassist.view.screens.entrypoint2.getFinalStatus
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistExtensionViewModel
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun ChargeAssistConsentScreen(
    navController: NavController,
    zip: String?,
    chargeAssistViewModel: ChargeAssistViewModel,
) {
    val chargeAssistExtensionViewModel: ChargeAssistExtensionViewModel = hiltViewModel()

    val isConsentAccepted by chargeAssistExtensionViewModel.isConsentAccepted.collectAsState()
    val enrollmentStatus by chargeAssistViewModel.enrollmentStatusData.collectAsState()
    val currentUserEnrollmentStatus = getFinalStatus(enrollmentStatus)
    val previousScreen = navController.previousBackStackEntry?.destination?.route

    LaunchedEffect(isConsentAccepted) {
        chargeAssistViewModel.fetchEnrollmentStatus()
        chargeAssistExtensionViewModel.resetConsentStatus()
    }
    LaunchedEffect(isConsentAccepted) {
        if (isConsentAccepted) {
            navController.navigate(
                ChargeAssistRoute.EnrollmentUtilityProgramScreen.route +
                    "/$zip/${true}/$currentUserEnrollmentStatus",
            )
        }
    }

    Box {
        Column(
            modifier = Modifier.fillMaxSize().padding(horizontal = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            ChargeAssistBackButtonTitleHeader(navController, true)

            when {
                previousScreen?.contains(ChargeAssistRoute.ZipNotAvailableScreen.route) == true ->
                    OACallOut1TextView(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(R.string.great_news_charging_program_in_your_area),
                        color = AppTheme.colors.tertiary05,
                        textAlign = TextAlign.Center,
                    )
                else -> {
                    OACallOut1TextView(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(R.string.please_accept_the_consent_text),
                        color = AppTheme.colors.tertiary05,
                        textAlign = TextAlign.Center,
                    )
                }
            }

            ExpandableQuestionAnswerList(navController, chargeAssistViewModel, chargeAssistExtensionViewModel)
        }
    }

    BackHandler {
        // Navigate back to the previous screen
        navController.popBackStack()
    }
}

@Composable
fun ExpandableQuestionAnswerList(
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
    chargeAssistExtensionViewModel: ChargeAssistExtensionViewModel,
) {
    val scrollState = rememberScrollState()

    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(top = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        ExpandableQuestionAnswerCard(
            question = stringResource(R.string.what_is_charge_assist),
            answer = stringResource(id = R.string.what_is_charge_assist_answer),
        )

        val context = LocalContext.current
        ExpandableQuestionAnswerCard(
            question = stringResource(R.string.consent_question),
            answerComposable = {
                ParagraphWithHyperlink(
                    textBeforeLink =
                        stringResource(
                            id = R.string.charge_assist_consent_answer,
                        ) + " ",
                    hyperlinkText = context.getString(R.string.weavegrid_privacy_policy),
                    textAfterLink = "",
                    url = "https://www.weavegrid.com/privacy-policy",
                    isUnderlined = true,
                    onLinkClick = { url ->
                        openUrl(context, url)
                    },
                )
            },
        )

        // You can add more question/answer cards here

        // Spacer to push the buttons to the bottom
        Spacer(modifier = Modifier.weight(1f))
        // Stationary button at the bottom
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                DeclineTextButton(navController, chargeAssistViewModel, chargeAssistExtensionViewModel)
                AcceptButton(chargeAssistViewModel, chargeAssistExtensionViewModel)
            }
        }
    }
}

fun openUrl(
    context: Context,
    url: String,
) {
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
    // Verify that there's an app available to handle the intent
    if (intent.resolveActivity(context.packageManager) != null) {
        context.startActivity(intent)
    }
}

@Composable
fun ParagraphWithHyperlink(
    textBeforeLink: String,
    hyperlinkText: String,
    textAfterLink: String,
    url: String,
    isUnderlined: Boolean,
    onLinkClick: (String) -> Unit,
) {
    val annotatedText =
        buildAnnotatedString {
            withStyle(
                style =
                    SpanStyle(
                        fontSize = 16.sp,
                        color = AppTheme.colors.tertiary05,
                    ),
            ) {
                append(textBeforeLink) // Regular part of the text
            }

            // Annotating the hyperlink part of the text
            pushStringAnnotation(tag = "URL", annotation = url)
            withStyle(
                style =
                    SpanStyle(
                        fontWeight = FontWeight.Bold,
                        color = AppTheme.colors.tertiary05,
                        textDecoration = if (isUnderlined) TextDecoration.Underline else TextDecoration.None,
                    ),
            ) {
                append(hyperlinkText) // Hyperlink part of the text
            }
            pop()

            append(textAfterLink)
        }

    Text(
        text = annotatedText,
        modifier =
            Modifier
                .padding(16.dp)
                .fillMaxWidth()
                .clickable { onLinkClick(url) } // Handle click action
                .semantics {
                    contentDescription = "$textBeforeLink $hyperlinkText, link"
                    role = Role.Button
                    onClick {
                        onLinkClick(url)
                        true
                    }
                },
        style = AppTheme.fontStyles.body3,
        onTextLayout = { textLayoutResult ->
            textLayoutResult.getOffsetForPosition(Offset.Zero) // Ensures interaction is processed
        },
    )
}

const val FONT_WEIGHT_FOR_DECLINE_TEXT = 600

@Composable
fun DeclineTextButton(
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
    chargeAssistExtensionViewModel: ChargeAssistExtensionViewModel,
) {
    val previousRoute = navController.previousBackStackEntry?.destination?.route
    val isConsentDeclined = chargeAssistExtensionViewModel.isConsentDeclined.collectAsState()

    LaunchedEffect(isConsentDeclined) {
        if (isConsentDeclined.value) {
            when (previousRoute) {
                // if previous screen is charge info go back to charge info on Decline
                OAScreen.ChargeInfo.route -> navController.navigateUp()

                // if previous screen is charge schedule go back to charge schedule on Decline
                OAScreen.ChargeSchedule.route -> navController.navigateUp()

                // if previous screen is not Charge Schedule or Charge Info then Navigate Home/Dashboard
                else -> navController.navigate(OAScreen.Home.route)
            }
            chargeAssistViewModel.chargeAssistVMHelper
                .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_CONSENT_DECLINE)
        }
    }

    Text(
        modifier =
            Modifier
                .padding(bottom = 16.dp)
                .clickable {
                    chargeAssistExtensionViewModel.isConsentDecline()
                },
        text = AnnotatedString(stringResource(R.string.decline_button_title)),
        style =
            TextStyle(
                fontSize = 14.sp,
                lineHeight = 20.sp,
                fontFamily = FontFamily(Font(com.toyota.one_ui.R.font.toyotatype_regular)),
                fontWeight = FontWeight(FONT_WEIGHT_FOR_DECLINE_TEXT),
                color = AppTheme.colors.button02a,
                textAlign = TextAlign.Center,
            ),
    )
}

@Composable
fun AcceptButton(
    chargeAssistViewModel: ChargeAssistViewModel,
    chargeAssistExtensionViewModel: ChargeAssistExtensionViewModel,
) {
    Column(
        modifier = Modifier.padding(bottom = 10.dp),
    ) {
        PrimaryButton02(
            text = stringResource(R.string.accept_button_text),
            modifier =
                Modifier
                    .padding(horizontal = 16.dp)
                    .height(52.dp),
            click = {
                chargeAssistExtensionViewModel.isConsentAccepted()
                chargeAssistViewModel.chargeAssistVMHelper
                    .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_CONSENT_ACCEPT)
            },
        )
    }
}

@Preview(showBackground = true, heightDp = 1000)
@Composable
fun PreviewChargeAssistConsentScreen() {
    val navController = rememberNavController()
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()
    ChargeAssistConsentScreen(navController, "39393", chargeAssistViewModel)
}
