package com.toyota.oneapp.features.chargeassist.view.helper

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.widget.Toast
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.features.chargeassist.ChargeAssistRoute
import com.toyota.oneapp.features.chargeassist.view.screens.ParagraphWithHyperlink
import com.toyota.oneapp.features.chargeassist.view.screens.openUrl
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OAClickableBody4TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.util.Brand

private const val NEWLINE = "\n"
private const val BULLET_MARKER = "--"

@Composable
fun FullLearnMoreScreen(
    navController: NavController,
    zipCode: String?,
    showConsent: Boolean,
    chargeAssistViewModel: ChargeAssistViewModel,
) {
    LaunchedEffect(Unit) {
        chargeAssistViewModel.fetchZipEligibilityStatus()
    }
    val context = LocalContext.current
    Box {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            ChargeAssistBackButtonTitleHeader(navController, false)

            Column(
                modifier =
                    Modifier
                        .padding(1.dp)
                        .fillMaxWidth(),
                horizontalAlignment = Alignment.Start,
            ) {
            }
            ChargeAssistFullTitle(context)
            ExpandableQuestionAnswerList(navController, zipCode, showConsent)
        }
    }
}

@Composable
fun ExpandableQuestionAnswerList(
    navController: NavController,
    zipCode: String? = null,
    showConsent: Boolean,
) {
    val context = LocalContext.current

    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        LazyColumn(
            modifier =
                Modifier
                    .weight(1f)
                    .padding(top = 5.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            item {
                ExpandableQuestionAnswerCard(
                    question = stringResource(R.string.what_is_charge_assist),
                    answer = stringResource(R.string.what_is_charge_assist_answer),
                )
            }

            item {
                ExpandableQuestionAnswerCard(
                    question = stringResource(R.string.how_charge_assist_it_works),
                    answer = stringResource(R.string.how_charge_assist_works_answer),
                )
            }

            item {
                ExpandableQuestionAnswerCard(
                    question = stringResource(R.string.charge_assist_benefits),
                    bulletPointHeaderText =
                        stringResource(
                            R.string.charge_assist_benefits_bullet_header,
                        ),
                    answer = stringResource(id = R.string.charge_assist_benefits_bullet_list),
                )
            }
            item {
                ExpandableQuestionAnswerCard(
                    question = stringResource(R.string.charge_assist_disclaimer),
                    answer = stringResource(R.string.charge_assist_disclaimer_answer),
                )
            }
            if (showConsent) {
                item {
                    ExpandableQuestionAnswerCard(
                        question = stringResource(R.string.consent_question),
                        answerComposable = {
                            ConsentAnswerComposable(context) { url ->
                                openUrl(context, url)
                            }
                        },
                    )
                }
            }
            // You can add more question/answer cards here
        }
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(if (showConsent) 8.dp else 16.dp),
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                if (showConsent) {
                    SeeFaqsButton(modifier = Modifier.padding(bottom = 20.dp))
                } else {
                    SeeFAQComponent(modifier = Modifier.padding(top = 30.dp))
                    EnrollButton(modifier = Modifier, navController, zipCode = zipCode)
                }
            }
        }
    }
}

@Composable
fun ConsentAnswerComposable(
    context: Context,
    onLinkClick: (String) -> Unit,
) {
    ParagraphWithHyperlink(
        textBeforeLink = stringResource(id = R.string.charge_assist_consent_answer) + " ",
        hyperlinkText = context.getString(R.string.weavegrid_privacy_policy),
        textAfterLink = "",
        url = stringResource(R.string.charge_assist_weavegrid_url),
        isUnderlined = true,
        onLinkClick = onLinkClick,
    )
}

@Composable
fun ExpandableQuestionAnswerCard(
    question: String,
    bulletPointHeaderText: String? = null,
    answer: String? = null,
    answerComposable: (@Composable () -> Unit)? = null,
) {
    val expanded by remember { mutableStateOf(false) }

    Card(
        elevation = 0.dp,
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(8.dp),
        shape = RoundedCornerShape(16.dp),
    ) {
        ExpandableCardContent(
            expanded = remember { mutableStateOf(expanded) },
            question,
            bulletPointHeaderText,
            answer,
            answerComposable,
        )
    }
}

@Composable
fun ExpandableCardContent(
    expanded: MutableState<Boolean>,
    question: String,
    bulletPointHeaderText: String? = null,
    answer: String? = null,
    answerComposable: (@Composable () -> Unit)? = null,
) {
    Column(
        modifier =
            Modifier
                .background(AppTheme.colors.tertiary15)
                .animateContentSize(
                    // This manages the expansion and collapse smoothly
                    animationSpec = tween(durationMillis = 600, easing = FastOutSlowInEasing),
                ),
    ) {
        Box(
            modifier =
                Modifier
                    .background(AppTheme.colors.tile05)
                    .fillMaxWidth()
                    .padding(16.dp),
        ) {
            TitleBlock(expanded, question) // title part of the expandable card
        }
        AnswerBlock(
            expanded = expanded,
            bulletPointHeaderText,
            answer = answer,
            answerComposable,
        )
//        }
    }
}

private const val QUESTION_WEIGHT = 4f // added to clear sonar smell

@Composable
fun TitleBlock(
    expanded: MutableState<Boolean>,
    question: String,
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        OABody4TextView(
            text = question,
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier
                    .semantics {
                        contentDescription = question
                    }
                    .weight(QUESTION_WEIGHT),
        )

        // Icon button collapse/expand function
        IconButton(
            modifier =
                Modifier
                    .weight(1f),
            onClick = { expanded.value = !expanded.value },
        ) {
            val icon =
                if (expanded.value) {
                    painterResource(id = R.drawable.remove_icon)
                } else {
                    painterResource(id = R.drawable.add_icon)
                }
            Icon(
                tint = AppTheme.colors.tertiary03,
                painter = icon,
                contentDescription = if (expanded.value) "Collapse" else "Expand",
                modifier =
                    Modifier
                        .semantics {
                            contentDescription = if (expanded.value) "Collapse" else "Expand"
                        }
                        .size(24.dp),
            )
        }
    }
}

@Composable
fun AnswerBlock(
    expanded: MutableState<Boolean>,
    bulletPointHeaderText: String? = null,
    answer: String?,
    answerComposable: (@Composable () -> Unit)? = null,
) {
    if (expanded.value) {
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(AppTheme.colors.tertiary15),
        ) {
            // Display header if provided
            bulletPointHeaderText?.let {
                OABody3TextView(
                    text = it,
                    color = AppTheme.colors.tertiary05,
                    modifier =
                        Modifier
                            .padding(top = 16.dp)
                            .padding(horizontal = 16.dp)
                            .wrapContentWidth(),
                )
            }

            when {
                answer != null -> AnswerContent(answer)
                answerComposable != null -> answerComposable()
            }
        }
    }
}

@Composable
fun AnswerContent(answer: String?) {
    when {
        answer?.contains(NEWLINE) == true || answer?.contains(BULLET_MARKER) == true -> {
            val bulletPoints =
                answer.toString()
                    .split("\n")
                    .filter {
                        it.isNotEmpty() && (
                            it.contains(BULLET_MARKER) ||
                                answer.contains(
                                    NEWLINE,
                                )
                        )
                    }

            bulletPoints.forEach { bulletPoint ->
                Row(
                    modifier =
                        Modifier
                            .padding(1.dp)
                            .padding(start = 19.dp),
                    verticalAlignment = Alignment.Top,
                ) {
                    OABody3TextView(
                        modifier = Modifier.padding(start = 6.dp),
                        text = "• ",
                        color = AppTheme.colors.tertiary05,
                    )

                    OABody3TextView(
                        text = bulletPoint,
                        color = AppTheme.colors.tertiary05,
                        modifier =
                            Modifier
                                .wrapContentWidth()
                                .padding(end = 16.dp),
                    )
                }
            }
        }

        else -> {
            // If no bullet points, show answer as a regular paragraph
            Box(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .background(AppTheme.colors.tertiary15),
            ) {
                Column(
                    modifier = Modifier.fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    OABody3TextView(
                        text = answer.toString(),
                        color = AppTheme.colors.tertiary05,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp)
                                .padding(top = 16.dp),
                    )
                }
            }
        }
    }
}

@Composable
fun ChargeAssistFullTitle(context: Context) {
    Column(
        modifier =
            Modifier
                .wrapContentSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        ChargeAssistTitleLogo(context)
        ChargeAssistTitleName()
    }
}

@Composable
fun ChargeAssistTitleName() {
    Text(
        modifier =
            Modifier
                .semantics {
                    contentDescription = "Charge Assist Title"
                },
        text = stringResource(id = R.string.charge_assist),
        style = TextStyle(fontWeight = FontWeight.Bold, fontSize = 24.sp),
        color = AppTheme.colors.tertiary03,
    )
}

@Composable
fun ChargeAssistTitleLogo(context: Context) {
    if (isDarkModeActive()) {
        Image(
            modifier =
                Modifier
                    .size(60.dp)
                    .semantics {
                        contentDescription = context.getString(R.string.charge_assist_logo)
                    },
            painter = painterResource(R.drawable.charge_assist_logo_dark),
            contentDescription = "content description",
        )
    } else {
        Image(
            modifier =
                Modifier
                    .size(60.dp)
                    .semantics {
                        contentDescription = "Charge Assist Logo"
                    },
            painter = painterResource(R.drawable.charge_assist_logo),
            contentDescription = "content description",
        )
    }
}

@Composable
fun SeeFAQComponent(modifier: Modifier = Modifier) {
    Column(modifier.padding(bottom = 10.dp)) {
        val context = LocalContext.current
        val brandFAQUrl = getBrandFAQUrl()
        val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()

        OAClickableBody4TextView(
            onClick = {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(brandFAQUrl))
                context.startActivity(intent)
                chargeAssistViewModel.chargeAssistVMHelper
                    .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_SEE_FAQS_BUTTON_ON_LEARN_MORE)
            },
            text = stringResource(id = R.string.see_faq),
            color = AppTheme.colors.button02a,
        )
    }
}

@Composable
fun HyperlinkText(
    url: String?,
    text: String,
    isUnderlined: Boolean = false,
) {
    val context = LocalContext.current

    BasicText(
        text =
            AnnotatedString(
                text = text,
                spanStyle =
                    SpanStyle(
                        color = AppTheme.colors.tertiary03,
                        textDecoration = if (isUnderlined) TextDecoration.Underline else TextDecoration.None,
                    ),
            ),
        modifier =
            Modifier.clickable {
                when {
                    url.isNullOrEmpty() -> {
                        Toast.makeText(context, "Link is not available.", Toast.LENGTH_SHORT).show()
                    }
                    else -> {
                        val intent =
                            Intent(Intent.ACTION_VIEW).apply {
                                data = Uri.parse(url)
                            }
                        // Check if there is an app that can handle intent
                        if (context.packageManager.resolveActivity(intent, 0) != null) {
                            context.startActivity(intent)
                        } else {
                            Toast.makeText(context, "Not Available", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            },
    )
}

@Composable
fun EnrollButton(
    modifier: Modifier = Modifier,
    navController: NavController,
    zipCode: String?,
) {
    Column(modifier.padding(bottom = 10.dp)) {
        PrimaryButton02(
            text = stringResource(R.string.enroll_button_title),
            modifier = Modifier,
            click = {
                if (zipCode != null) {
                    navController.navigate(ChargeAssistRoute.ConsentScreen.route + "/''")
                } else {
                    navController.navigate(ChargeAssistRoute.ZipNotAvailableScreen.route)
                }
            },
        )
    }
}

@Composable
fun getBrandFAQUrl(): String {
    val brandSpecificFAQUrl =
        when {
            Brand.currentAppBrand().isToyota() ->
                stringResource(
                    id = R.string.charge_assist_toyota_faq_url,
                )

            Brand.currentAppBrand().isLexus() ->
                stringResource(
                    id = R.string.charge_assist_lexus_faq_url,
                )

            else -> ""
        }
    return brandSpecificFAQUrl
}

@Composable
fun SeeFaqsButton(modifier: Modifier = Modifier) {
    val context = LocalContext.current
    val brandFAQUrl = getBrandFAQUrl()

    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()
    Column(modifier.padding(bottom = 10.dp)) {
        PrimaryButton02(
            text = stringResource(R.string.see_faqs),
            modifier = Modifier,
            click = {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(brandFAQUrl))
                context.startActivity(intent)
                chargeAssistViewModel.chargeAssistVMHelper
                    .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_SEE_FAQS_BUTTON_ON_LEARN_MORE)
            },
        )
    }
}
