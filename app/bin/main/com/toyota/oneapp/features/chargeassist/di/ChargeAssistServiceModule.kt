package com.toyota.oneapp.features.chargeassist.di

import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.features.chargeassist.application.ChargeAssistLogic
import com.toyota.oneapp.features.chargeassist.application.ChargeAssistUseCase
import com.toyota.oneapp.features.chargeassist.dataaccess.repository.ChargeAssistDefaultRepo
import com.toyota.oneapp.features.chargeassist.domain.repository.ChargeAssistRepository
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistVMHelper
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
interface ChargeAssistServiceModule {
    @Binds
    fun bindChargeAssistServiceRepo(chargeAssistDefaultRepo: ChargeAssistDefaultRepo): ChargeAssistRepository

    @Binds
    fun bindChargeAssistUseCase(chargeAssistLogic: ChargeAssistLogic): ChargeAssistUseCase

    companion object {
        @Provides
        @Singleton
        fun provideChargeAssistVMHelper(
            analyticsLogger: AnalyticsLogger,
            chargeAssistUseCase: ChargeAssistUseCase,
            oneAppPreferenceModel: OneAppPreferenceModel,
        ): ChargeAssistVMHelper {
            return ChargeAssistVMHelper(analyticsLogger, chargeAssistUseCase, oneAppPreferenceModel)
        }
    }
}
