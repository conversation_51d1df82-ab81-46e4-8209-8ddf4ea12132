package com.toyota.oneapp.features.chargeassist.view.helper

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistBottomSheetLogo
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.theme.AppTheme

private const val AREA_CODE_START_INDEX = 0
private const val AREA_CODE_END_INDEX = 3

private const val PHONE_PREFIX_START_INDEX = 3
private const val PHONE_PREFIX_END_INDEX = 6

private const val LINE_NUMBER_START_INDEX = 6

@Composable
fun BackButton(
    navController: NavController,
    modifier: Modifier = Modifier,
    onBackClick: (() -> Unit)? = null,
) {
    val context = LocalContext.current
    IconButton(
        modifier =
            modifier
                .semantics {
                    contentDescription = context.getString(R.string.back_button)
                },
        onClick = {
            navController.popBackStack()
            onBackClick?.invoke() // Trigger analytics if provided
        },
    ) {
        Icon(
            Icons.AutoMirrored.Filled.KeyboardArrowLeft,
            contentDescription = stringResource(R.string.back_arrow),
            tint = AppTheme.colors.tertiary03,
        )
    }
}

@Composable
fun ChargeAssistBackButtonTitleHeader(
    navController: NavController,
    showChargeAssistTitle: Boolean,
    onBackClick: (() -> Unit)? = null,
) {
    val context = LocalContext.current
    Row(
        modifier =
            Modifier
                .padding(top = 16.dp)
                .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box {
            BackButton(navController = navController, onBackClick = onBackClick)
        }
        if (showChargeAssistTitle) {
            OASubHeadLine3TextView(
                text = stringResource(R.string.charge_assist),
                color = AppTheme.colors.tertiary03,
                modifier =
                    Modifier
                        .semantics {
                            contentDescription = context.getString(R.string.charge_assist_title_content_description)
                        }
                        .padding(16.dp)
                        .fillMaxWidth()
                        .padding(end = 50.dp),
                textAlign = TextAlign.Center,
            )
        }
    }
}

@Composable
fun ManualSchedulingBackButtonTitleHeader(
    navController: NavController,
    text: String,
    showTitle: Boolean,
    onBackClick: (() -> Unit)? = null,
) {
    Row(
        modifier =
            Modifier
                .padding(top = 16.dp)
                .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        BackButton(navController = navController, onBackClick = onBackClick)
        if (showTitle) {
            OASubHeadLine3TextView(
                text = "$text",
                color = AppTheme.colors.tertiary03,
                modifier =
                    Modifier
                        .semantics { contentDescription = "$text Title" }
                        .padding(16.dp)
                        .padding(start = 16.dp)
                        .width(250.dp),
                textAlign = TextAlign.Center,
            )
        }
    }
}

@Composable
fun isDarkModeActive(): Boolean = AppTheme.darkMode.collectAsState().value

fun provideColdAppLaunchCounter(context: Context): ColdAppLaunchCounter {
    val prefs =
        EncryptedSharedPreferences.create(
            context,
            "app_preferences",
            MasterKey
                .Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build(),
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM,
        )
    return ColdAppLaunchCounter(prefs)
}

fun String.getPhoneNumberDomesticFormat(): String {
    if (!this.matches("[0-9]{10}".toRegex())) {
        return this
    }

    val first = this.subSequence(AREA_CODE_START_INDEX, AREA_CODE_END_INDEX) // Area Code
    val second = this.subSequence(PHONE_PREFIX_START_INDEX, PHONE_PREFIX_END_INDEX) // Phone Prefix
    val third = this.subSequence(LINE_NUMBER_START_INDEX, this.length) // Line Number

    return "$first-$second-$third"
}

@Composable
fun ChargeAssistCommonSheetContent(
    title: String,
    descriptionTexts: List<String>,
    buttonText: String,
    buttonPaddingTop: Dp,
    onButtonClick: () -> Unit,
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Handle bar
        Box(
            modifier =
                Modifier
                    .padding(top = 16.dp)
                    .width(40.dp)
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(AppTheme.colors.tertiary10),
        )

        ChargeAssistBottomSheetLogo(modifier = Modifier.padding(top = 50.dp))

        // Title
        OABody3TextView(
            modifier =
                Modifier
                    .width(311.dp)
                    .padding(top = 31.dp),
            text = title,
            color = AppTheme.colors.tertiary03,
            textAlign = TextAlign.Center,
        )

        // Description texts
        descriptionTexts.forEach { text ->
            OACallOut1TextView(
                modifier =
                    Modifier
                        .width(311.dp)
                        .padding(top = 8.dp),
                text = text,
                color = AppTheme.colors.tertiary05,
                textAlign = TextAlign.Center,
            )
        }

        // Button
        PrimaryButton02(
            text = buttonText,
            modifier =
                Modifier
                    .padding(16.dp)
                    .padding(top = buttonPaddingTop)
                    .height(52.dp)
                    .wrapContentSize(),
            click = { onButtonClick() },
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewBackButton() {
    val navController = rememberNavController()
    BackButton(navController)
}

@Preview(showBackground = true)
@Composable
fun PreviewChargeAssistBackButtonTitleHeader() {
    val navController = rememberNavController()
    ChargeAssistBackButtonTitleHeader(navController, true)
}

@Preview(showBackground = true)
@Composable
fun PreviewManualScheduleBackButtonTitleHeader() {
    val navController = rememberNavController()
    ManualSchedulingBackButtonTitleHeader(navController, "Manual Scheduling", true)
}
