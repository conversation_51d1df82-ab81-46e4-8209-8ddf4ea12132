package com.toyota.oneapp.features.chargeassist.view.screens.manualschedule

import android.content.Context
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistToggleSwitches.Companion.MANUAL_SCHEDULE_SWITCH
import com.toyota.oneapp.features.chargeassist.view.screens.ManualScheduleCardParamsForCAToggle
import com.toyota.oneapp.features.chargeassist.view.screens.ManualScheduleWidget
import com.toyota.oneapp.features.chargeassist.view.viewmodel.CHARGE_ASSIST_SWITCH
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.chargeschedule.application.ChargeScheduleState
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleContainer
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleEvents
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleUIState
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleViewModel
import com.toyota.oneapp.features.chargeschedule.presentation.MultiDayScheduleScreen
import com.toyota.oneapp.features.chargeschedule.presentation.PHEVScheduleScreen
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.ChargeScheduleRoute
import com.toyota.oneapp.features.core.composable.OAAppBar
import com.toyota.oneapp.features.core.composable.OABody1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.CommonUtil

@Composable
fun ManualScheduleScreen(
    navController: NavHostController,
    viewModel: ChargeScheduleViewModel,
    chargeAssistViewModel: ChargeAssistViewModel,
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current
    val activeSwitch by chargeAssistViewModel.chargeAssistVMHelper.activeSwitch.collectAsState()
    val isManualScheduleActive = activeSwitch == MANUAL_SCHEDULE_SWITCH
    val isAnySwitchActive = activeSwitch.isNotEmpty()

    val manualScheduleCardParamsForCAToggle =
        ManualScheduleCardParamsForCAToggle(
            isClickable = isManualScheduleActive || !isAnySwitchActive,
            isEnabled = isManualScheduleActive,
            isChargeAssistActive = activeSwitch == CHARGE_ASSIST_SWITCH,
            onToggle = { chargeAssistViewModel.chargeAssistVMHelper.toggleSwitch(MANUAL_SCHEDULE_SWITCH) },
        )
    chargeScheduleLaunchedEffect(uiState, context, viewModel = viewModel)

    var hasRefreshed by remember { mutableStateOf(false) }
    LaunchedEffect(uiState.viewState) {
        when {
            (uiState.viewState is ChargeScheduleState.Init && !hasRefreshed) ||
                (uiState.viewState is ChargeScheduleState.SchedulingList && !hasRefreshed) ||
                (uiState.viewState is ChargeScheduleState.MultiDayScheduleLoading && !hasRefreshed) ||
                (uiState.viewState is ChargeScheduleState.NoSchedule && !hasRefreshed) -> {
                hasRefreshed = true
                viewModel.refreshLastUpdatedData(isMultiDayRefresh = true)
            }
        }
    }
    Column(
        modifier = Modifier.fillMaxSize().padding(horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        OAAppBar(
            title = stringResource(R.string.manual_schedule),
            testTagId = "",
            modifier = Modifier.padding(vertical = 8.dp),
        ) { navController.popBackStack() }

        Box(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(vertical = 16.dp),
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                ManualScheduleWidget(
                    navController,
                    modifier = Modifier.width(380.dp),
                    showEntryPointSection = false,
                    manualScheduleCardParamsForCAToggle = manualScheduleCardParamsForCAToggle,
                    isChargeAssistActive = manualScheduleCardParamsForCAToggle.isChargeAssistActive,
                )

                // Add the Schedule Items Container
                ChargeScheduleContainer(
                    viewState = uiState.viewState,
                    onNoSchedule = { isMultiDay ->
                        NoScheduleWidgetForManualSchedule(
                            manualScheduleCardParamsForCAToggle,
                            isMultiDaySchedule = isMultiDay,
                        ) {
                            navController.navigate(ChargeScheduleRoute.EditMultidayScheduleScreen.route)
                        }
                    },
                    onMultiDaySchedule = { uiModel ->
                        MultiDayScheduleScreen(
                            navController = navController,
                            uiModel = uiModel,
                            viewModel = viewModel,
                            manualScheduleCardParamsForCAToggle = manualScheduleCardParamsForCAToggle,
                        )
                    },
                    onPHEVSchedule = { uiModel ->
                        PHEVScheduleScreen(
                            navController = navController,
                            uiModel = uiModel,
                            viewModel = viewModel,
                        )
                    },
                )
            }
        }
    }
    BackHandler {
        navController.popBackStack()
    }
}

@Composable
fun chargeScheduleLaunchedEffect(
    uiState: ChargeScheduleUIState,
    context: Context,
    viewModel: ChargeScheduleViewModel,
) {
    val toastMessage = viewModel.toastMessage
    val toastMessageRes = viewModel.toastMessageRes

    LaunchedEffect(key1 = uiState.viewState, key2 = toastMessage, key3 = toastMessageRes) {
        when {
            uiState.viewState is ChargeScheduleState.Init -> {
                viewModel.onEvent(ChargeScheduleEvents.InitChargeSchedule)
            }
            toastMessage != null -> {
                CommonUtil.showToast(context, toastMessage)
            }
            toastMessageRes != null -> {
                CommonUtil.showToast(context, context.getString(toastMessageRes))
            }
        }
        viewModel.reset()
    }
}

@Composable
fun NoScheduleWidgetForManualSchedule(
    manualScheduleCardParamsForCAToggle: ManualScheduleCardParamsForCAToggle,
    isMultiDaySchedule: Boolean,
    onCreateSchedule: () -> Unit,
) {
    Spacer(modifier = Modifier.height(8.dp))
    OABody1TextView(
        text =
            if (isMultiDaySchedule) {
                stringResource(id = R.string.no_created_schedules)
            } else {
                stringResource(id = R.string.no_phev_schedule_description)
            },
        color = AppTheme.colors.tertiary05,
        textAlign = TextAlign.Center,
        modifier = Modifier.padding(top = 24.dp),
    )
    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .padding(vertical = 16.dp),
    ) {
        // Handle button show/hide based on manualScheduleToggle
        when {
            isMultiDaySchedule && manualScheduleCardParamsForCAToggle.isEnabled -> {
                PrimaryButton02(
                    text = stringResource(id = R.string.create_schedule),
                    modifier =
                        Modifier
                            .align(Alignment.BottomCenter)
                            .testTagID(AccessibilityId.ID_NO_SCHEDULE_CREATE_SCHEDULE_BTN),
                ) {
                    onCreateSchedule()
                }
            }
        }
    }
}
