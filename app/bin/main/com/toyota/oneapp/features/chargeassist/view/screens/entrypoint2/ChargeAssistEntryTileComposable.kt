/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargeassist.view.screens.entrypoint2

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollmentcheck.EnrollmentCheckResponsePayload
import com.toyota.oneapp.features.chargeassist.view.helper.getEnrollmentScheduleStates
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.AGGREGATOR_UNENROLLMENT_FAILED
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_FAILURE_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_SUCCESSFUL_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.NOT_FOUND
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.UNENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStatus
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistScheduleStates.Companion.IN_PROGRESS
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistWidgetNamesState.Companion.CHARGE_INFO_ENROLLMENT_FAILURE_WIDGET
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistWidgetNamesState.Companion.CHARGE_INFO_INVALID_ZIP_WIDGET
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeInfoDisplayCardRenderState
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeInfoDisplayCardState
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.chargeinfo.presentation.ChargeInfoViewModel
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoResponse
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OAFootNote1TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.ApiResponse

@Composable
fun ChargeAssistEntryTile(
    navController: NavController,
    chargeInfoViewModel: ChargeInfoViewModel,
    chargeAssistViewModel: ChargeAssistViewModel,
    profileData: ProfileInfoResponse? = null,
) {
    EntryPointCardContent(navController, chargeInfoViewModel, chargeAssistViewModel, profileData)
}

const val PROGRAM_AVAILABLE_TEXT_WEIGHT = 900
const val ENROLL_CHARGE_ASSIST_TEXT_WEIGHT = 100

@Composable
fun EntryPointCardContent(
    navController: NavController,
    chargeInfoViewModel: ChargeInfoViewModel,
    chargeAssistViewModel: ChargeAssistViewModel,
    profileData: ProfileInfoResponse?,
) {
    val isZipEnteredOnEligibilityScreenValid by chargeAssistViewModel.chargeAssistVMHelper
        .isZipEnteredOnEligibilityScreenValid.collectAsState()
    val systemZip = profileData?.payload?.customer?.addresses?.getOrNull(0)?.zipCode
    val isEligibleForCAEnrollment by chargeAssistViewModel.isUserEligibilityForCAEnrollment.collectAsState()
    val chargeAssistEntryButtonData by chargeAssistViewModel.chargeAssistEntryButtonData.collectAsState()
    val chargeInfo = chargeInfoViewModel.chargeInfoState.collectAsState().value
    val shouldCAEntryCardBEDisplayed by chargeAssistViewModel
        .chargeAssistVMHelper.shouldCAEntryCardBeDisplayed.collectAsState()

    val shouldShowInvalidZipCardBeDisplayed by chargeAssistViewModel.chargeAssistVMHelper
        .shouldInvalidWidgetBeDisplayed.collectAsState()

    val shouldEnrollmentFailureCardBeDisplayed by chargeAssistViewModel.chargeAssistVMHelper
        .shouldShowEnrollmentFailureWidgetBeDisplayed.collectAsState()

    // Real Data
    // Already being collected via ChargeScheduleWidget via isUserEligibleForCAEnrollmentAndEnrollmentStatus()
    val currentEnrollmentStatus by chargeAssistViewModel.userEnrollmentStatusDataString.collectAsState()
    val collectProcessScheduleStatus by chargeAssistViewModel.processStatusData.collectAsState()
    val fetchIsInProgressStatusOver5min by chargeAssistViewModel.isInProgressStatusOver5min.collectAsState()
    val processScheduleStatus = collectProcessScheduleStatus?.data?.payload?.processStatus
    val processStatusEndTime = collectProcessScheduleStatus?.data?.payload?.chargeEndTime
    val pluggedInTime = collectProcessScheduleStatus?.data?.payload?.plugInDateTime
    val isEVPluggedIN by chargeAssistViewModel.chargeAssistVMHelper.isEVPluggedIN.collectAsState()
    val isEVCharging by chargeAssistViewModel.chargeAssistVMHelper.isEVCharging.collectAsState()

    LaunchedEffect(isEVPluggedIN, processStatusEndTime, pluggedInTime, isEligibleForCAEnrollment) {
        chargeAssistViewModel.getChargeAssistEntryButtonData(isEligibleForCAEnrollment)
        chargeAssistViewModel.loadBaseData()
        chargeAssistViewModel.chargeAssistVMHelper.grabRealEVChargeDataRaw(
            chargeInfo,
            processStatusEndTime,
            pluggedInTime,
        )

        chargeAssistViewModel.fetchIsInProgressStatusOver5min(processScheduleStatus)
    }
    val chargeInfoDisplayCardState =
        ChargeInfoDisplayCardState(
            currentEnrollmentStatus.toString(),
            isZipEnteredOnEligibilityScreenValid,
            systemZip,
            isEligibleForCAEnrollment,
            processScheduleStatus,
            isEVPluggedIN,
            isEVCharging,
            fetchIsInProgressStatusOver5min,
        )
    val chargeInfoDisplayCardRenderState =
        ChargeInfoDisplayCardRenderState(
            shouldCAEntryCardBEDisplayed,
            shouldShowInvalidZipCardBeDisplayed,
            shouldEnrollmentFailureCardBeDisplayed,
        )

    when (isEligibleForCAEnrollment) {
        true ->
            DisplayTheCard(
                chargeInfoDisplayCardState,
                chargeInfoDisplayCardRenderState,
                navController,
                chargeAssistViewModel,
                chargeAssistEntryButtonData,
            )
        else -> { /*Do Nothing*/ }
    }
}

@Composable
fun DisplayTheCard(
    chargeInfoDisplayCardState: ChargeInfoDisplayCardState,
    chargeInfoDisplayCardRenderState: ChargeInfoDisplayCardRenderState,
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
    chargeAssistEntryButtonData: Pair<String, String>?,
) {
    val chargeAssistDisplayCardData by chargeAssistViewModel.chargeAssistDisplayCardData.collectAsState()
    val chargeAssistDisplayCardRenderState by chargeAssistViewModel.chargeAssistDisplayCardRenderState.collectAsState()
    LaunchedEffect(
        chargeAssistDisplayCardData,
        chargeAssistDisplayCardRenderState,
    ) {
        chargeAssistViewModel.updateChargeAssistDisplayCardData(chargeInfoDisplayCardState)
        chargeAssistViewModel.updateChargeAssistDisplayCardRenderState(chargeAssistDisplayCardRenderState)

        // Entry Display
        chargeAssistViewModel.shouldCAEntryCardDisplay(
            chargeInfoDisplayCardState.currentEnrollmentStatus,
            chargeInfoDisplayCardState.isEligibleForCAEnrollment,
            chargeInfoDisplayCardState.systemZip,
        )

        // InvalidZip Display
        chargeAssistViewModel.chargeAssistVMHelper.shouldShowInvalidZipCard(
            chargeInfoDisplayCardState.currentEnrollmentStatus,
            CHARGE_INFO_INVALID_ZIP_WIDGET,
            chargeInfoDisplayCardState.systemZip,
        )

        chargeAssistViewModel.chargeAssistVMHelper
            .shouldShowEnrollmentFailureCard(
                chargeInfoDisplayCardState.currentEnrollmentStatus,
                CHARGE_INFO_ENROLLMENT_FAILURE_WIDGET,
            )
    }

    when {
        chargeInfoDisplayCardRenderState.shouldCAEntryCardBEDisplayed ->
            EntryPointCardData(navController, chargeAssistViewModel, chargeAssistEntryButtonData)

        // Invalid Zip CArd show and disappear after 1st visit on Charge Info
        chargeAssistDisplayCardRenderState.shouldShowInvalidZipCardBeDisplayed ->
            EnrollmentStatusCardData(chargeInfoDisplayCardState.currentEnrollmentStatus)

        chargeInfoDisplayCardRenderState.shouldEnrollmentFailureCardBeDisplayed ->
            EnrollmentStatusCardData(
                chargeInfoDisplayCardState.currentEnrollmentStatus,
                chargeInfoDisplayCardState.processScheduleStatus,
                chargeInfoDisplayCardState.isEVPluggedIN,
                chargeInfoDisplayCardState.isEVCharging,
            )

        /*
         show these variations of the card based on these statuses:
         EnrollmentSuccessful, Enrollment Pending and Unenrollment In Progress
         and when enrollment is pending
         */
        chargeInfoDisplayCardState.currentEnrollmentStatus in ENROLLMENT_SUCCESSFUL_STATUSES ||
            chargeInfoDisplayCardState.currentEnrollmentStatus in ENROLLMENT_PENDING_STATUSES ||
            chargeInfoDisplayCardState.currentEnrollmentStatus in UNENROLLMENT_PENDING_STATUSES ||
            chargeInfoDisplayCardState.currentEnrollmentStatus == AGGREGATOR_UNENROLLMENT_FAILED -> {
            EnrollmentStatusCardData(
                chargeInfoDisplayCardState.currentEnrollmentStatus,
                chargeInfoDisplayCardState.processScheduleStatus,
                chargeInfoDisplayCardState.isEVPluggedIN,
                chargeInfoDisplayCardState.isEVCharging,
                chargeInfoDisplayCardState.fetchIsInProgressStatusOver5min,
                chargeAssistViewModel = chargeAssistViewModel,
            )
        }

        else -> { // left intentionally blank
        }
    }
}

@Composable
fun getEnrollmentStatusDescription(
    enrollmentStatus: String?,
    currentProcessStatus: String? = null,
    isEVPluggedIN: Boolean?,
    isEVCharging: Boolean?,
    fetchIsInProgressStatusOver5min: Boolean? = null,
    chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel(),
): String {
    val context = LocalContext.current
    val isChargeAssistActive = chargeAssistViewModel.chargeAssistVMHelper.isChargeAssistActive.collectAsState()
    LaunchedEffect(Unit) { chargeAssistViewModel.chargeAssistVMHelper.isChargeAssistActive() }
    val completionTime = chargeAssistViewModel.chargeAssistVMHelper.chargeCompletionTime.collectAsState()

    val enrollmentAndScheduleProgress =
        getEnrollmentScheduleStates(
            Triple(enrollmentStatus, currentProcessStatus, isEVPluggedIN),
            isEVCharging,
            fetchIsInProgressStatusOver5min,
        )

    // EVM1-6181 - Schedule States
    // Checks Enrollment Status + Schedule Progress Status (progress, failed, success)
    return when {
        enrollmentStatus in ENROLLMENT_SUCCESSFUL_STATUSES && !isChargeAssistActive.value -> {
            "Personalized home charging and potential incentives."
        }
        // Failure - Enrolled + Plugged In + Schedule not retrieved after 5 minutes
        // ** Add the 5 minute condition here as well**
        enrollmentAndScheduleProgress ==
            ChargeAssistEnrollmentStatus.ENROLLED_SCHEDULE_NOT_RETRIEVED_AFTER_5_PLUGGED_IN ->
            context.getString(R.string.schedule_not_retrieved_please_check_your_plug_in_connection)

        // Enrollment Processing
        // Enrolled + Schedule Ready + Not plugged in
        enrollmentAndScheduleProgress == ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_READY_AND_NOT_PLUGGED_IN ->
            context.getString(R.string.plug_in_to_see_your_charging_schedule)

        // Enrolled + Schedule Pending + Plugged in
        enrollmentAndScheduleProgress == ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_PENDING_AND_PLUGGED_IN ||
            enrollmentAndScheduleProgress ==
            ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_PENDING_AND_NOT_PLUGGED_IN ->
            context.getString(R.string.schedule_will_be_available_shortly)

        // Enrolled + Schedule Ready + Plugged in
        enrollmentAndScheduleProgress == ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_READY_AND_PLUGGED_IN -> {
            context.getString(R.string.full_charge_estimated_by, completionTime.value)
        }

        // Enrollment + Schedule Pending
        enrollmentAndScheduleProgress == ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_PENDING ->
            context.getString(R.string.your_enrollment_and_schedule_profile_are_pending)

        // Failure - Plugged in + schedule not available
        enrollmentAndScheduleProgress == ChargeAssistEnrollmentStatus.ENROLLED_WITH_SCHEDULE_FAILED_AND_PLUGGED_IN ->
            context.getString(R.string.schedule_unavailable_please_try_again_later)

        // Pending enrollment status
        enrollmentStatus in ENROLLMENT_PENDING_STATUSES ->
            context.getString(R.string.your_enrollment_and_schedule_profile_are_pending_enrollment_state)

        // Failed Enrollment Status
        enrollmentStatus in ENROLLMENT_FAILURE_STATUSES ->
            context.getString(R.string.enrollment_not_approved_check_your_email_for_details)

        enrollmentStatus == NOT_FOUND -> context.getString(R.string.charge_assist_is_not_available_in_your_area)

        enrollmentStatus in UNENROLLMENT_PENDING_STATUSES ->
            stringResource(R.string.unenrollment_pending)

        else -> ""
    }
}

@Composable
fun EnrollmentStatusCardData(
    enrollmentStatus: String?,
    currentProcessStatus: String? = null,
    isEVPluggedIN: Boolean? = null,
    isEVCharging: Boolean? = null,
    fetchIsInProgressStatusOver5min: Boolean? = false,
    chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    val isChargeAssistActive = chargeAssistViewModel.chargeAssistVMHelper.isChargeAssistActive.collectAsState()
    LaunchedEffect(Unit) {
        chargeAssistViewModel.chargeAssistVMHelper.checkEnrollmentStatusForAutoToggle()
        chargeAssistViewModel.chargeAssistVMHelper.isChargeAssistActive()
    }

    if (enrollmentStatus in ENROLLMENT_SUCCESSFUL_STATUSES &&
        // only if Charge Assist is toggled on
        isChargeAssistActive.value &&
        currentProcessStatus == "success" &&
        (isEVPluggedIN == true || isEVCharging == true)
    ) {
        // display detailed card for user checking on current status
        ChargeAssistActiveVehicleStatusCardData(chargeAssistViewModel)
    } else {
        val enrollmentStatusDescription =
            getEnrollmentStatusDescription(
                enrollmentStatus,
                currentProcessStatus,
                isEVPluggedIN = isEVPluggedIN,
                isEVCharging = isEVCharging,
                fetchIsInProgressStatusOver5min,
                chargeAssistViewModel,
            )

        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(start = 16.dp, end = 15.dp, bottom = 17.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Card(
                elevation = 0.dp,
                shape = RoundedCornerShape(size = 8.dp),
                modifier =
                    Modifier
                        .semantics {
                            contentDescription =
                                context.getString(R.string.charge_assist_entry_card)
                        }
                        .height(116.dp),
            ) {
                // enrollment status card content
                EnrollmentStatusCardContent(
                    enrollmentStatus,
                    enrollmentStatusDescription,
                    currentProcessStatus,
                    isEVPluggedIN,
                )
            }
        }
    }
}

@Composable
fun EnrollmentStatusCardContent(
    enrollmentStatus: String?,
    enrollmentStatusDescription: String?,
    currentProcessStatus: String? = null,
    isEVPluggedIN: Boolean? = null,
) {
    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(AppTheme.colors.tile05),
    ) {
        Column {
            Column(
                modifier =
                    Modifier
                        .padding(top = 15.dp, start = 14.dp, bottom = 6.dp),
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(),
                ) {
                    ChargeAssistEntryPointTitleAndLogo() // logo and CA title
                    if (enrollmentStatus in ENROLLMENT_FAILURE_STATUSES) {
                        Spacer(modifier = Modifier.weight(1f))
                        AlertImage()
                    } else if (enrollmentStatus in ENROLLMENT_SUCCESSFUL_STATUSES &&
                        currentProcessStatus == IN_PROGRESS &&
                        isEVPluggedIN == true
                    ) {
                        Spacer(modifier = Modifier.weight(1f))
                        BatteryIcon()
                    }
                }
                Box(
                    modifier =
                        Modifier
                            .padding(start = 14.dp, end = 99.dp)
                            .fillMaxWidth()
                            .height(84.dp),
                ) {
                    Column(
                        modifier =
                            Modifier
                                .padding(start = 4.dp)
                                .fillMaxSize(),
                    ) {
                        OAFootNote1TextView(
                            text = "$enrollmentStatusDescription",
                            color = AppTheme.colors.tertiary05,
                            modifier =
                                Modifier
                                    .padding(top = 11.dp)
                                    .semantics {
                                        contentDescription = "$enrollmentStatusDescription"
                                    },
                            textAlign = TextAlign.Left,
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun AlertImage() {
    Box(
        modifier =
            Modifier
                .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
                .size(24.dp),
        // Ensures the Box is the same size as the circle
        contentAlignment = Alignment.Center,
    ) {
        Image(
            modifier = Modifier.matchParentSize(),
            // Makes the image fill the Box
            painter = painterResource(id = R.drawable.circle),
            contentDescription = "Circle image",
            contentScale = ContentScale.Inside,
            colorFilter = ColorFilter.tint(AppTheme.colors.primary02),
        )
        Image(
            modifier = Modifier.matchParentSize(),
            // Makes the image fill the Box
            painter = painterResource(id = R.drawable.alert_enrollment_failure_charge_assist),
            contentDescription = "Circle image",
            contentScale = ContentScale.Inside,
            colorFilter = ColorFilter.tint(AppTheme.colors.primary01),
        )
    }
}

@Composable
fun EntryPointCardData(
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
    chargeAssistEntryButtonData: Pair<String, String>?,
) {
    val (buttonText, route) = chargeAssistEntryButtonData ?: Pair("", "")
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, end = 15.dp, bottom = 17.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Card(
            elevation = 0.dp,
            shape = RoundedCornerShape(size = 8.dp),
            modifier =
                Modifier
                    .height(116.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .background(AppTheme.colors.tile05),
            ) {
                Column {
                    Column(
                        modifier =
                            Modifier
                                .padding(top = 15.dp, start = 14.dp, bottom = 6.dp),
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(),
                        ) {
                            ChargeAssistEntryPointTitleAndLogo()
                        }
                        Box(
                            modifier =
                                Modifier
                                    .padding(start = 14.dp, end = 99.dp)
                                    .width(198.dp)
                                    .height(84.dp),
                        ) {
                            Column(
                                modifier =
                                    Modifier
                                        .padding(start = 4.dp)
                                        .fillMaxSize(),
                            ) {
                                EntryTileText(chargeAssistEntryButtonData)
                            }
                        }
                    }
                }

                Enroll2ndEntryPointButton(
                    navController,
                    buttonText,
                    route,
                    chargeAssistViewModel = chargeAssistViewModel,
                )
            }
        }
    }
}

@Composable
fun ChargeAssistActiveVehicleStatusCardData(chargeAssistViewModel: ChargeAssistViewModel) {
    val context = LocalContext.current
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, end = 15.dp, bottom = 17.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Card(
            elevation = 0.dp,
            shape = RoundedCornerShape(size = 8.dp),
            modifier =
                Modifier
                    .semantics {
                        contentDescription =
                            context.getString(R.string.charge_assist_entry_card)
                    }
                    .height(116.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .background(AppTheme.colors.tile05),
            ) {
                Column {
                    Column(
                        modifier =
                            Modifier
                                .padding(top = 15.dp, start = 14.dp, bottom = 6.dp),
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(),
                        ) {
                            ChargeAssistEntryPointTitleAndLogo() // logo and CA title
                            Spacer(modifier = Modifier.weight(1f))
                            BatteryIcon()
                        }

                        ChargeAssistActiveChargeData(chargeAssistViewModel)
                    }
                }
            }
        }
    }
}

@Composable
fun ChargeAssistActiveChargeData(chargeAssistViewModel: ChargeAssistViewModel) {
    val chargeCompletionTime = chargeAssistViewModel.chargeAssistVMHelper.chargeCompletionTime.collectAsState()
    val pluggedInTime = chargeAssistViewModel.chargeAssistVMHelper.pluggedInTime.collectAsState()
    val chargingPercentage = chargeAssistViewModel.chargeAssistVMHelper.chargingPercentage.collectAsState()
    Box(
        modifier =
            Modifier
                .padding(start = 14.dp, end = 24.dp, top = 8.dp)
                .height(84.dp),
    ) {
        // Charge Progress
        Column(
            modifier =
                Modifier
                    .padding(start = 4.dp)
                    .fillMaxSize(),
        ) {
            Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
                OACallOut1TextView(
                    text = "${chargingPercentage.value} %",
                    color = AppTheme.colors.tertiary03,
                    modifier = Modifier.padding(end = 5.dp),
                )

                CAArrowPercentageIcon()
                Text(
                    modifier = Modifier.padding(top = 4.dp),
                    text = stringResource(R.string.hundred_percentage),
                    style = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.Bold),
                    color = AppTheme.colors.tertiary03,
                )

                Spacer(modifier = Modifier.weight(1f))
                OACallOut1TextView(
                    text = stringResource(R.string.full_charge_estimated),
                    color = AppTheme.colors.tertiary03,
                    modifier = Modifier,
                )
            }

            Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
                OAFootNote1TextView(
                    text = stringResource(R.string.plugged_in_at, pluggedInTime.value ?: ""),
                    color = AppTheme.colors.tertiary05,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp),
                )
                Spacer(modifier = Modifier.weight(1f))
                Text("by ")
                Text(
                    text = chargeCompletionTime.value,
                    style = TextStyle(fontWeight = FontWeight.Bold),
                    color = AppTheme.colors.tertiary03,
                )
            }
        }
    }
}

@Composable
fun CAArrowPercentageIcon() {
    Image(
        modifier =
            Modifier
                .semantics { contentDescription = "Battery Icon" }
                .padding(end = 5.dp, top = 4.dp)
                .size(15.dp),
        painter =
            painterResource(
                id = R.drawable.ca_arrow_percentage,
            ),
        contentDescription = "arrow icon",
        contentScale = ContentScale.Fit,
        colorFilter = ColorFilter.tint(AppTheme.colors.tertiary00),
    )
}

@Composable
fun BatteryIcon() {
    Image(
        modifier =
            Modifier
                .semantics { contentDescription = "Battery Icon" }
                .padding(end = 24.dp, bottom = 5.dp)
                .size(24.dp),
        painter =
            painterResource(
                id = R.drawable.battery_plug_charge_assist_active,
            ),
        contentDescription = "battery icon",
        contentScale = ContentScale.Fit,
        colorFilter = ColorFilter.tint(AppTheme.colors.button03d),
    )
}

// Entry point for enrollemnt
@Composable
fun EntryTileText(chargeAssistEntryButtonData: Pair<String, String>?) {
    val (buttonText) = chargeAssistEntryButtonData ?: Pair("", "")

    if (buttonText == "Enroll") {
        Text(
            modifier =
                Modifier
                    .padding(top = 11.dp)
                    .semantics { contentDescription = "A new program is available" },
            text = stringResource(R.string.program_is_available),
            style =
                TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 18.sp,
                    fontFamily = FontFamily(Font(com.toyota.one_ui.R.font.toyotatype_regular)),
                ),
            fontWeight = FontWeight(PROGRAM_AVAILABLE_TEXT_WEIGHT),
            color = AppTheme.colors.tertiary05,
        )
        Text(
            modifier =
                Modifier
                    .semantics {
                        contentDescription = "Enroll to take advantage of potential incentives"
                    },
            text = stringResource(R.string.enroll_to_for_incentives_text),
            style =
                TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 18.sp,
                    fontFamily = FontFamily(Font(com.toyota.one_ui.R.font.toyotatype_regular)),
                ),
            fontWeight = FontWeight(ENROLL_CHARGE_ASSIST_TEXT_WEIGHT),
            color = AppTheme.colors.tertiary05,
        )
    } else if (buttonText == "Enter") {
        Text(
            modifier =
                Modifier
                    .padding(top = 11.dp)
                    .semantics {
                        contentDescription =
                            "Enter your zip code to check if there is a program available."
                    },
            text = stringResource(R.string.enter_zip_to_check_CA_program_availability),
            style =
                TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 18.sp,
                    fontFamily = FontFamily(Font(com.toyota.one_ui.R.font.toyotatype_regular)),
                ),
            fontWeight = FontWeight(ENROLL_CHARGE_ASSIST_TEXT_WEIGHT),
            color = AppTheme.colors.tertiary05,
        )
    }
}

@Composable
fun Enroll2ndEntryPointButton(
    navController: NavController,
    buttonText: String,
    route: String,
    chargeAssistViewModel: ChargeAssistViewModel,
) {
    val buttonStyle =
        Modifier
            .semantics { contentDescription = "$buttonText Button" }
            .padding(start = 200.dp, end = 14.dp, top = 30.dp, bottom = 36.dp)
            .wrapContentWidth()
            .defaultMinSize(minWidth = 72.dp)
            .height(36.dp)

    val textStyle =
        TextStyle(
            fontSize = 14.sp,
            lineHeight = 20.sp,
            fontFamily = FontFamily(Font(com.toyota.one_ui.R.font.toyotatype_regular)),
        )

    /*
      If zip is eligible for enrollment -> Button name: Enroll
      if zip is empty -> Button name: Enter
     */

    // Enroll button content
    Column(
        verticalArrangement = Arrangement.Center,
        modifier =
            Modifier
                .fillMaxSize()
                .wrapContentWidth()
                .height(49.dp),
    ) {
        OutlinedButton(
            onClick = {
                when (buttonText) {
                    "Enroll" ->
                        chargeAssistViewModel.chargeAssistVMHelper
                            .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_INFO_ENROLL)

                    "Enter" ->
                        chargeAssistViewModel.chargeAssistVMHelper
                            .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_CHARGE_INFO_ENTER)
                }
                if (route.isNotNullOrEmpty()) navController.navigate(route)
            },
            modifier = buttonStyle,
            border = BorderStroke(2.dp, AppTheme.colors.outline01),
            shape = RoundedCornerShape(100.dp),
            colors =
                ButtonDefaults.outlinedButtonColors(
                    backgroundColor = Color.Transparent,
                ),
        ) {
            Text(
                text = buttonText,
                style = textStyle,
                maxLines = 1,
                softWrap = false,
                overflow = TextOverflow.Visible,
                modifier = Modifier.padding(horizontal = 8.dp),
                fontWeight = FontWeight(ENROLL_BUTTON_TEXT_WEIGHT),
                color = AppTheme.colors.tertiary03,
            )
        }
    }
}

const val ENROLL_BUTTON_TEXT_WEIGHT = 600

@Composable
fun ChargeAssistEntryPointTitleAndLogo() {
    Image(
        modifier =
            Modifier
                .semantics { contentDescription = "Charge Assist Logo" }
                .padding(start = 15.dp)
                .size(24.dp),
        painter =
            painterResource(
                id = R.drawable.charge_assist_logo_schedule,
            ),
        contentDescription = "image description",
        contentScale = ContentScale.Fit,
        colorFilter = ColorFilter.tint(AppTheme.colors.tertiary03),
    )
    Spacer(modifier = Modifier.width(4.dp))
    OABody4TextView(
        modifier =
            Modifier
                .semantics { contentDescription = "Charge Assist Title" },
        text = stringResource(R.string.charge_assist),
        color = AppTheme.colors.tertiary03,
    )
}

fun getFinalStatus(enrollmentStatus: Resource<ApiResponse<EnrollmentCheckResponsePayload?>?>?): String {
    val activationStatus =
        enrollmentStatus
            ?.data
            ?.payload
            ?.partnerEnrollment
            ?.partnerStatus
            ?.getOrNull(
                0,
            )?.vinStatus
            ?.getOrNull(0)
            ?.status // //RegistrationSuccess or RegistrationFailure in this structure

    val notFoundStatus =
        enrollmentStatus
            ?.data
            ?.payload
            ?.partnerEnrollment
            ?.partnerStatus
            ?.getOrNull(0)
            ?.status // "Not Found"

    val finalStatus =
        when {
            !activationStatus.isNullOrEmpty() -> activationStatus // Use activationStatus if it's not null or empty
            !notFoundStatus.isNullOrEmpty() -> notFoundStatus // Use notFoundStatus if status is null/empty
            else -> "Unknown Status" // Default value if both are null/empty
        }

    return finalStatus
}
