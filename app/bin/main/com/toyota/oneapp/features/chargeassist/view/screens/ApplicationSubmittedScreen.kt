package com.toyota.oneapp.features.chargeassist.view.screens

import android.content.Context
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Card
import androidx.compose.material.Divider
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun ApplicationSubmittedScreen(
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
) {
    val context = LocalContext.current
    Column(
        modifier =
            Modifier
                .background(AppTheme.colors.tertiary15)
                .padding(16.dp)
                .fillMaxSize()
                // enable vertical scroll
                .verticalScroll(rememberScrollState()),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxWidth(),
        ) {
            ApplicationSubmittedCheck()
            OASubHeadLine3TextView(
                text = stringResource(R.string.application_submitted),
                color = AppTheme.colors.tertiary03,
                modifier =
                    Modifier
                        .padding(bottom = 24.dp)
                        .semantics {
                            contentDescription = context.getString(R.string.application_submitted_title)
                        },
            )
            SubmittedApplicationFinalDetails()
        }
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxWidth(),
        ) {
            PrimaryButton02(
                text = stringResource(R.string.back_to_charge_info),
                modifier =
                    Modifier
                        .padding(top = 16.dp)
                        .semantics {
                            contentDescription = context.getString(R.string.back_to_charge_info_button)
                        },
                click = {
                    navController.navigate(OAScreen.ChargeInfo.route)
                    chargeAssistViewModel.chargeAssistVMHelper
                        .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_BACK_TO_CHARGE_INFO_SCREEN_BUTTON)
                },
            )
        }
    }
    BackHandler {
        // Navigate back to the previous screen
        navController.popBackStack()
    }
}

const val TITLE_WEIGHT = 400

@Composable
fun SubmittedApplicationFinalDetails() {
    val context = LocalContext.current
    Card(
        shape = RoundedCornerShape(16.dp),
        elevation = 0.dp,
        modifier =
            Modifier
                .wrapContentWidth()
                .height(580.dp),
        backgroundColor = AppTheme.colors.tile02,
    ) {
        Column {
            Text(
                modifier =
                    Modifier
                        .semantics {
                            contentDescription = context.getString(R.string.so_what_happens_next)
                        }
                        .padding(start = 16.dp, top = 10.dp),
                text = stringResource(R.string.so_what_happens_next),
                style =
                    TextStyle(
                        fontSize = 18.sp,
                        lineHeight = 27.sp,
                        fontFamily = FontFamily(Font(com.toyota.one_ui.R.font.toyotatype_regular)),
                        fontWeight = FontWeight(TITLE_WEIGHT),
                        color = AppTheme.colors.tertiary03,
                    ),
            )

            SubmittedApplicationInfoTiles(context)
        }
    }
}

@Composable
fun SubmittedApplicationInfoTiles(context: Context) {
    // Number point 1
    Row {
        ApplicationSubmittedNumberPoints(
            number = "1",
            modifier = Modifier.padding(top = 23.dp),
        )
        OABody3TextView(
            modifier =
                Modifier
                    .semantics {
                        contentDescription =
                            context.getString(R.string.utility_company_will_verify_enrollment_point)
                    }
                    .wrapContentWidth()
                    .padding(end = 38.dp)
                    .padding(top = 31.dp)
                    .padding(bottom = 14.dp),
            text = stringResource(id = R.string.utility_company_will_verify_enrollment_point),
            color = AppTheme.colors.tertiary03,
        )
    }
    UtilityVerifyEnrollmentDescription(context)
    TileDivider()
    // Number point 2
    Row {
        ApplicationSubmittedNumberPoints(number = "2", modifier = Modifier)
        OABody3TextView(
            modifier =
                Modifier
                    .semantics {
                        contentDescription =
                            context.getString(R.string.development_of_scheduling_profile_point)
                    }
                    .wrapContentWidth()
                    .padding(end = 41.dp)
                    .padding(top = 16.dp)
                    .padding(bottom = 14.dp),
            text = stringResource(id = R.string.development_of_scheduling_profile_point),
            color = AppTheme.colors.tertiary03,
        )
    }
    DevelopScheduleProfileDescription(context)
    TileDivider()
    // Number point 3
    Row {
        ApplicationSubmittedNumberPoints(number = "3")
        OABody3TextView(
            modifier =
                Modifier
                    .semantics {
                        contentDescription =
                            context.getString(R.string.creation_of_personalized_charging_schedule_point)
                    }
                    .wrapContentWidth()
                    .padding(top = 16.dp, end = 31.dp)
                    .padding(bottom = 14.dp),
            text = stringResource(R.string.creation_of_personalized_charging_schedule_point),
            color = AppTheme.colors.tertiary03,
        )
    }
    PersonalizedScheduleDescription(context)
}

@Composable
fun UtilityVerifyEnrollmentDescription(context: Context) {
    OACallOut1TextView(
        modifier =
            Modifier
                .padding(start = 22.dp)
                .semantics {
                    contentDescription =
                        context.getString(R.string.utility_company_will_verify_enrollment_details)
                },
        text = stringResource(id = R.string.utility_company_will_verify_enrollment_details),
        color = AppTheme.colors.tertiary05,
    )
}

@Composable
fun DevelopScheduleProfileDescription(context: Context) {
    OACallOut1TextView(
        modifier =
            Modifier
                .wrapContentWidth()
                .padding(start = 22.dp, end = 47.dp)
                .semantics {
                    contentDescription =
                        context.getString(R.string.development_of_scheduling_profile_details)
                },
        text = stringResource(R.string.development_of_scheduling_profile_details),
        color = AppTheme.colors.tertiary05,
    )
}

const val DIVIDER_COLOR = 0xFFF1F1F1

@Composable
fun TileDivider() {
    // Divider
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Divider(
            color = Color(DIVIDER_COLOR),
            thickness = 1.dp,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(top = 16.dp)
                    .padding(bottom = 16.dp),
        )
    }
}

@Composable
fun PersonalizedScheduleDescription(context: Context) {
    OACallOut1TextView(
        modifier =
            Modifier
                .wrapContentWidth()
                .padding(start = 22.dp, end = 10.dp)
                .semantics {
                    contentDescription =
                        context.getString(R.string.creation_of_personalized_charging_schedule_details)
                },
        text = stringResource(R.string.creation_of_personalized_charging_schedule_details),
        color = AppTheme.colors.tertiary05,
    )
}

const val TEXT_NUMBER_WEIGHT = 600

@Composable
fun ApplicationSubmittedNumberPoints(
    number: String,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier =
            modifier
                .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
                .size(48.dp),
        // Ensures the Box is the same size as the circle
        contentAlignment = Alignment.Center,
    ) {
        Image(
            modifier = Modifier.matchParentSize(),
            // Makes the image fill the Box
            painter = painterResource(id = R.drawable.circle),
            contentDescription = "Circle image",
            contentScale = ContentScale.Fit,
            colorFilter = ColorFilter.tint(AppTheme.colors.button05b),
        )

        Text(
            text = number,
            // Toyota/Button
            style =
                TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 20.sp,
                    fontFamily = FontFamily(Font(com.toyota.one_ui.R.font.toyotatype_regular)),
                    fontWeight = FontWeight(TEXT_NUMBER_WEIGHT),
                    color = AppTheme.colors.tertiary03,
                    textAlign = TextAlign.Center,
                ),
        )
    }
}

@Composable
fun ApplicationSubmittedCheck() {
    Box(
        modifier =
            Modifier
                .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
                .size(48.dp),
        // Ensures the Box is the same size as the circle
        contentAlignment = Alignment.Center,
    ) {
        Image(
            modifier = Modifier.matchParentSize(),
            // Makes the image fill the Box
            painter = painterResource(id = R.drawable.circle),
            contentDescription = "Circle image",
            contentScale = ContentScale.Crop,
            colorFilter = ColorFilter.tint(AppTheme.colors.secondary02),
        )

        Image(
            modifier = Modifier.size(24.dp),
            // Sizes the check image
            painter = painterResource(id = R.drawable.check),
            contentDescription = "Check overlay",
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewApplicationSubmittedScreen() {
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()
    ApplicationSubmittedScreen(rememberNavController(), chargeAssistViewModel)
}
