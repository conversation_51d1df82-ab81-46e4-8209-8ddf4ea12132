package com.toyota.oneapp.features.chargeassist.dataaccess.model.enrollment.request

import com.google.gson.annotations.SerializedName

data class EnrollmentRequestModel(
    @SerializedName("guid")
    val guid: String? = "",
    @SerializedName("emailAddress")
    val emailAddress: String? = "",
    @SerializedName("zipCode")
    val zipCode: String? = "",
    @SerializedName("country")
    val country: String? = "",
    @SerializedName("firstName")
    val firstName: String? = "",
    @SerializedName("lastName")
    val lastName: String? = "",
    @SerializedName("address1")
    val address1: String? = "",
    @SerializedName("address2")
    val address2: String? = "",
    @SerializedName("city")
    val city: String? = "",
    @SerializedName("stateCode")
    val stateCode: String? = "",
    @SerializedName("timeZone")
    val timeZone: String? = "",
    @SerializedName("evNetPassword")
    val evNetPassword: String? = "",
    @SerializedName("evNetwork")
    val evNetwork: String? = "",
    @SerializedName("mobilePhone")
    val mobilePhone: String? = "",
    @SerializedName("programName")
    val programName: String? = "",
    @SerializedName("utilityProviderAccountNumber")
    val utilityProviderAccountNumber: String? = "",
)
