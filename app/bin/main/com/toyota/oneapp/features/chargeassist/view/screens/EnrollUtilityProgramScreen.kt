package com.toyota.oneapp.features.chargeassist.view.screens

import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Checkbox
import androidx.compose.material.CheckboxDefaults
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import coil.compose.rememberAsyncImagePainter
import com.toyota.oneapp.R
import com.toyota.oneapp.R.string.enter_your_info_to_enroll_text
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.features.chargeassist.ChargeAssistRoute
import com.toyota.oneapp.features.chargeassist.dataaccess.model.programavailability.ProgramEligibilityModelPayload
import com.toyota.oneapp.features.chargeassist.view.helper.ChargeAssistBackButtonTitleHeader
import com.toyota.oneapp.features.chargeassist.view.helper.EnrollScreenBottomDescription
import com.toyota.oneapp.features.chargeassist.view.helper.MainCard
import com.toyota.oneapp.features.chargeassist.view.helper.UnEnrollmentConfirmationBottomSheet
import com.toyota.oneapp.features.chargeassist.view.helper.getPhoneNumberDomesticFormat
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLED
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_PENDING
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.ENROLLMENT_SUCCESSFUL_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.NOT_FOUND
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.UNENROLLMENT_PENDING_STATUSES
import com.toyota.oneapp.features.chargeassist.view.screens.ChargeAssistEnrollmentStates.Companion.UNENROLLMENT_SUCCESSFUL
import com.toyota.oneapp.features.chargeassist.view.viewmodel.ChargeAssistViewModel
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoCustomer
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.composable.OAButtonTextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OACaption1TextView
import com.toyota.oneapp.features.core.composable.OACaption2TextView
import com.toyota.oneapp.features.core.composable.OAFootNote1TextView
import com.toyota.oneapp.features.core.composable.OAHtmlTextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.shimmerEffect
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.OAThemeProvider
import com.toyota.oneapp.features.dashboard.dashboard.presentation.DashboardViewModel
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.ApiResponse
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

private const val MAX_ACCOUNT_NUMBER_LENGTH = 10

@Composable
fun EnrollUtilityProgramScreen(
    navController: NavController,
    dashboardViewModel: DashboardViewModel,
    chargeAssistViewModel: ChargeAssistViewModel,
    zipFromZipNotAvailableScreen: String?,
    enableEnrollmentFields: Boolean,
    currentEnrollmentStatus: String?,
) {
    val coroutineScope = rememberCoroutineScope()
    val unEnrollFromCABottomSheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            skipHalfExpanded = true,
        )

    val userEnrollmentState =
        remember {
            UserEnrollmentState(
                userName = mutableStateOf(""),
                userAddress = mutableStateOf(""),
                userCity = mutableStateOf(""),
                userState = mutableStateOf(""),
                userZip = mutableStateOf(""),
                userUtilityAccountNumber = mutableStateOf(""),
                userPhoneNumber = mutableStateOf(""),
                userEmail = mutableStateOf(""),
                legalConsentConfirmed = mutableStateOf(false),
                isValidAccountLengthEntered = mutableStateOf(false),
            )
        }

    LaunchedEffect(Unit) {
        dashboardViewModel.fetchProfileData()
        chargeAssistViewModel.fetchZipEligibilityStatus()
        chargeAssistViewModel.fetchEnrollmentStatus()
        chargeAssistViewModel.fetchEligibilityForZipOnUnavailableScreen(zipFromZipNotAvailableScreen)
        chargeAssistViewModel.chargeAssistVMHelper.loadZipValidity()
    }
    val collectDashboardData by dashboardViewModel.profileData.collectAsState()
    val customerProfileData = collectDashboardData?.payload?.customer
    val isZipValidOnZNAScreen =
        chargeAssistViewModel.chargeAssistVMHelper.isZipEnteredOnEligibilityScreenValid
            .collectAsState().value
    val zipDataFlow: StateFlow<Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?> =

        if (isZipValidOnZNAScreen == null || isZipValidOnZNAScreen == false) {
            chargeAssistViewModel.zipEligibilityStatusData
        } else {
            chargeAssistViewModel.zipEligibilityStatusDataForUnavailableScreenZip
        }

    val collectZipProgramData by zipDataFlow.collectAsState()
    // set initial values here by default from view model
    prePopulateEnrollData(
        customerProfileData,
        userEnrollmentState,
        zipFromZipNotAvailableScreen,
    )

    val enrollmentScreenData =
        EnrollmentScreenData(
            zipFromZipNotAvailableScreen = zipFromZipNotAvailableScreen,
            currentEnrollmentStatus = currentEnrollmentStatus,
            enableEnrollmentFields = enableEnrollmentFields,
            unEnrollFromCABottomSheetState = unEnrollFromCABottomSheetState,
            userEnrollmentState = userEnrollmentState,
            collectZipProgramData = collectZipProgramData,
        )

    Box(modifier = Modifier.fillMaxSize()) {
        MainScreenContent(
            navController,
            chargeAssistViewModel,
            userEnrollmentState,
            enrollmentScreenData,
        )
        UnEnrollmentConfirmationBottomSheet(
            navController,
            unEnrollFromCABottomSheetState,
            chargeAssistViewModel = chargeAssistViewModel,
        )
    }
    // Handle back press
    BackHandler {
        // if user clicks back button reset the zipEligibility To null
        coroutineScope.launch {
            chargeAssistViewModel.chargeAssistVMHelper.resetZipEligibilityToNull()

            when (unEnrollFromCABottomSheetState.isVisible) {
                true -> unEnrollFromCABottomSheetState.hide()
                false -> navController.popBackStack()
            }
        }
    }
}

@Composable
fun MainScreenContent(
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
    userEnrollmentState: UserEnrollmentState,
    enrollmentScreenData: EnrollmentScreenData,
) {
    val coroutineScope = rememberCoroutineScope()

    LazyColumn(
        modifier =
            Modifier
                .fillMaxSize(),
    ) {
        item {
            ChargeAssistBackButtonTitleHeader(navController, true, onBackClick = {
                // if user clicks back button reset the zipEligibility To null

                coroutineScope.launch {
                    chargeAssistViewModel.chargeAssistVMHelper.resetZipEligibilityToNull()
                }

                // log back button press event specifically for this Enroll Screen
                chargeAssistViewModel.chargeAssistVMHelper
                    .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_ENROLL_BACK_BUTTON)
            })
        }
        item {
            ChargeAssistEnrollSubtitle()
        }
        item {
            ChargeProgramCard(
                navController = navController,
                chargeAssistViewModel,
                enrollmentScreenData.zipFromZipNotAvailableScreen,
                enrollmentScreenData.currentEnrollmentStatus,
                enrollmentScreenData.collectZipProgramData,
            )
        }
        item {
            UserDataEntryComponents(
                userEnrollmentState,
                enrollmentScreenData,
                navController = navController,
                chargeAssistViewModel,
                enrollmentScreenData.collectZipProgramData,
            )
        }
    }
}

fun prePopulateEnrollData(
    profileCustomerData: ProfileInfoCustomer?,
    userEnrollmentState: UserEnrollmentState,
    zipFromZipNotAvailableScreen: String?,
) {
    val systemUserNameData =
        "${profileCustomerData?.firstName ?: ""} ${profileCustomerData?.lastName ?: ""}".trim()
    val systemUserAddressData =
        profileCustomerData
            ?.addresses
            ?.getOrNull(0)
            ?.address ?: ""

    val systemUserCityData =
        profileCustomerData
            ?.addresses
            ?.getOrNull(0)
            ?.city ?: ""

    val systemUserStateData =
        profileCustomerData
            ?.addresses
            ?.getOrNull(0)
            ?.state ?: ""

    val systemUserZipData =
        profileCustomerData
            ?.addresses
            ?.getOrNull(0)
            ?.zipCode ?: zipFromZipNotAvailableScreen

    val systemUserPhoneNumberData =
        profileCustomerData
            ?.phoneNumbers
            ?.getOrNull(0)
            ?.phoneNumber?.toString()?.getPhoneNumberDomesticFormat() ?: ""

    val systemUserEmailData =
        profileCustomerData
            ?.emails
            ?.getOrNull(0)
            ?.emailAddress ?: ""

    userEnrollmentState.userName.value = systemUserNameData
    userEnrollmentState.userAddress.value = systemUserAddressData
    userEnrollmentState.userCity.value = systemUserCityData
    userEnrollmentState.userState.value = systemUserStateData
    userEnrollmentState.userZip.value = systemUserZipData.toString()
    userEnrollmentState.userUtilityAccountNumber.value =
        "" // not pulled from API (entered from user)
    userEnrollmentState.userPhoneNumber.value = systemUserPhoneNumberData
    userEnrollmentState.userEmail.value = systemUserEmailData
}

@Composable
fun ChargeAssistEnrollSubtitle() {
    OACallOut1TextView(
        text = stringResource(enter_your_info_to_enroll_text),
        color = AppTheme.colors.tertiary05,
        textAlign = TextAlign.Center,
        modifier = Modifier.padding(top = 17.dp),
    )
}

@Composable
fun ChargeProgramCard(
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
    zipFromZipNotAvailableScreen: String?,
    currentEnrollmentStatus: String?,
    collectData: Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?,
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        when (collectData) {
            null, is Resource.Loading<*> -> {
                Box(
                    modifier =
                        Modifier
                            .padding(vertical = 24.dp)
                            .fillMaxWidth()
                            .height(116.dp)
                            .shimmerEffect(),
                )
            }

            is Resource.Success<*> -> {
                // Card
                MainCard(
                    navController = navController,
                    chargeAssistViewModel = chargeAssistViewModel,
                    zipFromZipNotAvailableScreen = zipFromZipNotAvailableScreen,
                    currentEnrollmentStatus = currentEnrollmentStatus,
                    collectData = collectData,
                )
            }

            else -> { // left intentionally blank
            }
        }
    }
}

@Composable
fun ProgramNameAndDescriptionOrStatus(
    name: String?,
    description: String?,
) {
    OAThemeProvider(isDarkMode = AppTheme.darkMode.collectAsState().value) {
        OASubHeadLine1TextView(
            text = name.orEmpty(),
            color = AppTheme.colors.tertiary03,
        )

        val enrollmentStatusColor =
            when (description) {
                ENROLLMENT_PENDING -> AppTheme.colors.primary01
                ENROLLED -> AppTheme.colors.button03d
                else -> AppTheme.colors.tertiary05
            }

        OAFootNote1TextView(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(end = 51.dp),
            text = description.orEmpty(),
            color = enrollmentStatusColor,
            textAlign = TextAlign.Left,
        )
    }
}

@Composable
fun UserDataEntryComponents(
    userEnrollmentState: UserEnrollmentState,
    enrollmentScreenData: EnrollmentScreenData,
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
    collectData: Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?,
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        UserTextBoxForEnrollment(
            textState = userEnrollmentState.userName,
            enableEnrollmentFields = enrollmentScreenData.enableEnrollmentFields,
            label = "Name",
            contentDescription = "Name",
        )
        UserTextBoxForEnrollment(
            textState = userEnrollmentState.userAddress,
            enableEnrollmentFields = enrollmentScreenData.enableEnrollmentFields,
            label = "Home Charging Address",
            contentDescription = "Address Field",
        )
        UserTextBoxForEnrollment(
            textState = userEnrollmentState.userCity,
            enableEnrollmentFields = enrollmentScreenData.enableEnrollmentFields,
            label = "City",
            contentDescription = "City Field",
        )

        UserTextBoxForEnrollment(
            textState = userEnrollmentState.userState,
            enableEnrollmentFields = enrollmentScreenData.enableEnrollmentFields,
            label = "State",
            contentDescription = "State Field",
        )

        UserTextBoxForEnrollment(
            textState = userEnrollmentState.userZip,
            enableEnrollmentFields = enrollmentScreenData.enableEnrollmentFields,
            label = stringResource(R.string.zip_code),
            contentDescription = stringResource(R.string.zip_code_field),
        )
    }
    // Utility Account Number text

    Box {
        OACallOut1TextView(
            modifier = Modifier.padding(top = 16.dp, end = 55.dp, start = 16.dp, bottom = 12.dp),
            text = stringResource(R.string.utility_account_number_found_on_statement),
            color = AppTheme.colors.tertiary07,
        )
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        UserUtilityAccountNumberTextBox(
            userEnrollmentState,
            enrollmentScreenData.enableEnrollmentFields,
            collectData,
        )

        UserTextBoxForEnrollment(
            textState = userEnrollmentState.userPhoneNumber,
            enableEnrollmentFields = enrollmentScreenData.enableEnrollmentFields,
            label = "Phone Number",
            contentDescription = "Phone Number",
        )

        UserTextBoxForEnrollment(
            textState = userEnrollmentState.userEmail,
            enableEnrollmentFields = false,
            label = "Email",
            contentDescription = "Email",
        )
        EnrollmentAndConsentSectionWithButtons(
            userEnrollmentState,
            enrollmentScreenData,
            navController,
            chargeAssistViewModel,
            collectData = collectData,
        )
    }
}

@Composable
fun EnrollmentAndConsentSectionWithButtons(
    userEnrollmentState: UserEnrollmentState,
    enrollmentScreenData: EnrollmentScreenData,
    navController: NavController,
    chargeAssistViewModel: ChargeAssistViewModel,
    collectData: Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?,
) {
    val coroutineScope: CoroutineScope = rememberCoroutineScope()

    /*
    Conditional display off disclaimer, legal consent, description,
    enroll and dismiss button based on users enrollment status (Enrollment Pending & Enrolled)
     */
    val currentEnrollmentStatus = enrollmentScreenData.currentEnrollmentStatus
    when {
        currentEnrollmentStatus.equals(NOT_FOUND, ignoreCase = true) ||
            currentEnrollmentStatus.equals(UNENROLLMENT_SUCCESSFUL, ignoreCase = true) -> {
            DisclaimerComponent()
            LegalConsentCheckBox(userEnrollmentState.legalConsentConfirmed, collectData, chargeAssistViewModel)
            EnrollUtilitySubmitButton(
                isButtonEnabled =
                    userEnrollmentState.legalConsentConfirmed.value &&
                        userEnrollmentState.isValidAccountLengthEntered.value &&
                        areAllTextFieldsFilled(userEnrollmentState),
                click = {
                    navController.navigate(ChargeAssistRoute.ApplicationSubmittedScreen.route)
                    navController.navigate("application_submitted_screen")
                },
                userEnrollmentState = userEnrollmentState,
            ) // enable if user clicks check box
        }

        // Pending
        currentEnrollmentStatus in ENROLLMENT_PENDING_STATUSES ||
            currentEnrollmentStatus in UNENROLLMENT_PENDING_STATUSES
        -> {
            EnrollScreenBottomDescription(
                enrollmentScreenData.currentEnrollmentStatus.toString(),
                chargeAssistViewModel,
                navController,
                enrollmentScreenData.unEnrollFromCABottomSheetState,
                coroutineScope,
            ) // handles pending and successful enrollment display
        }
        // Successfully Enrolled
        currentEnrollmentStatus in ENROLLMENT_SUCCESSFUL_STATUSES ->
            EnrollScreenBottomDescription(
                enrollmentScreenData.currentEnrollmentStatus.toString(),
                chargeAssistViewModel,
                navController,
                enrollmentScreenData.unEnrollFromCABottomSheetState,
                coroutineScope,
            ) // handles pending and success enrollment display
    }
}

// Function to check if all text fields are populated
fun areAllTextFieldsFilled(userEnrollmentState: UserEnrollmentState): Boolean {
    return userEnrollmentState.userName.value.isNotBlank() &&
        userEnrollmentState.userAddress.value.isNotBlank() &&
        userEnrollmentState.userState.value.isNotBlank() &&
        userEnrollmentState.userCity.value.isNotBlank() &&
        userEnrollmentState.userPhoneNumber.value.isNotBlank() &&
        userEnrollmentState.userZip.value.isNotBlank() &&
        userEnrollmentState.userEmail.value.isNotBlank()
}

@Composable
fun EnrollUtilitySubmitButton(
    isButtonEnabled: Boolean,
    click: () -> Unit,
    userEnrollmentState: UserEnrollmentState,
) {
    val context = LocalContext.current
    val chargeAssistViewModel: ChargeAssistViewModel = hiltViewModel()
    val enrollmentState by chargeAssistViewModel.userEnrollmentStatusData.collectAsState()
    var hasSubmitted by remember { mutableStateOf(false) } // Track submission button click

    LaunchedEffect(Unit) {
        chargeAssistViewModel.userEnrollmentStatusData.collect {
            if (hasSubmitted) {
                var message = ""
                when (enrollmentState) {
                    is Resource.Success -> {
                        message = "User Enrollment Application Submitted Successfully"
                        click() // go to application submitted screen
                    }

                    is Resource.Failure -> {
                        message = "User Not Enrolled Successfully"
                    }

                    else -> { // left intentionally blank
                    }
                }
                Toast.makeText(context, message, Toast.LENGTH_LONG).show()
                hasSubmitted = false
            }
        }
    }

    val buttonEnableStatusText =
        if (isButtonEnabled) "Submit Button enabled" else "Submit Button disabled"

    Button(
        onClick = {
            if (isButtonEnabled) {
                hasSubmitted = true

                chargeAssistViewModel.updateOfficialAddress(
                    address = userEnrollmentState.userAddress.value,
                    city = userEnrollmentState.userCity.value,
                    state = userEnrollmentState.userState.value,
                    zip = userEnrollmentState.userZip.value,
                    userName = userEnrollmentState.userName.value,
                )

                // enroll user into Charge Assist
                chargeAssistViewModel.fetchEnrollmentOfUserIntoChargeAssistResponse(
                    address = userEnrollmentState.userAddress.value,
                    city = userEnrollmentState.userCity.value,
                    state = userEnrollmentState.userState.value,
                    zip = userEnrollmentState.userZip.value,
                    userEnrollmentState.userUtilityAccountNumber.value,
                )
                click() // take user to Application Submitted Screen
            }

            chargeAssistViewModel.chargeAssistVMHelper
                .logAnalyticsEvent(AnalyticsEvent.CHARGE_ASSIST_ENROLLMENT_COMPLETE_BUTTON)
        },
        shape = RoundedCornerShape(100.dp),
        colors =
            ButtonDefaults.buttonColors(
                backgroundColor = AppTheme.colors.button02a,
                disabledBackgroundColor = AppTheme.colors.button02d,
            ),
        contentPadding = PaddingValues(vertical = 16.dp, horizontal = 60.dp),
        modifier =
            Modifier
                .semantics { contentDescription = buttonEnableStatusText }
                .padding(16.dp),
        enabled = isButtonEnabled,
    ) {
        OAButtonTextView(
            text = stringResource(R.string.submit),
            color = if (isButtonEnabled) AppTheme.colors.primaryButton01 else AppTheme.colors.button05a,
        )
    }
}

@Composable
fun DisclaimerComponent() {
    Box {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
        ) {
            OACaption2TextView(
                modifier =
                    Modifier
                        .padding(start = 16.dp, bottom = 8.dp),
                text = stringResource(R.string.disclaimer),
                color = AppTheme.colors.tertiary03,
            )
            OACaption1TextView(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .height(140.dp),
                text = stringResource(R.string.enroll_utility_disclaimer),
                color = AppTheme.colors.tertiary05,
            )
        }
    }
}

@Composable
fun LegalConsentCheckBox(
    legalConsent: MutableState<Boolean>,
    collectData: Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?,
    chargeAssistViewModel: ChargeAssistViewModel,
) {
    Row {
        Box(
            modifier =
                Modifier
                    .clip(RoundedCornerShape(4.dp)),
        ) {
            Checkbox(
                colors =
                    CheckboxDefaults.colors(
                        checkedColor = Color.Black,
                        uncheckedColor = Color.Gray,
                        checkmarkColor = Color.White,
                    ),
                modifier = Modifier.padding(start = 19.dp),
                checked = legalConsent.value,
                onCheckedChange = {
                    legalConsent.value = it
                },
            )
        }

        // Legal Consent Paragraphs and Links

        // ex BGE (Baltimore Gas and Electric)
        val utilityProviderName = collectData?.data?.payload?.firstOrNull()?.utilityProviderName

        // Link to Utility T & C
        val utilityProviderTCWebsite: String? = collectData?.data?.payload?.firstOrNull()?.utilityProviderTAndC
        val weaveGridTermsAndConditions = collectData?.data?.payload?.firstOrNull()?.aggregatorTAndC
        val privacyPolicyWebsite = collectData?.data?.payload?.firstOrNull()?.aggregatorPrivacyPortal

        OAHtmlTextView(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(top = 9.dp, start = 2.dp, bottom = 80.dp, end = 16.dp)
                    .padding(end = 16.dp),
            text =
                chargeAssistViewModel.chargeAssistVMHelper.fetchLegalConsentParagraph(
                    utilityProviderTCWebsite,
                    utilityProviderName,
                    weaveGridTermsAndConditions,
                    privacyPolicyWebsite,
                ),
            textStyle = AppTheme.fontStyles.callout1,
            color = AppTheme.colors.tertiary05,
            linkColor = AppTheme.colors.tertiary03,
        )
    }
}

@Composable
fun ProgramSmartChargeLogo(collectData: Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?) {
    val imageURL =
        collectData
            ?.data?.payload
            ?.firstOrNull()
            ?.imageLink
    Image(
        modifier =
            Modifier
                .semantics {
                    contentDescription = "Program Logo"
                }
                .padding(start = 16.dp, bottom = 16.dp, top = 16.dp)
                .size(60.dp),
        // pull image url from program availability api call
        painter = rememberAsyncImagePainter(model = imageURL),
        contentDescription = "Program Logo",
        contentScale = ContentScale.Fit,
    )
}

// Enroll Utility Screen user entry TextBoxes
// Majority of textboxes on this screen are handled here:
@Composable
fun UserTextBoxForEnrollment(
    textState: MutableState<String>,
    enableEnrollmentFields: Boolean,
    label: String,
    contentDescription: String,
) {
    OutlinedTextField(
        enabled = enableEnrollmentFields,
        value = textState.value,
        textStyle = AppTheme.fontStyles.body3,
        onValueChange = { textState.value = it },
        placeholder = {
            OABody3TextView(
                text = label,
                color = AppTheme.colors.tertiary07,
            )
        },
        modifier =
            Modifier
                .semantics { this.contentDescription = contentDescription }
                .padding(16.dp)
                .fillMaxWidth()
                .height(56.dp),
        colors =
            TextFieldDefaults.outlinedTextFieldColors(
                focusedBorderColor = AppTheme.colors.outline01,
                unfocusedBorderColor = AppTheme.colors.outline01,
                textColor = AppTheme.colors.tertiary03,
                placeholderColor = AppTheme.colors.tertiary07,
            ),
    )
}

@Composable
fun UserUtilityAccountNumberTextBox(
    userEnrollmentState: UserEnrollmentState,
    enableEnrollmentFields: Boolean,
    collectData: Resource<ApiResponse<List<ProgramEligibilityModelPayload?>?>?>?,
) {
    val numberLimit =
        collectData?.data?.payload?.firstOrNull()?.utilityProviderAccountNumberLength?.toInt()

    var showInvalidAccountNumError by remember { mutableStateOf(false) }

    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        val context = LocalContext.current
        OutlinedTextField(
            enabled = enableEnrollmentFields,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            value = userEnrollmentState.userUtilityAccountNumber.value,
            textStyle = AppTheme.fontStyles.body3,
            onValueChange = {
                if (it.length <= MAX_ACCOUNT_NUMBER_LENGTH && it.all { char -> char.isDigit() }) {
                    userEnrollmentState.userUtilityAccountNumber.value = it
                    userEnrollmentState.isValidAccountLengthEntered.value = it.length == numberLimit
                    showInvalidAccountNumError = !userEnrollmentState.isValidAccountLengthEntered.value
                }
            },
            placeholder = {
                OABody3TextView(
                    text = stringResource(R.string.add_utility_account_number),
                    color = AppTheme.colors.tertiary07,
                )
            },
            modifier =
                Modifier
                    .padding(bottom = 4.dp)
                    .semantics { contentDescription = context.getString(R.string.account_number_field) }
                    .fillMaxWidth()
                    .padding(start = 16.dp, end = 16.dp, bottom = 1.dp)
                    .height(56.dp),
            colors =
                TextFieldDefaults.outlinedTextFieldColors(
                    focusedBorderColor = AppTheme.colors.outline01,
                    unfocusedBorderColor = AppTheme.colors.outline01,
                    textColor = AppTheme.colors.tertiary03,
                    placeholderColor = AppTheme.colors.tertiary07,
                ),
        )

        if (showInvalidAccountNumError) {
            OACallOut1TextView(
                modifier =
                    Modifier
                        .padding(top = 1.dp).padding(start = 16.dp, end = 16.dp),
                // Ensure same horizontal padding
                text = stringResource(R.string.account_number_must_have_10_digits),
                color = AppTheme.colors.error01,
            )
        }
    }
}
