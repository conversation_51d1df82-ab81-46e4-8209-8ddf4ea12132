package com.toyota.oneapp.features.entrollment.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class PartnerTermsAndConditionResponse(
    @SerializedName("payload") var payload: Payload?,
)

data class Payload(
    @SerializedName("legal_details") var legalDetails: LegalDetails?,
)

data class LegalDetails(
    @SerializedName("partner_legal_id") var partnerLegalId: String?,
    @SerializedName("partner") var partner: String?,
    @SerializedName("content") var content: String?,
)
