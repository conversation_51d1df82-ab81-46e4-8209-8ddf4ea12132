/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.entrollment.application

import com.toyota.oneapp.features.entrollment.domain.model.EnrollmentData
import com.toyota.oneapp.features.entrollment.domain.model.EnrollmentRegisterData
import com.toyota.oneapp.features.entrollment.domain.model.TermsAndConditionData
import com.toyota.oneapp.features.entrollment.domain.model.WalletData
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface EnrollmentUseCase {
    fun fetchEnrollmentCheck(
        vin: String?,
        eMail: String?,
    ): Flow<EnrollmentData?>

    fun fetchWallet(): Flow<WalletData?>

    fun fetchEvPartnerTermsAndCondition(partnerName: String): Flow<TermsAndConditionData?>

    fun registerEnrollment(
        vehicleInfo: VehicleInfo,
        password: String?,
        type: String,
    ): Flow<EnrollmentRegisterData?>
}
