/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.entrollment.application

import com.toyota.oneapp.features.entrollment.domain.model.EnrollmentData
import com.toyota.oneapp.features.entrollment.domain.model.EnrollmentRegisterData
import com.toyota.oneapp.features.entrollment.domain.model.TermsAndConditionData

sealed class EnrollmentState {
    data object Initialize : EnrollmentState()

    data object Loading : EnrollmentState()

    class Success(
        val data: EnrollmentData,
    ) : EnrollmentState()

    data object Error : EnrollmentState()

    class TermsAndConditionSuccess(
        val data: TermsAndConditionData,
        val emailId: String?,
    ) : EnrollmentState()

    class EnrollmentRegisterSuccess(
        val data: EnrollmentRegisterData,
    ) : EnrollmentState()

    class UpdateProfile(
        val data: EnrollmentRegisterData,
    ) : EnrollmentState()
}
