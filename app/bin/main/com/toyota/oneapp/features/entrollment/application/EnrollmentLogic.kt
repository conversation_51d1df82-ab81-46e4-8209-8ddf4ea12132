/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */
package com.toyota.oneapp.features.entrollment.application

import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.repository.CommonApiDefaultRepository
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.Email
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.PhoneNumber
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoAddress
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoCustomer
import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.EnrollmentCheckPayload
import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.EnrollmentRegisterResponse
import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.EvGoSignInRequest
import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.LegalDetails
import com.toyota.oneapp.features.entrollment.domain.model.ChargingAlertData
import com.toyota.oneapp.features.entrollment.domain.model.EnrollmentData
import com.toyota.oneapp.features.entrollment.domain.model.EnrollmentRegisterData
import com.toyota.oneapp.features.entrollment.domain.model.TermsAndConditionData
import com.toyota.oneapp.features.entrollment.domain.model.WalletData
import com.toyota.oneapp.features.entrollment.domain.repository.EnrollmentRepository
import com.toyota.oneapp.features.entrollment.util.Constance
import com.toyota.oneapp.features.entrollment.util.Constance.FOUND
import com.toyota.oneapp.features.entrollment.util.Constance.SUCCESS_CODE
import com.toyota.oneapp.features.entrollment.util.Constance.UPDATE_PROFILE_CODE
import com.toyota.oneapp.features.pay.wallet.domain.repository.WalletRepository
import com.toyota.oneapp.model.pref.LanguagePreferenceModel
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.NetworkStatus
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.DateUtil
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.time.temporal.ChronoUnit
import java.util.TimeZone
import javax.inject.Inject

class EnrollmentLogic
    @Inject
    constructor(
        private val repository: EnrollmentRepository,
        private val walletRepository: WalletRepository,
        private val dateUtil: DateUtil,
        private val commonApiRepository: CommonApiDefaultRepository,
        private val preferenceModel: OneAppPreferenceModel,
    ) : EnrollmentUseCase {
        override fun fetchEnrollmentCheck(
            vin: String?,
            eMail: String?,
        ): Flow<EnrollmentData?> =
            flow {
                val enrollmentResponse = repository.fetchEnrollmentCheck(eMail)
                val walletResponse = walletRepository.fetchWalletDetails()

                if (enrollmentResponse is Resource.Success && enrollmentResponse.data != null) {
                    val data =
                        enrollmentResponse.data?.payload?.toUiModel()?.apply {
                            this.evGoExpiryDate = dateUtil.formatDate(this.evGoExpiryDate)
                            this.isWalletSetupDone = FOUND.equals(
                                enrollmentResponse.data
                                    ?.payload
                                    ?.partnerEnrollment
                                    ?.wallet,
                                ignoreCase = true,
                            ) ||
                                (
                                    walletResponse is Resource.Success &&
                                        walletResponse.data
                                            ?.payload
                                            ?.paymentMethods
                                            ?.isNotEmpty() == true
                                )
                        }
                    data?.let {
                        data.chargingAlertData = checkIfEVgoComplimentaryExpired(vin, data)
                        emit(data)
                    } ?: run {
                        emit(null)
                    }
                } else {
                    emit(null)
                }
            }

        override fun fetchWallet(): Flow<WalletData?> =
            flow {
                val response = walletRepository.fetchWalletDetails()
                if (response.status == NetworkStatus.SUCCESS) {
                    val paymentMethods = response.data?.payload?.paymentMethods
                    emit(WalletData(paymentMethod = paymentMethods?.firstOrNull()))
                } else {
                    emit(null)
                }
            }

        override fun fetchEvPartnerTermsAndCondition(partnerName: String): Flow<TermsAndConditionData?> =
            flow {
                val accountInfo = preferenceModel.getAccountInfoSubscriber()
                val response =
                    repository.fetchEvPartnerTermsAndCondition(
                        partnerName,
                        accountInfo?.uiLanguage ?: LanguagePreferenceModel.US_ENGLISH.language,
                    )
                if (response is Resource.Success && response.data != null) {
                    emit(
                        response.data
                            ?.payload
                            ?.legalDetails
                            ?.toUiData(),
                    )
                } else {
                    emit(null)
                }
            }

        override fun registerEnrollment(
            vehicleInfo: VehicleInfo,
            password: String?,
            type: String,
        ): Flow<EnrollmentRegisterData?> =
            flow {
                val response =
                    commonApiRepository.fetchProfileDetails(
                        brand = vehicleInfo.brand,
                        guid = preferenceModel.getGuid(),
                    )
                when {
                    response.status == NetworkStatus.SUCCESS && response.data != null -> {
                        response.data?.payload?.customer?.let { customer ->
                            val evGoRequest = getSignInRequest(customer, password, type)
                            val registerEnrollmentResponse =
                                repository.registerEnrollment(
                                    vehicleInfo.vin,
                                    evGoRequest,
                                )
                            emit(getEnrolmentState(registerEnrollmentResponse, type))
                        } ?: run {
                            emit(
                                EnrollmentRegisterData().apply {
                                    isError = true
                                },
                            )
                        }
                    }

                    else -> {
                        emit(
                            EnrollmentRegisterData().apply {
                                isError = true
                            },
                        )
                    }
                }
            }

        private fun getCustomerHomeAddress(customer: ProfileInfoCustomer): ProfileInfoAddress? {
            if (customer.addresses?.isNotEmpty() == true) {
                val address =
                    customer.addresses.firstOrNull { address ->
                        address.addressType.uppercase() == Constance.HOME
                    }
                return address
            }
            return null
        }

        private fun getCustomerHomeEmail(customer: ProfileInfoCustomer): Email? {
            if (customer.emails.isNotEmpty()) {
                val email =
                    customer.emails.firstOrNull { address ->
                        address.emailType.uppercase() == Constance.FORGEROCK
                    }
                return email
            }
            return null
        }

        private fun getCustomerHomePhone(customer: ProfileInfoCustomer): PhoneNumber? {
            if (!customer.phoneNumbers.isNullOrEmpty()) {
                val phoneNumber =
                    customer.phoneNumbers.firstOrNull { address ->
                        address.phoneType.uppercase() == Constance.MOBILE
                    }
                return phoneNumber
            }
            return null
        }

        private fun getSignInRequest(
            customer: ProfileInfoCustomer,
            password: String?,
            type: String,
        ): EvGoSignInRequest {
            val address = getCustomerHomeAddress(customer)
            val email = getCustomerHomeEmail(customer)
            val phone = getCustomerHomePhone(customer)

            return EvGoSignInRequest(
                address?.address,
                address?.address,
                address?.city,
                address?.country,
                address?.state,
                address?.zipCode,
                TimeZone.getDefault().getDisplayName(false, TimeZone.SHORT),
                email?.emailAddress,
                type,
                password,
                phone?.phoneNumber?.toString(),
                customer.firstName,
                customer.lastName,
                customer.guid,
            )
        }

        private fun getEnrolmentState(
            enrollmentResponse: Resource<EnrollmentRegisterResponse?>,
            type: String,
        ): EnrollmentRegisterData {
            val response = enrollmentResponse.data
            val messages = response?.message

            val responseCode = messages?.responseCode
            val description = messages?.description?.uppercase()
            val detailedDescription = messages?.detailedDescription
            val enrollmentRegisterData = EnrollmentRegisterData()
            enrollmentRegisterData.type = type
            when {
                responseCode == SUCCESS_CODE &&
                    description == Constance.SUCCESS &&
                    detailedDescription == Constance.DRIVER_FOUND.uppercase() -> {
                    enrollmentRegisterData.isChargePointAccountFound = true
                    enrollmentRegisterData.driverId = response.payload?.partnerDriverId
                }

                responseCode == SUCCESS_CODE &&
                    description == Constance.SUCCESS &&
                    detailedDescription != Constance.DRIVER_FOUND.uppercase() ->
                    enrollmentRegisterData.isAccountNotFound = true

                responseCode == UPDATE_PROFILE_CODE && description == Constance.FAILED -> {
                    enrollmentRegisterData.isUpdateProfile = true
                    enrollmentRegisterData.updateProfileDescription = detailedDescription
                }

                else -> enrollmentRegisterData.isError = true
            }
            return enrollmentRegisterData
        }

        private fun checkIfEVgoComplimentaryExpired(
            vin: String?,
            data: EnrollmentData,
        ): ChargingAlertData? {
            val isWalletSetupDone = data.isWalletSetupDone
            val isChargePointRegistered = data.chargePointStatus
            val evgoExpired = data.evGoExpired
            val evgoExpiringSoon = data.evGoExpiredSoon

            return vin?.let {
                if (evgoExpiringSoon || evgoExpired) {
                    when {
                        // Scenario 1.1: Wallet Setup & Not Registered with ChargePoint
                        isWalletSetupDone &&
                            !isChargePointRegistered -> {
                            evgoWalletNoCharge(vin = vin, evgoExpiringSoon = evgoExpiringSoon)
                        }

                        // Scenario 1.2: Wallet Setup & Registered with ChargePoint
                        isWalletSetupDone &&
                            isChargePointRegistered -> {
                            evgoWalletCharge(vin = vin, evgoExpiringSoon = evgoExpiringSoon)
                        }

                        // Scenario 1.3: No Wallet Setup & Registered with ChargePoint
                        !isWalletSetupDone &&
                            isChargePointRegistered -> {
                            evgoNoWalletCharge(vin = vin, evgoExpiringSoon = evgoExpiringSoon)
                        }

                        // Scenario 1.4: No Wallet Setup & Not Registered with ChargePoint
                        else -> evgoNoWalletNoCharge(vin = vin, evgoExpiringSoon = evgoExpiringSoon)
                    }
                } else {
                    null
                }
            }
        }

        private fun evgoNoWalletNoCharge(
            vin: String,
            evgoExpiringSoon: Boolean,
        ): ChargingAlertData? =
            if (!preferenceModel.getShowEVGoComplementaryPopUp1Point4(vin)) {
                preferenceModel.showEVGoComplementaryPopUp1Point4(vin)
                ChargingAlertData(
                    icon = R.drawable.ic_small_alert,
                    success = false,
                    title =
                        if (evgoExpiringSoon) {
                            R.string.evgo_nowallet_nocharge_endingsoon_title
                        } else {
                            R.string.evgo_nowallet_nocharge_ended_title
                        },
                    description =
                        if (evgoExpiringSoon) {
                            R.string.evgo_nowallet_nocharge_endingsoon_content
                        } else {
                            R.string.evgo_nowallet_nocharge_ended_content
                        },
                    primaryButtonText = R.string.evgo_setup_wallet_cta,
                    additionalButtonText = if (evgoExpiringSoon) R.string.evgo_skip_cta else null,
                    onPrimaryButtonPressed = {
                    },
                    onAdditionalButtonPressed = {
                    },
                )
            } else {
                null
            }

        private fun evgoWalletNoCharge(
            vin: String,
            evgoExpiringSoon: Boolean,
        ): ChargingAlertData? =
            if (!preferenceModel.getShowEVGoComplementaryPopUp1Point1(vin)) {
                preferenceModel.showEVGoComplementaryPopUp1Point1(vin)
                ChargingAlertData(
                    icon = R.drawable.ic_small_alert,
                    success = false,
                    title =
                        if (evgoExpiringSoon) {
                            R.string.evgo_wallet_nocharge_endingsoon_title
                        } else {
                            R.string.evgo_wallet_nocharge_ended_title
                        },
                    description =
                        if (evgoExpiringSoon) {
                            R.string.evgo_wallet_nocharge_endingsoon_content
                        } else {
                            R.string.evgo_wallet_nocharge_ended_content
                        },
                    primaryButtonText = R.string.evgo_register_chargepoint_cta,
                    additionalButtonText =
                        if (evgoExpiringSoon) {
                            R.string.evgo_skip_cta
                        } else {
                            R.string.evgo_goback_cta
                        },
                    onPrimaryButtonPressed = {
                    },
                    onAdditionalButtonPressed = {
                    },
                )
            } else {
                null
            }

        private fun evgoWalletCharge(
            vin: String,
            evgoExpiringSoon: Boolean,
        ): ChargingAlertData? =
            if (!preferenceModel.getShowEVGoComplementaryPopUp1Point2(vin)) {
                preferenceModel.showEVGoComplementaryPopUp1Point2(vin)
                ChargingAlertData(
                    icon = R.drawable.ic_history_info,
                    success = true,
                    title =
                        if (evgoExpiringSoon) {
                            R.string.evgo_wallet_charge_endingsoon_title
                        } else {
                            R.string.evgo_wallet_charge_ended_title
                        },
                    description =
                        if (evgoExpiringSoon) {
                            R.string.evgo_wallet_charge_endingsoon_content
                        } else {
                            R.string.evgo_wallet_charge_ended_content
                        },
                    primaryButtonText = R.string.evgo_okay_cta,
                    onPrimaryButtonPressed = {
                    },
                )
            } else {
                null
            }

        private fun evgoNoWalletCharge(
            vin: String,
            evgoExpiringSoon: Boolean,
        ): ChargingAlertData? =
            if (!preferenceModel.getShowEVGoComplementaryPopUp1Point3(vin)) {
                preferenceModel.showEVGoComplementaryPopUp1Point3(vin)
                ChargingAlertData(
                    icon = R.drawable.ic_small_alert,
                    success = false,
                    title =
                        if (evgoExpiringSoon) {
                            R.string.evgo_nowallet_charge_endingsoon_title
                        } else {
                            R.string.evgo_nowallet_charge_ended_title
                        },
                    description =
                        if (evgoExpiringSoon) {
                            R.string.evgo_nowallet_charge_endingsoon_content
                        } else {
                            R.string.evgo_nowallet_charge_ended_content
                        },
                    primaryButtonText = R.string.evgo_setup_wallet_cta,
                    additionalButtonText =
                        if (evgoExpiringSoon) {
                            R.string.evgo_skip_cta
                        } else {
                            R.string.evgo_goback_cta
                        },
                    onPrimaryButtonPressed = {
                    },
                    onAdditionalButtonPressed = {
                    },
                )
            } else {
                null
            }
    }

fun EnrollmentCheckPayload.toUiModel(): EnrollmentData {
    val enrollmentData = EnrollmentData()
    enrollmentData.isWalletSetupDone =
        FOUND.equals(
            this.partnerEnrollment?.wallet,
            ignoreCase = true,
        )
    this.partnerEnrollment?.partnerStatus?.forEach { status ->
        when (status.partnerName) {
            Constance.CHARGE_POINT -> {
                if (status.status?.uppercase() == FOUND.uppercase()) {
                    enrollmentData.chargePointStatus = true
                    enrollmentData.chargePointEnrollmentDate = status.enrollmentDate
                    enrollmentData.chargePointExpiryDate = status.expiryDate
                }
            }

            Constance.EV_GO -> {
                if (status.status?.uppercase() == FOUND.uppercase()) {
                    enrollmentData.evGoPointStatus = true
                    enrollmentData.evGoEnrollmentDate = status.enrollmentDate
                    enrollmentData.evGoExpiryDate = status.expiryDate
                    enrollmentData.evGoExpired = isEVGoExpired(status.expiryDate)
                    enrollmentData.evGoExpiredSoon = isEVGoExpiringSoon(status.expiryDate)
                }
            }
        }
    }
    return enrollmentData
}

fun LegalDetails.toUiData(): TermsAndConditionData = TermsAndConditionData(content.orEmpty())

fun isEVGoExpired(expiryDate: String?): Boolean =
    parseDate(expiryDate)?.let {
        ChronoUnit.DAYS.between(LocalDate.now(), it) < 0
    } ?: false

fun isEVGoExpiringSoon(expiryDate: String?): Boolean =
    parseDate(expiryDate)?.let {
        ChronoUnit.DAYS.between(LocalDate.now(), it) in 0..30
    } ?: false

internal fun parseDate(expiryDate: String?): LocalDate? =
    try {
        expiryDate?.let { LocalDate.parse(it) }
    } catch (e: DateTimeParseException) {
        try {
            expiryDate?.let { LocalDate.parse(it, DateTimeFormatter.ofPattern("yyyyMMdd")) }
        } catch (e: DateTimeParseException) {
            null
        }
    }
