package com.toyota.oneapp.features.entrollment.domain.model

import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletPaymentMethod

class EnrollmentData {
    var walletPaymentMethod: WalletPaymentMethod? = null
    var chargePointStatus: Boolean = false
    var chargePointEnrollmentDate: String? = null
    var chargePointExpiryDate: String? = null
    var evGoPointStatus: Boolean = false
    var evGoEnrollmentDate: String? = null
    var evGoExpiryDate: String? = null
    var evGoExpiredSoon: Boolean = false
    var evGoExpired: Boolean = false
    var isWalletSetupDone: Boolean = false
    var isEnrollmentSetupDone: Boolean = true
    var chargingAlertData: ChargingAlertData? = null
    var evPublicChargingControl: Boolean = true
}

data class ChargingAlertData(
    val icon: Int,
    val success: <PERSON>olean,
    val title: Int,
    val description: Int,
    val primaryButtonText: Int,
    val additionalButtonText: Int? = null,
    val onPrimaryButtonPressed: () -> Unit,
    val onAdditionalButtonPressed: () -> Unit = {},
)
