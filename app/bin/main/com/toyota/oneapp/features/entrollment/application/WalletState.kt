/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.entrollment.application

import com.toyota.oneapp.features.entrollment.domain.model.WalletData

sealed class WalletState {
    data object NotAvailable : WalletState()

    class Success(
        val data: WalletData?,
    ) : WalletState()

    data object Loading : WalletState()

    data object Error : WalletState()
}
