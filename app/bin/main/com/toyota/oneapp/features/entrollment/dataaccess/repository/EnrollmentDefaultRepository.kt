/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.entrollment.dataaccess.repository

import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.EnrollmentCheckResponse
import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.EnrollmentRegisterResponse
import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.EvGoSignInRequest
import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.PartnerTermsAndConditionResponse
import com.toyota.oneapp.features.entrollment.dataaccess.service.EnrollmentApi
import com.toyota.oneapp.features.entrollment.domain.repository.EnrollmentRepository
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class EnrollmentDefaultRepository
    @Inject
    constructor(
        val service: EnrollmentApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), EnrollmentRepository {
        override suspend fun fetchEnrollmentCheck(eMail: String?): Resource<EnrollmentCheckResponse?> {
            return makeApiCall {
                service.fetchEnrollmentCheck(
                    email = eMail,
                )
            }
        }

        override suspend fun fetchEvPartnerTermsAndCondition(
            partnerName: String,
            language: String,
        ): Resource<PartnerTermsAndConditionResponse?> {
            return makeApiCall {
                service.fetchEvPartnerTermsAndCondition(
                    partnerName = partnerName,
                    language = language,
                )
            }
        }

        override suspend fun registerEnrollment(
            vin: String,
            evGoSignInRequest: EvGoSignInRequest,
        ): Resource<EnrollmentRegisterResponse?> {
            return makeApiCall {
                service.registerEnrollment(
                    vin,
                    evGoSignInRequest,
                )
            }
        }
    }
