/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.entrollment.dataaccess.service

import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.EnrollmentCheckResponse
import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.EnrollmentRegisterResponse
import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.EvGoSignInRequest
import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.PartnerTermsAndConditionResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Query

interface EnrollmentApi {
    @GET("/charging/driver/enrollment-check")
    suspend fun fetchEnrollmentCheck(
        @Header("email") email: String?,
    ): Response<EnrollmentCheckResponse?>

    @GET("charging/driver/legal-content")
    suspend fun fetchEvPartnerTermsAndCondition(
        @Query("partner") partnerName: String,
        @Query("language") language: String,
    ): Response<PartnerTermsAndConditionResponse?>

    @POST("charging/driver/enrollment")
    suspend fun registerEnrollment(
        @Header("vin") vin: String?,
        @Body evGoSignInRequest: EvGoSignInRequest,
    ): Response<EnrollmentRegisterResponse?>
}
