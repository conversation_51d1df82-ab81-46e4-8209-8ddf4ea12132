package com.toyota.oneapp.features.entrollment.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class EnrollmentCheckResponse(
    @SerializedName("payload") var payload: EnrollmentCheckPayload?,
)

data class EnrollmentCheckPayload(
    @SerializedName("show_associated_banners") var showAssociatedBanners: String?,
    @SerializedName("partner_enrollment") var partnerEnrollment: PartnerEnrollment?,
)

data class PartnerEnrollment(
    @SerializedName("guid") var guid: String?,
    @SerializedName("wallet") var wallet: String?,
    @SerializedName("partner_status") var partnerStatus: List<PartnerStatus>?,
)

data class PartnerStatus(
    @SerializedName("partner_name") var partnerName: String?,
    @SerializedName("enrollment_date") var enrollmentDate: String?,
    @SerializedName("expiry_date") var expiryDate: String?,
    @SerializedName("status") var status: String?,
)
