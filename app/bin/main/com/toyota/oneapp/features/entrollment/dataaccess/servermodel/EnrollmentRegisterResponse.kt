package com.toyota.oneapp.features.entrollment.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class EnrollmentRegisterResponse(
    @SerializedName("payload") var payload: RegisterPayload?,
    @SerializedName("messages") var message: Message?,
)

data class RegisterPayload(
    @SerializedName("guid") var guid: String?,
    @SerializedName("partner_driver_id") var partnerDriverId: String?,
    @SerializedName("created_at") var createdAt: String?,
    @SerializedName("enrollment_date") var enrollmentDate: String?,
)
