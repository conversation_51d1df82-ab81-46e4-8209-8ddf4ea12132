/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.entrollment.di

import com.toyota.oneapp.features.entrollment.application.EnrollmentLogic
import com.toyota.oneapp.features.entrollment.application.EnrollmentUseCase
import com.toyota.oneapp.features.entrollment.dataaccess.repository.EnrollmentDefaultRepository
import com.toyota.oneapp.features.entrollment.domain.repository.EnrollmentRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class EnrollmentModule {
    @Binds
    abstract fun provideEnrollmentRepository(enrollmentDefaultRepository: EnrollmentDefaultRepository): EnrollmentRepository

    @Binds
    abstract fun provideEnrollmentLogic(enrollmentLogic: EnrollmentLogic): EnrollmentUseCase
}
