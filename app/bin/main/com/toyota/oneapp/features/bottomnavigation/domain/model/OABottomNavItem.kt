package com.toyota.oneapp.features.bottomnavigation.domain.model

import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.navigation.OAScreen

sealed class OABottomNavItem(
    var title: Int?,
    var defaultIcon: Int,
    var selectedIcon: Int,
    var route: String,
) {
    object Service : OABottomNavItem(
        title = R.string.tab_service,
        defaultIcon = R.drawable.ic_service,
        selectedIcon = R.drawable.ic_service_selected,
        route = OAScreen.Service.route,
    )

    object Pay : OABottomNavItem(
        title = R.string.tab_pay,
        defaultIcon = R.drawable.ic_pay,
        selectedIcon = R.drawable.ic_pay_selected,
        route = OAScreen.Pay.route,
    )

    object Home : OABottomNavItem(
        title = null,
        defaultIcon = R.drawable.ic_tab_home_default,
        selectedIcon = R.drawable.ic_tab_home,
        route = OAScreen.Home.route,
    )

    object Shop : OABottomNavItem(
        title = R.string.tab_shop,
        defaultIcon = R.drawable.ic_shop,
        selectedIcon = R.drawable.ic_shop_selected,
        route = OAScreen.Shop.route,
    )

    object Find : OABottomNavItem(
        title = R.string.tab_find,
        defaultIcon = R.drawable.ic_find,
        selectedIcon = R.drawable.ic_find_selected,
        route = OAScreen.Find.route,
    )
}
