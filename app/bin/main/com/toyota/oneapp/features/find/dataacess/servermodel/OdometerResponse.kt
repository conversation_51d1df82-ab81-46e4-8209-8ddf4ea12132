package com.toyota.oneapp.features.find.dataacess.servermodel

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.dashboard.dashboard.dataaccess.servermodel.TelemetryItem
import com.toyota.oneapp.model.vehicle.VehicleLocation
import java.util.ArrayList

data class OdometerResponse(
    val payload: PayLoad?,
    var status: String?,
    var code: Int? = null,
    var message: String? = null,
    var errors: ArrayList<String>? = null,
)

data class PayLoad(
    @SerializedName("vin") var vin: String? = null,
    @SerializedName("lastTimestamp") var lastTimestamp: String? = null,
    @SerializedName("tirePressureTimestamp") var tirePressureTimestamp: String? = null,
    @SerializedName("displayNextService") var displayNextService: Boolean? = null,
    @SerializedName("vehicleName") var vehicleName: String? = null,
    @SerializedName("fuelLevel") var fuelLevel: Int? = null,
    @SerializedName("odometer") var odometer: TelemetryItem? = null,
    @SerializedName("sunRoof") var sunRoof: Int? = null,
    @SerializedName("speed") var speed: TelemetryItem? = null,
    @SerializedName("flTirePressure") var flTirePressure: TelemetryItem? = null,
    @SerializedName("frTirePressure") var frTirePressure: TelemetryItem? = null,
    @SerializedName("rlTirePressure") var rlTirePressure: TelemetryItem? = null,
    @SerializedName("rrTirePressure") var rrTirePressure: TelemetryItem? = null,
    @SerializedName("spareTirePressure") var spareTirePressure: String? = null,
    @SerializedName("driverWindow") var driverWindow: Int? = null,
    @SerializedName("passengerWindow") var passengerWindow: Int? = null,
    @SerializedName("rlWindow") var rlWindow: String? = null,
    @SerializedName("rrWindow") var rrWindow: String? = null,
    @SerializedName("nextService") var nextService: TelemetryItem? = null,
    @SerializedName("tripA") var tripA: TelemetryItem? = null,
    @SerializedName("tripB") var tripB: TelemetryItem? = null,
    @SerializedName("distanceToEmpty") var distanceToEmpty: TelemetryItem? = null,
    @SerializedName("displayDistanceToEmpty") var displayDistanceToEmpty: Boolean? = null,
    @SerializedName("parkingNotes") var parkingNotes: String? = null,
    @SerializedName("vehicleLocation") var vehicleLocation: VehicleLocation? = null,
    @SerializedName("avgFuelConsumption") var avgFuelConsumption: String? = null,
    @SerializedName("pvlEligible") var pvlEligible: Boolean? = null,
    @SerializedName("tclastTimestamp") var tclastTimestamp: String? = null,
)
