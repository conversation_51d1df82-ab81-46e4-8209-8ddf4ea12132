package com.toyota.oneapp.features.find.dataacess.service

import com.toyota.oneapp.features.find.dataacess.servermodel.PreferredDealerResponse
import com.toyota.oneapp.features.find.dataacess.servermodel.VehicleStatusResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header

interface FindApi {
    @GET("/oneapi/v2/legacy/remote/status")
    suspend fun fetchVehicleStatusCY17(
        @Header("X-BRAND") brand: String?,
        @Header("VIN") vin: String?,
    ): Response<VehicleStatusResponse?>

    @GET("/oneapi/v1/remote/ng86/status")
    suspend fun fetchVehicleStatusNG86(
        @Header("X-BRAND") brand: String?,
        @Header("VIN") vin: String?,
    ): Response<VehicleStatusResponse?>

    @GET("/oneapi/v1/global/remote/status")
    suspend fun fetchVehicleStatusCY17Plus(
        @Header("X-BRAND") brand: String?,
        @Header("VIN") vin: String?,
    ): Response<VehicleStatusResponse?>

    @GET("/oneapi/v1/preferred-dealer")
    suspend fun fetchPreferredDealer(
        @Header("VIN") vin: String,
        @Header("X-REGION") region: String,
    ): Response<PreferredDealerResponse>
}
