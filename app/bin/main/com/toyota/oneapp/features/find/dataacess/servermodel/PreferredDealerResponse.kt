package com.toyota.oneapp.features.find.dataacess.servermodel

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.find.domain.model.Dealer
import com.toyota.oneapp.features.find.domain.model.PreferredDealer
import kotlinx.parcelize.Parcelize

@Parcelize
data class PreferredDealerResponse(
    @SerializedName("payload") val payload: List<DealerResponse>? = null,
) : Parcelable

@Parcelize
data class DealerResponse(
    @SerializedName("dealerCode") val dealerCode: String? = null,
    @SerializedName("dealerName") val dealerName: String? = null,
    @SerializedName("regionCode") val regionCode: String? = null,
    @SerializedName("tdaCode") val tdaCode: String? = null,
    @SerializedName("addresses") val addresses: String? = null,
    @SerializedName("city") val city: String? = null,
    @SerializedName("state") val state: String? = null,
    @SerializedName("country") val country: String? = null,
    @SerializedName("zip") val zip: String? = null,
    @SerializedName("latitude") val latitude: Double? = null,
    @SerializedName("longitude") val longitude: Double? = null,
    @SerializedName("dealerType") val dealerType: String? = null,
    @SerializedName("phone") val phone: String? = null,
    @SerializedName("distance") val distance: Double? = null,
    @SerializedName("distanceUnit") val distanceUnit: String? = null,
    @SerializedName("webUrls") val webUrls: ArrayList<String>? = null,
) : Parcelable

fun PreferredDealerResponse.toUiModel(): PreferredDealer {
    val list = ArrayList<Dealer>()
    payload?.forEach {
        list.add(
            Dealer(
                dealerCode = it.dealerCode,
                dealerName = it.dealerName,
                zip = it.zip,
                state = it.state,
                country = it.country,
                city = it.city,
                addresses = it.addresses,
            ),
        )
    }
    return PreferredDealer(
        payload = list,
    )
}
