package com.toyota.oneapp.features.find.dataacess.repository

import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.features.find.dataacess.servermodel.PreferredDealerResponse
import com.toyota.oneapp.features.find.dataacess.servermodel.VehicleStatusResponse
import com.toyota.oneapp.features.find.dataacess.service.FindApi
import com.toyota.oneapp.features.find.domain.repo.FindRepository
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.interceptor.GrpcInterceptor
import com.toyota.oneapp.util.ToyotaConstants
import io.grpc.ManagedChannel
import io.grpc.okhttp.OkHttpChannelBuilder
import toyotaone.commonlib.log.LogTool
import java.io.ByteArrayInputStream
import java.security.KeyStore
import java.security.cert.CertificateFactory
import javax.inject.Inject
import javax.net.ssl.KeyManagerFactory
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManagerFactory
import kotlin.coroutines.CoroutineContext

class FindDefaultRepository
    @Inject
    constructor(
        val service: FindApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), FindRepository {
        override suspend fun generateRPCManagedChannel(
            url: String?,
            port: Int,
            certData: ByteArray,
            grpcInterceptor: GrpcInterceptor,
        ): ManagedChannel? {
            val channelBuilder: OkHttpChannelBuilder = OkHttpChannelBuilder.forAddress(url, port)
            channelBuilder.intercept(grpcInterceptor)
            if (!BuildConfig.IS_CERTIFICATION_REQUIRED.toBoolean()) {
                return channelBuilder.build()
            }
            var sslSocketFactory: SSLSocketFactory? = null
            try {
                val inputStream = ByteArrayInputStream(certData)
                val keyStore =
                    KeyStore.getInstance(KeyStore.getDefaultType())
                keyStore.load(null, null)
                val cf =
                    CertificateFactory.getInstance(ToyotaConstants.GRPC_CERT_TYPE)
                keyStore.setCertificateEntry(
                    ToyotaConstants.GRPC_CERT_ALIAS,
                    cf.generateCertificate(inputStream),
                )
                inputStream.close()
                val keyManagerFactory =
                    KeyManagerFactory.getInstance(ToyotaConstants.KEY_MANAGER_ALGO)
                keyManagerFactory.init(keyStore, ToyotaConstants.EMPTY_STRING.toCharArray())
                val trustManagerFactory =
                    TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
                trustManagerFactory.init(keyStore)
                val sslContext = SSLContext.getInstance(ToyotaConstants.SSL_PROTOCOL)
                sslContext.init(
                    keyManagerFactory.keyManagers,
                    trustManagerFactory.trustManagers,
                    null,
                )
                sslSocketFactory = sslContext.socketFactory
            } catch (e: Exception) {
                LogTool.d(FindDefaultRepository::class.java.simpleName, e.message)
            }

            return sslSocketFactory?.let {
                channelBuilder.sslSocketFactory(it).build()
            }
        }

        override suspend fun fetchVehicleStatusCY17(
            vin: String,
            brand: String,
        ): Resource<VehicleStatusResponse?> {
            return makeApiCall {
                service.fetchVehicleStatusCY17(vin = vin, brand = brand)
            }
        }

        override suspend fun fetchVehicleStatusNG86(
            vin: String,
            brand: String,
        ): Resource<VehicleStatusResponse?> {
            return makeApiCall {
                service.fetchVehicleStatusNG86(vin = vin, brand = brand)
            }
        }

        override suspend fun fetchVehicleStatusCY17Plus(
            vin: String,
            brand: String,
        ): Resource<VehicleStatusResponse?> {
            return makeApiCall {
                service.fetchVehicleStatusCY17Plus(vin = vin, brand = brand)
            }
        }

        override suspend fun fetchPreferredDealer(
            vin: String,
            region: String,
        ): Resource<PreferredDealerResponse?> {
            return makeApiCall {
                service.fetchPreferredDealer(vin = vin, region = region)
            }
        }
    }
