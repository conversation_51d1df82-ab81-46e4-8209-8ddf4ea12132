package com.toyota.oneapp.features.find.dataacess.servermodel

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.find.domain.model.VehicleTripStatus
import kotlinx.android.parcel.Parcelize
import java.util.Date

@Parcelize
data class VehicleStatusResponse(
    @SerializedName("payload") val payload: VehicleStatusResponsePayload,
) : Parcelable

@Parcelize
data class VehicleStatusResponsePayload(
    @SerializedName("vehicleStatus") val vehicleStatus: List<VehicleStatus>,
    @SerializedName("occurrenceDate") val occurrenceDate: Date,
    @SerializedName("cautionOverallCount") val cautionOverallCount: Int,
    @SerializedName("latitude") var latitude: String? = null,
    @SerializedName("longitude") var longitude: String? = null,
    var address: String?,
) : Parcelable

@Parcelize
data class VehicleStatus(
    @SerializedName("category") val category: String,
    @SerializedName("displayOrder") val displayOrder: Int,
    @SerializedName("sections") var sections: List<Sections>,
) : Parcelable

@Parcelize
data class Sections(
    @SerializedName("section") var section: String,
    @SerializedName("values") var values: List<Values>,
) : Parcelable

@Parcelize
data class Values(
    @SerializedName("value") val value: String,
    @SerializedName("status") val status: Int,
) : Parcelable

fun VehicleStatusResponsePayload.getVehicleTripStatus(): VehicleTripStatus? {
    try {
        return VehicleTripStatus(
            tripA = vehicleStatus.last().sections.first().values.first().value,
            tripB = vehicleStatus.last().sections.last().values.first().value,
        )
    } catch (_: Exception) {
    }
    return null
}
