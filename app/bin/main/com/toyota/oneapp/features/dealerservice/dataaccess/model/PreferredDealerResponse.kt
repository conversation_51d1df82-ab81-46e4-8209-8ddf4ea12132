/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model

import com.google.gson.annotations.SerializedName

data class PreferredDealerResponse(
    @SerializedName("payload") val payload: List<DealerResponse>? = null,
)

data class DealerResponse(
    @SerializedName("dealerCode") val dealerCode: String? = null,
    @SerializedName("dealerName") val dealerName: String? = null,
    @SerializedName("regionCode") val regionCode: String? = null,
    @SerializedName("tdaCode") val tdaCode: String? = null,
    @SerializedName("address") val address: String? = null,
    @SerializedName("city") val city: String? = null,
    @SerializedName("state") val state: String? = null,
    @SerializedName("country") val country: String? = null,
    @SerializedName("zip") val zip: String? = null,
    @SerializedName("latitude") val latitude: Double? = null,
    @SerializedName("longitude") val longitude: Double? = null,
    @SerializedName("dealerType") val dealerType: String? = null,
    @SerializedName("phone") val phone: String? = null,
    @SerializedName("distance") val distance: Double? = null,
    @SerializedName("distanceUnit") val distanceUnit: String? = null,
    @SerializedName("webUrls") val webUrls: ArrayList<String>? = null,
)
