/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.appointmentservicepage.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.ScreenDynamicHeightComposable
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dealerservice.domain.model.UIAppointmentServiceList
import com.toyota.oneapp.features.dealerservice.presentation.appointmentservicepage.AppointmentServicePageEvent
import com.toyota.oneapp.features.dealerservice.presentation.appointmentservicepage.AppointmentServicePageState

@Composable
fun ServiceItemComposable(
    item: UIAppointmentServiceList,
    isSelected: Boolean,
    onEvent: (AppointmentServicePageEvent) -> Unit,
    event: AppointmentServicePageEvent,
) {
    val interactionSource = remember { MutableInteractionSource() }

    Box(
        modifier =
            Modifier
                .shadow(elevation = 1.dp, shape = RoundedCornerShape(12.dp))
                .background(
                    color = if (isSelected) AppTheme.colors.tile01 else AppTheme.colors.tile05,
                    shape = RoundedCornerShape(12.dp),
                )
                .fillMaxWidth()
                .padding(16.dp)
                .defaultMinSize(minHeight = 72.dp)
                .clickable(
                    interactionSource = interactionSource,
                    indication = null,
                ) {
                    onEvent(event)
                },
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(16.dp),
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = item.serviceName,
                    style = AppTheme.fontStyles.body4,
                    color = if (isSelected) AppTheme.colors.secondary01 else AppTheme.colors.tertiary03,
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = item.displayPrice,
                    style = AppTheme.fontStyles.callout1,
                    color = AppTheme.colors.tertiary05,
                )
            }
            if (isSelected) {
                Spacer(modifier = Modifier.width(8.dp))
                Image(
                    painter = painterResource(id = R.drawable.service_checked),
                    contentDescription = "Selected",
                    modifier =
                        Modifier
                            .size(38.dp)
                            .align(Alignment.CenterVertically),
                )
            }
        }
    }
}

/**
 * This Composable will display only services which is
 * type of frs, drs and grs
 */
@Composable
fun NoPackageListItemComposable(
    isExpanded: Boolean = true,
    listDetails: List<UIAppointmentServiceList>,
    state: AppointmentServicePageState,
    onEvent: (AppointmentServicePageEvent) -> Unit,
) {
    val selectedItems = state.selectedServices

    val listHeight = ScreenDynamicHeightComposable(0.9f)

    AnimatedVisibility(visible = isExpanded) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
        ) {
            LazyColumn(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .heightIn(min = 0.dp, max = listHeight),
            ) {
                items(listDetails) { item ->
                    ServiceItemComposable(
                        item = item,
                        isSelected = selectedItems.contains(item),
                        onEvent = onEvent,
                        event =
                            AppointmentServicePageEvent.ToggleItemSelection(
                                item,
                                !selectedItems.contains(item),
                            ),
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }
            }
        }
    }
}

@Composable
fun FilterPackageListDetails(
    listDetails: List<UIAppointmentServiceList>,
    searchQuery: String,
): List<UIAppointmentServiceList> {
    return if (searchQuery.isNotEmpty()) {
        listDetails.filter {
            it.serviceName.contains(searchQuery, ignoreCase = true)
        }
    } else {
        listDetails
    }
}
