/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model

import com.google.gson.annotations.SerializedName

data class ServiceAppointmentInitializeResponse(
    @SerializedName("id")
    val id: String,
    @SerializedName("schedulingAvailable")
    val schedulingAvailable: Boolean?,
    @SerializedName("phoneRequired")
    val phoneRequired: Boolean?,
    @SerializedName("addressRequired")
    val addressRequired: Boolean?,
    @SerializedName("odometer")
    val odometer: String?,
    @SerializedName("status")
    val statusResponse: StatusResponse,
)
