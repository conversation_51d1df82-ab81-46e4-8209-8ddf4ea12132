/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.common.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dealerservice.domain.DealerServiceAppointmentConst.SPACE_CHARACTER
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.ActiveAppointmentState
import com.toyota.oneapp.features.dealerservice.presentation.appointments.components.AppointmentCalendarWidget

@Composable
fun AppointmentDetailItemComposable(
    modifier: Modifier = Modifier,
    activeAppointmentUiState: ActiveAppointmentState?,
    onClick: () -> Unit,
) {
    // This is not a proper implementation, should use a string resource that takes in 4 args
    // of dayName, month, dayOfMonth, appointmentTime - but matches current flutter implementation
    val nextAppointmentMsg =
        buildString {
            append("*")
                .append(activeAppointmentUiState?.dayName)
                .append(", ")
                .append(activeAppointmentUiState?.month)
                .append(SPACE_CHARACTER)
                .append(activeAppointmentUiState?.dayOfMonth)
                .append(SPACE_CHARACTER)
                .append(stringResource(id = R.string.dsa_at))
                .append(SPACE_CHARACTER)
                .append(activeAppointmentUiState?.appointmentTime).toString()
        }

    Row(
        modifier =
            modifier
                .fillMaxWidth()
                .padding(all = 12.dp)
                .clickable { onClick.invoke() },
    ) {
        AppointmentCalendarWidget(
            date = activeAppointmentUiState?.dayOfMonth.toString(),
            month = activeAppointmentUiState?.month.toString(),
            size = 48.dp,
        )
        Spacer(modifier = Modifier.width(15.dp))
        Column(modifier = modifier) {
            OABody4TextView(
                text = stringResource(id = R.string.dsa_your_appointment_is_upcoming),
                color = AppTheme.colors.tertiary03,
            )
            Spacer(modifier = Modifier.height(2.dp))
            OACallOut1TextView(text = nextAppointmentMsg, color = AppTheme.colors.tertiary05)
        }
    }
}

@Preview
@Composable
fun AppointmentItemPreview() {
    AppointmentDetailItemComposable(
        modifier = Modifier,
        activeAppointmentUiState =
            ActiveAppointmentState(
                dayName = "Thu",
                month = "Jul",
                dayOfMonth = 20,
                appointmentTime = "9:00 am",
                appointmentId = "123",
            ),
        onClick = {},
    )
}
