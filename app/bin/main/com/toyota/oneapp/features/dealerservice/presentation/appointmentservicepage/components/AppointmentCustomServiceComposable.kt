/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.appointmentservicepage.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dealerservice.presentation.appointmentservicepage.CustomServiceState

/**
 * This composable is a box with custom service name where customer can type the service.
 */
@Composable
fun CustomServiceBoxComposable(
    onCustomServiceTextChanged: (String) -> Unit,
    onExpandToggle: () -> Unit,
    customServiceState: CustomServiceState,
) {
    val customServiceText = rememberSaveable { mutableStateOf(customServiceState.customServiceText) }

    val interactionSource = remember { MutableInteractionSource() }

    Box(
        modifier =
            Modifier
                .shadow(
                    elevation = 2.dp,
                    shape = RoundedCornerShape(12.dp),
                )
                .background(
                    color = AppTheme.colors.tile05,
                    shape = RoundedCornerShape(12.dp),
                )
                .fillMaxWidth()
                .padding(4.dp)
                .defaultMinSize(minHeight = 60.dp)
                .clickable(
                    interactionSource = interactionSource,
                    indication = null,
                ) {
                    onExpandToggle()
                    if (!customServiceState.isExpanded) {
                        customServiceText.value = ""
                        onCustomServiceTextChanged("")
                    }
                },
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
        ) {
            CustomServiceHeader(customServiceState.isExpanded)
            if (customServiceState.isExpanded) {
                Spacer(modifier = Modifier.height(2.dp))
                CustomServiceTextField(
                    customServiceText = customServiceText.value,
                    onTextChanged = { serviceText ->
                        customServiceText.value = serviceText
                        onCustomServiceTextChanged(serviceText)
                    },
                )
            }
        }
    }
}

@Composable
fun CustomServiceHeader(isExpanded: Boolean) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth(),
    ) {
        Text(
            text = stringResource(id = R.string.enter_custom_service_lbl),
            style = AppTheme.fontStyles.body4,
            color = if (isExpanded) AppTheme.colors.secondary01 else AppTheme.colors.tertiary03,
            modifier = Modifier.weight(1f),
        )
        if (isExpanded) {
            Image(
                painter = painterResource(id = R.drawable.service_checked),
                contentDescription = "Selected",
                modifier = Modifier.size(38.dp),
            )
        }
    }
}

@Composable
fun CustomServiceTextField(
    customServiceText: String,
    onTextChanged: (String) -> Unit,
) {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .border(BorderStroke(1.dp, Color.Black), RoundedCornerShape(12.dp))
                .background(AppTheme.colors.tile05, RoundedCornerShape(12.dp)),
    ) {
        TextField(
            value = customServiceText,
            onValueChange = onTextChanged,
            placeholder = {
                Text(
                    stringResource(id = R.string.custom_service_hint_text),
                    style = AppTheme.fontStyles.body1,
                    color = AppTheme.colors.tertiary07,
                )
            },
            modifier =
                Modifier
                    .fillMaxWidth()
                    .background(AppTheme.colors.tile05, RoundedCornerShape(12.dp))
                    .padding(4.dp),
            colors =
                TextFieldDefaults.textFieldColors(
                    backgroundColor = AppTheme.colors.tile05,
                    focusedIndicatorColor = Color.Transparent,
                    unfocusedIndicatorColor = Color.Transparent,
                ),
            shape = RoundedCornerShape(12.dp),
        )
    }
}
