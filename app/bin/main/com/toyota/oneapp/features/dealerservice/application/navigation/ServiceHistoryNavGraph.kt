/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.dealerservice.application.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.toyota.oneapp.features.core.navigation.sharedViewModel
import com.toyota.oneapp.features.dealerservice.presentation.servicehistory.ServiceHistoryScreen
import com.toyota.oneapp.features.dealerservice.presentation.servicehistory.ServiceHistoryViewModel
import com.toyota.oneapp.features.dealerservice.presentation.servicehistory.addedit.ServiceHistoryAddEditScreen
import com.toyota.oneapp.features.dealerservice.presentation.servicehistory.addedit.widget.ServiceHistoryServicesScreen
import com.toyota.oneapp.features.dealerservice.presentation.servicehistory.detail.ServiceHistoryDetailScreen

fun NavGraphBuilder.serviceHistoryNestedGraph(navController: NavHostController) {
    navigation(
        route = DealerServiceAppointmentRoute.ServiceHistoryEntryPage.route,
        startDestination = DealerServiceAppointmentRoute.ServiceHistoryPage.route,
    ) {
        composable(
            route = DealerServiceAppointmentRoute.ServiceHistoryPage.route,
        ) { entry ->
            val viewModel =
                entry.sharedViewModel<ServiceHistoryViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            ServiceHistoryScreen(viewModel, navController = navController)
        }
        composable(
            route = DealerServiceAppointmentRoute.ServiceHistoryDetailPage.route,
        ) { entry ->
            val viewModel =
                entry.sharedViewModel<ServiceHistoryViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            ServiceHistoryDetailScreen(viewModel, navController)
        }
        composable(
            route = DealerServiceAppointmentRoute.ServiceHistoryAddEditPage.route,
        ) { entry ->
            val viewModel =
                entry.sharedViewModel<ServiceHistoryViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            ServiceHistoryAddEditScreen(viewModel, navController)
        }
        composable(
            route = DealerServiceAppointmentRoute.ServiceHistoryServicesPage.route,
        ) { entry ->
            val viewModel =
                entry.sharedViewModel<ServiceHistoryViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            ServiceHistoryServicesScreen(viewModel, navController)
        }
    }
}
