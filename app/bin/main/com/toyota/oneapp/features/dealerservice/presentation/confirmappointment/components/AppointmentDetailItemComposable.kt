/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.confirmappointment.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dealerservice.presentation.landingpage.components.getIconContentDescription

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun AppointmentDetailItemComposable(
    modifier: Modifier = Modifier,
    title: String,
    subtitle: String,
    @DrawableRes icon: Int,
    onClick: () -> Unit,
) {
    Card(
        backgroundColor = AppTheme.colors.tile01,
        elevation = 8.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(bottom = 8.dp),
        onClick = { onClick.invoke() },
    ) {
        Row(
            modifier =
                Modifier.padding(
                    all = 12.dp,
                ),
        ) {
            ConfirmAppointmentStartImage(
                icon = icon,
            )
            Spacer(
                modifier =
                    Modifier.width(
                        12.dp,
                    ),
            )
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight(),
            ) {
                OABody4TextView(
                    text = title,
                    color = AppTheme.colors.tertiary03,
                )

                Spacer(modifier = Modifier.height(2.dp))
                OACallOut1TextView(
                    text = subtitle,
                    color = AppTheme.colors.tertiary05,
                    maxLines = 1,
                )
            }
        }
    }
}

@Composable
fun ConfirmAppointmentStartImage(
    @DrawableRes icon: Int,
) {
    val iconBackgroundColor = AppTheme.colors.button02b
    val iconColor = ColorFilter.tint(AppTheme.colors.success01)
    Box(
        modifier =
            Modifier
                .width(48.dp),
    ) {
        Surface(
            shape = CircleShape,
            color = iconBackgroundColor,
            modifier =
                Modifier
                    .size(size = 48.dp)
                    .align(Alignment.CenterStart),
        ) {
            Image(
                modifier =
                    Modifier
                        .padding(
                            all = 12.dp,
                        ),
                painter = painterResource(id = icon),
                colorFilter = iconColor,
                contentDescription = getIconContentDescription(icon = icon),
            )
        }
    }
}
