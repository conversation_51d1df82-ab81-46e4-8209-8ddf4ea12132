package com.toyota.oneapp.features.dealerservice.presentation.confirmappointment

import androidx.compose.material.ModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.navigation.NavHostController
import com.toyota.oneapp.features.core.presentation.ProgressEvent
import com.toyota.oneapp.features.core.presentation.UiEvent
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch

@Composable
fun ConfirmAppointmentScreenUiEventHandler(
    coroutineScope: CoroutineScope,
    additionalCommentsFullScreen: MutableState<Boolean>,
    navController: NavHostController,
    sheetState: ModalBottomSheetState,
    uiEvent: Flow<UiEvent>,
) {
    LaunchedEffect(key1 = sheetState.isVisible) {
        uiEvent.collect { event ->
            when (event) {
                is ConfirmAppointmentUiEvent.AppointmentSchedulingInProgress -> {
                    launchShow(coroutineScope, sheetState)
                }

                is ConfirmAppointmentUiEvent.ErrorScheduling -> {
                    additionalCommentsFullScreen.value = false
                }

                is ConfirmAppointmentUiEvent.EditAppointment -> {
                    launchShow(coroutineScope, sheetState)
                }

                is ConfirmAppointmentUiEvent.NavigationError -> {
                    // Do nothing
                }

                is ConfirmAppointmentUiEvent.AppointmentCreated -> {
                    additionalCommentsFullScreen.value = false
                    if (sheetState.isVisible) {
                        coroutineScope.launch {
                            sheetState.hide()
                        }
                    }
                    navController.navigate(event.route) {
                        // Clear the back stack up to the landing page
                        popUpTo(DealerServiceAppointmentRoute.LandingPage.route) {
                            saveState = true
                        }
                        launchSingleTop = true
                    }
                }

                is UiEvent.Navigate -> {
                    additionalCommentsFullScreen.value = false
                    if (sheetState.isVisible) {
                        coroutineScope.launch {
                            sheetState.hide()
                        }
                    }
                    navController.navigate(event.route)
                }

                is UiEvent.Error -> {
                    // Do nothing
                }

                is UiEvent.Progress -> {
                    onProgressEvent(event)
                }

                else -> {
                    // Handle any other UiEvent types
                }
            }
        }
    }
}

internal fun launchShow(
    coroutineScope: CoroutineScope,
    sheetState: ModalBottomSheetState,
) {
    coroutineScope.launch {
        sheetState.show()
    }
}

internal fun onProgressEvent(event: UiEvent.Progress) {
    when (event.event) {
        ProgressEvent.ShowProgress -> true
        ProgressEvent.DismissProgress -> false
    }
}
