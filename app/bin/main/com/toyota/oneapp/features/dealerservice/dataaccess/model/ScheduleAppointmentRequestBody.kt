/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model

import com.google.gson.annotations.SerializedName

data class ScheduleAppointmentRequestBody(
    val services: SelectedServicesBody? = null,
    val advisor: AppointmentAdvisor? = null,
    val customerConcern: String? = null,
    val transportation: TransportationRequest? = null,
    val mileage: Int? = null,
    val scheduledTime: String? = null,
)

@Suppress("PropertyName")
data class SelectedServicesBody(
    val drs: List<AppointmentServiceListRequest>?,
    val frs: List<AppointmentServiceListRequest>?,
    val grs: List<AppointmentServiceListRequest>?,
    val packageItemsSelectable: Boolean?,
    @SerializedName("FRSPackageItemsSelectable") val FRSPackageItemsSelectable: Boolean?,
)

data class AppointmentAdvisor(
    val advisorId: String,
    val advisorName: String,
    val advisorPhotoUrl: String,
    val selected: Boolean = false,
)

data class TransportationRequest(
    val transportationId: String,
    val transportationName: String,
    val transportationDescription: String,
    val selected: Boolean,
    val pickup: TransportationLocation,
    val dropOff: TransportationLocation,
)

data class TransportationLocation(
    val city: String? = null,
    val country: String? = null,
    val state: String? = null,
    val street: String? = null,
    val zipcode: String? = null,
)

data class AppointmentServiceListRequest(
    val type: String,
    val serviceId: String,
    val serviceName: String,
    val serviceDescription: String,
    val price: Double,
    val displayPrice: String,
    val laborHour: Double,
    val selected: Boolean,
    val serviceType: String,
    val category: String,
    val packageId: String,
    val packageItemsSelectable: Boolean,
    val childServices: List<String>,
)
