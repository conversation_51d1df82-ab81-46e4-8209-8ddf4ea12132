/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model

import com.google.gson.annotations.SerializedName

data class DealerSearchFiltersResponse(
    @SerializedName("filters") val filters: FiltersResponse,
    @SerializedName("status") val status: StatusResponse,
)

data class FiltersResponse(
    @SerializedName("accessibility") val accessibility: List<String>?,
    @SerializedName("services") val services: List<String>?,
    @SerializedName("payments") val payments: List<String>?,
    @SerializedName("transportation") val transportation: List<String>?,
    @SerializedName("amenities") val amenities: List<String>?,
)
