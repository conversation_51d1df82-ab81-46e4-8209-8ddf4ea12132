/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model.common

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.dealerservice.dataaccess.model.CoordinateResponse

data class AddressResponse(
    @SerializedName("line1") val line1: String?,
    @SerializedName("line2") val line2: String?,
    @SerializedName("coordinate") val coordinate: CoordinateResponse?,
    @SerializedName("country") val country: String?,
    @SerializedName("city") val city: String?,
    @SerializedName("state") val state: String?,
    @SerializedName("timeZone") val timeZone: String?,
    @SerializedName("zipCode") val zipCode: String?,
)
