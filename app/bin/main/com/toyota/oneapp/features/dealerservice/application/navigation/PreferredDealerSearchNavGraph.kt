/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.toyota.oneapp.features.core.navigation.sharedViewModel
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.DealerSearchField
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.DealerSearchFilter
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.PreferredDealerSearch
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.PreferredDealerSearchEntry
import com.toyota.oneapp.features.dealerservice.presentation.preferreddealersearch.DealerSearchFieldScreen
import com.toyota.oneapp.features.dealerservice.presentation.preferreddealersearch.DealerSearchFilterScreen
import com.toyota.oneapp.features.dealerservice.presentation.preferreddealersearch.PreferredDealerSearchScreen
import com.toyota.oneapp.features.dealerservice.presentation.preferreddealersearch.PreferredDealerSearchViewModel

fun NavGraphBuilder.preferredDealerSearchNavGraph(navController: NavHostController) {
    navigation(
        route = PreferredDealerSearchEntry.route,
        startDestination = PreferredDealerSearch.route,
    ) {
        composable(
            route = PreferredDealerSearch.route,
        ) { entry ->
            val preferredDealerSearchViewModel =
                entry.sharedViewModel<PreferredDealerSearchViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            PreferredDealerSearchScreen(
                preferredDealerSearchViewModel = preferredDealerSearchViewModel,
                navController = navController,
            )
        }
        composable(
            route = DealerSearchFilter.route,
        ) { entry ->
            val preferredDealerSearchViewModel =
                entry.sharedViewModel<PreferredDealerSearchViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            DealerSearchFilterScreen(
                preferredDealerSearchViewModel = preferredDealerSearchViewModel,
                navController = navController,
            )
        }
        // androidx-navigation-version 2.7.3+ supports slide in animation to be added here.
        composable(
            route = DealerSearchField.route,
        ) { entry ->
            val preferredDealerSearchViewModel =
                entry.sharedViewModel<PreferredDealerSearchViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            DealerSearchFieldScreen(
                preferredDealerSearchViewModel = preferredDealerSearchViewModel,
                navController = navController,
            )
        }
    }
}
