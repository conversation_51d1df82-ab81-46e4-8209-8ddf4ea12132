/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.dealerservice.application.common.DealerServiceCommonUseCase
import com.toyota.oneapp.features.dealerservice.dataaccess.model.SetPreferredDealerRequest
import com.toyota.oneapp.features.dealerservice.domain.model.common.BusinessHours
import com.toyota.oneapp.features.dealerservice.domain.model.dealerdetails.BusinessHoursUi
import com.toyota.oneapp.features.dealerservice.domain.model.dealerdetails.BusinessHoursUiItem
import com.toyota.oneapp.features.dealerservice.domain.model.dealerdetails.DealerDetailState
import com.toyota.oneapp.features.dealerservice.domain.model.dealerdetails.DealerDetailUiItem
import com.toyota.oneapp.features.dealerservice.domain.model.dealerdetails.DealerDetailUiState
import com.toyota.oneapp.features.dealerservice.domain.model.dealerdetails.DetailListUiItem
import com.toyota.oneapp.features.dealerservice.domain.model.dealerdetails.UpdatePreferredDealerState
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.NationWideDealerships
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.toUiModel
import com.toyota.oneapp.features.dealerservice.domain.repository.DealerServiceRepository
import com.toyota.oneapp.features.dealerservice.presentation.dealerdetails.DealerDetailsConst.ACCESSIBILITY
import com.toyota.oneapp.features.dealerservice.presentation.dealerdetails.DealerDetailsConst.AMENITIES
import com.toyota.oneapp.features.dealerservice.presentation.dealerdetails.DealerDetailsConst.BUSINESS_HOURS
import com.toyota.oneapp.features.dealerservice.presentation.dealerdetails.DealerDetailsConst.PAYMENTS
import com.toyota.oneapp.features.dealerservice.presentation.dealerdetails.DealerDetailsConst.SERVICES
import com.toyota.oneapp.features.dealerservice.presentation.dealerdetails.DealerDetailsConst.TRANSPORTATIONS
import com.toyota.oneapp.features.dealerservice.presentation.dealerdetails.DealerDetailsConst.dayToStringRes
import com.toyota.oneapp.features.dealerservice.presentation.dealerdetails.DealerDetailsConst.weekDays
import com.toyota.oneapp.features.dealerservice.presentation.dealerdetails.DealerDetailsConst.weekOrderComparator
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.time.LocalDate
import javax.inject.Inject

class DealerDetailsLogic
    @Inject
    constructor(
        private val repository: DealerServiceRepository,
        applicationData: ApplicationData,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val dealerServiceCommonUseCase: DealerServiceCommonUseCase,
    ) : DealerDetailsUseCase, DealerServiceCommonUseCase by dealerServiceCommonUseCase {
        val vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()

        override fun getDealerById(dealerCode: String): Flow<DealerDetailState> {
            return flow {
                if (vehicleInfo == null) {
                    emit(DealerDetailState.Error(null))
                } else {
                    val response =
                        repository.getDealershipDetailsById(
                            dealerCode = dealerCode,
                            brand = vehicleInfo.brand,
                            region = vehicleInfo.region,
                        )
                    when (response) {
                        is Resource.Failure -> {
                            emit(DealerDetailState.Error(response.error?.message))
                        }
                        is Resource.Loading -> {
                            emit(DealerDetailState.Loading)
                        }
                        is Resource.Success -> {
                            val dealership = response.data?.payload?.dealershipDetailResponse?.toUiModel()
                            dealership?.let {
                                emit(dealershipDetailUiState(dealership))
                            }
                        }
                    }
                }
            }
        }

        override fun updatePreferredDealer(dealerCode: String): Flow<UpdatePreferredDealerState> {
            return flow {
                vehicleInfo?.let {
                    val body =
                        SetPreferredDealerRequest(
                            dlrCode = dealerCode,
                            guid = oneAppPreferenceModel.getGuid(),
                            vin = vehicleInfo.vin,
                            brandCode = if (vehicleInfo.isToyotaBrand) "T" else "L",
                            region = vehicleInfo.region,
                        )

                    val response =
                        repository.updatePreferredDealer(
                            vin = vehicleInfo.vin,
                            body = body,
                        )
                    when (response) {
                        is Resource.Failure -> {
                            emit(UpdatePreferredDealerState.Error(response.error.toString()))
                        }

                        is Resource.Loading -> {
                            emit(UpdatePreferredDealerState.Loading)
                        }

                        is Resource.Success -> {
                            response.data?.let {
                                emit(UpdatePreferredDealerState.Success(it))
                            }
                        }
                    }
                }
            }
        }

        private fun dealershipDetailUiState(dealership: NationWideDealerships): DealerDetailState.Success {
            val dealerLocation =
                dealership.addresses?.firstOrNull()?.coordinate?.let {
                    if (it.longitude != null && it.latitude != null) {
                        LatLng(
                            it.latitude,
                            it.longitude,
                        )
                    } else {
                        null
                    }
                }
            return DealerDetailState.Success(
                DealerDetailUiState(
                    dealership = dealership,
                    dealershipName = dealership.dealershipName ?: "",
                    dealerDetails = getDealerDetailsUiList(dealership),
                    dealerLocation = dealerLocation,
                    brand = dealership.brand ?: "Toyota",
                    phoneNumber = dealership.phoneNumbers?.firstOrNull(),
                    smartPath = dealership.smartPath,
                    spmLogo = dealership.spmLogo,
                    toyotaCode = dealership.toyotaCode,
                    website = dealership.website,
                ),
            )
        }

        private fun getDealerDetailsUiList(dealership: NationWideDealerships): List<DealerDetailUiItem> {
            val listSections: List<DealerDetailUiItem> =
                listOf(
                    getBusinessHoursUi(dealership.businessHours),
                    DetailListUiItem(
                        AMENITIES.name,
                        dealership.amenities ?: emptyList(),
                        icon = AMENITIES.icon,
                    ),
                    DetailListUiItem(
                        PAYMENTS.name,
                        dealership.paymentOptions ?: emptyList(),
                        icon = PAYMENTS.icon,
                    ),
                    DetailListUiItem(
                        TRANSPORTATIONS.name,
                        dealership.transportationOptions ?: emptyList(),
                        icon = TRANSPORTATIONS.icon,
                    ),
                    DetailListUiItem(
                        SERVICES.name,
                        dealership.services ?: emptyList(),
                        icon = SERVICES.icon,
                    ),
                    DetailListUiItem(
                        ACCESSIBILITY.name,
                        dealership.accessibilityOptions ?: emptyList(),
                        icon = ACCESSIBILITY.icon,
                    ),
                )
            return listSections
        }

        private fun getBusinessHoursUi(businessHours: List<BusinessHours>?): DealerDetailUiItem {
            val icon = BUSINESS_HOURS.icon
            if (businessHours.isNullOrEmpty()) {
                return BusinessHoursUiItem(
                    name = BUSINESS_HOURS.name,
                    itemCount = 0,
                    businessHours = listOf(),
                    icon = icon,
                )
            }

            val filledOutList = addMissingDays(businessHours)

            val sortedBusinessHours: List<BusinessHours> =
                sortBusinessHours(
                    filledOutList,
                    weekOrderComparator,
                )

            return BusinessHoursUiItem(
                name = BUSINESS_HOURS.name,
                itemCount = 1,
                businessHours = sortedBusinessHours.map { it.toBusinessHoursUi() },
                icon = icon,
            )
        }

        private fun BusinessHours.toBusinessHoursUi(): BusinessHoursUi {
            return BusinessHoursUi(
                dayOfWeek = dayOfWeek ?: "",
                dayName = dayToStringRes[dayOfWeek],
                isCurrentDay = LocalDate.now().dayOfWeek.name.equals(dayOfWeek, ignoreCase = true),
                operatingHours = formatBusinessHours(openingTime, closingTime),
            )
        }

        private fun formatBusinessHours(
            openingHours: String?,
            closingHours: String?,
        ): String? {
            if (openingHours == null || closingHours == null) {
                return null
            }
            val startTimeAmPm = from24To12Hour(openingHours)
            val closingTimeAmPm = from24To12Hour(closingHours)

            return "$startTimeAmPm - $closingTimeAmPm"
        }

        private fun from24To12Hour(time: String): String {
            val startHour = time.substring(0, 2).toInt()
            val startMinutes = time.substring(3, 5)
            return if (startHour > 12) "${startHour - 12}:$startMinutes PM" else "$startHour:$startMinutes AM"
        }

        private fun sortBusinessHours(
            businessHours: List<BusinessHours>,
            comparator: Comparator<BusinessHours>,
        ): List<BusinessHours> {
            return businessHours.sortedWith(comparator)
        }

        private fun addMissingDays(businessHours: List<BusinessHours>): List<BusinessHours> {
            if (businessHours.size == 7) {
                return businessHours
            }

            val filledOutList: ArrayList<BusinessHours> = ArrayList()
            filledOutList.addAll(businessHours)
            weekDays.forEach { searchDay ->
                val hasDay = businessHours.find { it.dayOfWeek == searchDay }
                if (hasDay == null) {
                    val newDay =
                        BusinessHours(
                            closingTime = null,
                            openingTime = null,
                            dayOfWeek = searchDay,
                        )
                    filledOutList.add(newDay)
                }
            }
            return filledOutList
        }
    }
