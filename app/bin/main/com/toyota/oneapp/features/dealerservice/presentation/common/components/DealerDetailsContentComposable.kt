/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.common.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun RowScope.DealerDetailsContentComposable(
    title: String,
    subtitle: String,
) {
    Box(modifier = Modifier.weight(weight = 1f, fill = true)) {
        Column(
            modifier =
                Modifier
                    .wrapContentWidth()
                    .padding(start = 0.dp),
            horizontalAlignment = Alignment.Start,
        ) {
            OABody4TextView(
                text = title,
                color = AppTheme.colors.tertiary03,
            )

            Spacer(modifier = Modifier.height(2.dp))

            OACallOut1TextView(
                text = subtitle,
                color = AppTheme.colors.tertiary05,
                maxLines = 1,
            )
        }
    }
}
