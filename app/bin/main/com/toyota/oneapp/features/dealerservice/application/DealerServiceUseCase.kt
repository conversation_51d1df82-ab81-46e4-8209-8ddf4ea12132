/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application

import com.toyota.oneapp.features.dealerservice.domain.model.MaintenanceTimeline
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.MaintenanceScheduleState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.MakeAppointmentState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.PreferredDealerState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.ServiceAppointmentInitialize
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.ServiceAppointmentInitializeState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.ServiceAppointmentsState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.SmartPathState
import kotlinx.coroutines.flow.Flow

interface DealerServiceUseCase : RecentServiceHistoryUseCase {
    fun getPreferredDealer(): Flow<PreferredDealerState>

    fun getServiceAppointments(
        dealerAppointmentsEnabled: Boolean,
        serviceHistoryEnabled: Boolean,
    ): Flow<ServiceAppointmentsState>

    fun getServiceAppointmentInitialize(dealerCode: String): Flow<ServiceAppointmentInitializeState>

    fun isCaRegion(): Boolean

    suspend fun isPreferredDealerSmartPath(dealerCode: String?): SmartPathState?

    fun processMakeAppointmentState(
        isPreferredDealerSet: Boolean,
        serviceAppointments: ServiceAppointmentsState.Success,
        serviceAppointmentInitialize: ServiceAppointmentInitialize,
    ): MakeAppointmentState.Success

    suspend fun fetchMaintenanceTimeline(
        lastKnownMileage: String,
        mileageUnit: String,
    ): MaintenanceTimeline?

    fun processMaintenanceSchedule(
        maintenanceTimelineEnabled: Boolean,
        maintenanceTimeline: MaintenanceTimeline?,
    ): MaintenanceScheduleState.Success
}
