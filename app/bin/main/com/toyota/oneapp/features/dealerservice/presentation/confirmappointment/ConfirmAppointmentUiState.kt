/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.confirmappointment

import com.toyota.oneapp.features.dealerservice.presentation.appointmentodometerpage.OdometerUnit

data class ConfirmAppointmentUiState(
    val odometer: String = "",
    val odometerUnit: OdometerUnit = OdometerUnit.MILES,
    val servicesDescription: String = "",
    val hasCustomService: Boolean = false,
    val serviceAdvisorDescription: String = "",
    val transportationMethod: String = "",
    val dateTime: String = "",
    val additionalComments: String = "",
    val bottomSheetUiState: BottomSheetUiState = BottomSheetUiState.ConfirmingAppointment,
)

sealed class BottomSheetUiState {
    data class AppointmentError(val errorMsg: String?) : BottomSheetUiState()

    data object ConfirmingAppointment : BottomSheetUiState()

    data object AppointmentSuccess : BottomSheetUiState()

    data class ConfirmEdit(val editConfirmationCategory: EditConfirmationCategory) : BottomSheetUiState()
}
