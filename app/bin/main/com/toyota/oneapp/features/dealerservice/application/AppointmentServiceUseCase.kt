/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application

import com.toyota.oneapp.features.dealerservice.domain.model.UIAppointmentServiceList
import com.toyota.oneapp.features.dealerservice.domain.model.UIAppointmentServices
import com.toyota.oneapp.features.dealerservice.domain.model.UpdatedChildServiceSelectionResult
import com.toyota.oneapp.features.dealerservice.domain.model.UpdatedPackageSelectionResult
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.AppointmentServiceState
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.ServiceItemType
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.ServiceTabState
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.TabState
import com.toyota.oneapp.features.dealerservice.presentation.appointmentodometerpage.OdometerUnit
import kotlinx.coroutines.flow.Flow

interface AppointmentServiceUseCase {
    fun fetchServiceAppointmentServiceList(
        dealerId: String,
        odometer: String,
    ): Flow<AppointmentServiceState>

    fun getMileageList(odometerValue: String): List<Int>

    fun calculateMileage(odometerValue: String): Int

    fun getOdometerUnit(): OdometerUnit

    fun getNumericOdometer(odometerValue: String): String

    fun getFilteredServices(
        appointmentServiceState: UIAppointmentServices?,
        selectedTabState: ServiceTabState,
        serviceType: ServiceItemType,
    ): List<UIAppointmentServiceList>

    fun checkFrsAndDrsServicesEmpty(
        services: UIAppointmentServices?,
        serviceType: ServiceItemType,
    ): Boolean

    fun isOnlyServiceAvailable(
        services: UIAppointmentServices?,
        serviceType: ServiceItemType,
    ): Boolean

    fun logFirebaseEvents(selectedItems: List<UIAppointmentServiceList>)

    fun filterServices(
        query: String,
        appointmentServiceState: UIAppointmentServices?,
        tabState: TabState,
    ): List<UIAppointmentServiceList>

    fun getTabState(
        selectedMileage: Int,
        calculatedMileage: Int,
        serviceItemType: ServiceItemType,
    ): ServiceTabState

    fun handlePackageWithServiceSelection(
        packageItem: UIAppointmentServiceList,
        selectedItems: List<UIAppointmentServiceList>,
        services: UIAppointmentServices?,
    ): UpdatedPackageSelectionResult

    fun handleChildServiceSelection(
        childServiceItem: UIAppointmentServiceList,
        selectedItems: List<UIAppointmentServiceList>,
        selectedChildServices: List<UIAppointmentServiceList>,
        services: UIAppointmentServices?,
        selectedPackage: UIAppointmentServiceList?,
    ): UpdatedChildServiceSelectionResult
}
