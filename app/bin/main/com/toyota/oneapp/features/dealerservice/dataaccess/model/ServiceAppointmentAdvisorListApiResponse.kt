/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model

import com.google.gson.annotations.SerializedName

data class ServiceAppointmentAdvisorListApiResponse(
    @SerializedName("advisors") val advisors: List<Advisors>?,
    @SerializedName("status") val status: StatusResponse?,
)

data class Advisors(
    @SerializedName("advisorId") val advisorId: String?,
    @SerializedName("advisorName") val advisorName: String?,
    @SerializedName("advisorPhotoUrl") val advisorPhotoUrl: String?,
    @SerializedName("selected") val selected: Boolean? = false,
)
