/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.common.components

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.SmartPathState

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
fun SmartPathImageComposable(smartPathState: SmartPathState) {
    GlideImage(
        modifier =
            Modifier
                .size(height = 34.dp, width = 90.dp)
                .padding(start = 12.dp, end = 8.dp),
        model =
            if (AppTheme.darkMode.collectAsState().value) {
                smartPathState.smpLogo.getOrNull(1)
            } else {
                smartPathState.smpLogo.getOrNull(0)
            },
        contentDescription = null,
    )
}
