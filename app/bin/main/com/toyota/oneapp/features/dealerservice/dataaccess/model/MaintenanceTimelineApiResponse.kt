/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model

import com.google.gson.annotations.SerializedName

data class MaintenanceTimelineApiResponse(
    @SerializedName("payload") val payload: MaintenanceTimelineResponse?,
    @SerializedName("status") val status: StatusResponse,
)

data class MaintenanceTimelineResponse(
    @SerializedName("specialInstructions") val specialInstructions: List<SpecialInstructionsResponse>?,
    @SerializedName("operatingConditions") val operatingConditions: List<OperatingConditionsResponse>?,
    @SerializedName("scheduleMaintenanceDetails")
    val scheduleMaintenanceDetails: List<ScheduleMaintenanceDetailsResponse>?,
    @SerializedName("serviceIntervalList") val serviceIntervalList: List<ServiceIntervalListResponse>?,
    @SerializedName("footNotes") val footNotes: List<FootNotesResponse>?,
    @SerializedName("vin") val vin: String?,
    @SerializedName("language") val language: String?,
    @SerializedName("outputType") val outputType: String?,
    @SerializedName("lastKnownMileage") val lastKnownMileage: String?,
)

data class SpecialInstructionsResponse(
    @SerializedName("specialInstructionId") val specialInstructionId: String?,
    @SerializedName("specialInstructionDesc") val specialInstructionDesc: String?,
)

data class OperatingConditionsResponse(
    @SerializedName("conditionId") val conditionId: String?,
    @SerializedName("description") val description: String?,
)

data class ScheduleMaintenanceDetailsResponse(
    @SerializedName("interval") val interval: String?,
    @SerializedName("intervalMileage") val intervalMileage: String?,
    @SerializedName("mileageUnit") val mileageUnit: String?,
    @SerializedName("serviceIntervalTime") val serviceIntervalTime: String?,
    @SerializedName("timeUnit") val timeUnit: String?,
    @SerializedName("subCategories") val subCategories: List<SubCategoriesResponse>?,
)

data class ServiceIntervalListResponse(
    @SerializedName("serviceIntervalMileage") val serviceIntervalMileage: String?,
    @SerializedName("mileageUnit") val mileageUnit: String?,
    @SerializedName("serviceIntervalTime") val serviceIntervalTime: String?,
    @SerializedName("timeUnit") val timeUnit: String?,
)

data class FootNotesResponse(
    @SerializedName("footNoteId") val footNotesId: Int?,
    @SerializedName("footNoteDesc") val footNoteDesc: String?,
)

data class SubCategoriesResponse(
    @SerializedName("operatingCondDescription") val operatingCondDescription: String?,
    @SerializedName("operatingConditionSort") val operatingConditionSort: String?,
    @SerializedName("maintenanceTasks") val maintenanceTasks: List<MaintenanceTasksResponse>?,
)

data class MaintenanceTasksResponse(
    @SerializedName("operatingCondDescription") val operatingCondDescription: String?,
    @SerializedName("serviceItemDescription") val serviceItemDescription: String?,
    @SerializedName("operatingConditionSort") val operatingConditionSort: String?,
    @SerializedName("serviceItemSort") val serviceItemSort: String?,
    @SerializedName("serviceActionSort") val serviceActionSort: String?,
    @SerializedName("footNotesId") val footNotesId: Int?,
)
