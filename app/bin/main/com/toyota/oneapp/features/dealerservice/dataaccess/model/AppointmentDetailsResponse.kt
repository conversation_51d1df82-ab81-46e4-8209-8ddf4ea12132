/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model

import com.google.gson.annotations.SerializedName

data class AppointmentDetailsResponse(
    @SerializedName("appointment") val appointment: ArrayList<Appointment> = arrayListOf(),
    @SerializedName("status") val status: StatusResponse,
)

data class Appointment(
    @SerializedName("advisor") val advisor: Advisor? = Advisor(),
    @SerializedName("appointmentId") val appointmentId: String? = null,
    @SerializedName("appointmentStatus") val appointmentStatus: String? = null,
    @SerializedName("comments") val comments: String? = null,
    @SerializedName("consolidatedServicesFlag") val consolidatedServicesFlag: Boolean? = null,
    @SerializedName("datetime") val datetime: String? = null,
    @SerializedName("dealership") val dealership: String? = null,
    @SerializedName("dealershipAddresses") val dealershipAddresses: ArrayList<DealershipAddresses> = arrayListOf(),
    @SerializedName("dealershipName") val dealershipName: String? = null,
    @SerializedName(
        "dealershipPhoneNumbers",
    ) val dealershipPhoneNumbers: ArrayList<DealershipPhoneNumbers> = arrayListOf(),
    @SerializedName("estimatedTotal") val estimatedTotal: String? = null,
    @SerializedName("odometer") val odometer: String? = null,
    @SerializedName("readOnly") val readOnly: Boolean? = null,
    @SerializedName("services") val services: ArrayList<AppointmentServices> = arrayListOf(),
    @SerializedName("smartPath") val smartPath: Boolean? = null,
    @SerializedName("spmLogo") val spmLogo: String? = null,
    @SerializedName("status") val status: String? = null,
    @SerializedName("transportation") val transportation: Transportation? = Transportation(),
)

data class Transportation(
    @SerializedName("dropOff") val dropOff: DropOff? = DropOff(),
    @SerializedName("pickup") val pickup: Pickup? = Pickup(),
    @SerializedName("rentalDetails") val rentalDetails: RentalDetails? = RentalDetails(),
    @SerializedName("transportationDescription") val transportationDescription: String? = null,
    @SerializedName("transportationId") val transportationId: String? = null,
    @SerializedName("transportationName") val transportationName: String? = null,
)

data class RentalDetails(
    @SerializedName("invoiceDetails") val invoiceDetails: InvoiceDetails? = InvoiceDetails(),
    @SerializedName("reservationId") val reservationId: String? = null,
    @SerializedName("vehicleDetails") val vehicleDetails: VehicleDetails? = VehicleDetails(),
)

data class VehicleDetails(
    @SerializedName("classDescription") val classDescription: String? = null,
)

data class InvoiceDetails(
    @SerializedName("dailyRate") val dailyRate: String? = null,
    @SerializedName("lateCharge") val lateCharge: String? = null,
    @SerializedName("rentalCharge") val rentalCharge: String? = null,
    @SerializedName("rentalDays") val rentalDays: String? = null,
    @SerializedName("totalCharge") val totalCharge: String? = null,
    @SerializedName("totalTaxes") val totalTaxes: String? = null,
)

data class Pickup(
    @SerializedName("city") val city: String? = null,
    @SerializedName("country") val country: String? = null,
    @SerializedName("state") val state: String? = null,
    @SerializedName("street") val street: String? = null,
    @SerializedName("zipcode") val zipcode: String? = null,
)

data class DropOff(
    @SerializedName("city") val city: String? = null,
    @SerializedName("country") val country: String? = null,
    @SerializedName("state") val state: String? = null,
    @SerializedName("street") val street: String? = null,
    @SerializedName("zipcode") val zipcode: String? = null,
)

data class AppointmentServices(
    @SerializedName("category") val category: String? = null,
    @SerializedName("childServices") val childServices: ArrayList<String> = arrayListOf(),
    @SerializedName("displayPrice") val displayPrice: String? = null,
    @SerializedName("packageId") val packageId: String? = null,
    @SerializedName("packageItemsSelectable") val packageItemsSelectable: Boolean? = null,
    @SerializedName("price") val price: Double? = null,
    @SerializedName("serviceDescription") val serviceDescription: String? = null,
    @SerializedName("serviceId") val serviceId: String? = null,
    @SerializedName("serviceName") val serviceName: String? = null,
    @SerializedName("type") val type: String? = null,
)

data class DealershipPhoneNumbers(
    @SerializedName("countryCode") val countryCode: String? = null,
    @SerializedName("label") val label: String? = null,
    @SerializedName("number") val number: String? = null,
)

data class DealershipAddresses(
    @SerializedName("Coordinate") val dealerCoordinate: DealerCoordinate? = DealerCoordinate(),
    @SerializedName("city") val city: String? = null,
    @SerializedName("country") val country: String? = null,
    @SerializedName("line1") val line1: String? = null,
    @SerializedName("line2") val line2: String? = null,
    @SerializedName("state") val state: String? = null,
    @SerializedName("timezone") val timezone: String? = null,
    @SerializedName("zipCode") val zipCode: String? = null,
)

data class DealerCoordinate(
    @SerializedName("latitude") val latitude: String? = null,
    @SerializedName("longitude") val longitude: String? = null,
)

data class Advisor(
    @SerializedName("advisorId") val advisorId: String? = null,
    @SerializedName("advisorName") val advisorName: String? = null,
    @SerializedName("advisorPhotoUrl") val advisorPhotoUrl: String? = null,
)
