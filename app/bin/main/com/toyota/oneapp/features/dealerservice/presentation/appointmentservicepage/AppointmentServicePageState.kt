/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.appointmentservicepage

import com.toyota.oneapp.features.dealerservice.domain.model.UIAppointmentServiceList
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.AppointmentServiceState
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.TabState
import com.toyota.oneapp.features.dealerservice.presentation.appointmentodometerpage.OdometerUnit

data class AppointmentServicePageState(
    val services: AppointmentServiceState = AppointmentServiceState.Loading,
    val selectedServices: List<UIAppointmentServiceList> = emptyList(),
    val shouldOpenBottomSheet: Boolean = false,
    val mileageList: List<Int> = emptyList(),
    val odometerUnit: OdometerUnit = OdometerUnit.MILES,
    val selectedMileage: Int? = null,
    val calculatedMileage: Int = 0,
    val selectedButtonIndex: Int = 0,
    val selectedServiceCategoryState: SelectedServiceCategoryState = SelectedServiceCategoryState(),
    val tabState: TabState = TabState(),
    val packageState: PackageState = PackageState(),
    val isCustomServiceExpanded: Boolean = false,
    val customServiceText: String = "",
    val customServiceState: CustomServiceState = CustomServiceState(),
    val filteredServices: List<UIAppointmentServiceList> = emptyList(),
    val searchQuery: String = "",
    val serviceDataEmpty: ServiceDataEmpty = ServiceDataEmpty(),
    var serviceCategoryState: ServiceCategoryState = ServiceCategoryState(),
)

data class ServiceDataEmpty(
    val checkAllFrsServicesEmpty: Boolean = false,
    val checkAllDrsServicesEmpty: Boolean = false,
) {
    fun hasNonEmptyServices(): Boolean {
        return !checkAllDrsServicesEmpty || !checkAllFrsServicesEmpty
    }

    fun areAllServicesEmpty(): Boolean {
        return checkAllFrsServicesEmpty && checkAllDrsServicesEmpty
    }
}

data class PackageState(
    val selectedPackage: UIAppointmentServiceList? = null,
    val selectedChildServices: List<UIAppointmentServiceList> = emptyList(),
)

data class CustomServiceState(
    val customServiceText: String = "",
    val isExpanded: Boolean = false,
)

data class ServiceCategoryState(
    val isOnlyFrsServiceAvailable: Boolean = false,
    val isOnlyDrsServiceAvailable: Boolean = false,
)

data class SelectedServiceCategoryState(
    val currentSelectedFrsItem: UIAppointmentServiceList? = null,
    val currentSelectedDrsItem: UIAppointmentServiceList? = null,
)
