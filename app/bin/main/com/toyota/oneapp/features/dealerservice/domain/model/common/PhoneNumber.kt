/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.domain.model.common

import com.toyota.oneapp.features.dealerservice.dataaccess.model.common.PhoneNumberResponse

data class PhoneNumber(
    val countryCode: String?,
    val label: String?,
    val number: String?,
)

fun PhoneNumberResponse.toUiModel(): PhoneNumber {
    return PhoneNumber(
        countryCode = countryCode,
        label = label,
        number = number,
    )
}
