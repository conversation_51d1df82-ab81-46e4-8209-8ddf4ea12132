/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.di

import com.toyota.oneapp.features.dealerservice.application.AppointmentDetailsLogic
import com.toyota.oneapp.features.dealerservice.application.AppointmentDetailsUseCase
import com.toyota.oneapp.features.dealerservice.application.AppointmentServiceLogic
import com.toyota.oneapp.features.dealerservice.application.AppointmentServiceUseCase
import com.toyota.oneapp.features.dealerservice.application.DealerDetailsLogic
import com.toyota.oneapp.features.dealerservice.application.DealerDetailsUseCase
import com.toyota.oneapp.features.dealerservice.application.DealerServiceLogic
import com.toyota.oneapp.features.dealerservice.application.DealerServiceUseCase
import com.toyota.oneapp.features.dealerservice.application.OdometerLogic
import com.toyota.oneapp.features.dealerservice.application.OdometerUseCase
import com.toyota.oneapp.features.dealerservice.application.PreferredDealerLogic
import com.toyota.oneapp.features.dealerservice.application.PreferredDealerUseCase
import com.toyota.oneapp.features.dealerservice.application.RecentServiceHistoryUseCase
import com.toyota.oneapp.features.dealerservice.application.RecentServiceHistoryUseCaseImpl
import com.toyota.oneapp.features.dealerservice.application.ServiceAdvisorLogic
import com.toyota.oneapp.features.dealerservice.application.ServiceAdvisorUseCase
import com.toyota.oneapp.features.dealerservice.application.ServiceHistoryLogic
import com.toyota.oneapp.features.dealerservice.application.ServiceHistoryUseCase
import com.toyota.oneapp.features.dealerservice.application.ServiceTransportationsLogic
import com.toyota.oneapp.features.dealerservice.application.ServiceTransportationsUseCase
import com.toyota.oneapp.features.dealerservice.application.TransportationPickUpDeliveryLogic
import com.toyota.oneapp.features.dealerservice.application.TransportationPickUpDeliveryUseCase
import com.toyota.oneapp.features.dealerservice.application.common.DealerServiceCommonLogic
import com.toyota.oneapp.features.dealerservice.application.common.DealerServiceCommonUseCase
import com.toyota.oneapp.features.dealerservice.dataaccess.repository.DealerServiceDefaultRepo
import com.toyota.oneapp.features.dealerservice.dataaccess.repository.ServiceHistoryDefaultRepo
import com.toyota.oneapp.features.dealerservice.domain.repository.DealerServiceRepository
import com.toyota.oneapp.features.dealerservice.domain.repository.ServiceHistoryRepository
import com.toyota.oneapp.features.dealerservice.domain.usecase.AppointmentDateTimeLogic
import com.toyota.oneapp.features.dealerservice.domain.usecase.AppointmentDateTimeUseCase
import com.toyota.oneapp.features.dealerservice.domain.usecase.ConfirmAppointmentLogic
import com.toyota.oneapp.features.dealerservice.domain.usecase.ConfirmAppointmentUseCase
import com.toyota.oneapp.features.dealerservice.domain.usecase.MaintenanceScheduleLogic
import com.toyota.oneapp.features.dealerservice.domain.usecase.MaintenanceScheduleUseCase
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class DealerServiceModule {
    @Binds
    abstract fun bindDealerServiceRepo(dealerServiceDefaultRepo: DealerServiceDefaultRepo): DealerServiceRepository

    @Binds
    @Singleton
    abstract fun bindServiceHistoryRepo(serviceHistoryDefaultRepo: ServiceHistoryDefaultRepo): ServiceHistoryRepository

    @Binds
    abstract fun provideDealerServiceAppointmentUseCase(dealerServiceLogic: DealerServiceLogic): DealerServiceUseCase

    @Binds
    abstract fun provideOdometerUseCase(odometerLogic: OdometerLogic): OdometerUseCase

    @Binds
    abstract fun providedPreferredDealerUseCase(preferredDealerLogic: PreferredDealerLogic): PreferredDealerUseCase

    @Binds
    abstract fun provideAppointmentServiceUseCase(appointmentServiceLogic: AppointmentServiceLogic): AppointmentServiceUseCase

    @Binds
    abstract fun provideDealerDetailsUseCase(dealerDetailsLogic: DealerDetailsLogic): DealerDetailsUseCase

    @Binds
    abstract fun provideDealerServiceCommonLogicUseCase(dealerServiceCommonLogic: DealerServiceCommonLogic): DealerServiceCommonUseCase

    @Binds
    abstract fun provideServiceAdvisorUseCase(serviceAdvisorLogic: ServiceAdvisorLogic): ServiceAdvisorUseCase

    @Binds
    abstract fun provideMaintenanceScheduleUseCase(maintenanceScheduleLogic: MaintenanceScheduleLogic): MaintenanceScheduleUseCase

    @Binds
    abstract fun provideServiceTransportationsUseCase(
        serviceTransportationsLogic: ServiceTransportationsLogic,
    ): ServiceTransportationsUseCase

    @Binds
    abstract fun provideTransportationPickUpDeliveryUseCase(
        transportationPickUpDeliveryLogic: TransportationPickUpDeliveryLogic,
    ): TransportationPickUpDeliveryUseCase

    @Binds
    abstract fun provideRecentServiceHistoryUseCase(
        recentServiceHistoryUseCaseImpl: RecentServiceHistoryUseCaseImpl,
    ): RecentServiceHistoryUseCase

    @Binds
    abstract fun provideServiceHistoryUseCase(serviceHistoryLogic: ServiceHistoryLogic): ServiceHistoryUseCase

    @Binds
    abstract fun provideAppointmentDetailsUseCase(appointmentDetailsLogic: AppointmentDetailsLogic): AppointmentDetailsUseCase

    @Binds
    abstract fun provideConfirmAppointmentUseCase(confirmAppointmentLogic: ConfirmAppointmentLogic): ConfirmAppointmentUseCase

    @Binds
    abstract fun provideAppointmentDateUseCase(appointmentDateTimeLogic: AppointmentDateTimeLogic): AppointmentDateTimeUseCase
}
