/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.dealerservice.dataaccess.model

import com.google.gson.annotations.SerializedName

data class DeleteAppointmentResponse(
    @SerializedName("appointmentId") val appointmentId: String?,
    @SerializedName("appointmentStatus") val appointmentStatus: String?,
    @SerializedName("status") val status: StatusResponse,
)
