/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application

import com.toyota.oneapp.features.dealerservice.presentation.appointmentdetailpage.AppointmentDetailStatusState
import com.toyota.oneapp.features.dealerservice.presentation.appointmentdetailpage.AppointmentServiceDetailsStatusState
import kotlinx.coroutines.flow.Flow

interface AppointmentDetailsUseCase {
    fun getAppointmentDetailsById(
        appointmentId: String,
        vin: String,
        brand: String,
    ): Flow<AppointmentDetailStatusState>

    fun getAppointmentDetailsByIdForServiceDetails(
        appointmentId: String,
        vin: String,
        brand: String,
        getUpdatedData: Boolean = false,
    ): Flow<AppointmentServiceDetailsStatusState>

    fun formatDisplayPrice(displayPrice: String?): String

    fun deleteServiceAppointment(
        appointmentId: String,
        dealerId: String,
        services: String,
        vin: String,
        brand: String,
    ): Flow<Boolean>

    suspend fun clearCache()
}
