/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application

import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.core.util.DateTimeUtil
import com.toyota.oneapp.features.dealerservice.application.common.DealerServiceCommonUseCase
import com.toyota.oneapp.features.dealerservice.domain.DealerServiceAppointmentConst
import com.toyota.oneapp.features.dealerservice.domain.model.AppointmentItem
import com.toyota.oneapp.features.dealerservice.domain.model.MaintenanceTimeline
import com.toyota.oneapp.features.dealerservice.domain.model.ServiceAppointments
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.ActiveAppointmentState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.LandingPageItemState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.LandingPageItemStyle
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.MaintenanceScheduleState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.MakeAppointmentState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.ServiceAppointmentInitialize
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.ServiceAppointmentState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.ServiceAppointmentsState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.SmartPathState
import com.toyota.oneapp.features.dealerservice.domain.model.toUiModel
import com.toyota.oneapp.features.dealerservice.domain.repository.DealerServiceRepository
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.text.NumberFormat
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale
import javax.inject.Inject

class DealerServiceLogic
    @Inject
    constructor(
        private val repository: DealerServiceRepository,
        applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        private val dealerServiceCommonUseCase: DealerServiceCommonUseCase,
    ) : DealerServiceUseCase,
        DealerServiceCommonUseCase by dealerServiceCommonUseCase {
        val vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()

        override fun isCaRegion(): Boolean = vehicleInfo?.isVehicleCanadian == true

        override suspend fun isPreferredDealerSmartPath(dealerCode: String?): SmartPathState? {
            if (vehicleInfo == null || vehicleInfo.region.isNullOrBlank() || vehicleInfo.brand.isNullOrBlank()) {
                return null
            }
            if (dealerCode.isNullOrBlank()) {
                return null
            }
            val response =
                repository.getDealershipDetailsById(
                    dealerCode = dealerCode,
                    brand = vehicleInfo.brand,
                    region = vehicleInfo.region,
                )
            when (response) {
                is Resource.Failure -> {
                    return null
                }

                is Resource.Loading -> {
                    // No loading state in UI.
                }
                is Resource.Success -> {
                    val dealershipDetail = response.data?.toUiModel()
                    if (dealershipDetail?.smartPath == null || dealershipDetail.spmLogo == null) {
                        return null
                    }
                    return SmartPathState(
                        isSmartPath = dealershipDetail.smartPath,
                        smpLogo = dealershipDetail.spmLogo.split(","),
                    )
                }

                else -> {
                    return null
                }
            }
            return null
        }

        override fun getServiceAppointments(
            dealerAppointmentsEnabled: Boolean,
            serviceHistoryEnabled: Boolean,
        ): Flow<ServiceAppointmentsState> =
            flow {
                if (vehicleInfo != null) {
                    val response =
                        repository.getServiceAppointments(
                            vin = vehicleInfo.vin,
                            brand = vehicleInfo.brand,
                        )
                    when (response) {
                        is Resource.Success -> {
                            analyticsLogger.logEventWithParameter(
                                event = AnalyticsEventParam.VEHICLE_PAGE,
                                paramValue = AnalyticsEventParam.VEHICLE_FETCH_SERVICE_APPOINTMENT_SUCCESS,
                            )
                            response.data?.let {
                                val serviceAppointments = it.toUiModel()
                                emit(
                                    ServiceAppointmentsState.Success(
                                        serviceAppointments = serviceAppointments,
                                        serviceHistoryItemState =
                                            appointmentsServiceHistoryItemState(
                                                serviceHistoryEnabled = serviceHistoryEnabled,
                                                serviceAppointments = serviceAppointments,
                                            ),
                                        appointmentsItemState =
                                            appointmentsItemState(
                                                dealerAppointmentsEnabled = dealerAppointmentsEnabled,
                                            ),
                                    ),
                                )
                            }
                        }

                        is Resource.Loading -> {
                            emit(ServiceAppointmentsState.Loading)
                        }

                        is Resource.Failure -> {
                            analyticsLogger.logEventWithParameter(
                                event = AnalyticsEventParam.VEHICLE_PAGE,
                                paramValue = AnalyticsEventParam.VEHICLE_FETCH_SERVICE_APPOINTMENT_FAILURE,
                            )
                            emit(
                                ServiceAppointmentsState.Error(
                                    code = response.error?.code.toString(),
                                    message = response.error?.message,
                                ),
                            )
                        }
                    }
                } else {
                    emit(ServiceAppointmentsState.Error())
                }
            }

        override fun processMakeAppointmentState(
            isPreferredDealerSet: Boolean,
            serviceAppointments: ServiceAppointmentsState.Success,
            serviceAppointmentInitialize: ServiceAppointmentInitialize,
        ): MakeAppointmentState.Success {
            val serviceAppointmentState =
                getServiceAppointmentType(
                    hasUpcomingAppointment = serviceAppointments.serviceAppointments.upcoming.isNotEmpty(),
                    isSchedulingAvailable = serviceAppointmentInitialize.schedulingAvailable,
                )
            val nextAppointment = serviceAppointments.serviceAppointments.upcoming.firstOrNull()
            return MakeAppointmentState.Success(
                isPreferredDealerSet = isPreferredDealerSet,
                serviceAppointmentState = serviceAppointmentState,
                activeAppointmentState =
                    if (nextAppointment != null) {
                        getActiveAppointmentUiState(
                            nextAppointment,
                        )
                    } else {
                        null
                    },
            )
        }

        override fun processMaintenanceSchedule(
            maintenanceTimelineEnabled: Boolean,
            maintenanceTimeline: MaintenanceTimeline?,
        ): MaintenanceScheduleState.Success {
            if (!maintenanceTimelineEnabled) {
                return MaintenanceScheduleState.Success(
                    maintenanceItemState =
                        LandingPageItemState(
                            visible = false,
                            style = LandingPageItemStyle.Success,
                            subtitle = R.string.dsa_no_schedules_found,
                            stateIcon = R.drawable.ic_check,
                        ),
                )
            }

            // Correct logic for maintenanceDue is being determined. Currently not implemented in flutter.
            val maintenanceDue = false
            val style =
                if (maintenanceDue) LandingPageItemStyle.Warning else LandingPageItemStyle.Success
            val subtitle =
                if (maintenanceTimeline?.serviceIntervalList.isNullOrEmpty()) {
                    R.string.dsa_no_schedules_found
                } else {
                    R.string.dsa_maintenance_subtitle
                }

            return MaintenanceScheduleState.Success(
                maintenanceItemState =
                    LandingPageItemState(
                        visible = true,
                        style = style,
                        subtitle = subtitle,
                        subtitleArgs = getMaintenanceSubtitleArgs(maintenanceTimeline),
                        stateIcon =
                            if (style == LandingPageItemStyle.Success) {
                                R.drawable.ic_check
                            } else {
                                R.drawable.ic_alert_no_background
                            },
                    ),
            )
        }

        private fun getMaintenanceSubtitleArgs(maintenanceTimeline: MaintenanceTimeline?): List<String>? {
            val current =
                maintenanceTimeline?.let {
                    maintenanceTimeline.scheduleMaintenanceDetailsList.find {
                        it.interval != null && it.interval.lowercase() == "current"
                    }
                }
            return current?.let {
                if (it.intervalMileage != null && it.mileageUnit != null) {
                    listOf(formatMaintenanceSubtitleArg(it.intervalMileage, it.mileageUnit))
                } else {
                    null
                }
            }
        }

        private fun formatMaintenanceSubtitleArg(
            interval: String,
            timeUnit: String,
        ): String {
            val intervalFormatted =
                try {
                    NumberFormat.getIntegerInstance().format(interval.toInt())
                } catch (_: IllegalArgumentException) {
                    interval
                }
            return "$intervalFormatted $timeUnit"
        }

        private fun getActiveAppointmentUiState(nextAppointment: AppointmentItem): ActiveAppointmentState {
            val timedZoneDate: ZonedDateTime =
                ZonedDateTime.parse(
                    nextAppointment.dateTime,
                    DateTimeFormatter.ofPattern(DateTimeUtil.TIME_FORMAT_WITH_TSZ),
                )

            val dayName =
                timedZoneDate.dayOfWeek.name
                    .lowercase()
                    .replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }
                    .take(3)
            val month =
                timedZoneDate.month.name
                    .lowercase()
                    .replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }
                    .take(3)
            val appointmentTime =
                timedZoneDate
                    .toLocalTime()
                    .format(
                        DateTimeFormatter.ofPattern(DealerServiceAppointmentConst.TIME_WITH_AM_PM_PATTERN),
                    ).replaceFirst(DealerServiceAppointmentConst.REMOVE_LEADING_ZERO_PATTERN.toRegex(), "")
                    .lowercase(Locale.getDefault())

            return ActiveAppointmentState(
                dayName = dayName,
                month = month,
                dayOfMonth = timedZoneDate.dayOfMonth,
                appointmentTime = appointmentTime,
                appointmentId = nextAppointment.appointmentId,
            )
        }

        private fun appointmentsServiceHistoryItemState(
            serviceHistoryEnabled: Boolean,
            serviceAppointments: ServiceAppointments,
        ): LandingPageItemState {
            var lastAppointmentTime = ""
            if (serviceAppointments.completed.isNotEmpty()) {
                val timedZoneDate: ZonedDateTime =
                    ZonedDateTime.parse(
                        serviceAppointments.completed[0].dateTime,
                        DateTimeFormatter.ofPattern(DateTimeUtil.TIME_FORMAT_WITH_TSZ),
                    )
                val month =
                    timedZoneDate.month.name
                        .lowercase()
                        .replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }
                        .take(3)
                lastAppointmentTime =
                    buildString {
                        this
                            .append(month)
                            .append(" ")
                            .append(timedZoneDate.dayOfMonth)
                            .append(" ,")
                            .append(timedZoneDate.year)
                    }
            }
            return LandingPageItemState(
                visible = serviceHistoryEnabled,
                subtitle =
                    if (serviceAppointments.completed.isEmpty()) {
                        R.string.dsa_no_service_history
                    } else {
                        R.string.dsa_last_service_date
                    },
                subtitleArgs = listOf(lastAppointmentTime),
            )
        }

        private fun appointmentsItemState(dealerAppointmentsEnabled: Boolean) =
            LandingPageItemState(
                visible = dealerAppointmentsEnabled,
                subtitle = R.string.dsa_upcoming_and_past_service,
            )

        private fun getServiceAppointmentType(
            hasUpcomingAppointment: Boolean,
            isSchedulingAvailable: Boolean?,
        ): ServiceAppointmentState {
            if (hasUpcomingAppointment) {
                return ServiceAppointmentState.ActiveAppointment
            }
            return if (isSchedulingAvailable == true) {
                ServiceAppointmentState.NoScheduledAppointment
            } else {
                ServiceAppointmentState.AppointmentSchedulingUnavailable
            }
        }
    }
