/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.appointmentservicepage

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.features.core.presentation.UiEvent
import com.toyota.oneapp.features.dealerservice.application.AppointmentServiceUseCase
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute.AppointmentAdvisorPage
import com.toyota.oneapp.features.dealerservice.domain.model.MakeAppointment
import com.toyota.oneapp.features.dealerservice.domain.model.UIAppointmentServiceList
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.AppointmentServiceState
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.ServiceItemType
import com.toyota.oneapp.features.dealerservice.domain.model.getMakeAppointment
import com.toyota.oneapp.features.dealerservice.domain.model.toEncodedJson
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class
AppointmentServicePageViewModel
    @Inject
    constructor(
        internal val applicationData: ApplicationData,
        private val appointmentServiceUseCase: AppointmentServiceUseCase,
        private val dispatcherProvider: DispatcherProvider,
        private val savedStateHandle: SavedStateHandle,
    ) : BaseViewModel<AppointmentServicePageState, AppointmentServicePageEvent>() {
        private var makeAppointmentData: MakeAppointment = MakeAppointment()

        override fun defaultState() = AppointmentServicePageState()

        init {
            loadAppointmentStateData()
            fetchServiceAppointmentData()
            updateOdometerUnit()
        }

        private fun loadAppointmentStateData() {
            makeAppointmentData = savedStateHandle.getMakeAppointment()
        }

        override fun onEvent(event: AppointmentServicePageEvent) {
            when (event) {
                is AppointmentServicePageEvent.ToggleItemSelection ->
                    updateSelectedItemsState(
                        item = event.item,
                        isSelected = event.isSelected,
                    )
                is AppointmentServicePageEvent.SelectItem ->
                    updateSelectedItemsState(
                        item = event.item,
                        itemType = event.itemType,
                    )
                is AppointmentServicePageEvent.SelectButton ->
                    state.update {
                        it.copy(selectedButtonIndex = event.index)
                    }
                is AppointmentServicePageEvent.ShowServices -> showServicesStateUpdate(event)
                is AppointmentServicePageEvent.SelectMileage -> selectMileageStateUpdate(event.mileage)
                is AppointmentServicePageEvent.SelectPackage -> selectPackageItemStateUpdate(event)
                is AppointmentServicePageEvent.SelectChildService ->
                    selectChildServiceStateUpdate(
                        event,
                    )
                is AppointmentServicePageEvent.CustomServiceTextChanged -> {
                    state.update { currentState ->
                        currentState.copy(
                            customServiceState =
                                currentState.customServiceState.copy(
                                    customServiceText = event.updatedText,
                                ),
                        )
                    }
                }
                is AppointmentServicePageEvent.ToggleCustomServiceExpand -> {
                    state.update { currentState ->
                        state.value.copy(
                            customServiceState =
                                currentState.customServiceState.copy(
                                    isExpanded = !currentState.customServiceState.isExpanded,
                                ),
                        )
                    }
                }

                is AppointmentServicePageEvent.OpenBottomSheet ->
                    updateBottomSheetState(
                        shouldOpen = true,
                    )
                is AppointmentServicePageEvent.ResetBottomSheetState ->
                    updateBottomSheetState(
                        shouldOpen = false,
                    )

                is AppointmentServicePageEvent.UpdateFilteredServices ->
                    searchFilteredServiceStateUpdate(
                        event.query,
                    )

                is AppointmentServicePageEvent.ContinueButtonClicked -> continueAppointmentEvent()
            }
        }

        private fun updateSelectedItemsState(
            item: UIAppointmentServiceList,
            isSelected: Boolean? = null,
            itemType: ServiceItemType? = null,
        ) {
            val updatedSelectedItems = state.value.selectedServices.toMutableList()
            val shouldAddItem = isSelected ?: !updatedSelectedItems.contains(item)

            if (shouldAddItem) {
                updatedSelectedItems.add(
                    item.copy(
                        selected = true,
                    ),
                )
            } else {
                updatedSelectedItems.remove(item)
            }

            state.update { currentState ->
                when (itemType) {
                    ServiceItemType.FRS -> {
                        val currentSelectedFrsItem =
                            if (currentState.selectedServiceCategoryState.currentSelectedFrsItem ==
                                item
                            ) {
                                null
                            } else {
                                item
                            }
                        currentState.copy(
                            selectedServiceCategoryState =
                                currentState.selectedServiceCategoryState.copy(
                                    currentSelectedFrsItem = currentSelectedFrsItem,
                                    currentSelectedDrsItem = null,
                                ),
                            selectedServices = updatedSelectedItems,
                        )
                    }
                    ServiceItemType.DRS -> {
                        val currentSelectedDrsItem =
                            if (currentState.selectedServiceCategoryState.currentSelectedDrsItem ==
                                item
                            ) {
                                null
                            } else {
                                item
                            }
                        currentState.copy(
                            selectedServiceCategoryState =
                                currentState.selectedServiceCategoryState.copy(
                                    currentSelectedDrsItem = currentSelectedDrsItem,
                                    currentSelectedFrsItem = null,
                                ),
                            selectedServices = updatedSelectedItems,
                        )
                    }
                    else ->
                        currentState.copy(
                            selectedServices = updatedSelectedItems,
                        )
                }
            }
        }

        private fun checkFrsAndDrsServiceAvailability() {
            val currentState = state.value
            if (currentState.services is AppointmentServiceState.Success) {
                val services = (currentState.services).services?.services
                val checkAllFrsServicesEmpty =
                    appointmentServiceUseCase.checkFrsAndDrsServicesEmpty(
                        services,
                        serviceType = ServiceItemType.FRS,
                    )
                val checkAllDrsServicesEmpty =
                    appointmentServiceUseCase.checkFrsAndDrsServicesEmpty(
                        services,
                        serviceType = ServiceItemType.DRS,
                    )
                state.update {
                    val updatedState =
                        currentState.copy(
                            serviceDataEmpty =
                                currentState.serviceDataEmpty.copy(
                                    checkAllFrsServicesEmpty = checkAllFrsServicesEmpty,
                                    checkAllDrsServicesEmpty = checkAllDrsServicesEmpty,
                                ),
                        )
                    if (updatedState.serviceDataEmpty.areAllServicesEmpty()) {
                        onEvent(AppointmentServicePageEvent.SelectButton(1))
                    }
                    updatedState
                }
            }
        }

        private fun checkFrsAndDrsNoPackageCategoryStateUpdate() {
            val currentState = state.value
            if (currentState.services is AppointmentServiceState.Success) {
                val frsServices = (currentState.services).services?.services
                val drsServices = (currentState.services).services?.services
                val isOnlyFrsServiceAvailable =
                    appointmentServiceUseCase.isOnlyServiceAvailable(
                        frsServices,
                        ServiceItemType.FRS,
                    )
                val isOnlyDrsServiceAvailable =
                    appointmentServiceUseCase.isOnlyServiceAvailable(
                        drsServices,
                        ServiceItemType.DRS,
                    )
                val currentCategoryState = currentState.serviceCategoryState
                state.update {
                    currentState.copy(
                        serviceCategoryState =
                            currentCategoryState.copy(
                                isOnlyFrsServiceAvailable = isOnlyFrsServiceAvailable,
                                isOnlyDrsServiceAvailable = isOnlyDrsServiceAvailable,
                            ),
                    )
                }
            }
        }

        private fun searchFilteredServiceStateUpdate(query: String) {
            if (query.isEmpty()) {
                state.update {
                    it.copy(
                        filteredServices = emptyList(),
                        searchQuery = "",
                    )
                }
                return
            }

            val appointmentServiceState = (state.value.services as? AppointmentServiceState.Success)?.services?.services

            val filteredServices =
                appointmentServiceUseCase.filterServices(
                    query = query,
                    appointmentServiceState = appointmentServiceState,
                    tabState = state.value.tabState,
                )

            state.update {
                state.value.copy(
                    filteredServices = filteredServices,
                    searchQuery = query,
                )
            }
        }

        private fun continueAppointmentEvent() {
            appointmentServiceUseCase.logFirebaseEvents(
                state.value.selectedServices,
            )
            val makeAppointment =
                MakeAppointment(
                    appointmentId = makeAppointmentData.appointmentId,
                    odometer = makeAppointmentData.odometer,
                    selectedServices = state.value.selectedServices,
                    preferredDealer = makeAppointmentData.preferredDealer,
                )
            val encodedAppointment = makeAppointment.toEncodedJson()
            encodedAppointment?.let {
                val routeWithArgs =
                    AppointmentAdvisorPage.route
                        .replace("{${MakeAppointment.MAKE_APPOINTMENT_DATA}}", it)
                sendEvent(UiEvent.Navigate(routeWithArgs))
            }
        }

        private fun showServicesStateUpdate(event: AppointmentServicePageEvent.ShowServices) {
            val selectedMileage = event.mileage
            val calculatedMileage = state.value.calculatedMileage

            val serviceFrsTabState =
                appointmentServiceUseCase.getTabState(
                    selectedMileage = selectedMileage,
                    calculatedMileage = calculatedMileage,
                    serviceItemType = ServiceItemType.FRS,
                )
            val serviceDrsTabState =
                appointmentServiceUseCase.getTabState(
                    selectedMileage = selectedMileage,
                    calculatedMileage = calculatedMileage,
                    serviceItemType = ServiceItemType.DRS,
                )
            state.update {
                state.value.copy(
                    selectedMileage = selectedMileage,
                    tabState =
                        state.value.tabState.copy(
                            serviceFrsTabState = serviceFrsTabState,
                            serviceDrsTabState = serviceDrsTabState,
                        ),
                )
            }
        }

        private fun selectPackageItemStateUpdate(event: AppointmentServicePageEvent.SelectPackage) {
            val services = (state.value.services as? AppointmentServiceState.Success)?.services

            val result =
                appointmentServiceUseCase.handlePackageWithServiceSelection(
                    packageItem = event.packageItem,
                    selectedItems = state.value.selectedServices,
                    services = services?.services,
                )

            state.update { currentState ->
                when (event.itemType) {
                    ServiceItemType.FRS ->
                        currentState.copy(
                            packageState =
                                currentState.packageState.copy(
                                    selectedPackage = result.selectedPackage,
                                    selectedChildServices = result.updatedChildServices,
                                ),
                            selectedServices = result.updatedSelectedItems,
                            selectedServiceCategoryState = (
                                currentState.selectedServiceCategoryState.copy(
                                    currentSelectedFrsItem = result.selectedPackage,
                                    currentSelectedDrsItem = null,
                                )
                            ),
                        )

                    ServiceItemType.DRS ->
                        currentState.copy(
                            packageState =
                                currentState.packageState.copy(
                                    selectedPackage = result.selectedPackage,
                                    selectedChildServices = result.updatedChildServices,
                                ),
                            selectedServices = result.updatedSelectedItems,
                            selectedServiceCategoryState = (
                                currentState.selectedServiceCategoryState.copy(
                                    currentSelectedFrsItem = null,
                                    currentSelectedDrsItem = result.selectedPackage,
                                )
                            ),
                        )
                }
            }
        }

        private fun selectChildServiceStateUpdate(event: AppointmentServicePageEvent.SelectChildService) {
            val services = (state.value.services as? AppointmentServiceState.Success)?.services

            val result =
                appointmentServiceUseCase.handleChildServiceSelection(
                    childServiceItem = event.childServiceItem,
                    selectedItems = state.value.selectedServices,
                    selectedChildServices = state.value.packageState.selectedChildServices,
                    services = services?.services,
                    selectedPackage = state.value.packageState.selectedPackage,
                )

            state.update {
                state.value.copy(
                    packageState =
                        state.value.packageState.copy(
                            selectedChildServices = result.updatedChildServices,
                            selectedPackage = result.selectedPackage,
                        ),
                    selectedServices = result.updatedSelectedItems,
                )
            }
        }

        private fun selectMileageStateUpdate(mileage: Int) {
            state.update { currentState ->
                currentState.copy(
                    selectedMileage = mileage,
                )
            }
        }

        private fun fetchServiceAppointmentData() {
            viewModelScope.launch(dispatcherProvider.main()) {
                val dealerCode = makeAppointmentData.appointmentId

                val odometerLocalValue =
                    appointmentServiceUseCase.getNumericOdometer(
                        makeAppointmentData.odometer,
                    )

                calculateMileage(odometerLocalValue)

                appointmentServiceUseCase
                    .fetchServiceAppointmentServiceList(
                        dealerId = dealerCode,
                        odometer = odometerLocalValue,
                    ).collect { serviceState ->
                        when (serviceState) {
                            is AppointmentServiceState.Success -> {
                                state.update { currentState ->
                                    currentState.copy(
                                        services = serviceState,
                                    )
                                }
                                checkFrsAndDrsServiceAvailability()
                                checkFrsAndDrsNoPackageCategoryStateUpdate()
                            }
                            else -> {
                                // Do nothing
                            }
                        }
                    }
            }
        }

        private fun calculateMileage(odometerValue: String) {
            val calculatedMileage = appointmentServiceUseCase.calculateMileage(odometerValue)
            val mileageList = appointmentServiceUseCase.getMileageList(odometerValue)
            state.update { currentState ->
                val selectedMileageValue = currentState.selectedMileage ?: calculatedMileage
                currentState.copy(
                    calculatedMileage = calculatedMileage,
                    mileageList = mileageList,
                    selectedMileage = selectedMileageValue,
                )
            }
        }

        private fun updateOdometerUnit() {
            val odometerUnit = appointmentServiceUseCase.getOdometerUnit()
            state.update { it.copy(odometerUnit = odometerUnit) }
        }

        private fun updateBottomSheetState(shouldOpen: Boolean) {
            state.update { it.copy(shouldOpenBottomSheet = shouldOpen) }
        }
    }
