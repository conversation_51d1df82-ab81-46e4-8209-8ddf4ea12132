/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model

import com.google.gson.annotations.SerializedName

data class ServiceAppointmentTransportationListApiResponse(
    @SerializedName("transportations") val transportations: List<Transportations>?,
    @SerializedName("status") val status: StatusResponse?,
)

data class Transportations(
    @SerializedName("transportationId") val transportationId: String?,
    @SerializedName("transportationName") val transportationName: String?,
    @SerializedName("transportationDescription") val transportationDescription: String?,
    @SerializedName("selected") val selected: Boolean? = false,
    @SerializedName("pickup") val pickup: TransportationLocationAddress?,
    @SerializedName("dropOff") val dropOff: TransportationLocationAddress?,
)

data class TransportationLocationAddress(
    @SerializedName("street") val street: String?,
    @SerializedName("city") val city: String?,
    @SerializedName("state") val state: String?,
    @SerializedName("country") val country: String?,
    @SerializedName("zipcode") val zipcode: String?,
)
