/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model

import com.google.gson.annotations.SerializedName

data class ServiceAppointmentResponse(
    @SerializedName("upcoming")
    val upcoming: List<AppointmentItemResponse>,
    @SerializedName("completed")
    val completed: List<AppointmentItemResponse>,
    @SerializedName("status")
    val status: StatusResponse,
)

data class AppointmentItemResponse(
    @SerializedName("appointmentId")
    val appointmentId: String?,
    @SerializedName("dealershipName")
    val dealershipName: String?,
    @SerializedName("dateTime")
    val dateTime: String?,
    @SerializedName("selectedServices")
    val selectedServices: List<SelectedServicesResponse>?,
    @SerializedName("smartPath")
    val smartPath: Boolean?,
    @SerializedName("spmLogo")
    val spmLogo: String?,
    @SerializedName("odometer")
    val odometer: String?,
    @SerializedName("invoiceTotal")
    val invoiceTotal: String?,
)

data class SelectedServicesResponse(
    @SerializedName("name")
    val name: String?,
)
