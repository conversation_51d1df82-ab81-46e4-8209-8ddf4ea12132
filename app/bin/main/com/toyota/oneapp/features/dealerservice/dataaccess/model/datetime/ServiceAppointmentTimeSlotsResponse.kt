/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model.datetime

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.dealerservice.dataaccess.model.StatusResponse

data class ServiceAppointmentTimeSlotsResponse(
    @SerializedName("timeslots") val timeslots: List<String>,
    @SerializedName("status") val status: StatusResponse,
)
