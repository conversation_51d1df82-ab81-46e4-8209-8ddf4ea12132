/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model.servicehistory

import com.google.gson.annotations.SerializedName

data class ServiceHistoryRequest(
    @SerializedName("serviceProvider") val serviceProvider: String?,
    @SerializedName("odoMeter") val odoMeter: String?,
    @SerializedName("serviceDate") val serviceDate: String?,
    @SerializedName("serviceItems") val serviceItems: List<ServiceItem>?,
    @SerializedName("notes") val notes: String?,
)

data class ServiceItem(
    @SerializedName("serviceOther") val serviceItem: String,
)
