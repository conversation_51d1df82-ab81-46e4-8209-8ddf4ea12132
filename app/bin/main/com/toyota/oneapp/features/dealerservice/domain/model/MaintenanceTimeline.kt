/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.domain.model

import com.toyota.oneapp.features.dealerservice.dataaccess.model.FootNotesResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.MaintenanceTasksResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.MaintenanceTimelineApiResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.OperatingConditionsResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ScheduleMaintenanceDetailsResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceIntervalListResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.SubCategoriesResponse

data class MaintenanceTimeline(
    val operatingConditions: List<OperatingConditions>,
    val serviceIntervalList: List<ServiceIntervalList>,
    val scheduleMaintenanceDetailsList: List<ScheduleMaintenanceDetails>,
    val footNotes: List<FootNotes>,
    val vin: String?,
    val language: String?,
    val outputType: String?,
    val lastKnownMileage: String?,
)

data class ServiceIntervalList(
    val serviceIntervalMileage: String?,
    val mileageUnit: String?,
    val serviceIntervalTime: String?,
    val timeUnit: String?,
)

data class ScheduleMaintenanceDetails(
    val interval: String?,
    val intervalMileage: String?,
    val mileageUnit: String?,
    val serviceIntervalTime: String?,
    val timeUnit: String?,
    val subCategories: List<SubCategories>,
)

data class SubCategories(
    val operatingCondDescription: String?,
    val operatingConditionSort: String?,
    val maintenanceTasks: List<MaintenanceTasks>,
)

data class MaintenanceTasks(
    val operatingCondDescription: String?,
    val serviceItemDescription: String?,
    val operatingConditionSort: String?,
    val serviceItemSort: String?,
    val serviceActionSort: String?,
    val footNotesId: Int?,
)

data class OperatingConditions(
    val conditionId: String?,
    val description: String?,
)

data class FootNotes(
    val footNotesId: Int?,
    val footNotesDesc: String?,
)

fun MaintenanceTimelineApiResponse.toUiModel(): MaintenanceTimeline? {
    val maintenanceTimelineResponse = this.payload
    maintenanceTimelineResponse?.let {
        val serviceIntervalList: MutableList<ServiceIntervalList> = mutableListOf()
        it.serviceIntervalList?.forEach { serviceIntervalListResponse ->
            serviceIntervalList.add(serviceIntervalListResponse.toUiModel())
        }
        val scheduleMaintenanceDetailList: MutableList<ScheduleMaintenanceDetails> = mutableListOf()
        it.scheduleMaintenanceDetails?.forEach { scheduleMaintenanceDetails ->
            scheduleMaintenanceDetailList.add(scheduleMaintenanceDetails.toUiModel())
        }
        val operatingConditions: MutableList<OperatingConditions> = mutableListOf()
        it.operatingConditions?.forEach { operatingCondition ->
            operatingConditions.add(operatingCondition.toUiModel())
        }
        val footNotes: MutableList<FootNotes> = mutableListOf()
        it.footNotes?.forEach { footNote ->
            footNotes.add(footNote.toUiModel())
        }
        return MaintenanceTimeline(
            operatingConditions = operatingConditions,
            serviceIntervalList = serviceIntervalList,
            scheduleMaintenanceDetailsList = scheduleMaintenanceDetailList,
            footNotes = footNotes,
            vin = it.vin,
            language = it.language,
            outputType = it.outputType,
            lastKnownMileage = it.lastKnownMileage,
        )
    }
    return null
}

fun FootNotesResponse.toUiModel(): FootNotes {
    return FootNotes(
        footNotesId = this.footNotesId,
        footNotesDesc = this.footNoteDesc,
    )
}

fun OperatingConditionsResponse.toUiModel(): OperatingConditions {
    return OperatingConditions(
        conditionId = this.conditionId,
        description = this.description,
    )
}

fun ServiceIntervalListResponse.toUiModel(): ServiceIntervalList {
    return ServiceIntervalList(
        serviceIntervalMileage = this.serviceIntervalMileage,
        mileageUnit = this.mileageUnit,
        serviceIntervalTime = this.serviceIntervalTime,
        timeUnit = this.timeUnit,
    )
}

fun ScheduleMaintenanceDetailsResponse.toUiModel(): ScheduleMaintenanceDetails {
    val subCategoriesList: MutableList<SubCategories> = mutableListOf()
    this.subCategories?.forEach {
        subCategoriesList.add(it.toUiModel())
    }

    return ScheduleMaintenanceDetails(
        interval = this.interval,
        intervalMileage = this.intervalMileage,
        mileageUnit = this.mileageUnit,
        serviceIntervalTime = this.serviceIntervalTime,
        timeUnit = this.timeUnit,
        subCategories = subCategoriesList,
    )
}

fun SubCategoriesResponse.toUiModel(): SubCategories {
    val maintenanceTasksList: MutableList<MaintenanceTasks> = mutableListOf()
    this.maintenanceTasks?.forEach {
        maintenanceTasksList.add(it.toUiModel())
    }

    return SubCategories(
        operatingCondDescription = this.operatingCondDescription,
        operatingConditionSort = this.operatingConditionSort,
        maintenanceTasks = maintenanceTasksList,
    )
}

fun MaintenanceTasksResponse.toUiModel(): MaintenanceTasks {
    return MaintenanceTasks(
        operatingCondDescription = this.operatingCondDescription,
        serviceItemDescription = this.serviceItemDescription,
        operatingConditionSort = this.operatingConditionSort,
        serviceItemSort = this.serviceItemSort,
        serviceActionSort = this.serviceActionSort,
        footNotesId = this.footNotesId,
    )
}
