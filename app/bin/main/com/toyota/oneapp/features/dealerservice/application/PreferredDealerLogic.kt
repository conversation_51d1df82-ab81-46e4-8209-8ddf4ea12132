/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application

import apptentive.com.android.util.isNotNullOrEmpty
import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.DealerSearchFilters
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.DealershipSearchParams
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.DealershipsState
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.FilterCategory
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.FilterOption
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.SearchFiltersState
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.toUiModel
import com.toyota.oneapp.features.dealerservice.domain.repository.DealerServiceRepository
import com.toyota.oneapp.features.dealerservice.presentation.preferreddealersearch.DealerSearchFilterConst
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.util.Brand
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import okhttp3.internal.toImmutableList
import javax.inject.Inject

class PreferredDealerLogic
    @Inject
    constructor(
        private val repository: DealerServiceRepository,
        applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
    ) : PreferredDealerUseCase {
        val vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()

        override fun getAllDealerships(currentLoc: LatLng): Flow<DealershipsState> {
            return flow {
                if (vehicleInfo != null) {
                    val response =
                        repository.getDealerDetailByCoordinates(
                            headerBrand = vehicleInfo.brand,
                            headerRegion = vehicleInfo.region,
                            dealershipSearchParams =
                                DealershipSearchParams(
                                    brand = listOf(getVehicleBrandName()),
                                    region = listOf(vehicleInfo.region),
                                    longitude = currentLoc.longitude,
                                    latitude = currentLoc.latitude,
                                    radius = DealerSearchFilterConst.DEFAULT_SEARCH_RADIUS,
                                ),
                        )

                    when (response) {
                        is Resource.Failure -> {
                            analyticsLogger.logEventWithParameter(
                                event = AnalyticsEventParam.VEHICLE_PAGE,
                                paramValue = AnalyticsEventParam.DEALER_SEARCH_FETCH_DEALERS_FAILURE,
                            )
                            emit(
                                DealershipsState.Error(
                                    code = response.error?.code.toString(),
                                    message = response.error?.message,
                                ),
                            )
                        }

                        is Resource.Loading -> {
                            emit(DealershipsState.Loading)
                        }

                        is Resource.Success -> {
                            analyticsLogger.logEventWithParameter(
                                event = AnalyticsEventParam.VEHICLE_PAGE,
                                paramValue = AnalyticsEventParam.DEALER_SEARCH_FETCH_DEALERS_SUCCESS,
                            )
                            response.data?.let {
                                emit(DealershipsState.Success(it.toUiModel().dealerships))
                            }
                        }
                    }
                } else {
                    emit(DealershipsState.Error())
                }
            }
        }

        private fun getVehicleBrandName(): String {
            return when (vehicleInfo?.brand) {
                Brand.TOYOTA.appBrand -> {
                    Brand.TOYOTA.brandName
                }

                Brand.LEXUS.appBrand -> {
                    Brand.LEXUS.brandName
                }

                Brand.SUBARU.appBrand -> {
                    Brand.SUBARU.brandName
                }

                else -> Brand.TOYOTA.brandName
            }
        }

        override fun getAllDealershipsFiltered(
            filterList: List<FilterCategory>?,
            query: String?,
            currentLoc: LatLng,
        ): Flow<DealershipsState> {
            return flow {
                if (vehicleInfo != null) {
                    val response =
                        repository.getAllDealerships(
                            headerBrand = vehicleInfo.brand,
                            headerRegion = vehicleInfo.region,
                            dealershipSearchParams =
                                dealershipSearchParams(
                                    filterList = filterList,
                                    query = query,
                                    currentLoc = currentLoc,
                                ),
                        )

                    when (response) {
                        is Resource.Failure -> {
                            analyticsLogger.logEventWithParameter(
                                event = AnalyticsEventParam.VEHICLE_PAGE,
                                paramValue = AnalyticsEventParam.DEALER_SEARCH_FETCH_DEALERS_FILTERED_FAILURE,
                            )
                            emit(
                                DealershipsState.Error(
                                    code = response.error?.code.toString(),
                                    message = response.error?.message,
                                ),
                            )
                        }

                        is Resource.Loading -> {
                            emit(DealershipsState.Loading)
                        }

                        is Resource.Success -> {
                            analyticsLogger.logEventWithParameter(
                                event = AnalyticsEventParam.VEHICLE_PAGE,
                                paramValue = AnalyticsEventParam.DEALER_SEARCH_FETCH_DEALERS_FILTERED_SUCCESS,
                            )
                            response.data?.let {
                                emit(DealershipsState.Success(it.toUiModel().dealerships))
                            }
                        }
                    }
                } else {
                    emit(DealershipsState.Error())
                }
            }
        }

        private fun dealershipSearchParams(
            filterList: List<FilterCategory>?,
            query: String?,
            currentLoc: LatLng,
        ): DealershipSearchParams {
            return DealershipSearchParams(
                region = if (vehicleInfo != null) listOf(vehicleInfo.region) else null,
                brand = listOf(getVehicleBrandName()),
                accessibilityOption =
                    getSelectedFilterOptionsList(
                        filterList,
                        DealerSearchFilterConst.Category.Accessibility,
                    ),
                amenity =
                    getSelectedFilterOptionsList(
                        filterList,
                        DealerSearchFilterConst.Category.Amenities,
                    ),
                dayOpen =
                    getSelectedFilterOptionsList(
                        filterList,
                        DealerSearchFilterConst.Category.ServiceHours,
                    ),
                paymentOption =
                    getSelectedFilterOptionsList(
                        filterList,
                        DealerSearchFilterConst.Category.PaymentMethods,
                    ),
                service =
                    getSelectedFilterOptionsList(
                        filterList,
                        DealerSearchFilterConst.Category.Services,
                    ),
                transportationOption =
                    getSelectedFilterOptionsList(
                        filterList,
                        DealerSearchFilterConst.Category.Transportation,
                    ),
                q = query,
                radius =
                    milesToSearchRadius(
                        getSelectedFilterOptionsList(
                            filterList,
                            DealerSearchFilterConst.Category.Distance,
                        )?.firstOrNull(),
                    ),
                smartPath =
                    if ((
                            getSelectedFilterOptionsList(
                                filterList,
                                DealerSearchFilterConst.Category.Dealer,
                            )?.isNotEmpty() == true
                        )
                    ) {
                        true
                    } else {
                        null
                    },
                longitude = currentLoc.longitude,
                latitude = currentLoc.latitude,
            )
        }

        private fun getSelectedFilterOptionsList(
            filterList: List<FilterCategory>?,
            category: DealerSearchFilterConst.Category,
        ): List<String>? {
            if (filterList.isNullOrEmpty()) return null

            return filterList.find { it.category == category }?.options?.let { option ->
                option.filter { it.selected && it.apiName.isNotNullOrEmpty() }.map { it.apiName ?: "" }
            }
        }

        private fun milesToSearchRadius(miles: String?): Double {
            if (miles == null) return DealerSearchFilterConst.DEFAULT_SEARCH_RADIUS
            return try {
                miles.toDouble() * DealerSearchFilterConst.MILES_TO_RADIUS_FACTOR
            } catch (_: NumberFormatException) {
                DealerSearchFilterConst.DEFAULT_SEARCH_RADIUS
            }
        }

        override fun getDealerSearchFilters(): Flow<SearchFiltersState> {
            return flow {
                if (vehicleInfo != null) {
                    val response =
                        repository.getDealerSearchFilters(
                            headerBrand = vehicleInfo.brand,
                        )

                    when (response) {
                        is Resource.Failure -> {
                            analyticsLogger.logEventWithParameter(
                                event = AnalyticsEventParam.VEHICLE_PAGE,
                                paramValue = AnalyticsEventParam.DEALER_SEARCH_FETCH_FILTERS_FAILURE,
                            )
                            emit(
                                SearchFiltersState.Error(
                                    code = response.error?.code.toString(),
                                    message = response.error?.message,
                                ),
                            )
                        }
                        is Resource.Loading -> {
                            emit(SearchFiltersState.Loading)
                        }
                        is Resource.Success -> {
                            analyticsLogger.logEventWithParameter(
                                event = AnalyticsEventParam.VEHICLE_PAGE,
                                paramValue = AnalyticsEventParam.DEALER_SEARCH_FETCH_FILTERS_SUCCESS,
                            )
                            emit(SearchFiltersState.Success(filters = response.data.toUiModel()))
                        }
                    }
                }
            }
        }

        override fun getFilterList(searchFilters: DealerSearchFilters): List<FilterCategory> {
            // Dealers Filter - hardcoded
            val dealerCategory =
                FilterCategory(
                    category = DealerSearchFilterConst.DealerFilter.category,
                    title = DealerSearchFilterConst.DealerFilter.header,
                    icon = DealerSearchFilterConst.DealerFilter.icon,
                    options = getFilterOptionsHardcoded(DealerSearchFilterConst.DealerFilter.options),
                )
            // Services Filter - API
            val servicesCategory =
                FilterCategory(
                    category = DealerSearchFilterConst.ServicesFilter.category,
                    title = DealerSearchFilterConst.ServicesFilter.header,
                    icon = DealerSearchFilterConst.ServicesFilter.icon,
                    options = getFilterOptionsFromApi(searchFilters.filters.services),
                )
            // ServiceHours Filter - hardcoded
            val serviceHoursCategory =
                FilterCategory(
                    category = DealerSearchFilterConst.ServiceHoursFilter.category,
                    title = DealerSearchFilterConst.ServiceHoursFilter.header,
                    icon = DealerSearchFilterConst.ServiceHoursFilter.icon,
                    options = getFilterOptionsHardcoded(DealerSearchFilterConst.ServiceHoursFilter.options),
                )
            // Distance Filter - hardcoded
            val distanceCategory =
                FilterCategory(
                    category = DealerSearchFilterConst.DistanceFilter.category,
                    title = DealerSearchFilterConst.DistanceFilter.header,
                    icon = DealerSearchFilterConst.DistanceFilter.icon,
                    options = getFilterOptionsHardcoded(DealerSearchFilterConst.DistanceFilter.options),
                )
            // Accessibility Filter - api
            val accessibilityCategory =
                FilterCategory(
                    category = DealerSearchFilterConst.AccessibilityFilter.category,
                    title = DealerSearchFilterConst.AccessibilityFilter.header,
                    icon = DealerSearchFilterConst.AccessibilityFilter.icon,
                    options = getFilterOptionsFromApi(searchFilters.filters.accessibility),
                )
            // Transportation Filter - api
            val transportationCategory =
                FilterCategory(
                    category = DealerSearchFilterConst.TransportationFilter.category,
                    title = DealerSearchFilterConst.TransportationFilter.header,
                    icon = DealerSearchFilterConst.TransportationFilter.icon,
                    options = getFilterOptionsFromApi(searchFilters.filters.transportation),
                )
            // PaymentMethods Filter - api
            val paymentMethodsCategory =
                FilterCategory(
                    category = DealerSearchFilterConst.PaymentFilter.category,
                    title = DealerSearchFilterConst.PaymentFilter.header,
                    icon = DealerSearchFilterConst.PaymentFilter.icon,
                    options = getFilterOptionsFromApi(searchFilters.filters.payments),
                )
            // Amenities Filter - api
            val amenitiesCategory =
                FilterCategory(
                    category = DealerSearchFilterConst.AmenitiesFilter.category,
                    title = DealerSearchFilterConst.AmenitiesFilter.header,
                    icon = DealerSearchFilterConst.AmenitiesFilter.icon,
                    options = getFilterOptionsFromApi(searchFilters.filters.amenities),
                )
            return listOf(
                dealerCategory,
                servicesCategory,
                serviceHoursCategory,
                distanceCategory,
                accessibilityCategory,
                transportationCategory,
                paymentMethodsCategory,
                amenitiesCategory,
            )
        }

        private fun getFilterOptionsHardcoded(filterOptionTitles: List<DealerSearchFilterConst.Option>): List<FilterOption> {
            val filterOptions = mutableListOf<FilterOption>()
            filterOptionTitles.forEach {
                filterOptions.add(FilterOption(titleRes = it.titleRes, apiName = it.apiName))
            }
            return filterOptions.toImmutableList()
        }

        private fun getFilterOptionsFromApi(filtersFromApi: List<String>?): List<FilterOption> {
            val filterOptions = mutableListOf<FilterOption>()
            filtersFromApi?.forEach {
                filterOptions.add(FilterOption(title = it))
            }
            return filterOptions.toImmutableList()
        }
    }
