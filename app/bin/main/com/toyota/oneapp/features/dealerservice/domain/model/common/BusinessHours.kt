/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.domain.model.common

import com.toyota.oneapp.features.dealerservice.dataaccess.model.common.BusinessHoursResponse

data class BusinessHours(
    val closingTime: String?,
    val dayOfWeek: String?,
    val openingTime: String?,
)

fun BusinessHoursResponse.toUiModel(): BusinessHours {
    return BusinessHours(
        closingTime = closingTime,
        dayOfWeek = dayOfWeek,
        openingTime = openingTime,
    )
}
