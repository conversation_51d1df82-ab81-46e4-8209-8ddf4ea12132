/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application

import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.dealerservice.domain.model.UIAppointmentServiceList
import com.toyota.oneapp.features.dealerservice.domain.model.UIAppointmentServices
import com.toyota.oneapp.features.dealerservice.domain.model.UpdatedChildServiceSelectionResult
import com.toyota.oneapp.features.dealerservice.domain.model.UpdatedPackageSelectionResult
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.AppointmentServiceState
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.ServiceCategoryType
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.ServiceItemType
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.ServiceTabState
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.ServiceType
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentservicepage.TabState
import com.toyota.oneapp.features.dealerservice.domain.model.toUIModel
import com.toyota.oneapp.features.dealerservice.domain.repository.DealerServiceRepository
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class AppointmentServiceLogic
    @Inject
    constructor(
        private val repository: DealerServiceRepository,
        applicationData: ApplicationData,
        private val odometerUseCase: OdometerUseCase,
        private val analyticsLogger: AnalyticsLogger,
    ) : AppointmentServiceUseCase, OdometerUseCase by odometerUseCase {
        val vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()

        override fun fetchServiceAppointmentServiceList(
            dealerId: String,
            odometer: String,
        ): Flow<AppointmentServiceState> {
            return flow {
                if (vehicleInfo != null) {
                    val response =
                        repository.fetchAppointmentServiceList(
                            dealerId = dealerId,
                            odometer = odometer,
                            vin = vehicleInfo.vin,
                            brand = vehicleInfo.brand,
                        )

                    when (response) {
                        is Resource.Success -> {
                            response.data?.let {
                                emit(
                                    AppointmentServiceState.Success(
                                        services = it.toUIModel(),
                                    ),
                                )
                            }
                        }
                        is Resource.Loading -> {
                            emit(AppointmentServiceState.Loading)
                        }
                        is Resource.Failure -> {
                            emit(
                                AppointmentServiceState.Error(
                                    code = response.error?.code.toString(),
                                    message = response.error?.message,
                                ),
                            )
                        } else -> {
                            emit(AppointmentServiceState.Error())
                        }
                    }
                } else {
                    emit(AppointmentServiceState.Error())
                }
            }
        }

        override fun getMileageList(odometerValue: String): List<Int> {
            val intervalMileage = 5000

            val odometerIntValue = odometerValue.toIntOrNull() ?: 0

            val calculatedMileage = calculateMileage(odometerValue)

            return if (odometerIntValue > intervalMileage) {
                listOf(
                    calculatedMileage - intervalMileage,
                    calculatedMileage,
                    calculatedMileage + intervalMileage,
                )
            } else {
                listOf(
                    calculatedMileage,
                    calculatedMileage + intervalMileage,
                )
            }
        }

        override fun calculateMileage(odometerValue: String): Int {
            val intervalMileage = 5000
            val odometerIntValue = odometerValue.toIntOrNull() ?: 0
            return ((odometerIntValue + intervalMileage - 1) / intervalMileage) * intervalMileage
        }

        private fun getFrsFilteredServices(
            appointmentServiceState: UIAppointmentServices?,
            selectedTabState: ServiceTabState,
        ): List<UIAppointmentServiceList> {
            return when (selectedTabState) {
                ServiceTabState.FRS_CURRENT -> appointmentServiceState?.frsCurrent ?: emptyList()
                ServiceTabState.FRS_PREV -> appointmentServiceState?.frsPrevious ?: emptyList()
                ServiceTabState.FRS_NEXT -> appointmentServiceState?.frsNext ?: emptyList()
                else -> emptyList()
            }
        }

        private fun getDrsFilteredServices(
            appointmentServiceState: UIAppointmentServices?,
            selectedTabState: ServiceTabState,
        ): List<UIAppointmentServiceList> {
            return when (selectedTabState) {
                ServiceTabState.DRS_CURRENT -> appointmentServiceState?.drsCurrent ?: emptyList()
                ServiceTabState.DRS_PREV -> appointmentServiceState?.drsPrevious ?: emptyList()
                ServiceTabState.DRS_NEXT -> appointmentServiceState?.drsNext ?: emptyList()
                else -> emptyList()
            }
        }

        override fun getFilteredServices(
            appointmentServiceState: UIAppointmentServices?,
            selectedTabState: ServiceTabState,
            serviceType: ServiceItemType,
        ): List<UIAppointmentServiceList> {
            return when (serviceType) {
                ServiceItemType.FRS -> getFrsFilteredServices(appointmentServiceState, selectedTabState)
                ServiceItemType.DRS -> getDrsFilteredServices(appointmentServiceState, selectedTabState)
            }
        }

        override fun checkFrsAndDrsServicesEmpty(
            services: UIAppointmentServices?,
            serviceType: ServiceItemType,
        ): Boolean {
            return when (serviceType) {
                ServiceItemType.FRS -> {
                    val frsCurrentEmpty = services?.frsCurrent.isNullOrEmpty()
                    val frsPreviousEmpty = services?.frsPrevious.isNullOrEmpty()
                    val frsNextEmpty = services?.frsNext.isNullOrEmpty()
                    val frsEmpty = services?.frs.isNullOrEmpty()

                    frsCurrentEmpty && frsPreviousEmpty && frsNextEmpty && frsEmpty
                }
                ServiceItemType.DRS -> {
                    val drsCurrentEmpty = services?.drsCurrent.isNullOrEmpty()
                    val drsPreviousEmpty = services?.drsPrevious.isNullOrEmpty()
                    val drsNextEmpty = services?.drsNext.isNullOrEmpty()
                    val drsEmpty = services?.drs.isNullOrEmpty()

                    drsCurrentEmpty && drsPreviousEmpty && drsNextEmpty && drsEmpty
                }
            }
        }

        override fun isOnlyServiceAvailable(
            services: UIAppointmentServices?,
            serviceType: ServiceItemType,
        ): Boolean {
            return when (serviceType) {
                ServiceItemType.FRS -> {
                    val frsCurrentEmpty = services?.frsCurrent.isNullOrEmpty()
                    val frsPreviousEmpty = services?.frsPrevious.isNullOrEmpty()
                    val frsNextEmpty = services?.frsNext.isNullOrEmpty()
                    val frsIsEmpty = services?.frs.isNullOrEmpty()

                    frsCurrentEmpty && frsPreviousEmpty && frsNextEmpty && !frsIsEmpty
                }
                ServiceItemType.DRS -> {
                    val drsCurrentEmpty = services?.drsCurrent.isNullOrEmpty()
                    val drsPreviousEmpty = services?.drsPrevious.isNullOrEmpty()
                    val drsNextEmpty = services?.drsNext.isNullOrEmpty()
                    val drsIsEmpty = services?.drs.isNullOrEmpty()

                    drsCurrentEmpty && drsPreviousEmpty && drsNextEmpty && !drsIsEmpty
                }
            }
        }

        override fun logFirebaseEvents(selectedItems: List<UIAppointmentServiceList>) {
            val frsContains = selectedItems.any { it.category == ServiceCategoryType.FRS.category }
            val drsContains = selectedItems.any { it.category == ServiceCategoryType.DRS.category }
            val grsContains = selectedItems.any { it.category == ServiceCategoryType.GRS.category }
            if (frsContains) {
                analyticsLogger.logEventWithParameter(
                    event = AnalyticsEventParam.SERVICE_SCHEDULE,
                    paramValue = AnalyticsEventParam.SUPPORTDC_REPAIRS_FACTORYRECOMMENDED,
                )
            }

            if (drsContains) {
                analyticsLogger.logEventWithParameter(
                    event = AnalyticsEventParam.SERVICE_SCHEDULE,
                    paramValue = AnalyticsEventParam.SUPPORTDC_REPAIRS_DEALERRECOMMENDED,
                )
            }

            if (grsContains) {
                analyticsLogger.logEventWithParameter(
                    event = AnalyticsEventParam.SERVICE_SCHEDULE,
                    paramValue = AnalyticsEventParam.SUPPORTDC_REPAIRS_ALLSERVICES,
                )
            }
        }

        override fun filterServices(
            query: String,
            appointmentServiceState: UIAppointmentServices?,
            tabState: TabState,
        ): List<UIAppointmentServiceList> {
            if (query.isEmpty()) {
                return emptyList()
            }

            val allServices = mutableListOf<UIAppointmentServiceList>()

            val filteredFrsServices =
                getFilteredServices(
                    appointmentServiceState,
                    tabState.serviceFrsTabState,
                    ServiceItemType.FRS,
                )
            val filteredDrsServices =
                getFilteredServices(
                    appointmentServiceState,
                    tabState.serviceDrsTabState,
                    ServiceItemType.DRS,
                )

            val frs = appointmentServiceState?.frs ?: emptyList()
            val drs = appointmentServiceState?.drs ?: emptyList()
            val grs = appointmentServiceState?.grs ?: emptyList()

            filteredFrsServices.filter {
                it.type == ServiceType.PACKAGE.value &&
                    it.serviceName.contains(
                        query,
                        ignoreCase = true,
                    )
            }.forEach { allServices.add(it) }

            filteredDrsServices.filter {
                it.type == ServiceType.PACKAGE.value &&
                    it.serviceName.contains(
                        query,
                        ignoreCase = true,
                    )
            }.forEach { allServices.add(it) }

            frs.filter { it.serviceName.contains(query, ignoreCase = true) }
                .forEach { allServices.add(it) }

            drs.filter { it.serviceName.contains(query, ignoreCase = true) }
                .forEach { allServices.add(it) }

            grs.filter { it.serviceName.contains(query, ignoreCase = true) }
                .forEach { allServices.add(it) }

            return allServices
        }

        override fun getTabState(
            selectedMileage: Int,
            calculatedMileage: Int,
            serviceItemType: ServiceItemType,
        ): ServiceTabState {
            return when (serviceItemType) {
                ServiceItemType.FRS -> {
                    when {
                        selectedMileage < calculatedMileage -> ServiceTabState.FRS_PREV
                        selectedMileage > calculatedMileage -> ServiceTabState.FRS_NEXT
                        else -> ServiceTabState.FRS_CURRENT
                    }
                }
                ServiceItemType.DRS -> {
                    when {
                        selectedMileage < calculatedMileage -> ServiceTabState.DRS_PREV
                        selectedMileage > calculatedMileage -> ServiceTabState.DRS_NEXT
                        else -> ServiceTabState.DRS_CURRENT
                    }
                }
            }
        }

        override fun handlePackageWithServiceSelection(
            packageItem: UIAppointmentServiceList,
            selectedItems: List<UIAppointmentServiceList>,
            services: UIAppointmentServices?,
        ): UpdatedPackageSelectionResult {
            val childServices =
                services?.let {
                    val allServices =
                        listOfNotNull(
                            it.drs,
                            it.frs,
                            it.grs,
                            it.drsCurrent,
                            it.drsPrevious,
                            it.drsNext,
                            it.frsCurrent,
                            it.frsPrevious,
                            it.frsNext,
                        ).flatten()

                    allServices.filter { service ->
                        service.packageId == packageItem.packageId && service.type == ServiceType.SERVICE.value
                    }
                } ?: emptyList()

            val currentSelectedPackageItem =
                if (selectedItems.contains(packageItem)) null else packageItem

            val isSelectingPackage = currentSelectedPackageItem != null

            val updatedSelectedItems =
                selectedItems.toMutableList().apply {
                    remove(packageItem)
                    removeAll(childServices)

                    if (isSelectingPackage) {
                        add(packageItem)
                        childServices.forEach { add(it.copy(price = 0.0)) }
                    }
                }

            return UpdatedPackageSelectionResult(
                updatedSelectedItems = updatedSelectedItems,
                selectedPackage = currentSelectedPackageItem,
                updatedChildServices = if (currentSelectedPackageItem != null) childServices else emptyList(),
            )
        }

        override fun handleChildServiceSelection(
            childServiceItem: UIAppointmentServiceList,
            selectedItems: List<UIAppointmentServiceList>,
            selectedChildServices: List<UIAppointmentServiceList>,
            services: UIAppointmentServices?,
            selectedPackage: UIAppointmentServiceList?,
        ): UpdatedChildServiceSelectionResult {
            val updatedChildServices = selectedChildServices.toMutableList()

            if (updatedChildServices.contains(childServiceItem)) {
                updatedChildServices.remove(childServiceItem)
            } else {
                updatedChildServices.add(childServiceItem)
            }

            val childServicesOfPackage =
                services?.let {
                    val allServices =
                        listOfNotNull(
                            it.drs,
                            it.frs,
                            it.grs,
                            it.drsCurrent,
                            it.drsPrevious,
                            it.drsNext,
                            it.frsCurrent,
                            it.frsPrevious,
                            it.frsNext,
                        ).flatten()

                    allServices.filter { service ->
                        service.packageId == selectedPackage?.packageId && service.type == ServiceType.SERVICE.value
                    }
                } ?: emptyList()

            val packageShouldRemainSelected =
                childServicesOfPackage.isNotEmpty() &&
                    childServicesOfPackage.all { updatedChildServices.contains(it) }

            val updatedSelectedItems =
                selectedItems.toMutableList().apply {
                    if (updatedChildServices.contains(childServiceItem)) {
                        add(childServiceItem)
                    } else {
                        remove(childServiceItem)
                    }

                    if (packageShouldRemainSelected && selectedPackage != null) {
                        removeAll(childServicesOfPackage)
                        add(selectedPackage)
                    } else if (selectedPackage != null && contains(selectedPackage)) {
                        remove(selectedPackage)
                        addAll(childServicesOfPackage.filter { updatedChildServices.contains(it) })
                    }
                }

            return UpdatedChildServiceSelectionResult(
                updatedSelectedItems = updatedSelectedItems,
                selectedPackage = if (packageShouldRemainSelected) selectedPackage else null,
                updatedChildServices = updatedChildServices,
            )
        }
    }
