/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.domain.model.serviceadvisorpage

import com.toyota.oneapp.features.dealerservice.domain.model.UIServiceAppointmentAdvisor

sealed interface ServiceAdvisorState {
    object Loading : ServiceAdvisorState

    data class Error(val code: String? = null, val message: String? = null) : ServiceAdvisorState

    data class Success(val advisorList: List<UIServiceAppointmentAdvisor>?) : ServiceAdvisorState
}
