/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.confirmappointment

import com.toyota.oneapp.features.core.presentation.UiEvent

sealed interface ConfirmAppointmentUiEvent : UiEvent {
    object AppointmentSchedulingInProgress : ConfirmAppointmentUiEvent

    object ErrorScheduling : ConfirmAppointmentUiEvent

    object EditAppointment : ConfirmAppointmentUiEvent

    object NavigationError : ConfirmAppointmentUiEvent

    data class AppointmentCreated(
        val route: String,
    ) : ConfirmAppointmentUiEvent
}
