/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.repository.service

import com.toyota.oneapp.features.dealerservice.dataaccess.model.AppointmentDetailsResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.AppointmentServiceListApiResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.DealerSearchFiltersResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.DeleteAppointmentResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.MaintenanceTimelineApiResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.PreferredDealerResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ScheduleAppointmentRequestBody
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceAppointmentAdvisorListApiResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceAppointmentInitializeResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceAppointmentResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceAppointmentTransportationListApiResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceAppointmentUpdateResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceShopDealershipDetailResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceShopNationWideDealershipsResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.SetPreferredDealerRequest
import com.toyota.oneapp.features.dealerservice.dataaccess.model.datetime.ServiceAppointmentAvailabilityResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.datetime.ServiceAppointmentTimeSlotsResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.servicehistory.ServiceHistoryRequest
import com.toyota.oneapp.features.dealerservice.dataaccess.model.servicehistory.ServiceHistoryResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.HeaderMap
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface DealerServiceApi {
    @GET("/oneapi/v1/preferred-dealer")
    suspend fun getPreferredDealer(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
        @Header("X-REGION") region: String,
    ): Response<PreferredDealerResponse>

    @GET("/serviceshop/oneapi/scheduler/v1/appointments")
    suspend fun getServiceAppointments(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
    ): Response<ServiceAppointmentResponse>

    @GET("/serviceshop/oneapi/scheduler/v1/appointment/initialize")
    suspend fun getServiceAppointmentInitialize(
        @Header("toyota-code") dealerCode: String,
        @Header("vin") vin: String,
        @Header("odometer") odometer: String,
        @Header("X-BRAND") brand: String?,
    ): Response<ServiceAppointmentInitializeResponse>

    @GET("/serviceshop/oneapi/v1/service-shop/oneapp/dealers/toyotaCode/{dealerId}")
    suspend fun getDealershipDetailsById(
        @Path("dealerId") dealerCode: String,
        @Header("X-BRAND") brand: String,
        @Header("X-REGION") region: String,
    ): Response<ServiceShopDealershipDetailResponse>

    @GET("/oneapi/v2/vehicle/maintenance-schedule")
    suspend fun fetchMaintenanceTimeline(
        @Header("vin") vin: String,
        @Header("lastKnownMileage") lastKnownMileage: String,
        @Header("milage-unit") mileageUnit: String,
    ): Response<MaintenanceTimelineApiResponse>

    @GET("/oneapi/v1/servicehistory/vehicle/summary")
    suspend fun fetchServiceHistory(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
    ): Response<ServiceHistoryResponse?>

    @POST("/oneapi/v1/servicehistory/vehicle/createServiceHistory")
    suspend fun createServiceHistory(
        @Header("VIN") vin: String,
        @Body body: ServiceHistoryRequest,
    ): Response<BaseResponse>

    @PUT("/oneapi/v1/servicehistory/vehicle")
    suspend fun updateServiceHistory(
        @Header("VIN") vin: String,
        @Header("serviceHistoryId") serviceHistoryId: String?,
        @Header("X-BRAND") brand: String,
        @Body body: ServiceHistoryRequest,
    ): Response<BaseResponse>

    @DELETE("/oneapi/v1/servicehistory/vehicle")
    suspend fun deleteServiceHistory(
        @Header("VIN") vin: String,
        @Header("serviceHistoryId") serviceHistoryId: String?,
        @Header("X-BRAND") brand: String,
    ): Response<BaseResponse>

    @GET("/serviceshop/oneapi/v1/service-shop/oneapp/dealers")
    suspend fun getAllDealerships(
        @Header("X-BRAND") headerBrand: String,
        @Header("X-REGION") headerRegion: String,
        @Query("accessibilityOption") accessibilityOption: List<String>?,
        @Query("amenity") amenity: List<String>?,
        @Query("dayOpen") dayOpen: List<String>?,
        @Query("sortBy") sortBy: List<String>?,
        @Query("sortDir") sortDir: List<String>?,
        @Query("limit") limit: Int?,
        @Query("offset") offset: Int?,
        @Query("paymentOption") paymentOption: List<String>?,
        @Query("q") q: String?,
        @Query("radius") radius: Double?,
        @Query("service") service: List<String>?,
        @Query("transportationOption") transportationOption: List<String>?,
        @Query("region") region: List<String>?,
        @Query("brand") brand: List<String>?,
        @Query("smartPath") smartPath: Boolean?,
        @Query("latitude") latitude: Double?,
        @Query("longitude") longitude: Double?,
    ): Response<ServiceShopNationWideDealershipsResponse>

    @GET("/serviceshop/oneapi/v1/service-shop/oneapp/dealers/map")
    suspend fun getDealerDetailByCoordinates(
        @Header("X-BRAND") headerBrand: String,
        @Header("X-REGION") headerRegion: String,
        @Query("latitude") latitude: Double?,
        @Query("longitude") longitude: Double?,
        @Query("accessibilityOption") accessibilityOption: List<String>?,
        @Query("amenity") amenity: List<String>?,
        @Query("dayOpen") dayOpen: List<String>?,
        @Query("sortBy") sortBy: List<String>?,
        @Query("sortDir") sortDir: List<String>?,
        @Query("paymentOption") paymentOption: List<String>?,
        @Query("q") q: String?,
        @Query("radius") radius: Double?,
        @Query("service") service: List<String>?,
        @Query("smartPath") smartPath: Boolean?,
        @Query("transportationOption") transportationOption: List<String>?,
        @Query("region") region: List<String>?,
        @Query("brand") brand: List<String>?,
    ): Response<ServiceShopNationWideDealershipsResponse>

    @GET("serviceshop/oneapi/scheduler/v1/filters")
    suspend fun getDealerSearchFilters(
        @Header("X-BRAND") headerBrand: String,
    ): Response<DealerSearchFiltersResponse>

    @GET("/serviceshop/oneapi/scheduler/v1/services")
    suspend fun fetchServiceAppointmentServiceList(
        @Header("dealerid") dealerId: String,
        @Header("odometer") odometerReading: String,
        @Header("vin") vin: String,
        @Header("X-BRAND") brand: String,
    ): Response<AppointmentServiceListApiResponse>

    @GET("/serviceshop/oneapi/scheduler/v1/advisors")
    suspend fun fetchServiceAppointmentAdvisorList(
        @Header("dealerid") dealerId: String,
        @Header("services") services: String,
        @Header("vin") vin: String,
        @Header("X-BRAND") brand: String,
    ): Response<ServiceAppointmentAdvisorListApiResponse>

    @GET("/serviceshop/oneapi/scheduler/v1/transportations")
    suspend fun fetchServiceAppointmentTransportationList(
        @Header("dealerid") dealerId: String,
        @Header("services") services: String,
        @Header("odometer") odometer: String,
        @Header("vin") vin: String,
        @Header("X-BRAND") brand: String,
    ): Response<ServiceAppointmentTransportationListApiResponse>

    @Headers("Content-Type: application/json")
    @PUT("/oneapi/v1/preferred-dealer")
    suspend fun updatePreferredDealer(
        @Header("VIN") vin: String,
        @Body guid: SetPreferredDealerRequest,
    ): Response<PreferredDealerResponse>

    @GET("/serviceshop/oneapi/scheduler/v1/appointment/{appointmentId}")
    suspend fun getAppointmentDetailsById(
        @Path("appointmentId") appointmentId: String,
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
    ): Response<AppointmentDetailsResponse>

    @DELETE("/serviceshop/oneapi/scheduler/v1/appointment/{appointmentId}")
    suspend fun deleteServiceAppointment(
        @Path("appointmentId") appointmentId: String,
        @Header("dealerid") dealerId: String,
        @Header("services") services: String,
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
    ): Response<DeleteAppointmentResponse>

    @POST("/serviceshop/oneapi/scheduler/v1/appointment")
    suspend fun postServiceAppointmentRequest(
        @Body requestBody: ScheduleAppointmentRequestBody,
        @Header("vin") vin: String,
        @Header("dealerid") timeDealerId: String,
        @Header("X-BRAND") brand: String,
    ): Response<ServiceAppointmentUpdateResponse>

    @PUT("/serviceshop/oneapi/scheduler/v1/appointment/{appointmentId}")
    suspend fun updateServiceAppointmentRequest(
        @Body requestBody: ScheduleAppointmentRequestBody,
        @Path("appointmentId") appointmentId: String,
        @Header("vin") vin: String,
        @Header("X-BRAND") brand: String,
    ): Response<ServiceAppointmentUpdateResponse>

    @GET("serviceshop/oneapi/scheduler/v1/timeslots/availability")
    suspend fun getServiceAppointmentAvailability(
        @HeaderMap headers: Map<String, String>,
    ): Response<ServiceAppointmentAvailabilityResponse>

    @GET("serviceshop/oneapi/scheduler/v1/timeslots")
    suspend fun getServiceAppointmentTimeSlots(
        @HeaderMap headers: Map<String, String>,
    ): Response<ServiceAppointmentTimeSlotsResponse>
}
