/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.dealerservice.domain.model.serviceadvisorpage.ServiceAdvisorState
import com.toyota.oneapp.features.dealerservice.domain.model.toUIModelList
import com.toyota.oneapp.features.dealerservice.domain.repository.DealerServiceRepository
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class
ServiceAdvisorLogic
    @Inject
    constructor(
        private val repository: DealerServiceRepository,
        applicationData: ApplicationData,
    ) : ServiceAdvisorUseCase {
        val vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()

        override fun fetchServiceAppointmentAdvisorList(
            dealerId: String,
            services: String,
        ): Flow<ServiceAdvisorState> =
            flow {
                if (vehicleInfo != null) {
                    val response =
                        repository.fetchServiceAppointmentAdvisorList(
                            dealerId = dealerId,
                            services = services,
                            vin = vehicleInfo.vin,
                            brand = vehicleInfo.brand,
                        )

                    when (response) {
                        is Resource.Success -> {
                            response.data?.let {
                                emit(
                                    ServiceAdvisorState.Success(
                                        advisorList = it.toUIModelList(),
                                    ),
                                )
                            }
                        }
                        is Resource.Loading -> {
                            emit(ServiceAdvisorState.Loading)
                        }
                        is Resource.Failure -> {
                            emit(
                                ServiceAdvisorState.Error(
                                    code = response.error?.code.toString(),
                                    message = response.error?.message,
                                ),
                            )
                        }
                    }
                } else {
                    emit(ServiceAdvisorState.Error())
                }
            }
    }
