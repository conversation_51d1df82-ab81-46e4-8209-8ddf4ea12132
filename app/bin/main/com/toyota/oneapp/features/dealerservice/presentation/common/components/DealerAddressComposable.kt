/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.common.components

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.SmartPathState
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.NationWideDealerships

@Composable
fun DealerAddressComposable(
    modifier: Modifier = Modifier,
    dealership: NationWideDealerships,
) {
    Row(modifier = modifier.fillMaxWidth()) {
        var addressFull = ""
        dealership.addresses?.first()?.let {
            addressFull =
                buildString {
                    val line1 = if (it.line1.isNotNullOrEmpty()) "${it.line1} \n" else ""
                    val line2 = if (it.line2.isNotNullOrEmpty()) "${it.line2} \n" else ""
                    this.append(line1)
                        .append(line2)
                        .append("${it.city}, ${it.state} ${it.zipCode}")
                }
        }
        OACallOut1TextView(text = addressFull, color = AppTheme.colors.tertiary03)
        Spacer(
            modifier =
                Modifier
                    .weight(1f)
                    .height(0.dp),
        )
        if (dealership.smartPath == true) {
            SmartPathImageComposable(
                smartPathState =
                    SmartPathState(
                        isSmartPath = dealership.smartPath,
                        smpLogo = dealership.spmLogo?.split(",") ?: emptyList(),
                    ),
            )
        }
    }
}
