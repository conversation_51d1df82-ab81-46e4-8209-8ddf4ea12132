/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.domain.model.servicetransportationspage

import com.toyota.oneapp.features.dealerservice.domain.model.TransportationModel
import com.toyota.oneapp.features.dealerservice.presentation.appointmentodometerpage.OdometerUnit

sealed interface ServiceTransportationsState {
    object Loading : ServiceTransportationsState

    data class Error(val code: String? = null, val message: String? = null) : ServiceTransportationsState

    data class Success(val transportationsResult: ServiceTransportationsResult) : ServiceTransportationsState
}

data class ServiceTransportationsResult(
    val transportationsList: List<TransportationModel>,
    val unit: OdometerUnit = OdometerUnit.MILES,
)
