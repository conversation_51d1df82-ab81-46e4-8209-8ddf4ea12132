/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import com.toyota.oneapp.features.core.navigation.sharedViewModel
import com.toyota.oneapp.features.dealerservice.application.navigation.DSANavArguments.ODOMETER_VALUE
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.MaintenanceSchedule
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.MaintenanceScheduleDetail
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.MaintenanceScheduleEntry
import com.toyota.oneapp.features.dealerservice.presentation.maintenanceschedule.MaintenanceScheduleDetailScreen
import com.toyota.oneapp.features.dealerservice.presentation.maintenanceschedule.MaintenanceScheduleScreen
import com.toyota.oneapp.features.dealerservice.presentation.maintenanceschedule.MaintenanceScheduleViewModel

fun NavGraphBuilder.maintenanceScheduleNavGraph(navController: NavHostController) {
    navigation(
        route = MaintenanceScheduleEntry.route,
        startDestination = "${MaintenanceSchedule.route}/{$ODOMETER_VALUE}",
    ) {
        composable(
            route = "${MaintenanceSchedule.route}/{$ODOMETER_VALUE}",
            arguments =
                listOf(
                    navArgument(ODOMETER_VALUE) { type = NavType.StringType },
                ),
        ) { entry ->
            val maintenanceScheduleViewModel =
                entry.sharedViewModel<MaintenanceScheduleViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            MaintenanceScheduleScreen(
                maintenanceScheduleViewModel = maintenanceScheduleViewModel,
                navController = navController,
            )
        }
        composable(
            route = MaintenanceScheduleDetail.route,
        ) { entry ->
            val maintenanceScheduleViewModel =
                entry.sharedViewModel<MaintenanceScheduleViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            MaintenanceScheduleDetailScreen(
                maintenanceScheduleViewModel = maintenanceScheduleViewModel,
                navController = navController,
            )
        }
    }
}
