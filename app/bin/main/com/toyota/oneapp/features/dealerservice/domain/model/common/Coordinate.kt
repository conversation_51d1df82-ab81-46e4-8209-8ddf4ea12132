/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.domain.model.common

import com.toyota.oneapp.features.dealerservice.dataaccess.model.CoordinateResponse

data class Coordinate(
    val latitude: Double?,
    val longitude: Double?,
)

fun CoordinateResponse?.toUiModel(): Coordinate? {
    return this?.let {
        Coordinate(
            latitude = this.latitude,
            longitude = this.longitude,
        )
    }
}
