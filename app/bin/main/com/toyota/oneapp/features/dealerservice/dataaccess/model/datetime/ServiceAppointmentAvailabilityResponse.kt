/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model.datetime

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.dealerservice.dataaccess.model.StatusResponse

data class ServiceAppointmentAvailabilityResponse(
    @SerializedName("days") val days: List<DaysResponse>,
    @SerializedName("status") val status: StatusResponse,
)

data class DaysResponse(
    @SerializedName("date") val date: String,
    @SerializedName("slotsAvailable") val slotsAvailable: Int,
    @SerializedName("schedulingAvailable") val schedulingAvailable: <PERSON><PERSON><PERSON>,
)
