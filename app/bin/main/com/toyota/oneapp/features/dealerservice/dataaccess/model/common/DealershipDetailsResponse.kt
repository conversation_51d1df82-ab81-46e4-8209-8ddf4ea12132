/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model.common

import com.google.gson.annotations.SerializedName

data class DealershipDetailsResponse(
    @SerializedName("accessibilityOptions") val accessibilityOptions: List<String>?,
    @SerializedName("addresses") val addresses: List<AddressResponse>?,
    @SerializedName("amenities") val amenities: List<String>?,
    @SerializedName("brand") val brand: String?,
    @SerializedName("businessHours") val businessHours: List<BusinessHoursResponse>?,
    @SerializedName("dealershipName") val dealershipName: String?,
    @SerializedName("emails") val emails: List<String>?,
    @SerializedName("paymentOptions") val paymentOptions: List<String>?,
    @SerializedName("phoneNumbers") val phoneNumbers: List<PhoneNumberResponse>?,
    @SerializedName("services") val services: List<String>?,
    @SerializedName("smartPath") val smartPath: Boolean?,
    @SerializedName("spmLogo") val spmLogo: String?,
    @SerializedName("toyotaCode") val toyotaCode: String?,
    @SerializedName("transportationOptions") val transportationOptions: List<String>?,
    @SerializedName("website") val website: String?,
)
