/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application

import com.toyota.oneapp.features.dealerservice.domain.model.dealerdetails.DealerDetailState
import com.toyota.oneapp.features.dealerservice.domain.model.dealerdetails.UpdatePreferredDealerState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.PreferredDealerState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.ServiceAppointmentInitializeState
import kotlinx.coroutines.flow.Flow

interface DealerDetailsUseCase {
    fun getPreferredDealer(): Flow<PreferredDealerState>

    fun getServiceAppointmentInitialize(dealerCode: String): Flow<ServiceAppointmentInitializeState>

    fun getDealerById(dealerCode: String): Flow<DealerDetailState>

    fun updatePreferredDealer(dealerCode: String): Flow<UpdatePreferredDealerState>
}
