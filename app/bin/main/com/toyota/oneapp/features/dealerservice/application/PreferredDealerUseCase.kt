/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.DealerSearchFilters
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.DealershipsState
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.FilterCategory
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.SearchFiltersState
import kotlinx.coroutines.flow.Flow

interface PreferredDealerUseCase {
    fun getAllDealerships(currentLoc: LatLng): Flow<DealershipsState>

    fun getAllDealershipsFiltered(
        filterList: List<FilterCategory>?,
        query: String?,
        currentLoc: LatLng,
    ): Flow<DealershipsState>

    fun getDealerSearchFilters(): Flow<SearchFiltersState>

    fun getFilterList(searchFilters: DealerSearchFilters): List<FilterCategory>
}
