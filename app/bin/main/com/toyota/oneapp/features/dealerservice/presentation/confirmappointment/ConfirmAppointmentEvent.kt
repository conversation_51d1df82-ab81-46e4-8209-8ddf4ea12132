/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.confirmappointment

sealed interface ConfirmAppointmentEvent {
    object EditServices : ConfirmAppointmentEvent

    object EditServiceAdvisor : ConfirmAppointmentEvent

    object EditTransportation : ConfirmAppointmentEvent

    object EditDateTime : ConfirmAppointmentEvent

    data class EditConfirmation(val editCategory: EditConfirmationCategory) : ConfirmAppointmentEvent

    object ConfirmAppointment : ConfirmAppointmentEvent

    data class AdditionalComments(val comment: String) : ConfirmAppointmentEvent
}

enum class EditConfirmationCategory {
    Services,
    Advisor,
    Transportation,
}
