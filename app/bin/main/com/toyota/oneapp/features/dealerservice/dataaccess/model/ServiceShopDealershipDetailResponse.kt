/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.dealerservice.dataaccess.model.common.DealershipDetailsResponse

data class ServiceShopDealershipDetailResponse(
    @SerializedName("payload") val payload: DealershipResponse,
    @SerializedName("status") val status: StatusResponse,
)

data class DealershipResponse(
    @SerializedName("dealership")
    val dealershipDetailResponse: DealershipDetailsResponse,
)

data class CoordinateResponse(
    @SerializedName("latitude") val latitude: Double?,
    @SerializedName("longitude") val longitude: Double?,
)
