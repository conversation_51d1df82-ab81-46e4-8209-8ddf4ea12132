/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.maintenanceschedule.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.PreferredDealerState
import com.toyota.oneapp.features.dealerservice.domain.model.landingpage.ServiceAppointmentInitializeState
import com.toyota.oneapp.features.dealerservice.presentation.maintenanceschedule.MaintenanceScheduleEvent
import com.toyota.oneapp.features.dealerservice.presentation.maintenanceschedule.MaintenanceScheduleScreenState
import com.toyota.oneapp.features.dealerservice.presentation.preferreddealersearch.DealerSearchFilterConst.FILTER_ANIMATION_TIME

@Composable
fun MaintenanceScheduleBottomBarComposable(
    maintenanceScheduleState: MaintenanceScheduleScreenState,
    onEvent: (MaintenanceScheduleEvent) -> Unit,
) {
    AnimatedVisibility(
        visible = maintenanceScheduleState.preferredDealerState is PreferredDealerState.Success,
        enter =
            fadeIn(animationSpec = tween(FILTER_ANIMATION_TIME)) +
                expandVertically(
                    animationSpec = tween(FILTER_ANIMATION_TIME),
                ),
    ) {
        val scheduleAvailable =
            maintenanceScheduleState.serviceAppointmentInitializeState is
                ServiceAppointmentInitializeState.Success &&
                maintenanceScheduleState.serviceAppointmentInitializeState
                    .serviceAppointmentInitialize.schedulingAvailable == true
        Row(
            modifier =
                Modifier
                    .background(color = AppTheme.colors.tertiary15)
                    .fillMaxWidth()
                    .height(100.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            PrimaryButton02(
                modifier = Modifier,
                text =
                    stringResource(
                        id = if (scheduleAvailable) R.string.dsa_make_appointment else R.string.dsa_call_dealer,
                    ),
                click = { onEvent.invoke(MaintenanceScheduleEvent.BottomBarButtonClicked) },
            )
        }
    }
}
