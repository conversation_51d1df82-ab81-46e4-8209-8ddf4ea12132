/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.repository

import com.toyota.oneapp.features.dealerservice.dataaccess.model.AppointmentDetailsResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.AppointmentServiceListApiResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.DealerSearchFiltersResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.DeleteAppointmentResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.MaintenanceTimelineApiResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.PreferredDealerResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ScheduleAppointmentRequestBody
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceAppointmentAdvisorListApiResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceAppointmentInitializeResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceAppointmentResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceAppointmentTransportationListApiResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceAppointmentUpdateResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceShopDealershipDetailResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.ServiceShopNationWideDealershipsResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.SetPreferredDealerRequest
import com.toyota.oneapp.features.dealerservice.dataaccess.model.datetime.ServiceAppointmentAvailabilityResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.model.datetime.ServiceAppointmentTimeSlotsResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.repository.service.DealerServiceApi
import com.toyota.oneapp.features.dealerservice.domain.model.preferreddealersearch.DealershipSearchParams
import com.toyota.oneapp.features.dealerservice.domain.repository.DealerServiceRepository
import com.toyota.oneapp.model.dashboard.NotificationHistoryResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.NotificationHistoryRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class DealerServiceDefaultRepo
    @Inject
    constructor(
        private val dealerServiceApi: DealerServiceApi,
        private val notificationHistoryRepository: NotificationHistoryRepository,
        private val preferenceModel: OneAppPreferenceModel,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser = errorParser, ioContext = ioContext),
        DealerServiceRepository {
        private var cachedAppointmentDetailsResponse: Resource<AppointmentDetailsResponse?>? = null

        override suspend fun getPreferredDealer(
            vin: String,
            brand: String,
            region: String,
        ): Resource<PreferredDealerResponse?> =
            makeApiCall {
                dealerServiceApi.getPreferredDealer(
                    vin = vin,
                    brand = brand,
                    region = region,
                )
            }

        override suspend fun getServiceAppointments(
            vin: String,
            brand: String,
        ): Resource<ServiceAppointmentResponse?> =
            makeApiCall {
                dealerServiceApi.getServiceAppointments(
                    vin = vin,
                    brand = brand,
                )
            }

        override suspend fun getServiceAppointmentInitialize(
            dealerCode: String,
            vin: String,
            odometer: String,
            brand: String,
        ): Resource<ServiceAppointmentInitializeResponse?> =
            makeApiCall {
                dealerServiceApi.getServiceAppointmentInitialize(
                    dealerCode = dealerCode,
                    vin = vin,
                    odometer = odometer,
                    brand = brand,
                )
            }

        override suspend fun getDealershipDetailsById(
            dealerCode: String,
            brand: String,
            region: String,
        ): Resource<ServiceShopDealershipDetailResponse?> =
            makeApiCall {
                dealerServiceApi.getDealershipDetailsById(
                    dealerCode = dealerCode,
                    brand = brand,
                    region = region,
                )
            }

        override suspend fun fetchMaintenanceTimeline(
            vin: String,
            lastKnownMileage: String,
            mileageUnit: String,
        ): Resource<MaintenanceTimelineApiResponse?> =
            makeApiCall {
                dealerServiceApi.fetchMaintenanceTimeline(
                    vin = vin,
                    lastKnownMileage = lastKnownMileage,
                    mileageUnit = mileageUnit,
                )
            }

        override suspend fun getAllDealerships(
            headerBrand: String,
            headerRegion: String,
            sortBy: List<String>?,
            sortDir: List<String>?,
            limit: Int?,
            offset: Int?,
            dealershipSearchParams: DealershipSearchParams,
        ): Resource<ServiceShopNationWideDealershipsResponse?> =
            makeApiCall {
                dealerServiceApi.getAllDealerships(
                    headerBrand = headerBrand,
                    headerRegion = headerRegion,
                    accessibilityOption = dealershipSearchParams.accessibilityOption,
                    amenity = dealershipSearchParams.amenity,
                    dayOpen = dealershipSearchParams.dayOpen,
                    sortBy = sortBy,
                    sortDir = sortDir,
                    limit = limit,
                    offset = offset,
                    paymentOption = dealershipSearchParams.paymentOption,
                    q = dealershipSearchParams.q,
                    radius = dealershipSearchParams.radius,
                    service = dealershipSearchParams.service,
                    transportationOption = dealershipSearchParams.transportationOption,
                    region = dealershipSearchParams.region,
                    brand = dealershipSearchParams.brand,
                    smartPath = dealershipSearchParams.smartPath,
                    latitude = dealershipSearchParams.latitude,
                    longitude = dealershipSearchParams.longitude,
                )
            }

        override suspend fun getDealerDetailByCoordinates(
            headerBrand: String,
            headerRegion: String,
            dayOpen: List<String>?,
            sortBy: List<String>?,
            sortDir: List<String>?,
            dealershipSearchParams: DealershipSearchParams,
        ): Resource<ServiceShopNationWideDealershipsResponse?> =
            makeApiCall {
                dealerServiceApi.getDealerDetailByCoordinates(
                    headerBrand = headerBrand,
                    headerRegion = headerRegion,
                    latitude = dealershipSearchParams.latitude,
                    longitude = dealershipSearchParams.longitude,
                    accessibilityOption = dealershipSearchParams.accessibilityOption,
                    amenity = dealershipSearchParams.amenity,
                    dayOpen = dayOpen,
                    sortBy = sortBy,
                    sortDir = sortDir,
                    paymentOption = dealershipSearchParams.paymentOption,
                    q = dealershipSearchParams.q,
                    radius = dealershipSearchParams.radius,
                    service = dealershipSearchParams.service,
                    smartPath = dealershipSearchParams.smartPath,
                    transportationOption = dealershipSearchParams.transportationOption,
                    region = dealershipSearchParams.region,
                    brand = dealershipSearchParams.brand,
                )
            }

        override suspend fun getDealerSearchFilters(headerBrand: String): Resource<DealerSearchFiltersResponse?> =
            makeApiCall {
                dealerServiceApi.getDealerSearchFilters(headerBrand = headerBrand)
            }

        override suspend fun fetchAppointmentServiceList(
            dealerId: String,
            odometer: String,
            vin: String,
            brand: String,
        ): Resource<AppointmentServiceListApiResponse?> =
            makeApiCall {
                dealerServiceApi.fetchServiceAppointmentServiceList(
                    dealerId = dealerId,
                    odometerReading = odometer,
                    vin = vin,
                    brand = brand,
                )
            }

        override suspend fun fetchServiceAppointmentAdvisorList(
            dealerId: String,
            services: String,
            vin: String,
            brand: String,
        ): Resource<ServiceAppointmentAdvisorListApiResponse?> =
            makeApiCall {
                dealerServiceApi.fetchServiceAppointmentAdvisorList(
                    dealerId = dealerId,
                    services = services,
                    vin = vin,
                    brand = brand,
                )
            }

        override suspend fun fetchServiceAppointmentTransportationList(
            dealerId: String,
            services: String,
            odometer: String,
            vin: String,
            brand: String,
        ): Resource<ServiceAppointmentTransportationListApiResponse?> =
            makeApiCall {
                dealerServiceApi.fetchServiceAppointmentTransportationList(
                    dealerId = dealerId,
                    services = services,
                    odometer = odometer,
                    vin = vin,
                    brand = brand,
                )
            }

        override suspend fun updatePreferredDealer(
            vin: String,
            body: SetPreferredDealerRequest,
        ): Resource<PreferredDealerResponse?> =
            makeApiCall {
                dealerServiceApi.updatePreferredDealer(vin = vin, guid = body)
            }

        override suspend fun getAppointmentDetailsById(
            appointmentId: String,
            vin: String,
            brand: String,
            noCache: Boolean,
        ): Resource<AppointmentDetailsResponse?> {
            if (!noCache && cachedAppointmentDetailsResponse != null) {
                return cachedAppointmentDetailsResponse!!
            }
            val response =
                makeApiCall {
                    dealerServiceApi.getAppointmentDetailsById(
                        appointmentId = appointmentId,
                        vin = vin,
                        brand = brand,
                    )
                }
            cachedAppointmentDetailsResponse = response
            return response
        }

        override suspend fun deleteServiceAppointment(
            appointmentId: String,
            dealerId: String,
            services: String,
            vin: String,
            brand: String,
        ): Resource<DeleteAppointmentResponse?> =
            makeApiCall {
                dealerServiceApi.deleteServiceAppointment(
                    appointmentId = appointmentId,
                    dealerId = dealerId,
                    services = services,
                    vin = vin,
                    brand = brand,
                )
            }

        override suspend fun postServiceAppointmentRequest(
            requestBody: ScheduleAppointmentRequestBody,
            vin: String,
            appointmentId: String,
            brand: String,
        ): Resource<ServiceAppointmentUpdateResponse?> =
            makeApiCall {
                dealerServiceApi.postServiceAppointmentRequest(
                    requestBody = requestBody,
                    vin = vin,
                    timeDealerId = appointmentId,
                    brand = brand,
                )
            }

        override suspend fun updateServiceAppointmentRequest(
            requestBody: ScheduleAppointmentRequestBody,
            appointmentId: String,
            vin: String,
            brand: String,
        ): Resource<ServiceAppointmentUpdateResponse?> =
            makeApiCall {
                dealerServiceApi.updateServiceAppointmentRequest(
                    appointmentId = appointmentId,
                    vin = vin,
                    brand = brand,
                    requestBody = requestBody,
                )
            }

        override suspend fun getAvailability(
            appointmentId: String,
            services: String,
            advisorId: String,
            transportationId: String,
            odometer: String,
            startAndEndDate: Pair<String, String>,
            vehicleDetail: Pair<String, String>,
        ): Resource<ServiceAppointmentAvailabilityResponse?> {
            val headerMap =
                mapOf(
                    "dealerId" to appointmentId,
                    "services" to services,
                    "vin" to vehicleDetail.first,
                    "startdate" to startAndEndDate.first,
                    "enddate" to startAndEndDate.second,
                    "advisorid" to advisorId,
                    "transportid" to transportationId,
                    "odometer" to odometer,
                    "X-BRAND" to vehicleDetail.second,
                )
            return makeApiCall {
                dealerServiceApi.getServiceAppointmentAvailability(
                    headers = headerMap,
                )
            }
        }

        override suspend fun getTimeSlots(
            appointmentId: String,
            services: String,
            startDate: String,
            transportationId: String,
            advisorId: String,
            odometer: String,
            vehicleDetail: Pair<String, String>,
        ): Resource<ServiceAppointmentTimeSlotsResponse?> {
            val headerMap =
                mapOf(
                    "dealerId" to appointmentId,
                    "services" to services,
                    "vin" to vehicleDetail.first,
                    "startdate" to startDate,
                    "advisorid" to advisorId,
                    "transportid" to transportationId,
                    "odometer" to odometer,
                    "X-BRAND" to vehicleDetail.second,
                )

            return makeApiCall {
                dealerServiceApi.getServiceAppointmentTimeSlots(
                    headers = headerMap,
                )
            }
        }

        override suspend fun clearCache() {
            cachedAppointmentDetailsResponse = null
        }

        override suspend fun getNotificationHistory(): Resource<NotificationHistoryResponse?> {
            return notificationHistoryRepository.sendNotificationHistoryRequest(preferenceModel.getGuid())
        }
    }
