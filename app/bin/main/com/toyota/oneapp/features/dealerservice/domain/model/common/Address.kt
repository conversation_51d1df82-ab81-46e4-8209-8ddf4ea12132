/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.domain.model.common

import com.toyota.oneapp.features.dealerservice.dataaccess.model.common.AddressResponse

data class Address(
    val line1: String?,
    val line2: String?,
    val coordinate: Coordinate?,
    val country: String?,
    val city: String?,
    val state: String?,
    val timeZone: String?,
    val zipCode: String?,
)

fun AddressResponse.toUiModel(): Address {
    return Address(
        line1 = line1,
        line2 = line2,
        coordinate = coordinate.toUiModel(),
        country = country,
        city = city,
        state = state,
        timeZone = timeZone,
        zipCode = zipCode,
    )
}
