/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.repository

import com.toyota.oneapp.features.dealerservice.dataaccess.model.servicehistory.ServiceHistoryRequest
import com.toyota.oneapp.features.dealerservice.dataaccess.model.servicehistory.ServiceHistoryResponse
import com.toyota.oneapp.features.dealerservice.dataaccess.repository.service.DealerServiceApi
import com.toyota.oneapp.features.dealerservice.domain.repository.ServiceHistoryRepository
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.CoroutineContext

@Singleton
class ServiceHistoryDefaultRepo
    @Inject
    constructor(
        private val dealerServiceApi: DealerServiceApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser = errorParser, ioContext = ioContext), ServiceHistoryRepository {
        private var cachedServiceHistoryResponse: Resource<ServiceHistoryResponse?>? = null

        override suspend fun fetchServiceHistory(
            vin: String,
            brand: String,
            getUpdatedData: Boolean,
        ): Resource<ServiceHistoryResponse?> {
            if (!getUpdatedData && cachedServiceHistoryResponse != null) {
                return cachedServiceHistoryResponse!!
            }
            val response =
                makeApiCall {
                    dealerServiceApi.fetchServiceHistory(vin, brand)
                }
            cachedServiceHistoryResponse = response
            return response
        }

        override suspend fun createServiceHistory(
            vin: String,
            body: ServiceHistoryRequest,
        ): Resource<BaseResponse?> {
            return makeApiCall {
                dealerServiceApi.createServiceHistory(vin, body)
            }
        }

        override suspend fun updateServiceHistory(
            vin: String,
            serviceHistoryId: String?,
            brand: String,
            body: ServiceHistoryRequest,
        ): Resource<BaseResponse?> {
            return makeApiCall {
                dealerServiceApi.updateServiceHistory(vin, serviceHistoryId, brand, body)
            }
        }

        override suspend fun deleteServiceHistory(
            vin: String,
            serviceHistoryId: String?,
            brand: String,
        ): Resource<BaseResponse?> {
            return makeApiCall {
                dealerServiceApi.deleteServiceHistory(vin, serviceHistoryId, brand)
            }
        }

        override suspend fun clearCache() {
            cachedServiceHistoryResponse = null
        }
    }
