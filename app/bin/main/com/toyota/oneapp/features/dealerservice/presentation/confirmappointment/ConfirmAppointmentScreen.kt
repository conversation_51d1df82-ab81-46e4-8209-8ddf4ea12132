/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.confirmappointment

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OAButtonTextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OACaption1TextView
import com.toyota.oneapp.features.core.composable.OAHeadline1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dealerservice.presentation.appointmentodometerpage.OdometerUnit
import com.toyota.oneapp.features.dealerservice.presentation.common.components.OATopAppBar
import com.toyota.oneapp.features.dealerservice.presentation.common.components.OdometerDetailComposable
import com.toyota.oneapp.features.dealerservice.presentation.confirmappointment.components.AppointmentDetailItemComposable
import com.toyota.oneapp.features.dealerservice.presentation.landingpage.components.getIconContentDescription
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

const val ANIMATION_TIME = 200

@Composable
fun ConfirmAppointmentScreen(navController: NavHostController) {
    val confirmAppointmentViewModel = hiltViewModel<ConfirmAppointmentViewModel>()
    val confirmAppointmentUiState = confirmAppointmentViewModel.state.collectAsState().value
    val onEvent = confirmAppointmentViewModel::onEvent

    val additionalCommentsFullScreen =
        remember {
            mutableStateOf(false)
        }

    val focusManager = LocalFocusManager.current
    val onBack = {
        if (additionalCommentsFullScreen.value) {
            focusManager.clearFocus(true)
            additionalCommentsFullScreen.value = false
        } else {
            navController.navigateUp()
        }
    }
    BackHandler {
        onBack.invoke()
    }
    val sheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            skipHalfExpanded = true,
            confirmValueChange = { newState ->
                newState != ModalBottomSheetValue.Hidden
            },
        )

    HandleUiEvents(
        additionalCommentsFullScreen = additionalCommentsFullScreen,
        navController = navController,
        sheetState = sheetState,
    )

    ModalBottomSheetLayout(
        sheetState = sheetState,
        sheetShape = RoundedCornerShape(10.dp),
        sheetContent = {
            when (val state = confirmAppointmentUiState.bottomSheetUiState) {
                is BottomSheetUiState.AppointmentError -> {
                    ConfirmAppointmentErrorBottomSheetContent(errorMsg = state.errorMsg ?: "")
                }

                BottomSheetUiState.ConfirmingAppointment -> {
                    ConfirmAppointmentBottomSheetContent()
                }

                is BottomSheetUiState.ConfirmEdit -> {
                    EditAppointmentBottomSheetConfirmation(
                        confirmEditType = state.editConfirmationCategory,
                        sheetState = sheetState,
                        onEvent = onEvent,
                    )
                }

                BottomSheetUiState.AppointmentSuccess -> {
                    ConfirmAppointmentSuccessBottomSheetContent()
                }
            }
        },
    ) {
        Scaffold(
            modifier = Modifier.background(AppTheme.colors.tertiary15),
            topBar = {
                OATopAppBar(
                    title =
                        stringResource(
                            id =
                                if (confirmAppointmentViewModel.isEditingAppointment()) {
                                    R.string.dsa_edit_appointment
                                } else {
                                    R.string.dsa_make_appointment
                                },
                        ),
                    titleTestTagId = AccessibilityId.ID_DSA_CONFIRM_APPOINTMENT_NAVIGATION_BAR,
                    onBackTestTagId = AccessibilityId.ID_DSA_CONFIRM_APPOINTMENT_BACK_CTA,
                    onButtonPressed = {
                        onBack.invoke()
                    },
                    drawableRes = if (additionalCommentsFullScreen.value) R.drawable.ic_close else R.drawable.ic_back_arrow,
                )
            },
            bottomBar = {
                ConfirmAppointmentBottomBar(
                    focusManager = focusManager,
                    additionalCommentsFullScreen = additionalCommentsFullScreen,
                    onEvent = onEvent,
                )
            },
        ) { paddingValues ->
            ConfirmAppointmentScreenContent(
                confirmAppointmentUiState = confirmAppointmentUiState,
                onEvent = onEvent,
                additionalCommentsFullScreen = additionalCommentsFullScreen,
                paddingValues = paddingValues,
            )
        }
    }
}

@Composable
private fun HandleUiEvents(
    additionalCommentsFullScreen: MutableState<Boolean>,
    navController: NavHostController,
    sheetState: ModalBottomSheetState,
) {
    val showProgress by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    if (showProgress) {
        ShowProgressIndicator(dialogState = true)
    }
    val confirmAppointmentViewModel = hiltViewModel<ConfirmAppointmentViewModel>()

    ConfirmAppointmentScreenUiEventHandler(
        uiEvent = confirmAppointmentViewModel.uiEvent,
        coroutineScope = coroutineScope,
        additionalCommentsFullScreen = additionalCommentsFullScreen,
        navController = navController,
        sheetState = sheetState,
    )
}

@Composable
fun ConfirmAppointmentBottomBar(
    focusManager: FocusManager,
    additionalCommentsFullScreen: MutableState<Boolean>,
    onEvent: (ConfirmAppointmentEvent) -> Unit,
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    Row(
        modifier =
            Modifier
                .background(color = AppTheme.colors.tertiary12)
                .fillMaxWidth()
                .height(75.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        PrimaryButton02(
            modifier = Modifier,
            text = stringResource(id = R.string.Appointment_confirm_appointment),
            click = {
                focusManager.clearFocus(true)
                keyboardController?.hide()
                additionalCommentsFullScreen.value = false
                onEvent(ConfirmAppointmentEvent.ConfirmAppointment)
            },
        )
    }
}

@Composable
fun ConfirmAppointmentBottomSheetContent() {
    Column(
        modifier =
            Modifier
                .clickable(enabled = false) {}
                .fillMaxHeight(.4f)
                .fillMaxWidth()
                .scrollable(state = rememberScrollState(), orientation = Orientation.Vertical)
                .background(AppTheme.colors.tile03)
                .padding(horizontal = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(24.dp))
        LoadingIcon()
        Spacer(modifier = Modifier.height(16.dp))
        OAHeadline1TextView(
            text = stringResource(id = R.string.dsa_confirming_appointment),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary03,
        )
        Spacer(modifier = Modifier.height(8.dp))
        OACallOut1TextView(
            text = stringResource(id = R.string.dsa_confirming_appointment_subtitle),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary05,
        )
    }
}

@Composable
fun ConfirmAppointmentErrorBottomSheetContent(errorMsg: String) {
    Column(
        modifier =
            Modifier
                .clickable(enabled = false) {}
                .fillMaxHeight(.4f)
                .fillMaxWidth()
                .background(AppTheme.colors.tile03)
                .padding(horizontal = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(24.dp))
        ErrorIcon()
        Spacer(modifier = Modifier.height(16.dp))
        OAHeadline1TextView(
            text = stringResource(id = R.string.dsa_confirm_appointment_error_msg),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary03,
        )
        Spacer(modifier = Modifier.height(8.dp))
        OACallOut1TextView(
            text = errorMsg,
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary05,
        )
    }
}

@Composable
fun EditAppointmentBottomSheetConfirmation(
    confirmEditType: EditConfirmationCategory,
    sheetState: ModalBottomSheetState,
    onEvent: (ConfirmAppointmentEvent) -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()
    Column(
        modifier =
            Modifier
                .clickable(enabled = false) {}
                .fillMaxHeight(.6f)
                .fillMaxWidth()
                .background(AppTheme.colors.tile03)
                .padding(horizontal = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(24.dp))
        EditIcon()
        Spacer(modifier = Modifier.height(16.dp))
        OAHeadline1TextView(
            text = stringResource(id = R.string.dsa_edit_appointment_continue),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary03,
        )
        Spacer(modifier = Modifier.height(16.dp))
        OACallOut1TextView(
            text = getEditTypeSubtext(confirmEditType),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary05,
        )
        Spacer(modifier = Modifier.height(32.dp))
        OAButtonTextView(
            modifier =
                Modifier
                    .padding(horizontal = 13.dp, vertical = 16.dp)
                    .clickable {
                        coroutineScope
                            .launch {
                                if (sheetState.isVisible) {
                                    sheetState.hide()
                                }
                            }
                    },
            text = stringResource(id = R.string.not_now),
            color = AppTheme.colors.tertiary03,
        )
        Spacer(modifier = Modifier.height(16.dp))
        ContinueEditButton(coroutineScope, sheetState, confirmEditType, onEvent)
    }
}

@Composable
private fun getEditTypeSubtext(confirmEditType: EditConfirmationCategory): String {
    val subtext =
        when (confirmEditType) {
            EditConfirmationCategory.Services ->
                stringResource(
                    id = R.string.dsa_edit_appointment_services_subheading,
                )

            EditConfirmationCategory.Advisor ->
                stringResource(
                    id = R.string.dsa_edit_appointment_time_subheading,
                    stringResource(
                        id = R.string.advisor_title,
                    ),
                )

            EditConfirmationCategory.Transportation ->
                stringResource(
                    id = R.string.dsa_edit_appointment_time_subheading,
                    stringResource(
                        id = R.string.dsa_transportation,
                    ),
                )
        }
    return subtext
}

@Composable
private fun ContinueEditButton(
    coroutineScope: CoroutineScope,
    sheetState: ModalBottomSheetState,
    confirmEditType: EditConfirmationCategory,
    onEvent: (ConfirmAppointmentEvent) -> Unit,
) {
    PrimaryButton02(
        modifier = Modifier,
        text = stringResource(id = R.string.continue_button),
        click = {
            coroutineScope.launch {
                sheetState.hide()
            }
            when (confirmEditType) {
                EditConfirmationCategory.Services -> {
                    onEvent.invoke(ConfirmAppointmentEvent.EditServices)
                }

                EditConfirmationCategory.Advisor -> {
                    onEvent.invoke(ConfirmAppointmentEvent.EditServiceAdvisor)
                }

                EditConfirmationCategory.Transportation -> {
                    onEvent.invoke(ConfirmAppointmentEvent.EditTransportation)
                }
            }
        },
    )
}

@Composable
fun LoadingIcon() {
    Box(
        modifier =
            Modifier
                .width(48.dp),
    ) {
        Surface(
            shape = CircleShape,
            color = AppTheme.colors.secondary02,
            modifier =
                Modifier
                    .size(size = 48.dp)
                    .align(Alignment.CenterStart),
        ) {
            Image(
                modifier =
                    Modifier
                        .padding(
                            all = 12.dp,
                        ),
                painter = painterResource(id = R.drawable.ic_loading),
                colorFilter = ColorFilter.tint(AppTheme.colors.secondary01),
                contentDescription = getIconContentDescription(icon = R.drawable.ic_loading),
            )
        }
    }
}

@Composable
fun ErrorIcon() {
    Box(
        modifier =
            Modifier
                .width(48.dp),
    ) {
        Surface(
            shape = CircleShape,
            color = AppTheme.colors.primaryLight02,
            modifier =
                Modifier
                    .size(size = 48.dp)
                    .align(Alignment.CenterStart),
        ) {
            Image(
                modifier =
                    Modifier
                        .padding(
                            all = 12.dp,
                        ),
                painter = painterResource(id = R.drawable.ic_alert_no_background),
                colorFilter = ColorFilter.tint(AppTheme.colors.error01),
                contentDescription = getIconContentDescription(icon = R.drawable.ic_alert_no_background),
            )
        }
    }
}

@Composable
fun EditIcon() {
    Box(
        modifier =
            Modifier
                .width(48.dp),
    ) {
        Surface(
            shape = CircleShape,
            color = AppTheme.colors.primaryLight02,
            modifier =
                Modifier
                    .size(size = 48.dp)
                    .align(Alignment.CenterStart),
        ) {
            Image(
                modifier =
                    Modifier
                        .padding(
                            all = 12.dp,
                        ),
                painter = painterResource(id = R.drawable.ic_schedule),
                colorFilter = ColorFilter.tint(AppTheme.colors.error01),
                contentDescription = getIconContentDescription(icon = R.drawable.ic_schedule),
            )
        }
    }
}

@Composable
fun SuccessIcon() {
    Box(
        modifier =
            Modifier
                .width(48.dp),
    ) {
        Surface(
            shape = CircleShape,
            color = AppTheme.colors.primaryLight02,
            modifier =
                Modifier
                    .size(size = 48.dp)
                    .align(Alignment.CenterStart),
        ) {
            Image(
                modifier =
                    Modifier
                        .padding(
                            all = 12.dp,
                        ),
                painter = painterResource(id = R.drawable.ic_check_mark_solid_green),
                colorFilter = ColorFilter.tint(AppTheme.colors.success01),
                contentDescription = getIconContentDescription(icon = R.drawable.ic_check_mark_solid_green),
            )
        }
    }
}

@Composable
fun ConfirmAppointmentScreenContent(
    confirmAppointmentUiState: ConfirmAppointmentUiState,
    onEvent: (ConfirmAppointmentEvent) -> Unit,
    paddingValues: PaddingValues,
    additionalCommentsFullScreen: MutableState<Boolean>,
) {
    val additionalCommentsInteractionSource =
        remember {
            MutableInteractionSource()
        }.also { source ->
            LaunchedEffect(source) {
                source.interactions.collect {
                    if (it is PressInteraction.Release) {
                        additionalCommentsFullScreen.value = !additionalCommentsFullScreen.value
                    }
                }
            }
        }
    Column(
        modifier =
            Modifier
                .background(AppTheme.colors.tertiary15)
                .padding(paddingValues = paddingValues)
                .padding(horizontal = 16.dp, vertical = 24.dp)
                .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        AnimatedVisibility(
            visible = !additionalCommentsFullScreen.value,
            enter =
                fadeIn(animationSpec = tween(ANIMATION_TIME)) +
                    expandVertically(
                        animationSpec = tween(ANIMATION_TIME),
                    ),
            exit =
                fadeOut(animationSpec = tween(ANIMATION_TIME)) +
                    shrinkVertically(
                        animationSpec = tween(ANIMATION_TIME),
                    ),
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                OdometerDetailComposable(
                    odometerDisplayValue = confirmAppointmentUiState.odometer,
                    odometerUnit = confirmAppointmentUiState.odometerUnit,
                )
                Spacer(modifier = Modifier.height(40.dp))
                ConfirmAppointmentDetails(
                    confirmAppointmentUiState = confirmAppointmentUiState,
                    onEvent = onEvent,
                )
                Spacer(modifier = Modifier.height(16.dp))
                OACaption1TextView(
                    text = stringResource(id = R.string.dsa_all_available_times),
                    color = AppTheme.colors.button02a,
                    textAlign = TextAlign.Center,
                )
            }
        }
        Spacer(modifier = Modifier.height(16.dp))
        AdditionalComments(
            confirmAppointmentUiState = confirmAppointmentUiState,
            additionalCommentsInteractionSource = additionalCommentsInteractionSource,
            onEvent = onEvent,
        )
    }
}

@Composable
fun ConfirmAppointmentDetails(
    confirmAppointmentUiState: ConfirmAppointmentUiState,
    onEvent: (ConfirmAppointmentEvent) -> Unit,
) {
    val subtitle =
        buildString {
            this.append(confirmAppointmentUiState.servicesDescription)
            if (confirmAppointmentUiState.hasCustomService) {
                if (this.isNotEmpty()) {
                    this.append(", ")
                }
                this.append(stringResource(id = R.string.dsa_custom_service))
            }
        }
    AppointmentDetailItemComposable(
        modifier = Modifier,
        title = stringResource(id = R.string.service_title),
        subtitle = subtitle,
        icon = R.drawable.ic_maintenance,
        onClick = {
            onEvent.invoke(
                ConfirmAppointmentEvent.EditConfirmation(EditConfirmationCategory.Services),
            )
        },
    )
    AppointmentDetailItemComposable(
        modifier = Modifier,
        title = stringResource(id = R.string.advisor_title),
        subtitle =
            confirmAppointmentUiState.serviceAdvisorDescription.ifEmpty {
                stringResource(
                    id = R.string.any_advisor,
                )
            },
        icon = R.drawable.ic_person_24px,
        onClick = {
            onEvent.invoke(
                ConfirmAppointmentEvent.EditConfirmation(EditConfirmationCategory.Advisor),
            )
        },
    )
    AppointmentDetailItemComposable(
        modifier = Modifier,
        title = stringResource(id = R.string.dsa_transportation),
        subtitle = confirmAppointmentUiState.transportationMethod,
        icon = R.drawable.ic_filter_transportation,
        onClick = {
            onEvent.invoke(
                ConfirmAppointmentEvent.EditConfirmation(EditConfirmationCategory.Transportation),
            )
        },
    )
    AppointmentDetailItemComposable(
        modifier = Modifier,
        title = stringResource(id = R.string.dsa_date_and_time),
        subtitle = "*${confirmAppointmentUiState.dateTime}",
        icon = R.drawable.ic_calender,
        onClick = { onEvent.invoke(ConfirmAppointmentEvent.EditDateTime) },
    )
}

@Composable
fun AdditionalComments(
    confirmAppointmentUiState: ConfirmAppointmentUiState,
    additionalCommentsInteractionSource: MutableInteractionSource,
    onEvent: (ConfirmAppointmentEvent) -> Unit,
) {
    AdditionalCommentsTextField(
        additionalComments = confirmAppointmentUiState.additionalComments,
        additionalCommentsInteractionSource = additionalCommentsInteractionSource,
        onEvent = onEvent,
    )
    Spacer(modifier = Modifier.height(8.dp))
    OACallOut1TextView(
        text = stringResource(id = R.string.dsa_confirm_appointment_terms),
        textAlign = TextAlign.Center,
        color = AppTheme.colors.tertiary05,
    )
}

@Composable
fun AdditionalCommentsTextField(
    additionalComments: String,
    onEvent: (ConfirmAppointmentEvent) -> Unit,
    additionalCommentsInteractionSource: MutableInteractionSource,
) {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .border(BorderStroke(1.dp, Color.Black), RoundedCornerShape(12.dp))
                .background(AppTheme.colors.tile05, RoundedCornerShape(12.dp)),
    ) {
        TextField(
            value = additionalComments,
            onValueChange = {
                onEvent.invoke(ConfirmAppointmentEvent.AdditionalComments(it))
            },
            interactionSource = additionalCommentsInteractionSource,
            placeholder = {
                Text(
                    stringResource(id = R.string.dsa_additional_comments_hint),
                    style = AppTheme.fontStyles.body1,
                    color = AppTheme.colors.tertiary07,
                )
            },
            modifier =
                Modifier
                    .fillMaxWidth()
                    .sizeIn(minHeight = 100.dp)
                    .background(AppTheme.colors.tile05, RoundedCornerShape(12.dp))
                    .padding(4.dp),
            colors =
                TextFieldDefaults.textFieldColors(
                    backgroundColor = AppTheme.colors.tile05,
                    focusedIndicatorColor = Color.Transparent,
                    unfocusedIndicatorColor = Color.Transparent,
                ),
            shape = RoundedCornerShape(12.dp),
        )
    }
}

@Composable
fun ConfirmAppointmentSuccessBottomSheetContent() {
    Column(
        modifier =
            Modifier
                .clickable(enabled = false) {}
                .fillMaxHeight(.4f)
                .fillMaxWidth()
                .background(AppTheme.colors.tile03)
                .padding(horizontal = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(24.dp))
        SuccessIcon()
        Spacer(modifier = Modifier.height(16.dp))
        OAHeadline1TextView(
            text = stringResource(id = R.string.dsa_appointment_scheduled),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary03,
        )
        Spacer(modifier = Modifier.height(8.dp))
        OACallOut1TextView(
            text = stringResource(id = R.string.dsa_appointment_scheduled_subtitle),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary05,
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ConfirmAppointmentScreenContentPreview() {
    ConfirmAppointmentScreenContent(
        confirmAppointmentUiState =
            ConfirmAppointmentUiState(
                odometer = "10000",
                odometerUnit = OdometerUnit.MILES,
                servicesDescription = "Service 1, Service 2",
                hasCustomService = true,
                serviceAdvisorDescription = "Service Advisor Name",
                transportationMethod = "transportationMethod",
                dateTime = "dateTime",
                additionalComments = "",
            ),
        onEvent = {},
        paddingValues = PaddingValues(),
        additionalCommentsFullScreen = remember { mutableStateOf(false) },
    )
}

@Preview(showBackground = true)
@Composable
fun ConfirmAppointmentBottomSheetPreview() {
    Column(modifier = Modifier.height(500.dp)) {
        ConfirmAppointmentBottomSheetContent()
    }
}

@Preview(showBackground = true)
@Composable
fun ConfirmAppointmentErrorBottomSheetPreview() {
    Column(modifier = Modifier.height(500.dp)) {
        ConfirmAppointmentErrorBottomSheetContent(
            errorMsg = "User is not allowed to book more than three appointments",
        )
    }
}

@Preview(showBackground = true)
@Composable
fun EditAppointmentBottomSheetConfirmationPreview() {
    Column(modifier = Modifier.height(800.dp)) {
        EditAppointmentBottomSheetConfirmation(
            confirmEditType = EditConfirmationCategory.Advisor,
            sheetState =
                rememberModalBottomSheetState(
                    initialValue = ModalBottomSheetValue.Expanded,
                ),
            onEvent = {},
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewMakeAppointmentTopBar() {
    TopBarPreviewContent(isEditing = false, showCloseButton = false)
}

@Preview(showBackground = true)
@Composable
private fun PreviewEditAppointmentTopBar() {
    TopBarPreviewContent(isEditing = true, showCloseButton = false)
}

@Preview(showBackground = true)
@Composable
private fun PreviewCloseButtonTopBar() {
    TopBarPreviewContent(isEditing = false, showCloseButton = true)
}

@Composable
private fun TopBarPreviewContent(
    isEditing: Boolean,
    showCloseButton: Boolean,
) {
    OATopAppBar(
        title =
            stringResource(
                id =
                    if (isEditing) {
                        R.string.dsa_edit_appointment
                    } else {
                        R.string.dsa_make_appointment
                    },
            ),
        titleTestTagId = AccessibilityId.ID_DSA_CONFIRM_APPOINTMENT_NAVIGATION_BAR,
        onBackTestTagId = AccessibilityId.ID_DSA_CONFIRM_APPOINTMENT_BACK_CTA,
        onButtonPressed = { },
        drawableRes = if (showCloseButton) R.drawable.ic_close else R.drawable.ic_back_arrow,
    )
}
