/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import com.toyota.oneapp.features.dealerservice.application.navigation.DSANavArguments.IS_EDIT_APPOINTMENT
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute.AppointmentAdvisorPage
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute.AppointmentEntryPage
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute.ConfirmAppointmentPage
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute.DateTimePage
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute.MakeAppointmentEntry
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute.ServiceAppointmentPage
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute.ServiceDeliveryPage
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute.ServiceDeliveryPageFromPickup
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute.ServicePickUpPage
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute.ServiceTransportationsPage
import com.toyota.oneapp.features.dealerservice.domain.model.MakeAppointment
import com.toyota.oneapp.features.dealerservice.domain.model.toEncodedJson
import com.toyota.oneapp.features.dealerservice.presentation.appointmentdatetime.AppointmentDateTimeScreen
import com.toyota.oneapp.features.dealerservice.presentation.appointmentodometerpage.DealerServiceAppointmentOdometerScreen
import com.toyota.oneapp.features.dealerservice.presentation.appointmentservicepage.AppointmentServiceScreen
import com.toyota.oneapp.features.dealerservice.presentation.confirmappointment.ConfirmAppointmentScreen
import com.toyota.oneapp.features.dealerservice.presentation.serviceadvisorpage.ServiceAdvisorScreen
import com.toyota.oneapp.features.dealerservice.presentation.servicetransportationpage.ServiceTransportationsScreen
import com.toyota.oneapp.features.dealerservice.presentation.transportationpickupdeliverypage.TransportationDeliveryScreen
import com.toyota.oneapp.features.dealerservice.presentation.transportationpickupdeliverypage.TransportationPickUpScreen

fun NavGraphBuilder.dealerServiceMakeAppointmentNavGraph(navController: NavHostController) {
    val appointmentArguments =
        listOf(
            navArgument(MakeAppointment.MAKE_APPOINTMENT_DATA) { type = NavType.StringType },
        )
    navigation(
        route = MakeAppointmentEntry.route,
        startDestination = AppointmentEntryPage.route,
    ) {
        composable(
            route = AppointmentEntryPage.route,
        ) { _ ->
            DealerServiceAppointmentOdometerScreen(
                navController = navController,
            )
        }
        composable(
            route = ServiceAppointmentPage.route,
            arguments = appointmentArguments,
        ) { _ ->
            AppointmentServiceScreen(
                navController = navController,
            )
        }
        composable(
            route = AppointmentAdvisorPage.route,
            arguments = appointmentArguments,
        ) { _ ->
            ServiceAdvisorScreen(
                navController = navController,
            )
        }
        composable(
            route = ServiceTransportationsPage.route,
            arguments = appointmentArguments,
        ) { _ ->
            ServiceTransportationsScreen(
                navController = navController,
            )
        }
        composable(
            route = ServicePickUpPage.route,
            arguments = appointmentArguments,
        ) { _ ->
            TransportationPickUpScreen(
                navController = navController,
            )
        }
        composable(
            route = ServiceDeliveryPage.route,
            arguments = appointmentArguments,
        ) { _ ->
            TransportationDeliveryScreen(
                navController = navController,
            )
        }
        composable(
            route = ServiceDeliveryPageFromPickup.route,
            arguments = appointmentArguments,
        ) { _ ->
            TransportationDeliveryScreen(
                navController = navController,
            )
        }
        composable(
            route = DateTimePage.route,
            arguments = appointmentArguments,
        ) {
            AppointmentDateTimeScreen(
                navController = navController,
            )
        }
        composable(
            route = "${ConfirmAppointmentPage.route}&$IS_EDIT_APPOINTMENT={$IS_EDIT_APPOINTMENT}",
            arguments =
                appointmentArguments.plus(
                    navArgument(IS_EDIT_APPOINTMENT) {
                        type = NavType.BoolType
                        defaultValue = false
                    },
                ),
        ) {
            ConfirmAppointmentScreen(
                navController = navController,
            )
        }
    }
}

sealed class DealerServiceConfirmAppointmentRoute(
    val route: String,
) {
    object MakeAppointmentEntry : DealerServiceConfirmAppointmentRoute(
        route = "make_appointment_entry",
    )

    object AppointmentEntryPage : DealerServiceConfirmAppointmentRoute(
        route = "dsa_appointment_odometer_page",
    )

    object ServiceAppointmentPage : DealerServiceConfirmAppointmentRoute(
        route = "dsa_appointment_service_page?$MAKE_APPOINTMENT_NAV_ARG",
    )

    object AppointmentAdvisorPage : DealerServiceConfirmAppointmentRoute(
        route = "dsa_appointment_advisor_page?$MAKE_APPOINTMENT_NAV_ARG",
    )

    object ServiceTransportationsPage : DealerServiceConfirmAppointmentRoute(
        route = "dsa_appointment_transportation_page?MAKE_APPOINTMENT_DATA={MAKE_APPOINTMENT_DATA}",
    )

    object ServicePickUpPage : DealerServiceConfirmAppointmentRoute(
        route = "dsa_appointment_service_pickup_page?MAKE_APPOINTMENT_DATA={MAKE_APPOINTMENT_DATA}",
    )

    object ServiceDeliveryPage : DealerServiceConfirmAppointmentRoute(
        route = "dsa_appointment_service_delivery_page?MAKE_APPOINTMENT_DATA={MAKE_APPOINTMENT_DATA}",
    )

    object ServiceDeliveryPageFromPickup : DealerServiceConfirmAppointmentRoute(
        route = "dsa_appointment_service_delivery_page_from_pickup?MAKE_APPOINTMENT_DATA={MAKE_APPOINTMENT_DATA}",
    )

    object ConfirmAppointmentPage : DealerServiceConfirmAppointmentRoute(
        route = "dsa_appointment_confirm_appointment_page?$MAKE_APPOINTMENT_NAV_ARG",
    )

    object DateTimePage : DealerServiceConfirmAppointmentRoute(
        route = "dsa_appointment_date_time_page?$MAKE_APPOINTMENT_NAV_ARG",
    )
}

const val MAKE_APPOINTMENT_NAV_ARG = "MAKE_APPOINTMENT_DATA={MAKE_APPOINTMENT_DATA}"

fun DealerServiceConfirmAppointmentRoute.navigateTo(currentAppointment: MakeAppointment): String? {
    val encodedAppointment = currentAppointment.toEncodedJson()
    encodedAppointment?.let {
        return route.replace("{${MakeAppointment.MAKE_APPOINTMENT_DATA}}", it)
    }
    return null
}

fun ConfirmAppointmentPage.editAppointmentRoute(appointmentId: String): String? {
    val encodedAppointment = MakeAppointment(appointmentId = appointmentId).toEncodedJson()
    encodedAppointment?.let {
        val testTrue = true
        val route = "${route.replace("{${MakeAppointment.MAKE_APPOINTMENT_DATA}}", it)}&$IS_EDIT_APPOINTMENT=$testTrue"
        return route
    }
    return null
}
