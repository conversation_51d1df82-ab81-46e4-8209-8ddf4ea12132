/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.presentation.confirmappointment

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.features.core.presentation.ProgressEvent
import com.toyota.oneapp.features.core.presentation.UiEvent
import com.toyota.oneapp.features.dealerservice.application.OdometerUseCase
import com.toyota.oneapp.features.dealerservice.application.navigation.DSANavArguments.IS_EDIT_APPOINTMENT
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceConfirmAppointmentRoute
import com.toyota.oneapp.features.dealerservice.application.navigation.navigateTo
import com.toyota.oneapp.features.dealerservice.domain.mapper.AppointmentDetailsMapper
import com.toyota.oneapp.features.dealerservice.domain.model.AppointmentUpdateStatus
import com.toyota.oneapp.features.dealerservice.domain.model.UIServiceAppointmentAdvisor
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentdetail.AppointmentDetail
import com.toyota.oneapp.features.dealerservice.domain.model.appointmentdetail.toTransportationModel
import com.toyota.oneapp.features.dealerservice.domain.model.getMakeAppointment
import com.toyota.oneapp.features.dealerservice.domain.usecase.ConfirmAppointmentUseCase
import com.toyota.oneapp.features.dealerservice.presentation.appointmentodometerpage.OdometerUnit
import com.toyota.oneapp.util.ToyotaConstants.Companion.CUSTOM_SERVICE_DESCRIPTION
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ConfirmAppointmentViewModel
    @Inject
    constructor(
        private val confirmAppointmentUseCase: ConfirmAppointmentUseCase,
        private val odometerUseCase: OdometerUseCase,
        private val savedStateHandle: SavedStateHandle,
        @ApplicationContext private val context: Context,
    ) : BaseViewModel<ConfirmAppointmentUiState, ConfirmAppointmentEvent>() {
        private var currentAppointment = savedStateHandle.getMakeAppointment()
        private var editAppointment = savedStateHandle.get<Boolean>(IS_EDIT_APPOINTMENT) ?: false

        fun isEditingAppointment(): Boolean = editAppointment

        override fun defaultState() = ConfirmAppointmentUiState()

        override fun onEvent(event: ConfirmAppointmentEvent) {
            when (event) {
                ConfirmAppointmentEvent.ConfirmAppointment -> {
                    submitAppointment()
                }
                ConfirmAppointmentEvent.EditServiceAdvisor -> {
                    val route =
                        DealerServiceConfirmAppointmentRoute.AppointmentAdvisorPage.navigateTo(
                            currentAppointment,
                        )
                    if (route != null) {
                        sendEvent(UiEvent.Navigate(route))
                    } else {
                        sendEvent(ConfirmAppointmentUiEvent.NavigationError)
                    }
                }
                ConfirmAppointmentEvent.EditServices -> {
                    val route =
                        DealerServiceConfirmAppointmentRoute.ServiceAppointmentPage.navigateTo(
                            currentAppointment,
                        )
                    if (route != null) {
                        sendEvent(UiEvent.Navigate(route))
                    } else {
                        sendEvent(ConfirmAppointmentUiEvent.NavigationError)
                    }
                }
                ConfirmAppointmentEvent.EditTransportation -> {
                    val route =
                        DealerServiceConfirmAppointmentRoute.ServiceTransportationsPage.navigateTo(
                            currentAppointment,
                        )
                    if (route != null) {
                        sendEvent(UiEvent.Navigate(route))
                    } else {
                        sendEvent(ConfirmAppointmentUiEvent.NavigationError)
                    }
                }
                ConfirmAppointmentEvent.EditDateTime -> {
                    val route =
                        DealerServiceConfirmAppointmentRoute.DateTimePage.navigateTo(
                            currentAppointment,
                        )
                    if (route != null) {
                        sendEvent(UiEvent.Navigate(route))
                    } else {
                        sendEvent(ConfirmAppointmentUiEvent.NavigationError)
                    }
                }
                is ConfirmAppointmentEvent.AdditionalComments -> {
                    state.update {
                        it.copy(
                            additionalComments = event.comment,
                        )
                    }
                    currentAppointment =
                        currentAppointment.copy(
                            additionalComments = event.comment,
                        )
                }

                is ConfirmAppointmentEvent.EditConfirmation -> {
                    state.update {
                        it.copy(
                            bottomSheetUiState = BottomSheetUiState.ConfirmEdit(event.editCategory),
                        )
                    }
                    sendEvent(ConfirmAppointmentUiEvent.EditAppointment)
                }
            }
        }

        init {
            if (editAppointment) {
                updatedStateWithExistingAppointment()
            } else {
                updateStateWithCurrentAppointment()
            }
        }

        private fun updatedStateWithExistingAppointment() {
            viewModelScope.launch {
                sendEvent(UiEvent.Progress(ProgressEvent.ShowProgress))
                val appointmentDetail =
                    confirmAppointmentUseCase.fetchExistingAppointmentById(
                        appointmentId = currentAppointment.appointmentId,
                    )
                appointmentDetail?.let { appDetails ->
                    state.update {
                        it.copy(
                            odometer =
                                odometerUseCase.formatOdometerValue(
                                    appDetails.odometer.orEmpty(),
                                ),
                            servicesDescription = appDetails.serviceDetails.orEmpty(),
                            serviceAdvisorDescription = appDetails.dealerAdvisor?.advisorName.orEmpty(),
                            transportationMethod = appDetails.transportation?.transportationName.orEmpty(),
                            dateTime = appDetails.appointmentDateTime.orEmpty(),
                            additionalComments = appDetails.comments.orEmpty(),
                        )
                    }
                    updateCurrentAppointment(appDetails)
                }
                sendEvent(UiEvent.Progress(ProgressEvent.DismissProgress))
            }
        }

        private fun updateCurrentAppointment(appointmentDetails: AppointmentDetail) {
            currentAppointment =
                with(appointmentDetails) {
                    currentAppointment.copy(
                        appointmentId = appointmentId.orEmpty(),
                        odometer = odometer.orEmpty(),
                        selectedServices = appointmentServiceList,
                        preferredDealer = null,
                        transportationInfo = transportation?.toTransportationModel(),
                        dateTime = appointmentDateTime.orEmpty(),
                        advisor =
                            UIServiceAppointmentAdvisor(
                                advisorId = dealerAdvisor?.advisorId.orEmpty(),
                                advisorName = dealerAdvisor?.advisorName.orEmpty(),
                                advisorPhotoUrl = dealerAdvisor?.advisorPhotoUrl.orEmpty(),
                                selected = true,
                            ),
                        additionalComments = comments.orEmpty(),
                    )
                }
        }

        private fun updateStateWithCurrentAppointment() {
            state.update {
                it.copy(
                    odometer = odometerUseCase.formatOdometerValue(currentAppointment.odometer),
                    odometerUnit = OdometerUnit.MILES,
                    servicesDescription =
                        currentAppointment.selectedServices
                            .filter { service ->
                                service.serviceDescription != CUSTOM_SERVICE_DESCRIPTION
                            }.map { service ->
                                service.serviceName.lowercase().split(" ").joinToString(" ") { word ->
                                    word.replaceFirstChar { firstChar -> firstChar.uppercaseChar() }
                                }
                            }.joinToString(separator = ",") { serviceName -> serviceName },
                    hasCustomService =
                        currentAppointment.selectedServices.find { service ->
                            service.serviceDescription == CUSTOM_SERVICE_DESCRIPTION
                        } != null,
                    serviceAdvisorDescription = currentAppointment.advisor?.advisorName.orEmpty(),
                    transportationMethod = currentAppointment.transportationInfo?.name.orEmpty(),
                    dateTime = AppointmentDetailsMapper.formatAppointmentDateTime(currentAppointment.dateTime),
                    additionalComments = "",
                )
            }
        }

        private fun submitAppointment() {
            currentAppointment =
                currentAppointment.copy(
                    dateTime = state.value.dateTime,
                    additionalComments = state.value.additionalComments,
                )

            viewModelScope.launch {
                confirmAppointmentUseCase
                    .submitAppointment(
                        appointment = currentAppointment,
                        isEdit = editAppointment,
                    ).collect { appointmentUpdateStatus ->
                        when (appointmentUpdateStatus) {
                            AppointmentUpdateStatus.InProgress -> {
                                state.update {
                                    it.copy(
                                        bottomSheetUiState = BottomSheetUiState.ConfirmingAppointment,
                                    )
                                }
                                sendEvent(ConfirmAppointmentUiEvent.AppointmentSchedulingInProgress)
                            }
                            is AppointmentUpdateStatus.Success -> {
                                val appointmentId = appointmentUpdateStatus.appointmentId
                                currentAppointment = currentAppointment.copy(appointmentId = appointmentId)
                                state.update {
                                    it.copy(
                                        bottomSheetUiState = BottomSheetUiState.AppointmentSuccess,
                                    )
                                }
                                delay(1000)
                                sendEvent(
                                    ConfirmAppointmentUiEvent.AppointmentCreated(
                                        "${DealerServiceAppointmentRoute.AppointmentDetails.route}${currentAppointment.appointmentId}",
                                    ),
                                )
                            }
                            is AppointmentUpdateStatus.Error -> {
                                state.update {
                                    it.copy(
                                        bottomSheetUiState =
                                            BottomSheetUiState.AppointmentError(
                                                appointmentUpdateStatus.msg,
                                            ),
                                    )
                                }
                                sendEvent(ConfirmAppointmentUiEvent.ErrorScheduling)
                            }
                            is AppointmentUpdateStatus.TimedOut -> {
                                state.update {
                                    it.copy(
                                        bottomSheetUiState =
                                            BottomSheetUiState.AppointmentError(
                                                errorMsg = context.getString(R.string.dsa_appointment_timeout),
                                            ),
                                    )
                                }
                                sendEvent(ConfirmAppointmentUiEvent.ErrorScheduling)
                            }
                        }
                    }
            }
        }
    }
