/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application

import com.toyota.oneapp.features.dealerservice.domain.mapper.ServiceHistoryMapper
import com.toyota.oneapp.features.dealerservice.domain.model.servicehistory.ServiceHistoryAddEditItem
import com.toyota.oneapp.features.dealerservice.domain.model.servicehistory.ServiceHistoryItem
import com.toyota.oneapp.features.dealerservice.domain.model.servicehistory.ServiceRecordResponse
import com.toyota.oneapp.features.dealerservice.domain.repository.ServiceHistoryRepository
import com.toyota.oneapp.features.dealerservice.presentation.servicehistory.ServiceHistoryState
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class ServiceHistoryLogic
    @Inject
    constructor(
        private val repository: ServiceHistoryRepository,
        private val mapper: ServiceHistoryMapper,
    ) : ServiceHistoryUseCase {
        override fun getServiceHistoryList(
            vehicleInfo: VehicleInfo,
            getUpdatedData: Boolean,
        ): Flow<ServiceHistoryState> {
            return flow {
                val response =
                    repository.fetchServiceHistory(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        getUpdatedData = getUpdatedData,
                    )
                when (response) {
                    is Resource.Success -> {
                        response.data?.let {
                            emit(ServiceHistoryState.Success(mapper.toList(it)))
                        }
                    }

                    is Resource.Failure -> {
                        emit(
                            ServiceHistoryState.Error(
                                message = response.error?.message,
                            ),
                        )
                    }
                    else -> {
                        emit(
                            ServiceHistoryState.Error(
                                message = response.error?.message,
                            ),
                        )
                    }
                }
            }
        }

        override fun getServiceHistory(
            vehicleInfo: VehicleInfo,
            serviceId: String,
        ): Flow<ServiceHistoryItem?> {
            return flow {
                val response = repository.fetchServiceHistory(vehicleInfo.vin, vehicleInfo.brand)
                val serviceHistories = (response as? Resource.Success)?.data?.payload?.serviceHistories

                // First, search for the item by serviceId.
                // If it’s not found, use the index instead, since dealer-created items do not have an ID.
                val serviceHistoryItem =
                    serviceHistories
                        ?.firstOrNull { it.serviceHistoryId == serviceId }
                        ?: serviceHistories?.getOrNull(serviceId.toInt())
                emit(serviceHistoryItem?.let { mapper.toDetail(it) })
            }
        }

        override fun getEditServiceHistory(
            vehicleInfo: VehicleInfo,
            serviceId: String,
        ): Flow<ServiceHistoryAddEditItem?> =
            flow {
                val response = repository.fetchServiceHistory(vehicleInfo.vin, vehicleInfo.brand)
                val serviceHistoryItem =
                    (response as? Resource.Success)?.data
                        ?.payload?.serviceHistories?.find { it.serviceHistoryId == serviceId }
                val editItem = serviceHistoryItem?.let { mapper.toEdit(it) }
                emit(editItem)
            }

        override fun createServiceHistory(
            vehicleInfo: VehicleInfo,
            historyAddItem: ServiceHistoryAddEditItem,
        ): Flow<ServiceRecordResponse> {
            return flow {
                val response =
                    repository.createServiceHistory(
                        vin = vehicleInfo.vin,
                        body = mapper.toBodyRequest(historyAddItem),
                    )
                emit(serviceRecordResponse(response))
            }
        }

        override fun updateServiceHistory(
            vehicleInfo: VehicleInfo,
            historyAddItem: ServiceHistoryAddEditItem,
        ): Flow<ServiceRecordResponse> {
            return flow {
                val response =
                    repository.updateServiceHistory(
                        vin = vehicleInfo.vin,
                        serviceHistoryId = historyAddItem.serviceHistoryId,
                        brand = vehicleInfo.brand,
                        body = mapper.toBodyRequest(historyAddItem),
                    )
                emit(serviceRecordResponse(response))
            }
        }

        override fun deleteServiceHistory(
            vehicleInfo: VehicleInfo,
            serviceHistoryId: String,
        ): Flow<ServiceRecordResponse> {
            return flow {
                val response =
                    repository.deleteServiceHistory(
                        vin = vehicleInfo.vin,
                        serviceHistoryId = serviceHistoryId,
                        brand = vehicleInfo.brand,
                    )
                emit(serviceRecordResponse(response))
            }
        }

        override suspend fun clearCache() = repository.clearCache()

        private fun serviceRecordResponse(response: Resource<BaseResponse?>) =
            if (response is Resource.Success) {
                ServiceRecordResponse.Success
            } else {
                (response as Resource.Failure).error?.message.let {
                    ServiceRecordResponse.Error(
                        message = it.toString(),
                    )
                }
            }
    }
