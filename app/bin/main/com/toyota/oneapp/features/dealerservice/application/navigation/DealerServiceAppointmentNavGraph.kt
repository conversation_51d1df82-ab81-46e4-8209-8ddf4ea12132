/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.application.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.navigation.sharedViewModel
import com.toyota.oneapp.features.dealerservice.application.navigation.DSANavArguments.APPOINTMENT_ID
import com.toyota.oneapp.features.dealerservice.application.navigation.DSANavArguments.DEALER_CODE
import com.toyota.oneapp.features.dealerservice.application.navigation.DSANavArguments.IS_PREFERRED_DEALER
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.AppointmentsPage
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.DealerDetails
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.LandingPage
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.UpdateOdometer
import com.toyota.oneapp.features.dealerservice.presentation.appointmentdetailpage.AppointmentDetailScreen
import com.toyota.oneapp.features.dealerservice.presentation.appointmentdetailpage.AppointmentDetailsViewModel
import com.toyota.oneapp.features.dealerservice.presentation.appointments.DealerServiceAppointmentsScreen
import com.toyota.oneapp.features.dealerservice.presentation.appointmentservicedetailpage.ServiceDetailsScreen
import com.toyota.oneapp.features.dealerservice.presentation.dealerdetails.DealerDetailsScreen
import com.toyota.oneapp.features.dealerservice.presentation.landingpage.DealerServiceAppointmentLandingPageScreen
import com.toyota.oneapp.features.dealerservice.presentation.landingpage.DealerServiceLandingPageViewModel
import com.toyota.oneapp.features.dealerservice.presentation.maintenanceschedule.updateodometer.UpdateOdometerScreen

fun NavGraphBuilder.dealerServiceAppointmentNavGraph(navController: NavHostController) {
    navigation(
        route = OAScreen.DealerServiceAppointment.route,
        startDestination = LandingPage.route,
    ) {
        composable(
            route = LandingPage.route,
        ) { entry ->
            val dealerServiceLandingPageViewModel =
                entry.sharedViewModel<DealerServiceLandingPageViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            DealerServiceAppointmentLandingPageScreen(
                navController = navController,
                dealerServiceLandingPageViewModel = dealerServiceLandingPageViewModel,
            )
        }
        preferredDealerSearchNavGraph(navController = navController)
        dealerServiceMakeAppointmentNavGraph(navController = navController)
        composable(
            route = AppointmentsPage.route,
        ) { entry ->
            val dealerServiceLandingPageViewModel =
                entry.sharedViewModel<DealerServiceLandingPageViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            DealerServiceAppointmentsScreen(
                navController = navController,
                viewModel = dealerServiceLandingPageViewModel,
            )
        }
        composable(
            route = "${DealerDetails.route}/{$DEALER_CODE}&$IS_PREFERRED_DEALER={$IS_PREFERRED_DEALER}",
            arguments =
                listOf(
                    navArgument(DEALER_CODE) { type = NavType.StringType },
                    navArgument(IS_PREFERRED_DEALER) {
                        type = NavType.BoolType
                        defaultValue = false
                    },
                ),
        ) {
            DealerDetailsScreen(navController = navController)
        }
        composable(
            route = UpdateOdometer.route,
        ) {
            UpdateOdometerScreen(navController = navController)
        }
        maintenanceScheduleNavGraph(navController = navController)
        serviceHistoryNestedGraph(navController = navController)
        navigation(
            route = DealerServiceAppointmentRoute.AppointmentDetailsEntry.route,
            startDestination = "appointment_details/{$APPOINTMENT_ID}",
        ) {
            composable(
                route = "appointment_details/{$APPOINTMENT_ID}",
                arguments =
                    listOf(
                        navArgument(APPOINTMENT_ID) { type = NavType.StringType },
                    ),
            ) { entry ->
                val viewModel =
                    entry.sharedViewModel<AppointmentDetailsViewModel>(
                        getBackStackEntry = { path ->
                            navController.getBackStackEntry(path)
                        },
                    )
                AppointmentDetailScreen(navController = navController, viewModel = viewModel)
            }
            composable(
                route = DealerServiceAppointmentRoute.AppointmentServiceDetails.route,
            ) { entry ->
                val viewModel =
                    entry.sharedViewModel<AppointmentDetailsViewModel>(
                        getBackStackEntry = { path ->
                            navController.getBackStackEntry(path)
                        },
                    )
                ServiceDetailsScreen(navController = navController, viewModel = viewModel)
            }
        }
    }
}

sealed class DealerServiceAppointmentRoute(
    val route: String,
) {
    object LandingPage : DealerServiceAppointmentRoute(route = "dsa_landing_page")

    object PreferredDealerSearchEntry : DealerServiceAppointmentRoute(
        route = "preferred_dealer_search_entry",
    )

    object PreferredDealerSearch : DealerServiceAppointmentRoute(route = "preferred_dealer_search")

    object DealerSearchField : DealerServiceAppointmentRoute(route = "dealer_search_field")

    object DealerSearchFilter : DealerServiceAppointmentRoute(route = "dealer_search_filter")

    object AppointmentsPage : DealerServiceAppointmentRoute(route = "dsa_appointments_page")

    object DealerDetails : DealerServiceAppointmentRoute(route = "dealer_details")

    object AppointmentDetailsEntry : DealerServiceAppointmentRoute(
        route = "appointment_details_entry",
    )

    object MaintenanceScheduleEntry : DealerServiceAppointmentRoute(
        route = "maintenance_schedule_entry",
    )

    object MaintenanceSchedule : DealerServiceAppointmentRoute(route = "maintenance_schedule")

    object MaintenanceScheduleDetail : DealerServiceAppointmentRoute(
        route = "maintenance_schedule_detail",
    )

    object UpdateOdometer : DealerServiceAppointmentRoute(route = "update_odometer_screen")

    object ServiceHistoryEntryPage : DealerServiceAppointmentRoute(
        route = "service_history_entry",
    )

    object ServiceHistoryPage : DealerServiceAppointmentRoute(
        route = "service_history_page",
    )

    object ServiceHistoryDetailPage : DealerServiceAppointmentRoute(
        route = "service_history_detail_page",
    )

    object ServiceHistoryAddEditPage : DealerServiceAppointmentRoute(
        route = "service_history_edit_page",
    )

    object ServiceHistoryServicesPage : DealerServiceAppointmentRoute(
        route = "service_history_service_page",
    )

    object AppointmentDetails : DealerServiceAppointmentRoute(route = "appointment_details/")

    object AppointmentServiceDetails : DealerServiceAppointmentRoute(
        route = "appointment_service_details",
    )
}

fun DealerDetails.createRoute(
    dealerCode: String,
    isPreferredDealer: Boolean,
): String = "$route/$dealerCode&$IS_PREFERRED_DEALER=$isPreferredDealer"

object DSANavArguments {
    const val DEALER_CODE = "dealerCode"
    const val IS_PREFERRED_DEALER = "isPreferredDealer"
    const val ODOMETER_VALUE = "odometerValue"
    const val APPOINTMENT_ID = "appointment_id"
    const val IS_EDIT_APPOINTMENT = "isEditAppointment"
}
