/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dealerservice.dataaccess.model.servicehistory

import com.google.gson.annotations.SerializedName

data class ServiceHistoryResponse(
    @SerializedName("payload") val payload: ServiceHistoryPayload?,
)

data class ServiceHistoryPayload(
    @SerializedName("serviceHistories") val serviceHistories: List<ServiceHistoryItemResponse>?,
)

data class ServiceHistoryItemResponse(
    @SerializedName("serviceHistoryId") val serviceHistoryId: String?,
    @SerializedName("servicingDealer") val servicingDealer: DealerItemResponse?,
    @SerializedName("mileage") val mileage: String?,
    @SerializedName("unit") val unit: String?,
    @SerializedName("roNumber") val roNumber: String?,
    @SerializedName("serviceDate") val serviceDate: String?,
    @SerializedName("operationsPerformed") val operationsPerformed: ArrayList<String>?,
    @SerializedName("customerCreatedRecord") val customerCreatedRecord: Boolean?,
    @SerializedName("serviceProvider") val serviceProvider: String?,
    @SerializedName("notes") val notes: String?,
)

data class DealerItemResponse(
    @SerializedName("servicingDealerCode") val servicingDealerCode: String?,
    @SerializedName("servicingDealerName") val servicingDealerName: String?,
    @SerializedName("address") val address: String?,
    @SerializedName("city") val city: String?,
    @SerializedName("latitude") val latitude: Double?,
    @SerializedName("longitude") val longitude: Double?,
    @SerializedName("phone") val phone: String?,
)
