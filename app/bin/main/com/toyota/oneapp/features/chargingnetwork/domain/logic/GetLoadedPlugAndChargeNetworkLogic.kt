/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadedPlugAndChargeNetworkUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetPlugAndChargeEnrollmentStatusUseCase
import javax.inject.Inject

class GetLoadedPlugAndChargeNetworkLogic
    @Inject
    constructor(
        private val getPlugAndChargeEnrolledStatus: GetPlugAndChargeEnrollmentStatusUseCase,
    ) : GetLoadedPlugAndChargeNetworkUseCase {
        override suspend fun invoke(isChargingNetworksDataConsentAccepted: Boolean): PlugAndChargeNetworkModel.Loaded? =
            getPlugAndChargeEnrolledStatus()
                .getOrNull()
                ?.toPlugAndChargeNetworkModel(isChargingNetworksDataConsentAccepted)

        private fun PlugAndChargeEnrollmentStatus.toPlugAndChargeNetworkModel(
            isChargingNetworksDataConsentAccepted: Boolean,
        ): PlugAndChargeNetworkModel.Loaded =
            PlugAndChargeNetworkModel.Loaded(
                status =
                    takeIf {
                        isChargingNetworksDataConsentAccepted
                    } ?: PlugAndChargeEnrollmentStatus.NotEnrolled.EnrollmentNotPossible,
            )
    }
