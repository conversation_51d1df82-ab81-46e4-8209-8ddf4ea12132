/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.IsRemoteFeatureFlagEnabledForChargingNetworkUseCase
import javax.inject.Inject

class IsRemoteFeatureFlagEnabledForChargingNetworkLogic
    @Inject
    constructor() :
    IsRemoteFeatureFlagEnabledForChargingNetworkUseCase {
        // At the moment the app doesn't have any global remote flags for the strategies
        override fun invoke(chargingNetworkType: ChargingNetworkType): Boolean =
            when (chargingNetworkType) {
                // For Ionna we'll keep true meawhile to don't affect the current behviour
                ChargingNetworkType.IONNA -> true
                // For Tesla it is new, so we return false
                ChargingNetworkType.TESLA -> true
                ChargingNetworkType.PLUG_AND_CHARGE -> true
            }
    }
