/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.domain.model.DataConsentNetworksModel
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStatus
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus

@Composable
fun ChargingNetworksComposable(
    modifier: Modifier = Modifier,
    chargingNetworksState: ChargingNetworksState,
    onDataConsentStatusClick: () -> Unit,
    onOpenEnrollmentPageClick: () -> Unit,
    onPlugAndChargeStatusClick: () -> Unit,
) {
    when (chargingNetworksState) {
        is ChargingNetworksState.Loaded ->
            LoadedChargingNetworkComposable(
                chargingNetworksState = chargingNetworksState,
                onDataConsentStatusClick = onDataConsentStatusClick,
                onOpenEnrollmentPageClick = onOpenEnrollmentPageClick,
                onPlugAndChargeStatusClick = onPlugAndChargeStatusClick,
            )
        is ChargingNetworksState.Loading ->
            Column {
                repeat(chargingNetworksState.enabledChargingNetworksAmount) {
                    ChargingNetworkShimmerRowComposable(modifier)
                }
            }
    }
}

@Composable
@Preview
private fun ChargingNetworksLoadedComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        themeMode = themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        ChargingNetworksComposable(
            chargingNetworksState =
                ChargingNetworksState.Loaded(
                    withDataConsent =
                        DataConsentNetworksModel(
                            status = DataConsentStatus.REGISTER,
                            dataConsentStrategies = DataConsentStrategy.entries,
                        ),
                    plugAndChargeNetwork =
                        PlugAndChargeNetworkModel.Loaded(
                            PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted,
                        ),
                ),
            onDataConsentStatusClick = {},
            onOpenEnrollmentPageClick = {},
            onPlugAndChargeStatusClick = {},
        )
    }
}

@Composable
@Preview
private fun ChargingNetworksLoadingComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        themeMode = themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        ChargingNetworksComposable(
            chargingNetworksState = ChargingNetworksState.Loading(2),
            onDataConsentStatusClick = {},
            onOpenEnrollmentPageClick = {},
            onPlugAndChargeStatusClick = {},
        )
    }
}
