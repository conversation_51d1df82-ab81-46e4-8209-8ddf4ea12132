/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.component

import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.component.PlugAndChargeEnrollmentEntryPointComposable
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.utils.toDescriptionTextId
import java.util.Date

@Composable
fun LoadedPlugAndChargeNetworkRowComposable(
    modifier: Modifier = Modifier,
    status: PlugAndChargeEnrollmentStatus,
    onOpenEnrollmentPageClick: () -> Unit,
    onPlugAndChargeStatusClick: () -> Unit,
) {
    ChargingNetworkInfoRowComposable(
        modifier = modifier,
        title = stringResource(id = R.string.plug_and_charge),
        statusComponent = {
            PlugAndChargeEnrollmentEntryPointComposable(
                status = status,
                onOpenEnrollmentPageClick = onOpenEnrollmentPageClick,
                onPlugAndChargeStatusClick = onPlugAndChargeStatusClick,
            )
        },
        descriptionText =
            status.toDescriptionTextId()?.let {
                stringResource(it)
            },
    )
}

@Composable
@Preview
private fun LoadedPlugAndChargeNetworkRowComposablePreview(
    @PreviewParameter(LoadedPlugAndChargeNetworkRowPreviewProvider::class)
    parameter: LoadedPlugAndChargeNetworkRowPreviewParameter,
) {
    ContentPreview(
        themeMode = parameter.themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        LoadedPlugAndChargeNetworkRowComposable(
            status = parameter.status,
            onOpenEnrollmentPageClick = {},
            onPlugAndChargeStatusClick = {},
        )
    }
}

private class LoadedPlugAndChargeNetworkRowPreviewProvider :
    PreviewParameterProvider<LoadedPlugAndChargeNetworkRowPreviewParameter> {
    override val values: Sequence<LoadedPlugAndChargeNetworkRowPreviewParameter>
        get() {
            val enrolled = PlugAndChargeEnrollmentStatus.Enrolled(true, Date())
            val enrollmentNotPossible =
                PlugAndChargeEnrollmentStatus.NotEnrolled.EnrollmentNotPossible
            val installing = PlugAndChargeEnrollmentStatus.NotEnrolled.Incomplete(progress = 25)
            val notStarted = PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted
            return sequenceOf(
                LoadedPlugAndChargeNetworkRowPreviewParameter(
                    themeMode = ThemeMode.Light,
                    status = enrolled,
                ),
                LoadedPlugAndChargeNetworkRowPreviewParameter(
                    themeMode = ThemeMode.Dark,
                    status = enrolled,
                ),
                LoadedPlugAndChargeNetworkRowPreviewParameter(
                    themeMode = ThemeMode.Light,
                    status = enrollmentNotPossible,
                ),
                LoadedPlugAndChargeNetworkRowPreviewParameter(
                    themeMode = ThemeMode.Dark,
                    status = enrollmentNotPossible,
                ),
                LoadedPlugAndChargeNetworkRowPreviewParameter(
                    themeMode = ThemeMode.Light,
                    status = installing,
                ),
                LoadedPlugAndChargeNetworkRowPreviewParameter(
                    themeMode = ThemeMode.Dark,
                    status = installing,
                ),
                LoadedPlugAndChargeNetworkRowPreviewParameter(
                    themeMode = ThemeMode.Light,
                    status = notStarted,
                ),
                LoadedPlugAndChargeNetworkRowPreviewParameter(
                    themeMode = ThemeMode.Dark,
                    status = notStarted,
                ),
            )
        }
}

private data class LoadedPlugAndChargeNetworkRowPreviewParameter(
    val themeMode: ThemeMode,
    val status: PlugAndChargeEnrollmentStatus,
)
