/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState

@Composable
fun LoadedChargingNetworkComposable(
    modifier: Modifier = Modifier,
    chargingNetworksState: ChargingNetworksState.Loaded,
    onDataConsentStatusClick: () -> Unit,
    onOpenEnrollmentPageClick: () -> Unit,
    onPlugAndChargeStatusClick: () -> Unit,
) {
    chargingNetworksState.run {
        Column(modifier = modifier) {
            val rowModifier = Modifier.padding(16.dp)
            withDataConsent?.let {
                DataConsentNetworkRowLoadedComposable(
                    modifier = rowModifier,
                    status = it.status,
                    onDataConsentStatusClick = onDataConsentStatusClick,
                    dataConsentStrategies = it.dataConsentStrategies,
                )
            }
            plugAndChargeNetwork?.let {
                PlugAndChargeNetworkRowComposable(
                    modifier = rowModifier,
                    model = it,
                    onOpenEnrollmentPageClick = onOpenEnrollmentPageClick,
                    onPlugAndChargeStatusClick = onPlugAndChargeStatusClick,
                )
            }
        }
    }
}
