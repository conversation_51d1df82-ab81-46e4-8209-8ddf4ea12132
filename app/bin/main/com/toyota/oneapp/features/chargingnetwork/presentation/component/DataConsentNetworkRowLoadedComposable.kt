/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.component

import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargingnetwork.presentation.utils.toStringResource
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStatus
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy
import com.toyota.oneapp.features.dataconsent.domain.model.isAccepted
import com.toyota.oneapp.features.dataconsent.util.toStringResource
import com.toyota.oneapp.features.entrollment.presentation.component.EnrolledComposable

@Composable
fun DataConsentNetworkRowLoadedComposable(
    modifier: Modifier = Modifier,
    dataConsentStrategies: List<DataConsentStrategy>,
    status: DataConsentStatus,
    onDataConsentStatusClick: () -> Unit,
) {
    val context = LocalContext.current
    ChargingNetworkInfoRowComposable(
        modifier = modifier,
        title = stringResource(R.string.charging_networks),
        statusComponent = {
            if (!status.isAccepted()) {
                ChargingNetworkStatusButton(
                    stringResourceId = status.toStringResource(),
                    onClick = onDataConsentStatusClick,
                )
            } else {
                EnrolledComposable(
                    textId = R.string.registered,
                )
            }
        },
        descriptionText =
            dataConsentStrategies.joinToString(", ") { item ->
                context.getText(item.toStringResource())
            },
    )
}

@Composable
@Preview
private fun DataConsentNetworkRowLoadedComposablePreview(
    @PreviewParameter(DataConsentStatusPreviewProvider::class)
    parameter: DataConsentNetworkRowPreviewParameter,
) {
    ContentPreview(
        themeMode = parameter.themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        DataConsentNetworkRowLoadedComposable(
            dataConsentStrategies = parameter.dataConsentStrategies,
            status = parameter.dataConsentStatus,
            onDataConsentStatusClick = {},
        )
    }
}

private class DataConsentStatusPreviewProvider :
    PreviewParameterProvider<DataConsentNetworkRowPreviewParameter> {
    override val values: Sequence<DataConsentNetworkRowPreviewParameter> =
        sequenceOf(
            DataConsentNetworkRowPreviewParameter(
                dataConsentStatus = DataConsentStatus.DECLINED,
                themeMode = ThemeMode.Dark,
                dataConsentStrategies = DataConsentStrategy.entries,
            ),
            DataConsentNetworkRowPreviewParameter(
                dataConsentStatus = DataConsentStatus.ACCEPTED,
                themeMode = ThemeMode.Light,
                dataConsentStrategies = DataConsentStrategy.entries,
            ),
            DataConsentNetworkRowPreviewParameter(
                dataConsentStatus = DataConsentStatus.REGISTER,
                themeMode = ThemeMode.Dark,
                dataConsentStrategies = DataConsentStrategy.entries,
            ),
            DataConsentNetworkRowPreviewParameter(
                dataConsentStatus = DataConsentStatus.DECLINED,
                themeMode = ThemeMode.Light,
                dataConsentStrategies = listOf(DataConsentStrategy.IONNA),
            ),
            DataConsentNetworkRowPreviewParameter(
                dataConsentStatus = DataConsentStatus.ACCEPTED,
                themeMode = ThemeMode.Dark,
                dataConsentStrategies = listOf(DataConsentStrategy.IONNA),
            ),
            DataConsentNetworkRowPreviewParameter(
                dataConsentStatus = DataConsentStatus.REGISTER,
                themeMode = ThemeMode.Dark,
                dataConsentStrategies = listOf(DataConsentStrategy.IONNA),
            ),
            DataConsentNetworkRowPreviewParameter(
                dataConsentStatus = DataConsentStatus.REGISTER,
                themeMode = ThemeMode.Light,
                dataConsentStrategies = listOf(DataConsentStrategy.TESLA),
            ),
            DataConsentNetworkRowPreviewParameter(
                dataConsentStatus = DataConsentStatus.ACCEPTED,
                themeMode = ThemeMode.Light,
                dataConsentStrategies = listOf(DataConsentStrategy.TESLA),
            ),
            DataConsentNetworkRowPreviewParameter(
                dataConsentStatus = DataConsentStatus.DECLINED,
                themeMode = ThemeMode.Light,
                dataConsentStrategies = listOf(DataConsentStrategy.TESLA),
            ),
        )
}

private data class DataConsentNetworkRowPreviewParameter(
    val dataConsentStatus: DataConsentStatus,
    val themeMode: ThemeMode,
    val dataConsentStrategies: List<DataConsentStrategy>,
)
