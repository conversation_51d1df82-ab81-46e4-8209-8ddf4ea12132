/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.component

import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import java.util.Date

@Composable
fun PlugAndChargeNetworkRowComposable(
    modifier: Modifier = Modifier,
    model: PlugAndChargeNetworkModel,
    onOpenEnrollmentPageClick: () -> Unit,
    onPlugAndChargeStatusClick: () -> Unit,
) {
    when (model) {
        is PlugAndChargeNetworkModel.Loaded ->
            LoadedPlugAndChargeNetworkRowComposable(
                modifier = modifier,
                status = model.status,
                onOpenEnrollmentPageClick = onOpenEnrollmentPageClick,
                onPlugAndChargeStatusClick = onPlugAndChargeStatusClick,
            )

        PlugAndChargeNetworkModel.Loading -> ChargingNetworkShimmerRowComposable()
    }
}

@Composable
@Preview
private fun PlugAndChargeNetworkRowComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = themeMode,
    ) {
        PlugAndChargeNetworkRowComposable(
            model = PlugAndChargeNetworkModel.Loaded(PlugAndChargeEnrollmentStatus.Enrolled(true, Date())),
            onOpenEnrollmentPageClick = {},
            onPlugAndChargeStatusClick = {},
        )
    }
}

@Composable
@Preview
private fun PlugAndChargeLoadingComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = themeMode,
    ) {
        PlugAndChargeNetworkRowComposable(
            model = PlugAndChargeNetworkModel.Loading,
            onOpenEnrollmentPageClick = {},
            onPlugAndChargeStatusClick = {},
        )
    }
}
