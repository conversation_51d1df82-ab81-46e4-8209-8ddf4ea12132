package com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.detail

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.toyota.one_ui.OneUIUtil
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentEnableTrialSubscriptionDetailBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.dataconsent.activities.BannerDataConsentActivity
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.ServiceDetailWebviewClient
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

/**
 * A Fragment - Displays Service Details for Enable Trial Subscription flow.
 */
@AndroidEntryPoint
class EnableTrialSubscriptionDetailFragment : BaseDataBindingFragment<FragmentEnableTrialSubscriptionDetailBinding>() {
    private val viewModel: EnableTrialSubscriptionDetailViewModel by viewModels()
    private val args: EnableTrialSubscriptionDetailFragmentArgs by navArgs()

    private val getDataConsent =
        registerForActivityResult<Intent, ActivityResult>(
            ActivityResultContracts.StartActivityForResult(),
            ActivityResultCallback { result: ActivityResult ->
                if (result.resultCode == Activity.RESULT_OK) {
                    val intent = result.data
                    if (intent != null) {
                        val consents = intent.getSerializableExtra(ToyotaConstants.CONSENTS) as List<ConsentRequestItem>
                        viewModel.onDataConsentReceived(consents)
                    }
                }
            },
        )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentEnableTrialSubscriptionDetailBinding,
        savedInstance: Bundle?,
    ) {
        setUpViewModelBindings()
        populateView()
    }

    override fun getLayout(): Int = R.layout.fragment_enable_trial_subscription_detail

    private fun setUpViewModelBindings() {
        viewModel.event.observe(
            viewLifecycleOwner,
            Observer {
                handleViewModelEvents(it)
            },
        )
    }

    private fun populateView() {
        context?.let {
            viewDataBinding.descriptionWebview.webViewClient =
                ServiceDetailWebviewClient(
                    it,
                    viewDataBinding.webviewProgress,
                )

            viewModel.uiModel.observe(viewLifecycleOwner) { data ->
                DataBindingAdapters.loadImage(viewDataBinding.ivImage, data.productImageUrl, null)
                viewDataBinding.tvName.text = data.productName

                val htmlData = OneUIUtil.appendBodyContentToHtml(data.productDescription, BuildConfig.IS_TOYOTA_APP)
                DataBindingAdapters.loadHTMLData(viewDataBinding.descriptionWebview, htmlData)

                data.wifiCarrierIcon?.let { iconRes -> viewDataBinding.ivCarrierIcon.setImageResource(iconRes) }

                viewDataBinding.btnEnableAllTrials.run {
                    setOnClickListener { viewModel.onSubmit() }
                    setText(data.actionTxt)
                }
            }
        }
    }

    private fun handleViewModelEvents(event: EnableTrialSubscriptionDetailViewModel.Event) {
        when (event) {
            is EnableTrialSubscriptionDetailViewModel.Event.NavigateToEnableAllTrialsScreen -> {
                navigateToEnableAllTrialsScreen(event)
            }
            is EnableTrialSubscriptionDetailViewModel.Event.NavigateToDataConsentScreen -> {
                navigateToDataConsentScreen(event)
            }
            is EnableTrialSubscriptionDetailViewModel.Event.NavigateToEnableWIFITrialSuccessScreen -> {
                navigateToEnableWIFITrialSuccessScreen()
            }
            is EnableTrialSubscriptionDetailViewModel.Event.NavigateToWIFIConsentDeclinedScreen -> {
                navigateToWIFIConsentDeclinedScreen(event)
            }
        }.exhaustive
    }

    private fun navigateToEnableAllTrialsScreen(event: EnableTrialSubscriptionDetailViewModel.Event.NavigateToEnableAllTrialsScreen) {
        val action =
            EnableTrialSubscriptionDetailFragmentDirections.actionEnableTrialSubscriptionDetailFragmentToEnableTrialSubscriptionFragment(
                vehicle = args.vehicle,
                title =
                    if (viewModel.isAddVehicleFlow) {
                        ""
                    } else {
                        getString(
                            R.string.enable_subscription,
                        )
                    },
                trialSubscriptions = event.trialSubscriptions,
                alerts = event.alerts,
                accessToken = event.accessToken,
                isAzure = event.isAzure ?: false,
                isCPOEligible = event.isCPOEligible ?: false,
                isPPOEligible = event.isPPOEligible ?: false,
            )
        findNavController().navigate(action)
    }

    private fun navigateToDataConsentScreen(event: EnableTrialSubscriptionDetailViewModel.Event.NavigateToDataConsentScreen) {
        val bannerConsentIntent =
            BannerDataConsentActivity.getIntent(
                context = requireContext(),
                vehicle = event.vehicle,
                isSubscriptionFlow = true,
                eligibleConsents = event.eligibleConsents.value,
            )
        getDataConsent.launch(bannerConsentIntent)
    }

    private fun navigateToEnableWIFITrialSuccessScreen() {
        val action =
            EnableTrialSubscriptionDetailFragmentDirections.actionEnableTrialSubscriptionDetailFragmentToSubscriptionActionSuccessActivity(
                vehicle = args.vehicle,
                icon = R.drawable.ic_success_check_mark_green,
                title = null,
                msg1 = getString(R.string.Subscription_added_success_title),
                msg2 = getString(R.string.wifi_success_msg),
                actionTxt = getString(R.string.ServiceCampaign_back_to_dashboard),
                msg3 = null,
            )
        findNavController().navigate(action)
    }

    private fun navigateToWIFIConsentDeclinedScreen(
        event: EnableTrialSubscriptionDetailViewModel.Event.NavigateToWIFIConsentDeclinedScreen,
    ) {
        val action =
            EnableTrialSubscriptionDetailFragmentDirections.actionEnableTrialSubscriptionDetailFragmentToSubscriptionActionSuccessActivity(
                vehicle = args.vehicle,
                icon = R.drawable.ic_decline_wifi,
                title = null,
                msg1 = getString(R.string.wifi_consent_declined_message, event.displayProductName),
                msg2 = getString(R.string.wifi_consent_declined_msg, event.displayProductName),
                actionTxt = getString(R.string.ServiceCampaign_back_to_dashboard),
                msg3 = null,
            )
        findNavController().navigate(action)
    }
}
