package com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.manage

import android.os.Bundle
import android.widget.LinearLayout
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.one_ui.OneUIUtil
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.FragmentManagePaidSubscriptionBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.common.subscriptionbundle.IndividualServiceAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.ServiceDetailWebviewClient
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * A Fragment - Used to manage the paid subscriptions.
 */
@AndroidEntryPoint
class ManagePaidSubscriptionFragment : BaseDataBindingFragment<FragmentManagePaidSubscriptionBinding>() {
    @Inject lateinit var applicationData: ApplicationData
    private val args: ManagePaidSubscriptionFragmentArgs by navArgs()
    private val viewModel: ManagePaidSubscriptionViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentManagePaidSubscriptionBinding,
        savedInstance: Bundle?,
    ) {
        setUpViewModelBindings(binding)
        populateView(binding)
    }

    override fun getLayout(): Int = R.layout.fragment_manage_paid_subscription

    private fun setUpViewModelBindings(viewBinding: FragmentManagePaidSubscriptionBinding) {
        viewModel.event.observe(viewLifecycleOwner) {
            handleEvent(it)
        }

        viewModel.uiModel.observe(viewLifecycleOwner) {
            viewBinding.tvProductName.text = it.screenTitle
            DataBindingAdapters.setIsVisible(viewBinding.bundleCard, it.showBundleCard)
            viewBinding.tvBundleTitle.text = it.displayProductName
            viewBinding.tvAutoRenewStatus.setText(it.autoRenewStatus)
            viewBinding.tvExpiryDate.text = it.displayTerms
            DataBindingAdapters.setIsVisible(viewBinding.descriptionWebview, !it.showBundleCard)
            DataBindingAdapters.loadHTMLData(
                viewBinding.descriptionWebview,
                OneUIUtil.appendBodyContentToHtml(it.productLongDesc, BuildConfig.IS_TOYOTA_APP),
            )
            DataBindingAdapters.setIsVisible(viewBinding.cbAutoRenew, it.isAutoRenewVisible)
            DataBindingAdapters.setIsVisible(viewBinding.tvAutoRenewMsg, it.isAutoRenewVisible)
            viewBinding.tvAutoRenewMsg.text = it.autoRenewDisclaimer
            DataBindingAdapters.setIsVisible(viewBinding.clFooter, it.isAutoRenewVisible || !it.futureCancel)
            viewBinding.btnUpdate.setText(it.updateActionTxt)
            DataBindingAdapters.setIsVisible(viewBinding.btnUpdate, it.isAutoRenewVisible)
            viewBinding.btnCancelSubscription.setText(it.cancelActionTxt)
            DataBindingAdapters.setIsVisible(viewBinding.btnCancelSubscription, !it.futureCancel)
        }

        viewModel.autoRenew.observe(viewLifecycleOwner) {
            viewBinding.cbAutoRenew.isChecked = it
        }
        viewModel.updateOptionEnabled.observe(viewLifecycleOwner) {
            viewBinding.btnUpdate.isChecked = it
        }
    }

    private fun populateView(viewBinding: FragmentManagePaidSubscriptionBinding) {
        if (args.selectedSubscription.category == "BUNDLE") {
            applicationData.getSelectedVehicle()?.let {
                val individualServiceAdapter =
                    IndividualServiceAdapter(
                        it,
                        args.selectedSubscription.components ?: emptyList(),
                    )

                viewBinding.rvBundleServices.run {
                    layoutManager = LinearLayoutManager(requireContext())
                    adapter = individualServiceAdapter
                    addItemDecoration(
                        DividerItemDecoration(requireContext(), LinearLayout.VERTICAL),
                    )
                }
            }
        }

        context?.let {
            viewDataBinding.descriptionWebview.webViewClient =
                ServiceDetailWebviewClient(
                    it,
                    viewDataBinding.webviewProgress,
                )
        }

        viewBinding.cbAutoRenew.setOnCheckedChangeListener { _, isChecked ->
            viewModel.onAutoRenewCheckedChanged(isChecked)
        }

        viewBinding.btnUpdate.setOnClickListener {
            viewModel.onUpdateSubscription()
        }
        viewBinding.btnCancelSubscription.setOnClickListener {
            viewModel.onCancelSubscription()
        }
    }

    private fun handleEvent(event: ManagePaidSubscriptionViewModel.Event) {
        when (event) {
            is ManagePaidSubscriptionViewModel.Event.NavigateToCancelSubscriptionForCY17Screen -> {
                navigateToCancelSubscriptionForCY17Screen(event)
            }
            is ManagePaidSubscriptionViewModel.Event.NavigateToCancelSubscriptionForCY17PlusScreen -> {
                navigateToCancelSubscriptionForCY17PlusScreen(event)
            }
            is ManagePaidSubscriptionViewModel.Event.UpdateSubscriptionScreen -> {
                handleUpdateSubscriptionSuccessEvent(event)
            }
        }
    }

    private fun navigateToCancelSubscriptionForCY17Screen(
        event: ManagePaidSubscriptionViewModel.Event.NavigateToCancelSubscriptionForCY17Screen,
    ) {
        val action =
            ManagePaidSubscriptionFragmentDirections.actionManagePaidSubscriptionFragmentToCancelAllSubscriptionFragment(
                vehicle = args.vehicle,
                trialSubscriptions = event.trialSubscriptions,
                paidSubscriptions = event.paidSubscriptions,
                ppoCancelDisclaimer = event.ppoCancelDisclaimer,
            )
        findNavController().navigate(action)
    }

    private fun navigateToCancelSubscriptionForCY17PlusScreen(
        event: ManagePaidSubscriptionViewModel.Event.NavigateToCancelSubscriptionForCY17PlusScreen,
    ) {
        val action =
            ManagePaidSubscriptionFragmentDirections
                .actionPaidSubscriptionBottomSheetDialogFragmentToCancelPaidSubscriptionForCY17PlusFragment(
                    vehicle = args.vehicle,
                    subscription = event.subscription,
                )
        findNavController().navigate(action)
    }

    private fun handleUpdateSubscriptionSuccessEvent(event: ManagePaidSubscriptionViewModel.Event.UpdateSubscriptionScreen) {
        val action =
            ManagePaidSubscriptionFragmentDirections
                .actionUpdateCancelPaidSubscriptionBottomSheetDialogFragmentToSubscriptionActionSuccessActivity(
                    vehicle = args.vehicle,
                    icon = R.drawable.success_checkmark,
                    title = getString(event.title),
                    msg1 = getString(event.msg1),
                    msg2 = getString(event.msg2),
                    actionTxt = getString(R.string.ServiceCampaign_back_to_dashboard),
                    msg3 = null,
                )
        findNavController().navigate(action)
    }
}
