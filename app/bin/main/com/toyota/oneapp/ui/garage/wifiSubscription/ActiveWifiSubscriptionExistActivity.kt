package com.toyota.oneapp.ui.garage.wifiSubscription

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.databinding.Observable
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityActiveWifiSubscriptionExistBinding
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.IntentUtil
import com.toyota.oneapp.util.ToyUtil
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ActiveWifiSubscriptionExistActivity : UiBaseActivity() {
    companion object {
        const val TRIAL_SUBSCRIPTIONS = "TRIAL_SUBSCRIPTIONS"
        const val VEHICLE_GENERATION = "VEHICLE_GENERATION"
        const val VEHICLE_REGION = "VEHICLE_REGION"
        const val IS_ON_BOARDING = "IS_ON_BOARDING"
        const val IS_GARAGE_FLOW = "IS_GARAGE_FLOW"

        fun getIntent(
            context: Context,
            trialSubscriptions: Array<SubscriptionV2>? = null,
            vehicleGeneration: String,
            vehicleRegion: String,
            isOnBoarding: Boolean? = false,
            isMyGarageFlow: Boolean? = false,
        ): Intent {
            val intent = Intent(context, ActiveWifiSubscriptionExistActivity::class.java)
            intent.putExtra(VEHICLE_GENERATION, vehicleGeneration)
            intent.putExtra(VEHICLE_REGION, vehicleRegion)
            intent.putExtra(IS_ON_BOARDING, isOnBoarding)
            intent.putExtra(IS_GARAGE_FLOW, isMyGarageFlow)
            intent.putExtra(TRIAL_SUBSCRIPTIONS, trialSubscriptions)
            return intent
        }
    }

    private val viewModel: ActiveWifiSubscriptionExistViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        val dataBinding =
            DataBindingUtil.setContentView<ActivityActiveWifiSubscriptionExistBinding>(
                this,
                R.layout.activity_active_wifi_subscription_exist,
            )
        dataBinding.lifecycleOwner = this
        observeBaseEvents(viewModel)
        performActivitySetup(dataBinding.toolbar)
        dataBinding.executePendingBindings()
        viewModel.onInit(
            trialSubscriptions = intent.getParcelableArrayExtra(TRIAL_SUBSCRIPTIONS)?.map { it as SubscriptionV2 },
            vehicleGeneration = intent.getStringExtra(VEHICLE_GENERATION) ?: "",
            showToolbar = intent.getBooleanExtra(IS_ON_BOARDING, false),
        )

        viewModel.contactSupportCtaText.observe(
            this,
            Observer {
                dataBinding.contactSupportCta.setText(it)
            },
        )
        viewModel.activeWifiSubscriptionExistNavigationEvent.observe(
            this,
            Observer {
                when (it) {
                    is ActiveWifiSubscriptionExistViewModel.ActiveWifiSubscriptionExistNavigationEvent.OnContactSupportClicked ->
                        ToyUtil.phoneCall(this, it.supportNumber)
                }
            },
        )

        viewModel.showAppBar.addOnPropertyChangedCallback(
            object :
                Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(
                    sender: Observable?,
                    propertyId: Int,
                ) {
                    dataBinding.appBar.isVisible = viewModel.showAppBar.get()
                }
            },
        )

        dataBinding.contactSupportCta.setOnClickListener {
            viewModel.applicationData.getSelectedVehicle()?.let { it1 ->
                val supportNumber =
                    ToyUtil.getPhoneNO(
                        this,
                        intent.getStringExtra(VEHICLE_REGION),
                        it1.brand,
                    )
                viewModel.onContactSupportClicked(viewModel.getSupportNumber(supportNumber))
            }
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        if (intent.getBooleanExtra(IS_GARAGE_FLOW, false)) {
            val intent =
                viewModel.applicationData.getSelectedVehicle()?.let {
                    IntentUtil
                        .getManageSubscriptionIntent(
                            this,
                            it,
                        ).apply {
                            flags = Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT
                        }
                }
            startActivity(intent)
        }
        if (!intent.getBooleanExtra(IS_ON_BOARDING, false)) finish()
    }
}
