package com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.detail

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.account.AcknowledgeConsentRequest
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.combineddataconsent.DataConsentStatus
import com.toyota.oneapp.model.subscription.*
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.detail.mapper.EnableTrialSubscriptionDetailUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.detail.model.EnableTrialSubscriptionDetailUIModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel - For [EnableTrialSubscriptionDetailFragment]
 */
@HiltViewModel
class EnableTrialSubscriptionDetailViewModel
    @Inject
    constructor(
        private val savedStateHandle: SavedStateHandle,
        private val mapper: EnableTrialSubscriptionDetailUIMapper,
        private val preferenceModel: OneAppPreferenceModel,
        private val combinedDataConsentRepository: CombinedDataConsentRepository,
        private val applicationData: ApplicationData? = null,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        val isAddVehicleFlow: Boolean = savedStateHandle["IS_ADD_VEHICLE_FLOW"] ?: false
        private val args =
            EnableTrialSubscriptionDetailFragmentArgs.fromSavedStateHandle(
                savedStateHandle,
            )

        companion object {
            const val CATEGORY_WIFI_CONSENT = "WIFI_CONSENT"
            const val WIFI = "wifi"
        }

        sealed class Event {
            data class NavigateToEnableAllTrialsScreen(
                val trialSubscriptions: Array<SubscriptionV2>,
                val alerts: Array<VehicleSubscriptionAlert>?,
                val accessToken: String?,
                val isAzure: Boolean?,
                val isCPOEligible: Boolean?,
                val isAddVehicleFlow: Boolean?,
                val isPPOEligible: Boolean?,
            ) : Event()

            data class NavigateToDataConsentScreen(
                val vehicle: VehicleInfo,
                val subscriptionGetPayload: SubscriptionGetPayload,
                val eligibleConsents: CombinedDataConsentRepository.DataConsentFlag,
            ) : Event()

            object NavigateToEnableWIFITrialSuccessScreen : Event()

            data class NavigateToWIFIConsentDeclinedScreen(
                val displayProductName: String,
            ) : Event()
        }

        private var consents: List<ConsentRequestItem>? = null

        private val _uiModel = MutableLiveData<EnableTrialSubscriptionDetailUIModel>()
        private val _event = SingleLiveEvent<Event>()

        val uiModel: LiveData<EnableTrialSubscriptionDetailUIModel>
            get() = _uiModel
        val event: LiveData<Event>
            get() = _event

        init {
            _uiModel.value = mapper.map(args)
        }

        /**
         * If the subscription is ExternalSubscription (WIFI) & it is the only subscription to be enabled - Then make API call to enable it in the same screen.
         * Else - Navigate user to EnableAllTrials (Where we list all trials that will be enabled) screen.
         */
        fun onSubmit() {
            if (args.selectedSubscription.externalProduct && args.trialSubscriptions.size == 1) {
                navigateToDataConsentScreen()
            } else {
                navigateToEnableAllTrialsScreen()
            }
        }

        private fun navigateToEnableAllTrialsScreen() {
            analyticsLogger.logEvent(
                AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_DETAIL_ENABLE_ALL_TRIAL_CLICKED,
            )

            _event.value =
                Event.NavigateToEnableAllTrialsScreen(
                    trialSubscriptions = args.trialSubscriptions,
                    alerts = args.alerts,
                    accessToken = args.accessToken,
                    isAzure = args.isAzure,
                    isCPOEligible = args.isCPOEligible,
                    isAddVehicleFlow = args.isAddVehicleFlow,
                    isPPOEligible = args.isPPOEligible,
                )
        }

        private fun navigateToDataConsentScreen() {
            analyticsLogger.logEvent(
                AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_DETAIL_ENABLE_TRIAL_CLICKED,
            )

            _event.value =
                Event.NavigateToDataConsentScreen(
                    vehicle = args.vehicle,
                    subscriptionGetPayload = formSubscriptionGetPayload(),
                    eligibleConsents = CombinedDataConsentRepository.DataConsentFlag.WIFI_CONSENT,
                )
        }

        fun onDataConsentReceived(consents: List<ConsentRequestItem>) {
            this.consents = consents

            val isWIFIConsentAccepted = checkIsWIFIConsentAccepted(consents)
            if (isWIFIConsentAccepted) {
                createWIFITrialSubscription()
            } else {
                updateWIFIConsentDeclined(consents)
            }
        }

        private fun checkIsWIFIConsentAccepted(consents: List<ConsentRequestItem>?): Boolean {
            val wifiConsent = consents?.firstOrNull { it.category == CATEGORY_WIFI_CONSENT }
            return (DataConsentStatus.fromValue(wifiConsent?.status) == DataConsentStatus.ACCEPTED)
        }

        private fun createWIFITrialSubscription() {
            val subscriptionGetPayload = formSubscriptionGetPayload()
            val productsTotalAmount = ProductsTotalAmount(ToyotaConstants.US_CURRENCY_CODE, 0.0)
            val productsAmount = ProductsAmount(ToyotaConstants.US_CURRENCY_CODE, 0.0)
            val taxAmount = TaxAmount(ToyotaConstants.US_CURRENCY_CODE, 0.0)
            val capabilities = args.vehicle.capabilityItems

            viewModelScope.launch {
                showProgress()
                val resource =
                    combinedDataConsentRepository.createSubscription(
                        asiCode = applicationData?.getSelectedVehicle()?.asiCode.orEmpty(),
                        hwtType = applicationData?.getSelectedVehicle()?.hwType.orEmpty(),
                        refId = "",
                        isPaymentDefault = false,
                        paymentToken = "",
                        accessToken = subscriptionGetPayload.accessToken,
                        subscriptionGetPayload = subscriptionGetPayload,
                        capabilities = capabilities,
                        consent = consents,
                        totalAmount = productsTotalAmount,
                        productAmount = productsAmount,
                        taxAmount = taxAmount,
                    )

                when (resource) {
                    is Resource.Success -> {
                        hideProgress()
                        _event.postValue(Event.NavigateToEnableWIFITrialSuccessScreen)
                    }

                    is Resource.Failure -> {
                        hideProgress()
                        showErrorMessage(R.string.generic_error)
                    }

                    is Resource.Loading -> {}
                }
            }
        }

        private fun formSubscriptionGetPayload(): SubscriptionGetPayload =
            SubscriptionUtil.formSubscriptionGetPayload(
                vehicle = args.vehicle,
                guid = preferenceModel.getGuid(),
                trialSubscriptions = args.trialSubscriptions.toList(),
                accessToken = args.accessToken ?: "",
                isCPOEligible = args.isCPOEligible,
                isPPOEligible = args.isPPOEligible,
            )

        private fun updateWIFIConsentDeclined(consents: List<ConsentRequestItem>) {
            viewModelScope.launch {
                showProgress()

                val body =
                    AcknowledgeConsentRequest(
                        vin = args.vehicle.vin,
                        guid = preferenceModel.getGuid(),
                        consents = consents,
                    )

                val resource =
                    combinedDataConsentRepository.acknowledgeConsent(
                        vin = args.vehicle.vin,
                        brand = args.vehicle.brand,
                        region = args.vehicle.region,
                        eligibleConsent = WIFI,
                        body = body,
                    )
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.let {
                            _event.value =
                                Event.NavigateToWIFIConsentDeclinedScreen(
                                    displayProductName = args.selectedSubscription.displayProductName,
                                )
                        }
                    }

                    is Resource.Failure -> {
                        showErrorMessage(resource.message)
                    }

                    is Resource.Loading -> {}
                }

                hideProgress()
            }
        }
    }
