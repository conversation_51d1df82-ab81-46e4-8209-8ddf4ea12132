package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.mapper

import android.content.Context
import com.toyota.oneapp.extensions.getLocale
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutItemUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.SelectedPackageInfo
import com.toyota.oneapp.util.DoubleUtil.toDisplayPrice
import javax.inject.Inject

class CheckoutServiceListUIMapper
    @Inject
    constructor(
        private val context: Context,
        private val languageManager: LanguageManager,
    ) {
        fun map(
            services: List<AvailableSubscription>,
            selectedPkgInfoMap: Map<String, SelectedPackageInfo>,
            vehicle: VehicleInfo,
        ): List<CheckoutItemUIModel> {
            val uiModels = mutableListOf<CheckoutItemUIModel>()
            val vehicleLocale = vehicle.getLocale(language = languageManager.getCurrentLanguage())

            services.forEach { service ->
                val selectedPkgInfo = selectedPkgInfoMap[service.productLine]
                if (selectedPkgInfo != null && service.category.equals("BUNDLE", true)) {
                    val isRenewable = (!vehicle.isCY17) && (service.renewable == true)

                    val uiModel =
                        CheckoutItemUIModel.CheckoutBundleListItemUIModel(
                            productLine = service.productLine.orEmpty(),
                            title = service.displayProductName,
                            subscriptionPrice =
                                selectedPkgInfo.pkg.price
                                    ?.toDisplayPrice(vehicleLocale)
                                    .orEmpty(),
                            subscriptionTerm =
                                getSubscriptionBundleTerms(
                                    selectedPkgInfo.pkg.subscriptionTerm.orEmpty(),
                                ),
                            isRenewable = isRenewable,
                            isAutoRenew = selectedPkgInfo.autoRenew,
                            bundleComponents = service.components ?: emptyList(),
                        )
                    uiModels.add(uiModel)
                } else if (selectedPkgInfo != null) {
                    // Individual service checkout
                    val displayTerm =
                        context.getString(
                            SubscriptionUtil.getDisplayTermUnit(
                                selectedPkgInfo.pkg.subscriptionTerm
                                    ?: "",
                            ),
                        )
                    val subTitle = "${selectedPkgInfo.pkg.price?.toDisplayPrice(vehicleLocale)}/$displayTerm"

                    val isRenewable = (!vehicle.isCY17) && (service.renewable == true)

                    val uiModel =
                        CheckoutItemUIModel.CheckoutServiceListItemUIModel(
                            productLine = service.productLine.orEmpty(),
                            title = service.displayProductName,
                            subTitle = subTitle,
                            isRenewable = isRenewable,
                            isAutoRenew = selectedPkgInfo.autoRenew,
                        )
                    uiModels.add(uiModel)
                }
            }

            return uiModels
        }

        private fun getSubscriptionBundleTerms(term: String): String =
            when (term) {
                "MTH" -> " /mo"
                "YRLY" -> " /yr"
                else -> ""
            }
    }
