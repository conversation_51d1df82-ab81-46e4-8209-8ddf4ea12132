package com.toyota.oneapp.ui.garage

import android.os.Bundle
import android.view.animation.AnimationUtils.loadAnimation
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityEditSingleSubscriptionBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.garage.subscriptionCancellation.CancelSubscriptionActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class EditSingleSubscriptionActivity : UiBaseActivity() {
    private val viewModel: EditSingleSubscriptionViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(viewModel)
        val binding =
            DataBindingUtil
                .setContentView<ActivityEditSingleSubscriptionBinding>(
                    this,
                    R.layout.activity_edit_single_subscription,
                ).apply {
                    editSubscriptionLayout.startAnimation(
                        loadAnimation(this@EditSingleSubscriptionActivity, com.toyota.one_ui.R.anim.slide_from_bottom),
                    )
                    lifecycleOwner = this@EditSingleSubscriptionActivity
                    executePendingBindings()
                    performActivitySetup(toolbar)
                }

        viewModel.onCancelSubscriptionClick.observe(this) {
            onCancelSubscription(it)
        }
        viewModel.apiCallStatus.observe(this) {
            onApiCallResponse(it)
        }

        val data = EditSubscriptionData.createFromIntent(intent)
        viewModel.populateSubscriptionInfo(data)
        viewModel.state.observe(this) {
            binding.subscriptionNameTextview.text = it.title
            binding.subscriptionDescriptionTextview.text = it.description
            binding.autoRenewCheckbox.isEnabled = it.autoRenewEnabled
            binding.autoRenewCheckbox.isVisible = it.autoRenewEnabled && !it.isOnlyRemoteUser
            binding.autoRenewSubscription.isEnabled = it.autoRenewEnabled
            binding.autoRenewSubscription.isVisible = it.autoRenewEnabled && !it.isOnlyRemoteUser
            binding.autoRenewWarning.isEnabled = it.autoRenewEnabled
            binding.autoRenewWarning.isVisible = it.autoRenewEnabled && !it.isOnlyRemoteUser
            binding.updateSubscriptionButton.isVisible = it.autoRenewEnabled && !it.isOnlyRemoteUser
            binding.cancelSubscriptionButton.isVisible = it.futureCancel && !it.isOnlyRemoteUser
        }
        viewModel.subscriptionAutoRenewOn.observe(this) {
            binding.autoRenewCheckbox.setImageResource(it)
        }
        viewModel.subscriptionChanged.observe(this) {
            binding.updateSubscriptionButton.isEnabled = it
        }
        binding.autoRenewCheckbox.setOnClickListener {
            viewModel.onAutoRenewClick()
        }
        binding.updateSubscriptionButton.setOnClickListener {
            viewModel.onUpdateSubscription()
        }
        binding.cancelSubscriptionButton.setOnClickListener {
            viewModel.onCancelSubscription()
        }
    }

    private fun onCancelSubscription(editSubscriptionData: EditSubscriptionData) {
        startActivity(
            editSubscriptionData.createIntent(this, CancelSubscriptionActivity::class.java),
        )
    }

    private fun onApiCallResponse(status: EditSingleSubscriptionViewModel.Status) {
        if (status.isSuccess) {
            val parameters =
                PaymentSuccessActivity.Parameters(
                    if (status.newAutoRenewValue) {
                        SubscriptionEditSuccess.AUTO_RENEW_ACTIVATED
                    } else {
                        SubscriptionEditSuccess.AUTO_RENEW_DEACTIVATED
                    },
                    null,
                )
            startActivity(parameters.createIntent(this))
        } else {
            val parameters = PaymentFailedActivity.Parameters(SubscriptionEditFailure.AUTO_RENEW)
            startActivity(parameters.createIntent(this))
        }
    }
}
