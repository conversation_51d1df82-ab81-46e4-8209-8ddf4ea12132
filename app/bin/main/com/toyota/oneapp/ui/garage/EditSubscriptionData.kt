package com.toyota.oneapp.ui.garage

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.toyota.oneapp.model.subscription.CancellationDataPayload
import com.toyota.oneapp.model.subscription.SubscriptionUIItem

class EditSubscriptionData(
    val subscriptionItem: SubscriptionUIItem?,
    val cancellationDataPayload: CancellationDataPayload?,
) {
    val title = (subscriptionItem?.previewSubscriptionItem?.displayProductName ?: subscriptionItem?.name) ?: ""

    val description = subscriptionItem?.previewSubscriptionItem?.productDescription ?: ""

    fun createBundle(): Bundle {
        val bundle = Bundle()
        bundle.putParcelable(SUBSCRIPTION_ITEM_KEY, subscriptionItem)
        bundle.putParcelable(REFUND_RESPONSE_KEY, cancellationDataPayload)
        return bundle
    }

    fun <T> createIntent(
        context: Context,
        activityClass: Class<T>,
    ): Intent {
        val intent = Intent(context, activityClass)
        intent.putExtra(SUBSCRIPTION_ITEM_KEY, subscriptionItem)
        intent.putExtra(REFUND_RESPONSE_KEY, cancellationDataPayload)
        return intent
    }

    companion object {
        private const val SUBSCRIPTION_ITEM_KEY = "subscriptionItemKey"
        private const val REFUND_RESPONSE_KEY = "refundResponseKey"

        fun createFromBundle(bundle: Bundle?) =
            EditSubscriptionData(
                bundle?.getParcelable(SUBSCRIPTION_ITEM_KEY),
                bundle?.getParcelable(REFUND_RESPONSE_KEY),
            )

        fun createFromIntent(intent: Intent) =
            EditSubscriptionData(
                intent.getParcelableExtra(SUBSCRIPTION_ITEM_KEY),
                intent.getParcelableExtra(REFUND_RESPONSE_KEY),
            )
    } // companion object
} // EditSubscriptionData class
