package com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.detail.mapper

import android.content.Context
import com.toyota.oneapp.R
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.detail.CancelTrialSubscriptionDetailFragmentArgs
import com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.detail.model.CancelTrialSubscriptionDetailUIModel
import javax.inject.Inject

class CancelTrialSubscriptionDetailMapper
    @Inject
    constructor(
        private val context: Context,
    ) {
        fun map(
            args: CancelTrialSubscriptionDetailFragmentArgs,
            vehicleInfo: VehicleInfo,
        ): CancelTrialSubscriptionDetailUIModel {
            with(args) {
                val isSubscriptionAvailableToPurchase =
                    availableSubscriptions.any {
                        it.productLine == selectedSubscription.productLine
                    }

                return CancelTrialSubscriptionDetailUIModel(
                    subscription = selectedSubscription,
                    subscriptionStatus =
                        if (selectedSubscription.isPPOProduct == true) {
                            selectedSubscription.ppoDescription.orEmpty()
                        } else {
                            context.getString(R.string.trial_service)
                        },
                    isPrimarySubscriber = vehicleInfo.isPrimarySubscriber,
                    isPurchaseVisible = selectedSubscription.isExpiringSoon == true && isSubscriptionAvailableToPurchase,
                )
            }
        }
    }
