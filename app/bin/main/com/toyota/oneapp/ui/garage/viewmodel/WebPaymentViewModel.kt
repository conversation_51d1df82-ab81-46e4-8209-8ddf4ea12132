package com.toyota.oneapp.ui.garage.viewmodel

import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import androidx.lifecycle.ViewModel
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscription.RSATokenResponse
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager.WebPaymentType
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.util.Encoder
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@HiltViewModel
class WebPaymentViewModel
    @Inject
    constructor(
        private val subscriptionAPIManager: SubscriptionAPIManager,
        private val applicationData: ApplicationData,
        private val languageManager: LanguageManager,
        private val encoder: Encoder,
        private val analyticsLogger: AnalyticsLogger,
    ) : ViewModel() {
        @VisibleForTesting
        var default = false

        val navigationEvents = SingleLiveEvent<WebPaymentNavigationEvent>()

        val onOpenPrivacy = SingleLiveEvent<String>()
        val onLoadFailed = SingleLiveEvent<Unit>()
        val onSuccess = SingleLiveEvent<SuccessResult>()

        fun getRSASignature(
            paymentType: WebPaymentType,
            default: Boolean,
            accountId: String?,
            vehicleBrand: String,
            vehicleRegion: String,
            vehicleGeneration: String,
        ) {
            analyticsLogger.logEvent(
                when (paymentType) {
                    WebPaymentType.CREDIT_CARD -> AnalyticsEvent.ZUORA_ADD_CARD_PAYMENT_METHOD
                    WebPaymentType.ACH -> AnalyticsEvent.ZUORA_ADD_ACH_PAYMENT_METHOD
                },
            )
            this.default = default
            subscriptionAPIManager.sendGetZuoraRSAToken(
                vehicleBrand,
                vehicleRegion,
                paymentType,
                object : BaseCallback<RSATokenResponse>() {
                    override fun onSuccess(response: RSATokenResponse) {
                        analyticsLogger.logEvent(AnalyticsEvent.ZUORA_PAYMENT_TOKEN_RECEIVED)
                        navigationEvents.postValue(
                            WebPaymentNavigationEvent.GetRSASignatureSuccess(
                                BuildConfig.CY17PLUS_PAYMENT_BASE_URL +
                                    "?brand=$vehicleBrand" +
                                    "&appBrand=${BuildConfig.APP_BRAND}" +
                                    "&language=${languageManager.getCurrentLanguage()}" +
                                    "&region=$vehicleRegion" +
                                    "&tenantId=${response.payload.tenantId}" +
                                    "&token=${response.payload.token}" +
                                    "&key=${encoder.base64Encode(response.payload.key)}" +
                                    "&signature=${encoder.base64Encode(response.payload.signature)}" +
                                    "&paymentType=${paymentType.value}" +
                                    "&showDefaultPayment=$default" +
                                    "&gen=$vehicleGeneration" +
                                    if (accountId.isNullOrBlank()) "" else "&accountId=$accountId",
                            ),
                        )
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        navigationEvents.postValue(
                            WebPaymentNavigationEvent.GetRSASignatureFailed(
                                errorMsg,
                            ),
                        )
                    }
                },
            )
        }

        @WorkerThread
        fun webEvent(
            message: String,
            rfid: String,
            defaultPayment: Boolean,
        ) {
            LogTool.d(TAG, "message ----$message defaultPayment ----$defaultPayment")
            when (message) {
                MESSAGE_SUCCESS -> {
                    analyticsLogger.logEvent(AnalyticsEvent.ZUORA_PAYMENT_PAGE_ADD_PAYMENT_SUCCESS)
                    onSuccess.postValue(SuccessResult(rfid, if (default) defaultPayment else true))
                }
                MESSAGE_FAILED -> {
                    analyticsLogger.logEvent(AnalyticsEvent.ZUORA_PAYMENT_PAGE_ADD_PAYMENT_FAIL)
                    onLoadFailed.postCall()
                }
                MESSAGE_REDIRECT -> {
                    analyticsLogger.logEvent(AnalyticsEvent.ZUORA_PAYMENT_PAGE_PRIVACY)
                    onOpenPrivacy.postValue(rfid)
                }
            }
        }

        companion object {
            private const val TAG = "WebPaymentViewModel"
            private const val MESSAGE_SUCCESS = "success"
            private const val MESSAGE_FAILED = "failed"
            private const val MESSAGE_REDIRECT = "redirect"
        }
    }

data class SuccessResult(
    val token: String,
    val defaultPayment: Boolean,
)

sealed class WebPaymentNavigationEvent {
    data class GetRSASignatureFailed(
        val message: String?,
    ) : WebPaymentNavigationEvent()

    data class GetRSASignatureSuccess(
        val url: String,
    ) : WebPaymentNavigationEvent()
}
