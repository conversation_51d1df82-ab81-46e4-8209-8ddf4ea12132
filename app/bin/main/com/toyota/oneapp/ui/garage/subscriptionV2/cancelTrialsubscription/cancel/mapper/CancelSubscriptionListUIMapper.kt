package com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.cancel.mapper

import android.content.Context
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.cancel.model.CancelSubscriptionListItemUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.util.ToyotaConstants
import javax.inject.Inject

/**
 * A Mapper - Used to map Subscriptions to List of [CancelSubscriptionListUIMapper]
 */
class CancelSubscriptionListUIMapper
    @Inject
    constructor(
        private val context: Context,
        private val applicationData: ApplicationData,
    ) {
        fun map(
            trialSubscriptions: List<SubscriptionV2>,
            paidSubscriptions: List<SubscriptionV2>,
        ): List<CancelSubscriptionListItemUIModel> {
            val iconFillColor =
                if (applicationData.getSelectedVehicle()?.isToyotaBrand ==
                    true
                ) {
                    R.color.toyota_brand_color
                } else {
                    R.color.lexus_brand_color
                }
            val items = mutableListOf<CancelSubscriptionListItemUIModel>()

            // Trial.
            if (trialSubscriptions.isNotEmpty()) {
                items.add(
                    CancelSubscriptionListItemUIModel.Header(context.getString(R.string.trial_services)),
                )
                for (subscription in trialSubscriptions) {
                    val subTitle = "${context.getString(R.string.active)}\n${subscription.displayTerm}"
                    items.add(
                        CancelSubscriptionListItemUIModel.Service(
                            title = subscription.displayProductName,
                            subTitle = subTitle,
                            icon =
                                SubscriptionUtil.getIconForSubscriptionFromProductLine(
                                    subscription.productLine
                                        ?: "",
                                ),
                            iconFillColor = iconFillColor,
                            subscription = subscription,
                        ),
                    )
                }
            }

            // Paid.
            if (paidSubscriptions.isNotEmpty()) {
                items.add(
                    CancelSubscriptionListItemUIModel.Header(context.getString(R.string.paid_services)),
                )
                for (subscription in paidSubscriptions) {
                    var subTitle =
                        if (subscription.autoRenew == true) {
                            context.getString(R.string.Subscription_Auto_Renew_On)
                        } else {
                            context.getString(R.string.Subscription_Auto_Renew_Off)
                        }
                    subTitle += "\n"
                    subTitle += subscription.displayTerm
                    items.add(
                        if (subscription.category == ToyotaConstants.BUNDLE) {
                            CancelSubscriptionListItemUIModel.Bundle(
                                title = subscription.displayProductName,
                                subTitle = subTitle,
                                subscription = subscription,
                            )
                        } else {
                            CancelSubscriptionListItemUIModel.Service(
                                title = subscription.displayProductName,
                                subTitle = subTitle,
                                icon =
                                    SubscriptionUtil.getIconForSubscriptionFromProductLine(
                                        subscription.productLine
                                            ?: "",
                                    ),
                                iconFillColor = iconFillColor,
                                subscription = subscription,
                            )
                        },
                    )
                }
            }

            return items
        }
    }
