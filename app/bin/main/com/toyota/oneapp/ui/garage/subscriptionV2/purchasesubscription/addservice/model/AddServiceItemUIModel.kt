package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice.model

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription

/**
 * A Class used to populate AddService ListItem.
 */
data class AddServiceItemUIModel(
    val title: String,
    val subTitle: String?,
    @DrawableRes val icon: Int,
    @ColorRes val iconFillColor: Int,
    val subscription: AvailableSubscription,
)
