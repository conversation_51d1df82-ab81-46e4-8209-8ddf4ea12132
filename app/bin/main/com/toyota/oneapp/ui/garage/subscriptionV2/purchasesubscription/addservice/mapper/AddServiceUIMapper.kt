package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice.mapper

import android.content.Context
import com.toyota.oneapp.R
import com.toyota.oneapp.extensions.getLocale
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice.model.AddServiceItemUIModel
import com.toyota.oneapp.util.DoubleUtil.toDisplayPrice
import javax.inject.Inject

/**
 * A Class used to map List of [AvailableSubscription] to List of [AddServiceItemUIModel].
 */
class AddServiceUIMapper
    @Inject
    constructor(
        private val context: Context,
        private val languageManager: LanguageManager,
    ) {
        fun map(
            subscriptions: List<AvailableSubscription>,
            vehicle: VehicleInfo,
        ): List<AddServiceItemUIModel> {
            val itemUIModels = mutableListOf<AddServiceItemUIModel>()

            val iconFillColor = if (vehicle.isToyotaBrand) R.color.toyota_brand_color else R.color.lexus_brand_color
            val vehicleLocale = vehicle.getLocale(language = languageManager.getCurrentLanguage())

            subscriptions.forEach { subscription ->
                // SubTitle - subscription package details
                val subTitle: String?
                // For WiFi - We will not have package details.
                if (subscription.productLine == SubscriptionConstants.WIFI_CONNECT) {
                    // WIFI
                    subTitle = null
                } else {
                    // Other Subscriptions.
                    val subscriptionPackages = subscription.packages
                    var subscriptionPackageDetails = ""
                    subscriptionPackages.forEachIndexed { index, element ->
                        val duration =
                            context.getString(
                                SubscriptionUtil.getDisplayTermUnit(
                                    element.subscriptionTerm
                                        ?: "",
                                ),
                            )
                        val displayPrice = element.price?.toDisplayPrice(vehicleLocale)
                        subscriptionPackageDetails += ("$displayPrice/$duration")
                        if (index != (subscriptionPackages.size - 1)) {
                            subscriptionPackageDetails += (" " + context.getString(R.string.Common_or) + " ")
                        }
                    }
                    subTitle = subscriptionPackageDetails
                }

                val itemUIModel =
                    AddServiceItemUIModel(
                        title = subscription.displayProductName,
                        subTitle = subTitle,
                        icon =
                            SubscriptionUtil.getIconForSubscriptionFromProductLine(
                                subscription.productLine
                                    ?: "",
                            ),
                        iconFillColor = iconFillColor,
                        subscription = subscription,
                    )
                itemUIModels.add(itemUIModel)
            }
            return itemUIModels
        }
    }
