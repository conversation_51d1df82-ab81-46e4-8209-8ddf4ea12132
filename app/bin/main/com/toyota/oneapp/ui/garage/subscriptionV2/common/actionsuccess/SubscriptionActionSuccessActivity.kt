package com.toyota.oneapp.ui.garage.subscriptionV2.common.actionsuccess

import android.os.Bundle
import android.view.MenuItem
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivitySubscriptionActionSuccessBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.IntentUtil
import dagger.hilt.android.AndroidEntryPoint

/**
 * A Activity - Displays Success screen after Subscription Action success.
 */
@AndroidEntryPoint
class SubscriptionActionSuccessActivity : UiBaseActivity() {
    private lateinit var binding: ActivitySubscriptionActionSuccessBinding
    private lateinit var args: SubscriptionActionSuccessActivityArgs

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding =
            DataBindingUtil.setContentView(
                this,
                R.layout.activity_subscription_action_success,
            )

        initializeExtras()
        initializeViews()
        addListeners()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            navigateToDashboard()
            return true
        } else {
            return super.onOptionsItemSelected(item)
        }
    }

    override fun onBackPressed() {
        navigateToDashboard()
    }

    private fun initializeExtras() {
        args = SubscriptionActionSuccessActivityArgs.fromBundle(intent.extras!!)
    }

    private fun initializeViews() {
        performActivitySetup(binding.toolbar)

        binding.toolbar.title = args.title
        binding.toolbar.isVisible = args.title.isNotNullOrEmpty()
        binding.ivIcon.setImageResource(args.icon)
        binding.tvMsg1.text = args.msg1
        binding.tvMsg2.text = args.msg2
        binding.tvMsg2.isVisible = args.msg2.isNotNullOrEmpty()
        binding.tvReactivate.text = args.msg3
        binding.tvReactivate.isVisible = args.msg3.isNotNullOrEmpty()
        binding.btnAction.text = args.actionTxt
    }

    private fun addListeners() {
        binding.btnAction.setOnClickListener {
            navigateToDashboard()
        }
    }

    private fun navigateToDashboard() {
        startActivity(IntentUtil.getOADashBoardIntent(context = this, isDashboardRefresh = true))
        finish()
    }
}
