package com.toyota.oneapp.ui.garage.subscriptionV2.common.util

/**
 * A Object - Holds constants for Subscription screens.
 */
object SubscriptionConstants {
    const val ANALYTICS_KEY_SERVICE = "Service"
    const val ANALYTICS_KEY_SUBSCRIPTION_TYPE = "SubscriptionType"
    const val ANALYTICS_KEY_AUTO_RENEW = "Autorenew"

    const val SUBSCRIPTION_TERM_MONTH = "MTH"
    const val SUBSCRIPTION_TERM_YEAR = "YRLY"

    const val WIFI_CONNECT = "WIFI-CONNECT"
    val remoteConnectProductLine =
        listOf(
            // 21MM
            "PROD_EREMKEY",
            "PROD_EREMOTE",
            "PROD_REMKEY1",
            "PROD_REMKEY2",
            "PROD_REMOTELM",
            "PROD_REMOTELS",
            "PROD_REMOTEV2",
            "PROD_REMOTESERVICE",
            // 20TM
            "PROD_TMREMOTESERVICE",
            "PROD_TMEREMOTE",
            // 17CY+ & Legacy
            "PROD_REMOTESERVICE",
            "PROD_EREMOTE",
            "REMOTESERV",
            "LEXSCNRS",
            "LEXENFRS",
            // 21MM 2024 Tacoma
            "PROD_REMLSKEY",
            "PROD_B1_REMLSKEYNAV",
            "PROD_B2_REMLSKEYNAVSTRM",
            "PROD_B3_REMLSKEYSTRM",
        )
    val serviceConnectProductLines =
        listOf(
            "PROD_SERVICECONNECT",
        )
    val safetyConnectProductLines =
        listOf(
            // 21MM
            "PROD_SAFETYCONNECT",
            // 17CY+ & Legacy
            "PROD_SAFETYCONNECT",
            "TOYSCNCT",
            "LEXSCNCT",
            "SAFETYNDIAG",
        )
    val driveConnectProductLines =
        listOf(
            // 21MM
            "PROD_NAVPKG",
        )
    val destinationAssistProductLines =
        listOf(
            // 17CY+ & Legacy
            "PROD_DESTASSIST",
            "DESTASSIST",
        )
    val dynamicNaviProductLines =
        listOf(
            // 17CY+ & Legacy
            "PROD_NAVIGATION",
            "HYBRIDNAVIA",
            "HYBRIDNAVIB",
        )
    val wifiConnectProductLines =
        listOf(
            // 17CY+ & Legacy
            "PROD_WIFI",
            "WIFI-CONNECT",
        )
    val teammateProductLines =
        listOf(
            "PROD_TMPKG1",
        )
    val integratedStreamingLines =
        listOf(
            "PROD_STREAM",
        )
}
