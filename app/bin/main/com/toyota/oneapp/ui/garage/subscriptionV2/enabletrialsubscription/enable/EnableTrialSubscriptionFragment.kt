package com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.enable

import android.app.Activity
import android.content.Intent
import android.graphics.Typeface
import android.net.Uri
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.widget.LinearLayout
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.FragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentEnableTrialSubscriptionBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.vehicle.VehicleDetail
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.dataconsent.PackageInfoActivity
import com.toyota.oneapp.ui.dataconsent.VehicleSubscriptionAlertsContract
import com.toyota.oneapp.ui.dataconsent.VehicleSubscriptionAlertsResult
import com.toyota.oneapp.ui.dataconsent.activities.CombinedDataConsentActivity
import com.toyota.oneapp.ui.garage.BillingAddressActivity
import com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.declinetrialconfirmation.DeclineTrialConfirmationDialogFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.enable.adapter.EnableTrialSubscriptionAdapter
import com.toyota.oneapp.ui.garage.wifiSubscription.ActiveWifiSubscriptionExistActivity
import com.toyota.oneapp.util.Brand
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

/**
 * This fragment used to enable trial subscription.
 */
@AndroidEntryPoint
class EnableTrialSubscriptionFragment :
    BaseDataBindingFragment<FragmentEnableTrialSubscriptionBinding>(),
    EnableTrialSubscriptionAdapter.OnItemClickListener {
    private val args: EnableTrialSubscriptionFragmentArgs by navArgs()

    private var isAppUpdateDialogShown = false

    private val viewModel: EnableTrialSubscriptionViewModel by viewModels()

    private lateinit var adapter: EnableTrialSubscriptionAdapter
    private var declineTrialConfirmationDialogFragment: DeclineTrialConfirmationDialogFragment? =
        null

    private val alertsActivityResult =
        registerForActivityResult<Intent, ActivityResult>(VehicleSubscriptionAlertsContract()) { result ->
            if (result is VehicleSubscriptionAlertsResult.Success) {
                viewModel.onAlertsAccepted()
            }
        }

    private val getBillingAddress =
        registerForActivityResult<Intent, ActivityResult>(
            ActivityResultContracts.StartActivityForResult(),
            ActivityResultCallback { result: ActivityResult ->
                if (result.resultCode == Activity.RESULT_OK) {
                    viewModel.onBillingAddressReceived()
                }
            },
        )

    private val getDataConsent =
        registerForActivityResult<Intent, ActivityResult>(
            ActivityResultContracts.StartActivityForResult(),
            ActivityResultCallback { result: ActivityResult ->
                if (result.resultCode == Activity.RESULT_OK) {
                    val intent = result.data
                    if (intent != null) {
                        val consents =
                            intent.getSerializableExtra(ToyotaConstants.CONSENTS) as List<ConsentRequestItem>
                        viewModel.onDataConsentReceived(consents) // consents won't be null here
                    }
                }
            },
        )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
        observeBaseEvents(viewModel)
        registerForResultListener()
    }

    override fun onViewBound(
        binding: FragmentEnableTrialSubscriptionBinding,
        savedInstance: Bundle?,
    ) {
        binding.viewModel = viewModel

        initializeViews()
        addListeners()
        setUpViewModelBindings()
    }

    override fun getLayout(): Int = R.layout.fragment_enable_trial_subscription

    override fun onCreateOptionsMenu(
        menu: Menu,
        inflater: MenuInflater,
    ) {
        inflater.inflate(R.menu.menu_enable_trial_subscription, menu)
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)
        menu.findItem(R.id.no_thanks).isVisible = viewModel.isNoThanksVisible()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            R.id.no_thanks -> {
                onNoThanks()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }

    // EnableTrialSubscriptionAdapter.OnItemClickListener
    override fun onSubscriptionServiceItemClick(subscription: SubscriptionV2) {
        viewModel.onSubscriptionServiceItemClick(subscription)
    }

    override fun onResume() {
        super.onResume()
        if (isAppUpdateDialogShown) {
            viewModel.showTheAppUpdateDialogOnResume()
        }
    }

    private fun registerForResultListener() {
        childFragmentManager.setFragmentResultListener(
            DeclineTrialConfirmationDialogFragment.REQUEST_KEY_DECLINE_TRIAL_CONFIRMATION_DIALOG,
            this,
            FragmentResultListener { _, result ->
                val isConfirm =
                    result.getBoolean(
                        DeclineTrialConfirmationDialogFragment.EXTRA_IS_CONFIRM,
                        false,
                    )
                if (isConfirm) {
                    onUserConfirmToDeclineTrial()
                }
            },
        )
    }

    private fun initializeViews() {
        initConnectedServicesInfo()

        viewModel.checkForTrialServices()
    }

    private fun initConnectedServicesInfo() {
        viewDataBinding.tvTrialInfo.text =
            getString(R.string.Subscription_trial_package_information_prompt_part1)
        val spannableString =
            SpannableString(
                when (args.vehicle.brand.toUpperCase()) {
                    Brand.LEXUS.appBrand -> getString(R.string.Subscription_available_services_lexus)
                    Brand.SUBARU.appBrand ->
                        getString(
                            R.string.Subscription_available_services_information,
                        )
                    else -> getString(R.string.Subscription_available_services_toyota)
                },
            )
        spannableString.setSpan(
            StyleSpan(Typeface.BOLD),
            0,
            spannableString.length,
            Spanned.SPAN_INCLUSIVE_INCLUSIVE,
        )
        spannableString.setSpan(
            ForegroundColorSpan(requireActivity().getColor(R.color.high_light)),
            0,
            spannableString.length,
            Spanned.SPAN_INCLUSIVE_INCLUSIVE,
        )
        viewDataBinding.tvTrialInfo.append(spannableString)

        viewDataBinding.tvDisclaimer.text =
            String.format(
                getString(R.string.enable_trial_subscription_disclaimer),
                when (args.vehicle.brand.toUpperCase()) {
                    Brand.LEXUS.appBrand -> getString(R.string.Common_brand_name_lexus)
                    Brand.SUBARU.appBrand -> getString(R.string.Common_brand_name_subaru)
                    else -> getString(R.string.Common_brand_name_toyota)
                },
            )
        viewDataBinding.tvDisclaimer.visibility =
            if (ToyotaConstants.REGION_CA.equals(args.vehicle.region, ignoreCase = true)) {
                View.VISIBLE
            } else {
                View.GONE
            }

        // set click listener
        viewDataBinding.tvTrialInfo.setOnClickListener {
            val vehicleDetail = VehicleDetail()
            vehicleDetail.region = args.vehicle.region
            vehicleDetail.isNonCvtVehicle = args.vehicle.isNonCvtVehicle

            val intent = Intent(activity, PackageInfoActivity::class.java)
            intent.putExtra("IsToyota", args.vehicle.isToyotaBrand)
            intent.putExtra("isSubaru", args.vehicle.isSubaruBrand)
            intent.putExtra("Vehicle", vehicleDetail)
            startActivity(intent)
        }
    }

    private fun addListeners() {
        viewDataBinding.btnContinue.setOnClickListener {
            viewModel.onSubmit()
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.event.observe(
            viewLifecycleOwner,
            Observer {
                handleViewModelEvents(it)
            },
        )
    }

    private fun showUserConfirmationToPurchaseServicesInAddVehicleFlow() {
        DialogUtil.showDialog(
            requireActivity(),
            null,
            getString(R.string.TrialSubscriptionPage_No_Subscriptions_Available_Purchase),
            getString(R.string.Common_continue),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    viewModel.onUserConfirmedToPurchaseServices()
                }

                override fun onCancelClick() {
                    viewModel.onUserDeclinedToPurchaseServices()
                }
            },
            false,
        )
    }

    private fun showNoPackagesAvailableInAddVehicleFlow() {
        DialogUtil.showDialog(
            requireActivity(),
            "",
            getString(R.string.TrialSubscriptionPage_No_Subscriptions_Available),
            getString(R.string.Common_call),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    ToyUtil.phoneCall(
                        requireActivity(),
                        ToyUtil.getPhoneNO(
                            requireActivity(),
                            args.vehicle.region,
                            args.vehicle.brand,
                        ),
                    )
                }

                override fun onCancelClick() {
                    viewModel.onUserDeclinedToPurchaseServices()
                }
            },
            false,
        )
    }

    private fun handleViewModelEvents(event: EnableTrialSubscriptionViewModel.Event) {
        when (event) {
            is EnableTrialSubscriptionViewModel.Event.ShowTrialServices -> {
                adapter =
                    EnableTrialSubscriptionAdapter(
                        args.trialSubscriptions.toList(),
                        args.vehicle,
                        this,
                    )
                viewDataBinding.rvServices.run {
                    layoutManager = LinearLayoutManager(requireContext())
                    adapter = <EMAIL>
                    addItemDecoration(
                        DividerItemDecoration(
                            requireContext(),
                            LinearLayout.VERTICAL,
                        ),
                    )
                }
            }
            is EnableTrialSubscriptionViewModel.Event.ShowUserConfirmationToPurchaseServicesInAddVehicleFlow -> {
                showUserConfirmationToPurchaseServicesInAddVehicleFlow()
            }
            is EnableTrialSubscriptionViewModel.Event.ShowNoPackagesAvailableInAddVehicleFlow -> {
                showNoPackagesAvailableInAddVehicleFlow()
            }
            is EnableTrialSubscriptionViewModel.Event.NavigateToPurchaseServiceScreen -> {
                navigateToPurchaseServiceScreen(event)
            }
            is EnableTrialSubscriptionViewModel.Event.NavigateToServiceDetailScreen -> {
                navigateToSubscriptionServiceDetailScreen(event)
            }
            is EnableTrialSubscriptionViewModel.Event.NavigateToAlertsScreen -> {
                navigateToAlertsScreen(event)
            }
            is EnableTrialSubscriptionViewModel.Event.NavigateToBillingAddressScreen -> {
                navigateToGetBillingAddressScreen()
            }
            is EnableTrialSubscriptionViewModel.Event.NavigateToDataConsentScreen -> {
                navigateToDataConsentScreen(event)
            }
            is EnableTrialSubscriptionViewModel.Event.NavigateToEnableTrialSuccessScreen -> {
                navigateToEnableTrialSuccessScreen(event)
            }
            is EnableTrialSubscriptionViewModel.Event.NavigateToWaiveSubscriptionSuccessScreen -> {
                navigateToWaiveSubscriptionSuccessScreen(event)
            }
            is EnableTrialSubscriptionViewModel.Event.NavigateToActiveWifiSubscriptionFound -> {
                navigateToActiveWifiSubscriptionFound(event)
            }
            is EnableTrialSubscriptionViewModel.Event.NavigateToAddVehicleSuccessScreen -> {
                navigateAToAddVehicleSuccessScreen(event)
            }
            is EnableTrialSubscriptionViewModel.Event.ShowAppUpdateRequired -> {
                showAppUpdateRequired()
            }
            is EnableTrialSubscriptionViewModel.Event.ShowConnectedServicesDecline -> {
                declineTrialConfirmationDialogFragment?.dismiss()
                declineTrialConfirmationDialogFragment =
                    DeclineTrialConfirmationDialogFragment
                        .getInstance(
                            isToyotaBrand = args.vehicle.isToyotaBrand,
                            is21MMVehicle = args.vehicle.is21MMVehicle,
                            title = event.datConsent?.title,
                            body = event.datConsent?.body,
                        ).also {
                            it.show(childFragmentManager, DeclineTrialConfirmationDialogFragment.TAG)
                        }
            }
        }.exhaustive
    }

    private fun navigateToPurchaseServiceScreen(event: EnableTrialSubscriptionViewModel.Event.NavigateToPurchaseServiceScreen) {
        if (BuildConfig.SUBSCRIPTION_PURCHASE_MULTI_PRODUCT_ENABLED) {
            val action =
                EnableTrialSubscriptionFragmentDirections.actionEnableTrialSubscriptionFragmentToSelectServicesForPurchaseFragment(
                    vehicle = args.vehicle,
                    isAddVehicleFlow = args.isAddVehicleFlow,
                    trialSubscriptions = event.trialSubscriptions.toTypedArray(),
                    paidSubscriptions = event.paidSubscriptions.toTypedArray(),
                    availableSubscriptions = event.availableSubscription.toTypedArray(),
                    accessToken = event.accessToken,
                    taxDisclaimer = args.payload?.taxDisclaimer,
                    isAzure = event.isAzure ?: false,
                    alerts = event.alerts?.toTypedArray(),
                )
            findNavController().navigate(action)
        } else {
            val action =
                EnableTrialSubscriptionFragmentDirections.actionEnableTrialSubscriptionFragmentToAddServiceFragment(
                    vehicle = args.vehicle,
                    isAddVehicleFlow = args.isAddVehicleFlow,
                    trialSubscriptions = event.trialSubscriptions.toTypedArray(),
                    paidSubscriptions = event.paidSubscriptions.toTypedArray(),
                    availableSubscriptions = event.availableSubscription.toTypedArray(),
                    accessToken = event.accessToken,
                    isAzure = event.isAzure ?: false,
                    alerts = event.alerts?.toTypedArray(),
                )
            findNavController().navigate(action)
        }
    }

    private fun navigateToSubscriptionServiceDetailScreen(event: EnableTrialSubscriptionViewModel.Event.NavigateToServiceDetailScreen) {
        val action =
            EnableTrialSubscriptionFragmentDirections.actionEnableTrialSubscriptionFragmentToSubscriptionServiceDetailFragment(
                vehicle = args.vehicle,
                uiModel = event.uiModel,
            )
        findNavController().navigate(action)
    }

    private fun navigateToAlertsScreen(event: EnableTrialSubscriptionViewModel.Event.NavigateToAlertsScreen) {
        alertsActivityResult.launch(event.vehicleSubscriptionAlertsArguments)
    }

    private fun navigateToGetBillingAddressScreen() {
        val intent = Intent(requireContext(), BillingAddressActivity::class.java)
        getBillingAddress.launch(intent)
    }

    private fun navigateToDataConsentScreen(event: EnableTrialSubscriptionViewModel.Event.NavigateToDataConsentScreen) {
        val intent =
            CombinedDataConsentActivity.getIntent(
                context = requireContext(),
                vehicle = event.vehicle,
                isPaidFlow = false,
                trialSubscriptions = event.trialSubscriptions,
                subscriptionGetPayload = event.subscriptionGetPayload,
                isGetConsentInfoAsResult = true,
            )
        getDataConsent.launch(intent)
    }

    private fun navigateToEnableTrialSuccessScreen(event: EnableTrialSubscriptionViewModel.Event.NavigateToEnableTrialSuccessScreen) {
        if (event.isAddVehicleFlow) {
            val action =
                EnableTrialSubscriptionFragmentDirections.actionEnableTrialSubscriptionFragmentToAddVehicleSuccessFragment(
                    vehicle = args.vehicle,
                    icon = R.drawable.ic_success_check_mark_green,
                    title = getString(R.string.Subscription_added_success_title),
                    msg = getString(R.string.Subscription_added_success_trial_subtitle),
                    ctaText = getString(R.string.AddVehicle_finish_setup),
                    isAddVehicleFlow = args.isAddVehicleFlow,
                )
            findNavController().navigate(action)
        } else {
            val action =
                EnableTrialSubscriptionFragmentDirections.actionEnableTrialSubscriptionFragmentToSubscriptionActionSuccessActivity(
                    vehicle = args.vehicle,
                    icon = R.drawable.ic_success_check_mark_green,
                    title = null,
                    msg1 = getString(R.string.Subscription_added_success_title),
                    msg2 = getString(R.string.Subscription_added_success_trial_subtitle),
                    actionTxt = getString(R.string.ServiceCampaign_back_to_dashboard),
                    msg3 =
                        if (viewModel.isWaivedEarlier()) {
                            getString(
                                R.string.Subscription_added_success_waived_earlier,
                            )
                        } else {
                            null
                        },
                )
            findNavController().navigate(action)
        }
    }

    private fun navigateToWaiveSubscriptionSuccessScreen(
        event: EnableTrialSubscriptionViewModel.Event.NavigateToWaiveSubscriptionSuccessScreen,
    ) {
        val action =
            if (event.isAddVehicleFlow) {
                EnableTrialSubscriptionFragmentDirections.actionEnableTrialSubscriptionFragmentToAddVehicleSuccessFragment(
                    vehicle = args.vehicle,
                    icon = R.drawable.ic_cdma_decline,
                    title = getString(R.string.add_vehicle_flow_trial_declined_msg1),
                    msg = getString(R.string.add_vehicle_flow_trial_declined_msg2),
                    ctaText = getString(R.string.Common_continue),
                    isAddVehicleFlow = args.isAddVehicleFlow,
                )
            } else {
                EnableTrialSubscriptionFragmentDirections.actionEnableTrialSubscriptionFragmentToSubscriptionActionSuccessActivity(
                    vehicle = args.vehicle,
                    icon = R.drawable.ic_cdma_decline,
                    title = "",
                    msg1 = getString(R.string.add_vehicle_flow_trial_declined_msg1),
                    msg2 = getString(R.string.add_vehicle_flow_trial_declined_msg2),
                    actionTxt = getString(R.string.ServiceCampaign_back_to_dashboard),
                    msg3 = null,
                )
            }
        findNavController().navigate(action)
    }

    private fun onNoThanks() {
        viewModel.fetchCombinedDataConsents()
    }

    private fun onUserConfirmToDeclineTrial() {
        viewModel.onUserConfirmToDeclineTrial()
    }

    private fun navigateToActiveWifiSubscriptionFound(event: EnableTrialSubscriptionViewModel.Event.NavigateToActiveWifiSubscriptionFound) {
        val intent =
            ActiveWifiSubscriptionExistActivity.getIntent(
                requireContext(),
                trialSubscriptions = event.trialSubscriptions,
                vehicleGeneration = event.vehicle.generation,
                vehicleRegion = event.vehicle.region,
                isMyGarageFlow = true,
            )
        startActivity(intent)
        requireActivity().finish()
    }

    private fun navigateAToAddVehicleSuccessScreen(event: EnableTrialSubscriptionViewModel.Event.NavigateToAddVehicleSuccessScreen) {
        val action =
            EnableTrialSubscriptionFragmentDirections.actionEnableTrialSubscriptionFragmentToAddVehicleSuccessFragment(
                vehicle = args.vehicle,
                icon = R.drawable.ic_success_check_mark_green,
                title = getString(R.string.thank_you),
                msg = getString(event.msg),
                ctaText = getString(R.string.AddVehicle_finish_setup),
                isAddVehicleFlow = args.isAddVehicleFlow,
            )
        findNavController().navigate(action)
    }

    private fun showAppUpdateRequired() {
        if (!isAppUpdateDialogShown) {
            DialogUtil.showDialog(
                requireActivity(),
                getString(R.string.App_Update_Required),
                String.format(
                    getString(R.string.App_Update_Available_Message),
                    getString(R.string.app_name),
                ),
                getString(R.string.Common_update_now),
                object : OnCusDialogInterface {
                    override fun onConfirmClick() {
                        isAppUpdateDialogShown = false
                        startActivity(
                            Intent(
                                Intent.ACTION_VIEW,
                                Uri.parse(ToyotaConstants.APP_GOOGLE_PLAY_URL),
                            ),
                        )
                    }

                    override fun onCancelClick() {
                    }
                },
                false,
            )
        }
        isAppUpdateDialogShown = true
    }
}
