package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemAddServiceBinding
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice.model.AddServiceItemUIModel
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

/**
 * This class is used to show list of available services in add service screen
 */
class AddServiceAdapter(
    private var items: List<AddServiceItemUIModel>,
    private val onItemCLickListener: OnItemClickListener,
) : RecyclerView.Adapter<AddServiceAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<AddServiceItemUIModel> {
    override fun getItemCount(): Int = items.size

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding =
            ItemAddServiceBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.onBind(items[position])
    }

    inner class ViewHolder(
        private val binding: ItemAddServiceBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        init {
            itemView.setOnClickListener {
                onItemCLickListener.onServiceItemClick(items[adapterPosition])
            }
        }

        fun onBind(item: AddServiceItemUIModel) {
            binding.tvProductName.text = item.title
            binding.tvSubtitle.isVisible = item.subTitle != null
            binding.tvSubtitle.text = item.subTitle
            binding.ivIcon.setImageResource(item.icon)
        }
    }

    override fun setData(data: List<AddServiceItemUIModel>?) {
        this.items = data ?: emptyList()
        notifyDataSetChanged()
    }

    interface OnItemClickListener {
        fun onServiceItemClick(item: AddServiceItemUIModel)
    }
}
