package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.selectsubscription

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.extensions.getLocale
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.subscription.BillingAddress
import com.toyota.oneapp.model.subscription.CY17CalculateTaxResponse
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.model.subscriptionV2.SubscriptionPackage
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.dataconsent.VehicleSubscriptionAlertsArguments
import com.toyota.oneapp.ui.garage.paymentOptions.model.PaymentCY17PlusResult
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Args
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Result
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import java.util.Locale
import javax.inject.Inject

/**
 *  ViewModel - for [SelectSubscriptionFragment]
 */
@HiltViewModel
class SelectSubscriptionViewModel
    @Inject
    constructor(
        private val savedStateHandle: SavedStateHandle,
        private val combinedDataConsentRepository: CombinedDataConsentRepository,
        private val preferenceModel: OneAppPreferenceModel,
        private val languageManager: LanguageManager,
        private val analyticsLogger: AnalyticsLogger,
        private val applicationData: ApplicationData? = null,
    ) : BaseViewModel() {
        sealed class Event {
            data class NavigateToAlerts(
                val vehicleSubscriptionAlertsArguments: VehicleSubscriptionAlertsArguments,
            ) : Event()

            data class NavigateToGetDateConsentScreen(
                val trialSubscriptions: Array<SubscriptionV2>,
            ) : Event() {
                override fun equals(other: Any?): Boolean {
                    if (this === other) return true
                    if (other !is NavigateToGetDateConsentScreen) return false
                    return trialSubscriptions.contentEquals(other.trialSubscriptions)
                }

                override fun hashCode(): Int {
                    return trialSubscriptions.contentHashCode()
                }

                override fun toString(): String {
                    return super.toString()
                }
            }

            object NavigateToGetBillingAddress : Event()

            data class NavigateToGetPaymentInfoScreenForCY17(
                val args: PaymentCY17Args,
            ) : Event()

            object NavigateToGetPaymentInfoScreenForCY17Plus : Event()

            data class NavigateToConfirmPaymentScreenForCY17(
                val subscription: AvailableSubscription,
                val subscriptionPackage: SubscriptionPackage,
                val consents: List<ConsentRequestItem>,
                val billingAddress: BillingAddress,
                val paymentAccessToken: String,
                val taxResponse: CY17CalculateTaxResponse,
                val accessToken: String?,
            ) : Event()

            data class NavigateToConfirmPaymentScreenForCY17Plus(
                val subscription: AvailableSubscription,
                val subscriptionPackage: SubscriptionPackage,
                val consents: List<ConsentRequestItem>,
                val billingAddress: BillingAddress,
                val paymentID: String,
                val isDefaultPayment: Boolean,
                val paymentRecord: PaymentRecord?,
                val isAutoRenew: Boolean,
            ) : Event()

            object NavigateToWaiveSubscriptionSuccessScreen : Event()

            data class NavigateToMyGarageScreen(
                val isMyGarageRefresh: Boolean,
            ) : Event()

            data class NavigateToActiveWifiSubscriptionFound(
                val trialSubscriptions: Array<SubscriptionV2>,
                val vehicle: VehicleInfo,
            ) : Event()
        }

        private val args = SelectSubscriptionFragmentArgs.fromSavedStateHandle(savedStateHandle)

        private var _productName = MutableLiveData<String>()
        private val _packages = MutableLiveData<List<SubscriptionPackage>>()
        private val _isAutoRenewVisible = MutableLiveData<Boolean>()
        private val _isContinueToPurchaseEnabled = MutableLiveData<Boolean>()
        private val _clearSelection = SingleLiveEvent<Boolean>()
        private val _event = SingleLiveEvent<Event>()

        private var selectedPackage: SubscriptionPackage? = null
        private var consents: List<ConsentRequestItem>? = null
        private var billingAddress: BillingAddress? = null

        val productName: LiveData<String>
            get() = _productName
        val packages: LiveData<List<SubscriptionPackage>>
            get() = _packages
        val isAutoRenewVisible: LiveData<Boolean>
            get() = _isAutoRenewVisible
        val autoRenew = MutableLiveData<Boolean>()
        val isContinueToPurchaseEnabled: LiveData<Boolean>
            get() = _isContinueToPurchaseEnabled
        val clearSelection: LiveData<Boolean>
            get() = _clearSelection
        val event: LiveData<Event>
            get() = _event

        init {
            _productName.value = args.selectedSubscription.displayProductName
            _packages.value = args.selectedSubscription.packages
            // Show AutoRenew - Only to CY17PLUS vehicle & Subscription should be renewable.
            _isAutoRenewVisible.value = (!args.vehicle.isCY17) && (args.selectedSubscription.renewable == true)
            autoRenew.value = true
            _isContinueToPurchaseEnabled.value = false
        }

        fun getVehicleLocale(): Locale = args.vehicle.getLocale(language = languageManager.getCurrentLanguage())

        fun onSubscriptionSelected(selectedPackage: SubscriptionPackage) {
            this.selectedPackage = selectedPackage
            _isContinueToPurchaseEnabled.value = true
        }

        fun onSubscriptionDeSelected() {
            selectedPackage = null
            _isContinueToPurchaseEnabled.value = false
        }

        fun onContinueToPurchase() {
            analyticsLogger.logEvent(
                AnalyticsEvent.MANAGE_SUBSCRIPTION_PURCHASE_FLOW_SUBSCRIPTION_TYPE,
                SubscriptionConstants.ANALYTICS_KEY_SUBSCRIPTION_TYPE to selectedPackage?.displaySubscriptionTerm,
            )
            analyticsLogger.logEvent(
                AnalyticsEvent.MANAGE_SUBSCRIPTION_PURCHASE_FLOW_AUTO_RENEW,
                SubscriptionConstants.ANALYTICS_KEY_AUTO_RENEW to autoRenew.value,
            )

            if (!args.alerts.isNullOrEmpty()) {
                val allAvailableSubscriptionNames = args.availableSubscriptions.joinToString { it.displayProductName }
                _event.value =
                    Event.NavigateToAlerts(
                        vehicleSubscriptionAlertsArguments =
                            VehicleSubscriptionAlertsArguments(
                                allAvailableSubscriptionNames,
                                ArrayList(args.alerts!!.toList()),
                            ),
                    )
            } else {
                _event.value =
                    Event.NavigateToGetDateConsentScreen(
                        trialSubscriptions = args.trialSubscriptions,
                    )
            }
        }

        fun onDataConsentReceived(consents: List<ConsentRequestItem>) {
            this.consents = consents

            if (SubscriptionUtil.checkIsMasterConsentAccepted(consents)) {
                _event.value = Event.NavigateToGetBillingAddress
            } else {
                // Master consent - Declined.
                if (!args.vehicle.isCY17) {
                    // For CY17Plus - Waive subscription.
                    waiveSubscriptions()
                } else {
                    // For CY17 - Navigate to MyGarage screen.
                    _event.value = Event.NavigateToMyGarageScreen(isMyGarageRefresh = false)
                }
            }
        }

        private fun waiveSubscriptions() {
            // TODO:Check isCPOEligible flag
            val subscriptionGetPayload =
                SubscriptionUtil.formSubscriptionGetPayload(
                    vehicle = args.vehicle,
                    guid = preferenceModel.getGuid(),
                    trialSubscriptions = args.trialSubscriptions.toList(),
                    accessToken = args.accessToken ?: "",
                    isCPOEligible = args.isCPOEligible,
                    isPPOEligible = args.isPPOEligible,
                )
            val capabilities = args.vehicle.capabilityItems
            val existingSubscriptions = args.vehicle.subscriptions
            if (existingSubscriptions.isNullOrEmpty().not()) {
                val type = existingSubscriptions[0].type
                val eventValue =
                    if (type == "PAID") {
                        AnalyticsEventParam.PAID
                    } else {
                        AnalyticsEventParam.TRIAL
                    }
                analyticsLogger.logEventWithParameter(
                    AnalyticsEventParam.CONNECTED_SERVICES_WAIVE,
                    eventValue,
                )
            }

            viewModelScope.launch {
                showProgress()
                val resource =
                    combinedDataConsentRepository.waiveSubscription(
                        isWaiver = true,
                        asiCode = applicationData?.getSelectedVehicle()?.asiCode.orEmpty(),
                        hwtType = applicationData?.getSelectedVehicle()?.hwType.orEmpty(),
                        subscriptionGetPayload = subscriptionGetPayload,
                        capabilities = capabilities,
                    )

                when (resource) {
                    is Resource.Success -> {
                        hideProgress()
                        _event.postValue(Event.NavigateToWaiveSubscriptionSuccessScreen)
                    }

                    is Resource.Failure -> {
                        hideProgress()
                        if (ToyotaConstants.WAIVE_SUBSCRIPTION_ATT_ACTIVE_WIFI_ERROR_CODE.equals(
                                resource.error?.responseCode,
                                true,
                            )
                        ) {
                            _event.value =
                                Event.NavigateToActiveWifiSubscriptionFound(
                                    trialSubscriptions = args.trialSubscriptions,
                                    vehicle = args.vehicle,
                                )
                        } else {
                            showErrorMessage(resource.message)
                        }
                    }

                    is Resource.Loading -> {}
                }
            }
        }

        fun onBillingAddressReceived(billingAddress: BillingAddress) {
            this.billingAddress = billingAddress
            if (args.vehicle.isCY17) {
                val args =
                    PaymentCY17Args(
                        vehicle = args.vehicle,
                        subscriptionPackages = listOf(selectedPackage!!),
                        billingAddress = billingAddress,
                        accessToken = args.accessToken.orEmpty(),
                        isAzure = if (args.isAddVehicleFlow) args.isAzure else args.vehicle.isAzure,
                    )
                _event.value = Event.NavigateToGetPaymentInfoScreenForCY17(args)
            } else {
                _event.value = Event.NavigateToGetPaymentInfoScreenForCY17Plus
            }
        }

        fun onPaymentInfoReceivedForCY17(result: PaymentCY17Result) {
            _event.value =
                Event.NavigateToConfirmPaymentScreenForCY17(
                    subscription = args.selectedSubscription,
                    subscriptionPackage = selectedPackage!!,
                    consents = consents!!,
                    billingAddress = billingAddress!!,
                    paymentAccessToken = result.paymentAccessToken,
                    taxResponse = result.calculateTaxResponse,
                    accessToken = args.accessToken,
                )
        }

        fun onPaymentInfoReceivedForCY17Plus(result: PaymentCY17PlusResult) {
            _event.value =
                Event.NavigateToConfirmPaymentScreenForCY17Plus(
                    subscription = args.selectedSubscription,
                    subscriptionPackage = selectedPackage!!,
                    consents = consents!!,
                    billingAddress = billingAddress!!,
                    paymentID = result.paymentId,
                    isDefaultPayment = result.isDefaultPayment,
                    paymentRecord = result.paymentRecord,
                    isAutoRenew = autoRenew.value ?: true,
                )
        }
    }
