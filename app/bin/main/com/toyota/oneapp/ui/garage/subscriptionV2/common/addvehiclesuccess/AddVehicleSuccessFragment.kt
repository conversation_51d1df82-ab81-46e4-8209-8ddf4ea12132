package com.toyota.oneapp.ui.garage.subscriptionV2.common.addvehiclesuccess

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentAddVehicleSuccessBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.util.GlideUtil
import com.toyota.oneapp.util.GlideUtil.CropTransparentTransform
import com.toyota.oneapp.util.IntentUtil
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AddVehicleSuccessFragment : BaseDataBindingFragment<FragmentAddVehicleSuccessBinding>() {
    private val viewModel: AddVehicleSuccessViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentAddVehicleSuccessBinding,
        savedInstance: Bundle?,
    ) {
        viewModel.getVehicles()
        setUpViewModelBindings()
    }

    override fun getLayout(): Int = R.layout.fragment_add_vehicle_success

    fun setUpViewModelBindings() {
        with(viewModel) {
            navigationEvent.observe(
                viewLifecycleOwner,
                Observer {
                    handleVMNavigationEvent(it)
                },
            )
        }
        viewModel.uiModel.observe(viewLifecycleOwner) { uiModel ->
            viewDataBinding.ivSuccessIcon.setImageResource(uiModel.icon)
            viewDataBinding.tvSuccessTitle.text = uiModel.title
            viewDataBinding.tvSuccessSubtitle.text = uiModel.msg
            GlideUtil.loadImage(
                context,
                uiModel.vehicle.image,
                CropTransparentTransform(),
                R.drawable.car_placeholder,
                viewDataBinding.ivVehicleImage,
            )
            viewDataBinding.tvVin.text = uiModel.vehicle.vin
            viewDataBinding.vDivider.visibility = if (uiModel.vehicle.vin != null) View.VISIBLE else View.INVISIBLE
            viewDataBinding.btnFinishSetup.text = uiModel.ctaText
            viewDataBinding.btnFinishSetup.visibility = if (uiModel.ctaText != null) View.VISIBLE else View.INVISIBLE
        }
        viewModel.modelText.observe(viewLifecycleOwner) { model ->
            viewDataBinding.tvModel.text = model
        }
        viewDataBinding.btnFinishSetup.setOnClickListener {
            viewModel.onFinishSetUpClicked()
        }
    }

    private fun handleVMNavigationEvent(navigationEvent: AddVehicleSuccessViewModel.NavigationEvent) {
        when (navigationEvent) {
            is AddVehicleSuccessViewModel.NavigationEvent.NavigateToDashboardScreen -> {
                navigateToDashboardScreen()
            }
        }
    }

    private fun navigateToDashboardScreen() {
        startActivity(
            IntentUtil.getOADashBoardIntent(
                context = activityContext,
                isDashboardRefresh = true,
                newAddVehicle = true,
            ),
        )
    }
}
