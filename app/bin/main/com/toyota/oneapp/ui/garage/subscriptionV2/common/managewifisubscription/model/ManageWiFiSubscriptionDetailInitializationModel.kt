package com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.model

import android.os.Parcelable
import com.toyota.oneapp.model.subscriptionV2.SubscriptionType
import kotlinx.parcelize.Parcelize

@Parcelize
data class ManageWiFiSubscriptionDetailInitializationModel(
    val productName: String,
    val productLongDesc: String,
    val productImageUrl: String,
    val subscriptionType: SubscriptionType,
    val externalTargetUrl: String,
    val externalContactDetail: String,
) : Parcelable
