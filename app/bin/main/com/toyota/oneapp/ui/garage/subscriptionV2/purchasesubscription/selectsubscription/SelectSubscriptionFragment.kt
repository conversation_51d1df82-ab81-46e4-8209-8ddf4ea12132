package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.selectsubscription

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.selection.SelectionPredicates
import androidx.recyclerview.selection.SelectionTracker
import androidx.recyclerview.selection.StorageStrategy
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentSelectSubscriptionBinding
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.subscription.BillingAddress
import com.toyota.oneapp.model.subscriptionV2.SubscriptionPackage
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.dataconsent.VehicleSubscriptionAlertsContract
import com.toyota.oneapp.ui.dataconsent.VehicleSubscriptionAlertsResult
import com.toyota.oneapp.ui.dataconsent.activities.CombinedDataConsentActivity
import com.toyota.oneapp.ui.garage.BillingAddressActivity
import com.toyota.oneapp.ui.garage.paymentOptions.ManagePaymentOptionsActivity
import com.toyota.oneapp.ui.garage.paymentOptions.model.PaymentCY17PlusResult
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.SubscriptionV2PaymentCY17Contract
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Args
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Result
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.selectsubscription.adapter.SelectSubscriptionAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.selectsubscription.adapter.SelectSubscriptionForPurchaseItemDetailsLookup
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.selectsubscription.adapter.SelectSubscriptionForPurchaseKeyProvider
import com.toyota.oneapp.ui.garage.wifiSubscription.ActiveWifiSubscriptionExistActivity
import com.toyota.oneapp.util.IntentUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

/**
 * Fragment - Allows user to select subscription for purchase.
 */
@AndroidEntryPoint
class SelectSubscriptionFragment : BaseViewModelFragment() {
    private val args: SelectSubscriptionFragmentArgs by navArgs()
    private val viewModel: SelectSubscriptionViewModel by viewModels()

    private lateinit var binding: FragmentSelectSubscriptionBinding
    private lateinit var adapter: SelectSubscriptionAdapter
    private lateinit var selectionTracker: SelectionTracker<SubscriptionPackage>
    private val bundle = Bundle()

    private val getDataConsent =
        registerForActivityResult<Intent, ActivityResult>(
            ActivityResultContracts.StartActivityForResult(),
            ActivityResultCallback { result: ActivityResult ->
                if (result.resultCode == Activity.RESULT_OK) {
                    val intent = result.data
                    if (intent != null) {
                        val consents = intent.getSerializableExtra(ToyotaConstants.CONSENTS) as List<ConsentRequestItem>
                        viewModel.onDataConsentReceived(consents)
                    }
                }
            },
        )
    private val getBillingAddress =
        registerForActivityResult<Intent, ActivityResult>(
            ActivityResultContracts.StartActivityForResult(),
            ActivityResultCallback { result: ActivityResult ->
                if (result.resultCode == Activity.RESULT_OK) {
                    val intent = result.data
                    if (intent != null) {
                        val address =
                            BillingAddress(
                                street = intent.getStringExtra(ToyotaConstants.STREET_ADDRESS) ?: "",
                                city = intent.getStringExtra(ToyotaConstants.CITY) ?: "",
                                state = intent.getStringExtra(ToyotaConstants.STATE) ?: "",
                                postalCode = intent.getStringExtra(ToyotaConstants.ZIP_CODE) ?: "",
                                country = intent.getStringExtra(ToyotaConstants.COUNTRY) ?: "",
                            )
                        viewModel.onBillingAddressReceived(address)
                    }
                }
            },
        )
    private val getPaymentInfoCY17 =
        registerForActivityResult<PaymentCY17Args, PaymentCY17Result?>(
            SubscriptionV2PaymentCY17Contract(),
            ActivityResultCallback { result: PaymentCY17Result? ->
                if (result != null) {
                    viewModel.onPaymentInfoReceivedForCY17(result)
                }
            },
        )
    private val getPaymentInfoCY17Plus =
        registerForActivityResult<Intent, ActivityResult>(
            ActivityResultContracts.StartActivityForResult(),
            ActivityResultCallback { result: ActivityResult ->
                if (result.resultCode == Activity.RESULT_OK) {
                    val intent = result.data
                    if (intent != null) {
                        val paymentResult =
                            intent.getParcelableExtra<PaymentCY17PlusResult>(
                                ManagePaymentOptionsActivity.EXTRA_RESULT,
                            )
                        if (paymentResult != null) {
                            viewModel.onPaymentInfoReceivedForCY17Plus(paymentResult)
                        }
                    }
                }
            },
        )

    private val alertsActivityResult =
        registerForActivityResult<Intent, ActivityResult>(
            VehicleSubscriptionAlertsContract(),
        ) { result ->
            if (result is VehicleSubscriptionAlertsResult.Success) {
                navigateToDataConsentScreen(args.trialSubscriptions)
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentSelectSubscriptionBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        initializeViews()
        addListeners()
        setUpViewModelBindings()

        selectionTracker.onRestoreInstanceState(bundle)

        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        selectionTracker.onSaveInstanceState(bundle)
    }

    private fun initializeViews() {
        adapter =
            SelectSubscriptionAdapter(
                vehicleLocale = viewModel.getVehicleLocale(),
            )

        binding.rvSubscriptions.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
        }
        selectionTracker =
            SelectionTracker
                .Builder(
                    "SelectionTracker",
                    binding.rvSubscriptions,
                    SelectSubscriptionForPurchaseKeyProvider(adapter),
                    SelectSubscriptionForPurchaseItemDetailsLookup(binding.rvSubscriptions),
                    StorageStrategy.createParcelableStorage(SubscriptionPackage::class.java),
                ).withSelectionPredicate(SelectionPredicates.createSelectSingleAnything())
                .build()
        adapter.selectionTracker = selectionTracker

        binding.tvTitle.text = getString(R.string.select_subscription_for_service, viewModel.productName)
    }

    private fun addListeners() {
        selectionTracker.addObserver(
            object : SelectionTracker.SelectionObserver<SubscriptionPackage>() {
                override fun onSelectionChanged() {
                    super.onSelectionChanged()
                    <EMAIL>()
                }
            },
        )
        binding.btnContinue.setOnClickListener {
            viewModel.onContinueToPurchase()
        }
    }

    private fun onSelectionChanged() {
        val selection = selectionTracker.selection
        if (selection.isEmpty) {
            viewModel.onSubscriptionDeSelected()
        } else {
            viewModel.onSubscriptionSelected(selectedPackage = selection.first())
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.clearSelection.observe(viewLifecycleOwner) {
            selectionTracker.clearSelection()
        }
        viewModel.event.observe(viewLifecycleOwner) {
            handleViewModelEvents(it)
        }
        viewModel.packages.observe(viewLifecycleOwner) {
            DataBindingAdapters.setRecyclerViewAdapterData(binding.rvSubscriptions, it, null)
        }
        viewModel.autoRenew.observe(viewLifecycleOwner) {
            binding.cbAutoRenew.isChecked = it
        }
        viewModel.isAutoRenewVisible.observe(viewLifecycleOwner) {
            DataBindingAdapters.setIsVisible(binding.cbAutoRenew, it)
            DataBindingAdapters.setIsVisible(binding.tvAutoRenewMsg, it)
        }
        viewModel.isContinueToPurchaseEnabled.observe(viewLifecycleOwner) {
            binding.btnContinue.isEnabled = it
        }
    }

    private fun handleViewModelEvents(event: SelectSubscriptionViewModel.Event) {
        when (event) {
            is SelectSubscriptionViewModel.Event.NavigateToAlerts -> {
                navigateToAlertsScreen(event)
            }
            is SelectSubscriptionViewModel.Event.NavigateToGetDateConsentScreen -> {
                navigateToDataConsentScreen(event.trialSubscriptions)
            }
            is SelectSubscriptionViewModel.Event.NavigateToGetBillingAddress -> {
                navigateToGetBillingAddressScreen()
            }
            is SelectSubscriptionViewModel.Event.NavigateToGetPaymentInfoScreenForCY17 -> {
                navigateToGetPaymentInfoScreenForCY17(event.args)
            }
            is SelectSubscriptionViewModel.Event.NavigateToGetPaymentInfoScreenForCY17Plus -> {
                navigateToGetPaymentInfoScreenForCY17Plus()
            }
            is SelectSubscriptionViewModel.Event.NavigateToConfirmPaymentScreenForCY17 -> {
                navigateToConfirmPaymentScreenForCY17(event)
            }
            is SelectSubscriptionViewModel.Event.NavigateToConfirmPaymentScreenForCY17Plus -> {
                navigateToConfirmPaymentScreenForCY17Plus(event)
            }
            is SelectSubscriptionViewModel.Event.NavigateToWaiveSubscriptionSuccessScreen -> {
                navigateToWaiveSubscriptionSuccessScreen()
            }
            is SelectSubscriptionViewModel.Event.NavigateToMyGarageScreen -> {
                navigateToMyGarageScreen(event)
            }
            is SelectSubscriptionViewModel.Event.NavigateToActiveWifiSubscriptionFound -> {
                navigateToActiveWifiSubscriptionFound(event)
            }
        }
    }

    private fun navigateToAlertsScreen(event: SelectSubscriptionViewModel.Event.NavigateToAlerts) {
        alertsActivityResult.launch(event.vehicleSubscriptionAlertsArguments)
    }

    private fun navigateToDataConsentScreen(trialSubscriptions: Array<SubscriptionV2>) {
        val intent =
            CombinedDataConsentActivity.getIntent(
                context = requireContext(),
                vehicle = args.vehicle,
                isPaidFlow = true,
                trialSubscriptions = trialSubscriptions,
                subscriptionGetPayload = null,
                isGetConsentInfoAsResult = true,
            )
        getDataConsent.launch(intent)
    }

    private fun navigateToGetBillingAddressScreen() {
        val intent = Intent(requireContext(), BillingAddressActivity::class.java)
        getBillingAddress.launch(intent)
    }

    private fun navigateToGetPaymentInfoScreenForCY17(args: PaymentCY17Args) {
        getPaymentInfoCY17.launch(args)
    }

    private fun navigateToGetPaymentInfoScreenForCY17Plus() {
        val intent = Intent(requireContext(), ManagePaymentOptionsActivity::class.java)
        intent.putExtra(
            ManagePaymentOptionsActivity.EXTRA_IS_GET_SELECTED_PAYMENT_INFORMATION_AS_RESULT,
            true,
        )
        getPaymentInfoCY17Plus.launch(intent)
    }

    private fun navigateToConfirmPaymentScreenForCY17(event: SelectSubscriptionViewModel.Event.NavigateToConfirmPaymentScreenForCY17) {
        // Perform navigation only after Fragment has been Resumed - Because this navigation is called in ActivityResult.
        lifecycleScope.launchWhenResumed {
            val action =
                SelectSubscriptionFragmentDirections.actionSelectSubscriptionForPurchaseFragmentToConfirmPaymentForCY17Fragment(
                    vehicle = args.vehicle,
                    isAddVehicleFlow = args.isAddVehicleFlow,
                    subscription = event.subscription,
                    subscriptionPackage = event.subscriptionPackage,
                    consents = event.consents.toTypedArray(),
                    billingAddress = event.billingAddress,
                    paymentAccessToken = event.paymentAccessToken,
                    taxResponse = event.taxResponse,
                    accessToken = event.accessToken,
                )
            findNavController().navigate(action)
        }
    }

    private fun navigateToConfirmPaymentScreenForCY17Plus(
        event: SelectSubscriptionViewModel.Event.NavigateToConfirmPaymentScreenForCY17Plus,
    ) {
        // Perform navigation only after Fragment has been Resumed - Because this navigation is called in ActivityResult.
        lifecycleScope.launchWhenResumed {
            val action =
                SelectSubscriptionFragmentDirections.actionSelectSubscriptionForPurchaseFragmentToConfirmPaymentForCY17PlusFragment(
                    vehicle = args.vehicle,
                    isAddVehicleFlow = args.isAddVehicleFlow,
                    subscription = event.subscription,
                    subscriptionPackage = event.subscriptionPackage,
                    consents = event.consents.toTypedArray(),
                    billingAddress = event.billingAddress,
                    paymentID = event.paymentID,
                    isDefaultPayment = event.isDefaultPayment,
                    paymentRecord = event.paymentRecord,
                    isAutoRenew = event.isAutoRenew,
                )
            findNavController().navigate(action)
        }
    }

    private fun navigateToWaiveSubscriptionSuccessScreen() {
        val action =
            SelectSubscriptionFragmentDirections.actionSelectSubscriptionForPurchaseFragmentToSubscriptionActionSuccessActivity(
                vehicle = args.vehicle,
                icon = R.drawable.success_checkmark,
                title = getString(R.string.ManagePaidSubscription_Cancellation_Complete),
                msg1 = getString(R.string.ManagePaidSubscription_cancellation_succeeded),
                msg2 = null,
                actionTxt = getString(R.string.ServiceCampaign_back_to_dashboard),
                msg3 = null,
            )
        findNavController().navigate(action)
    }

    private fun navigateToMyGarageScreen(event: SelectSubscriptionViewModel.Event.NavigateToMyGarageScreen) {
        startActivity(
            IntentUtil.getOADashBoardIntent(context = activityContext, isDashboardRefresh = true),
        )
        requireActivity().finish()
    }

    private fun navigateToActiveWifiSubscriptionFound(event: SelectSubscriptionViewModel.Event.NavigateToActiveWifiSubscriptionFound) {
        val intent =
            ActiveWifiSubscriptionExistActivity.getIntent(
                requireContext(),
                trialSubscriptions = event.trialSubscriptions,
                vehicleGeneration = event.vehicle.generation,
                vehicleRegion = event.vehicle.region,
                isMyGarageFlow = true,
            )
        startActivity(intent)
        requireActivity().finish()
    }
}
