package com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.manage

import androidx.annotation.StringRes
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.subscription.AutoRenew
import com.toyota.oneapp.model.subscription.AutoRenewSubscriptionRequest
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.manage.mapper.ManagePaidSubscriptionUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.manage.model.ManagePaidSubscriptionUIModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel - for [ManagePaidSubscriptionFragment]
 */
@HiltViewModel
class ManagePaidSubscriptionViewModel
    @Inject
    constructor(
        savedStateHandle: SavedStateHandle,
        mapper: ManagePaidSubscriptionUIMapper,
        private val subscriptionApiManager: SubscriptionAPIManager,
        private val preferenceModel: OneAppPreferenceModel,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        private val args = ManagePaidSubscriptionFragmentArgs.fromSavedStateHandle(savedStateHandle)

        sealed class Event {
            class NavigateToCancelSubscriptionForCY17Screen(
                val trialSubscriptions: Array<SubscriptionV2>,
                val paidSubscriptions: Array<SubscriptionV2>,
                val ppoCancelDisclaimer: String?,
            ) : Event()

            class NavigateToCancelSubscriptionForCY17PlusScreen(
                val subscription: SubscriptionV2,
            ) : Event()

            data class UpdateSubscriptionScreen(
                @StringRes val title: Int,
                @StringRes val msg1: Int,
                @StringRes val msg2: Int,
            ) : Event()
        }

        private val _uiModel = MutableLiveData<ManagePaidSubscriptionUIModel>()
        private val _updateOptionEnabled = MutableLiveData<Boolean>()
        private val _event = SingleLiveEvent<Event>()

        val uiModel: LiveData<ManagePaidSubscriptionUIModel>
            get() = _uiModel
        val autoRenew = MutableLiveData<Boolean>()
        val updateOptionEnabled: LiveData<Boolean>
            get() = _updateOptionEnabled
        val event: LiveData<Event>
            get() = _event

        init {
            _uiModel.value = mapper.map(args.selectedSubscription, args.vehicle)

            autoRenew.value = args.selectedSubscription.autoRenew ?: true
            _updateOptionEnabled.value = false

            autoRenew.observeForever {
                onAutoRenewValueChanges(it)
            }
        }

        private fun onAutoRenewValueChanges(isAutoRenew: Boolean) {
            _updateOptionEnabled.value = isAutoRenew != args.selectedSubscription.autoRenew
        }

        fun onAutoRenewCheckedChanged(isChecked: Boolean) {
            autoRenew.postValue(isChecked)
        }

        fun onUpdateSubscription() {
            analyticsLogger.logEvent(
                AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_DETAIL_MANAGE_SUBSCRIPTION_UPDATE_CLICKED,
                SubscriptionConstants.ANALYTICS_KEY_SERVICE to args.selectedSubscription.displayProductName,
            )

            val autoRenewValue = autoRenew.value ?: true
            val subscriptionList = ArrayList<AutoRenew>()
            subscriptionList.add(
                AutoRenew(
                    args.selectedSubscription.subscriptionID
                        ?: "",
                    autoRenewValue,
                ),
            )

            val autoRenewSubscriptionRequest =
                AutoRenewSubscriptionRequest(
                    vin = args.vehicle.vin,
                    subscriberGuid = preferenceModel.getGuid(),
                    generation = args.vehicle.generation,
                    subscriptions = subscriptionList,
                )

            showProgress()

            subscriptionApiManager.autoRenewSubscription(
                args.vehicle.brand,
                autoRenewSubscriptionRequest,
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        _event.value =
                            Event.UpdateSubscriptionScreen(
                                title = R.string.ManagePaidSubscription_Update_Complete,
                                msg1 = R.string.ManagePaidSubscription_auto_renew_succeeded,
                                msg2 = if (autoRenewValue) R.string.ManagePaidSubscription_auto_renew_turned_on_description else R.string.ManagePaidSubscription_auto_renew_turned_off_description,
                            )
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        showErrorMessage(R.string.generic_error)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun onCancelSubscription() {
            with(args) {
                analyticsLogger.logEvent(
                    AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_DETAIL_MANAGE_SUBSCRIPTION_CANCEL_CLICKED,
                    SubscriptionConstants.ANALYTICS_KEY_SERVICE to selectedSubscription.displayProductName,
                )

                _event.value =
                    if (vehicle.isCY17) {
                        Event.NavigateToCancelSubscriptionForCY17Screen(
                            trialSubscriptions = trialSubscriptions,
                            paidSubscriptions = paidSubscriptions,
                            ppoCancelDisclaimer = null,
                        )
                    } else {
                        Event.NavigateToCancelSubscriptionForCY17PlusScreen(
                            subscription = selectedSubscription,
                        )
                    }
            }
        }
    }
