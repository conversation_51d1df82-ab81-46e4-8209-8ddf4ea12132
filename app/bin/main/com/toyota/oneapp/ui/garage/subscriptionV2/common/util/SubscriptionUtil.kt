package com.toyota.oneapp.ui.garage.subscriptionV2.common.util

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.toyota.oneapp.R
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.combineddataconsent.DataConsentStatus
import com.toyota.oneapp.model.subscription.PreviewSubscriptionItem
import com.toyota.oneapp.model.subscription.SubscriptionGetPayload
import com.toyota.oneapp.model.subscription.SubscriptionUIItem
import com.toyota.oneapp.model.subscriptionV2.*
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.model.ManageWiFiSubscriptionDetailInitializationModel
import java.util.*
import kotlin.collections.ArrayList

object SubscriptionUtil {
    // Remote
    const val PROD_REMOTESERVICE = "PROD_REMOTESERVICE"
    const val PROD_EREMOTE = "PROD_EREMOTE"
    const val REMOTESERV = "REMOTESERV"

    // Service Connect
    const val PROD_SERVICE_CONNECT = "PROD_SERVICECONNECT"

    // Destination Assist
    const val PROD_DESTASSIST = "PROD_DESTASSIST"
    const val DESTASSIST = "DESTASSIST"

    // Safety Connect
    const val PROD_SAFETYCONNECT = "PROD_SAFETYCONNECT"
    const val LEXSCNCT = "LEXSCNCT"
    const val TOYSCNCT = "TOYSCNCT"
    const val SAFETYNDIAG = "SAFETYNDIAG"

    // Navigation
    const val PROD_NAVIGATION = "PROD_NAVIGATION"
    const val HYBRIDNAVIA = "HYBRIDNAVIA"
    const val HYBRIDNAVIB = "HYBRIDNAVIB"

    // Wi-Fi
    const val PROD_WIFI = "PROD_WIFI"
    const val WIFI_CONNECT = "WIFI-CONNECT"

    // Combinations
    const val LEXENFRM = "LEXENFRM"
    const val LEXSCNRS = "LEXSCNRS"
    const val LEXENFRS = "LEXENFRS"

    // 21MM products
    const val PROD_NAVPKG = "PROD_NAVPKG"
    const val PROD_REMOTEV2 = "PROD_REMOTEV2"
    const val PROD_REMOTELM = "PROD_REMOTELM"
    const val PROD_REMOTELS = "PROD_REMOTELS"
    const val PROD_REMKEY1 = "PROD_REMKEY1"
    const val PROD_REMKEY2 = "PROD_REMKEY2"
    const val PROD_EREMKEY = "PROD_EREMKEY"

    private const val AUTO_RENEWED_AVAILABILITY_CODE = "AUTO_RENEWED"

    // Get Icon for given Subscription Product.
    @DrawableRes
    fun getIconForSubscriptionFromProductLine(productLine: String?): Int =
        when {
            SubscriptionConstants.remoteConnectProductLine.contains(productLine) -> {
                R.drawable.ic_product_remote_connect
            }
            SubscriptionConstants.safetyConnectProductLines.contains(productLine) -> {
                R.drawable.ic_product_safety_connect
            }
            SubscriptionConstants.destinationAssistProductLines.contains(productLine) -> {
                R.drawable.ic_product_destination_assist
            }
            SubscriptionConstants.dynamicNaviProductLines.contains(productLine) -> {
                R.drawable.ic_product_dynamic_navigation
            }
            SubscriptionConstants.wifiConnectProductLines.contains(productLine) -> {
                R.drawable.ic_product_wifi_connect
            }
            SubscriptionConstants.driveConnectProductLines.contains(productLine) -> {
                R.drawable.ic_product_drive_connect
            }
            SubscriptionConstants.teammateProductLines.contains(productLine) -> {
                R.drawable.ic_product_teammate
            }
            SubscriptionConstants.integratedStreamingLines.contains(productLine) -> {
                R.drawable.ic_product_streaming
            }
            SubscriptionConstants.serviceConnectProductLines.contains(productLine) -> {
                R.drawable.ic_product_service_connect
            }
            else -> {
                R.drawable.ic_product_default
            }
        }

    // Get Subscription Display Term Unit (i.e Month or Year).
    @StringRes
    fun getDisplayTermUnit(subscriptionTerm: String): Int =
        when (subscriptionTerm) {
            "MTH" -> R.string.month
            "YRLY" -> R.string.year
            else -> R.string.empty
        }

    @ColorRes
    fun getIconTintColor(brandName: String): Int =
        if (VehicleInfo.isToyotaBrand(brandName)) {
            R.color.toyota_brand_color
        } else {
            R.color.lexus_brand_color
        }

    // Get master consent accepted or not.
    fun checkIsMasterConsentAccepted(consents: List<ConsentRequestItem>?): Boolean {
        val masterConsent = consents?.firstOrNull { it.category == CombineDataConsent.CATEGORY_TELEMATICS_CONSENT }
        return (DataConsentStatus.fromValue(masterConsent?.status) == DataConsentStatus.ACCEPTED)
    }

    fun checkIsSubscriptionIsAlreadyPurchasedAndIsAutoRenewable(
        vehicle: VehicleInfo,
        subscription: AvailableSubscription,
    ): Boolean =
        (
            !vehicle.isCY17 &&
                subscription.available == false &&
                subscription.availabilityCode == AUTO_RENEWED_AVAILABILITY_CODE
        )

    fun createWiFiSubscriptionInitializationModel(subscription: SubscriptionV2): ManageWiFiSubscriptionDetailInitializationModel =
        with(subscription) {
            ManageWiFiSubscriptionDetailInitializationModel(
                productName = productName,
                productLongDesc = productDescription(),
                productImageUrl = productImageUrl.orEmpty(),
                subscriptionType = type(),
                externalTargetUrl = externalTargetUrl.orEmpty(),
                externalContactDetail = externalContactDetail.orEmpty(),
            )
        }

    fun createWiFiSubscriptionInitializationModel(subscription: AvailableSubscription): ManageWiFiSubscriptionDetailInitializationModel =
        with(subscription) {
            ManageWiFiSubscriptionDetailInitializationModel(
                productName = productName,
                productLongDesc = productDescription(),
                productImageUrl = productImageUrl.orEmpty(),
                subscriptionType = SubscriptionType.PAID,
                externalTargetUrl = externalTargetUrl.orEmpty(),
                externalContactDetail = externalContactDetail.orEmpty(),
            )
        }

    @DrawableRes
    fun getWifiCarrierIcon(vehicle: VehicleInfo?): Int = if (vehicle?.isCY17 == true) R.drawable.ic_verizon_logo else R.drawable.ic_att_logo

    fun convertSubscriptionV2ToPreviewSubscriptionItem(subscription: SubscriptionV2): PreviewSubscriptionItem {
        val previewSubscriptionItem =
            PreviewSubscriptionItem().apply {
                currency = subscription.currency
                packageID = subscription.packageID
                productID = subscription.productID
                productName = subscription.productName
                subscriptionTerm = subscription.subscriptionTerm
                productCode = subscription.productCode
                productLine = subscription.productLine
                ratePlanID = subscription.ratePlanID
                termUnit = subscription.termUnit
                subscriptionStartDate = subscription.subscriptionStartDate
                subscriptionEndDate = subscription.subscriptionEndDate
                type = subscription.type
                displayTerm = subscription.displayTerm
                isRenewable = subscription.renewable
                isAutoRenew = subscription.autoRenew ?: false
                price = subscription.price
                discount = 0.0
                term = subscription.term
                isCPOProduct = subscription.isCPOProduct ?: false
                isPPOProduct = subscription.isPPOProduct ?: false
                isService = subscription.isService ?: false
            }

        return previewSubscriptionItem
    }

    fun convertAvailableSubscriptionToPreviewSubscriptionItem(
        subscription: AvailableSubscription,
        subscriptionPackage: SubscriptionPackage,
        isAutoRenew: Boolean,
    ): PreviewSubscriptionItem {
        val previewSubscriptionItem =
            PreviewSubscriptionItem().apply {
                currency = subscriptionPackage.currency
                packageID = subscriptionPackage.packageID
                productID = subscriptionPackage.productID
                productName = subscription.productName
                subscriptionTerm = subscriptionPackage.subscriptionTerm
                productCode = subscriptionPackage.productCode
                productLine = subscription.productLine
                ratePlanID = subscriptionPackage.ratePlanID
                termUnit = subscriptionPackage.termUnit
                description = subscription.description
                subscriptionStartDate = subscriptionPackage.subscriptionStartDate
                subscriptionEndDate = subscriptionPackage.subscriptionEndDate
                type = subscriptionPackage.type
                isRenewable = subscription.renewable ?: false
                this.isAutoRenew = isAutoRenew
                available = subscription.available ?: false
                price = subscriptionPackage.price
                discount = subscriptionPackage.discount ?: 0.0
                term = subscriptionPackage.term ?: 0
                category = subscription.category
                components = ArrayList(subscription.components ?: emptyList())
            }

        return previewSubscriptionItem
    }

    fun formSubscriptionGetPayload(
        vehicle: VehicleInfo,
        guid: String,
        trialSubscriptions: List<SubscriptionV2>,
        accessToken: String,
        isCPOEligible: Boolean,
        isPPOEligible: Boolean,
    ): SubscriptionGetPayload {
        val (v2Subscriptions, externalV2Subscriptions) = trialSubscriptions.partition { !it.externalProduct }

        val subscriptionGetPayload =
            SubscriptionGetPayload(vehicle, guid).apply {
                list =
                    ArrayList(
                        v2Subscriptions.map { subscription ->
                            SubscriptionUIItem().apply {
                                previewSubscriptionItem =
                                    convertSubscriptionV2ToPreviewSubscriptionItem(
                                        subscription,
                                    )
                            }
                        },
                    )
                externalSubscriptions =
                    ArrayList(
                        externalV2Subscriptions.map { subscription ->
                            convertSubscriptionV2ToPreviewSubscriptionItem(subscription)
                        },
                    )
                this.accessToken = accessToken
                this.isCPOEligible = isCPOEligible
                this.isPPOEligible = isPPOEligible
            }

        return subscriptionGetPayload
    }
}
