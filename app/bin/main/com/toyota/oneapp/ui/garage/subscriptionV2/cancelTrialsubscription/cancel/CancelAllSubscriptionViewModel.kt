package com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.cancel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.subscription.CancelSubscriptionResponse
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.subscriptionV2.isActive
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.cancel.mapper.CancelSubscriptionListUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.cancel.model.CancelSubscriptionListItemUIModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class CancelAllSubscriptionViewModel
    @Inject
    internal constructor(
        private val savedStateHandle: SavedStateHandle,
        private val mapper: CancelSubscriptionListUIMapper,
        private val subscriptionManager: SubscriptionAPIManager,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        private val args = CancelAllSubscriptionFragmentArgs.fromSavedStateHandle(savedStateHandle)

        sealed class NavigationEvent {
            object SuccessCancelSubscription : NavigationEvent()
        }

        private val _subscriptionUIModels = MutableLiveData<List<CancelSubscriptionListItemUIModel>>()
        private val _cancelSubscriptionNavigationEvent = SingleLiveEvent<NavigationEvent>()
        private val _isPPOEligible = MutableLiveData<Boolean>()
        private val _ppoCancelDisclaimer = MutableLiveData<String>()

        val subscriptionsUIModels: LiveData<List<CancelSubscriptionListItemUIModel>>
            get() = _subscriptionUIModels
        val cancelSubscriptionNavigationEvent: LiveData<NavigationEvent>
            get() = _cancelSubscriptionNavigationEvent

        val isPPOEligible: LiveData<Boolean>
            get() = _isPPOEligible
        val ppoCancelDisclaimer: LiveData<String>
            get() = _ppoCancelDisclaimer

        init {
            // We should not show external product(Wifi) inside cancel trial screen.
            // Because External Products are not cancelled only Toyota products will be cancelled.
            val trialSubscriptionsWithoutExternalProducts = args.trialSubscriptions.toList().filter { (!it.externalProduct) }
            val paidSubscriptionsWithoutExternalProducts = args.paidSubscriptions.toList().filter { (!it.externalProduct) }
            _isPPOEligible.value = args.isPPOEligible
            _ppoCancelDisclaimer.value = args.ppoCancelDisclaimer ?: ""

            _subscriptionUIModels.value =
                mapper.map(
                    trialSubscriptions = trialSubscriptionsWithoutExternalProducts,
                    paidSubscriptions = paidSubscriptionsWithoutExternalProducts,
                )
        }

        fun cancelSubscription() {
            analyticsLogger.logEvent(
                AnalyticsEvent.MANAGE_SUBSCRIPTION_CANCEL_FLOW_CANCEL_ALL_SUBSCRIPTION_CONFIRMATION_CLICKED,
            )

            showProgress()

            val subscriptionIds = ArrayList<String>()
            val externalSubscriptions = mutableListOf<SubscriptionV2>()

            // Trial Subscriptions
            for (subscription in args.trialSubscriptions) {
                if (subscription.isActive()) {
                    if (!subscription.externalProduct) {
                        with(subscription) {
                            subscriptionID?.let { subscriptionIds.add(it) }
                            consolidatedProductIds?.let { subscriptionIds.addAll(it) }
                            consolidatedGoodwillIds?.let { subscriptionIds.addAll(it) }
                        }
                    } else {
                        externalSubscriptions.add(subscription)
                    }
                }
            }

            // Paid Subscription
            for (subscription in args.paidSubscriptions) {
                if (!subscription.externalProduct) {
                    with(subscription) {
                        subscriptionID?.let { subscriptionIds.add(it) }
                        consolidatedProductIds?.let { subscriptionIds.addAll(it) }
                        consolidatedGoodwillIds?.let { subscriptionIds.addAll(it) }
                    }
                } else {
                    externalSubscriptions.add(subscription)
                }
            }

            subscriptionManager.sendCancelSubscription(
                args.vehicle,
                subscriptionIds,
                externalSubscriptions,
                null,
                null,
                object : BaseCallback<CancelSubscriptionResponse?>() {
                    override fun onSuccess(response: CancelSubscriptionResponse?) {
                        super.onSuccess(response)
                        showSuccessToastMessage(R.string.Cancellation_Request_In_Progress_note)
                        _cancelSubscriptionNavigationEvent.postValue(
                            NavigationEvent.SuccessCancelSubscription,
                        )
                    }

                    override fun onError() {
                        showErrorMessage(R.string.generic_error)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }
    }
