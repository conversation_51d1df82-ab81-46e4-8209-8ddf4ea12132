package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.model

import android.os.Parcelable
import com.toyota.oneapp.model.subscriptionV2.BundleComponent
import com.toyota.oneapp.model.subscriptionV2.SubscriptionPackage
import kotlinx.parcelize.Parcelize

sealed class SelectPackageUIModel : Parcelable {
    @Parcelize data class SelectServicePackageUIModel(
        val isPackageSelected: Boolean,
        val title: String,
        val subscriptionAmount: String,
        val subscriptionTerm: String,
        val packageInfo: SubscriptionPackage,
    ) : SelectPackageUIModel()

    @Parcelize data class SelectBundlePackageUIModel(
        val isPackageSelected: Boolean,
        val title: String,
        val subscriptionAmount: String,
        val subscriptionTerm: String,
        val bundleComponents: List<BundleComponent>,
        val packageInfo: SubscriptionPackage,
    ) : SelectPackageUIModel()
}
