package com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.subscription.VehicleSubscriptionAlert
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.subscriptionV2.VehicleSubscriptionPayload
import com.toyota.oneapp.model.subscriptionV2.isActive
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.model.ManageWiFiSubscriptionDetailInitializationModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.mapper.SubscriptionListUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.model.SubscriptionListItemUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.model.SubscriptionUIModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel for Subscription List Screen.
 */
@HiltViewModel
class SubscriptionListViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val mapper: SubscriptionListUIMapper,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        sealed class NavigationEvent {
            data class NavigateToCancelTrialSubscriptionDetailScreen(
                val vehicle: VehicleInfo,
                val isAddVehicleFlow: Boolean,
                val selectedSubscription: SubscriptionV2,
                val trialSubscriptions: List<SubscriptionV2>,
                val paidSubscriptions: List<SubscriptionV2>,
                val availableSubscriptions: List<AvailableSubscription>,
                val accessToken: String?,
                val taxDisclaimer: String?,
                val isAzure: Boolean?,
                val isPPOEligible: Boolean,
                val ppoCancelDisclaimer: String?,
                val alerts: List<VehicleSubscriptionAlert>?,
            ) : NavigationEvent()

            data class NavigateToEnableTrialSubscriptionDetailScreen(
                val vehicle: VehicleInfo,
                val selectedSubscription: SubscriptionV2,
                val trialSubscriptions: List<SubscriptionV2>,
                val paidSubscriptions: List<SubscriptionV2>,
                val accessToken: String?,
                val isAzure: Boolean?,
                val alerts: List<VehicleSubscriptionAlert>?,
                val isAddVehicleFlow: Boolean?,
            ) : NavigationEvent()

            data class NavigateToManageWiFiConnectSubscriptionDetailScreen(
                val vehicle: VehicleInfo,
                val initializationModel: ManageWiFiSubscriptionDetailInitializationModel,
            ) : NavigationEvent()

            data class NavigateToManagePaidSubscriptionDetailScreen(
                val vehicle: VehicleInfo,
                val selectedSubscription: SubscriptionV2,
                val trialSubscriptions: List<SubscriptionV2>,
                val paidSubscriptions: List<SubscriptionV2>,
            ) : NavigationEvent()

            data class NavigateToAddServiceScreen(
                val vehicle: VehicleInfo,
                val isAddVehicleFlow: Boolean,
                val trialSubscriptions: List<SubscriptionV2>,
                val paidSubscriptions: List<SubscriptionV2>,
                val availableSubscription: List<AvailableSubscription>,
                val accessToken: String?,
                val taxDisclaimer: String?,
                val isAzure: Boolean?,
                val alerts: List<VehicleSubscriptionAlert>?,
            ) : NavigationEvent()

            data class NavigateToEnableAlTrialScreen(
                val vehicle: VehicleInfo,
                val isAddVehicleFlow: Boolean,
                val payload: VehicleSubscriptionPayload,
                val trialSubscriptions: List<SubscriptionV2>,
                val accessToken: String?,
                val alerts: List<VehicleSubscriptionAlert>?,
            ) : NavigationEvent()
        }

        private val args = SubscriptionListFragmentArgs.fromSavedStateHandle(state)

        private val _uiModel = MutableLiveData<SubscriptionUIModel>()
        private val _subscriptionListNavigationEvent = SingleLiveEvent<NavigationEvent>()

        val uiModel: LiveData<SubscriptionUIModel>
            get() = _uiModel
        val subscriptionListNavigationEvent: LiveData<NavigationEvent>
            get() = _subscriptionListNavigationEvent

        init {
            initializeUIModel()
        }

        private fun initializeUIModel() {
            viewModelScope.launch {
                showProgress()

                _uiModel.value = mapper.map(args.vehicle, args.response.payload)

                hideProgress()
            }
        }

        fun onServiceItemClick(service: SubscriptionListItemUIModel.Service) {
            when (service) {
                is SubscriptionListItemUIModel.Service.TrialService ->
                    onTrialSubscriptionClicked(
                        service,
                    )
                is SubscriptionListItemUIModel.Service.MyPaidService ->
                    onMyPaidSubscriptionClicked(
                        service,
                    )
            }
        }

        private fun onTrialSubscriptionClicked(service: SubscriptionListItemUIModel.Service.TrialService) {
            with(args) {
                if (service.subscription.isActive()) {
                    // Cancel Trial
                    analyticsLogger.logEvent(
                        com.toyota.oneapp.analytics.AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_TRIAL_ENABLED_SERVICE_CLICKED,
                        SubscriptionConstants.ANALYTICS_KEY_SERVICE to service.subscription.displayProductName,
                    )

                    if (service.subscription.productLine == SubscriptionConstants.WIFI_CONNECT) {
                        // WiFi Connect.
                        _subscriptionListNavigationEvent.value =
                            NavigationEvent.NavigateToManageWiFiConnectSubscriptionDetailScreen(
                                vehicle = args.vehicle,
                                initializationModel =
                                    SubscriptionUtil.createWiFiSubscriptionInitializationModel(
                                        service.subscription,
                                    ),
                            )
                    } else {
                        // Other Trial services.
                        _subscriptionListNavigationEvent.value =
                            NavigationEvent.NavigateToCancelTrialSubscriptionDetailScreen(
                                vehicle = args.vehicle,
                                isAddVehicleFlow = args.isAddVehicleFlow,
                                selectedSubscription = service.subscription,
                                trialSubscriptions = response.payload.trialSubscriptions,
                                paidSubscriptions = response.payload.paidSubscriptions,
                                availableSubscriptions = response.payload.availableSubscriptions,
                                accessToken = response.payload.accessToken,
                                taxDisclaimer = response.payload.taxDisclaimer,
                                isAzure = response.payload.isAzure,
                                isPPOEligible = response.payload.isPPOEligible ?: false,
                                ppoCancelDisclaimer = response.payload.ppoCancelDisclaimer,
                                alerts = response.payload.alerts,
                            )
                    }
                } else {
                    // Enable Trial
                    analyticsLogger.logEvent(
                        com.toyota.oneapp.analytics.AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_TRIAL_AVAILABLE_CLICKED,
                        SubscriptionConstants.ANALYTICS_KEY_SERVICE to service.subscription.displayProductName,
                    )

                    val trialSubscriptionsToBeActivated = response.payload.trialSubscriptions.filter { !it.isActive() }
                    val paidSubscriptionsToBeActivated = response.payload.paidSubscriptions.filter { !it.isActive() }
                    _subscriptionListNavigationEvent.value =
                        NavigationEvent.NavigateToEnableTrialSubscriptionDetailScreen(
                            vehicle = args.vehicle,
                            selectedSubscription = service.subscription,
                            trialSubscriptions = trialSubscriptionsToBeActivated,
                            paidSubscriptions = paidSubscriptionsToBeActivated,
                            accessToken = response.payload.accessToken,
                            isAzure = response.payload.isAzure,
                            alerts = response.payload.alerts,
                            isAddVehicleFlow = args.isAddVehicleFlow,
                        )
                }
            }
        }

        private fun onMyPaidSubscriptionClicked(service: SubscriptionListItemUIModel.Service.MyPaidService) {
            with(args) {
                analyticsLogger.logEvent(
                    com.toyota.oneapp.analytics.AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_PAID_SERVICE_CLICKED,
                    SubscriptionConstants.ANALYTICS_KEY_SERVICE to service.subscription.displayProductName,
                )

                if (service.subscription.productLine == SubscriptionConstants.WIFI_CONNECT) {
                    // WiFi Connect.
                    _subscriptionListNavigationEvent.value =
                        NavigationEvent.NavigateToManageWiFiConnectSubscriptionDetailScreen(
                            vehicle = args.vehicle,
                            initializationModel =
                                SubscriptionUtil.createWiFiSubscriptionInitializationModel(
                                    service.subscription,
                                ),
                        )
                } else {
                    // Other Paid services.
                    _subscriptionListNavigationEvent.value =
                        NavigationEvent.NavigateToManagePaidSubscriptionDetailScreen(
                            vehicle = args.vehicle,
                            selectedSubscription = service.subscription,
                            trialSubscriptions = response.payload.trialSubscriptions,
                            paidSubscriptions = response.payload.paidSubscriptions,
                        )
                }
            }
        }

        fun onActionButtonClicked() {
            with(args) {
                val isAllTrialServicesActive =
                    response.payload.trialSubscriptions
                        .filter { !it.externalProduct }
                        .all { it.isActive() }

                if (isAllTrialServicesActive) {
                    // Add Service.
                    analyticsLogger.logEvent(
                        com.toyota.oneapp.analytics.AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_ADD_SERVICE_CLICKED,
                    )

                    _subscriptionListNavigationEvent.value =
                        NavigationEvent.NavigateToAddServiceScreen(
                            vehicle = args.vehicle,
                            isAddVehicleFlow = args.isAddVehicleFlow,
                            trialSubscriptions = response.payload.trialSubscriptions,
                            paidSubscriptions = response.payload.paidSubscriptions,
                            availableSubscription = response.payload.availableSubscriptions,
                            accessToken = response.payload.accessToken,
                            taxDisclaimer = response.payload.taxDisclaimer,
                            isAzure = response.payload.isAzure,
                            alerts = response.payload.alerts,
                        )
                } else {
                    // Enable All Trial.
                    analyticsLogger.logEvent(
                        com.toyota.oneapp.analytics.AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_ENABLE_ALL_TRIAL_CLICKED,
                    )

                    val trialSubscriptionsToBeActivated = response.payload.trialSubscriptions.filter { !it.isActive() }
                    _subscriptionListNavigationEvent.value =
                        NavigationEvent.NavigateToEnableAlTrialScreen(
                            vehicle = args.vehicle,
                            isAddVehicleFlow = args.isAddVehicleFlow,
                            payload = response.payload,
                            trialSubscriptions = trialSubscriptionsToBeActivated,
                            alerts = response.payload.alerts,
                            accessToken = response.payload.accessToken,
                        )
                }
            }
        }
    }
