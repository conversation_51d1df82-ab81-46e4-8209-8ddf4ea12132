package com.toyota.oneapp.ui.garage.subscriptionCancellation

import android.content.Context
import com.toyota.oneapp.util.dataBinding.BindableSpinnerAdapter

class SubscriptionCancellationReasons(
    private val cancelTextsArray: Int,
    private val cancelCodesArray: Int,
) {
    val spinnerItems: MutableList<BindableSpinnerAdapter.SpinnerItem> = mutableListOf()

    fun createReasonStrings(context: Context) {
        val cancelTexts = context.resources.getStringArray(cancelTextsArray)
        val cancelCodes = context.resources.getStringArray(cancelCodesArray)

        for (index in cancelTexts.indices) {
            spinnerItems.add(
                BindableSpinnerAdapter.SpinnerItem(cancelTexts[index], cancelCodes[index]),
            )
        }
    }
} // SubscriptionCancellationReasons class
