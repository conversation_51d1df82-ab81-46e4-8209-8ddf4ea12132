package com.toyota.oneapp.ui.garage.vehicleUpdate21mm

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentVehicleSoftwareUpdateBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VehicleSoftware21MMUpdateFragment : BaseDataBindingFragment<FragmentVehicleSoftwareUpdateBinding>() {
    private val mViewModel: VehicleSoftwareUpdateViewModel by activityViewModels()

    private lateinit var vehicleSoftware21MMOtaImageAdapter: VehicleSoftware21MMOtaImageAdapter

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(mViewModel)
    }

    override fun onViewBound(
        binding: FragmentVehicleSoftwareUpdateBinding,
        savedInstance: Bundle?,
    ) {
        binding.apply {
            lifecycleOwner = this@VehicleSoftware21MMUpdateFragment
            viewModel = mViewModel
            continueSoftwareUpdateBtn.setOnClickListener {
                findNavController().navigate(
                    R.id.action_vehicleSoftware21MMFragment_to_vehicleSoftwareTermFragment,
                )
            }

            softwareUpdateToolBar.apply {
                setNavigationOnClickListener { requireActivity().onBackPressed() }
            }
            val registrationId = arguments?.getString(ToyotaConstants.REGISTRATION_REQUEST_ID)
            val vin = arguments?.getString(ToyotaConstants.VIN)
            mViewModel.fetchLatestSoftwareVersionAvailableDetails(registrationId, vin)

            vehicleSoftware21MMOtaImageAdapter = VehicleSoftware21MMOtaImageAdapter()
            adapter = vehicleSoftware21MMOtaImageAdapter
            executePendingBindings()
            val recyclerView = rvOtaImageList
            recyclerView.apply {
                layoutManager = LinearLayoutManager(context as Context)
            }
            mViewModel.onSoftwareUpdate.observe(this@VehicleSoftware21MMUpdateFragment) {
                getOtaContentImageList(it.contentImageUrl)
            }
        }
    }

    override fun getLayout(): Int = R.layout.fragment_vehicle_software_update

    private fun getOtaContentImageList(otaContentList: List<String>) {
        vehicleSoftware21MMOtaImageAdapter.notifyDataSetChanged(otaContentList)
    }
}
