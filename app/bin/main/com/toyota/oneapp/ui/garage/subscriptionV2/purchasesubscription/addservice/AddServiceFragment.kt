package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.databinding.FragmentAddServiceBinding
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice.AddServiceFragmentDirections.Companion.actionAddServiceFragmentToWiFiConnectSubscriptionDetailFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice.AddServiceFragmentDirections.Companion.actionAddserviceFragmentToPurchaseSubscriptionDetailFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice.adapter.AddServiceAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice.model.AddServiceItemUIModel
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

/**
 * This class is used to show available services to purchase
 */
@AndroidEntryPoint
class AddServiceFragment :
    BaseViewModelFragment(),
    AddServiceAdapter.OnItemClickListener {
    private val args: AddServiceFragmentArgs by navArgs()
    private val viewModel: AddServiceViewModel by viewModels()

    private lateinit var binding: FragmentAddServiceBinding
    private lateinit var adapter: AddServiceAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentAddServiceBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        initializeViews()
        setUpViewModelBindings()

        return binding.root
    }

    // AddServiceAdapter.OnItemClickListener
    override fun onServiceItemClick(item: AddServiceItemUIModel) {
        viewModel.onServiceItemClick(item.subscription)
    }

    private fun initializeViews() {
        adapter = AddServiceAdapter(emptyList(), this)
        binding.rvServices.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            addItemDecoration(DividerItemDecoration(requireContext(), LinearLayout.VERTICAL))
        }
        viewModel.subscriptionUIModels.observe(viewLifecycleOwner) { subscription ->
            DataBindingAdapters.setRecyclerViewAdapterData(binding.rvServices, subscription, emptyList())
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.addServiceNavigationEvent.observe(
            viewLifecycleOwner,
            Observer {
                handleNavigationEvents(it)
            },
        )
    }

    private fun handleNavigationEvents(event: AddServiceViewModel.NavigationEvent) {
        when (event) {
            is AddServiceViewModel.NavigationEvent.NavigateToManageWiFiConnectSubscriptionDetailScreen -> {
                navigateToManageWiFiConnectSubscriptionDetailScreen(event)
            }
            is AddServiceViewModel.NavigationEvent.NavigateToPurchaseSubscriptionDetailScreen -> {
                navigateToPurchaseSubscriptionScreen(event)
            }
        }
    }

    private fun navigateToManageWiFiConnectSubscriptionDetailScreen(
        event: AddServiceViewModel.NavigationEvent.NavigateToManageWiFiConnectSubscriptionDetailScreen,
    ) {
        val action =
            actionAddServiceFragmentToWiFiConnectSubscriptionDetailFragment(
                vehicle = args.vehicle,
                initializationModel = event.initializationModel,
            )
        findNavController().navigate(action)
    }

    private fun navigateToPurchaseSubscriptionScreen(
        event: AddServiceViewModel.NavigationEvent.NavigateToPurchaseSubscriptionDetailScreen,
    ) {
        val action =
            actionAddserviceFragmentToPurchaseSubscriptionDetailFragment(
                vehicle = args.vehicle,
                isAddVehicleFlow = args.isAddVehicleFlow,
                selectedSubscription = event.selectedSubscription,
                trialSubscriptions = event.trialSubscriptions.toTypedArray(),
                paidSubscriptions = event.paidSubscriptions.toTypedArray(),
                availableSubscriptions = event.availableSubscriptions.toTypedArray(),
                accessToken = event.accessToken,
                alerts = event.alerts?.toTypedArray(),
            )
        findNavController().navigate(action)
    }
}
