package com.toyota.oneapp.ui.garage.vehicleUpdate21mm

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.garage.Software21MMUpdate
import com.toyota.oneapp.model.garage.SoftwareVersionUpdateRequest
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.SoftwareUpdate21mmRepository
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class VehicleSoftwareUpdateViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        private val softwareUpdate21mmRepository: SoftwareUpdate21mmRepository,
    ) : BaseViewModel() {
        private val softwareUpdate = MutableLiveData<Software21MMUpdate>()
        val onSoftwareUpdate: LiveData<Software21MMUpdate>
            get() = softwareUpdate

        private val mIsSoftWareUpdate = SingleLiveEvent<Boolean>()
        val isSoftWareUpdate: LiveData<Boolean>
            get() = mIsSoftWareUpdate

        private val mSoftwareUpdateSuccess = SingleLiveEvent<Boolean>()
        val softwareUpdateSuccess: LiveData<Boolean>
            get() = mSoftwareUpdateSuccess

        fun fetchLatestSoftwareVersionAvailableDetails(
            registrationRequestId: String?,
            vin: String?,
        ) {
            viewModelScope.launch {
                showProgress()
                val resource =
                    softwareUpdate21mmRepository
                        .getSoftware21MMVersion(
                            vin ?: applicationData.getSelectedVehicle()?.vin ?: "",
                            registrationRequestId,
                        )
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.payload?.firstOrNull()?.let {
                            val software21MMUpdate =
                                Software21MMUpdate(
                                    registrationRequestId = it.registrationRequestId,
                                    vin = it.vin,
                                    titleOfMessage = it.messageContent?.titleOfMessage,
                                    message = it.messageContent?.message,
                                    termsOfUseStatement = it.messageContent?.updateContent?.termsOfUseStatement,
                                    updateInformation = it.messageContent?.updateContent?.updateInformation,
                                    currentVersion = it.messageContent?.updateContent?.currentVersion,
                                    updateVersion = it.messageContent?.updateContent?.updateVersion,
                                    caution = it.messageContent?.updateContent?.caution,
                                    workingTime = it.messageContent?.updateContent?.workingTime,
                                    contentImageUrl = it.messageContent?.updateContent?.contentImageUrlList ?: listOf(),
                                    urlOfOwnersManual = it.messageContent?.updateContent?.urlOfOwnersManual,
                                    urlOfInstructionManualMovie = it.messageContent?.updateContent?.urlOfInstructionManualMovie,
                                    howToUse = it.messageContent?.updateContent?.howToUse,
                                )
                            softwareUpdate.postValue(software21MMUpdate)
                        }
                        mIsSoftWareUpdate.postValue(resource.data?.payload?.isNotEmpty())
                    }
                    is Resource.Failure -> {
                        showErrorMessage(resource.message)
                    }
                    else -> {
                        mIsSoftWareUpdate.postValue(false)
                    }
                }
            }
        }

        fun logAnalyticEvent(
            event: String,
            paramValue: String,
        ) = analyticsLogger.logEventWithParameter(
            event,
            paramValue,
        )

        fun softwareVersionUpdateSuccess(isAuthorized: Boolean) {
            viewModelScope.launch {
                showProgress()

                val softwareVersionUpdateRequest =
                    SoftwareVersionUpdateRequest(
                        softwareUpdate.value?.registrationRequestId,
                        softwareUpdate.value?.vin,
                        isAuthorized = isAuthorized,
                    )
                val resource =
                    softwareUpdate21mmRepository
                        .updateSoftwareVersion21MM(softwareVersionUpdateRequest)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        if (resource.data != null) {
                            mSoftwareUpdateSuccess.postValue(true)
                        }
                    }
                    is Resource.Failure -> {
                        showErrorMessage(resource.message)
                        mSoftwareUpdateSuccess.postValue(false)
                    }

                    is Resource.Loading -> {}
                }
            }
        }
    }
