package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemPurchaseBundleSuccessBinding
import com.toyota.oneapp.databinding.ItemPurchaseServiceSuccessBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.subscriptionbundle.IndividualServiceAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutItemUIModel
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

class PurchaseServiceSuccessAdapter(
    private val vehicleInfo: VehicleInfo,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>(),
    BindableRecyclerViewAdapter<CheckoutItemUIModel> {
    private var items: List<CheckoutItemUIModel> = emptyList()

    companion object {
        const val ITEM_PURCHASE_SUCCESS_SERVICE = 1
        const val ITEM_PURCHASE_SUCCESS_BUNDLE = 2
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder {
        when (viewType) {
            ITEM_PURCHASE_SUCCESS_SERVICE -> {
                val binding =
                    ItemPurchaseServiceSuccessBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false,
                    )
                return ServiceViewHolder(binding)
            }

            ITEM_PURCHASE_SUCCESS_BUNDLE -> {
                val binding =
                    ItemPurchaseBundleSuccessBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false,
                    )
                return BundleViewHolder(binding)
            }

            else -> {
                throw Exception("Invalid ViewType")
            }
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        when (holder.itemViewType) {
            ITEM_PURCHASE_SUCCESS_SERVICE -> {
                val serviceVH = holder as ServiceViewHolder
                val service = items[position] as CheckoutItemUIModel.CheckoutServiceListItemUIModel
                serviceVH.bind(service, holder.itemView.context)
            }
            ITEM_PURCHASE_SUCCESS_BUNDLE -> {
                val bundleVH = holder as BundleViewHolder
                val purchaseBundle = items[position] as CheckoutItemUIModel.CheckoutBundleListItemUIModel
                bundleVH.bind(purchaseBundle)
            }
        }
    }

    override fun getItemCount(): Int = items.size

    override fun getItemViewType(position: Int): Int =
        when (items[position]) {
            is CheckoutItemUIModel.CheckoutServiceListItemUIModel -> ITEM_PURCHASE_SUCCESS_SERVICE
            is CheckoutItemUIModel.CheckoutBundleListItemUIModel -> ITEM_PURCHASE_SUCCESS_BUNDLE
        }.exhaustive

    // BindableRecyclerViewAdapter Methods
    @SuppressLint("NotifyDataSetChanged")
    override fun setData(data: List<CheckoutItemUIModel>?) {
        items = data ?: emptyList()
        notifyDataSetChanged()
    }

    inner class ServiceViewHolder(
        private val binding: ItemPurchaseServiceSuccessBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        fun bind(
            service: CheckoutItemUIModel.CheckoutServiceListItemUIModel,
            context: Context,
        ) {
            binding.tvTitle.text = service.title
            binding.tvSubTitle.text = service.subTitle
            binding.tvAutoRenew.isVisible = service.isRenewable
            if (service.isAutoRenew) {
                binding.tvAutoRenew.setText(context.getString(R.string.Subscription_Auto_Renew_On))
            } else {
                binding.tvAutoRenew.setText(context.getString(R.string.Subscription_Auto_Renew_Off))
            }
            if (adapterPosition == items.size - 1) {
                binding.divider.visibility = View.GONE
            }
        }
    }

    inner class BundleViewHolder(
        private val binding: ItemPurchaseBundleSuccessBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        fun bind(bundle: CheckoutItemUIModel.CheckoutBundleListItemUIModel) {
            val individualServiceAdapter =
                IndividualServiceAdapter(
                    vehicleInfo,
                    bundle.bundleComponents,
                )
            binding.rvBundleServices.run {
                layoutManager = LinearLayoutManager(itemView.context)
                adapter = individualServiceAdapter
                addItemDecoration(DividerItemDecoration(itemView.context, LinearLayout.VERTICAL))
            }
            binding.tvBundleTitle.text = bundle.title
            binding.tvSubscriptionAmount.text = bundle.subscriptionPrice
            binding.tvSubscriptionTerm.text = bundle.subscriptionTerm
            binding.autoRenewCheckbox.isVisible = bundle.isAutoRenew
            binding.autoRenewCheckbox.isChecked = bundle.isAutoRenew
            binding.tvAutoRenew.isVisible = bundle.isAutoRenew
        }
    }
}
