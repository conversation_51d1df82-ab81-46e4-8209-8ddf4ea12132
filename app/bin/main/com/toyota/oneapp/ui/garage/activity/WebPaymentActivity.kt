package com.toyota.oneapp.ui.garage.activity

import android.app.Activity.RESULT_OK
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.webkit.GeolocationPermissions
import android.webkit.JavascriptInterface
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityWebPaymentBinding
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager.WebPaymentType
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.garage.activity.WebPaymentActivity.Companion.ACCOUNT_ID
import com.toyota.oneapp.ui.garage.activity.WebPaymentActivity.Companion.VEHICLE_BRAND
import com.toyota.oneapp.ui.garage.activity.WebPaymentActivity.Companion.VEHICLE_GENERATION
import com.toyota.oneapp.ui.garage.activity.WebPaymentActivity.Companion.VEHICLE_REGION
import com.toyota.oneapp.ui.garage.viewmodel.WebPaymentNavigationEvent
import com.toyota.oneapp.ui.garage.viewmodel.WebPaymentViewModel
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.log.LogTool

private val WHITELIST_URL =
    arrayListOf(
        "ctpayment.stg.telematicsct.com",
        "ctpayment.telematicsct.com",
    )

@AndroidEntryPoint
class WebPaymentActivity : UiBaseActivity() {
    lateinit var binding: ActivityWebPaymentBinding
    private val viewModel: WebPaymentViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        setContentView(R.layout.activity_web_payment)
        binding =
            DataBindingUtil.setContentView<ActivityWebPaymentBinding>(
                this,
                R.layout.activity_web_payment,
            )

        performActivitySetup(findViewById(R.id.toolbar))
        showProgressDialog()
        viewModel.getRSASignature(
            intent.getSerializableExtra(ToyotaConstants.PAYMENT_TYPE) as WebPaymentType,
            intent.getBooleanExtra(ToyotaConstants.PAYMENT_METHOD_AVAILABLE, false),
            intent.getStringExtra(ACCOUNT_ID),
            intent.getStringExtra(VEHICLE_BRAND) ?: "",
            intent.getStringExtra(VEHICLE_REGION) ?: "",
            intent.getStringExtra(VEHICLE_GENERATION) ?: "",
        )

        viewModel.navigationEvents.observe(
            this,
        ) {
            when (it) {
                is WebPaymentNavigationEvent.GetRSASignatureSuccess -> {
                    binding.paymentWebview.visibility = View.VISIBLE
                    init(it.url)
                }

                is WebPaymentNavigationEvent.GetRSASignatureFailed -> {
                    hideProgressDialog()
                }
            }
        }
        viewModel.onSuccess.observe(
            this,
        ) {
            setResult(
                RESULT_OK,
                Intent().apply {
                    putExtra(ToyotaConstants.PAYMENT_TOKEN, it.token)
                    putExtra(ToyotaConstants.PAYMENT_METHOD_AVAILABLE, it.defaultPayment)
                },
            )
            finish()
        }
        viewModel.onLoadFailed.observe(
            this,
        ) {
            showFailedToLoadDialog()
        }
        viewModel.onOpenPrivacy.observe(
            this,
        ) {
            openPrivacy(it)
        }
    }

    private fun init(url: String) {
        binding.paymentWebview.apply {
            settings.apply {
                displayZoomControls = false
                javaScriptEnabled = true
                builtInZoomControls = true
                setSupportZoom(true)
                loadWithOverviewMode = true
                useWideViewPort = true
                setGeolocationEnabled(false)
                domStorageEnabled = true
                allowFileAccess = false
                allowContentAccess = true
                loadsImagesAutomatically = true
                javaScriptCanOpenWindowsAutomatically = true
                mediaPlaybackRequiresUserGesture = false
                cacheMode = WebSettings.LOAD_NO_CACHE
                mixedContentMode = WebSettings.MIXED_CONTENT_NEVER_ALLOW
            }
            setLayerType(View.LAYER_TYPE_SOFTWARE, null)
            scrollBarStyle = WebView.SCROLLBARS_OUTSIDE_OVERLAY
            setInitialScale(1)
            webViewClient = mWebViewClient
            addJavascriptInterface(
                WebAppInterface(viewModel),
                "jsHandler",
            )
            webChromeClient =
                object : WebChromeClient() {
                    override fun onGeolocationPermissionsShowPrompt(
                        origin: String,
                        callback: GeolocationPermissions.Callback,
                    ) {
                        callback.invoke(origin, true, false)
                    }

                    override fun onReceivedTitle(
                        view: WebView,
                        title: String?,
                    ) {
                        super.onReceivedTitle(view, title)
                        if (title != null) {
                            binding.toolbarWebTitleTxt.text = title
                        }
                    }
                }
            LogTool.v("WebPaymentActivity", "loading: $url")
            loadUrl(url)
        }
    }

    private fun showFailedToLoadDialog() {
        DialogUtil.showDialog(
            this,
            null,
            getString(R.string.Vin_List_Failure),
            getString(R.string.Common_okay),
            null,
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    setResult(RESULT_CANCELED)
                    finish()
                }

                override fun onCancelClick() {}
            },
            false,
        )
    }

    private fun openPrivacy(privacyURL: String) {
        if (ToyUtil.hasProtocol(privacyURL)) {
            ToyUtil.openCustomChromeTab(this, privacyURL)
        } else {
            showErrorToast(getString(R.string.collision_assistance_error))
        }
    }

    private val mWebViewClient: WebViewClient =
        object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView,
                request: WebResourceRequest,
            ): Boolean = shouldOverrideURL(this@WebPaymentActivity, view, request.url.toString())

            override fun onPageFinished(
                view: WebView?,
                url: String?,
            ) {
                super.onPageFinished(view, url)
                hideProgressDialog()
            }

            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?,
            ) {
                super.onReceivedError(view, request, error)
                showFailedToLoadDialog()
            }
        }

    companion object {
        const val ACCOUNT_ID = "account_id"
        const val VEHICLE_BRAND = "vehicle_brand"
        const val VEHICLE_REGION = "vehicle_region"
        const val VEHICLE_GENERATION = "vehicle_generation"

        private fun shouldOverrideURL(
            context: Context,
            view: WebView,
            url: String?,
        ): Boolean {
            if (url == null) return false
            try {
                if (!url.startsWith("http://") && !url.startsWith("https://")) {
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                    context.startActivity(intent)
                    return true
                }
            } catch (e: Exception) {
                return true
            }
            view.loadUrl(url)
            return isValidHost(url = url)
        }

        private fun isValidHost(url: String): Boolean {
            if (url.isNotBlank()) {
                val host = Uri.parse(url).host
                return WHITELIST_URL.contains(host)
            }
            return false
        }
    }

    inner class WebAppInterface(
        private val viewModel: WebPaymentViewModel,
    ) {
        @Suppress("unused")
        @JavascriptInterface
        fun postMessage(
            message: String,
            rfid: String,
            defaultPayment: Boolean,
        ) {
            viewModel.webEvent(message, rfid, defaultPayment)
        }
    }
}

data class WebPaymentArguments(
    val type: WebPaymentType,
    val hasPaymentMethods: Boolean,
    val accountId: String? = null,
    val vehicleBrand: String,
    val vehicleRegion: String,
    val vehicleGeneration: String,
)

sealed class WebPaymentResult {
    object Failure : WebPaymentResult()

    data class Success(
        val token: String,
        val setAsDefault: Boolean,
    ) : WebPaymentResult()
}

class WebPaymentContract : ActivityResultContract<WebPaymentArguments, WebPaymentResult>() {
    override fun createIntent(
        context: Context,
        input: WebPaymentArguments,
    ): Intent =
        Intent(context, WebPaymentActivity::class.java).apply {
            putExtra(ToyotaConstants.PAYMENT_METHOD_AVAILABLE, input.hasPaymentMethods)
            putExtra(ToyotaConstants.PAYMENT_TYPE, input.type)
            putExtra(ACCOUNT_ID, input.accountId)
            putExtra(VEHICLE_BRAND, input.vehicleBrand)
            putExtra(VEHICLE_REGION, input.vehicleRegion)
            putExtra(VEHICLE_GENERATION, input.vehicleGeneration)
        }

    override fun parseResult(
        resultCode: Int,
        intent: Intent?,
    ): WebPaymentResult =
        when (resultCode) {
            RESULT_OK ->
                intent?.run {
                    WebPaymentResult.Success(
                        getStringExtra(ToyotaConstants.PAYMENT_TOKEN)!!,
                        getBooleanExtra(ToyotaConstants.PAYMENT_METHOD_AVAILABLE, false),
                    )
                } ?: WebPaymentResult.Failure

            else -> WebPaymentResult.Failure
        }
}
