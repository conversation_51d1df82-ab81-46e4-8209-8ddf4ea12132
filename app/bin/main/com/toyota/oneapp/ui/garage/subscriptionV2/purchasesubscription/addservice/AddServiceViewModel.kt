package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.subscription.VehicleSubscriptionAlert
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.model.ManageWiFiSubscriptionDetailInitializationModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice.mapper.AddServiceUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.addservice.model.AddServiceItemUIModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel - For [AddServiceFragment]
 */
@HiltViewModel
class AddServiceViewModel
    @Inject
    constructor(
        private val savedStateHandle: SavedStateHandle,
        private val mapper: AddServiceUIMapper,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        sealed class NavigationEvent {
            data class NavigateToManageWiFiConnectSubscriptionDetailScreen(
                val initializationModel: ManageWiFiSubscriptionDetailInitializationModel,
            ) : NavigationEvent()

            data class NavigateToPurchaseSubscriptionDetailScreen(
                val selectedSubscription: AvailableSubscription,
                val trialSubscriptions: List<SubscriptionV2>,
                val paidSubscriptions: List<SubscriptionV2>,
                val availableSubscriptions: List<AvailableSubscription>,
                val accessToken: String?,
                val isAzure: Boolean?,
                val alerts: List<VehicleSubscriptionAlert>?,
            ) : NavigationEvent()
        }

        private val args = AddServiceFragmentArgs.fromSavedStateHandle(savedStateHandle)

        private val _subscriptionUIModels = MutableLiveData<List<AddServiceItemUIModel>>()
        private val _addServiceNavigationEvent = SingleLiveEvent<NavigationEvent>()

        val subscriptionUIModels: LiveData<List<AddServiceItemUIModel>>
            get() = _subscriptionUIModels
        val addServiceNavigationEvent: LiveData<NavigationEvent>
            get() = _addServiceNavigationEvent

        init {
            _subscriptionUIModels.value = mapper.map(args.availableSubscriptions.toList(), args.vehicle)
        }

        fun onServiceItemClick(selectedSubscription: AvailableSubscription) {
            analyticsLogger.logEvent(
                AnalyticsEvent.MANAGE_SUBSCRIPTION_ADD_SERVICE_SERVICE_ITEM_CLICKED,
                SubscriptionConstants.ANALYTICS_KEY_SERVICE to selectedSubscription.displayProductName,
            )

            if (selectedSubscription.productLine == SubscriptionConstants.WIFI_CONNECT) {
                // WIFI Connect Service.
                _addServiceNavigationEvent.value =
                    NavigationEvent.NavigateToManageWiFiConnectSubscriptionDetailScreen(
                        initializationModel =
                            SubscriptionUtil.createWiFiSubscriptionInitializationModel(
                                selectedSubscription,
                            ),
                    )
            } else {
                // Other Services.
                _addServiceNavigationEvent.value =
                    NavigationEvent.NavigateToPurchaseSubscriptionDetailScreen(
                        selectedSubscription = selectedSubscription,
                        trialSubscriptions = args.trialSubscriptions.toList(),
                        paidSubscriptions = args.paidSubscriptions.toList(),
                        availableSubscriptions = args.availableSubscriptions.toList(),
                        accessToken = args.accessToken,
                        isAzure = args.isAzure,
                        alerts = args.alerts?.toList(),
                    )
            }
        }
    }
