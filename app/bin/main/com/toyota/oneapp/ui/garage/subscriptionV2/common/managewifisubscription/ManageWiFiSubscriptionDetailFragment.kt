package com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription

import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import com.toyota.one_ui.OneUIUtil
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentManageWifiSubscriptionDetailBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.ServiceDetailWebviewClient
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

/**
 * A Fragment - Displays service detail for WiFi connect.
 */
@AndroidEntryPoint
class ManageWiFiSubscriptionDetailFragment : BaseDataBindingFragment<FragmentManageWifiSubscriptionDetailBinding>() {
    private val viewModel: ManageWiFiSubscriptionDetailViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setHasOptionsMenu(true)
    }

    override fun onViewBound(
        binding: FragmentManageWifiSubscriptionDetailBinding,
        savedInstance: Bundle?,
    ) {
        setUpViewModelBindings()
        populateView()
    }

    override fun getLayout(): Int = R.layout.fragment_manage_wifi_subscription_detail

    override fun onCreateOptionsMenu(
        menu: Menu,
        inflater: MenuInflater,
    ) {
        inflater.inflate(R.menu.menu_manage_wifi_subscription_detail, menu)
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)

        menu.findItem(R.id.call).isVisible = viewModel.isCallActionEnabled()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            R.id.call -> {
                viewModel.onCallClick()
                true
            }

            else -> {
                super.onOptionsItemSelected(item)
            }
        }

    private fun setUpViewModelBindings() {
        viewModel.event.observe(
            viewLifecycleOwner,
            Observer { event ->
                handleEvent(event)
            },
        )
    }

    private fun populateView() {
        context?.let {
            viewDataBinding.descriptionWebview.webViewClient =
                ServiceDetailWebviewClient(
                    it,
                    viewDataBinding.webviewProgress,
                )

            viewModel.uiModel.observe(viewLifecycleOwner) { uiModel ->
                DataBindingAdapters.loadImage(
                    viewDataBinding.ivImage,
                    uiModel.productImageUrl,
                    null,
                )
                viewDataBinding.tvName.text = uiModel.productName

                val htmlData = OneUIUtil.appendBodyContentToHtml(uiModel.productLongDesc, BuildConfig.IS_TOYOTA_APP)
                DataBindingAdapters.loadHTMLData(viewDataBinding.descriptionWebview, htmlData)

                viewDataBinding.ivCarrierIcon.setImageResource(uiModel.carrierIcon)

                viewDataBinding.tvManageAccountInfo.run {
                    text = uiModel.actionInfoText
                    DataBindingAdapters.setIsVisible(this, uiModel.actionInfoText.isNotEmpty())
                }

                viewDataBinding.btnAction.run {
                    text = uiModel.actionText
                    setOnClickListener { viewModel.onActionClick() }
                }
            }
        }
    }

    private fun handleEvent(event: ManageWiFiSubscriptionDetailViewModel.Event) {
        when (event) {
            is ManageWiFiSubscriptionDetailViewModel.Event.NavigateToWiFiSubscriptionCarrierScreen -> {
                ToyUtil.openCustomChromeTab(requireContext(), event.url)
            }

            is ManageWiFiSubscriptionDetailViewModel.Event.NavigateToCallCarrierScreen -> {
                ToyUtil.phoneCall(requireContext(), event.number)
            }
        }
    }
}
