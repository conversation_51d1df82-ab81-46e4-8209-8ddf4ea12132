package com.toyota.oneapp.ui.garage.paymentOptions

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.*
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemPaymentOptionBinding
import com.toyota.oneapp.model.subscription.PaymentRecord

class PaymentOptionsAdapter(
    private val parameters: Parameters,
    private val paymentRecords: ManagePaymentOptionsViewModel.OptionListData,
) : RecyclerView.Adapter<PaymentOptionsAdapter.PaymentOptionHolder>() {
    private val viewOptionHolders: MutableList<PaymentOptionHolder> = mutableListOf()

    override fun getItemCount() = paymentRecords.paymentOptions.size

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): PaymentOptionHolder {
        val binding =
            DataBindingUtil.inflate<ItemPaymentOptionBinding>(
                parameters.layoutInflater,
                R.layout.item_payment_option,
                parent,
                false,
            )
        val viewHolder = PaymentOptionHolder(binding)
        binding.lifecycleOwner = viewHolder
        viewHolder.markCreated()
        viewOptionHolders.add(viewHolder)

        return viewHolder
    }

    override fun onBindViewHolder(
        optionHolder: PaymentOptionHolder,
        position: Int,
    ) {
        optionHolder.bind(
            parameters,
            paymentRecords.isInEditMode,
            paymentRecords.paymentOptions[position],
        )
    }

    override fun onViewAttachedToWindow(optionHolder: PaymentOptionHolder) {
        super.onViewAttachedToWindow(optionHolder)
        optionHolder.markAttach()
    }

    override fun onViewDetachedFromWindow(optionHolder: PaymentOptionHolder) {
        super.onViewDetachedFromWindow(optionHolder)
        optionHolder.markDetach()
    }

    fun setLifecycleDestroyed() {
        viewOptionHolders.forEach {
            it.markDestroyed()
        }
    }

    class PaymentOptionHolder(
        private val binding: ItemPaymentOptionBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ),
        LifecycleOwner { // PaymentOptionHolder class

        private val lifecycleRegistry = LifecycleRegistry(this)
        private var wasPaused: Boolean = false
        private val viewModel: ItemPaymentOptionViewModel = ItemPaymentOptionViewModel()

        init {
            lifecycleRegistry.currentState = Lifecycle.State.INITIALIZED
        }

        fun bind(
            parameters: Parameters,
            isInEditMode: Boolean,
            paymentRecord: PaymentRecord,
        ) {
            binding.apply {
                viewModel.extractViewData(
                    paymentRecord,
                    isInEditMode,
                    parameters.areSubscriptionsPresent,
                )
                executePendingBindings()
                viewModel.state.observeForever {
                    binding.paymentOptionDelete.isVisible = it.deleteEnabled
                    binding.paymentOptionLogo.setImageResource(it.logoImageSrc)
                    binding.paymentOptionAccountNumber.text = it.accountNumber
                    binding.paymentOptionDefault.isVisible = it.isDefaultPaymentOptionVisible
                    binding.paymentOptionRightArrow.isVisible = !it.editEnabled
                }
                binding.paymentOptionDelete.setOnClickListener {
                    viewModel.onDelete()
                }
                binding.clMain.setOnClickListener {
                    viewModel.onClick()
                }
            }

            viewModel.let {
                parameters.clickObserver.addOptionClickSource(it.optionClicked)
                parameters.clickObserver.addDeleteClickSource(it.deleteClicked)
            }
        }

        fun markCreated() {
            lifecycleRegistry.currentState = Lifecycle.State.CREATED
        }

        fun markAttach() {
            if (wasPaused) {
                lifecycleRegistry.currentState = Lifecycle.State.RESUMED
                wasPaused = false
            } else {
                lifecycleRegistry.currentState = Lifecycle.State.STARTED
            }
        }

        fun markDetach() {
            wasPaused = true
            lifecycleRegistry.currentState = Lifecycle.State.CREATED
        }

        fun markDestroyed() {
            lifecycleRegistry.currentState = Lifecycle.State.DESTROYED
        }

        override val lifecycle: Lifecycle
            get() = lifecycleRegistry
    }

    interface ClickObserver {
        fun addOptionClickSource(editClickSource: LiveData<PaymentRecord>)

        fun addDeleteClickSource(deleteClickSource: LiveData<PaymentRecord>)
    } // ClickObserver interface

    data class Parameters(
        val areSubscriptionsPresent: Boolean,
        val layoutInflater: LayoutInflater,
        val clickObserver: ClickObserver,
    )
} // PaymentOptionsAdapter class
