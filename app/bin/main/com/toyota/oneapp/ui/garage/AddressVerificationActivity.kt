package com.toyota.oneapp.ui.garage

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityAddressVerificationBinding
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.model.subscription.AddressEnteredByUser
import com.toyota.oneapp.model.subscription.Addresses
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class AddressVerificationActivity : UiBaseActivity() {
    lateinit var dataBinding: ActivityAddressVerificationBinding
    private var suggestedAddressObj: Addresses? = null
    private lateinit var getSelectedAddress: String
    private var enteredByUserObject: AddressEnteredByUser? = null
    private val viewModel: AddressVerificationViewModel by viewModels()

    @Inject
    lateinit var applicationData: ApplicationData

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger
    private lateinit var accountInfoSubscriber: AccountInfoSubscriber

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        dataBinding = DataBindingUtil.setContentView(this, R.layout.activity_address_verification)
        dataBinding.viewModel = viewModel
        dataBinding.lifecycleOwner = this
        dataBinding.executePendingBindings()
        observeBaseEvents(viewModel)
        performActivitySetup(dataBinding.addVerificationToolbar)

        suggestedAddressObj =
            intent.extras?.getParcelable<Addresses>(
                ToyotaConstants.SUGGESTED_ADDRESS_OBJ,
            )
        enteredByUserObject =
            intent.extras?.getParcelable<AddressEnteredByUser>(
                ToyotaConstants.ENTERED_ADDRESS_OBJ,
            )
        accountInfoSubscriber = AccountInfoSubscriber()
        dataBinding.enteredAddressTextview.text =
            getString(
                R.string.entered_address_text,
                enteredByUserObject?.streetAddress,
                enteredByUserObject?.enteredCity,
                enteredByUserObject?.enteredState,
                enteredByUserObject?.enteredZipCode,
                enteredByUserObject?.enteredCountry,
            )
        dataBinding.suggestedAddressTextview.text =
            getString(
                R.string.entered_address_text,
                suggestedAddressObj?.address1,
                suggestedAddressObj?.city,
                suggestedAddressObj?.state,
                suggestedAddressObj?.zipCode,
                suggestedAddressObj?.country,
            )
        viewModel.onSelectAddressClick.observe(this, Observer { savedSelectedAddress(it) })
        viewModel.adiUpdateSuccessfully.observe(this, Observer { onApiCallResponse(it) })
        viewModel.onAddressEditClicked.observe(this, Observer { finish() })
        initAddressSuggestionUI(suggestedAddressObj)
    }

    private fun onApiCallResponse(status: Boolean) {
        if (status) {
            val i = Intent()
            if ("1" == getSelectedAddress) {
                i.putExtra(ToyotaConstants.COUNTRY, enteredByUserObject?.enteredCountry)
                i.putExtra(ToyotaConstants.CITY, enteredByUserObject?.enteredCity)
                i.putExtra(ToyotaConstants.ZIP_CODE, enteredByUserObject?.enteredZipCode)
                i.putExtra(ToyotaConstants.STREET_ADDRESS, enteredByUserObject?.streetAddress)
                i.putExtra(ToyotaConstants.STATE, enteredByUserObject?.enteredState)
                setResult(Activity.RESULT_OK, i)
            } else {
                i.putExtra(ToyotaConstants.COUNTRY, suggestedAddressObj?.country)
                i.putExtra(ToyotaConstants.CITY, suggestedAddressObj?.city)
                i.putExtra(ToyotaConstants.ZIP_CODE, suggestedAddressObj?.zipCode)
                i.putExtra(ToyotaConstants.STREET_ADDRESS, suggestedAddressObj?.address1)
                i.putExtra(ToyotaConstants.STATE, suggestedAddressObj?.state)
                setResult(Activity.RESULT_OK, i)
            }
        }
        finish()
    }

    private fun savedSelectedAddress(isClicked: Boolean?) {
        if (isClicked == true) {
            if (dataBinding.radioButtonYouEntered.isChecked) {
                getSelectedAddress = "1"
                if (viewModel.isAccountUpdated(
                        enteredByUserObject?.streetAddress,
                        enteredByUserObject?.enteredCity,
                        enteredByUserObject?.enteredCountry,
                        enteredByUserObject?.enteredZipCode,
                        enteredByUserObject?.enteredState,
                        getAccountCustomerAddress(),
                    )
                ) {
                    onApiCallResponse(true)
                } else {
                    viewModel.updateAccountInfo(
                        enteredByUserObject?.streetAddress,
                        enteredByUserObject?.enteredCity,
                        enteredByUserObject?.enteredState,
                        enteredByUserObject?.enteredZipCode,
                        enteredByUserObject?.enteredCountry,
                    )
                }
            } else if (dataBinding.radioButtonWeSuggest.isChecked) {
                getSelectedAddress = "2"
                if (viewModel.isAccountUpdated(
                        suggestedAddressObj?.address1,
                        suggestedAddressObj?.city,
                        suggestedAddressObj?.country,
                        suggestedAddressObj?.zipCode,
                        suggestedAddressObj?.state,
                        getAccountCustomerAddress(),
                    )
                ) {
                    onApiCallResponse(true)
                } else {
                    viewModel.updateAccountInfo(
                        suggestedAddressObj?.address1,
                        suggestedAddressObj?.city,
                        suggestedAddressObj?.state,
                        suggestedAddressObj?.zipCode,
                        suggestedAddressObj?.country,
                    )
                }
            }
        }
    }

    private fun getAccountCustomerAddress() =
        if (accountInfoSubscriber.customerAddresses != null && accountInfoSubscriber.customerAddresses.isNotEmpty()) {
            accountInfoSubscriber.customerAddresses[0]
        } else {
            null
        }

    private fun initAddressSuggestionUI(suggestedAddressObj: Addresses?) {
        if (suggestedAddressObj?.resultPercentage?.toDouble()!! > 0.00) {
            dataBinding.textViewAddressVerification.text =
                getString(
                    R.string.subscription_address_verification_text_with_suggestion,
                )
            dataBinding.radioButtonWeSuggest.isChecked = true
        } else {
            dataBinding.textViewAddressVerification.text =
                getString(
                    R.string.subscription_address_verfication_no_suggestion,
                )
            dataBinding.radioButtonWeSuggest.visibility = View.GONE
            dataBinding.suggestedAddressTextview.visibility = View.GONE
            dataBinding.radioButtonYouEntered.isChecked = true
        }
    }
}
