package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success.mapper

import android.content.Context
import com.toyota.oneapp.R
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success.model.PurchaseServiceSuccessInitializationModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success.model.PurchaseServiceSuccessUIModel
import com.toyota.oneapp.util.DateUtil
import java.util.*
import javax.inject.Inject

class PurchaseServiceSuccessUIMapper
    @Inject
    constructor(
        private val context: Context,
        private val dateUtil: DateUtil,
    ) {
        fun map(
            initializationModel: PurchaseServiceSuccessInitializationModel,
            vehicle: VehicleInfo,
        ): PurchaseServiceSuccessUIModel {
            return with(initializationModel) {
                val vehicleModel = "${vehicle.modelYear.orEmpty()} ${vehicle.modelName.orEmpty()}"
                val purchasedDateInfo =
                    context.getString(
                        R.string.purchase_submitted_date_info,
                        dateUtil.formatMediumDate(Date()),
                    )

                return@with PurchaseServiceSuccessUIModel(
                    vehicleImage = vehicle.image.orEmpty(),
                    vehicleModel = vehicleModel,
                    totalPrice = totalPrice,
                    purchasedDateInfo = purchasedDateInfo,
                    purchasedServices = purchasedServices,
                )
            }
        }
    }
