package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.webkit.*
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivitySubscriptionV2PaymentCy17Binding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Args
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Result
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17UIModel
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

/**
 * A Fragment - responsible to get Payment details from User for CY17 & before generation vehicles.
 */
@AndroidEntryPoint
class SubscriptionV2PaymentCY17Activity : UiBaseActivity() {
    private val viewModel: SubscriptionV2PaymentCY17ViewModel by viewModels()

    private lateinit var binding: ActivitySubscriptionV2PaymentCy17Binding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        binding =
            DataBindingUtil.setContentView(
                this,
                R.layout.activity_subscription_v2_payment_cy17,
            )
        binding.lifecycleOwner = this
        binding.viewModel = viewModel

        initializeViews()
        setUpViewModelBindings()
    }

    private fun initializeViews() {
        observeBaseEvents(viewModel)

        performActivitySetup(binding.toolbar)

        initializeWebView()
    }

    private fun initializeWebView() {
        binding.webView.apply {
            settings.apply {
                displayZoomControls = false
                javaScriptEnabled = true
                builtInZoomControls = true
                setSupportZoom(true)
                loadWithOverviewMode = true
                useWideViewPort = true
                setGeolocationEnabled(false)
                domStorageEnabled = true
                allowFileAccess = false
                allowContentAccess = true
                loadsImagesAutomatically = true
                javaScriptCanOpenWindowsAutomatically = true
                mediaPlaybackRequiresUserGesture = false
                cacheMode = WebSettings.LOAD_NO_CACHE
                mixedContentMode = WebSettings.MIXED_CONTENT_NEVER_ALLOW
            }
            setLayerType(View.LAYER_TYPE_SOFTWARE, null)
            scrollBarStyle = WebView.SCROLLBARS_OUTSIDE_OVERLAY
            setInitialScale(1)
            webViewClient = WebViewClient()
            addJavascriptInterface(
                WebAppInterface(viewModel),
                "jsHandler",
            )
            webChromeClient = WebChromeClient()
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.uiModel.observe(
            this,
            Observer { uiModel ->
                populateWebView(uiModel)
            },
        )
        viewModel.event.observe(
            this,
            Observer { event ->
                handleEvents(event)
            },
        )
    }

    private fun populateWebView(uiModel: PaymentCY17UIModel) {
        with(uiModel) {
            val headers = HashMap<String, String>()
            headers["cv-ip-cookie-request"] = "ip"
            headers["cv-cookie-at"] = authorization
            headers["Content-Type"] = "application/json"
            headers["cv-cookie-g"] = guid
            headers["cv-cookie-ot"] = "purchase"
            headers["cv-cookie-ta"] = totalAmount
            headers["cv-cookie-v"] = vin
            headers["cv-cookie-tt"] = totalTax
            headers["cv-cookie-b"] = brand
            headers["cv-cookie-po"] = productIds
            headers["cv-cookie-l"] = language

            binding.webView.loadUrl(url, headers)
        }
    }

    private fun handleEvents(event: SubscriptionV2PaymentCY17ViewModel.Event) {
        when (event) {
            is SubscriptionV2PaymentCY17ViewModel.Event.PaymentAddedSuccessfully ->
                onPaymentAddedSuccessfully(
                    event.result,
                )
            is SubscriptionV2PaymentCY17ViewModel.Event.Error -> onError()
            is SubscriptionV2PaymentCY17ViewModel.Event.OnBack -> <EMAIL>()
        }
    }

    private fun onPaymentAddedSuccessfully(result: PaymentCY17Result) {
        val intent = Intent()
        intent.putExtra(SubscriptionV2PaymentCY17Contract.EXTRA_RESULT, result)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    private fun onError() {
        DialogUtil.showDialog(
            this,
            getString(R.string.Login_error),
            getString(R.string.generic_error),
            getString(R.string.Common_ok),
            null,
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    <EMAIL>()
                }

                override fun onCancelClick() {
                }
            },
            false,
        )
    }

    inner class WebAppInterface(
        private val viewModel: SubscriptionV2PaymentCY17ViewModel,
    ) {
        @Suppress("unused")
        @JavascriptInterface
        fun postMessage(message: String) {
            viewModel.webEvent(message)
        }
    }
}

class SubscriptionV2PaymentCY17Contract : ActivityResultContract<PaymentCY17Args, PaymentCY17Result?>() {
    companion object {
        const val EXTRA_ARGS = "EXTRA_ARGS"
        const val EXTRA_RESULT = "EXTRA_RESULT"
    }

    override fun createIntent(
        context: Context,
        input: PaymentCY17Args,
    ): Intent {
        val intent = Intent(context, SubscriptionV2PaymentCY17Activity::class.java)
        intent.putExtra(EXTRA_ARGS, input)
        return intent
    }

    override fun parseResult(
        resultCode: Int,
        intent: Intent?,
    ): PaymentCY17Result? =
        when (resultCode) {
            Activity.RESULT_OK -> {
                intent?.run {
                    getParcelableExtra<PaymentCY17Result>(EXTRA_RESULT)
                }
            }
            else -> {
                null
            }
        }
}
