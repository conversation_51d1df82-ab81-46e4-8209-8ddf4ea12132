package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.cy17

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.extensions.getLocale
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscription.*
import com.toyota.oneapp.model.subscriptionV2.subscriptionStartDate
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.model.ConfirmPaymentItemModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.model.ConfirmPaymentUIModel
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.DoubleUtil.toDisplayPrice
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel - for [ConfirmPaymentForCY17Fragment]
 */
@HiltViewModel
class ConfirmPaymentForCY17ViewModel
    @Inject
    constructor(
        private val context: Context,
        private val savedStateHandle: SavedStateHandle,
        private val dateUtil: DateUtil,
        private val preferenceModel: OneAppPreferenceModel,
        private val combinedDataConsentRepository: CombinedDataConsentRepository,
        private val languageManager: LanguageManager,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        sealed class Event {
            object PaymentSuccessNavigation : Event()
        }

        private val _uiModel = MutableLiveData<ConfirmPaymentUIModel>()
        private val _event = SingleLiveEvent<Event>()

        val uiModel: LiveData<ConfirmPaymentUIModel>
            get() = _uiModel
        val event: LiveData<Event>
            get() = _event

        private val args = ConfirmPaymentForCY17FragmentArgs.fromSavedStateHandle(savedStateHandle)

        init {
            populateView()
        }

        private fun populateView() {
            _uiModel.value = createUIModel()
        }

        private fun createUIModel(): ConfirmPaymentUIModel {
            with(args) {
                val vehicleLocale = vehicle.getLocale(language = languageManager.getCurrentLanguage())
                val taxedProduct =
                    taxResponse.payload.taxResponse.taxedProducts
                        .first()
                val displayTerm =
                    context.getString(
                        SubscriptionUtil.getDisplayTermUnit(
                            subscriptionPackage.subscriptionTerm
                                ?: "",
                        ),
                    )

                val items =
                    listOf(
                        ConfirmPaymentItemModel(
                            name = subscription.displayProductName,
                            subTitle = null,
                            value = "${subscriptionPackage.price?.toDisplayPrice(vehicleLocale)}/" + displayTerm,
                            isBold = false,
                        ),
                        ConfirmPaymentItemModel(
                            name = context.getString(R.string.subscription),
                            subTitle = null,
                            value = subscriptionPackage.displaySubscriptionTerm ?: "",
                            isBold = false,
                        ),
                        ConfirmPaymentItemModel(
                            name = context.getString(R.string.start_date),
                            subTitle = null,
                            value =
                                subscriptionPackage.subscriptionStartDate(dateUtil)?.let {
                                    dateUtil.formatMediumDate(
                                        it,
                                    )
                                }
                                    ?: "",
                            isBold = false,
                        ),
                        ConfirmPaymentItemModel(
                            name = context.getString(R.string.Subscription_subtotal),
                            subTitle = null,
                            value = taxedProduct.productPrice.amount.toDisplayPrice(vehicleLocale),
                            isBold = false,
                        ),
                        ConfirmPaymentItemModel(
                            name = context.getString(R.string.Subscription_tax),
                            subTitle = null,
                            value =
                                taxedProduct.taxAmount.totalAmount.amount.toDisplayPrice(
                                    vehicleLocale,
                                ),
                            isBold = false,
                        ),
                        ConfirmPaymentItemModel(
                            name = context.getString(R.string.Subscription_total),
                            subTitle = null,
                            value = taxedProduct.totalAmount.amount.toDisplayPrice(vehicleLocale),
                            isBold = true,
                        ),
                    )
                return ConfirmPaymentUIModel(
                    serviceName = subscription.displayProductName,
                    items = items,
                )
            }
        }

        fun createSubscription() {
            with(args) {
                analyticsLogger.logEvent(
                    com.toyota.oneapp.analytics.AnalyticsEvent.MANAGE_SUBSCRIPTION_PURCHASE_FLOW_CONFIRM_PURCHASE_CLICKED,
                    SubscriptionConstants.ANALYTICS_KEY_SERVICE to subscription.displayProductName,
                    SubscriptionConstants.ANALYTICS_KEY_SUBSCRIPTION_TYPE to subscriptionPackage.displaySubscriptionTerm,
                    SubscriptionConstants.ANALYTICS_KEY_AUTO_RENEW to false,
                )

                val subscriptions = arrayListOf<PreviewSubscriptionItem>()
                // for cy17 & pre-cy17 vehicle, auto renew always false.
                val previewSubscriptionItem =
                    SubscriptionUtil.convertAvailableSubscriptionToPreviewSubscriptionItem(
                        subscription = subscription,
                        subscriptionPackage = subscriptionPackage,
                        isAutoRenew = false,
                    )
                subscriptions.add(previewSubscriptionItem)

                val subscriptionGetPayload = SubscriptionGetPayload(vehicle, preferenceModel.getGuid())
                subscriptionGetPayload.list = arrayListOf()
                subscriptionGetPayload.accessToken = args.accessToken ?: ""

                val currency = subscriptionPackage.currency ?: "USD"
                val productAmount = taxResponse.payload.taxResponse.productTotalAmount.amount
                val productTotalAmount = taxResponse.payload.taxResponse.totalPurchaseAmount.amount
                val tax = taxResponse.payload.taxResponse.taxTotalAmount.amount

                val productsTotalAmount = ProductsTotalAmount(currency, productTotalAmount)
                val productsAmount = ProductsAmount(currency, productAmount)
                val taxAmount = TaxAmount(currency, tax)

                subscriptionGetPayload.list.clear()
                for (i in 0 until subscriptions.size) {
                    val next = SubscriptionUIItem()
                    next.previewSubscriptionItem = subscriptions[i]
                    subscriptionGetPayload.list.add(next)
                }

                val capabilities = vehicle.capabilityItems

                viewModelScope.launch {
                    showProgress()
                    val resource =
                        combinedDataConsentRepository.createSubscription(
                            asiCode = args.vehicle.asiCode.orEmpty(),
                            hwtType = args.vehicle.hwType.orEmpty(),
                            refId = args.paymentAccessToken,
                            isPaymentDefault = false,
                            paymentToken = args.paymentAccessToken,
                            accessToken = args.accessToken.orEmpty(),
                            subscriptionGetPayload = subscriptionGetPayload,
                            capabilities = capabilities,
                            consent = args.consents.toList(),
                            totalAmount = productsTotalAmount,
                            productAmount = productsAmount,
                            taxAmount = taxAmount,
                        )

                    when (resource) {
                        is Resource.Success -> {
                            hideProgress()
                            _event.postValue(Event.PaymentSuccessNavigation)
                        }

                        is Resource.Failure -> {
                            hideProgress()
                            showErrorMessage(R.string.generic_error)
                        }

                        is Resource.Loading -> {}
                    }
                }
            }
        }
    }
