package com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.detail

import android.os.Bundle
import androidx.core.view.isVisible
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.one_ui.OneUIUtil
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.FragmentPaidSubscriptionDetailBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.common.subscriptionbundle.IndividualServiceAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.ServiceDetailWebviewClient
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * A Fragment - displays Service information of My Paid Subscription.
 */
@AndroidEntryPoint
class PaidSubscriptionDetailFragment : BaseDataBindingFragment<FragmentPaidSubscriptionDetailBinding>() {
    @Inject lateinit var analyticsLogger: AnalyticsLogger
    private val args: PaidSubscriptionDetailFragmentArgs by navArgs()

    override fun onViewBound(
        binding: FragmentPaidSubscriptionDetailBinding,
        savedInstance: Bundle?,
    ) {
        initializeViews()
        addListeners()
    }

    override fun getLayout(): Int = R.layout.fragment_paid_subscription_detail

    private fun initializeViews() {
        // Show AutoRenew - Only to CY17PLUS vehicle & Subscription should be renewable.
        viewDataBinding.tvAutoRenew.isVisible =
            (!args.vehicle.isCY17 && args.selectedSubscription.renewable)
        // Show Manage Subscription only to Primary Account Holder
        viewDataBinding.cvCard.isVisible = args.vehicle.isPrimarySubscriber
        viewDataBinding.rvBundleComponents.isVisible =
            args.selectedSubscription.category == "BUNDLE"
        if (args.selectedSubscription.category == "BUNDLE") {
            val individualServiceAdapter =
                IndividualServiceAdapter(
                    args.vehicle,
                    args.selectedSubscription.components ?: emptyList(),
                    true,
                )
            viewDataBinding.rvBundleComponents.run {
                layoutManager = LinearLayoutManager(requireContext())
                adapter = individualServiceAdapter
            }
        }
        context?.let {
            viewDataBinding.descriptionWebview.webViewClient =
                ServiceDetailWebviewClient(
                    it,
                    viewDataBinding.webviewProgress,
                )
        }
        args.selectedSubscription.let {
            viewDataBinding.tvName.text = it.displayProductName
            DataBindingAdapters.loadHTMLData(
                viewDataBinding.descriptionWebview,
                OneUIUtil.appendBodyContentToHtml(it.formattedProductDesc, BuildConfig.IS_TOYOTA_APP, it.productLongDesc),
            )
            val autoText = if (it.autoRenew == true) R.string.Subscription_Auto_Renew_On else R.string.Subscription_Auto_Renew_Off
            viewDataBinding.tvAutoRenew.setText(autoText)
            viewDataBinding.tvDisplayTerm.text = it.displayTerm
            viewDataBinding.tvDisplayTerm.isVisible = it.hideSubscriptionStatus == null || it.hideSubscriptionStatus == false
            DataBindingAdapters.loadImage(viewDataBinding.ivImage, it.productImageUrl)
        }
    }

    private fun addListeners() {
        viewDataBinding.btnManageSubscription.setOnClickListener {
            navigateToManageSubscriptionScreen()
        }
    }

    private fun navigateToManageSubscriptionScreen() {
        with(args) {
            analyticsLogger.logEvent(
                com.toyota.oneapp.analytics.AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_DETAIL_MANAGE_SUBSCRIPTION_CLICKED,
                SubscriptionConstants.ANALYTICS_KEY_SERVICE to selectedSubscription.displayProductName,
            )

            val action =
                PaidSubscriptionDetailFragmentDirections.actionPaidSubscriptionDetailFragmentToUpdateCancelPaidSubscriptionFragment(
                    vehicle = args.vehicle,
                    selectedSubscription = selectedSubscription,
                    trialSubscriptions = trialSubscriptions,
                    paidSubscriptions = paidSubscriptions,
                )
            findNavController().navigate(action)
        }
    }
}
