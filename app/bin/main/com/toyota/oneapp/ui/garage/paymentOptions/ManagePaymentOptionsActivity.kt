package com.toyota.oneapp.ui.garage.paymentOptions

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.databinding.Observable
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivitySubscriptionPaymentBinding
import com.toyota.oneapp.ui.baseClasses.DialogEventObserver
import com.toyota.oneapp.ui.baseClasses.DialogEventObserverDelegate
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.garage.activity.WebPaymentContract
import com.toyota.oneapp.ui.garage.activity.WebPaymentResult
import com.toyota.oneapp.ui.garage.paidSubscriptions.PaidSubscriptionData.Companion.buildFromIntent
import com.toyota.oneapp.ui.garage.paymentOptions.ManagePaymentOptionsViewModel.NextActivityData
import com.toyota.oneapp.ui.garage.paymentOptions.model.PaymentCY17PlusResult
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ManagePaymentOptionsActivity : UiBaseActivity() {
    companion object {
        // If the value is TRUE - Then the selected payment will sent in Activity Result.
        const val EXTRA_IS_GET_SELECTED_PAYMENT_INFORMATION_AS_RESULT =
            "IS_GET_SELECTED_PAYMENT_INFORMATION_AS_RESULT"
        const val EXTRA_RESULT = "EXTRA_RESULT"
    }

    private val viewModel: ManagePaymentOptionsViewModel by viewModels()

    private val dialogObserver: DialogEventObserver = DialogEventObserverDelegate()

    private var isGetSelectedPaymentInformationAsResult = false
    private var areSubscriptionsPresent = false

    private lateinit var creditCardRecyclerViewWrapper: RecyclerViewWrapper
    private lateinit var bankAccountRecyclerViewWrapper: RecyclerViewWrapper

    private val addPaymentContract =
        registerForActivityResult(WebPaymentContract()) { result ->
            if (result is WebPaymentResult.Success) {
                continueToNextActivity(NextActivityData(result.token, result.setAsDefault))
            }
        }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        val activityMainBinding =
            DataBindingUtil.setContentView<ActivitySubscriptionPaymentBinding>(
                this,
                R.layout.activity_subscription_payment,
            )
        activityMainBinding.lifecycleOwner = this
        activityMainBinding.executePendingBindings()

        val visibilityLiveData = MutableLiveData<Triple<Boolean?, Boolean?, Boolean?>>()

        viewModel.isInEditMode.observe(this@ManagePaymentOptionsActivity) { isInEditMode ->
            visibilityLiveData.value = Triple(isInEditMode, visibilityLiveData.value?.second, visibilityLiveData.value?.third)
            activityMainBinding.btnEdit.text =
                if (isInEditMode) {
                    resources.getString(R.string.done_label)
                } else {
                    resources.getString(
                        R.string.common_edit,
                    )
                }
            DataBindingAdapters.setIsVisible(activityMainBinding.addCreditCardLayout, !isInEditMode)
        }
        viewModel.isACHPaymentMethodAvailable.addOnPropertyChangedCallback(
            object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(
                    sender: Observable?,
                    propertyId: Int,
                ) {
                    val isACHPaymentMethodAvailable = (sender as ObservableBoolean).get()
                    visibilityLiveData.value =
                        Triple(visibilityLiveData.value?.first, isACHPaymentMethodAvailable, visibilityLiveData.value?.third)

                    DataBindingAdapters.setIsVisible(activityMainBinding.bankAccountRecyclerview, isACHPaymentMethodAvailable)
                }
            },
        )
        viewModel.isACHEligible.addOnPropertyChangedCallback(
            object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(
                    sender: Observable?,
                    propertyId: Int,
                ) {
                    val isACHEligible = (sender as ObservableBoolean).get()
                    visibilityLiveData.value = Triple(visibilityLiveData.value?.first, visibilityLiveData.value?.second, isACHEligible)
                }
            },
        )

        visibilityLiveData.observe(this@ManagePaymentOptionsActivity) { triple ->
            val isInEditMode = triple.first ?: false
            val isACHPaymentMethodAvailable = triple.second ?: false
            val isACHEligible = triple.third ?: false
            DataBindingAdapters.setIsVisible(activityMainBinding.tvBankAccount, isACHPaymentMethodAvailable || isACHEligible)
            DataBindingAdapters.setIsVisible(activityMainBinding.addBankAccountLayout, isACHEligible && !isInEditMode)
        }

        activityMainBinding.btnEdit.setOnClickListener { viewModel.onEditClick() }
        activityMainBinding.addCreditCardLayout.setOnClickListener { viewModel.onAddCreditCardClick() }
        activityMainBinding.addBankAccountLayout.setOnClickListener { viewModel.onAddBankAccountClick() }

        performActivitySetup(activityMainBinding.toolbar)

        val paidSubscriptionData = buildFromIntent(intent)
        areSubscriptionsPresent =
            paidSubscriptionData.subscriptionGetPayload?.subscriptions?.isNotEmpty()
                ?: false
        isGetSelectedPaymentInformationAsResult =
            intent.getBooleanExtra(
                EXTRA_IS_GET_SELECTED_PAYMENT_INFORMATION_AS_RESULT,
                false,
            )

        creditCardRecyclerViewWrapper =
            RecyclerViewWrapper(
                activityMainBinding.creditCardRecyclerview,
                this,
            )
        bankAccountRecyclerViewWrapper =
            RecyclerViewWrapper(
                activityMainBinding.bankAccountRecyclerview,
                this,
            )

        setObservers()
        observeBaseEvents(viewModel)
        viewModel.populatePaymentMethods()
    }

    private fun setObservers() {
        dialogObserver.observeDialog(this, viewModel)
        viewModel.startNextActivity.observe(this, Observer { continueToNextActivity(it) })
        viewModel.onAddPayment.observe(this, Observer { addPaymentContract.launch(it) })

        val parameters =
            PaymentOptionsAdapter.Parameters(
                areSubscriptionsPresent = areSubscriptionsPresent,
                layoutInflater = layoutInflater,
                clickObserver = viewModel,
            )
        viewModel.creditCardOptions.observe(
            this,
            Observer { creditCardRecyclerViewWrapper.setAdapter(parameters, it) },
        )
        viewModel.bankAccountOptions.observe(
            this,
            Observer { bankAccountRecyclerViewWrapper.setAdapter(parameters, it) },
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        creditCardRecyclerViewWrapper.onDestroy()
        bankAccountRecyclerViewWrapper.onDestroy()
    }

    private fun continueToNextActivity(nextActivityData: NextActivityData) {
        val result =
            PaymentCY17PlusResult(
                paymentId = nextActivityData.token,
                isDefaultPayment = nextActivityData.defaultPayment,
                paymentRecord = nextActivityData.paymentRecord,
            )
        val intent = Intent()
        intent.putExtra(EXTRA_RESULT, result)
        setResult(RESULT_OK, intent)
        finish()
    }

    private class RecyclerViewWrapper(
        private val recyclerView: RecyclerView,
        context: Context,
    ) {
        private var optionsAdapter: PaymentOptionsAdapter? = null

        init {
            recyclerView.layoutManager = LinearLayoutManager(context)
            recyclerView.isNestedScrollingEnabled = false
        }

        fun setAdapter(
            parameters: PaymentOptionsAdapter.Parameters,
            paymentRecords: ManagePaymentOptionsViewModel.OptionListData,
        ) {
            optionsAdapter = PaymentOptionsAdapter(parameters, paymentRecords)
            recyclerView.adapter = optionsAdapter
        }

        fun onDestroy() {
            optionsAdapter?.setLifecycleDestroyed()
        }
    } // RecyclerViewWrapper class
} // ManagePaymentOptionsActivity class
