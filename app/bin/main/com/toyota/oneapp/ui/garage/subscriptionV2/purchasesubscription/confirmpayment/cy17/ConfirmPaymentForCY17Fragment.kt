package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.cy17

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentConfirmPaymentBinding
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.adapter.ConfirmPaymentItemAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.model.ConfirmPaymentUIModel
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

/**
 * A Fragment - Allows user to confirm & purchase the subscription for CY17 & Before Vehicle.
 */
@AndroidEntryPoint
class ConfirmPaymentForCY17Fragment : BaseViewModelFragment() {
    private val args: ConfirmPaymentForCY17FragmentArgs by navArgs()
    private val viewModel: ConfirmPaymentForCY17ViewModel by viewModels()

    private lateinit var binding: FragmentConfirmPaymentBinding
    private lateinit var adapter: ConfirmPaymentItemAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentConfirmPaymentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        initializeViews()
        addListeners()
        setUpViewModelBindings()

        return binding.root
    }

    private fun initializeViews() {
        adapter = ConfirmPaymentItemAdapter(emptyList())
        binding.rvItems.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            addItemDecoration(DividerItemDecoration(requireContext(), LinearLayout.VERTICAL))
        }

        viewModel.uiModel.observe(viewLifecycleOwner) { data ->
            DataBindingAdapters.setRecyclerViewAdapterData(binding.rvItems, data.items, emptyList())
            DataBindingAdapters.setIsVisible(binding.btnPurchaseSubscription, data != null)
        }
    }

    private fun addListeners() {
        binding.btnPurchaseSubscription.setOnClickListener {
            viewModel.createSubscription()
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.uiModel.observe(
            viewLifecycleOwner,
            Observer { uiModel ->
                populateView(uiModel)
            },
        )

        viewModel.event.observe(
            viewLifecycleOwner,
            Observer { event ->
                handleEvents(event)
            },
        )
    }

    private fun populateView(uiModel: ConfirmPaymentUIModel) {
    }

    private fun handleEvents(event: ConfirmPaymentForCY17ViewModel.Event) {
        when (event) {
            is ConfirmPaymentForCY17ViewModel.Event.PaymentSuccessNavigation -> {
                val action =
                    ConfirmPaymentForCY17FragmentDirections.actionConfirmPaymentForCY17FragmentToSubscriptionActionSuccessActivity(
                        vehicle = args.vehicle,
                        icon = R.drawable.success_checkmark,
                        title = getString(R.string.ManagePaidSubscription_Purchase_Complete),
                        msg1 = getString(R.string.ManagePaidSubscription_Thanks_for_your_purchase),
                        msg2 = null,
                        actionTxt = getString(R.string.ServiceCampaign_back_to_dashboard),
                        msg3 = null,
                    )
                findNavController().navigate(action)
            }
        }
    }
}
