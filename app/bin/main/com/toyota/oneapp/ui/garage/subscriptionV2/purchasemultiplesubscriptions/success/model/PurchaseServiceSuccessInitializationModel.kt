package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success.model

import android.os.Parcelable
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutItemUIModel
import kotlinx.parcelize.Parcelize

@Parcelize
data class PurchaseServiceSuccessInitializationModel(
    val totalPrice: String,
    val purchasedServices: List<CheckoutItemUIModel>,
) : Parcelable
