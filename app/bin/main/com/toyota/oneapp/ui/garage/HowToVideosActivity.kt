package com.toyota.oneapp.ui.garage

import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.HowToVideosAdapter
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityHowToVideosBinding
import com.toyota.oneapp.features.glovebox.gloveboxlist.domain.model.HowToVideosPayload
import com.toyota.oneapp.model.pref.LanguagePreferenceModel
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HowToVideosActivity : UiBaseActivity() {
    companion object {
        const val EXTRA_HOW_TO_VIDOES = "HowToVideos"
    }

    private lateinit var binding: ActivityHowToVideosBinding
    private val viewModel: HowToVideosViewModel by viewModels()

    @Inject lateinit var languagePreferenceModel: LanguagePreferenceModel

    @Inject lateinit var applicationData: ApplicationData

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityHowToVideosBinding.inflate(layoutInflater)
        setContentView(binding.root)

        performActivitySetup(findViewById(R.id.toolbar))
        observeBaseEvents(viewModel)
        binding.tvHtvNoShow.text =
            applicationData.getSelectedVehicle()?.let {
                String.format(
                    resources.getString(R.string.How_To_Videos_no_show),
                    it.modelDescription,
                )
            }

        val howToVideos: ArrayList<HowToVideosPayload>? =
            intent.getParcelableArrayListExtra(
                EXTRA_HOW_TO_VIDOES,
            )

        if (howToVideos?.isNotEmpty() == true) {
            binding.tvHtvNoShow.visibility = View.GONE
            val rvVideos = findViewById<RecyclerView>(R.id.rv_videos)
            rvVideos.layoutManager = LinearLayoutManager(this)
            rvVideos.adapter = HowToVideosAdapter(this, howToVideos)
        } else {
            binding.tvHtvNoShow.visibility = View.VISIBLE
        }
    }
}
