package com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.cancel.cy17plus.model

import com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.cancel.cy17plus.CancelPaidSubscriptionForCY17PlusFragment

/**
 * A UI Model - Used to populate Refund section of [CancelPaidSubscriptionForCY17PlusFragment] Screen.
 */
data class RefundUIModel(
    val isRefundVisible: Boolean,
    val refundAmount: String,
    val estimatedTax: String,
    val refundDue: String,
    val endDate: String,
)
