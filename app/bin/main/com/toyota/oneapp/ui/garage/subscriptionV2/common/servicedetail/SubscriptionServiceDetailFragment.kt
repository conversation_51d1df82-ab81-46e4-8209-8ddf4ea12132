package com.toyota.oneapp.ui.garage.subscriptionV2.common.servicedetail

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.one_ui.OneUIUtil
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentSubscriptionServiceDetailBinding
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.common.subscriptionbundle.IndividualServiceAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.ServiceDetailWebviewClient
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * This fragment used to show subscription service details.
 */
@AndroidEntryPoint
class SubscriptionServiceDetailFragment : BaseDataBindingFragment<FragmentSubscriptionServiceDetailBinding>() {
    private lateinit var args: SubscriptionServiceDetailFragmentArgs

    @Inject lateinit var vehicleInfo: VehicleInfo

    override fun onViewBound(
        binding: FragmentSubscriptionServiceDetailBinding,
        savedInstance: Bundle?,
    ) {
        initializeExtras()
        populateViews()
    }

    override fun getLayout(): Int = R.layout.fragment_subscription_service_detail

    private fun initializeExtras() {
        args = SubscriptionServiceDetailFragmentArgs.fromBundle(requireArguments())
    }

    private fun populateViews() {
        if (args.uiModel.canShowBundleComponents) {
            val individualServiceAdapter =
                IndividualServiceAdapter(
                    vehicleInfo,
                    args.uiModel.bundleComponents ?: emptyList(),
                    true,
                )
            viewDataBinding.rvBundleComponents.run {
                layoutManager = LinearLayoutManager(requireContext())
                adapter = individualServiceAdapter
            }
        }

        context?.let {
            viewDataBinding.descriptionWebview.webViewClient =
                ServiceDetailWebviewClient(
                    it,
                    viewDataBinding.webviewProgress,
                )
        }

        with(args.uiModel) {
            DataBindingAdapters.loadImage(viewDataBinding.ivService, productImageUrl, null)
            viewDataBinding.tvTitle.text = productName
            DataBindingAdapters.loadHTMLData(
                viewDataBinding.descriptionWebview,
                OneUIUtil.appendBodyContentToHtml(productDescription, BuildConfig.IS_TOYOTA_APP),
            )
            DataBindingAdapters.setIsVisible(
                viewDataBinding.rvBundleComponents,
                canShowBundleComponents,
            )
            wifiCarrierIcon?.let {
                viewDataBinding.ivCarrierIcon.setImageResource(wifiCarrierIcon)
            }
        }
    }
}
