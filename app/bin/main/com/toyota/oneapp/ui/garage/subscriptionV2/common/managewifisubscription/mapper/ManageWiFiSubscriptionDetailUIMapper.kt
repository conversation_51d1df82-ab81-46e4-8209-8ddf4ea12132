package com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.mapper

import android.content.Context
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.subscriptionV2.SubscriptionType
import com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.model.ManageWiFiSubscriptionDetailInitializationModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.model.ManageWiFiSubscriptionDetailUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import javax.inject.Inject

class ManageWiFiSubscriptionDetailUIMapper
    @Inject
    constructor(
        private val context: Context,
        private val applicationData: ApplicationData,
    ) {
        fun map(initializationModel: ManageWiFiSubscriptionDetailInitializationModel): ManageWiFiSubscriptionDetailUIModel {
            val uiModel: ManageWiFiSubscriptionDetailUIModel

            with(initializationModel) {
                val actionInfoText: String
                val actionText: String

                if (applicationData.getSelectedVehicle()?.isCY17 == true) {
                    // CY17 - Cancellation of WIFI Trial - Is handled by Toyota and not Verizon.
                    if (subscriptionType == SubscriptionType.PAID) {
                        actionInfoText = context.getString(R.string.go_to_verizon_to_manage_account)
                        actionText = context.getString(R.string.go_to_verizon)
                    } else {
                        actionInfoText = ""
                        actionText = context.getString(R.string.learn_more)
                    }
                } else {
                    actionInfoText = context.getString(R.string.go_to_at_and_t_to_manage_account)
                    actionText = context.getString(R.string.go_to_at_and_t)
                }

                uiModel =
                    ManageWiFiSubscriptionDetailUIModel(
                        productName = productName,
                        productLongDesc = productLongDesc,
                        productImageUrl = productImageUrl,
                        carrierIcon =
                            SubscriptionUtil.getWifiCarrierIcon(
                                applicationData.getSelectedVehicle(),
                            ),
                        actionInfoText = actionInfoText,
                        actionText = actionText,
                    )
            }

            return uiModel
        }
    }
