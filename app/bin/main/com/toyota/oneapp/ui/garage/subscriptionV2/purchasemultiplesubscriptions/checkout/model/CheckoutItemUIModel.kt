package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model

import android.os.Parcelable
import com.toyota.oneapp.model.subscriptionV2.BundleComponent
import kotlinx.parcelize.Parcelize

sealed class CheckoutItemUIModel : Parcelable {
    @Parcelize
    data class CheckoutServiceListItemUIModel(
        val productLine: String,
        val title: String,
        val subTitle: String,
        val isRenewable: Boolean,
        val isAutoRenew: Boolean,
    ) : CheckoutItemUIModel()

    @Parcelize
    data class CheckoutBundleListItemUIModel(
        val productLine: String,
        val title: String,
        val subscriptionPrice: String,
        val subscriptionTerm: String,
        val isRenewable: Boolean,
        val isAutoRenew: Boolean,
        val bundleComponents: List<BundleComponent>,
    ) : CheckoutItemUIModel()
}
