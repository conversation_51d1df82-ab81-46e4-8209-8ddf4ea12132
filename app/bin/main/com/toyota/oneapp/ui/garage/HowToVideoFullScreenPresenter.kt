package com.toyota.oneapp.ui.garage

import com.toyota.oneapp.ui.BasePresenter

/**
 * Created by <PERSON> on 2020-01-08.
 */
class HowToVideoFullScreenPresenter : BasePresenter<HowToVideoFullScreenPresenter.View>() {
    var videoId: String? = null

    var currentSec = 0f

    var position: Int = -1

    override fun onCreate() {
        super.onCreate()
        videoId = bundle.getString("VideoId")
        currentSec = bundle.getFloat("CurrentSec")
        position = bundle.getInt("Position")
    }

    interface View : BaseView
}
