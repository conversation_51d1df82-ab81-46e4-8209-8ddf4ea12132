package com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist

import android.os.Bundle
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentSubscriptionListBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.adapter.SubscriptionListAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.model.SubscriptionListItemUIModel
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.recyclerview.decorator.SectionItemDecorator

/**
 * The Fragment lists all Services related to the selected Vehicle.
 */
@AndroidEntryPoint
class SubscriptionListFragment :
    BaseDataBindingFragment<FragmentSubscriptionListBinding>(),
    SubscriptionListAdapter.OnItemClickListener {
    private val viewModel: SubscriptionListViewModel by viewModels()

    private lateinit var adapter: SubscriptionListAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentSubscriptionListBinding,
        savedInstance: Bundle?,
    ) {
        initializeViews()
        addListeners()
        setUpViewModelBindings()
    }

    override fun getLayout(): Int = R.layout.fragment_subscription_list

    // SubscriptionListAdapter.OnItemClickListener Methods.
    override fun onServiceItemClick(service: SubscriptionListItemUIModel.Service) {
        viewModel.onServiceItemClick(service)
    }

    private fun initializeViews() {
        viewDataBinding.clEmpty.isVisible = false
        adapter = SubscriptionListAdapter(emptyList(), this)
        viewDataBinding.rvServices.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            addItemDecoration(SectionItemDecorator(requireContext()))
        }
    }

    private fun addListeners() {
        viewDataBinding.btnAction.setOnClickListener {
            viewModel.onActionButtonClicked()
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.subscriptionListNavigationEvent.observe(
            viewLifecycleOwner,
            Observer { navigationEvent ->
                handleNavigationEvents(navigationEvent)
            },
        )
        viewModel.uiModel.observe(viewLifecycleOwner) {
            DataBindingAdapters.setIsVisible(viewDataBinding.clContent, !it.showEmptyView)
            viewDataBinding.tvVehicleName.text = it.vehicleName
            DataBindingAdapters.setRecyclerViewAdapterData(viewDataBinding.rvServices, it.items, null)
            DataBindingAdapters.setIsVisible(viewDataBinding.txtPpoDisclaimer, it.showPPODisclaimer)
            viewDataBinding.txtPpoDisclaimer.text = it.PPODisclaimer
            DataBindingAdapters.setIsVisible(viewDataBinding.clEmpty, it.showEmptyView)
            viewDataBinding.tvNoServiceMsg.text = getString(R.string.no_subscription_msg, it.vehicleName)
            viewDataBinding.btnAction.text = it.actionTxt
            DataBindingAdapters.setIsVisible(viewDataBinding.btnAction, it.isActionBtnVisible)
        }
    }

    private fun handleNavigationEvents(navigationEvent: SubscriptionListViewModel.NavigationEvent) {
        when (navigationEvent) {
            is SubscriptionListViewModel.NavigationEvent.NavigateToCancelTrialSubscriptionDetailScreen -> {
                navigateToCancelTrialSubscriptionScreen(navigationEvent)
            }
            is SubscriptionListViewModel.NavigationEvent.NavigateToEnableTrialSubscriptionDetailScreen -> {
                navigateToEnableTrialSubscriptionScreen(navigationEvent)
            }
            is SubscriptionListViewModel.NavigationEvent.NavigateToManageWiFiConnectSubscriptionDetailScreen -> {
                navigateToManageWiFiConnectSubscriptionDetailScreen(navigationEvent)
            }
            is SubscriptionListViewModel.NavigationEvent.NavigateToManagePaidSubscriptionDetailScreen -> {
                navigateToManagePaidSubscriptionScreen(navigationEvent)
            }
            is SubscriptionListViewModel.NavigationEvent.NavigateToAddServiceScreen -> {
                navigateToAddServiceScreen(navigationEvent)
            }
            is SubscriptionListViewModel.NavigationEvent.NavigateToEnableAlTrialScreen -> {
                navigateToEnableAllTrialScreen(navigationEvent)
            }
        }
    }

    private fun navigateToCancelTrialSubscriptionScreen(
        navigationEvent: SubscriptionListViewModel.NavigationEvent.NavigateToCancelTrialSubscriptionDetailScreen,
    ) {
        val action =
            SubscriptionListFragmentDirections.actionSubscriptionV2FragmentToCancelTrialSubscriptionDetailFragment(
                vehicle = navigationEvent.vehicle,
                isAddVehicleFlow = navigationEvent.isAddVehicleFlow,
                selectedSubscription = navigationEvent.selectedSubscription,
                trialSubscriptions = navigationEvent.trialSubscriptions.toTypedArray(),
                paidSubscriptions = navigationEvent.paidSubscriptions.toTypedArray(),
                availableSubscriptions = navigationEvent.availableSubscriptions.toTypedArray(),
                accessToken = navigationEvent.accessToken,
                taxDisclaimer = navigationEvent.taxDisclaimer,
                isAzure = navigationEvent.isAzure ?: false,
                alerts = navigationEvent.alerts?.toTypedArray(),
                isPPOEligible = navigationEvent.isPPOEligible,
                ppoCancelDisclaimer = navigationEvent.ppoCancelDisclaimer,
            )
        findNavController().navigate(action)
    }

    private fun navigateToEnableTrialSubscriptionScreen(
        navigationEvent: SubscriptionListViewModel.NavigationEvent.NavigateToEnableTrialSubscriptionDetailScreen,
    ) {
        val action =
            SubscriptionListFragmentDirections.actionSubscriptionV2FragmentToEnableTrialSubscriptionDetailFragment(
                vehicle = navigationEvent.vehicle,
                selectedSubscription = navigationEvent.selectedSubscription,
                paidSubscriptions = navigationEvent.paidSubscriptions.toTypedArray(),
                trialSubscriptions = navigationEvent.trialSubscriptions.toTypedArray(),
                alerts = navigationEvent.alerts?.toTypedArray(),
                accessToken = navigationEvent.accessToken,
                isAzure = navigationEvent.isAzure ?: false,
                isAddVehicleFlow = navigationEvent.isAddVehicleFlow ?: false,
            )
        findNavController().navigate(action)
    }

    private fun navigateToManageWiFiConnectSubscriptionDetailScreen(
        event: SubscriptionListViewModel.NavigationEvent.NavigateToManageWiFiConnectSubscriptionDetailScreen,
    ) {
        val action =
            SubscriptionListFragmentDirections.actionSubscriptionV2FragmentToWiFiConnectSubscriptionDetailFragment(
                vehicle = event.vehicle,
                initializationModel = event.initializationModel,
            )
        findNavController().navigate(action)
    }

    private fun navigateToManagePaidSubscriptionScreen(
        navigationEvent: SubscriptionListViewModel.NavigationEvent.NavigateToManagePaidSubscriptionDetailScreen,
    ) {
        val action =
            SubscriptionListFragmentDirections.actionSubscriptionV2FragmentToPaidSubscriptionDetailFragment(
                vehicle = navigationEvent.vehicle,
                selectedSubscription = navigationEvent.selectedSubscription,
                trialSubscriptions = navigationEvent.trialSubscriptions.toTypedArray(),
                paidSubscriptions = navigationEvent.paidSubscriptions.toTypedArray(),
            )
        findNavController().navigate(action)
    }

    private fun navigateToAddServiceScreen(navigationEvent: SubscriptionListViewModel.NavigationEvent.NavigateToAddServiceScreen) {
        val action =
            if (BuildConfig.SUBSCRIPTION_PURCHASE_MULTI_PRODUCT_ENABLED) {
                SubscriptionListFragmentDirections.actionSubscriptionV2FragmentToSelectServicesForPurchaseFragment(
                    vehicle = navigationEvent.vehicle,
                    isAddVehicleFlow = navigationEvent.isAddVehicleFlow,
                    trialSubscriptions = navigationEvent.trialSubscriptions.toTypedArray(),
                    paidSubscriptions = navigationEvent.paidSubscriptions.toTypedArray(),
                    availableSubscriptions = navigationEvent.availableSubscription.toTypedArray(),
                    accessToken = navigationEvent.accessToken,
                    taxDisclaimer = navigationEvent.taxDisclaimer,
                    isAzure = navigationEvent.isAzure ?: false,
                    alerts = navigationEvent.alerts?.toTypedArray(),
                )
            } else {
                SubscriptionListFragmentDirections.actionSubscriptionV2FragmentToAddServiceFragment(
                    vehicle = navigationEvent.vehicle,
                    isAddVehicleFlow = navigationEvent.isAddVehicleFlow,
                    trialSubscriptions = navigationEvent.trialSubscriptions.toTypedArray(),
                    paidSubscriptions = navigationEvent.paidSubscriptions.toTypedArray(),
                    availableSubscriptions = navigationEvent.availableSubscription.toTypedArray(),
                    accessToken = navigationEvent.accessToken,
                    isAzure = navigationEvent.isAzure ?: false,
                    alerts = navigationEvent.alerts?.toTypedArray(),
                )
            }
        findNavController().navigate(action)
    }

    private fun navigateToEnableAllTrialScreen(navigationEvent: SubscriptionListViewModel.NavigationEvent.NavigateToEnableAlTrialScreen) {
        val action =
            SubscriptionListFragmentDirections.actionSubscriptionV2FragmentToEnableTrialSubscriptionFragment(
                vehicle = navigationEvent.vehicle,
                isAddVehicleFlow = navigationEvent.isAddVehicleFlow,
                title =
                    if (navigationEvent.isAddVehicleFlow) {
                        ""
                    } else {
                        getString(
                            R.string.enable_subscription,
                        )
                    },
                payload = navigationEvent.payload,
                trialSubscriptions = navigationEvent.trialSubscriptions.toTypedArray(),
                alerts = navigationEvent.alerts?.toTypedArray(),
                accessToken = navigationEvent.accessToken,
            )
        findNavController().navigate(action)
    }
}
