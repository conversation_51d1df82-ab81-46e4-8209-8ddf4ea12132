package com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.detail

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.toyota.one_ui.OneUIUtil
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentCancelTrialSubscriptionDetailBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.detail.model.CancelTrialSubscriptionDetailUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.ServiceDetailWebviewClient
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil

/**
 * This fragment used to show subscription service details.
 */
@AndroidEntryPoint
class CancelTrialSubscriptionDetailFragment : BaseDataBindingFragment<FragmentCancelTrialSubscriptionDetailBinding>() {
    private val viewModel: CancelTrialSubscriptionDetailViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentCancelTrialSubscriptionDetailBinding,
        savedInstance: Bundle?,
    ) {
        setUpViewModelBindings()
        populateView()
        setUpOnClickListeners()
    }

    override fun getLayout(): Int = R.layout.fragment_cancel_trial_subscription_detail

    private fun setUpViewModelBindings() {
        with(viewModel) {
            event.observe(
                viewLifecycleOwner,
            ) {
                handleViewModelEvents(it)
            }
        }

        with(viewModel) {
            uiModel.observe(viewLifecycleOwner) {
                handleUiStateChanges(it)
            }
        }
    }

    private fun populateView() {
        context?.let {
            viewDataBinding.descriptionWebview.webViewClient =
                ServiceDetailWebviewClient(
                    it,
                    viewDataBinding.webviewProgress,
                )
        }
    }

    private fun setUpOnClickListeners() {
        viewDataBinding.btnCancelTrials.setOnClickListener {
            viewModel.onCancel()
        }
        viewDataBinding.btnPurchase.setOnClickListener {
            viewModel.onPurchase()
        }
    }

    private fun handleViewModelEvents(event: CancelTrialSubscriptionDetailViewModel.Event) {
        when (event) {
            is CancelTrialSubscriptionDetailViewModel.Event.NavigateToCancelAllSubscriptionScreen -> {
                navigateToCancelAllSubscriptionScreen(event)
            }
            is CancelTrialSubscriptionDetailViewModel.Event.NavigateToSelectSubscriptionForPurchaseScreen -> {
                navigateToSelectSubscriptionForPurchaseScreen(event)
            }
            is CancelTrialSubscriptionDetailViewModel.Event.ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable -> {
                showMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable(event)
            }
        }
    }

    private fun navigateToCancelAllSubscriptionScreen(
        event: CancelTrialSubscriptionDetailViewModel.Event.NavigateToCancelAllSubscriptionScreen,
    ) {
        val action =
            CancelTrialSubscriptionDetailFragmentDirections.actionCancelTrialSubscriptionDetailFragmentToCancelAllSubscriptionFragment(
                vehicle = event.vehicle,
                trialSubscriptions = event.trialSubscriptions,
                paidSubscriptions = event.paidSubscriptions,
                isPPOEligible = event.isPPOEligible,
                ppoCancelDisclaimer = event.ppoCancelDisclaimer,
            )
        findNavController().navigate(action)
    }

    private fun navigateToSelectSubscriptionForPurchaseScreen(
        event: CancelTrialSubscriptionDetailViewModel.Event.NavigateToSelectSubscriptionForPurchaseScreen,
    ) {
        val action =
            if (BuildConfig.SUBSCRIPTION_PURCHASE_MULTI_PRODUCT_ENABLED) {
                CancelTrialSubscriptionDetailFragmentDirections
                    .actionCancelTrialSubscriptionDetailFragmentToSelectServicesForPurchaseFragment(
                        vehicle = event.vehicle,
                        isAddVehicleFlow = event.isAddVehicleFlow,
                        paidSubscriptions = event.paidSubscriptions,
                        trialSubscriptions = event.trialSubscriptions,
                        availableSubscriptions = event.availableSubscriptions,
                        accessToken = event.accessToken,
                        taxDisclaimer = event.taxDisclaimer,
                        isAzure = event.isAzure ?: false,
                        alerts = event.alerts,
                    )
            } else {
                CancelTrialSubscriptionDetailFragmentDirections
                    .actionCancelTrialSubscriptionDetailFragmentToSelectSubscriptionForPurchaseFragment(
                        vehicle = event.vehicle,
                        isAddVehicleFlow = event.isAddVehicleFlow,
                        selectedSubscription = event.selectedSubscription,
                        trialSubscriptions = event.trialSubscriptions,
                        paidSubscriptions = event.paidSubscriptions,
                        availableSubscriptions = event.availableSubscriptions,
                        accessToken = event.accessToken,
                        isAzure = event.isAzure ?: false,
                        alerts = event.alerts,
                    )
            }
        findNavController().navigate(action)
    }

    private fun showMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable(
        event: CancelTrialSubscriptionDetailViewModel.Event.ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable,
    ) {
        DialogUtil.showDialog(
            requireActivity(),
            null,
            getString(R.string.Subscription_availability_auto_renewed),
            getString(R.string.Common_ok),
        )
    }

    private fun handleUiStateChanges(uiModel: CancelTrialSubscriptionDetailUIModel) {
        DataBindingAdapters.loadImage(
            viewDataBinding.ivService,
            uiModel.subscription.productImageUrl,
            null,
        )
        viewDataBinding.tvTitle.text = uiModel.subscription.displayProductName
        val htmlData =
            OneUIUtil.appendBodyContentToHtml(
                uiModel.subscription.formattedProductDesc,
                BuildConfig.IS_TOYOTA_APP,
                uiModel.subscription.productLongDesc,
            )
        DataBindingAdapters.loadHTMLData(viewDataBinding.descriptionWebview, htmlData)
        DataBindingAdapters.setIsVisible(
            viewDataBinding.primarySubscriberLayout,
            uiModel.isPrimarySubscriber,
        )
        viewDataBinding.tvExpiresTxt.text = uiModel.subscription.displayTerm
        DataBindingAdapters.setIsInvisible(
            viewDataBinding.tvExpiresTxt,
            uiModel.subscription.hideSubscriptionStatus ?: true,
        )
        DataBindingAdapters.setIsVisible(viewDataBinding.btnPurchase, uiModel.isPurchaseVisible)
    }
}
