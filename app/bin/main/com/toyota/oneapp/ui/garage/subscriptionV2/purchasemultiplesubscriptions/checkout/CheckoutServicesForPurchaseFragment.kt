package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout

import android.net.Uri
import android.os.Bundle
import android.text.Html
import android.text.SpannableString
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.URLSpan
import android.widget.LinearLayout
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.customviews.SwipeLayout
import com.toyota.oneapp.databinding.FragmentCheckoutServicesForPurchaseBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.extensions.model
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.adapter.CheckoutPriceAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.adapter.CheckoutServiceAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutItemUIModel
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CheckoutServicesForPurchaseFragment :
    BaseDataBindingFragment<FragmentCheckoutServicesForPurchaseBinding>(),
    CheckoutServiceAdapter.CheckoutServiceAdapterListener {
    private val args: CheckoutServicesForPurchaseFragmentArgs by navArgs()
    private val viewModel: CheckoutServicesForPurchaseViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentCheckoutServicesForPurchaseBinding,
        savedInstance: Bundle?,
    ) {
        initializeViews()
        populateViews()
        setUpViewModelBindings(binding)
    }

    override fun getLayout(): Int = R.layout.fragment_checkout_services_for_purchase

    // CheckoutServiceAdapter.CheckoutServiceAdapterListener Methods.
    override fun onServiceAutoRenewToggled(
        service: CheckoutItemUIModel.CheckoutServiceListItemUIModel,
        autoRenew: Boolean,
    ) {
        viewModel.onServiceAutoRenewToggled(
            productLine = service.productLine,
            autoRenew = autoRenew,
        )
    }

    override fun onBundleAutoRenewToggled(
        bundle: CheckoutItemUIModel.CheckoutBundleListItemUIModel,
        autoRenew: Boolean,
    ) {
        viewModel.onServiceAutoRenewToggled(
            productLine = bundle.productLine,
            autoRenew = autoRenew,
        )
    }

    private fun initializeViews() {
        val serviceAdapter = CheckoutServiceAdapter(args.vehicle, this)
        viewDataBinding.rvServices.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = serviceAdapter
        }

        val priceAdapter = CheckoutPriceAdapter()
        DataBindingAdapters.loadImage(
            viewDataBinding.ivVehicle,
            args.vehicle.image,
            context?.getDrawable(R.drawable.car_placeholder),
        )
        viewDataBinding.slPay.setText(getString(R.string.swipe_to_pay))
        viewDataBinding.rvPrice.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = priceAdapter
            addItemDecoration(DividerItemDecoration(requireContext(), LinearLayout.VERTICAL))
        }
    }

    fun populateViews() {
        val spannableString =
            SpannableString(
                Html.fromHtml(args.taxDisclaimer.orEmpty(), Html.FROM_HTML_MODE_LEGACY),
            )
        val urlSpans = spannableString.getSpans(0, spannableString.length, URLSpan::class.java)

        for (urlSpan in urlSpans) {
            val start = spannableString.getSpanStart(urlSpan)
            val end = spannableString.getSpanEnd(urlSpan)
            spannableString.removeSpan(urlSpan)
            spannableString.setSpan(NoUnderlineURLSpan(urlSpan.url), start, end, 0)
        }
        viewDataBinding.tvInfo.text = spannableString
        viewDataBinding.tvInfo.movementMethod = LinkMovementMethod.getInstance()
    }

    private class NoUnderlineURLSpan(
        url: String?,
    ) : URLSpan(url) {
        override fun updateDrawState(ds: TextPaint) {
            super.updateDrawState(ds)
            ds.isUnderlineText = false
        }
    }

    private fun setUpViewModelBindings(binding: FragmentCheckoutServicesForPurchaseBinding) {
        with(viewModel) {
            event.observe(viewLifecycleOwner) {
                handleEvent(it)
            }
            navigationEvent.observe(viewLifecycleOwner) {
                handleNavigationEvents(it)
            }
            binding.ivVehicle.setImageURI(Uri.parse(viewModel.args.vehicle.image))
            binding.tvVehicleModel.text = viewModel.args.vehicle.model
            binding.tvVehicleVin.text = viewModel.args.vehicle.vin
            binding.slPay.setOnSwipeCompleteListener(
                object : SwipeLayout.OnSwipeCompleteListener {
                    override fun onSwipeComplete(view: SwipeLayout) {
                        viewModel.purchaseServices()
                    }
                },
            )
            viewModel.serviceUIModels.observe(viewLifecycleOwner) { data ->
                DataBindingAdapters.setRecyclerViewAdapterData(binding.rvServices, data, emptyList())
            }
            viewModel.priceUIModels.observe(viewLifecycleOwner) { data ->
                DataBindingAdapters.setRecyclerViewAdapterData(binding.rvPrice, data, emptyList())
            }
        }
    }

    private fun handleEvent(event: CheckoutServicesForPurchaseViewModel.Event) {
        when (event) {
            is CheckoutServicesForPurchaseViewModel.Event.ResetSwipeLayout -> {
                viewDataBinding.slPay.moveButtonBack()
            }
        }
    }

    private fun handleNavigationEvents(event: CheckoutServicesForPurchaseViewModel.NavigationEvent) {
        when (event) {
            is CheckoutServicesForPurchaseViewModel.NavigationEvent.NavigateToAddVehicleSuccessScreen -> {
                navigateToAddVehicleSuccessScreen(event)
            }
            is CheckoutServicesForPurchaseViewModel.NavigationEvent.NavigateToPurchaseSubscriptionSuccessScreen -> {
                navigateToPurchaseSubscriptionSuccessScreen(event)
            }
        }.exhaustive
    }

    private fun navigateToAddVehicleSuccessScreen(
        event: CheckoutServicesForPurchaseViewModel.NavigationEvent.NavigateToAddVehicleSuccessScreen,
    ) {
        val action =
            CheckoutServicesForPurchaseFragmentDirections.actionPurchaseServicesCheckoutFragmentToAddVehicleSuccessFragment(
                vehicle = args.vehicle,
                isAddVehicleFlow = args.isAddVehicleFlow,
                icon = event.icon,
                title = getString(event.title),
                msg = getString(event.msg),
                ctaText = getString(event.ctaText),
            )
        findNavController().navigate(action)
    }

    private fun navigateToPurchaseSubscriptionSuccessScreen(
        event: CheckoutServicesForPurchaseViewModel.NavigationEvent.NavigateToPurchaseSubscriptionSuccessScreen,
    ) {
        val action =
            CheckoutServicesForPurchaseFragmentDirections.actionPurchaseServicesCheckoutFragmentToPurchaseServiceSuccessFragment(
                vehicle = args.vehicle,
                initializationModel = event.initializationModel,
            )
        findNavController().navigate(action)
    }
}
