package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.detail

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.toyota.one_ui.OneUIUtil
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentPurchaseSubscriptionDetailBinding
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.ServiceDetailWebviewClient
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.detail.PurchaseSubscriptionDetailFragmentDirections.Companion.actionPurchaseSubscriptionDetailFragmentToSelectSubscriptionForPurchaseFragment
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil

/**
 * A Fragment - displays Service information to purchase it.
 */
@AndroidEntryPoint
class PurchaseSubscriptionDetailFragment : BaseViewModelFragment() {
    private val args: PurchaseSubscriptionDetailFragmentArgs by navArgs()
    private val viewModel: PurchaseSubscriptionDetailViewModel by viewModels()

    private lateinit var binding: FragmentPurchaseSubscriptionDetailBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentPurchaseSubscriptionDetailBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        setUpViewModelBindings()
        populateView()

        return binding.root
    }

    private fun setUpViewModelBindings() {
        with(viewModel) {
            event.observe(
                viewLifecycleOwner,
                Observer {
                    handleViewModelEvents(it)
                },
            )
        }
    }

    private fun populateView() {
        context?.let {
            binding.descriptionWebview.webViewClient =
                ServiceDetailWebviewClient(
                    it,
                    binding.webviewProgress,
                )

            viewModel.subscription.observe(viewLifecycleOwner) { subscription ->
                DataBindingAdapters.loadImage(
                    binding.ivImage,
                    subscription.productImageUrl,
                    resources.getDrawable(R.drawable.image_not_found),
                )
                binding.tvName.text = subscription.displayProductName
                val htmlData =
                    OneUIUtil.appendBodyContentToHtml(
                        subscription.formattedProductDesc,
                        BuildConfig.IS_TOYOTA_APP,
                        subscription.productLongDesc,
                    )
                DataBindingAdapters.loadHTMLData(binding.descriptionWebview, htmlData)
            }

            binding.btnPurcahse.setOnClickListener {
                viewModel.onPurchase()
            }
        }
    }

    private fun handleViewModelEvents(event: PurchaseSubscriptionDetailViewModel.Event) {
        when (event) {
            is PurchaseSubscriptionDetailViewModel.Event.NavigateToSelectSubscriptionForPurchaseScreen -> {
                navigateToSelectSubscriptionForPurchaseScreen(event)
            }

            is PurchaseSubscriptionDetailViewModel.Event.ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable -> {
                showMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable(event)
            }
        }
    }

    private fun navigateToSelectSubscriptionForPurchaseScreen(
        event: PurchaseSubscriptionDetailViewModel.Event.NavigateToSelectSubscriptionForPurchaseScreen,
    ) {
        val action =
            actionPurchaseSubscriptionDetailFragmentToSelectSubscriptionForPurchaseFragment(
                vehicle = args.vehicle,
                isAddVehicleFlow = args.isAddVehicleFlow,
                selectedSubscription = event.selectedSubscription,
                trialSubscriptions = event.trialSubscriptions,
                paidSubscriptions = event.paidSubscriptions,
                availableSubscriptions = event.availableSubscriptions,
                accessToken = event.accessToken,
                isAzure = event.isAzure,
                alerts = event.alerts,
            )
        findNavController().navigate(action)
    }

    private fun showMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable(
        event: PurchaseSubscriptionDetailViewModel.Event.ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable,
    ) {
        DialogUtil.showDialog(
            requireActivity(),
            null,
            getString(R.string.Subscription_availability_auto_renewed),
            getString(R.string.Common_ok),
        )
    }
}
