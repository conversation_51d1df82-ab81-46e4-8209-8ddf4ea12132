package com.toyota.oneapp.ui.garage.paymentOptions

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.toyota.oneapp.model.subscription.PaymentRecord
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ItemPaymentOptionViewModel
    @Inject
    constructor() : ViewModel() {
        val state: MutableLiveData<State> = MutableLiveData()
        val optionClicked: MutableLiveData<PaymentRecord> = MutableLiveData()
        val deleteClicked: MutableLiveData<PaymentRecord> = MutableLiveData()

        private lateinit var paymentRecord: PaymentRecord

        fun extractViewData(
            record: PaymentRecord,
            isInEditMode: Boolean,
            areSubscriptionsPresent: Boolean,
        ) {
            paymentRecord = record

            val paymentOption = PaymentOption.create(paymentRecord)

            val newState =
                State(
                    isDefaultPaymentOptionVisible = paymentOption.isDefaultPayment,
                    accountNumber = (paymentOption.accountNumber ?: ""),
                    logoImageSrc = paymentOption.logoResourceId,
                    deleteEnabled = (isInEditMode && !areSubscriptionsPresent),
                    editEnabled = isInEditMode,
                )
            state.postValue(newState)
        }

        fun onClick() {
            optionClicked.postValue(paymentRecord)
        }

        fun onDelete() {
            deleteClicked.postValue(paymentRecord)
        }

        data class State(
            val isDefaultPaymentOptionVisible: Boolean,
            val accountNumber: String,
            val logoImageSrc: Int,
            val deleteEnabled: Boolean,
            val editEnabled: Boolean,
        )
    } // ItemPaymentOptionViewModel class
