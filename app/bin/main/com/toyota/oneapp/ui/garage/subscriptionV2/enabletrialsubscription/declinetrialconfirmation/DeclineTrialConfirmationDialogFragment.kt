package com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.declinetrialconfirmation

import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.setFragmentResult
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentDeclineTrialConfirmationDialogBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DeclineTrialConfirmationDialogFragment : DialogFragment() {
    companion object {
        const val TAG = "DeclineTrialConfirmationDialogFragment"
        const val REQUEST_KEY_DECLINE_TRIAL_CONFIRMATION_DIALOG = "RESULT_DECLINE_TRIAL_CONFIRMATION_DIALOG"
        const val EXTRA_IS_CONFIRM = "EXTRA_IS_CONFIRM"
        private const val EXTRA_IS_TOYOTA_BRAND = "EXTRA_IS_TOYOTA_BRAND"
        private const val VEHICLE_21MM = "VEHICLE_21MM"
        private const val TITLE = "TITLE"
        private const val BODY = "BODY"

        fun getInstance(
            isToyotaBrand: Boolean,
            is21MMVehicle: Boolean,
            title: String? = null,
            body: String? = null,
        ): DeclineTrialConfirmationDialogFragment {
            val fragment = DeclineTrialConfirmationDialogFragment()
            fragment.arguments =
                bundleOf(
                    EXTRA_IS_TOYOTA_BRAND to isToyotaBrand,
                    VEHICLE_21MM to is21MMVehicle,
                    TITLE to title,
                    BODY to body,
                )
            return fragment
        }
    }

    private lateinit var binding: FragmentDeclineTrialConfirmationDialogBinding

    private var isToyotaBrand: Boolean = true

    private var is21MMVehicle: Boolean = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setStyle(STYLE_NORMAL, R.style.AppTheme_FullScreenDialog)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentDeclineTrialConfirmationDialogBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        initializeExtras()
        populateView()
        addListeners()

        return binding.root
    }

    override fun onStart() {
        super.onStart()

        val width = ViewGroup.LayoutParams.MATCH_PARENT
        val height = ViewGroup.LayoutParams.MATCH_PARENT
        dialog?.window?.setLayout(width, height)
    }

    private fun initializeExtras() {
        requireArguments().let {
            isToyotaBrand = it.getBoolean(EXTRA_IS_TOYOTA_BRAND, true)
            is21MMVehicle = it.getBoolean(VEHICLE_21MM, true)
        }
    }

    private fun populateView() {
        val cancelSubscriptionTitleLexus = if (is21MMVehicle) R.string.Garage_cancel_subscription_title_lexus_21mm else R.string.Garage_cancel_subscription_title_lexus
        val cancelSubscriptionNoteLexus = if (is21MMVehicle) R.string.Garage_cancel_subscription_note_lexus_21mm else R.string.Garage_cancel_subscription_note_lexus
        val title = if (isToyotaBrand) R.string.Garage_cancel_subscription_title else cancelSubscriptionTitleLexus
        val message = if (isToyotaBrand) R.string.Garage_cancel_subscription_note else cancelSubscriptionNoteLexus

        var declineTitle: String = ""
        var declineBody: String = ""
        requireArguments().let {
            declineTitle = it.getString(TITLE, "")
            declineBody = it.getString(BODY, "")
        }
        if (declineTitle.isNotBlank() && declineBody.isNotBlank()) {
            binding.tvTitle.text = declineTitle
            binding.tvDescription.text = Html.fromHtml(declineBody, Html.FROM_HTML_MODE_LEGACY)
        } else {
            binding.tvTitle.setText(title)
            binding.tvDescription.setText(message)
        }
    }

    private fun addListeners() {
        binding.toolbar.setNavigationOnClickListener {
            onCancel()
        }
        binding.btnCancel.setOnClickListener {
            onCancel()
        }
        binding.btnConfirm.setOnClickListener {
            onConfirm()
        }
    }

    private fun onCancel() {
        setResult(isConfirm = false)
        dismiss()
    }

    private fun onConfirm() {
        setResult(isConfirm = true)
        dismiss()
    }

    private fun setResult(isConfirm: Boolean) {
        setFragmentResult(
            REQUEST_KEY_DECLINE_TRIAL_CONFIRMATION_DIALOG,
            bundleOf(
                EXTRA_IS_CONFIRM to isConfirm,
            ),
        )
    }
}
