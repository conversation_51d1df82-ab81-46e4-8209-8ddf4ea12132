package com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.manualrefund

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.R
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.model.subscription.UpdatePaymentRequest
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.widget.AddressView
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel for [CancellationSuccessManualRefundActivity] Screen
 */
@HiltViewModel
class CancellationSuccessManualRefundViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val subscriptionAPIManager: SubscriptionAPIManager,
    ) : BaseViewModel() {
        private var paymentRecord: PaymentRecord? = null

        sealed class Event {
            object NavigateToDashboard : Event()
        }

        private val _address = MutableLiveData<AddressView.Address>()
        private val _event = SingleLiveEvent<Event>()

        val address: LiveData<AddressView.Address>
            get() = _address
        val event: LiveData<Event>
            get() = _event

        init {
            paymentRecord = state["paymentRecord"]
            _address.value =
                object : AddressView.Address {
                    override var line1: String? = paymentRecord?.creditCardAddress1
                    override var line2: String? = null
                    override var city: String? = paymentRecord?.creditCardCity
                    override var stateCode: String? = paymentRecord?.creditCardState
                    override var zipCode: String? = paymentRecord?.creditCardPostalCode
                    override var countryCode: String? = paymentRecord?.creditCardCountry
                }
        }

        fun onContinueClicked(address: AddressView.Address) {
            val paymentRecord = paymentRecord

            if (paymentRecord != null) {
                showProgress()
                subscriptionAPIManager.updatePayment(
                    paymentRecord,
                    UpdatePaymentRequest(
                        addressLine1 = address.line1,
                        addressLine2 = address.line2,
                        city = address.city,
                        state = address.stateCode,
                        zipCode = address.zipCode,
                        country = address.countryCode,
                    ),
                    object : BaseCallback<BaseResponse>() {
                        override fun onSuccess(response: BaseResponse) {
                            showSuccessToastMessage(R.string.payment_method_updated)
                            _event.value = Event.NavigateToDashboard
                        }

                        override fun onFailError(
                            httpCode: Int,
                            errorMsg: String?,
                        ) {
                            showErrorMessage(errorMsg)
                        }

                        override fun onComplete() {
                            hideProgress()
                        }
                    },
                )
            } else {
                // Not need to update the address
                _event.value = Event.NavigateToDashboard
            }
        }
    }
