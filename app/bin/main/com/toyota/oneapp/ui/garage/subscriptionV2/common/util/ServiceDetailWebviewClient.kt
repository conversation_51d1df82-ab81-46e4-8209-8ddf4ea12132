package com.toyota.oneapp.ui.garage.subscriptionV2.common.util

import android.content.Context
import android.graphics.Bitmap
import android.view.View
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.widget.ProgressBar
import com.toyota.oneapp.customviews.MultiOverrideWebViewClient
import com.toyota.oneapp.util.ToyUtil

class ServiceDetailWebviewClient(
    private val context: Context,
    private val progressBar: ProgressBar,
) : MultiOverrideWebViewClient() {
    override fun onPageStarted(
        view: WebView?,
        url: String?,
        favicon: Bitmap?,
    ) {
        progressBar.visibility = View.VISIBLE
        super.onPageStarted(view, url, favicon)
    }

    override fun onPageFinished(
        view: WebView?,
        url: String?,
    ) {
        progressBar.visibility = View.GONE
        super.onPageFinished(view, url)
    }

    override fun shouldOverrideUrlLoading(
        view: WebView?,
        request: WebResourceRequest?,
    ): Boolean {
        ToyUtil.openBrowserWithoutAllSettingsEnabled(context, request?.url.toString())
        return true
    }
}
