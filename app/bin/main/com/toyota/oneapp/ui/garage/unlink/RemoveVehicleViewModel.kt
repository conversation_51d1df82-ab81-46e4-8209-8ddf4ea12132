package com.toyota.oneapp.ui.garage.unlink

import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.subscription.CancellationDataRequest
import com.toyota.oneapp.model.subscription.CancellationDataResponse
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.model.subscription.UpdatePaymentRequest
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.widget.AddressView
import com.toyota.oneapp.util.Brand
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject

private const val DATE_FORMAT_FOR_CANCEL_DATA_REQUEST = "yyyy-MM-dd"

@HiltViewModel
class RemoveVehicleViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val preferenceModel: OneAppPreferenceModel,
        private val subscriptionApiManager: SubscriptionAPIManager,
    ) : BaseViewModel() {
        val removeVehicleNavigationEvent = SingleLiveEvent<RemoveVehicleNavigationEvent>()
        val data: MutableLiveData<List<String>> = MutableLiveData()
        var refundEligible = ObservableBoolean(false)
        lateinit var paymentRecord: PaymentRecord
        val address: MutableLiveData<AddressView.Address> = MutableLiveData()
        var vehicle = applicationData.getSelectedVehicle()
        var isVehicle21mm = applicationData.getSelectedVehicle()?.is21MMVehicle
        var vehicleModelName: String = applicationData.getSelectedVehicle()?.modelName.orEmpty()
        val appBrand = Brand.currentAppBrand().brandName

        fun updateVehicle(updatedVehicle: VehicleInfo) {
            vehicle = updatedVehicle
            isVehicle21mm = updatedVehicle.is21MMVehicle
            vehicleModelName = updatedVehicle.modelName
            onGetRefundPreview()
        }

        fun onRemoveVehicleClicked() {
            if (refundEligible.get()) {
                removeVehicleNavigationEvent.postValue(RemoveVehicleNavigationEvent.UpdateAddress)
            } else {
                removeVehicleNavigationEvent.postValue(RemoveVehicleNavigationEvent.RemoveVehicle)
            }
        }

        private fun onGetRefundPreview() {
            val cancellationDataRequest =
                vehicle?.let {
                    CancellationDataRequest(
                        orderDate =
                            SimpleDateFormat(DATE_FORMAT_FOR_CANCEL_DATA_REQUEST, Locale.ENGLISH).format(
                                Date(),
                            ),
                        guid = preferenceModel.getGuid(),
                        vin = it.vin,
                        subscriptionsIds = it.subscriptions.map { it.subscriptionID },
                    )
                }

            showProgress()
            subscriptionApiManager.sendGetRefundPreview(
                cancellationDataRequest,
                object : BaseCallback<CancellationDataResponse>() {
                    override fun onSuccess(response: CancellationDataResponse) {
                        super.onSuccess(response)
                        if (response.payload.records != null) {
                            paymentRecord = response.payload.records
                            if (response.payload.previewResult?.creditMemos?.any { creditMemo ->
                                    creditMemo.creditMemoItems?.any {
                                        it.refundEligibilityStatus ==
                                            true
                                    } ==
                                        true
                                } ==
                                true
                            ) {
                                val customerAddress =
                                    preferenceModel.getAccountInfoSubscriber()?.customerAddresses?.firstOrNull()
                                val refundAddress =
                                    object : AddressView.Address {
                                        override var line1: String? = customerAddress?.address
                                        override var line2: String? = null
                                        override var city: String? = customerAddress?.city
                                        override var stateCode: String? = customerAddress?.state
                                        override var zipCode: String? = customerAddress?.zipCode
                                        override var countryCode: String? = customerAddress?.country
                                    }
                                refundEligible.set(true)
                                address.postValue(refundAddress)
                            } else {
                                refundEligible.set(false)
                            }
                        }
                        removeVehicleNavigationEvent.postValue(
                            RemoveVehicleNavigationEvent.RefundStatus,
                        )
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun updateAddress(updatedAddress: AddressView.Address) {
            if (!isAddressUpdated(updatedAddress)) {
                removeVehicleNavigationEvent.postValue(RemoveVehicleNavigationEvent.RemoveVehicle)
            } else {
                showProgress()
                subscriptionApiManager.updatePayment(
                    paymentRecord,
                    UpdatePaymentRequest(
                        addressLine1 = updatedAddress.line1,
                        addressLine2 = updatedAddress.line2,
                        city = updatedAddress.city,
                        state = updatedAddress.stateCode,
                        zipCode = updatedAddress.zipCode,
                        country = updatedAddress.countryCode,
                    ),
                    object : BaseCallback<BaseResponse>() {
                        override fun onSuccess(response: BaseResponse) {
                            showSuccessToastMessage(R.string.payment_method_updated)
                            removeVehicleNavigationEvent.postValue(
                                RemoveVehicleNavigationEvent.RemoveVehicle,
                            )
                        }

                        override fun onFailError(
                            httpCode: Int,
                            errorMsg: String?,
                        ) {
                            showErrorMessage(errorMsg)
                        }

                        override fun onComplete() {
                            hideProgress()
                        }
                    },
                )
            }
        }

        private fun isAddressUpdated(updatedAddress: AddressView.Address): Boolean =
            !(
                updatedAddress.line1.equals(paymentRecord.creditCardAddress1, true) &&
                    updatedAddress.city.equals(paymentRecord.creditCardCity, true) &&
                    updatedAddress.stateCode.equals(paymentRecord.creditCardState, true) &&
                    updatedAddress.countryCode.equals(paymentRecord.creditCardCountry, true) &&
                    updatedAddress.zipCode.equals(paymentRecord.creditCardPostalCode, true)
            )
    }

sealed class RemoveVehicleNavigationEvent {
    object RemoveVehicle : RemoveVehicleNavigationEvent()

    object UpdateAddress : RemoveVehicleNavigationEvent()

    object RefundStatus : RemoveVehicleNavigationEvent()
}
