package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.mapper

import android.content.Context
import com.toyota.oneapp.R
import com.toyota.oneapp.extensions.getLocale
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutPriceListItemUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.PaymentInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.PriceInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.cy17plus.ConfirmPaymentForCY17PlusViewModel
import com.toyota.oneapp.util.DoubleUtil.toDisplayPrice
import com.toyota.oneapp.util.ToyotaConstants
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

class CheckoutPriceListUIMapper
    @Inject
    constructor(
        private val context: Context,
        private val languageManager: LanguageManager,
    ) {
        fun map(
            priceInfo: PriceInfo,
            paymentInfo: PaymentInfo,
            vehicle: VehicleInfo,
        ): List<CheckoutPriceListItemUIModel> {
            val uiModels = mutableListOf<CheckoutPriceListItemUIModel>()
            val vehicleLocale = vehicle.getLocale(language = languageManager.getCurrentLanguage())

            // Add Payment Method - Available only for CY17Plus
            if (paymentInfo is PaymentInfo.CY17PlusPaymentInfo) {
                val paymentMethodValue: String? =
                    paymentInfo.paymentRecord?.let {
                        formPaymentMethodValue(
                            it,
                        )
                    }
                if (paymentMethodValue != null) {
                    uiModels.add(
                        CheckoutPriceListItemUIModel(
                            name = context.getString(R.string.pay_from),
                            value = paymentMethodValue,
                            isValueBold = false,
                        ),
                    )
                }
            }

            uiModels.add(
                CheckoutPriceListItemUIModel(
                    name = context.getString(R.string.Subscription_subtotal),
                    value = priceInfo.totalAmountWithoutTax.toDisplayPrice(vehicleLocale),
                    isValueBold = false,
                ),
            )

            LogTool.d("***Vehicle Region", vehicle.region)
            LogTool.d("Size****", priceInfo.taxationItems?.size.toString())
            LogTool.d("****", priceInfo.taxationItems?.toString())
            if (ToyotaConstants.REGION_CA.equals(vehicle.region, true) && !priceInfo.taxationItems.isNullOrEmpty()) {
                // Summing up tax amounts by tax code
                val taxSummary = mutableMapOf<String, Double>()
                for (tax in priceInfo.taxationItems) {
                    taxSummary[tax.taxCode] = taxSummary.getOrDefault(tax.taxCode, 0.0) + tax.taxAmount
                }

                val paymentItemModel =
                    taxSummary.map {
                        CheckoutPriceListItemUIModel(
                            name = it.key,
                            value = it.value.toDisplayPrice(vehicleLocale),
                            isValueBold = false,
                        )
                    }

                uiModels.addAll(paymentItemModel)
            } else {
                uiModels.add(
                    CheckoutPriceListItemUIModel(
                        name = context.getString(R.string.Subscription_tax),
                        value = priceInfo.totalTaxAmount.toDisplayPrice(vehicleLocale),
                        isValueBold = false,
                    ),
                )
            }

            uiModels.add(
                CheckoutPriceListItemUIModel(
                    name = context.getString(R.string.Subscription_total),
                    value = priceInfo.totalAmount.toDisplayPrice(vehicleLocale),
                    isValueBold = true,
                ),
            )

            return uiModels
        }

        private fun formPaymentMethodValue(paymentRecord: PaymentRecord): String =
            with(paymentRecord) {
                when (paymentRecord.type) {
                    ConfirmPaymentForCY17PlusViewModel.CREDIT_CARD -> {
                        val number =
                            creditCardMaskNumber?.substring(creditCardMaskNumber.length - 4)
                                ?: ""
                        context.getString(R.string.payment_method_value_for_card, number)
                    }
                    else -> {
                        val number = achAccountNumberMask ?: ""
                        context.getString(R.string.payment_method_value_for_account, number)
                    }
                }
            }
    }
