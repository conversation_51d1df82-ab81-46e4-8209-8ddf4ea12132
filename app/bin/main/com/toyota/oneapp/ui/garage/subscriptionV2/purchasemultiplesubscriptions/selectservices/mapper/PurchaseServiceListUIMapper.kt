package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.mapper

import android.content.Context
import com.toyota.oneapp.R
import com.toyota.oneapp.extensions.getLocale
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.model.subscriptionV2.SubscriptionPackage
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants.SUBSCRIPTION_TERM_MONTH
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants.SUBSCRIPTION_TERM_YEAR
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.PurchaseServiceListItemUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.SelectedPackageInfo
import com.toyota.oneapp.util.DoubleUtil.toDisplayPrice
import javax.inject.Inject

class PurchaseServiceListUIMapper
    @Inject
    constructor(
        private val context: Context,
        private val languageManager: LanguageManager,
    ) {
        fun map(
            availableServices: List<AvailableSubscription>,
            selectedPackages: Map<String, SelectedPackageInfo>,
            vehicle: VehicleInfo,
        ): List<PurchaseServiceListItemUIModel> {
            val uiModels = mutableListOf<PurchaseServiceListItemUIModel>()

            val vehicleLocale = vehicle.getLocale(language = languageManager.getCurrentLanguage())

            // Toyota Services
            val toyotaServices =
                availableServices.filter {
                    !it.externalProduct &&
                        !it.category.equals("BUNDLE", false)
                }

            toyotaServices.forEach { service ->
                val selectedPackage = selectedPackages[service.productLine]

                var subTitle: String? = null
                if (selectedPackage != null) {
                    val displayTerm = getDisplayTerm(selectedPackage)
                    val displayPrice = selectedPackage.pkg.price?.toDisplayPrice(vehicleLocale) ?: ""
                    subTitle = "$displayTerm - $displayPrice"

                    val isRenewable = (!vehicle.isCY17) && (service.renewable == true)
                    if (isRenewable) {
                        subTitle += "\n"
                        subTitle +=
                            if (selectedPackage.autoRenew) {
                                context.getString(R.string.Subscription_Auto_Renew_On)
                            } else {
                                context.getString(R.string.Subscription_Auto_Renew_Off)
                            }
                    }
                }

                val serviceUIModel =
                    PurchaseServiceListItemUIModel.Service(
                        title = service.displayProductName,
                        subTitle = subTitle,
                        isSelected = (selectedPackage != null),
                        productLine = service.productLine.orEmpty(),
                        selectedPackageInfo = selectedPackage,
                    )
                uiModels.add(serviceUIModel)
            }

            // Subscription Bundle
            val subscriptionBundles =
                availableServices.filter {
                    it.category != null &&
                        it.category.equals("BUNDLE", true)
                }
            subscriptionBundles.forEach { bundle ->
                val selectedBundle = selectedPackages[bundle.productLine]

                val bundleUIModel =
                    PurchaseServiceListItemUIModel.SubscriptionBundle(
                        isBundleSelected = selectedBundle != null,
                        productLine = bundle.productLine.orEmpty(),
                        title = bundle.displayProductName,
                        subscriptionAmount =
                            if (selectedBundle != null) {
                                selectedBundle.pkg.price?.toDisplayPrice(vehicleLocale)
                            } else {
                                bundle.packages[0].price?.toDisplayPrice(vehicleLocale)
                            },
                        subscriptionTerm =
                            getDistrayTermForBundle(
                                if (selectedBundle != null) {
                                    selectedBundle.pkg
                                } else {
                                    bundle.packages[0]
                                },
                            ),
                        services = bundle.components ?: emptyList(),
                        selectedBundleInfo = selectedBundle,
                    )
                uiModels.add(bundleUIModel)
            }

            // External Services
            val externalServices = availableServices.filter { it.externalProduct }

            if (externalServices.isNotEmpty()) {
                val headerUIModel =
                    PurchaseServiceListItemUIModel.Header(
                        title = context.getString(R.string.third_party_service),
                    )
                uiModels.add(headerUIModel)

                externalServices.forEach { service ->
                    var provider: String? = null
                    if (service.productLine == SubscriptionConstants.WIFI_CONNECT) {
                        provider =
                            if (vehicle.isCY17) {
                                context.getString(R.string.verizon)
                            } else {
                                context.getString(
                                    R.string.at_and_t,
                                )
                            }
                    }

                    val serviceUIModel =
                        PurchaseServiceListItemUIModel.ExternalService(
                            title = service.displayProductName,
                            provider = provider,
                            productLine = service.productLine.orEmpty(),
                        )
                    uiModels.add(serviceUIModel)
                }
            }

            return uiModels
        }

        private fun getDisplayTerm(packageInfo: SelectedPackageInfo): String =
            with(packageInfo) {
                when (pkg.subscriptionTerm) {
                    SUBSCRIPTION_TERM_MONTH -> {
                        context.getString(R.string.Garage_plan_monthly)
                    }
                    SUBSCRIPTION_TERM_YEAR -> {
                        context.getString(R.string.Garage_plan_yearly)
                    }
                    else -> {
                        ""
                    }
                }
            }

        private fun getDistrayTermForBundle(packageInfo: SubscriptionPackage): String =
            with(packageInfo) {
                when (subscriptionTerm) {
                    SUBSCRIPTION_TERM_MONTH -> {
                        " /mo"
                    }
                    SUBSCRIPTION_TERM_YEAR -> {
                        " /yr"
                    }
                    else -> {
                        ""
                    }
                }
            }
    }
