package com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.detail

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.subscription.VehicleSubscriptionAlert
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.detail.mapper.CancelTrialSubscriptionDetailMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.detail.model.CancelTrialSubscriptionDetailUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel - For [CancelTrialSubscriptionDetailFragment]
 */
@HiltViewModel
class CancelTrialSubscriptionDetailViewModel
    @Inject
    constructor(
        private val uiMapper: CancelTrialSubscriptionDetailMapper,
        private val state: SavedStateHandle,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        sealed class Event {
            data class NavigateToCancelAllSubscriptionScreen(
                val vehicle: VehicleInfo,
                val trialSubscriptions: Array<SubscriptionV2>,
                val paidSubscriptions: Array<SubscriptionV2>,
                val isPPOEligible: Boolean,
                val ppoCancelDisclaimer: String?,
            ) : Event()

            data class NavigateToSelectSubscriptionForPurchaseScreen(
                val vehicle: VehicleInfo,
                val isAddVehicleFlow: Boolean,
                val selectedSubscription: AvailableSubscription,
                val trialSubscriptions: Array<SubscriptionV2>,
                val paidSubscriptions: Array<SubscriptionV2>,
                val availableSubscriptions: Array<AvailableSubscription>,
                val accessToken: String?,
                val taxDisclaimer: String?,
                val isAzure: Boolean?,
                val alerts: Array<VehicleSubscriptionAlert>?,
            ) : Event()

            object ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable : Event()
        }

        private val args = CancelTrialSubscriptionDetailFragmentArgs.fromSavedStateHandle(state)

        private val _subscription = MutableLiveData<SubscriptionV2>()
        private val _isPurchaseVisible = MutableLiveData<Boolean>()
        private val _isPrimarySubscriber = MutableLiveData<Boolean>()
        private val _uiModel = MutableLiveData<CancelTrialSubscriptionDetailUIModel>()
        private val _event = SingleLiveEvent<Event>()

        val uiModel: LiveData<CancelTrialSubscriptionDetailUIModel>
            get() = _uiModel
        val event: LiveData<Event>
            get() = _event

        init {
            _uiModel.value = uiMapper.map(args, args.vehicle)
            with(args) {
                _subscription.value = selectedSubscription

                // show purchase button - If the Trial going to expire & Product is available to purchase.
                val isSubscriptionAvailableToPurchase =
                    availableSubscriptions.any {
                        it.productLine == selectedSubscription.productLine
                    }
                _isPrimarySubscriber.value = vehicle.isPrimarySubscriber
                _isPurchaseVisible.value =
                    selectedSubscription.isExpiringSoon == true &&
                    isSubscriptionAvailableToPurchase
            }
        }

        fun onCancel() {
            if (args.selectedSubscription.isExpiringSoon == true) {
                analyticsLogger.logEvent(
                    AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_DETAIL_CANCEL_TRIAL_LESS_THAN_99_DAYS_CLICKED,
                )
            } else {
                analyticsLogger.logEvent(
                    AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_DETAIL_CANCEL_TRIAL_GREATER_THAN_99_DAYS_CLICKED,
                )
            }

            with(args) {
                _event.value =
                    Event.NavigateToCancelAllSubscriptionScreen(
                        vehicle = vehicle,
                        trialSubscriptions = trialSubscriptions,
                        paidSubscriptions = paidSubscriptions,
                        isPPOEligible = isPPOEligible,
                        ppoCancelDisclaimer = ppoCancelDisclaimer,
                    )
            }
        }

        fun onPurchase() {
            with(args) {
                analyticsLogger.logEvent(
                    AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_DETAIL_PURCHASE_CLICKED,
                    SubscriptionConstants.ANALYTICS_KEY_SERVICE to selectedSubscription.displayProductName,
                )

                val subscriptionToPurchase =
                    availableSubscriptions.firstOrNull {
                        it.productLine == selectedSubscription.productLine
                    }

                if (subscriptionToPurchase != null) {
                    if (SubscriptionUtil.checkIsSubscriptionIsAlreadyPurchasedAndIsAutoRenewable(
                            vehicle,
                            subscriptionToPurchase,
                        )
                    ) {
                        _event.value = Event.ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable
                    } else {
                        _event.value =
                            Event.NavigateToSelectSubscriptionForPurchaseScreen(
                                vehicle = vehicle,
                                isAddVehicleFlow = isAddVehicleFlow,
                                selectedSubscription = subscriptionToPurchase,
                                trialSubscriptions = trialSubscriptions,
                                paidSubscriptions = paidSubscriptions,
                                availableSubscriptions = availableSubscriptions,
                                accessToken = accessToken,
                                taxDisclaimer = taxDisclaimer,
                                isAzure = isAzure,
                                alerts = alerts,
                            )
                    }
                }
            }
        }
    }
