package com.toyota.oneapp.ui.garage.wifiSubscription

import androidx.databinding.ObservableBoolean
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.subscriptionV2.SubscriptionType
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.subscriptionV2.isActive
import com.toyota.oneapp.model.subscriptionV2.type
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class ActiveWifiSubscriptionExistViewModel
    @Inject
    constructor(
        val applicationData: ApplicationData,
    ) : BaseViewModel() {
        val activeWifiSubscriptionExistNavigationEvent = SingleLiveEvent<ActiveWifiSubscriptionExistNavigationEvent>()

        private var vehicleGeneration: String = ""
        private var trialSubscriptions: List<SubscriptionV2>? = null

        private val _contactSupportCtaText = MutableLiveData<Int>()

        val contactSupportCtaText: LiveData<Int> = _contactSupportCtaText
        var showAppBar = ObservableBoolean(false)

        fun onInit(
            vehicleGeneration: String,
            trialSubscriptions: List<SubscriptionV2>?,
            showToolbar: Boolean,
        ) {
            this.vehicleGeneration = vehicleGeneration
            this.trialSubscriptions = trialSubscriptions

            _contactSupportCtaText.value = getContactSupportCTAText()
            showAppBar.set(!showToolbar)
        }

        fun onContactSupportClicked(supportNumber: String) {
            activeWifiSubscriptionExistNavigationEvent.postValue(
                ActiveWifiSubscriptionExistNavigationEvent.OnContactSupportClicked(
                    supportNumber = supportNumber,
                ),
            )
        }

        private fun getContactSupportCTAText(): Int =
            if (ToyUtil.isCY17(vehicleGeneration)) R.string.contact_verizon_support else R.string.contact_at_t_support

        fun getSupportNumber(toyotaLexusSupportNumber: String): String {
            if (ToyUtil.isCY17(vehicleGeneration)) {
                trialSubscriptions?.forEach {
                    if (SubscriptionConstants.wifiConnectProductLines.contains(it.productLine)) {
                        // Call Toyota or Lexus support for Wifi trial
                        if (it.type() == SubscriptionType.TRIAL && it.isActive()) {
                            return toyotaLexusSupportNumber
                        }
                    }
                }
                // Call Verizon support for Paid Wifi
                return ToyotaConstants.VERIZON_WIFI_CUSTOMER_SUPPORT
            } else {
                return ToyotaConstants.ATT_WIFI_CUSTOMER_SUPPORT
            }
        }

        sealed class ActiveWifiSubscriptionExistNavigationEvent {
            data class OnContactSupportClicked(
                val supportNumber: String,
            ) : ActiveWifiSubscriptionExistNavigationEvent()
        }
    }
