package com.toyota.oneapp.ui.garage.vehicleUpdate21mm

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.navigation.NavGraph
import androidx.navigation.fragment.NavHostFragment
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityVehicleSoftware21mmUpdateBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.IntentUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VehicleSoftware21MMParentActivity : UiBaseActivity() {
    private lateinit var navGraph: NavGraph

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        val registrationRequestId = intent.getStringExtra(ToyotaConstants.REGISTRATION_REQUEST_ID)
        val vin = intent.getStringExtra(ToyotaConstants.VIN)
        DataBindingUtil
            .setContentView<ActivityVehicleSoftware21mmUpdateBinding>(
                this,
                R.layout.activity_vehicle_software21mm_update,
            ).let { binding ->
                (supportFragmentManager.findFragmentById(binding.navHost.id) as NavHostFragment).navController.let {
                    val graphInflater = it.navInflater
                    navGraph = graphInflater.inflate(R.navigation.software_update_21mm)
                    val bundle = Bundle()
                    bundle.putString(ToyotaConstants.REGISTRATION_REQUEST_ID, registrationRequestId)
                    bundle.putString(ToyotaConstants.VIN, vin)
                    it.setGraph(navGraph, bundle)
                }
            }
    }

    override fun onBackPressed() {
        (supportFragmentManager.findFragmentById(R.id.nav_host) as NavHostFragment).navController.let {
            if (it.currentDestination?.id == R.id.vehicleSoftwareSuccessFragment) {
                startActivity(
                    IntentUtil.getOADashBoardIntent(
                        context = activityContext,
                        isDashboardRefresh = true,
                    ),
                )
                finish()
            } else {
                super.onBackPressed()
            }
        }
    }
}
