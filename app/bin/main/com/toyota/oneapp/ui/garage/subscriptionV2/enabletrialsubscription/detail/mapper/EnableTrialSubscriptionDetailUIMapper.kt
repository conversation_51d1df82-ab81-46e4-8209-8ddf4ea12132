package com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.detail.mapper

import com.toyota.oneapp.R
import com.toyota.oneapp.model.subscriptionV2.productDescription
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.detail.EnableTrialSubscriptionDetailFragmentArgs
import com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.detail.model.EnableTrialSubscriptionDetailUIModel
import javax.inject.Inject

class EnableTrialSubscriptionDetailUIMapper
    @Inject
    constructor() {
        fun map(args: EnableTrialSubscriptionDetailFragmentArgs): EnableTrialSubscriptionDetailUIModel {
            val uiModel: EnableTrialSubscriptionDetailUIModel

            with(args) {
                // If the subscription is ExternalSubscription (WIFI) & it is the only subscription to be enabled - then show action text as "Enable Trial"
                val actionTxt =
                    if (selectedSubscription.externalProduct && trialSubscriptions.size == 1) {
                        R.string.enroll_now
                    } else {
                        R.string.enable_all_trials
                    }

                val wifiCarrierIcon =
                    if (selectedSubscription.externalProduct) {
                        SubscriptionUtil.getWifiCarrierIcon(vehicle)
                    } else {
                        null
                    }

                uiModel =
                    EnableTrialSubscriptionDetailUIModel(
                        productName = selectedSubscription.displayProductName,
                        productDescription = selectedSubscription.productDescription(),
                        productImageUrl = selectedSubscription.productImageUrl,
                        wifiCarrierIcon = wifiCarrierIcon,
                        actionTxt = actionTxt,
                    )
            }

            return uiModel
        }
    }
