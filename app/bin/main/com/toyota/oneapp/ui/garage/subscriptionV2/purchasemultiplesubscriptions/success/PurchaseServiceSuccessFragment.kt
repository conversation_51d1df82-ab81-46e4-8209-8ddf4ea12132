package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success

import android.os.Bundle
import android.view.MenuItem
import androidx.activity.addCallback
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.FragmentPurchaseServiceSuccessBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success.adapter.PurchaseServiceSuccessAdapter
import com.toyota.oneapp.util.IntentUtil.getOADashBoardIntent
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class PurchaseServiceSuccessFragment : BaseDataBindingFragment<FragmentPurchaseServiceSuccessBinding>() {
    private val viewModel: PurchaseServiceSuccessViewModel by viewModels()

    @Inject lateinit var applicationData: ApplicationData

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Add BackPress Callback.
        requireActivity().onBackPressedDispatcher.addCallback(this, true) {
            navigateToDashboard()
        }
    }

    override fun onViewBound(
        binding: FragmentPurchaseServiceSuccessBinding,
        savedInstance: Bundle?,
    ) {
        initializeViews()
        addListeners()
    }

    override fun getLayout(): Int = R.layout.fragment_purchase_service_success

    private fun initializeViews() {
        setHasOptionsMenu(true)
        applicationData.getSelectedVehicle()?.let {
            val serviceAdapter = PurchaseServiceSuccessAdapter(it)
            viewDataBinding.rvServices.run {
                layoutManager = LinearLayoutManager(requireContext())
                adapter = serviceAdapter
            }
        }

        viewModel.uiModel.observe(viewLifecycleOwner) { subscription ->
            DataBindingAdapters.loadImage(
                viewDataBinding.ivVehicle,
                subscription.vehicleImage,
                resources.getDrawable(R.drawable.car_placeholder),
            )
            viewDataBinding.tvVehicleModel.text = subscription.vehicleModel
            viewDataBinding.tvTotalPrice.text = subscription.totalPrice
            viewDataBinding.tvPurchasedDateInfo.text = subscription.purchasedDateInfo
            DataBindingAdapters.setRecyclerViewAdapterData(viewDataBinding.rvServices, subscription.purchasedServices, emptyList())
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            android.R.id.home -> {
                navigateToDashboard()
                true
            }
            else -> {
                super.onOptionsItemSelected(item)
            }
        }

    private fun addListeners() {
        viewDataBinding.btnReturnToDashboard.setOnClickListener {
            navigateToDashboard()
        }
    }

    private fun navigateToDashboard() {
        requireActivity().startActivity(
            getOADashBoardIntent(context = activityContext, isDashboardRefresh = true),
        )
        requireActivity().finish()
    }
}
