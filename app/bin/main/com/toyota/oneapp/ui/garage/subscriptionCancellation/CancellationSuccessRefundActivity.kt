package com.toyota.oneapp.ui.garage.subscriptionCancellation

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityCancellationSuccessRefundBinding
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.ui.BaseActivity
import com.toyota.oneapp.ui.garage.SubscriptionEditSuccess
import com.toyota.oneapp.ui.widget.AddressView.OnValidListener
import com.toyota.oneapp.util.IntentUtil
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
open class CancellationSuccessRefundActivity :
    BaseActivity(),
    OnValidListener {
    lateinit var dataBinding: ActivityCancellationSuccessRefundBinding
    protected val viewModel: CancellationSuccessRefundViwModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        dataBinding =
            DataBindingUtil.setContentView<ActivityCancellationSuccessRefundBinding>(
                this,
                R.layout.activity_cancellation_success_refund,
            )
        dataBinding.lifecycleOwner = this@CancellationSuccessRefundActivity
        dataBinding.executePendingBindings()
        observeBaseEvents(viewModel)
        val parameters = Parameters.createFromIntent(intent)
        viewModel.populateUI(parameters.subscriptionEditSuccess(), parameters.paymentRecord())
        viewModel.onSave.observe(
            this,
            Observer {
                onPaymentRecordUpdated()
            },
        )
        viewModel.address.observe(
            this,
            Observer {
                dataBinding.cancellationSuccessAddress.address = it
            },
        )
        dataBinding.cancellationSuccessAddress.setOnValidListener(this)
        viewModel.state.observe(this) {
            dataBinding.cancellationSuccessDescription1.setText(it.title)
            dataBinding.cancellationSuccessDescription2.setText(it.description)
        }
        dataBinding.cancellationSuccessConfirm.setOnClickListener {
            viewModel.onContinueClicked()
        }
    }

    override fun onValid(isValid: Boolean) {
        dataBinding.cancellationSuccessConfirm.isEnabled = isValid
    }

    protected open fun onPaymentRecordUpdated() {
        startActivity(IntentUtil.getOADashBoardIntent(this, true, false))
        finish()
    }

    class Parameters(
        private val subscriptionEditSuccess: SubscriptionEditSuccess,
        private val paymentRecord: PaymentRecord?,
    ) {
        fun subscriptionEditSuccess(): SubscriptionEditSuccess = subscriptionEditSuccess

        fun paymentRecord(): PaymentRecord? = paymentRecord

        fun createIntent(context: Context?): Intent {
            val intent = Intent(context, CancellationSuccessRefundActivity::class.java)
            intent.putExtra(SUBSCRIPTION_EDIT_SUCCESS_KEY, subscriptionEditSuccess)
            intent.putExtra(PAYMENT_RECORD, paymentRecord)
            return intent
        }

        fun <T : Any> createIntent(
            context: Context?,
            target: Class<T>? = null,
        ): Intent {
            val intent =
                if (target == null) {
                    Intent(
                        context,
                        CancellationSuccessRefundActivity::class.java,
                    )
                } else {
                    Intent(context, target)
                }
            intent.putExtra(SUBSCRIPTION_EDIT_SUCCESS_KEY, subscriptionEditSuccess)
            intent.putExtra(PAYMENT_RECORD, paymentRecord)
            return intent
        }

        companion object {
            private const val SUBSCRIPTION_EDIT_SUCCESS_KEY = "subscriptionEditSuccessKey"
            private const val PAYMENT_RECORD = "paymentRecord"

            fun createFromIntent(intent: Intent): Parameters {
                val subscriptionEditSuccess =
                    intent.getSerializableExtra(
                        SUBSCRIPTION_EDIT_SUCCESS_KEY,
                    ) as SubscriptionEditSuccess
                val paymentRecord = intent.getParcelableExtra<PaymentRecord>(PAYMENT_RECORD)
                return Parameters(subscriptionEditSuccess, paymentRecord)
            }
        }
    } // Parameters class
}
