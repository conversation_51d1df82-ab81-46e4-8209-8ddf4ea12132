package com.toyota.oneapp.ui.garage.subscriptionV2

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.annotation.IdRes
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.navigation.findNavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.NavigationUI
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivitySubscriptionV2HostBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.detail.CancelTrialSubscriptionDetailFragmentArgs
import com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.ManageWiFiSubscriptionDetailFragmentArgs
import com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.detail.EnableTrialSubscriptionDetailFragmentArgs
import com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.enable.EnableTrialSubscriptionFragmentArgs
import com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.detail.PaidSubscriptionDetailFragmentArgs
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseFragmentArgs
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.detail.PurchaseSubscriptionDetailFragmentArgs
import com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.SubscriptionListFragmentArgs
import dagger.hilt.android.AndroidEntryPoint

/**
 * The Activity acts as a Host for all Subscription related screens (fragment).
 *
 * Input
 * 1. isAddVehicleFlow - Used to differentiate whether ManageSubscription is called from Add VIN flow or MyGarage flow.
 * 2. productLine - If produceLine is available then we need to load respective subscription detail screen.
 */

@AndroidEntryPoint
class SubscriptionV2Activity : UiBaseActivity() {
    companion object {
        const val EXTRA_IS_ADD_VEHICLE_FLOW = "EXTRA_IS_ADD_VEHICLE_FLOW"
        const val EXTRA_VEHICLE = "EXTRA_VEHICLE"
        const val EXTRA_PRODUCT_LINE = "EXTRA_PRODUCT_LINE"
        const val EXTRA_PRODUCT_TYPE = "EXTRA_PRODUCT_TYPE"
        const val EXTRA_SUBSCRIPTION_BTN_FLOW = "EXTRA_SUBSCRIPTION_BTN_FLOW"

        // Nav Args
        const val trialSubscriptions = "trialSubscriptions"
        const val selectedSubscription = "selectedSubscription"
        const val availableSubscriptions = "availableSubscriptions"
        const val paidSubscriptions = "paidSubscriptions"
        const val accessToken = "accessToken"
        const val isAzure = "isAzure"
        const val alerts = "alerts"
        const val payload = "payload"

        fun getIntent(
            context: Context,
            vehicle: VehicleInfo,
            isAddVehicleFlow: Boolean,
            productLine: String?,
        ): Intent {
            val intent = Intent(context, SubscriptionV2Activity::class.java)
            intent.putExtra(EXTRA_IS_ADD_VEHICLE_FLOW, isAddVehicleFlow)
            intent.putExtra(EXTRA_VEHICLE, vehicle)
            intent.putExtra(EXTRA_PRODUCT_LINE, productLine)
            return intent
        }
    }

    private val viewModel: SubscriptionV2ViewModel by viewModels()

    private lateinit var binding: ActivitySubscriptionV2HostBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_subscription_v2_host)
        binding.lifecycleOwner = this

        observeBaseEvents(viewModel)

        initializeViews()
        addListeners()
        setUpViewModelBindings()
    }

    private fun initializeViews() {
        performActivitySetup(binding.toolbar)
        val navController = findNavController(R.id.nav_host_fragment)
        val appBarConfiguration =
            AppBarConfiguration
                .Builder()
                .setFallbackOnNavigateUpListener {
                    finish()
                    true
                }.build()
        NavigationUI.setupActionBarWithNavController(this, navController, appBarConfiguration)
    }

    private fun addListeners() {
        val navController = findNavController(R.id.nav_host_fragment)
        navController.addOnDestinationChangedListener { _, destination, _ ->
            when (destination.id) {
                R.id.addVehicleSuccessFragment -> {
                    binding.toolbar.visibility = View.GONE
                }
                else -> {
                    binding.toolbar.visibility = View.VISIBLE
                }
            }
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.event.observe(
            this,
            Observer {
                handleViewModelEvents(it)
            },
        )
    }

    private fun handleViewModelEvents(event: SubscriptionV2ViewModel.Event) {
        when (event) {
            is SubscriptionV2ViewModel.Event.NavigateToSubscriptionListScreen -> {
                navigateToSubscriptionListScreen(event)
            }
            is SubscriptionV2ViewModel.Event.NavigateToEnableTrialSubscriptionScreen -> {
                navigateToEnableTrialSubscriptionScreen(event)
            }
            is SubscriptionV2ViewModel.Event.NavigateToCancelTrialSubscriptionDetailScreen -> {
                navigateToCancelTrialSubscriptionDetailScreen(event)
            }
            is SubscriptionV2ViewModel.Event.NavigateToEnableTrialSubscriptionDetailScreen -> {
                navigateToEnableTrialSubscriptionDetailScreen(event)
            }
            is SubscriptionV2ViewModel.Event.NavigateToManagePaidSubscriptionDetailScreen -> {
                navigateToManagePaidSubscriptionDetailScreen(event)
            }
            is SubscriptionV2ViewModel.Event.NavigateToPurchaseSubscriptionDetailScreen -> {
                navigateToPurchaseSubscriptionDetailScreen(event)
            }
            is SubscriptionV2ViewModel.Event.NavigateToManageWiFiConnectSubscriptionDetailScreen -> {
                navigateToManageWiFiConnectSubscriptionDetailScreen(event)
            }
            is SubscriptionV2ViewModel.Event.NavigateToAddServiceScreen -> {
                navigateToAddServiceScreen(event)
            }
            else -> {
            }
        }.exhaustive
    }

    private fun loadNavigationGraph(
        @IdRes startDestination: Int,
        bundle: Bundle,
    ) {
        val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
        val graph =
            navHostFragment.navController.navInflater.inflate(
                R.navigation.subscription_v2_nav_graph,
            )
        graph.setStartDestination(startDestination)
        navHostFragment.navController.setGraph(graph, bundle)
    }

    private fun navigateToSubscriptionListScreen(event: SubscriptionV2ViewModel.Event.NavigateToSubscriptionListScreen) {
        val bundle =
            SubscriptionListFragmentArgs(
                vehicle = event.vehicle,
                response = event.response,
                isAddVehicleFlow = event.isAddVehicleFlow ?: false,
            ).toBundle()
        loadNavigationGraph(
            startDestination = R.id.subscriptionV2Fragment,
            bundle = bundle,
        )
    }

    private fun navigateToEnableTrialSubscriptionScreen(event: SubscriptionV2ViewModel.Event.NavigateToEnableTrialSubscriptionScreen) {
        val bundle =
            EnableTrialSubscriptionFragmentArgs(
                vehicle = event.vehicle,
                payload = event.payload,
                trialSubscriptions = event.trialSubscriptions.toTypedArray(),
                alerts = event.alerts?.toTypedArray(),
                accessToken = event.accessToken,
                isAzure = event.isAzure ?: false,
                isCPOEligible = event.isCPOEligible ?: false,
                isPaidEnabled = event.isPaidEnabled ?: true,
                isPPOEligible = event.isPPOEligible ?: false,
                isAppUpdateRequired = event.isAppUpdateRequired ?: false,
                isAddVehicleFlow = event.isAddVehicleFlow ?: false,
            ).toBundle()
        loadNavigationGraph(
            startDestination = R.id.enableTrialSubscriptionFragment,
            bundle = bundle,
        )
    }

    private fun navigateToCancelTrialSubscriptionDetailScreen(
        event: SubscriptionV2ViewModel.Event.NavigateToCancelTrialSubscriptionDetailScreen,
    ) {
        val bundle =
            CancelTrialSubscriptionDetailFragmentArgs(
                vehicle = event.vehicle,
                selectedSubscription = event.selectedSubscription,
                trialSubscriptions = event.trialSubscriptions.toTypedArray(),
                paidSubscriptions = event.paidSubscriptions.toTypedArray(),
                availableSubscriptions = event.availableSubscriptions.toTypedArray(),
                accessToken = event.accessToken,
                taxDisclaimer = event.taxDisclaimer,
                isAzure = event.isAzure ?: false,
                isCPOEligible = event.isCPOEligible ?: false,
                isPPOEligible = event.isPPOEligible ?: false,
                ppoCancelDisclaimer = event.ppoCancelDisclaimer,
                alerts = event.alerts?.toTypedArray(),
                isAddVehicleFlow = event.isAddVehicleFlow ?: false,
            ).toBundle()
        loadNavigationGraph(
            startDestination = R.id.cancelTrialSubscriptionDetailFragment,
            bundle = bundle,
        )
    }

    private fun navigateToEnableTrialSubscriptionDetailScreen(
        event: SubscriptionV2ViewModel.Event.NavigateToEnableTrialSubscriptionDetailScreen,
    ) {
        val bundle =
            EnableTrialSubscriptionDetailFragmentArgs(
                vehicle = event.vehicle,
                selectedSubscription = event.selectedSubscription,
                paidSubscriptions = event.paidSubscriptions.toTypedArray(),
                trialSubscriptions = event.trialSubscriptions.toTypedArray(),
                alerts = event.alerts?.toTypedArray(),
                accessToken = event.accessToken,
                isAzure = event.isAzure ?: false,
                isCPOEligible = event.isCPOEligible ?: false,
                isPPOEligible = event.isPPOEligible ?: false,
                isAddVehicleFlow = event.isAddVehicleFlow ?: false,
            ).toBundle()
        loadNavigationGraph(
            startDestination = R.id.enableTrialSubscriptionDetailFragment,
            bundle = bundle,
        )
    }

    private fun navigateToManagePaidSubscriptionDetailScreen(
        event: SubscriptionV2ViewModel.Event.NavigateToManagePaidSubscriptionDetailScreen,
    ) {
        val bundle =
            PaidSubscriptionDetailFragmentArgs(
                vehicle = event.vehicle,
                selectedSubscription = event.selectedSubscription,
                trialSubscriptions = event.trialSubscriptions.toTypedArray(),
                paidSubscriptions = event.paidSubscriptions.toTypedArray(),
            ).toBundle()
        loadNavigationGraph(
            startDestination = R.id.paidSubscriptionDetailFragment,
            bundle = bundle,
        )
    }

    private fun navigateToPurchaseSubscriptionDetailScreen(
        event: SubscriptionV2ViewModel.Event.NavigateToPurchaseSubscriptionDetailScreen,
    ) {
        val bundle =
            PurchaseSubscriptionDetailFragmentArgs(
                vehicle = event.vehicle,
                selectedSubscription = event.selectedSubscription,
                trialSubscriptions = event.trialSubscriptions.toTypedArray(),
                paidSubscriptions = event.paidSubscriptions.toTypedArray(),
                availableSubscriptions = event.availableSubscriptions.toTypedArray(),
                accessToken = event.accessToken,
                isAzure = event.isAzure ?: false,
                alerts = event.alerts?.toTypedArray(),
                isAddVehicleFlow = event.isAddVehicleFlow ?: false,
            ).toBundle()
        loadNavigationGraph(
            startDestination = R.id.purchaseSubscriptionDetailFragment,
            bundle = bundle,
        )
    }

    private fun navigateToManageWiFiConnectSubscriptionDetailScreen(
        event: SubscriptionV2ViewModel.Event.NavigateToManageWiFiConnectSubscriptionDetailScreen,
    ) {
        val bundle =
            ManageWiFiSubscriptionDetailFragmentArgs(
                vehicle = event.vehicle,
                initializationModel = event.initializationModel,
            ).toBundle()
        loadNavigationGraph(
            startDestination = R.id.wiFiSubscriptionDetailFragment,
            bundle = bundle,
        )
    }

    private fun navigateToAddServiceScreen(navigationEvent: SubscriptionV2ViewModel.Event.NavigateToAddServiceScreen) {
        val bundle =
            SelectServicesForPurchaseFragmentArgs(
                vehicle = navigationEvent.vehicle,
                trialSubscriptions = navigationEvent.trialSubscriptions.toTypedArray(),
                paidSubscriptions = navigationEvent.paidSubscriptions.toTypedArray(),
                availableSubscriptions = navigationEvent.availableSubscriptions.toTypedArray(),
                accessToken = navigationEvent.accessToken,
                taxDisclaimer = navigationEvent.taxDisclaimer,
                isAzure = navigationEvent.isAzure ?: false,
                isCPOEligible = navigationEvent.isCPOEligible ?: false,
                alerts = navigationEvent.alerts?.toTypedArray(),
                isPPOEligible = navigationEvent.isPPOEligible ?: false,
                isAddVehicleFlow = navigationEvent.isAddVehicleFlow ?: false,
            ).toBundle()
        loadNavigationGraph(
            startDestination = R.id.selectServicesForPurchaseFragment,
            bundle = bundle,
        )
    }
}
