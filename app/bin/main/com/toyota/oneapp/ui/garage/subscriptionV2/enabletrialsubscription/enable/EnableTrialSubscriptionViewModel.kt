package com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.enable

import androidx.annotation.StringRes
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.combineddataconsent.DeclinePayload
import com.toyota.oneapp.model.subscription.*
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.dataconsent.VehicleSubscriptionAlertsArguments
import com.toyota.oneapp.ui.garage.subscriptionV2.common.servicedetail.mapper.SubscriptionToServiceDetailUIModelMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.common.servicedetail.model.ServiceDetailUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class EnableTrialSubscriptionViewModel
    @Inject
    constructor(
        private val savedStateHandle: SavedStateHandle,
        private val serviceDetailUIModelMapper: SubscriptionToServiceDetailUIModelMapper,
        private val preferenceModel: OneAppPreferenceModel,
        private val subscriptionManager: SubscriptionAPIManager,
        private val combinedDataConsentRepository: CombinedDataConsentRepository,
        private val applicationData: ApplicationData? = null,
    ) : BaseViewModel() {
        private val args = EnableTrialSubscriptionFragmentArgs.fromSavedStateHandle(savedStateHandle)
        private val isAddVehicleFlow: Boolean = args.isAddVehicleFlow

        private var consents: List<ConsentRequestItem>? = null

        sealed class Event {
            data class NavigateToServiceDetailScreen(
                val uiModel: ServiceDetailUIModel,
            ) : Event()

            data class NavigateToAlertsScreen(
                val vehicleSubscriptionAlertsArguments: VehicleSubscriptionAlertsArguments,
            ) : Event()

            object NavigateToBillingAddressScreen : Event()

            data class NavigateToDataConsentScreen(
                val vehicle: VehicleInfo,
                val trialSubscriptions: Array<SubscriptionV2>?,
                val subscriptionGetPayload: SubscriptionGetPayload,
            ) : Event()

            data class NavigateToEnableTrialSuccessScreen(
                val isAddVehicleFlow: Boolean,
            ) : Event()

            data class NavigateToWaiveSubscriptionSuccessScreen(
                val isAddVehicleFlow: Boolean,
            ) : Event()

            data class NavigateToActiveWifiSubscriptionFound(
                val trialSubscriptions: Array<SubscriptionV2>?,
                val vehicle: VehicleInfo,
            ) : Event()

            data class ShowTrialServices(
                val trialSubscriptions: List<SubscriptionV2>,
            ) : Event()

            object ShowUserConfirmationToPurchaseServicesInAddVehicleFlow : Event()

            object ShowNoPackagesAvailableInAddVehicleFlow : Event()

            object ShowAppUpdateRequired : Event()

            data class NavigateToPurchaseServiceScreen(
                val trialSubscriptions: List<SubscriptionV2>,
                val paidSubscriptions: List<SubscriptionV2>,
                val availableSubscription: List<AvailableSubscription>,
                val accessToken: String?,
                val isAzure: Boolean?,
                val alerts: List<VehicleSubscriptionAlert>?,
            ) : Event()

            data class NavigateToAddVehicleSuccessScreen(
                @StringRes val msg: Int,
            ) : Event()

            data class ShowConnectedServicesDecline(
                val datConsent: DeclinePayload?,
            ) : Event()
        }

        private val _event = SingleLiveEvent<Event>()
        private val _PPODisclaimer = MutableLiveData<String?>()

        val event: LiveData<Event>
            get() = _event

        val PPODisclaimer: LiveData<String?>
            get() = _PPODisclaimer

        init {
            _PPODisclaimer.value = if (args.isPPOEligible) args.payload?.ppoDisclaimer else null
        }

        fun isNoThanksVisible(): Boolean = isAddVehicleFlow

        fun checkForTrialServices() {
            if (args.trialSubscriptions.isNotEmpty()) {
                // Trial subscription is available.
                _event.value = Event.ShowTrialServices(args.trialSubscriptions.toList())
            } else {
                _event.value =
                    when {
                        args.isAppUpdateRequired -> Event.ShowAppUpdateRequired
                        args.isPaidEnabled -> Event.ShowUserConfirmationToPurchaseServicesInAddVehicleFlow
                        else -> Event.ShowNoPackagesAvailableInAddVehicleFlow
                    }
            }
        }

        fun showTheAppUpdateDialogOnResume() {
            if (!args.trialSubscriptions.isNotEmpty()) {
                _event.value =
                    when {
                        args.isAppUpdateRequired -> Event.ShowAppUpdateRequired
                        args.isPaidEnabled -> Event.ShowUserConfirmationToPurchaseServicesInAddVehicleFlow
                        else -> Event.ShowNoPackagesAvailableInAddVehicleFlow
                    }
            }
        }

        fun onUserConfirmedToPurchaseServices() {
            args.payload?.apply {
                _event.value =
                    Event.NavigateToPurchaseServiceScreen(
                        trialSubscriptions = trialSubscriptions,
                        paidSubscriptions = paidSubscriptions,
                        availableSubscription = availableSubscriptions,
                        accessToken = accessToken,
                        isAzure = isAzure,
                        alerts = alerts,
                    )
            }
        }

        fun onUserDeclinedToPurchaseServices() {
            _event.value =
                Event.NavigateToAddVehicleSuccessScreen(
                    msg = R.string.add_vehicle_success_msg_for_user_declined_to_purchase,
                )
        }

        fun onSubscriptionServiceItemClick(subscription: SubscriptionV2) {
            val uiModel = serviceDetailUIModelMapper.map(subscription)
            _event.value =
                Event.NavigateToServiceDetailScreen(
                    uiModel = uiModel,
                )
        }

        fun onSubmit() {
            if (!args.alerts?.toList().isNullOrEmpty()) {
                val allAvailableSubscriptionNames =
                    args.trialSubscriptions.toList().joinToString { it.displayProductName }
                _event.value =
                    Event.NavigateToAlertsScreen(
                        vehicleSubscriptionAlertsArguments =
                            VehicleSubscriptionAlertsArguments(
                                allAvailableSubscriptionNames,
                                ArrayList(args.alerts!!.toList()),
                            ),
                    )
            } else {
                if (args.vehicle.isCY17) {
                    _event.value = Event.NavigateToBillingAddressScreen
                } else {
                    navigateToDataConsentScreen()
                }
            }
        }

        fun onAlertsAccepted() {
            if (args.vehicle.isCY17) {
                _event.value = Event.NavigateToBillingAddressScreen
            } else {
                navigateToDataConsentScreen()
            }
        }

        fun onBillingAddressReceived() {
            navigateToDataConsentScreen()
        }

        private fun navigateToDataConsentScreen() {
            _event.value =
                Event.NavigateToDataConsentScreen(
                    trialSubscriptions = args.payload?.trialSubscriptions?.toTypedArray(),
                    vehicle = args.vehicle,
                    subscriptionGetPayload = formSubscriptionGetPayload(),
                )
        }

        fun onDataConsentReceived(consents: List<ConsentRequestItem>) {
            this.consents = consents

            val isMasterConsentAccepted = SubscriptionUtil.checkIsMasterConsentAccepted(consents)

            if (isMasterConsentAccepted) {
                createTrialSubscription()
            } else {
                // Master consent - Declined.
                // Waive subscription.
                waiveSubscriptions()
            }
        }

        fun onUserConfirmToDeclineTrial() {
            waiveSubscriptions()
        }

        fun isWaivedEarlier(): Boolean = args.vehicle.isSubscriptionStatusWaived

        fun fetchCombinedDataConsents() {
            args.vehicle.let {
                viewModelScope.launch {
                    showProgress()
                    val resource =
                        combinedDataConsentRepository.getCombinedDataConsent(
                            vin = it.vin,
                            brand = it.brand,
                            gen = it.generation,
                            region = it.region,
                        )
                    hideProgress()
                    when (resource) {
                        is Resource.Success -> {
                            resource.data?.payload?.eligibleConsents?.let {
                                noThanksPayload(
                                    it.firstOrNull { consent -> consent.masterConsent }?.description?.declinePayload,
                                )
                            }
                        }
                        is Resource.Failure -> {
                            noThanksPayload()
                        }

                        is Resource.Loading -> {}
                    }
                }
            }
        }

        private fun noThanksPayload(declinePayload: DeclinePayload? = null) {
            _event.value = Event.ShowConnectedServicesDecline(datConsent = declinePayload)
        }

        private fun createTrialSubscription() {
            val subscriptionGetPayload = formSubscriptionGetPayload()
            val productsTotalAmount = ProductsTotalAmount(ToyotaConstants.US_CURRENCY_CODE, 0.0)
            val productsAmount = ProductsAmount(ToyotaConstants.US_CURRENCY_CODE, 0.0)
            val taxAmount = TaxAmount(ToyotaConstants.US_CURRENCY_CODE, 0.0)
            val capabilities = args.vehicle.capabilityItems

            viewModelScope.launch {
                showProgress()
                val resource =
                    combinedDataConsentRepository.createSubscription(
                        asiCode = applicationData?.getSelectedVehicle()?.asiCode.orEmpty(),
                        hwtType = applicationData?.getSelectedVehicle()?.hwType.orEmpty(),
                        refId = "",
                        isPaymentDefault = false,
                        paymentToken = "",
                        accessToken = subscriptionGetPayload.accessToken,
                        subscriptionGetPayload = subscriptionGetPayload,
                        capabilities = capabilities,
                        consent = consents,
                        totalAmount = productsTotalAmount,
                        productAmount = productsAmount,
                        taxAmount = taxAmount,
                    )

                when (resource) {
                    is Resource.Success -> {
                        hideProgress()
                        _event.postValue(
                            Event.NavigateToEnableTrialSuccessScreen(
                                isAddVehicleFlow = isAddVehicleFlow,
                            ),
                        )
                    }

                    is Resource.Failure -> {
                        hideProgress()
                        showErrorMessage(R.string.generic_error)
                    }

                    is Resource.Loading -> {}
                }
            }
        }

        private fun waiveSubscriptions() {
            val subscriptionGetPayload = formSubscriptionGetPayload()
            val capabilities = args.vehicle.capabilityItems

            viewModelScope.launch {
                showProgress()
                val resource =
                    combinedDataConsentRepository.waiveSubscription(
                        isWaiver = true,
                        asiCode = applicationData?.getSelectedVehicle()?.asiCode.orEmpty(),
                        hwtType = applicationData?.getSelectedVehicle()?.hwType.orEmpty(),
                        subscriptionGetPayload = subscriptionGetPayload,
                        capabilities = capabilities,
                    )

                when (resource) {
                    is Resource.Success -> {
                        hideProgress()
                        _event.postValue(
                            Event.NavigateToWaiveSubscriptionSuccessScreen(
                                isAddVehicleFlow = isAddVehicleFlow,
                            ),
                        )
                    }

                    is Resource.Failure -> {
                        hideProgress()
                        if (ToyotaConstants.WAIVE_SUBSCRIPTION_ATT_ACTIVE_WIFI_ERROR_CODE.equals(
                                resource.error?.responseCode,
                                true,
                            )
                        ) {
                            _event.value =
                                Event.NavigateToActiveWifiSubscriptionFound(
                                    trialSubscriptions = args.payload?.trialSubscriptions?.toTypedArray(),
                                    vehicle = args.vehicle,
                                )
                        } else {
                            showErrorMessage(resource.message)
                        }
                    }

                    is Resource.Loading -> {}
                }
            }
        }

        private fun formSubscriptionGetPayload(): SubscriptionGetPayload =
            SubscriptionUtil.formSubscriptionGetPayload(
                vehicle = args.vehicle,
                guid = preferenceModel.getGuid(),
                trialSubscriptions = args.trialSubscriptions.toList(),
                accessToken = args.accessToken ?: "",
                isCPOEligible = args.isCPOEligible,
                isPPOEligible = args.isPPOEligible,
            )
    }
