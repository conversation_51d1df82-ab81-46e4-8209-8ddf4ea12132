package com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.manualrefund

import android.os.Bundle
import android.view.MenuItem
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityCancellationSuccessManualRefundBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.widget.AddressView
import com.toyota.oneapp.util.IntentUtil
import dagger.hilt.android.AndroidEntryPoint

/**
 * A Activity - displays Cancellation Success which has Manual Refund - And allows user to confirm address for manual refund.
 */
@AndroidEntryPoint
class CancellationSuccessManualRefundActivity : UiBaseActivity() {
    val viewModel: CancellationSuccessManualRefundViewModel by viewModels()

    private lateinit var binding: ActivityCancellationSuccessManualRefundBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding =
            DataBindingUtil.setContentView(
                this,
                R.layout.activity_cancellation_success_manual_refund,
            )
        binding.viewModel = viewModel

        observeBaseEvents(viewModel)
        addListeners()

        setUpViewModelBindings()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            navigateToDashboard()
            return true
        } else {
            return super.onOptionsItemSelected(item)
        }
    }

    override fun onBackPressed() {
        navigateToDashboard()
    }

    private fun setUpViewModelBindings() {
        viewModel.event.observe(
            this,
            Observer { event ->
                handleEvent(event)
            },
        )
    }

    private fun addListeners() {
        binding.cancellationSuccessAddress.setOnValidListener(
            object : AddressView.OnValidListener {
                override fun onValid(isValid: Boolean) {
                    binding.cancellationSuccessConfirm.isEnabled = isValid
                }
            },
        )
    }

    private fun handleEvent(event: CancellationSuccessManualRefundViewModel.Event) {
        when (event) {
            is CancellationSuccessManualRefundViewModel.Event.NavigateToDashboard -> {
                navigateToDashboard()
            }
        }.exhaustive
    }

    private fun navigateToDashboard() {
        startActivity(IntentUtil.getOADashBoardIntent(context = this, isDashboardRefresh = true))
        finish()
    }
}
