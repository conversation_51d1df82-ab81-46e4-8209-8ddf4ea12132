package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage

import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.selection.SelectionPredicates
import androidx.recyclerview.selection.SelectionTracker
import androidx.recyclerview.selection.StorageStrategy
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.FragmentSelectPackageForServiceBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.adapter.SelectPackageAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.adapter.SelectPackageItemDetailsLookup
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.adapter.SelectPackageKeyProvider
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.model.SelectPackageUIModel
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * A Fragment - Allows user to select Package for the Service.
 */
@AndroidEntryPoint
class SelectPackageForServiceFragment : BaseDataBindingFragment<FragmentSelectPackageForServiceBinding>() {
    companion object {
        const val SELECTED_PACKAGE_REQUEST_KEY = "SELECTED_PACKAGE_REQUEST_KEY"
        const val PRODUCT_LINE = "PRODUCT_LINE"
        const val SELECTED_PACKAGE_INFO = "SELECTED_PACKAGE_INFO"
    }

    @Inject lateinit var applicationData: ApplicationData

    private val args: SelectPackageForServiceFragmentArgs by navArgs()
    private val viewModel: SelectPackageForServiceViewModel by viewModels()

    private lateinit var selectionTracker: SelectionTracker<SelectPackageUIModel>
    private val bundle = Bundle()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentSelectPackageForServiceBinding,
        savedInstance: Bundle?,
    ) {
        initializeViews()
        addListeners()
        setUpViewModelBindings()

        selectionTracker.onRestoreInstanceState(bundle)
    }

    override fun getLayout(): Int = R.layout.fragment_select_package_for_service

    override fun onDestroyView() {
        super.onDestroyView()

        selectionTracker.onSaveInstanceState(bundle)
    }

    private fun initializeViews() {
        viewDataBinding.cbAutoRenew.setOnCheckedChangeListener { _, isChecked ->
            viewModel.updateAutoRenewStatus(isChecked)
        }
        applicationData.getSelectedVehicle()?.let {
            val selectPackageAdapter =
                SelectPackageAdapter(
                    vehicleInfo = it,
                    vehicleLocale = viewModel.getVehicleLocale(),
                )
            viewDataBinding.tvAutoRenewMsg.text =
                if (ToyotaConstants.BUNDLE.equals(args.service.category, true)) {
                    getString(R.string.auto_renew_description) + " " +
                        getString(R.string.bundle_renew_disclaimer) + " " +
                        getString(R.string.bundle_4G_network_disclaimer)
                } else {
                    getString(R.string.auto_renew_description)
                }
            viewDataBinding.rvSubscriptions.run {
                layoutManager = LinearLayoutManager(requireContext())
                adapter = selectPackageAdapter
            }
            selectionTracker =
                SelectionTracker
                    .Builder(
                        "SelectionTracker",
                        viewDataBinding.rvSubscriptions,
                        SelectPackageKeyProvider(selectPackageAdapter),
                        SelectPackageItemDetailsLookup(viewDataBinding.rvSubscriptions),
                        StorageStrategy.createParcelableStorage(SelectPackageUIModel::class.java),
                    ).withSelectionPredicate(SelectionPredicates.createSelectSingleAnything())
                    .build()
            selectPackageAdapter.selectionTracker = selectionTracker

            viewModel.productName.observe(viewLifecycleOwner) { data ->
                viewDataBinding.tvTitle.text = resources.getString(R.string.select_subscription_for_service, data)
            }

            viewModel.packages.observe(viewLifecycleOwner) { data ->
                DataBindingAdapters.setRecyclerViewAdapterData(viewDataBinding.rvSubscriptions, data, emptyList())
            }

            viewModel.autoRenew.observe(viewLifecycleOwner) { data ->
                viewDataBinding.cbAutoRenew.isChecked = data
            }

            viewModel.isAutoRenewVisible.observe(viewLifecycleOwner) { data ->
                DataBindingAdapters.setIsVisible(viewDataBinding.cbAutoRenew, data)
                DataBindingAdapters.setIsVisible(viewDataBinding.tvAutoRenewMsg, data)
            }

            viewModel.isContinueEnabled.observe(viewLifecycleOwner) { data ->
                viewDataBinding.btnContinue.isEnabled = data
            }
            viewDataBinding.btnContinue.setOnClickListener { viewModel.onContinue() }
        }
    }

    private fun addListeners() {
        selectionTracker.addObserver(
            object : SelectionTracker.SelectionObserver<SelectPackageUIModel>() {
                override fun onSelectionChanged() {
                    super.onSelectionChanged()
                    <EMAIL>()
                }
            },
        )
    }

    private fun onSelectionChanged() {
        val selection = selectionTracker.selection
        if (selection.isEmpty) {
            viewModel.onSubscriptionDeSelected()
        } else {
            viewModel.onSubscriptionSelected(selectedPackageUIModel = selection.first())
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.navigationEvent.observe(viewLifecycleOwner, {
            handleNavigationEvents(it)
        })

        viewModel.selectedSubscriptionPackage.observe(viewLifecycleOwner, {
            selectionTracker.select(it)
            viewModel.onSubscriptionSelected(it)
        })
    }

    private fun handleNavigationEvents(event: SelectPackageForServiceViewModel.NavigationEvent) {
        when (event) {
            is SelectPackageForServiceViewModel.NavigationEvent.SendSelectedPackageResultAndFinishTheScreen -> {
                sendSelectedPackageResultAndFinishTheScreen(event)
            }
        }.exhaustive
    }

    private fun sendSelectedPackageResultAndFinishTheScreen(
        event: SelectPackageForServiceViewModel.NavigationEvent.SendSelectedPackageResultAndFinishTheScreen,
    ) {
        val fragmentBundle =
            bundleOf(
                PRODUCT_LINE to event.productLine,
                SELECTED_PACKAGE_INFO to event.selectedPackageInfo,
            )
        setFragmentResult(SELECTED_PACKAGE_REQUEST_KEY, fragmentBundle)
        findNavController().popBackStack()
    }
}
