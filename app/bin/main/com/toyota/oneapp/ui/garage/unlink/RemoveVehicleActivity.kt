package com.toyota.oneapp.ui.garage.unlink

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Html
import android.view.View
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.databinding.Observable
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityRemoveVehicleBinding
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.widget.AddressView
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RemoveVehicleActivity : UiBaseActivity() {
    private val viewModel: RemoveVehicleViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        if (intent.hasExtra(ToyotaConstants.SELECTED_VEHICLE)) {
            val pickedVehicle =
                intent.getParcelableExtra<VehicleInfo>(ToyotaConstants.SELECTED_VEHICLE)
            pickedVehicle?.run {
                viewModel.updateVehicle(this)
            }
        }

        val dataBinding =
            DataBindingUtil.setContentView<ActivityRemoveVehicleBinding>(
                this,
                R.layout.activity_remove_vehicle,
            )
        dataBinding.lifecycleOwner = this
        dataBinding.executePendingBindings()
        performActivitySetup(dataBinding.toolbar)
        observeBaseEvents(viewModel)

        viewModel.removeVehicleNavigationEvent.observe(this) {
            when (it) {
                is RemoveVehicleNavigationEvent.RemoveVehicle -> {
                    val intent = Intent()
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                }
                is RemoveVehicleNavigationEvent.UpdateAddress ->
                    viewModel.updateAddress(
                        dataBinding.removeVehicleAddress.address,
                    )

                is RemoveVehicleNavigationEvent.RefundStatus -> {
                    if (viewModel.refundEligible.get()) {
                        dataBinding.removeVehicleAddress.visibility = View.VISIBLE
                    } else {
                        dataBinding.removeVehicleAddress.visibility = View.GONE
                    }
                }
            }
        }

        viewModel.address.observe(this) {
            if (viewModel.refundEligible.get()) {
                dataBinding.removeVehicleAddress.visibility = View.VISIBLE
                dataBinding.removeVehicleAddress.address = it
            } else {
                dataBinding.removeVehicleAddress.visibility = View.GONE
            }
        }

        dataBinding.removeVehicleAddress.setOnValidListener(
            object : AddressView.OnValidListener {
                override fun onValid(isValid: Boolean) {
                    if (viewModel.refundEligible.get()) {
                        dataBinding.removeVehicleButton.isEnabled = isValid
                    }
                }
            },
        )

        dataBinding.textViewCancel.setOnClickListener {
            val intent = Intent()
            setResult(Activity.RESULT_CANCELED, intent)
            finish()
        }
        dataBinding.removeVehicleHeader.text = getString(R.string.Remove_Vehicle_header, viewModel.vehicleModelName)
        dataBinding.mm21RemoveVehicleContent.isVisible = viewModel.isVehicle21mm ?: false
        dataBinding.mm21RemoveVehicleContent.text =
            Html.fromHtml(String.format(getString(R.string.Remove_Vehicle_21MM_body), viewModel.appBrand))
        dataBinding.non21mmRemoveVehicleContent.isVisible = viewModel.isVehicle21mm != true
        viewModel.refundEligible.addOnPropertyChangedCallback(
            object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(
                    sender: Observable?,
                    propertyId: Int,
                ) {
                    val refundEligible = viewModel.refundEligible.get()
                    dataBinding.removeVehicleContent4.container.isVisible = refundEligible
                    dataBinding.removeVehicleAddress.isVisible = refundEligible
                }
            },
        )
        dataBinding.removeVehicleButton.setOnClickListener {
            viewModel.onRemoveVehicleClicked()
        }
    }

    class Contract : ActivityResultContract<Void, Boolean>() {
        override fun parseResult(
            resultCode: Int,
            intent: Intent?,
        ): Boolean = resultCode == Activity.RESULT_OK

        override fun createIntent(
            context: Context,
            input: Void,
        ): Intent = Intent(context, RemoveVehicleActivity::class.java)
    }
}
