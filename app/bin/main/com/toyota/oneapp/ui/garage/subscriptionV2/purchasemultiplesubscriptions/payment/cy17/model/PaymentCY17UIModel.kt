package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model

import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.SubscriptionV2PaymentCY17Activity

/**
 * A Model - Used to populate [SubscriptionV2PaymentCY17Activity]
 */
data class PaymentCY17UIModel(
    val url: String,
    val authorization: String,
    val guid: String,
    val vin: String,
    val brand: String,
    val productIds: String,
    val totalAmount: String,
    val totalTax: String,
    val language: String,
)
