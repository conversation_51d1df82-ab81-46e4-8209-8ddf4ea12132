package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.cy17plus

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.extensions.getLocale
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscription.*
import com.toyota.oneapp.model.subscriptionV2.subscriptionStartDate
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.network.api.repository.SubscriptionV2Repository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.model.ConfirmPaymentItemModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.model.ConfirmPaymentUIModel
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.DoubleUtil.toDisplayPrice
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel - For [ConfirmPaymentForCY17PlusFragment]
 */
@HiltViewModel
class ConfirmPaymentForCY17PlusViewModel
    @Inject
    constructor(
        private val context: Context,
        private val savedStateHandle: SavedStateHandle,
        private val subscriptionV2Repository: SubscriptionV2Repository,
        private val combinedDataConsentRepository: CombinedDataConsentRepository,
        private val dateUtil: DateUtil,
        private val preferenceModel: OneAppPreferenceModel,
        private val languageManager: LanguageManager,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        sealed class Event {
            object PaymentSuccessNavigation : Event()
        }

        companion object {
            const val CREDIT_CARD = "CreditCard"
        }

        private val args = ConfirmPaymentForCY17PlusFragmentArgs.fromSavedStateHandle(savedStateHandle)
        private val _uiModel = MutableLiveData<ConfirmPaymentUIModel>()
        private val _event = SingleLiveEvent<Event>()

        private var taxesResponse: CalculateTaxesResponse? = null

        val uiModel: LiveData<ConfirmPaymentUIModel>
            get() = _uiModel
        val event: LiveData<Event>
            get() = _event

        init {
            getSubscriptionTaxAmount()
        }

        private fun getSubscriptionTaxAmount() {
            viewModelScope.launch {
                showProgress()

                val resource =
                    subscriptionV2Repository.getSubscriptionTaxAmount(
                        region = args.vehicle.region,
                        billingAddress = args.billingAddress,
                        packages = listOf(args.subscriptionPackage),
                    )
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.let { taxesResponse ->
                            <EMAIL> = taxesResponse
                            _uiModel.value = createUIModel(taxesResponse)
                        }
                    }

                    is Resource.Failure -> {
                        showErrorMessage(R.string.generic_error)
                    }

                    is Resource.Loading -> {}
                }

                hideProgress()
            }
        }

        private fun createUIModel(taxesResponse: CalculateTaxesResponse): ConfirmPaymentUIModel {
            with(args) {
                val vehicleLocale = vehicle.getLocale(language = languageManager.getCurrentLanguage())
                val paymentMethodValue: String? = paymentRecord?.let { formPaymentMethodValue(it) }
                val displayTerm =
                    context.getString(
                        SubscriptionUtil.getDisplayTermUnit(
                            subscriptionPackage.subscriptionTerm
                                ?: "",
                        ),
                    )
                // Show AutoRenew - Only to CY17PLUS vehicle & Subscription should be renewable.
                var autoRenewText: String? = null
                if (args.subscription.renewable == true) {
                    autoRenewText =
                        if (args.isAutoRenew) {
                            context.getString(R.string.Subscription_Auto_Renew_On)
                        } else {
                            context.getString(R.string.Subscription_Auto_Renew_Off)
                        }
                }

                val taxationItems = mutableListOf<ConfirmPaymentItemModel>()
                if (ToyotaConstants.REGION_CA == vehicle.region && !taxesResponse.payload.invoiceItems.isNullOrEmpty()) {
                    // Summing up tax amounts by tax code
                    val taxSummary = mutableMapOf<String, Double>()
                    for (item in taxesResponse.payload.invoiceItems) {
                        for (tax in item.taxationItems.orEmpty()) {
                            taxSummary[tax.taxCode] = taxSummary.getOrDefault(tax.taxCode, 0.0) + tax.taxAmount
                        }
                    }

                    val paymentItemModel =
                        taxSummary.map {
                            ConfirmPaymentItemModel(
                                name = it.key,
                                subTitle = null,
                                value = it.value.toDisplayPrice(vehicleLocale),
                                isBold = false,
                            )
                        }

                    taxationItems.addAll(paymentItemModel)
                } else {
                    taxationItems.add(
                        ConfirmPaymentItemModel(
                            name = context.getString(R.string.Subscription_tax),
                            subTitle = null,
                            value =
                                taxesResponse.payload.totalTaxAmount.toDisplayPrice(
                                    vehicleLocale,
                                ),
                            isBold = false,
                        ),
                    )
                }

                val items =
                    mutableListOf(
                        ConfirmPaymentItemModel(
                            name = subscription.displayProductName,
                            subTitle = autoRenewText,
                            value = "${subscriptionPackage.price?.toDisplayPrice(vehicleLocale)}/$displayTerm",
                            isBold = false,
                        ),
                        ConfirmPaymentItemModel(
                            name = context.getString(R.string.subscription),
                            subTitle = null,
                            value = subscriptionPackage.displaySubscriptionTerm ?: "",
                            isBold = false,
                        ),
                        ConfirmPaymentItemModel(
                            name = context.getString(R.string.start_date),
                            subTitle = null,
                            value =
                                subscriptionPackage.subscriptionStartDate(dateUtil)?.let {
                                    dateUtil.formatMediumDate(
                                        it,
                                    )
                                }
                                    ?: "",
                            isBold = false,
                        ),
                    )

                // Adding Payment Method if Available
                paymentMethodValue?.let {
                    items.add(
                        ConfirmPaymentItemModel(
                            name = context.getString(R.string.ManagePaidSubscription_Payment_Method),
                            subTitle = null,
                            value = it,
                            isBold = false,
                        ),
                    )
                }

                items.add(
                    ConfirmPaymentItemModel(
                        name = context.getString(R.string.Subscription_subtotal),
                        subTitle = null,
                        value =
                            taxesResponse.payload.totalAmountWithoutTax.toDisplayPrice(
                                vehicleLocale,
                            ),
                        isBold = false,
                    ),
                )

                // Adding Tax
                items.addAll(taxationItems)
                items.add(
                    ConfirmPaymentItemModel(
                        name = context.getString(R.string.Subscription_total),
                        subTitle = null,
                        value = taxesResponse.payload.totalAmount.toDisplayPrice(vehicleLocale),
                        isBold = true,
                    ),
                )

                return ConfirmPaymentUIModel(
                    serviceName = subscription.displayProductName,
                    items = items.toList(),
                )
            }
        }

        private fun formPaymentMethodValue(paymentRecord: PaymentRecord): String =
            with(paymentRecord) {
                when (paymentRecord.type) {
                    CREDIT_CARD -> {
                        val number =
                            creditCardMaskNumber?.substring(creditCardMaskNumber.length - 4)
                                ?: ""
                        context.getString(R.string.payment_method_value_for_credit_card, number)
                    }
                    else -> {
                        val number = achAccountNumberMask ?: ""
                        context.getString(R.string.payment_method_value_for_bank_account, number)
                    }
                }
            }

        fun createSubscription() {
            with(args) {
                analyticsLogger.logEvent(
                    com.toyota.oneapp.analytics.AnalyticsEvent.MANAGE_SUBSCRIPTION_PURCHASE_FLOW_CONFIRM_PURCHASE_CLICKED,
                    SubscriptionConstants.ANALYTICS_KEY_SERVICE to subscription.displayProductName,
                    SubscriptionConstants.ANALYTICS_KEY_SUBSCRIPTION_TYPE to subscriptionPackage.displaySubscriptionTerm,
                    SubscriptionConstants.ANALYTICS_KEY_AUTO_RENEW to isAutoRenew,
                )

                val subscriptions = arrayListOf<PreviewSubscriptionItem>()
                val previewSubscriptionItem =
                    SubscriptionUtil.convertAvailableSubscriptionToPreviewSubscriptionItem(
                        subscription = subscription,
                        subscriptionPackage = subscriptionPackage,
                        isAutoRenew = isAutoRenew,
                    )
                subscriptions.add(previewSubscriptionItem)

                val subscriptionGetPayload = SubscriptionGetPayload(vehicle, preferenceModel.getGuid())
                subscriptionGetPayload.list = arrayListOf()
                subscriptionGetPayload.accessToken = ""

                val productsTotalAmount = ProductsTotalAmount("USD", 0.0)
                val productsAmount = ProductsAmount("USD", 0.0)
                val taxAmount = TaxAmount("USD", 0.0)
                val paymentToken = " "

                subscriptionGetPayload.list.clear()
                for (i in 0 until subscriptions.size) {
                    val next = SubscriptionUIItem()
                    next.previewSubscriptionItem = subscriptions[i]
                    subscriptionGetPayload.list.add(next)
                }

                val capabilities = vehicle.capabilityItems

                viewModelScope.launch {
                    showProgress()
                    val resource =
                        combinedDataConsentRepository.createSubscription(
                            asiCode = args.vehicle.asiCode.orEmpty(),
                            hwtType = args.vehicle.hwType.orEmpty(),
                            refId = args.paymentID,
                            isPaymentDefault = args.isDefaultPayment,
                            paymentToken = paymentToken,
                            accessToken = "",
                            subscriptionGetPayload = subscriptionGetPayload,
                            capabilities = capabilities,
                            consent = args.consents.toList(),
                            totalAmount = productsTotalAmount,
                            productAmount = productsAmount,
                            taxAmount = taxAmount,
                        )

                    when (resource) {
                        is Resource.Success -> {
                            hideProgress()
                            _event.postValue(Event.PaymentSuccessNavigation)
                        }

                        is Resource.Failure -> {
                            hideProgress()
                            showErrorMessage(R.string.generic_error)
                        }

                        is Resource.Loading -> {}
                    }
                }
            }
        }
    }
