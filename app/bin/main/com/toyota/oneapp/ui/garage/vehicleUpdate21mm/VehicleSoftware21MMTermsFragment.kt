package com.toyota.oneapp.ui.garage.vehicleUpdate21mm

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.databinding.FragmentVehicleSoftwareTermsBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.util.IntentUtil
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

@AndroidEntryPoint
class VehicleSoftware21MMTermsFragment : BaseDataBindingFragment<FragmentVehicleSoftwareTermsBinding>() {
    private val viewModel: VehicleSoftwareUpdateViewModel by activityViewModels()

    override fun onViewBound(
        binding: FragmentVehicleSoftwareTermsBinding,
        savedInstance: Bundle?,
    ) {
        binding.lifecycleOwner = this
        binding.executePendingBindings()
        binding.viewModel = viewModel

        binding.softwareUpdateToolBar.apply {
            setNavigationOnClickListener { requireActivity().onBackPressed() }
        }

        binding.agreeInstallButton.setOnClickListener {
            findNavController().navigate(
                R.id.action_vehicleUpdateAvailableFragment_to_vehicleSoftwareSuccessFragment,
            )
        }

        binding.disagreeButton.setOnClickListener {
            openDisagreeOTADialog()
        }
    }

    private fun disagreeOTASoftwareUpdate() {
        viewModel.softwareVersionUpdateSuccess(false)
        viewModel.logAnalyticEvent(
            AnalyticsEventParam.VEHICLE_SOFTWARE_OTA,
            AnalyticsEvent.OTA21MM_DISAGREE_UPDATE.eventName,
        )
        activity?.let {
            startActivity(
                IntentUtil.getOADashBoardIntent(
                    context = activityContext,
                    isDashboardRefresh = true,
                ),
            )
        }
        finishActivity()
    }

    private fun openDisagreeOTADialog() {
        DialogUtil.showDialog(
            activity,
            getString(R.string.are_you_sure),
            getString(R.string.ota_disagree_message),
            getString(R.string.sure),
            getString(R.string.go_back),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    disagreeOTASoftwareUpdate()
                }

                override fun onCancelClick() {
                    // Unused
                }
            },
            false,
        )
    }

    override fun getLayout(): Int = R.layout.fragment_vehicle_software_terms
}
