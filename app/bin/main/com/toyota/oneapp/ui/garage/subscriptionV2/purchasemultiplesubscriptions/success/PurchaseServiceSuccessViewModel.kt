package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success.mapper.PurchaseServiceSuccessUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success.model.PurchaseServiceSuccessInitializationModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success.model.PurchaseServiceSuccessUIModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class PurchaseServiceSuccessViewModel
    @Inject
    constructor(
        private val savedStateHandle: SavedStateHandle,
        private val uiMapper: PurchaseServiceSuccessUIMapper,
    ) : BaseViewModel() {
        private val args = PurchaseServiceSuccessFragmentArgs.fromSavedStateHandle(savedStateHandle)
        private val initializationModel: PurchaseServiceSuccessInitializationModel = args.initializationModel

        private val _uiModel = MutableLiveData<PurchaseServiceSuccessUIModel>()

        val uiModel: LiveData<PurchaseServiceSuccessUIModel>
            get() = _uiModel

        init {
            _uiModel.value = uiMapper.map(initializationModel, args.vehicle)
        }
    }
