package com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.cancel.model

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2

/**
 * A UI Model - Used to populate Cancel Subscription List Item.
 */
sealed class CancelSubscriptionListItemUIModel {
    data class Header(
        val title: String,
    ) : CancelSubscriptionListItemUIModel()

    data class Service(
        val title: String,
        val subTitle: String,
        @DrawableRes val icon: Int,
        @ColorRes val iconFillColor: Int,
        val subscription: SubscriptionV2,
    ) : CancelSubscriptionListItemUIModel()

    data class Bundle(
        val title: String,
        val subTitle: String,
        val subscription: SubscriptionV2,
    ) : CancelSubscriptionListItemUIModel()
}
