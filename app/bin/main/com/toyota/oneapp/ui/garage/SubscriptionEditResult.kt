package com.toyota.oneapp.ui.garage

import com.toyota.oneapp.R

enum class SubscriptionEditSuccess(
    val titleResId: Int,
    val firstLineResId: Int,
    val secondLineResId: Int? = null,
) {
    AUTO_RENEW_ACTIVATED(
        R.string.ManagePaidSubscription_Update_Complete,
        R.string.ManagePaidSubscription_auto_renew_succeeded,
        R.string.ManagePaidSubscription_auto_renew_turned_on_description,
    ),
    AUTO_RENEW_DEACTIVATED(
        R.string.ManagePaidSubscription_Update_Complete,
        R.string.ManagePaidSubscription_auto_renew_succeeded,
        R.string.ManagePaidSubscription_auto_renew_turned_off_description,
    ),
    CANCELLED(
        R.string.ManagePaidSubscription_Cancellation_Complete,
        R.string.ManagePaidSubscription_cancellation_succeeded,
        R.string.ManagePaidSubscription_cancellation_description,
    ),
    CANCELLED_NO_REFUND(
        R.string.ManagePaidSubscription_Cancellation_Complete,
        R.string.ManagePaidSubscription_cancellation_succeeded,
    ),
    CANCELLED_REFUND_PENDING(
        R.string.ManagePaidSubscription_cancellation_succeeded,
        R.string.Cancellation_Success_Refund_disclaimer,
        R.string.ManagePaidSubscription_cancellation_description,
    ),
    OTA(
        R.string.OTA_Update_Scheduled,
        R.string.OTA_Update_Scheduled,
    ),
} // SubscriptionEditSuccess enum class

enum class SubscriptionEditFailure(
    val titleResId: Int,
    val descriptionResId: Int? = null,
) {
    AUTO_RENEW(R.string.ManagePaidSubscription_Update_Incomplete),
    CANCELLATION(
        R.string.ManagePaidSubscription_Cancellation_Incomplete,
        R.string.ManagePaidSubscription_Cancellation_Incomplete_description,
    ),
} // SubscriptionEditFailure enum class
