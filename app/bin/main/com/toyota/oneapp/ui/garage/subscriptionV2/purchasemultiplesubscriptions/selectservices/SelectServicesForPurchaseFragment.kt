package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Spannable
import android.text.style.TextAppearanceSpan
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.text.toSpannable
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentSelectServicesForPurchaseBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.subscription.BillingAddress
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.dataconsent.VehicleSubscriptionAlertsContract
import com.toyota.oneapp.ui.dataconsent.VehicleSubscriptionAlertsResult
import com.toyota.oneapp.ui.dataconsent.activities.CombinedDataConsentActivity
import com.toyota.oneapp.ui.garage.BillingAddressActivity
import com.toyota.oneapp.ui.garage.paymentOptions.ManagePaymentOptionsActivity
import com.toyota.oneapp.ui.garage.paymentOptions.model.PaymentCY17PlusResult
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.SubscriptionV2PaymentCY17Contract
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Args
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Result
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.SelectPackageForServiceFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.Event.ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToActiveWifiSubscriptionFound
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToAlerts
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToCheckoutScreen
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToGetBillingAddress
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToGetDateConsentScreen
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToGetPaymentInfoScreenForCY17
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToGetPaymentInfoScreenForCY17Plus
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToManageWiFiConnectServiceDetailScreen
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToMyGarageScreen
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToPackageSelectionScreen
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToServiceDetailScreen
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.SelectServicesForPurchaseViewModel.NavigationEvent.NavigateToWaiveSubscriptionSuccessScreen
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.adapter.PurchaseServiceAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.PurchaseServiceListItemUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.SelectedPackageInfo
import com.toyota.oneapp.ui.garage.wifiSubscription.ActiveWifiSubscriptionExistActivity
import com.toyota.oneapp.util.IntentUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.recyclerview.decorator.SectionItemDecorator

/**
 * A Fragment - Allows user to select Services for Purchase.
 */
@AndroidEntryPoint
class SelectServicesForPurchaseFragment :
    BaseDataBindingFragment<FragmentSelectServicesForPurchaseBinding>(),
    PurchaseServiceAdapter.PurchaseServiceAdapterListener {
    private val args: SelectServicesForPurchaseFragmentArgs by navArgs()

    private val viewModel: SelectServicesForPurchaseViewModel by viewModels()

    private val alertsActivityResult =
        registerForActivityResult<Intent, ActivityResult>(VehicleSubscriptionAlertsContract()) { result ->
            if (result is VehicleSubscriptionAlertsResult.Success) {
                navigateToDataConsentScreen(args.trialSubscriptions, args.paidSubscriptions)
            }
        }

    private val getDataConsent =
        registerForActivityResult<Intent, ActivityResult>(
            ActivityResultContracts.StartActivityForResult(),
            ActivityResultCallback { result: ActivityResult ->
                if (result.resultCode == Activity.RESULT_OK) {
                    val intent = result.data
                    val consents =
                        intent?.getSerializableExtra(ToyotaConstants.CONSENTS) as List<ConsentRequestItem>
                    viewModel.onDataConsentReceived(consents)
                }
            },
        )

    private val getBillingAddress =
        registerForActivityResult<Intent, ActivityResult>(
            ActivityResultContracts.StartActivityForResult(),
            ActivityResultCallback { result: ActivityResult ->
                if (result.resultCode == Activity.RESULT_OK) {
                    result.data?.run {
                        val address = getStringExtra(ToyotaConstants.STREET_ADDRESS) ?: ""
                        val city = getStringExtra(ToyotaConstants.CITY) ?: ""
                        val state = getStringExtra(ToyotaConstants.STATE) ?: ""
                        val zipCode = getStringExtra(ToyotaConstants.ZIP_CODE) ?: ""
                        val country = getStringExtra(ToyotaConstants.COUNTRY) ?: ""
                        val billingAddress =
                            BillingAddress(
                                street = address,
                                city = city,
                                state = state,
                                postalCode = zipCode,
                                country = country,
                            )
                        viewModel.onBillingAddressReceived(billingAddress)
                    }
                }
            },
        )

    private val getPaymentInfoCY17 =
        registerForActivityResult<PaymentCY17Args, PaymentCY17Result?>(
            SubscriptionV2PaymentCY17Contract(),
            ActivityResultCallback { result: PaymentCY17Result? ->
                if (result != null) {
                    viewModel.onPaymentInfoReceivedForCY17(result)
                }
            },
        )

    private val getPaymentInfoCY17Plus =
        registerForActivityResult<Intent, ActivityResult>(
            ActivityResultContracts.StartActivityForResult(),
            ActivityResultCallback { result: ActivityResult ->
                if (result.resultCode == Activity.RESULT_OK) {
                    val intent = result.data
                    val paymentResult =
                        intent?.getParcelableExtra<PaymentCY17PlusResult>(
                            ManagePaymentOptionsActivity.EXTRA_RESULT,
                        )
                    paymentResult?.let { viewModel.onPaymentInfoReceivedForCY17Plus(it) }
                }
            },
        )

    override fun onViewBound(
        binding: FragmentSelectServicesForPurchaseBinding,
        savedInstance: Bundle?,
    ) {
        observeBaseEvents(viewModel)
        initializeViews()
        populateView()
        setUpViewModelBindings(binding)
        registerForResult()
    }

    override fun getLayout(): Int = R.layout.fragment_select_services_for_purchase

    // PurchaseServiceAdapter.PurchaseServiceAdapterListener
    override fun onToyotaServiceCheckboxClicked(service: PurchaseServiceListItemUIModel.Service) {
        viewModel.onToyotaServiceCheckboxClicked(
            selectedPackage = service.selectedPackageInfo,
            productLine = service.productLine,
        )
    }

    override fun onToyotaServiceClicked(service: PurchaseServiceListItemUIModel.Service) {
        viewModel.onToyotaServiceClicked(
            selectedPackage = service.selectedPackageInfo,
            productLine = service.productLine,
        )
    }

    override fun onToyotaServiceMoreIconClicked(service: PurchaseServiceListItemUIModel.Service) {
        viewModel.onToyotaServiceMoreIconClicked(productLine = service.productLine)
    }

    override fun onSubscriptionBundleRadioButtonClicked(bundle: PurchaseServiceListItemUIModel.SubscriptionBundle) {
        viewModel.onSubscriptionBundleRadioButtonClicked(
            bundle.selectedBundleInfo,
            bundle.productLine,
        )
    }

    override fun onSubscriptionBundleClicked(bundle: PurchaseServiceListItemUIModel.SubscriptionBundle) {
        viewModel.onSubscriptionBundleClicked(bundle.selectedBundleInfo, bundle.productLine)
    }

    override fun onSubscriptionBundleChevronClicked(bundle: PurchaseServiceListItemUIModel.SubscriptionBundle) {
        viewModel.onSubscriptionBundleChevronClicked(bundle.productLine)
    }

    override fun onExternalServiceClicked(service: PurchaseServiceListItemUIModel.ExternalService) {
        viewModel.onExternalServiceClicked(productLine = service.productLine)
    }

    private fun initializeViews() {
        val serviceAdapter = PurchaseServiceAdapter(requireContext(), args.vehicle, this)
        viewDataBinding.rvServices.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = serviceAdapter
            addItemDecoration(SectionItemDecorator(requireContext()))
        }
    }

    private fun populateView() {
        val model = "${args.vehicle.modelYear.orEmpty()} ${args.vehicle.modelDescription.orEmpty()}"
        val description = getString(R.string.purchase_service_description, model)
        val descriptionSpan = description.toSpannable()
        descriptionSpan.setSpan(
            TextAppearanceSpan(requireContext(), com.toyota.one_ui.R.style.TextAppearance_OneUi_Caption2),
            0,
            description.indexOf(model) - 1,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE,
        )
        viewDataBinding.tvDescription.text = descriptionSpan
    }

    private fun setUpViewModelBindings(binding: FragmentSelectServicesForPurchaseBinding) {
        with(viewModel) {
            event.observe(viewLifecycleOwner) {
                handleEvent(it)
            }
            navigationEvent.observe(viewLifecycleOwner) {
                handleNavigationEvents(it)
            }
            viewModel.serviceUIModels.observe(viewLifecycleOwner) { data ->
                DataBindingAdapters.setRecyclerViewAdapterData(binding.rvServices, data, emptyList())
            }
            viewModel.isContinueToPurchaseEnabled.observe(viewLifecycleOwner) {
                binding.btnContinue.isEnabled = it
            }
            binding.btnContinue.setOnClickListener {
                viewModel.onContinueToPurchase()
            }
        }
    }

    private fun handleEvent(event: SelectServicesForPurchaseViewModel.Event) {
        when (event) {
            is ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable -> {
                DialogUtil.showDialog(
                    requireActivity(),
                    null,
                    getString(R.string.Subscription_availability_auto_renewed),
                    getString(R.string.Common_ok),
                )
            }
        }
    }

    private fun handleNavigationEvents(event: SelectServicesForPurchaseViewModel.NavigationEvent) {
        when (event) {
            is NavigateToPackageSelectionScreen -> {
                navigateToPackageSelectionScreen(event)
            }
            is NavigateToServiceDetailScreen -> {
                navigateToServiceDetailScreen(event)
            }
            is NavigateToManageWiFiConnectServiceDetailScreen -> {
                navigateToManageWiFiConnectServiceDetailScreen(event)
            }
            is NavigateToAlerts -> {
                navigateToAlertsScreen(event)
            }
            is NavigateToGetDateConsentScreen -> {
                navigateToDataConsentScreen(event.trialSubscriptions, event.paidSubscriptions)
            }
            is NavigateToGetBillingAddress -> {
                navigateToGetBillingAddressScreen()
            }
            is NavigateToGetPaymentInfoScreenForCY17 -> {
                navigateToGetPaymentInfoScreenForCY17(event)
            }
            is NavigateToGetPaymentInfoScreenForCY17Plus -> {
                navigateToGetPaymentInfoScreenForCY17Plus()
            }
            is NavigateToCheckoutScreen -> {
                navigateToCheckoutScreen(event)
            }
            is NavigateToMyGarageScreen -> {
                navigateToMyGarageScreen(event)
            }
            is NavigateToWaiveSubscriptionSuccessScreen -> {
                navigateToWaiveSubscriptionSuccessScreen(event)
            }
            is NavigateToActiveWifiSubscriptionFound -> {
                navigateToActiveWifiSubscriptionFound(event)
            }
        }.exhaustive
    }

    private fun navigateToPackageSelectionScreen(event: NavigateToPackageSelectionScreen) {
        val action =
            SelectServicesForPurchaseFragmentDirections.actionSelectServicesForPurchaseFragmentToSelectPackageForServiceFragment2(
                vehicle = args.vehicle,
                service = event.service,
                selectedPackage = event.selectedPackage,
            )
        findNavController().navigate(action)
    }

    private fun navigateToServiceDetailScreen(event: NavigateToServiceDetailScreen) {
        val action =
            SelectServicesForPurchaseFragmentDirections.actionSelectServicesForPurchaseFragmentToSubscriptionServiceDetailFragment(
                vehicle = args.vehicle,
                uiModel = event.serviceDetailUIModel,
            )
        findNavController().navigate(action)
    }

    private fun navigateToManageWiFiConnectServiceDetailScreen(event: NavigateToManageWiFiConnectServiceDetailScreen) {
        val action =
            SelectServicesForPurchaseFragmentDirections.actionSelectServicesForPurchaseFragmentToWiFiSubscriptionDetailFragment(
                vehicle = args.vehicle,
                initializationModel = event.initializationModel,
            )
        findNavController().navigate(action)
    }

    private fun navigateToAlertsScreen(event: NavigateToAlerts) {
        alertsActivityResult.launch(event.vehicleSubscriptionAlertsArguments)
    }

    private fun navigateToDataConsentScreen(
        trialSubscriptions: Array<SubscriptionV2>,
        paidSubscriptions: Array<SubscriptionV2>,
    ) {
        val intent =
            CombinedDataConsentActivity.getIntent(
                context = requireContext(),
                vehicle = args.vehicle,
                isPaidFlow = true,
                paidSubscriptions = paidSubscriptions,
                trialSubscriptions = trialSubscriptions,
                subscriptionGetPayload = null,
                isGetConsentInfoAsResult = true,
            )
        getDataConsent.launch(intent)
    }

    private fun navigateToGetBillingAddressScreen() {
        val intent = Intent(requireContext(), BillingAddressActivity::class.java)
        getBillingAddress.launch(intent)
    }

    private fun navigateToGetPaymentInfoScreenForCY17(event: NavigateToGetPaymentInfoScreenForCY17) {
        getPaymentInfoCY17.launch(event.args)
    }

    private fun navigateToGetPaymentInfoScreenForCY17Plus() {
        val intent = Intent(requireContext(), ManagePaymentOptionsActivity::class.java)
        intent.putExtra(
            ManagePaymentOptionsActivity.EXTRA_IS_GET_SELECTED_PAYMENT_INFORMATION_AS_RESULT,
            true,
        )
        getPaymentInfoCY17Plus.launch(intent)
    }

    private fun navigateToCheckoutScreen(event: NavigateToCheckoutScreen) {
        val action =
            SelectServicesForPurchaseFragmentDirections.actionSelectServicesForPurchaseFragmentToPurchaseServicesCheckoutFragment(
                vehicle = args.vehicle,
                initializationModel = event.initializationModel,
                isAddVehicleFlow = args.isAddVehicleFlow,
                taxDisclaimer = args.taxDisclaimer,
            )
        findNavController().navigate(action)
    }

    private fun navigateToMyGarageScreen(event: NavigateToMyGarageScreen) {
        startActivity(
            IntentUtil.getOADashBoardIntent(context = activityContext, isDashboardRefresh = true),
        )
        requireActivity().finish()
    }

    private fun navigateToWaiveSubscriptionSuccessScreen(event: NavigateToWaiveSubscriptionSuccessScreen) {
        val action =
            SelectServicesForPurchaseFragmentDirections.actionSelectServicesForPurchaseFragmentToSubscriptionActionSuccessActivity(
                vehicle = args.vehicle,
                icon = R.drawable.ic_cdma_decline,
                title = "",
                msg1 = getString(R.string.add_vehicle_flow_trial_declined_msg1),
                msg2 = getString(R.string.add_vehicle_flow_trial_declined_msg2),
                actionTxt =
                    if (event.isAddVehicleFlow) {
                        getString(R.string.Common_continue)
                    } else {
                        getString(
                            R.string.ServiceCampaign_back_to_dashboard,
                        )
                    },
                msg3 = null,
            )
        findNavController().navigate(action)
    }

    private fun navigateToActiveWifiSubscriptionFound(event: NavigateToActiveWifiSubscriptionFound) {
        val intent =
            ActiveWifiSubscriptionExistActivity.getIntent(
                requireContext(),
                trialSubscriptions = event.trialSubscriptions,
                vehicleGeneration = event.vehicle.generation,
                vehicleRegion = event.vehicle.region,
                isMyGarageFlow = true,
            )
        startActivity(intent)
        requireActivity().finish()
    }

    private fun registerForResult() {
        setFragmentResultListener(
            SelectPackageForServiceFragment.SELECTED_PACKAGE_REQUEST_KEY,
            listener = { _, bundle ->
                val productLine = bundle.getString(SelectPackageForServiceFragment.PRODUCT_LINE, "")
                val selectedPackageInfo =
                    bundle.getParcelable<SelectedPackageInfo>(
                        SelectPackageForServiceFragment.SELECTED_PACKAGE_INFO,
                    )
                selectedPackageInfo?.let { packageInfo ->
                    viewModel.onPackageSelectedForService(
                        productLine = productLine,
                        packageInfo = packageInfo,
                    )
                }
            },
        )
    }
}
