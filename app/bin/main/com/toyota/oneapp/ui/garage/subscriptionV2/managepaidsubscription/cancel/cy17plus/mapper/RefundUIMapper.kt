package com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.cancel.cy17plus.mapper

import com.toyota.oneapp.model.subscription.CancellationDataResponse
import com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.cancel.cy17plus.model.RefundUIModel
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.DoubleUtil.toDisplayPrice
import java.util.*
import javax.inject.Inject

/**
 * A Mapper used to map API model [CancellationDataResponse] to UI Model [RefundUIModel].
 */
class RefundUIMapper
    @Inject
    constructor(
        val dateUtil: DateUtil,
    ) {
        fun map(
            model: CancellationDataResponse,
            vehicleLocale: Locale,
        ): RefundUIModel {
            val creditMemo =
                model.payload.previewResult
                    ?.creditMemos
                    ?.get(0)

            val targetDate = creditMemo?.targetDate?.let { dateUtil.parseLocalDate(it) }
            val displayTargetDate = targetDate?.let { dateUtil.formatMediumDate(it) } ?: ""

            return RefundUIModel(
                isRefundVisible =
                    creditMemo?.creditMemoItems?.get(0)?.refundEligibilityStatus
                        ?: false,
                refundAmount = "${creditMemo?.amountWithoutTax?.toDisplayPrice(vehicleLocale) ?: 0.00}",
                estimatedTax = "${creditMemo?.taxAmount?.toDisplayPrice(vehicleLocale) ?: 0.00}",
                refundDue = "${creditMemo?.amount?.toDisplayPrice(vehicleLocale) ?: 0.00}",
                endDate = displayTargetDate,
            )
        }
    }
