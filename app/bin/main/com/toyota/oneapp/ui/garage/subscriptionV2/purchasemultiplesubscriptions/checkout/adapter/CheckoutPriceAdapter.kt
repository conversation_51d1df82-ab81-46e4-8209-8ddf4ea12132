package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemCheckoutPriceBinding
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutPriceListItemUIModel
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

class CheckoutPriceAdapter :
    RecyclerView.Adapter<CheckoutPriceAdapter.PriceAdapter>(),
    BindableRecyclerViewAdapter<CheckoutPriceListItemUIModel> {
    private var items: List<CheckoutPriceListItemUIModel> = emptyList()

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): PriceAdapter {
        val binding =
            ItemCheckoutPriceBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return PriceAdapter(binding)
    }

    override fun onBindViewHolder(
        holder: PriceAdapter,
        position: Int,
    ) {
        val item = items[position]
        holder.bind(item)
    }

    override fun getItemCount(): Int = items.size

    // BindableRecyclerViewAdapter Methods
    override fun setData(data: List<CheckoutPriceListItemUIModel>?) {
        items = data ?: emptyList()
        notifyDataSetChanged()
    }

    class PriceAdapter(
        private val binding: ItemCheckoutPriceBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        fun bind(item: CheckoutPriceListItemUIModel) {
            binding.item = item
        }
    }
}
