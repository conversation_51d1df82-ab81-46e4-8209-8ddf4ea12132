package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.detail

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.subscription.VehicleSubscriptionAlert
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel - For [PurchaseSubscriptionDetailFragment]
 */
@HiltViewModel
class PurchaseSubscriptionDetailViewModel
    @Inject
    constructor(
        private val savedStateHandle: SavedStateHandle,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        sealed class Event {
            data class NavigateToSelectSubscriptionForPurchaseScreen(
                val selectedSubscription: AvailableSubscription,
                val trialSubscriptions: Array<SubscriptionV2>,
                val paidSubscriptions: Array<SubscriptionV2>,
                val availableSubscriptions: Array<AvailableSubscription>,
                val accessToken: String?,
                val isAzure: Boolean,
                val alerts: Array<VehicleSubscriptionAlert>?,
            ) : Event()

            object ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable : Event()
        }

        private val args = PurchaseSubscriptionDetailFragmentArgs.fromSavedStateHandle(savedStateHandle)
        private val _subscription = MutableLiveData<AvailableSubscription>()
        private val _event = SingleLiveEvent<Event>()

        val subscription: LiveData<AvailableSubscription>
            get() = _subscription
        val event: LiveData<Event>
            get() = _event

        init {
            _subscription.value = args.selectedSubscription
        }

        fun onPurchase() {
            with(args) {
                analyticsLogger.logEvent(
                    com.toyota.oneapp.analytics.AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_DETAIL_PURCHASE_CLICKED,
                    SubscriptionConstants.ANALYTICS_KEY_SERVICE to selectedSubscription.displayProductName,
                )

                if (SubscriptionUtil.checkIsSubscriptionIsAlreadyPurchasedAndIsAutoRenewable(
                        vehicle,
                        selectedSubscription,
                    )
                ) {
                    _event.value = Event.ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable
                } else {
                    _event.value =
                        Event.NavigateToSelectSubscriptionForPurchaseScreen(
                            selectedSubscription = selectedSubscription,
                            trialSubscriptions = trialSubscriptions,
                            paidSubscriptions = paidSubscriptions,
                            availableSubscriptions = availableSubscriptions,
                            accessToken = accessToken,
                            isAzure = isAzure,
                            alerts = alerts,
                        )
                }
            }
        }
    }
