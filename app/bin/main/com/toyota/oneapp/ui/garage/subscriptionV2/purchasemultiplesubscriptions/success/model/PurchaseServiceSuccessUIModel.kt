package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success.model

import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutItemUIModel

data class PurchaseServiceSuccessUIModel(
    val vehicleImage: String,
    val vehicleModel: String,
    val totalPrice: String,
    val purchasedDateInfo: String,
    val purchasedServices: List<CheckoutItemUIModel>,
)
