package com.toyota.oneapp.ui.garage.paymentOptions

import com.toyota.oneapp.model.subscription.PaymentRecord

abstract class PaymentOption protected constructor(
    val paymentRecord: PaymentRecord,
) {
    abstract val accountNumber: String?

    abstract val logoResourceId: Int

    val isDefaultPayment = paymentRecord.defaultPaymentMethod ?: false

    companion object Factory {
        fun selectType(paymentRecord: PaymentRecord) =
            when (paymentRecord.type) {
                "CreditCard" -> Type.CREDIT_CARD
                else -> Type.BANK_ACCOUNT
            }

        fun create(paymentRecord: PaymentRecord) =
            when (selectType(paymentRecord)) {
                Type.CREDIT_CARD -> CreditCardPaymentOption(paymentRecord)
                Type.BANK_ACCOUNT -> BankAccountPaymentOption(paymentRecord)
            }
    } // Factory companion object

    enum class Type {
        CREDIT_CARD,
        BANK_ACCOUNT,
    } // Type enum class
} // PaymentOption class
