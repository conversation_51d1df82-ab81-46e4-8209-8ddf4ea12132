package com.toyota.oneapp.ui.garage.subscriptionV2.common.subscriptionbundle

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemBundleComponentTileViewBinding
import com.toyota.oneapp.databinding.ItemServiceBinding
import com.toyota.oneapp.model.subscriptionV2.BundleComponent
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters

class IndividualServiceAdapter(
    private val vehicleInfo: VehicleInfo,
    private val serviceList: List<BundleComponent>,
    private val showTileView: Boolean = false,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder {
        if (showTileView) {
            val binding =
                ItemBundleComponentTileViewBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                )
            return ServiceTileViewHolder((binding))
        } else {
            val binding =
                ItemServiceBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                )
            return ServiceViewHolder((binding))
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        if (showTileView) {
            val tileVH = holder as ServiceTileViewHolder
            tileVH.bind(serviceList[position])
        } else {
            val serviceVH = holder as ServiceViewHolder
            serviceVH.bind(serviceList[position])
        }
    }

    override fun getItemCount(): Int = serviceList.size

    inner class ServiceTileViewHolder(
        private val binding: ItemBundleComponentTileViewBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        fun bind(serviceItem: BundleComponent) {
            binding.imgService.run {
                setImageResource(
                    SubscriptionUtil.getIconForSubscriptionFromProductLine(
                        serviceItem.productCode,
                    ),
                )
                val tintColor =
                    if (vehicleInfo.isToyotaBrand) R.color.toyota_brand_color else R.color.lexus_brand_color
                DataBindingAdapters.setTint(this, tintColor)
            }
            binding.tvService.text = serviceItem.productName
        }
    }

    inner class ServiceViewHolder(
        private val binding: ItemServiceBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        fun bind(serviceItem: BundleComponent) {
            binding.imgService.run {
                setImageResource(
                    SubscriptionUtil.getIconForSubscriptionFromProductLine(serviceItem.productCode),
                )
                val tintColor =
                    if (vehicleInfo.isToyotaBrand) R.color.toyota_brand_color else R.color.lexus_brand_color
                DataBindingAdapters.setTint(this, tintColor)
            }

            binding.tvService.text = serviceItem.productName
        }
    }
}
