package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscription.*
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.SXMAPIManager
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.api.repository.FRIdpRepository
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Args
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Result
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17UIModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

/**
 * A ViewModel for [SubscriptionV2PaymentCY17Activity]
 */
@HiltViewModel
class SubscriptionV2PaymentCY17ViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val subscriptionManager: SubscriptionAPIManager,
        private val sxmManager: SXMAPIManager,
        private val preferenceModel: OneAppPreferenceModel,
        private val analyticsLogger: AnalyticsLogger,
        private val languageManager: LanguageManager,
        private val frIdpRepository: FRIdpRepository,
    ) : BaseViewModel() {
        sealed class Event {
            data class PaymentAddedSuccessfully(
                val result: PaymentCY17Result,
            ) : Event()

            object Error : Event()

            object OnBack : Event()
        }

        companion object {
            const val PAYMENT_INFORMATION_STORED = "Payment Information Stored"
            const val PAYMENT_INFORMATION_FAILED = "Payment Information Failed"
            const val PAYMENT_INFORMATION_CANCELLED = "Payment Information Cancelled"
            const val PAYMENT_INFORMATION_CONTINUE = "Payment Information Continue"
            const val USER_NEED_TO_RETRY = "User Need to Retry"
        }

        private val args: PaymentCY17Args =
            state[SubscriptionV2PaymentCY17Contract.EXTRA_ARGS] ?: throw IllegalArgumentException(
                "PaymentCY17Args is missing.",
            )

        private val _uiModel = MutableLiveData<PaymentCY17UIModel>()
        private val _event = SingleLiveEvent<Event>()

        private var calculateTaxResponse: CY17CalculateTaxResponse? = null
        private var accessTokenResponse: SXMAccessTokenResponse? = null
        private var isCalculateTaxRequestCompleted = false
        private var isGetTokenRequestCompleted = false

        val uiModel: LiveData<PaymentCY17UIModel>
            get() = _uiModel
        val event: LiveData<Event>
            get() = _event

        init {
            showProgress()
            getTaxAmount()
            getSXMTokenFromFRIDP()
        }

        private fun getTaxAmount() {
            with(args) {
                val taxableProducts = mutableListOf<CY17TaxableProducts>()
                var totalProductPrice = 0.0
                subscriptionPackages.forEach { pkg ->
                    totalProductPrice += pkg.price ?: 0.0

                    val productPrice =
                        CY17Price(
                            currency = pkg.currency.orEmpty(),
                            amount =
                                pkg.price
                                    ?: 0.0,
                        )
                    val taxableProduct =
                        CY17TaxableProducts(
                            productOfferId = pkg.ratePlanID.orEmpty(),
                            productPrice = productPrice,
                            totalProductPrice = productPrice,
                        )
                    taxableProducts.add(taxableProduct)
                }

                val calculateTaxRequest =
                    CY17CalculateTaxRequest(
                        billingAddress = billingAddress,
                        taxableProducts = taxableProducts,
                        totalProductPrice =
                            CY17Price(
                                currency = subscriptionPackages.getOrNull(0)?.currency.orEmpty(),
                                amount = totalProductPrice,
                            ),
                        vin = vehicle.vin,
                    )

                subscriptionManager.sendGetCY17TaxesRequest(
                    vehicle.brand,
                    accessToken,
                    calculateTaxRequest,
                    object : BaseCallback<CY17CalculateTaxResponse>() {
                        override fun onSuccess(response: CY17CalculateTaxResponse) {
                            <EMAIL> = response
                        }

                        override fun onComplete() {
                            isCalculateTaxRequestCompleted = true
                            onRequestCompleted()
                        }
                    },
                )
            }
        }

        private fun getSXMTokenFromFRIDP() {
            viewModelScope.launch {
                val resource = frIdpRepository.getSXMIdTokenFromFRIdp()
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.idToken?.let {
                            getSXMToken(sxmIdTokenFromFRIDP = it)
                        } ?: showErrorMessage(errorMessage = null)
                        LogTool.d("SXM Token From IDP", resource.data?.idToken)
                    }
                    is Resource.Failure -> {
                        hideProgress()
                        _event.postValue(Event.Error)
                    }

                    is Resource.Loading -> {}
                }
            }
        }

        private fun getSXMToken(sxmIdTokenFromFRIDP: String = ToyotaConstants.EMPTY_STRING) {
            sxmManager.sendGetSxmToken(
                sxmIdTokenFromFRIDP,
                object : BaseCallback<SXMAccessTokenResponse>() {
                    override fun onSuccess(response: SXMAccessTokenResponse) {
                        analyticsLogger.logEvent(AnalyticsEvent.PAYMENT_TOKEN_RECEIVED)
                        <EMAIL> = response
                    }

                    override fun onComplete() {
                        isGetTokenRequestCompleted = true
                        onRequestCompleted()
                    }
                },
            )
        }

        private fun onRequestCompleted() {
            if (isCalculateTaxRequestCompleted && isGetTokenRequestCompleted) {
                hideProgress()
                if (calculateTaxResponse != null && accessTokenResponse != null) {
                    analyticsLogger.logEvent(AnalyticsEvent.PAYMENT_PAGE_LAUNCHED_SXM)
                    _uiModel.postValue(
                        createUIModel(
                            calculateTaxResponse = calculateTaxResponse!!,
                            accessTokenResponse = accessTokenResponse!!,
                        ),
                    )
                } else {
                    _event.postValue(Event.Error)
                }
            }
        }

        private fun createUIModel(
            calculateTaxResponse: CY17CalculateTaxResponse,
            accessTokenResponse: SXMAccessTokenResponse,
        ): PaymentCY17UIModel {
            val productIds =
                args.subscriptionPackages.joinToString(separator = ",") { pkg ->
                    pkg.ratePlanID.orEmpty()
                }

            return PaymentCY17UIModel(
                url = BuildConfig.SXM_PAYMENT_PAGE_BASE_URL,
                authorization = accessTokenResponse.accessToken,
                guid = preferenceModel.getGuid(),
                vin = args.vehicle.vin,
                brand = if (args.vehicle.brand == "T") "toyota" else "lexus",
                productIds = productIds,
                totalAmount =
                    calculateTaxResponse.payload.taxResponse.totalPurchaseAmount.amount
                        .toString(),
                totalTax =
                    calculateTaxResponse.payload.taxResponse.taxTotalAmount.amount
                        .toString(),
                language = languageManager.getSXMPaymentLocaleString(),
            )
        }

        fun webEvent(message: String) {
            LogTool.d("****", message)
            val storedPayment = if (message.contains(PAYMENT_INFORMATION_STORED, true)) message else ""
            val continuePayment = if (message.contains(PAYMENT_INFORMATION_CONTINUE, true)) message else ""
            when (message) {
                storedPayment -> {
                    /**
                     * A String - Used to hold result from SXM
                     * Example: "Payment Information Stored - paymentMethodId:dgud677-83780234-ddfr4404"
                     */
                    val paymentStored = storedPayment.split(":")
                    if (paymentStored.size >= 2) {
                        analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_STORED)
                        _event.postValue(
                            Event.PaymentAddedSuccessfully(
                                result =
                                    PaymentCY17Result(
                                        paymentMethodId = paymentStored[1],
                                        paymentAccessToken = accessTokenResponse?.accessToken ?: "",
                                        calculateTaxResponse = calculateTaxResponse!!,
                                    ),
                            ),
                        )
                    } else {
                        analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_FAILED)
                        _event.postValue(Event.Error)
                    }
                }
                PAYMENT_INFORMATION_FAILED -> {
                    analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_FAILED)
                    _event.postValue(Event.Error)
                }
                PAYMENT_INFORMATION_CANCELLED -> {
                    analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_CANCELED)
                    _event.postValue(Event.OnBack)
                }
                continuePayment -> {
                    /**
                     * A String - Used to hold result from SXM
                     * Example: "Payment Information Continue - paymentMethodId:dgud677-83780234-ddfr4404"
                     */
                    val paymentStored = continuePayment.split(":")
                    if (paymentStored.size >= 2) {
                        analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_CONTINUE)
                        _event.postValue(
                            Event.PaymentAddedSuccessfully(
                                result =
                                    PaymentCY17Result(
                                        paymentMethodId = paymentStored[1],
                                        paymentAccessToken = accessTokenResponse!!.accessToken,
                                        calculateTaxResponse = calculateTaxResponse!!,
                                    ),
                            ),
                        )
                    } else {
                        analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_FAILED)
                        _event.postValue(Event.Error)
                    }
                }
                USER_NEED_TO_RETRY -> {
                    analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_RETRY)
                    _event.postValue(Event.Error)
                }
            }
        }
    }
