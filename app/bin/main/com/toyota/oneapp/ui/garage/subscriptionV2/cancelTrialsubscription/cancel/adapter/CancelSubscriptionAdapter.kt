package com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.cancel.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemCancelSubscriptionBundleBinding
import com.toyota.oneapp.databinding.ItemCancelSubscriptionServiceBinding
import com.toyota.oneapp.databinding.ItemSubscriptionListHeaderBinding
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.cancel.model.CancelSubscriptionListItemUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.subscriptionbundle.IndividualServiceAdapter
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter
import toyotaone.commonlib.recyclerview.decorator.SectionIdentifier

/**
 * Adapter - Used to populate Cancel Subscription Screen.
 */
class CancelSubscriptionAdapter(
    private val vehicleInfo: VehicleInfo,
    private var items: List<CancelSubscriptionListItemUIModel>,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>(),
    BindableRecyclerViewAdapter<CancelSubscriptionListItemUIModel> {
    companion object {
        const val ITEM_TYPE_HEADER = 1
        const val ITEM_TYPE_SERVICE = 2
        const val ITEM_TYPE_BUNDLE = 3
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder =
        if (viewType == ITEM_TYPE_HEADER) {
            val binding =
                ItemSubscriptionListHeaderBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                )
            HeaderViewHolder(binding)
        } else if (viewType == ITEM_TYPE_SERVICE) {
            val binding =
                ItemCancelSubscriptionServiceBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                )
            ServiceViewHolder(binding)
        } else {
            val binding =
                ItemCancelSubscriptionBundleBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                )
            BundleViewHolder(binding)
        }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        if (holder.itemViewType == ITEM_TYPE_HEADER) {
            val headerVH = holder as HeaderViewHolder
            val header = items[position] as CancelSubscriptionListItemUIModel.Header
            headerVH.bind(header.title)
        } else if (holder.itemViewType == ITEM_TYPE_SERVICE) {
            val itemVH = holder as ServiceViewHolder
            val service = items[position] as CancelSubscriptionListItemUIModel.Service
            itemVH.bind(service)
        } else {
            val itemVH = holder as BundleViewHolder
            val bundle = items[position] as CancelSubscriptionListItemUIModel.Bundle
            itemVH.bind(bundle)
        }
    }

    override fun getItemCount(): Int = items.size

    override fun getItemViewType(position: Int): Int =
        when (items[position]) {
            is CancelSubscriptionListItemUIModel.Header -> ITEM_TYPE_HEADER
            is CancelSubscriptionListItemUIModel.Service -> ITEM_TYPE_SERVICE
            else -> ITEM_TYPE_BUNDLE
        }

    override fun setData(data: List<CancelSubscriptionListItemUIModel>?) {
        items = data ?: emptyList()
        notifyDataSetChanged()
    }

    inner class HeaderViewHolder(
        private val binding: ItemSubscriptionListHeaderBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ),
        SectionIdentifier {
        fun bind(title: String) {
            binding.title = title
        }

        override fun isSection(): Boolean = true
    }

    inner class ServiceViewHolder(
        private val binding: ItemCancelSubscriptionServiceBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ),
        SectionIdentifier {
        fun bind(service: CancelSubscriptionListItemUIModel.Service) {
            binding.ivIcon.setImageResource(service.icon)
            binding.tvTitle.text = service.title
            binding.tvSubTitle.text = service.subTitle
        }

        override fun isSection(): Boolean = false
    }

    inner class BundleViewHolder(
        private val binding: ItemCancelSubscriptionBundleBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ),
        SectionIdentifier {
        override fun isSection(): Boolean = false

        fun bind(bundle: CancelSubscriptionListItemUIModel.Bundle) {
            val individualServiceAdapter =
                IndividualServiceAdapter(
                    vehicleInfo,
                    bundle.subscription.components ?: emptyList(),
                )
            binding.rvBundleServices.run {
                layoutManager = LinearLayoutManager(itemView.context)
                adapter = individualServiceAdapter
                addItemDecoration(DividerItemDecoration(itemView.context, LinearLayout.VERTICAL))
            }
            binding.item = bundle
        }
    }
}
