package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.selectsubscription.adapter

import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.style.TextAppearanceSpan
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.selection.ItemDetailsLookup
import androidx.recyclerview.selection.ItemKeyProvider
import androidx.recyclerview.selection.SelectionTracker
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemSelectSubscriptionBinding
import com.toyota.oneapp.model.subscriptionV2.SubscriptionPackage
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.util.CurrencyUtils
import com.toyota.oneapp.util.DoubleUtil.toDisplayPrice
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter
import java.util.*

/**
 * Adapter - used to populate Select Subscription for Purchase screen.
 */
class SelectSubscriptionAdapter(
    private val vehicleLocale: Locale,
) : ListAdapter<SubscriptionPackage, SelectSubscriptionAdapter.ViewHolder>(DiffUtilCallback()),
    BindableRecyclerViewAdapter<SubscriptionPackage> {
    lateinit var selectionTracker: SelectionTracker<SubscriptionPackage>

    // RecyclerView.Adapter Methods.
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding =
            ItemSelectSubscriptionBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        val subscriptionPackage = getItem(position)
        val isSelected = selectionTracker.isSelected(subscriptionPackage)
        holder.bind(subscriptionPackage, isSelected)
    }

    override fun setData(data: List<SubscriptionPackage>?) {
        if (data != null) {
            submitList(data)
        }
    }

    // View Holder.
    inner class ViewHolder(
        private val binding: ItemSelectSubscriptionBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        fun bind(
            subscriptionPackage: SubscriptionPackage,
            isSelected: Boolean,
        ) {
            with(subscriptionPackage) {
                binding.tvTitle.text = displaySubscriptionTerm
                binding.tvSubTitle.text = ""
                binding.tvSubTitle.visibility = View.GONE
                val displayPrice = price?.toDisplayPrice(vehicleLocale) ?: ""
                binding.tvPrice.text =
                    formatDisplayPrice(
                        displayPrice,
                        currency
                            ?: ToyotaConstants.US_CURRENCY_CODE,
                    )
                val displayTermUnitStringId =
                    SubscriptionUtil.getDisplayTermUnit(subscriptionTerm ?: "")
                val tvTerm = itemView.context.getString(displayTermUnitStringId)
                binding.tvTerm.text = String.format("/%s", tvTerm)
            }
            itemView.isActivated = isSelected
            if (isSelected) {
                binding.cvCard.strokeWidth =
                    itemView.context.resources
                        .getDimension(R.dimen.size_4dp)
                        .toInt()
            } else {
                binding.cvCard.strokeWidth =
                    itemView.context.resources
                        .getDimension(R.dimen.size_1dp)
                        .toInt()
            }
        }

        // Format DisplayPrice - Change TextSize & color for Currency.
        private fun formatDisplayPrice(
            displayPrice: String,
            currencyCode: String,
        ): SpannableString {
            val displayPriceSpannable = SpannableString(displayPrice)
            val currencySymbol = CurrencyUtils.getCurrencySymbol(currencyCode)
            val startIndex = displayPrice.indexOf(currencySymbol)
            if (startIndex != -1) {
                val endIndex = startIndex + currencySymbol.length
                displayPriceSpannable.setSpan(
                    TextAppearanceSpan(itemView.context, com.toyota.one_ui.R.style.TextAppearance_OneUi_Callout),
                    startIndex,
                    endIndex,
                    Spannable.SPAN_INCLUSIVE_EXCLUSIVE,
                )
                displayPriceSpannable.setSpan(
                    ForegroundColorSpan(
                        ContextCompat.getColor(itemView.context, R.color.coloradadb1),
                    ),
                    startIndex,
                    endIndex,
                    Spannable.SPAN_INCLUSIVE_EXCLUSIVE,
                )
            }
            return displayPriceSpannable
        }

        // ItemDetails
        fun getItemDetails(): ItemDetailsLookup.ItemDetails<SubscriptionPackage> =
            object : ItemDetailsLookup.ItemDetails<SubscriptionPackage>() {
                override fun getPosition(): Int = adapterPosition

                override fun getSelectionKey(): SubscriptionPackage? = getItem(adapterPosition)

                override fun inSelectionHotspot(e: MotionEvent): Boolean = true
            }
    }

    // Diff Util.
    class DiffUtilCallback : DiffUtil.ItemCallback<SubscriptionPackage>() {
        override fun areItemsTheSame(
            oldItem: SubscriptionPackage,
            newItem: SubscriptionPackage,
        ): Boolean = (newItem.packageID == oldItem.packageID)

        override fun areContentsTheSame(
            oldItem: SubscriptionPackage,
            newItem: SubscriptionPackage,
        ): Boolean = newItem == oldItem
    }
}

// Item Details Lookup
class SelectSubscriptionForPurchaseItemDetailsLookup(
    private val recyclerView: RecyclerView,
) : ItemDetailsLookup<SubscriptionPackage>() {
    override fun getItemDetails(event: MotionEvent): ItemDetails<SubscriptionPackage>? {
        val view = recyclerView.findChildViewUnder(event.x, event.y)
        if (view != null) {
            return (recyclerView.getChildViewHolder(view) as SelectSubscriptionAdapter.ViewHolder)
                .getItemDetails()
        }
        return null
    }
}

// Item Key Provider
class SelectSubscriptionForPurchaseKeyProvider(
    private val adapter: SelectSubscriptionAdapter,
) : ItemKeyProvider<SubscriptionPackage>(SCOPE_CACHED) {
    override fun getKey(position: Int): SubscriptionPackage? = adapter.currentList[position]

    override fun getPosition(key: SubscriptionPackage): Int = adapter.currentList.indexOfFirst { it == key }
}
