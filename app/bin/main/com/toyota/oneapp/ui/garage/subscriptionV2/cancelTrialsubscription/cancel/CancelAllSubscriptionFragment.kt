package com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.cancel

import android.app.Activity.RESULT_OK
import android.content.Intent
import android.os.Bundle
import android.widget.LinearLayout
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentCancelAllSubscriptionBinding
import com.toyota.oneapp.ui.ConnectedServicesDialogActivity
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.cancelTrialsubscription.cancel.adapter.CancelSubscriptionAdapter
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

/**
 * This fragment used to cancel all subscription service.
 */
@AndroidEntryPoint
class CancelAllSubscriptionFragment : BaseDataBindingFragment<FragmentCancelAllSubscriptionBinding>() {
    private val args: CancelAllSubscriptionFragmentArgs by navArgs()
    private val viewModel: CancelAllSubscriptionViewModel by viewModels()

    private lateinit var adapter: CancelSubscriptionAdapter

    val CANCEL_SUBSCRIPTION_RESULT = 1001

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentCancelAllSubscriptionBinding,
        savedInstance: Bundle?,
    ) {
        initializeViews()
        addListeners()
        setUpViewModelBindings()
    }

    override fun getLayout(): Int = R.layout.fragment_cancel_all_subscription

    private fun initializeViews() {
        adapter = CancelSubscriptionAdapter(args.vehicle, emptyList())
        viewDataBinding.rvServices.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            addItemDecoration(DividerItemDecoration(requireContext(), LinearLayout.VERTICAL))
        }

        viewModel.subscriptionsUIModels.observe(viewLifecycleOwner) { subscriptions ->
            DataBindingAdapters.setRecyclerViewAdapterData(
                viewDataBinding.rvServices,
                subscriptions,
                emptyList(),
            )
        }

        viewDataBinding.tvCancelDisclaimer.run {
            viewModel.ppoCancelDisclaimer.observe(viewLifecycleOwner) { data ->
                text = data
            }

            viewModel.isPPOEligible.observe(viewLifecycleOwner) { data ->
                DataBindingAdapters.setIsVisible(this, data)
            }
        }
    }

    private fun addListeners() {
        viewDataBinding.btnCancelAllTrials.setOnClickListener {
            showCancelSubscriptionAlert()
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.cancelSubscriptionNavigationEvent.observe(
            viewLifecycleOwner,
            Observer { navigationEvent ->
                when (navigationEvent) {
                    is CancelAllSubscriptionViewModel.NavigationEvent.SuccessCancelSubscription -> {
                        navigateToCancelSuccessScreen()
                    }
                }
            },
        )
    }

    private fun showCancelSubscriptionAlert() {
        val intent =
            ConnectedServicesDialogActivity.getIntent(
                context,
                isToyota = args.vehicle.isToyotaBrand,
                isGarageFlow = true,
            )
        startActivityForResult(intent, CANCEL_SUBSCRIPTION_RESULT)
    }

    override fun onActivityResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
    ) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == CANCEL_SUBSCRIPTION_RESULT) {
            viewModel.cancelSubscription()
        }
    }

    private fun navigateToCancelSuccessScreen() {
        val action =
            CancelAllSubscriptionFragmentDirections.actionCancelAllSubscriptionFragmentToSubscriptionActionSuccessActivity(
                vehicle = args.vehicle,
                icon = R.drawable.success_checkmark,
                title = getString(R.string.ManagePaidSubscription_Cancellation_Complete),
                msg1 = getString(R.string.ManagePaidSubscription_cancellation_succeeded),
                msg2 = null,
                actionTxt = getString(R.string.ServiceCampaign_back_to_dashboard),
                msg3 = null,
            )
        findNavController().navigate(action)
    }
}
