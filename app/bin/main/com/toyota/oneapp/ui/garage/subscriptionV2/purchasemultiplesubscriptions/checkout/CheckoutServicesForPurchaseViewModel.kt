package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.extensions.getLocale
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscription.*
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.mapper.CheckoutPriceListUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.mapper.CheckoutServiceListUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutInitializationModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutItemUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutPriceListItemUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.PaymentInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.SelectedPackageInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.success.model.PurchaseServiceSuccessInitializationModel
import com.toyota.oneapp.util.DoubleUtil.toDisplayPrice
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel - For [CheckoutServicesForPurchaseFragment]
 */
@HiltViewModel
class CheckoutServicesForPurchaseViewModel
    @Inject
    constructor(
        private val savedStateHandle: SavedStateHandle,
        private val serviceListUIMapper: CheckoutServiceListUIMapper,
        private val priceListUIMapper: CheckoutPriceListUIMapper,
        private val preferenceModel: OneAppPreferenceModel,
        private val languageManager: LanguageManager,
        private val combinedDataConsentRepository: CombinedDataConsentRepository,
    ) : BaseViewModel() {
        sealed class Event {
            object ResetSwipeLayout : Event()
        }

        sealed class NavigationEvent {
            data class NavigateToAddVehicleSuccessScreen(
                @DrawableRes val icon: Int,
                @StringRes val title: Int,
                @StringRes val msg: Int,
                @StringRes val ctaText: Int,
            ) : NavigationEvent()

            data class NavigateToPurchaseSubscriptionSuccessScreen(
                val initializationModel: PurchaseServiceSuccessInitializationModel,
            ) : NavigationEvent()
        }

        val args = CheckoutServicesForPurchaseFragmentArgs.fromSavedStateHandle(savedStateHandle)
        private val initializationModel: CheckoutInitializationModel = args.initializationModel

        private val services: List<AvailableSubscription>
        private val selectedPkgInfoMap: Map<String, SelectedPackageInfo>

        private val _serviceUIModels = MutableLiveData<List<CheckoutItemUIModel>>()
        private val _priceUIModels = MutableLiveData<List<CheckoutPriceListItemUIModel>>()
        private val _event = SingleLiveEvent<Event>()
        private val _navigationEvent = SingleLiveEvent<NavigationEvent>()

        val serviceUIModels: LiveData<List<CheckoutItemUIModel>>
            get() = _serviceUIModels
        val priceUIModels: LiveData<List<CheckoutPriceListItemUIModel>>
            get() = _priceUIModels
        val event: LiveData<Event>
            get() = _event
        val navigationEvent: LiveData<NavigationEvent>
            get() = _navigationEvent

        init {
            with(initializationModel) {
                <EMAIL> = services
                <EMAIL> = selectedPackages
            }

            populateView()
        }

        private fun populateView() {
            populateServices()
            populatePaymentDetails()
        }

        private fun populateServices() {
            _serviceUIModels.value =
                serviceListUIMapper.map(
                    services = services,
                    selectedPkgInfoMap = selectedPkgInfoMap,
                    vehicle = args.vehicle,
                )
        }

        private fun populatePaymentDetails() {
            _priceUIModels.value =
                priceListUIMapper.map(
                    priceInfo = initializationModel.priceInfo,
                    paymentInfo = initializationModel.paymentInfo,
                    vehicle = args.vehicle,
                )
        }

        fun onServiceAutoRenewToggled(
            productLine: String,
            autoRenew: Boolean,
        ) {
            selectedPkgInfoMap[productLine]?.autoRenew = autoRenew

            populateServices()
        }

        fun purchaseServices() {
            createSubscription()
        }

        fun createSubscription() {
            with(initializationModel) {
                val subscriptions = arrayListOf<PreviewSubscriptionItem>()
                services.forEach { service ->
                    val selectedPkgInfo = selectedPkgInfoMap[service.productLine]
                    if (selectedPkgInfo != null) {
                        val previewSubscriptionItem =
                            SubscriptionUtil.convertAvailableSubscriptionToPreviewSubscriptionItem(
                                subscription = service,
                                subscriptionPackage = selectedPkgInfo.pkg,
                                isAutoRenew = selectedPkgInfo.autoRenew,
                            )
                        subscriptions.add(previewSubscriptionItem)
                    }
                }

                val subscriptionGetPayload =
                    SubscriptionGetPayload(
                        args.vehicle,
                        preferenceModel.getGuid(),
                    )
                subscriptionGetPayload.list = arrayListOf()
                subscriptionGetPayload.accessToken = accessToken.orEmpty()

                val currency =
                    selectedPackages.values
                        .first()
                        .pkg.currency ?: "USD"
                val productsTotalAmount = ProductsTotalAmount(currency, priceInfo.totalAmount)
                val productsAmount = ProductsAmount(currency, priceInfo.totalAmountWithoutTax)
                val taxAmount = TaxAmount(currency, priceInfo.totalTaxAmount)

                for (i in 0 until subscriptions.size) {
                    val next = SubscriptionUIItem()
                    next.previewSubscriptionItem = subscriptions[i]
                    subscriptionGetPayload.list.add(next)
                }

                val paymentId: String
                val paymentToken: String
                val isDefaultPayment: Boolean
                when (paymentInfo) {
                    is PaymentInfo.CY17PaymentInfo -> {
                        paymentId = paymentInfo.paymentId
                        paymentToken = paymentInfo.paymentAccessToken
                        isDefaultPayment = false
                    }
                    is PaymentInfo.CY17PlusPaymentInfo -> {
                        paymentId = paymentInfo.paymentId
                        paymentToken = ""
                        isDefaultPayment = paymentInfo.isDefaultPayment
                    }
                }.exhaustive

                val capabilities = args.vehicle.capabilityItems

                viewModelScope.launch {
                    showProgress()
                    val resource =
                        combinedDataConsentRepository.createSubscription(
                            asiCode = args.vehicle.asiCode.orEmpty(),
                            hwtType = args.vehicle.hwType.orEmpty(),
                            refId = paymentId,
                            isPaymentDefault = isDefaultPayment,
                            paymentToken = paymentToken,
                            accessToken = accessToken.orEmpty(),
                            subscriptionGetPayload = subscriptionGetPayload,
                            capabilities = capabilities,
                            consent = consents,
                            totalAmount = productsTotalAmount,
                            productAmount = productsAmount,
                            taxAmount = taxAmount,
                        )

                    when (resource) {
                        is Resource.Success -> {
                            hideProgress()
                            onCreateSubscriptionSuccess()
                        }

                        is Resource.Failure -> {
                            hideProgress()
                            onCreateSubscriptionFailure()
                        }
                        else -> {}
                    }
                }
            }
        }

        private fun onCreateSubscriptionSuccess() {
            _navigationEvent.value =
                if (args.isAddVehicleFlow) {
                    NavigationEvent.NavigateToAddVehicleSuccessScreen(
                        icon = R.drawable.ic_success_check_mark_green,
                        title = R.string.Subscription_added_success_title,
                        msg = R.string.add_vehicle_success_msg_for_purchase_service_success,
                        ctaText = R.string.AddVehicle_finish_setup,
                    )
                } else {
                    val vehicleLocale =
                        args.vehicle.getLocale(
                            language = languageManager.getCurrentLanguage(),
                        )
                    val purchaseServiceSuccessInitializationModel =
                        PurchaseServiceSuccessInitializationModel(
                            totalPrice =
                                initializationModel.priceInfo.totalAmount.toDisplayPrice(
                                    vehicleLocale,
                                ),
                            purchasedServices = serviceUIModels.value!!,
                        )

                    NavigationEvent.NavigateToPurchaseSubscriptionSuccessScreen(
                        initializationModel = purchaseServiceSuccessInitializationModel,
                    )
                }
        }

        private fun onCreateSubscriptionFailure() {
            _event.value = Event.ResetSwipeLayout

            if (args.isAddVehicleFlow) {
                _navigationEvent.value =
                    NavigationEvent.NavigateToAddVehicleSuccessScreen(
                        icon = R.drawable.nav_alert,
                        title = R.string.add_vehicle_success_title_for_purchase_service_failure,
                        msg = R.string.add_vehicle_success_msg_for_purchase_service_failure,
                        ctaText = R.string.Common_continue,
                    )
            } else {
                showErrorMessage(R.string.generic_error)
            }
        }
    }
