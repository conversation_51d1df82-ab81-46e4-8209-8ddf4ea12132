package com.toyota.oneapp.ui.garage.subscriptionV2

import androidx.lifecycle.LiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.subscription.VehicleSubscriptionAlert
import com.toyota.oneapp.model.subscriptionV2.*
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.SubscriptionV2Repository
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.model.ManageWiFiSubscriptionDetailInitializationModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel - For [SubscriptionV2Activity]
 */

@HiltViewModel
class SubscriptionV2ViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val analyticsLogger: AnalyticsLogger,
        private val subscriptionV2Repository: SubscriptionV2Repository,
    ) : BaseViewModel() {
        sealed class Event {
            data class NavigateToSubscriptionListScreen(
                val vehicle: VehicleInfo,
                val response: VehicleSubscriptionResponse,
                val isAddVehicleFlow: Boolean?,
            ) : Event()

            data class NavigateToEnableTrialSubscriptionScreen(
                val vehicle: VehicleInfo,
                val payload: VehicleSubscriptionPayload,
                val trialSubscriptions: List<SubscriptionV2>,
                val accessToken: String?,
                val isAzure: Boolean?,
                val isCPOEligible: Boolean?,
                val isPPOEligible: Boolean?,
                val isPaidEnabled: Boolean?,
                val isAppUpdateRequired: Boolean?,
                val alerts: List<VehicleSubscriptionAlert>?,
                val isAddVehicleFlow: Boolean?,
            ) : Event()

            data class NavigateToCancelTrialSubscriptionDetailScreen(
                val vehicle: VehicleInfo,
                val selectedSubscription: SubscriptionV2,
                val trialSubscriptions: List<SubscriptionV2>,
                val paidSubscriptions: List<SubscriptionV2>,
                val availableSubscriptions: List<AvailableSubscription>,
                val accessToken: String?,
                val taxDisclaimer: String?,
                val isAzure: Boolean?,
                val isCPOEligible: Boolean?,
                val isPPOEligible: Boolean?,
                val ppoCancelDisclaimer: String?,
                val alerts: List<VehicleSubscriptionAlert>?,
                val isAddVehicleFlow: Boolean?,
            ) : Event()

            data class NavigateToEnableTrialSubscriptionDetailScreen(
                val vehicle: VehicleInfo,
                val selectedSubscription: SubscriptionV2,
                val trialSubscriptions: List<SubscriptionV2>,
                val paidSubscriptions: List<SubscriptionV2>,
                val accessToken: String?,
                val isAzure: Boolean?,
                val isCPOEligible: Boolean?,
                val isPPOEligible: Boolean?,
                val alerts: List<VehicleSubscriptionAlert>?,
                val isAddVehicleFlow: Boolean?,
            ) : Event()

            data class NavigateToManagePaidSubscriptionDetailScreen(
                val vehicle: VehicleInfo,
                val selectedSubscription: SubscriptionV2,
                val trialSubscriptions: List<SubscriptionV2>,
                val paidSubscriptions: List<SubscriptionV2>,
            ) : Event()

            data class NavigateToPurchaseSubscriptionDetailScreen(
                val vehicle: VehicleInfo,
                val selectedSubscription: AvailableSubscription,
                val trialSubscriptions: List<SubscriptionV2>,
                val paidSubscriptions: List<SubscriptionV2>,
                val availableSubscriptions: List<AvailableSubscription>,
                val accessToken: String?,
                val isAzure: Boolean?,
                val alerts: List<VehicleSubscriptionAlert>?,
                val isAddVehicleFlow: Boolean?,
            ) : Event()

            data class NavigateToManageWiFiConnectSubscriptionDetailScreen(
                val vehicle: VehicleInfo,
                val initializationModel: ManageWiFiSubscriptionDetailInitializationModel,
            ) : Event()

            data class NavigateToAddServiceScreen(
                val vehicle: VehicleInfo,
                val trialSubscriptions: List<SubscriptionV2>,
                val paidSubscriptions: List<SubscriptionV2>,
                val availableSubscriptions: List<AvailableSubscription>,
                val accessToken: String?,
                val taxDisclaimer: String?,
                val isAzure: Boolean?,
                val isCPOEligible: Boolean?,
                val isPPOEligible: Boolean?,
                val alerts: List<VehicleSubscriptionAlert>?,
                val isAddVehicleFlow: Boolean?,
            ) : Event()
        }

        private val vehicle: VehicleInfo =
            state[SubscriptionV2Activity.EXTRA_VEHICLE]
                ?: throw IllegalArgumentException("Vehicle detail is missing.")
        private val isAddVehicleFlow: Boolean =
            state[SubscriptionV2Activity.EXTRA_IS_ADD_VEHICLE_FLOW] ?: false
        private val productLine: String? = state[SubscriptionV2Activity.EXTRA_PRODUCT_LINE]
        private val productType: String? = state[SubscriptionV2Activity.EXTRA_PRODUCT_TYPE]
        private val isSubscriptionBtnFlow: Boolean =
            state[SubscriptionV2Activity.EXTRA_SUBSCRIPTION_BTN_FLOW] ?: false

        private var response: VehicleSubscriptionResponse? = null

        private val _event = SingleLiveEvent<Event>()

        val event: LiveData<Event>
            get() = _event

        init {
            getSubscriptions()
        }

        private fun getSubscriptions() {
            viewModelScope.launch {
                showProgress()

                val resource =
                    subscriptionV2Repository.getVehicleSubscriptions(
                        vin = vehicle.vin,
                        brand = vehicle.brand,
                        region = vehicle.region,
                        generation = vehicle.generation,
                        asiCode = vehicle.asiCode,
                        hwType = vehicle.hwType,
                    )
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.let { responseData ->
                            onGetSubscriptionsSuccess(responseData)
                        }
                    }

                    is Resource.Failure -> {
                        showErrorMessageAndNavigateBack(resource.message)
                    }

                    is Resource.Loading -> {}
                }

                hideProgress()
            }
        }

        private fun onGetSubscriptionsSuccess(responseData: VehicleSubscriptionResponse) {
            this.response = responseData

            if (isAddVehicleFlow) {
                addVehicleFlow(responseData)
            } else {
                if (isSubscriptionBtnFlow) {
                    onActionButtonClicked(responseData)
                } else {
                    onServiceItemClick(responseData)
                }
            }
        }

        fun onServiceItemClick(subscriptionResponse: VehicleSubscriptionResponse) {
            if (subscriptionResponse.payload.trialSubscriptions.firstOrNull { it.productLine == productLine && it.type == productType } !=
                null
            ) {
                val index = subscriptionResponse.payload.trialSubscriptions.indexOfFirst { it.productLine == productLine }
                onTrialSubscriptionClicked(
                    subscriptionResponse,
                    subscriptionResponse.payload.trialSubscriptions[index],
                )
            } else if (subscriptionResponse.payload.paidSubscriptions.firstOrNull { it.productLine == productLine } != null) {
                val index = subscriptionResponse.payload.paidSubscriptions.indexOfFirst { it.productLine == productLine }
                onMyPaidSubscriptionClicked(
                    subscriptionResponse,
                    subscriptionResponse.payload.paidSubscriptions[index],
                )
            } else if (subscriptionResponse.payload.availableSubscriptions.firstOrNull { it.productLine == productLine } != null) {
                /**Go to Subscription List if any trail is available **/
                val trialCard: List<SubscriptionV2> =
                    subscriptionResponse.payload.trialSubscriptions.filter {
                        it.isInActive() &&
                            !it.externalProduct
                    }
                if (trialCard.isEmpty()) {
                    navigateToAddServiceScreen(subscriptionResponse.payload)
                } else {
                    navigateToSubscriptionListScreen(subscriptionResponse)
                }
            } else {
                /** handle if the productLine not matched with availableSubscriptions **/
                navigateToSubscriptionListScreen(subscriptionResponse)
            }
        }

        private fun onTrialSubscriptionClicked(
            subscriptionResponse: VehicleSubscriptionResponse,
            subscriptionV2: SubscriptionV2,
        ) {
            if (subscriptionResponse.payload.trialSubscriptions.firstOrNull { it.productLine == productLine } != null) {
                val subscription = subscriptionResponse.payload.trialSubscriptions.first { it.productLine == productLine }
                if (subscription.isActive()) {
                    // Cancel Trial
                    analyticsLogger.logEvent(
                        AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_TRIAL_ENABLED_SERVICE_CLICKED,
                        SubscriptionConstants.ANALYTICS_KEY_SERVICE to subscriptionV2.displayProductName,
                    )
                    navigateToCancelTrialSubscriptionDetailScreen(
                        subscription = subscription,
                        payload = subscriptionResponse.payload,
                    )
                } else {
                    // Enable Trial
                    analyticsLogger.logEvent(
                        AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_TRIAL_AVAILABLE_CLICKED,
                        SubscriptionConstants.ANALYTICS_KEY_SERVICE to subscriptionV2.displayProductName,
                    )
                    navigateToEnableTrialSubscriptionDetailScreen(
                        subscription = subscription,
                        payload = subscriptionResponse.payload,
                    )
                }
            }
        }

        private fun onMyPaidSubscriptionClicked(
            subscriptionResponse: VehicleSubscriptionResponse,
            subscriptionV2: SubscriptionV2,
        ) {
            analyticsLogger.logEvent(
                AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_PAID_SERVICE_CLICKED,
                SubscriptionConstants.ANALYTICS_KEY_SERVICE to subscriptionV2.displayProductName,
            )

            // Paid
            if (subscriptionResponse.payload.paidSubscriptions.firstOrNull { it.productLine == productLine } != null) {
                val subscription = subscriptionResponse.payload.paidSubscriptions.first { it.productLine == productLine }
                navigateToManagePaidSubscriptionDetailScreen(
                    subscription = subscription,
                    payload = subscriptionResponse.payload,
                )
            }
        }

        private fun onActionButtonClicked(subscriptionResponse: VehicleSubscriptionResponse) {
            val isAllTrialServicesActive =
                subscriptionResponse.payload.trialSubscriptions
                    .filter { !it.externalProduct }
                    .all { it.isActive() }

            if (isAllTrialServicesActive) {
                // Add Service.
                analyticsLogger.logEvent(
                    AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_ADD_SERVICE_CLICKED,
                )
                navigateToAddServiceScreen(subscriptionResponse.payload)
            } else {
                // Enable All Trial.
                analyticsLogger.logEvent(
                    AnalyticsEvent.MANAGE_SUBSCRIPTION_SERVICE_LIST_ENABLE_ALL_TRIAL_CLICKED,
                )
                addVehicleFlow(subscriptionResponse)
            }
        }

        private fun addVehicleFlow(response: VehicleSubscriptionResponse) {
            val trialSubscriptionsToBeEnabled = response.payload.trialSubscriptions.filter { !it.isActive() }
            _event.value =
                Event.NavigateToEnableTrialSubscriptionScreen(
                    vehicle = vehicle,
                    payload = response.payload,
                    trialSubscriptions = trialSubscriptionsToBeEnabled,
                    alerts = response.payload.alerts,
                    accessToken = response.payload.accessToken,
                    isAzure = response.payload.isAzure,
                    isCPOEligible = response.payload.isCPOEligible,
                    isPPOEligible = response.payload.isPPOEligible,
                    isPaidEnabled = response.payload.isPaidEnabled,
                    isAppUpdateRequired = response.payload.isAppUpdateRequired,
                    isAddVehicleFlow = isAddVehicleFlow,
                )
        }

        private fun navigateToSubscriptionListScreen(response: VehicleSubscriptionResponse) {
            _event.value =
                Event.NavigateToSubscriptionListScreen(
                    vehicle = vehicle,
                    response = response,
                    isAddVehicleFlow = isAddVehicleFlow,
                )
        }

        private fun navigateToCancelTrialSubscriptionDetailScreen(
            subscription: SubscriptionV2,
            payload: VehicleSubscriptionPayload,
        ) {
            with(payload) {
                if (subscription.productLine == SubscriptionConstants.WIFI_CONNECT) {
                    // WiFi Connect.
                    _event.value =
                        Event.NavigateToManageWiFiConnectSubscriptionDetailScreen(
                            vehicle = vehicle,
                            initializationModel =
                                SubscriptionUtil.createWiFiSubscriptionInitializationModel(
                                    subscription,
                                ),
                        )
                } else {
                    _event.value =
                        Event.NavigateToCancelTrialSubscriptionDetailScreen(
                            vehicle = vehicle,
                            selectedSubscription = subscription,
                            trialSubscriptions = trialSubscriptions,
                            paidSubscriptions = paidSubscriptions,
                            availableSubscriptions = availableSubscriptions,
                            accessToken = accessToken,
                            taxDisclaimer = taxDisclaimer,
                            isAzure = isAzure,
                            isCPOEligible = isCPOEligible,
                            alerts = alerts,
                            isPPOEligible = isPPOEligible,
                            ppoCancelDisclaimer = ppoCancelDisclaimer,
                            isAddVehicleFlow = isAddVehicleFlow,
                        )
                }
            }
        }

        private fun navigateToAddServiceScreen(payload: VehicleSubscriptionPayload) {
            with(payload) {
                _event.value =
                    Event.NavigateToAddServiceScreen(
                        vehicle = vehicle,
                        trialSubscriptions = trialSubscriptions,
                        paidSubscriptions = paidSubscriptions,
                        availableSubscriptions = availableSubscriptions,
                        accessToken = accessToken,
                        taxDisclaimer = taxDisclaimer,
                        isAzure = isAzure,
                        isCPOEligible = isCPOEligible,
                        isPPOEligible = isPPOEligible,
                        alerts = alerts,
                        isAddVehicleFlow = isAddVehicleFlow,
                    )
            }
        }

        private fun navigateToEnableTrialSubscriptionDetailScreen(
            subscription: SubscriptionV2,
            payload: VehicleSubscriptionPayload,
        ) {
            with(payload) {
                _event.value =
                    Event.NavigateToEnableTrialSubscriptionDetailScreen(
                        vehicle = vehicle,
                        selectedSubscription = subscription,
                        trialSubscriptions = trialSubscriptions.filter { it.isInActive() },
                        paidSubscriptions = paidSubscriptions,
                        accessToken = accessToken,
                        isAzure = isAzure,
                        isCPOEligible = isCPOEligible,
                        isPPOEligible = isPPOEligible,
                        alerts = alerts,
                        isAddVehicleFlow = isAddVehicleFlow,
                    )
            }
        }

        private fun navigateToManagePaidSubscriptionDetailScreen(
            subscription: SubscriptionV2,
            payload: VehicleSubscriptionPayload,
        ) {
            with(payload) {
                if (subscription.productLine == SubscriptionConstants.WIFI_CONNECT) {
                    // WiFi Connect.
                    _event.value =
                        Event.NavigateToManageWiFiConnectSubscriptionDetailScreen(
                            vehicle = vehicle,
                            initializationModel =
                                SubscriptionUtil.createWiFiSubscriptionInitializationModel(
                                    subscription,
                                ),
                        )
                } else {
                    _event.value =
                        Event.NavigateToManagePaidSubscriptionDetailScreen(
                            vehicle = vehicle,
                            selectedSubscription = subscription,
                            trialSubscriptions = trialSubscriptions,
                            paidSubscriptions = paidSubscriptions,
                        )
                }
            }
        }

        private fun navigateToPurchaseSubscriptionDetailScreen(
            subscription: AvailableSubscription,
            payload: VehicleSubscriptionPayload,
        ) {
            with(payload) {
                if (subscription.productLine == SubscriptionConstants.WIFI_CONNECT) {
                    // WiFi Connect.
                    _event.value =
                        Event.NavigateToManageWiFiConnectSubscriptionDetailScreen(
                            vehicle = vehicle,
                            initializationModel =
                                SubscriptionUtil.createWiFiSubscriptionInitializationModel(
                                    subscription,
                                ),
                        )
                } else {
                    _event.value =
                        Event.NavigateToPurchaseSubscriptionDetailScreen(
                            vehicle = vehicle,
                            selectedSubscription = subscription,
                            trialSubscriptions = trialSubscriptions,
                            paidSubscriptions = paidSubscriptions,
                            availableSubscriptions = availableSubscriptions,
                            accessToken = accessToken,
                            isAzure = isAzure,
                            alerts = alerts,
                            isAddVehicleFlow = isAddVehicleFlow,
                        )
                }
            }
        }
    }
