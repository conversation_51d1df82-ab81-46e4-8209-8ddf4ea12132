package com.toyota.oneapp.ui.garage

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.LinearLayout.VERTICAL
import androidx.appcompat.widget.Toolbar
import androidx.recyclerview.widget.DividerItemDecoration
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.OwnerManualAdapter
import com.toyota.oneapp.adapter.Section
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.extensions.showToast
import com.toyota.oneapp.model.vehicle.OwnerManualItem
import com.toyota.oneapp.model.vehicle.OwnerManualList
import com.toyota.oneapp.model.vehicle.OwnersManualPayload
import com.toyota.oneapp.ui.MVPBaseActivity
import com.toyota.oneapp.ui.newdashboard.OwnersManualPresenter
import com.toyota.oneapp.util.ToyUtil.hasNetwork
import com.toyota.oneapp.util.ToyUtil.openCustomChromeTab
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@AndroidEntryPoint
class OwnerManualActivity :
    MVPBaseActivity<OwnersManualPresenter>(),
    OwnersManualPresenter.View,
    OwnerManualAdapter.OwnerManualClickListener {
    companion object {
        private const val TAG = "OwnerManualActivity"
        private const val EXTRA_OWNER_LIST = "EXTRA_OWNER_LIST"
        private const val EXTRA_OWNER_MANUAL_PAYLOAD = "EXTRA_OWNER_MANUAL_PAYLOAD"

        fun getIntent(
            context: Context,
            ownersList: OwnerManualList,
            ownerManualPayload: OwnersManualPayload,
        ): Intent {
            val intent = Intent(context, OwnerManualActivity::class.java)
            intent.putExtra(EXTRA_OWNER_LIST, ownersList)
            intent.putExtra(EXTRA_OWNER_MANUAL_PAYLOAD, ownerManualPayload)
            return intent
        }
    }

    private lateinit var ownersList: OwnerManualList
    private lateinit var ownerManualPayload: OwnersManualPayload

    @Inject
    lateinit var presenter: OwnersManualPresenter

    @Inject
    lateinit var applicationData: ApplicationData

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        setContentView(R.layout.activity_owner_manuals)
        val toolbar = findViewById<View>(R.id.toolbar) as Toolbar
        setSupportActionBar(toolbar)
        val recyclerView =
            findViewById<androidx.recyclerview.widget.RecyclerView>(
                R.id.owner_manual_recyclerview,
            )
        intent?.extras?.let {
            try {
                ownersList = intent.getParcelableExtra(EXTRA_OWNER_LIST)
                    ?: throw IllegalArgumentException("OwnerList is mandatory")
                ownerManualPayload = intent.getParcelableExtra(EXTRA_OWNER_MANUAL_PAYLOAD)
                    ?: throw IllegalArgumentException("OwnerManualPayload is mandatory")
            } catch (e: IllegalArgumentException) {
                LogTool.e(TAG, e.message)
                finish()
            }
        }
        val sections = ArrayList<Section>()
        if (!ownersList.ownersManual.isNullOrEmpty()) {
            sections.add(Section(ownerManualPayload.translations?.omName, ownersList.ownersManual))
        }
        if (!ownersList.audioAndNavigation.isNullOrEmpty()) {
            sections.add(
                Section(ownerManualPayload.translations?.omnavName, ownersList.audioAndNavigation),
            )
        }
        if (!ownersList.warrantiesAndMaintenance.isNullOrEmpty()) {
            sections.add(
                Section(
                    ownerManualPayload.translations?.ommsName,
                    ownersList.warrantiesAndMaintenance,
                ),
            )
        }
        if (!ownersList.ownersManualsForCanada.isNullOrEmpty()) {
            sections.add(Section(null, ownersList.ownersManualsForCanada))
        }
        recyclerView?.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this)
        recyclerView?.addItemDecoration(DividerItemDecoration(applicationContext, VERTICAL))
        val adapter = OwnerManualAdapter(this, sections)
        recyclerView?.adapter = adapter
        toolbar.title = ownerManualPayload.appTitle
    }

    override fun onOwnerManualItemClick(item: OwnerManualItem?) {
        if (item?.manualUrl != null) {
            if (ToyotaConstants.REGION_CA.equals(applicationData.getSelectedVehicle()?.region, true)) {
                openCustomChromeTab(this, item.manualUrl)
            } else {
                presenter.getOwnerManualPdfLink(item.manualUrl)
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressed()
        }
        return true
    }

    override fun createPresenter(): OwnersManualPresenter = this.presenter

    override fun sendPdfLink(pdfLink: String?) {
        if (pdfLink != null) {
            openCustomChromeTab(this, pdfLink)
        } else {
            showToast(getString(R.string.generic_error))
        }
    }

    override fun openPdfLink(pdfLink: String?) {
        if (pdfLink != null && hasNetwork(this)) {
            sendPdfLink(pdfLink)
        } else if (!hasNetwork(this)) {
            showNoNetworkToast()
        } else {
            sendPdfLink(null)
        }
    }
}
