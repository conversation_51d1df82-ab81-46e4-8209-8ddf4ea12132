package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model

import android.os.Parcelable
import com.toyota.oneapp.model.subscription.BillingAddress
import com.toyota.oneapp.model.subscriptionV2.SubscriptionPackage
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.SubscriptionV2PaymentCY17Activity
import kotlinx.parcelize.Parcelize

/**
 * A Model - Used to hold input arguments for [SubscriptionV2PaymentCY17Activity]
 */
@Parcelize
data class PaymentCY17Args(
    val vehicle: VehicleInfo,
    val subscriptionPackages: List<SubscriptionPackage>,
    val billingAddress: BillingAddress,
    val accessToken: String,
    val isAzure: Boolean,
) : Parcelable
