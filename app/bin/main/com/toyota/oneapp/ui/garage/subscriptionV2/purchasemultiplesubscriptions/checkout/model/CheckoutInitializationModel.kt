package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model

import android.os.Parcelable
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.model.subscription.TaxationItems
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.SelectedPackageInfo
import kotlinx.parcelize.Parcelize

@Parcelize
data class CheckoutInitializationModel(
    val services: List<AvailableSubscription>,
    val selectedPackages: Map<String, SelectedPackageInfo>,
    val priceInfo: PriceInfo,
    val paymentInfo: PaymentInfo,
    val consents: List<ConsentRequestItem>,
    val accessToken: String?,
) : Parcelable

@Parcelize
data class PriceInfo(
    val totalAmountWithoutTax: Double,
    val totalTaxAmount: Double,
    val totalAmount: Double,
    val taxationItems: List<TaxationItems>?,
) : Parcelable

sealed class PaymentInfo : Parcelable {
    @Parcelize
    data class CY17PaymentInfo(
        val paymentId: String,
        val paymentAccessToken: String,
    ) : PaymentInfo()

    @Parcelize
    data class CY17PlusPaymentInfo(
        val paymentId: String,
        val isDefaultPayment: Boolean,
        val paymentRecord: PaymentRecord?,
    ) : PaymentInfo()
}
