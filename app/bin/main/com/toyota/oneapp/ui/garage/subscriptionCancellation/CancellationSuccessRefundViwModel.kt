package com.toyota.oneapp.ui.garage.subscriptionCancellation

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.R
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.model.subscription.UpdatePaymentRequest
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.SubscriptionEditSuccess
import com.toyota.oneapp.ui.widget.AddressView
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class CancellationSuccessRefundViwModel
    @Inject
    constructor(
        private val subscriptionAPIManager: SubscriptionAPIManager,
    ) : BaseViewModel() {
        val state: MutableLiveData<State> = MutableLiveData()
        val address: MutableLiveData<AddressView.Address> = MutableLiveData()
        private val onSaveEvent = SingleLiveEvent<Unit>()
        val onSave: LiveData<Unit> = onSaveEvent
        var paymentRecord: PaymentRecord? = null

        fun populateUI(
            subscriptionEditSuccess: SubscriptionEditSuccess,
            paymentRecord: PaymentRecord?,
        ) {
            val newState =
                State(
                    title = subscriptionEditSuccess.titleResId,
                    description = subscriptionEditSuccess.firstLineResId,
                )
            state.postValue(newState)
            if (paymentRecord != null) {
                this.paymentRecord = paymentRecord
            }

            val address =
                object : AddressView.Address {
                    override var line1: String? = paymentRecord?.creditCardAddress1
                    override var line2: String? = null
                    override var city: String? = paymentRecord?.creditCardCity
                    override var stateCode: String? = paymentRecord?.creditCardState
                    override var zipCode: String? = paymentRecord?.creditCardPostalCode
                    override var countryCode: String? = paymentRecord?.creditCardCountry
                }
            this.address.postValue(address)
        }

        fun onContinueClicked() {
            if (paymentRecord == null) onSaveEvent.call() // not need to update the address
            paymentRecord?.let {
                subscriptionAPIManager.updatePayment(
                    it,
                    UpdatePaymentRequest(
                        addressLine1 = address.value?.line1,
                        addressLine2 = address.value?.line2,
                        city = address.value?.city,
                        state = address.value?.stateCode,
                        zipCode = address.value?.zipCode,
                        country = address.value?.countryCode,
                    ),
                    object : BaseCallback<BaseResponse>() {
                        override fun onSuccess(response: BaseResponse) {
                            showSuccessToastMessage(R.string.payment_method_updated)
                            onSaveEvent.call()
                        }

                        override fun onFailError(
                            httpCode: Int,
                            errorMsg: String?,
                        ) {
                            showErrorMessage(errorMsg)
                        }

                        override fun onComplete() {
                            hideProgress()
                        }
                    },
                )
            }
        }

        data class State(
            val title: Int,
            val description: Int,
        )
    }
