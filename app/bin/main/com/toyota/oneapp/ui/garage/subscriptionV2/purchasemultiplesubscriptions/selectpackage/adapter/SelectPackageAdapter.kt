package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.adapter

import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.style.TextAppearanceSpan
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.selection.ItemDetailsLookup
import androidx.recyclerview.selection.ItemKeyProvider
import androidx.recyclerview.selection.SelectionTracker
import androidx.recyclerview.widget.*
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemBundlePackageSelectionBinding
import com.toyota.oneapp.databinding.ItemSelectPackageBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.model.subscriptionV2.SubscriptionPackage
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.subscriptionbundle.IndividualServiceAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.model.SelectPackageUIModel
import com.toyota.oneapp.util.CurrencyUtils
import com.toyota.oneapp.util.DoubleUtil.toDisplayPrice
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter
import java.util.*

class SelectPackageAdapter(
    private val vehicleInfo: VehicleInfo,
    private val vehicleLocale: Locale,
) : ListAdapter<SelectPackageUIModel, RecyclerView.ViewHolder>(DiffUtilCallback()),
    BindableRecyclerViewAdapter<SelectPackageUIModel> {
    lateinit var selectionTracker: SelectionTracker<SelectPackageUIModel>

    companion object {
        const val ITEM_SERVICE_PACKAGE = 1
        const val ITEM_BUNDLE_PACKAGE = 2
    }

    // RecyclerView.Adapter Methods.
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder {
        return when (viewType) {
            ITEM_SERVICE_PACKAGE -> {
                val binding =
                    ItemSelectPackageBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false,
                    )
                return ServicePackageViewHolder(binding)
            }

            ITEM_BUNDLE_PACKAGE -> {
                val binding =
                    ItemBundlePackageSelectionBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false,
                    )
                return BundlePackageViewHolder(binding)
            }
            else -> {
                throw Exception("Invalid ViewType")
            }
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        val uiModel = getItem(position)
        val isSelected = selectionTracker.isSelected(uiModel)

        when (holder.itemViewType) {
            ITEM_SERVICE_PACKAGE -> {
                (holder as ServicePackageViewHolder)
                    .bind(
                        (uiModel as SelectPackageUIModel.SelectServicePackageUIModel).packageInfo,
                        isSelected,
                    )
            }

            ITEM_BUNDLE_PACKAGE -> {
                (holder as BundlePackageViewHolder)
                    .bind(uiModel as SelectPackageUIModel.SelectBundlePackageUIModel, isSelected)
            }
        }
    }

    override fun getItemViewType(position: Int): Int =
        when (getItem(position)) {
            is SelectPackageUIModel.SelectServicePackageUIModel -> ITEM_SERVICE_PACKAGE
            is SelectPackageUIModel.SelectBundlePackageUIModel -> ITEM_BUNDLE_PACKAGE
        }.exhaustive

    // BindableRecyclerViewAdapter Methods.
    override fun setData(data: List<SelectPackageUIModel>?) {
        if (data != null) {
            submitList(data)
        }
    }

    inner class BundlePackageViewHolder(
        private val binding: ItemBundlePackageSelectionBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        fun bind(
            uiModel: SelectPackageUIModel.SelectBundlePackageUIModel,
            isSelected: Boolean,
        ) {
            val individualServiceAdapter =
                IndividualServiceAdapter(
                    vehicleInfo,
                    uiModel.bundleComponents,
                )
            binding.rvBundleServices.run {
                layoutManager = LinearLayoutManager(itemView.context)
                adapter = individualServiceAdapter
                addItemDecoration(DividerItemDecoration(itemView.context, LinearLayout.VERTICAL))
            }
            binding.uiModel = uiModel
            itemView.isActivated = isSelected
            binding.itemBundleCheckbox.isChecked = isSelected
        }

        fun getItemDetails(): ItemDetailsLookup.ItemDetails<SelectPackageUIModel> =
            object : ItemDetailsLookup.ItemDetails<SelectPackageUIModel>() {
                override fun getPosition(): Int = adapterPosition

                override fun getSelectionKey(): SelectPackageUIModel? = getItem(adapterPosition)

                override fun inSelectionHotspot(e: MotionEvent): Boolean = true
            }
    }

    // View Holder.
    inner class ServicePackageViewHolder(
        private val binding: ItemSelectPackageBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        fun bind(
            pkg: SubscriptionPackage,
            isSelected: Boolean,
        ) {
            with(pkg) {
                binding.tvTitle.text = displaySubscriptionTerm
                val displayPrice = price?.toDisplayPrice(vehicleLocale) ?: ""
                binding.tvPrice.text =
                    formatDisplayPrice(
                        displayPrice,
                        currency
                            ?: ToyotaConstants.US_CURRENCY_CODE,
                    )
                val displayTermUnitStringId =
                    SubscriptionUtil.getDisplayTermUnit(subscriptionTerm ?: "")
                val tvTerm = itemView.context.getString(displayTermUnitStringId)
                binding.tvTerm.text = String.format("/%s", tvTerm)
            }
            itemView.isActivated = isSelected
            if (isSelected) {
                binding.cvCard.strokeWidth =
                    itemView.context.resources
                        .getDimension(R.dimen.size_4dp)
                        .toInt()
            } else {
                binding.cvCard.strokeWidth =
                    itemView.context.resources
                        .getDimension(R.dimen.size_1dp)
                        .toInt()
            }
        }

        // Format DisplayPrice - Change TextSize & color for Currency.
        private fun formatDisplayPrice(
            displayPrice: String,
            currencyCode: String,
        ): SpannableString {
            val displayPriceSpannable = SpannableString(displayPrice)
            val currencySymbol = CurrencyUtils.getCurrencySymbol(currencyCode)
            val startIndex = displayPrice.indexOf(currencySymbol)
            if (startIndex != -1) {
                val endIndex = startIndex + currencySymbol.length
                displayPriceSpannable.setSpan(
                    TextAppearanceSpan(itemView.context, com.toyota.one_ui.R.style.TextAppearance_OneUi_Callout),
                    startIndex,
                    endIndex,
                    Spannable.SPAN_INCLUSIVE_EXCLUSIVE,
                )
                displayPriceSpannable.setSpan(
                    ForegroundColorSpan(
                        ContextCompat.getColor(itemView.context, R.color.coloradadb1),
                    ),
                    startIndex,
                    endIndex,
                    Spannable.SPAN_INCLUSIVE_EXCLUSIVE,
                )
            }
            return displayPriceSpannable
        }

        // ItemDetails
        fun getItemDetails(): ItemDetailsLookup.ItemDetails<SelectPackageUIModel> =
            object : ItemDetailsLookup.ItemDetails<SelectPackageUIModel>() {
                override fun getPosition(): Int = adapterPosition

                override fun getSelectionKey(): SelectPackageUIModel? = getItem(adapterPosition)

                override fun inSelectionHotspot(e: MotionEvent): Boolean = true
            }
    }

    // Diff Util.
    class DiffUtilCallback : DiffUtil.ItemCallback<SelectPackageUIModel>() {
        override fun areItemsTheSame(
            oldItem: SelectPackageUIModel,
            newItem: SelectPackageUIModel,
        ): Boolean {
            if (oldItem is SelectPackageUIModel.SelectServicePackageUIModel &&
                newItem is SelectPackageUIModel.SelectServicePackageUIModel
            ) {
                return (newItem.packageInfo.packageID == oldItem.packageInfo.packageID)
            } else if (oldItem is SelectPackageUIModel.SelectBundlePackageUIModel &&
                newItem is SelectPackageUIModel.SelectBundlePackageUIModel
            ) {
                return (newItem.packageInfo.packageID == oldItem.packageInfo.packageID)
            }

            return false
        }

        override fun areContentsTheSame(
            oldItem: SelectPackageUIModel,
            newItem: SelectPackageUIModel,
        ): Boolean = newItem == oldItem
    }
}

// Item Details Lookup
class SelectPackageItemDetailsLookup(
    private val recyclerView: RecyclerView,
) : ItemDetailsLookup<SelectPackageUIModel>() {
    override fun getItemDetails(event: MotionEvent): ItemDetails<SelectPackageUIModel>? {
        val view = recyclerView.findChildViewUnder(event.x, event.y)
        if (view != null) {
            if (recyclerView.getChildViewHolder(view) is SelectPackageAdapter.ServicePackageViewHolder) {
                return (recyclerView.getChildViewHolder(view) as SelectPackageAdapter.ServicePackageViewHolder)
                    .getItemDetails()
            } else if (recyclerView.getChildViewHolder(view) is SelectPackageAdapter.BundlePackageViewHolder) {
                return (recyclerView.getChildViewHolder(view) as SelectPackageAdapter.BundlePackageViewHolder)
                    .getItemDetails()
            }
        }
        return null
    }
}

// Item Key Provider
class SelectPackageKeyProvider(
    private val adapter: SelectPackageAdapter,
) : ItemKeyProvider<SelectPackageUIModel>(SCOPE_CACHED) {
    override fun getKey(position: Int): SelectPackageUIModel? = adapter.currentList[position]

    override fun getPosition(key: SelectPackageUIModel): Int = adapter.currentList.indexOfFirst { it == key }
}
