package com.toyota.oneapp.ui.garage.subscriptionCancellation

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.language.CurrentCurrencyFormat
import com.toyota.oneapp.model.subscription.CancelSubscriptionResponse
import com.toyota.oneapp.model.subscription.RefundResponse.Companion.MANUALCHECK
import com.toyota.oneapp.model.subscription.SubscriptionUIItem
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.callback.CancelSubscriptionStatus
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.EditSubscriptionData
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.BindableSpinnerAdapter
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import java.text.NumberFormat
import java.util.Date
import javax.inject.Inject
import kotlin.collections.ArrayList

@HiltViewModel
class CancelSubscriptionViewModel
    @Inject
    constructor(
        @CurrentCurrencyFormat private val currencyFormatter: NumberFormat,
        private val subscriptionApiManager: SubscriptionAPIManager,
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : BaseViewModel() {
        val state: MutableLiveData<State> = MutableLiveData()
        val apiCallStatus: MutableLiveData<CancelSubscriptionStatus> = MutableLiveData()
        val cancellationReason: MutableLiveData<BindableSpinnerAdapter.SpinnerItem> = MutableLiveData()
        val cancellationReasons: MutableLiveData<List<BindableSpinnerAdapter.SpinnerItem>> = MutableLiveData()
        val cancelSubscriptionNavigationEvents = SingleLiveEvent<CancelSubscriptionNavigationEvent>()

        val cancellationReasonChanged: LiveData<String> =
            cancellationReason.map {
                cancellationReasonCode = it.value
                it.text
            }

        private lateinit var subscriptionItem: SubscriptionUIItem
        lateinit var editSubscriptionData: EditSubscriptionData

        var customerEmail: String =
            oneAppPreferenceModel
                .getAccountInfoSubscriber()
                ?.customerEmails
                ?.get(
                    0,
                )?.emailAddress
                ?: ""

        private var cancellationReasonCode: String = ""

        fun populateView(
            editSubscriptionData: EditSubscriptionData,
            defaultReasons: SubscriptionCancellationReasons,
        ) {
            this.editSubscriptionData = editSubscriptionData
            subscriptionItem = editSubscriptionData.subscriptionItem
                ?: throw IllegalArgumentException(
                    "You should not start the CancelSubscriptionActivity with a null SubscriptionItem object.",
                )
            val creditMemos =
                editSubscriptionData.cancellationDataPayload?.previewResult?.creditMemos?.get(
                    0,
                )

            populateSpinner(defaultReasons)

            val isTotalVisible = creditMemos?.creditMemoItems?.get(0)?.refundEligibilityStatus ?: false
            val subtotal = creditMemos?.amount ?: 0
            val estimatedTax = creditMemos?.taxAmount ?: 0
            val refund = creditMemos?.amountWithoutTax ?: 0
            val endDate = creditMemos?.targetDate ?: ""
            val isCancelDescriptionVisible =
                !isTotalVisible &&
                    Date().after(
                        creditMemos?.creditMemoItems?.get(0)?.serviceStartDate,
                    )
            val cancelDescription =
                if (ToyotaConstants.MTH.equals(
                        subscriptionItem.previewSubscriptionItem.subscriptionTerm,
                        true,
                    )
                ) {
                    R.string.RefundPreview_message_cancel_no_refund_due_monthly
                } else {
                    R.string.RefundPreview_message_cancel_no_refund_due_yearly
                }

            val newState =
                State(
                    title = editSubscriptionData.title,
                    description = editSubscriptionData.description,
                    isTotalVisible = isTotalVisible,
                    isCancelDescriptionVisible = isCancelDescriptionVisible,
                    cancelDescription = cancelDescription,
                    subtotal = currencyFormatter.format(subtotal),
                    tax = currencyFormatter.format(estimatedTax),
                    refund = currencyFormatter.format(refund),
                    endDate = endDate,
                )
            state.postValue(newState)
        }

        private fun populateSpinner(defaultReasons: SubscriptionCancellationReasons) {
            cancellationReasons.postValue(defaultReasons.spinnerItems)
        }

        fun onCancelSubscriptionClicked() {
            cancelSubscriptionNavigationEvents.postValue(
                CancelSubscriptionNavigationEvent.OnCancelSubscriptionClicked(
                    editSubscriptionData.title,
                ),
            )
        }

        fun onCancelSubscription() {
            val subscriptionIds = ArrayList<String>()
            subscriptionIds.add(subscriptionItem.id)

            if (!subscriptionItem.previewSubscriptionItem?.consolidatedProductIds.isNullOrEmpty()) {
                subscriptionItem.previewSubscriptionItem?.consolidatedProductIds?.map {
                    subscriptionIds.add(it)
                }
            }

            if (!subscriptionItem.previewSubscriptionItem?.consolidatedGoodwillIds.isNullOrEmpty()) {
                subscriptionItem.previewSubscriptionItem?.consolidatedGoodwillIds?.map {
                    subscriptionIds.add(it)
                }
            }

            showProgress()

            subscriptionApiManager.sendCancelSubscription(
                applicationData.getSelectedVehicle(),
                subscriptionIds,
                emptyList(),
                cancellationReasonCode,
                editSubscriptionData.cancellationDataPayload?.records,
                object : BaseCallback<CancelSubscriptionResponse>() {
                    override fun onSuccess(response: CancelSubscriptionResponse) {
                        analyticsLogger.logEvent(AnalyticsEvent.CANCEL_PAID_SUBSCRIPTION_SUCCESS)
                        showSuccessToastMessage(R.string.Cancellation_Request_In_Progress_note)
                        when (MANUALCHECK) {
                            response.payload?.refundResponse?.type ->
                                apiCallStatus.postValue(
                                    CancelSubscriptionStatus(
                                        isManualRefund = true,
                                        isRefundAvailable = state.value?.isTotalVisible == true,
                                    ),
                                )
                            else -> {
                                analyticsLogger.logEvent(
                                    AnalyticsEvent.CANCEL_PAID_SUBSCRIPTION_MANUAL_REFUND,
                                    Pair("reason", cancellationReasonCode),
                                )
                                apiCallStatus.postValue(
                                    CancelSubscriptionStatus(
                                        isManualRefund = false,
                                        isRefundAvailable = state.value?.isTotalVisible == true,
                                    ),
                                )
                            }
                        }
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        analyticsLogger.logEvent(AnalyticsEvent.CANCEL_PAID_SUBSCRIPTION_FAILED)
                        apiCallStatus.postValue(CancelSubscriptionStatus(errorMsg))
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        data class State(
            val title: String,
            val description: String,
            val isTotalVisible: Boolean,
            val isCancelDescriptionVisible: Boolean,
            val cancelDescription: Int,
            val subtotal: String,
            val tax: String,
            val refund: String,
            val endDate: String,
        )
    } // CancelSubscriptionViewModel class

sealed class CancelSubscriptionNavigationEvent {
    data class OnCancelSubscriptionClicked(
        val subscriptionName: String,
    ) : CancelSubscriptionNavigationEvent()
}
