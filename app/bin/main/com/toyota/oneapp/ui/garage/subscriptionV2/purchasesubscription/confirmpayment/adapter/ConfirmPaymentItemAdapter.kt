package com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemConfirmPaymentBinding
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasesubscription.confirmpayment.model.ConfirmPaymentItemModel
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

/**
 * A Adapter - used to populate Confirm Payment Items.
 */
class ConfirmPaymentItemAdapter(
    private var items: List<ConfirmPaymentItemModel>,
) : RecyclerView.Adapter<ConfirmPaymentItemAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<ConfirmPaymentItemModel> {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding =
            ItemConfirmPaymentBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        val item = items[position]
        holder.bind(item)
    }

    override fun getItemCount(): Int = items.size

    // BindableRecyclerViewAdapter Methods.
    override fun setData(data: List<ConfirmPaymentItemModel>?) {
        this.items = data ?: emptyList()
        notifyDataSetChanged()
    }

    inner class ViewHolder(
        private val binding: ItemConfirmPaymentBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        fun bind(item: ConfirmPaymentItemModel) {
            binding.item = item
            if (!item.subTitle.isNullOrEmpty()) {
                binding.tvSubtitle.visibility = View.VISIBLE
            } else {
                binding.tvSubtitle.visibility = View.GONE
            }
        }
    }
}
