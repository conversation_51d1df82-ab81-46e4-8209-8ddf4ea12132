package com.toyota.oneapp.ui.garage.subscriptionV2.common.servicedetail.mapper

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.subscriptionV2.productDescription
import com.toyota.oneapp.ui.garage.subscriptionV2.common.servicedetail.model.ServiceDetailUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import javax.inject.Inject

class SubscriptionToServiceDetailUIModelMapper
    @Inject
    constructor(
        private val applicationData: ApplicationData,
    ) {
        fun map(subscription: SubscriptionV2): ServiceDetailUIModel {
            val wifiCarrierIcon =
                if (subscription.externalProduct) {
                    SubscriptionUtil.getWifiCarrierIcon(applicationData.getSelectedVehicle())
                } else {
                    null
                }

            val uiModel =
                ServiceDetailUIModel(
                    productName = subscription.displayProductName,
                    productDescription = subscription.productDescription(),
                    productImageUrl = subscription.productImageUrl,
                    canShowBundleComponents = subscription.category == "BUNDLE",
                    bundleComponents = subscription.components,
                    wifiCarrierIcon = wifiCarrierIcon,
                )

            return uiModel
        }
    }
