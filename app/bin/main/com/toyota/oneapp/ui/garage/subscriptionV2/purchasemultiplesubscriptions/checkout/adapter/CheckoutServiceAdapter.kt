package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemCheckoutBundleBinding
import com.toyota.oneapp.databinding.ItemCheckoutServiceBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.subscriptionbundle.IndividualServiceAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutItemUIModel
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

class CheckoutServiceAdapter(
    private val vehicleInfo: VehicleInfo,
    private val listener: CheckoutServiceAdapterListener,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>(),
    BindableRecyclerViewAdapter<CheckoutItemUIModel> {
    private var items: List<CheckoutItemUIModel> = emptyList()

    companion object {
        const val ITEM_CHECKOUT_SERVICE = 1
        const val ITEM_CHECKOUT_BUNDLE = 2
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder {
        when (viewType) {
            ITEM_CHECKOUT_SERVICE -> {
                val binding =
                    ItemCheckoutServiceBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false,
                    )
                return CheckoutServiceViewHolder(binding)
            }

            ITEM_CHECKOUT_BUNDLE -> {
                val binding =
                    ItemCheckoutBundleBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false,
                    )
                return CheckoutBundleViewHolder(binding)
            }

            else -> {
                throw Exception("Invalid ViewType")
            }
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        when (holder.itemViewType) {
            ITEM_CHECKOUT_SERVICE -> {
                val checkoutServiceVH = holder as CheckoutServiceViewHolder
                val checkoutService = items[position] as CheckoutItemUIModel.CheckoutServiceListItemUIModel
                checkoutServiceVH.bind(checkoutService)
            }

            ITEM_CHECKOUT_BUNDLE -> {
                val checkoutBundleVH = holder as CheckoutBundleViewHolder
                val checkoutBundle = items[position] as CheckoutItemUIModel.CheckoutBundleListItemUIModel
                checkoutBundleVH.bind(checkoutBundle)
            }
        }
    }

    override fun getItemViewType(position: Int): Int =
        when (items[position]) {
            is CheckoutItemUIModel.CheckoutServiceListItemUIModel -> ITEM_CHECKOUT_SERVICE
            is CheckoutItemUIModel.CheckoutBundleListItemUIModel -> ITEM_CHECKOUT_BUNDLE
        }.exhaustive

    override fun getItemCount(): Int = items.size

    // BindableRecyclerViewAdapter Methods
    @SuppressLint("NotifyDataSetChanged")
    override fun setData(data: List<CheckoutItemUIModel>?) {
        items = data ?: emptyList()
        notifyDataSetChanged()
    }

    inner class CheckoutServiceViewHolder(
        private val binding: ItemCheckoutServiceBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        init {
            binding.scAutoRenew.setOnClickListener {
                val isChecked = binding.scAutoRenew.isChecked
                val service = items[adapterPosition] as CheckoutItemUIModel.CheckoutServiceListItemUIModel
                listener.onServiceAutoRenewToggled(service, isChecked)
            }
        }

        fun bind(service: CheckoutItemUIModel.CheckoutServiceListItemUIModel) {
            binding.tvTitle.text = service.title
            binding.tvSubTitle.text = service.subTitle
            binding.tvAutoRenew.isVisible = service.isRenewable
            binding.scAutoRenew.isChecked = service.isAutoRenew
            binding.scAutoRenew.isVisible = service.isRenewable
            if (adapterPosition == items.size - 1) {
                binding.divider.visibility = View.GONE
            }
        }
    }

    inner class CheckoutBundleViewHolder(
        private val binding: ItemCheckoutBundleBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        init {
            binding.autoRenewCheckbox.setOnClickListener {
                val isChecked = binding.autoRenewCheckbox.isChecked
                val bundle = items[adapterPosition] as CheckoutItemUIModel.CheckoutBundleListItemUIModel
                listener.onBundleAutoRenewToggled(bundle, isChecked)
            }
        }

        fun bind(bundle: CheckoutItemUIModel.CheckoutBundleListItemUIModel) {
            binding.tvBundleTitle.text = bundle.title
            binding.tvSubscriptionAmount.text = bundle.subscriptionPrice
            binding.tvSubscriptionTerm.text = bundle.subscriptionTerm
            binding.autoRenewCheckbox.isChecked = bundle.isAutoRenew
            binding.autoRenewCheckbox.isVisible = bundle.isRenewable
            val individualServiceAdapter =
                IndividualServiceAdapter(
                    vehicleInfo,
                    bundle.bundleComponents,
                )
            binding.rvBundleServices.run {
                layoutManager = LinearLayoutManager(itemView.context)
                adapter = individualServiceAdapter
                addItemDecoration(DividerItemDecoration(itemView.context, LinearLayout.VERTICAL))
            }
        }
    }

    interface CheckoutServiceAdapterListener {
        fun onServiceAutoRenewToggled(
            service: CheckoutItemUIModel.CheckoutServiceListItemUIModel,
            autoRenew: Boolean,
        )

        fun onBundleAutoRenewToggled(
            bundle: CheckoutItemUIModel.CheckoutBundleListItemUIModel,
            autoRenew: Boolean,
        )
    }
}
