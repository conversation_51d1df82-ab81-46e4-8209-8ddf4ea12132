package com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.mapper

import android.content.Context
import com.toyota.oneapp.R
import com.toyota.oneapp.model.subscriptionV2.VehicleSubscriptionPayload
import com.toyota.oneapp.model.subscriptionV2.isActive
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.model.SubscriptionListItemUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.model.SubscriptionUIModel
import kotlinx.coroutines.withContext
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

/**
 * A Class used to map subscription API to UI model.
 */
class SubscriptionListUIMapper
    @Inject
    constructor(
        private val context: Context,
        private val dispatcherProvider: DispatcherProvider,
    ) {
        suspend fun map(
            vehicle: VehicleInfo,
            payload: VehicleSubscriptionPayload,
        ): SubscriptionUIModel {
            return withContext(dispatcherProvider.default()) {
                val iconFillColor = if (vehicle.isToyotaBrand) R.color.toyota_brand_color else R.color.lexus_brand_color

                // For Remote Authorised User - Can't do any action from subscription screen.
                val isActionable = !vehicle.isRemoteOnlyUser

                val items = mutableListOf<SubscriptionListItemUIModel>()

                // Trial Subscription
                val trialSubscriptions = payload.trialSubscriptions
                if (trialSubscriptions.isNotEmpty()) {
                    items +=
                        SubscriptionListItemUIModel.Header(
                            context.getString(R.string.trial_services),
                        )
                    for (subscription in trialSubscriptions) {
                        val PPOSubTitle =
                            if (subscription.isPPOProduct == true) {
                                context.getString(R.string.extended_trial) + " "
                            } else {
                                ""
                            }
                        val subTitle =
                            if (subscription.isActive()) {
                                "${PPOSubTitle}${context.getString(R.string.active)}\n${subscription.displayTerm}"
                            } else {
                                "${context.getString(R.string.trial_available)}\n${subscription.displayTerm}"
                            }

                        items +=
                            SubscriptionListItemUIModel.Service.TrialService(
                                _title = subscription.displayProductName,
                                _subTitle = subTitle,
                                _isHideSubTitle = subscription.hideSubscriptionStatus ?: false,
                                _icon =
                                    SubscriptionUtil.getIconForSubscriptionFromProductLine(
                                        subscription.productLine ?: "",
                                    ),
                                _iconFillColor = iconFillColor,
                                _isActionable = isActionable,
                                subscription = subscription,
                            )
                    }
                }

                // Paid Subscription
                val paidSubscriptions = payload.paidSubscriptions
                if (paidSubscriptions.isNotEmpty()) {
                    items +=
                        SubscriptionListItemUIModel.Header(
                            context.getString(R.string.paid_services),
                        )
                    for (subscription in paidSubscriptions) {
                        var subTitle = ""
                        // Add AutoRenew info. - Only to CY17PLUS vehicle & Subscription should be renewable.
                        // For external subscriptions - Don't add AutoRenew info.
                        if (!vehicle.isCY17 && subscription.renewable && !subscription.externalProduct) {
                            subTitle =
                                if (subscription.autoRenew == true) {
                                    context.getString(R.string.Subscription_Auto_Renew_On)
                                } else {
                                    context.getString(R.string.Subscription_Auto_Renew_Off)
                                }
                            subTitle += "\n"
                        }
                        subTitle += subscription.displayTerm

                        items +=
                            SubscriptionListItemUIModel.Service.MyPaidService(
                                _title = subscription.displayProductName,
                                _subTitle = subTitle,
                                _isHideSubTitle = subscription.hideSubscriptionStatus ?: false,
                                _icon =
                                    SubscriptionUtil.getIconForSubscriptionFromProductLine(
                                        subscription.productLine ?: "",
                                    ),
                                _iconFillColor = iconFillColor,
                                _isActionable = isActionable,
                                subscription = subscription,
                            )
                    }
                }

                val vehicleName = "${vehicle.modelYear} ${vehicle.modelName}"

                // Ignore External Products.
                val isAllTrialServicesActive = trialSubscriptions.filter { !it.externalProduct }.all { it.isActive() }
                val isActionBtnVisible: Boolean
                val actionTxt: String
                if (isAllTrialServicesActive) {
                    actionTxt = context.getString(R.string.add_service)
                    isActionBtnVisible = vehicle.isFeatureEnabled(Feature.PAID_SUBSCRIPTION) && payload.availableSubscriptions.isNotEmpty()
                } else {
                    actionTxt = context.getString(R.string.enable_all_trials)
                    isActionBtnVisible = isActionable
                }

                val uiModel =
                    SubscriptionUIModel(
                        vehicleName = vehicleName,
                        items = items,
                        showPPODisclaimer = payload.isPPOEligible ?: false,
                        PPODisclaimer = payload.ppoDisclaimer.orEmpty(),
                        showEmptyView = items.isEmpty(),
                        isActionBtnVisible = isActionBtnVisible,
                        actionTxt = actionTxt,
                    )

                return@withContext uiModel
            }
        }
    }
