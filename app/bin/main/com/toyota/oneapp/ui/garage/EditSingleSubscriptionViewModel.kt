package com.toyota.oneapp.ui.garage

import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.subscription.*
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.callback.CancelSubscriptionStatus
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.ValueTracker
import dagger.hilt.android.lifecycle.HiltViewModel
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import kotlin.collections.ArrayList

private const val DATE_FORMAT_FOR_CANCEL_DATA_REQUEST = "yyyy-MM-dd"

@HiltViewModel
class EditSingleSubscriptionViewModel
    @Inject
    constructor(
        private val subscriptionApiManager: SubscriptionAPIManager,
        private val applicationData: ApplicationData,
        private val preferenceModel: OneAppPreferenceModel,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        val state: MutableLiveData<State> = MutableLiveData()
        val subscriptionAutoRenewOn: MutableLiveData<Int> = MutableLiveData()
        val subscriptionChanged: MutableLiveData<Boolean> = MutableLiveData()
        val onCancelSubscriptionClick: MutableLiveData<EditSubscriptionData> = MutableLiveData()
        val apiCallStatus: MutableLiveData<Status> = MutableLiveData()

        private lateinit var subscriptionItem: SubscriptionUIItem
        private lateinit var autoRenewValueTracker: ValueTracker<Boolean>

        fun populateSubscriptionInfo(editSubscriptionData: EditSubscriptionData) {
            subscriptionItem = editSubscriptionData.subscriptionItem ?: return
            autoRenewValueTracker =
                ValueTracker(
                    subscriptionItem.previewSubscriptionItem?.isAutoRenew ?: false,
                )

            applicationData.getSelectedVehicle()?.let {
                val newState =
                    State(
                        title = editSubscriptionData.title,
                        description = editSubscriptionData.description,
                        autoRenewEnabled = subscriptionItem.previewSubscriptionItem?.isRenewable ?: false,
                        futureCancel =
                            ToyotaConstants.TRUE.equals(
                                subscriptionItem.previewSubscriptionItem?.futureCancel,
                                ignoreCase = true,
                            ),
                        isOnlyRemoteUser = it.isRemoteOnlyUser,
                    )

                state.postValue(newState)
            }
            postAutoRenew(autoRenewValueTracker.currentValue)
            subscriptionChanged.postValue(false)
        }

        private fun postAutoRenew(newAutoRenewValue: Boolean) {
            subscriptionAutoRenewOn.postValue(
                if (newAutoRenewValue) {
                    R.drawable.ic_checkbox_selected
                } else {
                    R.drawable.ic_checkbox_unselected
                },
            )
        }

        fun onAutoRenewClick() {
            autoRenewValueTracker.currentValue = !autoRenewValueTracker.currentValue
            postAutoRenew(autoRenewValueTracker.currentValue)
            subscriptionChanged.postValue(autoRenewValueTracker.valueHasChanged)
        }

        fun onUpdateSubscription() {
            val subscriptionList = ArrayList<AutoRenew>()
            subscriptionList.add(AutoRenew(subscriptionItem.id, autoRenewValueTracker.currentValue))

            val autoRenewSubscriptionRequest =
                applicationData.getSelectedVehicle()?.let {
                    AutoRenewSubscriptionRequest(
                        vin = it.vin,
                        subscriberGuid = preferenceModel.getGuid(),
                        generation = it.generation,
                        subscriptions = subscriptionList,
                    )
                }

            showProgress()
            subscriptionApiManager.autoRenewSubscription(
                applicationData.getSelectedVehicle()?.brand,
                autoRenewSubscriptionRequest,
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        analyticsLogger.logEvent(
                            AnalyticsEvent.PAYMENT_SUBSCRIPTION_UPDATE_AUTORENEW_SUCCESS,
                        )
                        subscriptionItem.previewSubscriptionItem?.isAutoRenew = autoRenewValueTracker.currentValue
                        apiCallStatus.postValue(Status(autoRenewValueTracker.currentValue))
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        analyticsLogger.logEvent(
                            AnalyticsEvent.PAYMENT_SUBSCRIPTION_UPDATE_AUTORENEW_FAILED,
                        )
                        apiCallStatus.postValue(Status(errorMsg ?: ""))
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun onCancelSubscription() {
            val subscriptionList = ArrayList<String>()
            subscriptionList.add(subscriptionItem.id)
            subscriptionList.addAll(subscriptionItem.previewSubscriptionItem.consolidatedProductIds)

            val cancellationDataRequest =
                applicationData.getSelectedVehicle()?.let {
                    CancellationDataRequest(
                        orderDate =
                            SimpleDateFormat(DATE_FORMAT_FOR_CANCEL_DATA_REQUEST, Locale.ENGLISH).format(
                                Date(),
                            ),
                        guid = preferenceModel.getGuid(),
                        vin = it.vin,
                        subscriptionsIds = subscriptionList,
                    )
                }

            showProgress()
            subscriptionApiManager.sendGetRefundPreview(
                cancellationDataRequest,
                object : BaseCallback<CancellationDataResponse>() {
                    override fun onSuccess(response: CancellationDataResponse) {
                        onCancelSubscriptionClick.postValue(
                            EditSubscriptionData(subscriptionItem, response.payload),
                        )
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        apiCallStatus.postValue(Status(errorMsg ?: ""))
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        data class State(
            val title: String,
            val description: String?,
            val autoRenewEnabled: Boolean,
            val futureCancel: Boolean,
            val isOnlyRemoteUser: Boolean,
        )

        class Status private constructor(
            isSuccess: Boolean,
            errorMsg: String,
            val newAutoRenewValue: Boolean,
        ) : CancelSubscriptionStatus(
                isSuccess,
                false,
                false,
                errorMsg,
            ) {
            constructor(newAutoRenewValue: Boolean) : this(true, "", newAutoRenewValue)

            constructor(errorMsg: String) : this(false, errorMsg, false)
        }
    }
