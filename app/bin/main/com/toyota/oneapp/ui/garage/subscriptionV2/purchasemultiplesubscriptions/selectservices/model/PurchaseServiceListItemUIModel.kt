package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model

import com.toyota.oneapp.model.subscriptionV2.BundleComponent

sealed class PurchaseServiceListItemUIModel {
    data class Header(
        val title: String,
    ) : PurchaseServiceListItemUIModel()

    data class Service(
        val title: String,
        val subTitle: String?,
        val isSelected: Boolean,
        val productLine: String,
        val selectedPackageInfo: SelectedPackageInfo?,
    ) : PurchaseServiceListItemUIModel()

    data class ExternalService(
        val title: String,
        val provider: String?,
        val productLine: String,
    ) : PurchaseServiceListItemUIModel()

    data class SubscriptionBundle(
        val isBundleSelected: Boolean,
        val productLine: String,
        val title: String,
        val subscriptionAmount: String?,
        val subscriptionTerm: String,
        val services: List<BundleComponent>,
        val selectedBundleInfo: SelectedPackageInfo?,
    ) : PurchaseServiceListItemUIModel()
}
