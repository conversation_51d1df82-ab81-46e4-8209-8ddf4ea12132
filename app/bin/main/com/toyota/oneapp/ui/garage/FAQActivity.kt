package com.toyota.oneapp.ui.garage

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.appcompat.widget.Toolbar
import com.toyota.oneapp.R
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.MVPBaseActivity
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.mvpcore.EXMVPBasePresenter

@AndroidEntryPoint
class FAQActivity : MVPBaseActivity<EXMVPBasePresenter<*>>() {
    companion object {
        private const val EXTRA_VEHICLE = "EXTRA_VEHICLE"

        fun getIntent(
            context: Context,
            vehicle: VehicleInfo,
        ): Intent {
            val intent = Intent(context, FAQActivity::class.java)
            intent.putExtra(EXTRA_VEHICLE, vehicle)
            return intent
        }
    }

    private var faqWebview: WebView? = null
    private lateinit var vehicle: VehicleInfo

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        setContentView(R.layout.activity_faq)

        val toolbar = findViewById<View>(R.id.toolbar) as Toolbar
        setSupportActionBar(toolbar)

        faqWebview = findViewById<WebView>(R.id.faq_webview)
        showProgressDialog()
        faqWebview?.settings?.javaScriptEnabled = true
        faqWebview?.settings?.setSupportZoom(true)
        faqWebview?.settings?.domStorageEnabled = true
        faqWebview?.settings?.pluginState = WebSettings.PluginState.ON
        faqWebview?.settings?.cacheMode = WebSettings.LOAD_NO_CACHE

        faqWebview?.webViewClient =
            object : WebViewClient() {
                override fun shouldOverrideUrlLoading(
                    view: WebView,
                    url: String,
                ): Boolean {
                    view.loadUrl(url)
                    return true
                }

                override fun onPageFinished(
                    view: WebView?,
                    url: String?,
                ) {
                    hideProgressDialog()
                }
            }

        vehicle = intent.getParcelableExtra(EXTRA_VEHICLE) ?: throw IllegalArgumentException(
            "Vehicle can't be NULL",
        )
        faqWebview?.loadUrl(vehicle.faqUrl)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressed()
            else -> {
            }
        }
        return true
    }

    override fun createPresenter(): EXMVPBasePresenter<*>? = null
}
