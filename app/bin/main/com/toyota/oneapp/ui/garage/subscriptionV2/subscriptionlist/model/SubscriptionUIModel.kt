package com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.model

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2

/**
 * A Class used to populate Subscription list screen.
 */
data class SubscriptionUIModel(
    val vehicleName: String,
    val items: List<SubscriptionListItemUIModel>,
    val showPPODisclaimer: <PERSON>olean,
    val PPODisclaimer: String,
    val showEmptyView: Boolean,
    val isActionBtnVisible: Boolean,
    val actionTxt: String,
)

sealed class SubscriptionListItemUIModel {
    data class Header(
        val title: String,
    ) : SubscriptionListItemUIModel()

    sealed class Service(
        val title: String,
        val subTitle: String,
        val isHideSubTitle: Boolean,
        @DrawableRes val icon: Int,
        @ColorRes val iconFillColor: Int,
        val isActionable: Boolean,
    ) : SubscriptionListItemUIModel() {
        data class TrialService(
            private val _title: String,
            private val _subTitle: String,
            private val _isHideSubTitle: Boolean,
            @DrawableRes private val _icon: Int,
            @ColorRes private val _iconFillColor: Int,
            private val _isActionable: Boolean,
            val subscription: SubscriptionV2,
        ) : Service(
                _title,
                _subTitle,
                _isHideSubTitle,
                _icon,
                _iconFillColor,
                _isActionable,
            )

        data class MyPaidService(
            private val _title: String,
            private val _subTitle: String,
            private val _isHideSubTitle: Boolean,
            @DrawableRes private val _icon: Int,
            @ColorRes private val _iconFillColor: Int,
            private val _isActionable: Boolean,
            val subscription: SubscriptionV2,
        ) : Service(
                _title,
                _subTitle,
                _isHideSubTitle,
                _icon,
                _iconFillColor,
                _isActionable,
            )
    }
}
