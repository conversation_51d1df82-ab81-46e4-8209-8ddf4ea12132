package com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.manage.model

import androidx.annotation.StringRes
import com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.manage.ManagePaidSubscriptionFragment

/**
 * A UI Model - Used to populate [ManagePaidSubscriptionFragment]
 */
data class ManagePaidSubscriptionUIModel(
    val showBundleCard: <PERSON>olean,
    val screenTitle: String,
    val displayProductName: String,
    val productLongDesc: String,
    val isAutoRenewVisible: Boolean,
    val futureCancel: <PERSON>olean,
    val autoRenewDisclaimer: String,
    val displayTerms: String,
    @StringRes val autoRenewStatus: Int,
    @StringRes val updateActionTxt: Int,
    @StringRes val cancelActionTxt: Int,
)
