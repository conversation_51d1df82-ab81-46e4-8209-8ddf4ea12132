package com.toyota.oneapp.ui.garage.vehicleUpdate21mm

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.databinding.FragmentVehicleSoftwareUpdateSuccessBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.util.IntentUtil
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VehicleSoftware21MMSuccessFragment : BaseDataBindingFragment<FragmentVehicleSoftwareUpdateSuccessBinding>() {
    private val viewModel: VehicleSoftwareUpdateViewModel by activityViewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentVehicleSoftwareUpdateSuccessBinding,
        savedInstance: Bundle?,
    ) {
        binding.viewModel = viewModel
        binding.executePendingBindings()

        viewModel.softwareVersionUpdateSuccess(true)
        viewModel.logAnalyticEvent(
            AnalyticsEventParam.VEHICLE_SOFTWARE_OTA,
            AnalyticsEvent.OTA21MM_AGREE_UPDATE.eventName,
        )

        binding.goToDashboardBtn.setOnClickListener {
            activity?.let {
                startActivity(
                    IntentUtil.getOADashBoardIntent(
                        context = activityContext,
                        isDashboardRefresh = true,
                    ),
                )
                finishActivity()
            }
        }
    }

    override fun getLayout(): Int = R.layout.fragment_vehicle_software_update_success
}
