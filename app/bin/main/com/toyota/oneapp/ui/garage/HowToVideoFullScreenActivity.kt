package com.toyota.oneapp.ui.garage

import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.View
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.PlayerConstants
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.YouTubePlayer
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.YouTubePlayerListener
import com.toyota.oneapp.databinding.ActivityHowToVideoFullScreenBinding
import com.toyota.oneapp.model.howtovideos.FullScreenBack
import com.toyota.oneapp.ui.MVPBaseActivity
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.eventbus.RxBus

@AndroidEntryPoint
class HowToVideoFullScreenActivity :
    MVPBaseActivity<HowToVideoFullScreenPresenter>(),
    YouTubePlayerListener {
    private lateinit var binding: ActivityHowToVideoFullScreenBinding
    private val presenter by lazy { HowToVideoFullScreenPresenter() }

    private var currentSec: Float = 0f

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        binding = ActivityHowToVideoFullScreenBinding.inflate(layoutInflater)
        setContentView(binding.root)

        lifecycle.addObserver(binding.youtubePlayerView)
        binding.youtubePlayerView.initialize(this, true)
        binding.youtubePlayerView.enterFullScreen()
        binding.youtubePlayerView
            .getPlayerUiController()
            .setFullScreenButtonClickListener(View.OnClickListener { onBackPressed() })
    }

    override fun createPresenter(): HowToVideoFullScreenPresenter = presenter

    override fun onBackPressed() {
        RxBus.get().post(FullScreenBack(presenter.position, currentSec))
        super.onBackPressed()
    }

    override fun onApiChange(youTubePlayer: YouTubePlayer) {
    }

    override fun onCurrentSecond(
        youTubePlayer: YouTubePlayer,
        second: Float,
    ) {
        currentSec = second
    }

    override fun onError(
        youTubePlayer: YouTubePlayer,
        error: PlayerConstants.PlayerError,
    ) {
    }

    override fun onPlaybackQualityChange(
        youTubePlayer: YouTubePlayer,
        playbackQuality: PlayerConstants.PlaybackQuality,
    ) {
    }

    override fun onPlaybackRateChange(
        youTubePlayer: YouTubePlayer,
        playbackRate: PlayerConstants.PlaybackRate,
    ) {
    }

    override fun onReady(youTubePlayer: YouTubePlayer) {
        if (presenter.videoId != null) {
            youTubePlayer.loadVideo(presenter.videoId!!, presenter.currentSec)
        }
    }

    override fun onStateChange(
        youTubePlayer: YouTubePlayer,
        state: PlayerConstants.PlayerState,
    ) {
    }

    override fun onVideoDuration(
        youTubePlayer: YouTubePlayer,
        duration: Float,
    ) {
    }

    override fun onVideoId(
        youTubePlayer: YouTubePlayer,
        videoId: String,
    ) {
    }

    override fun onVideoLoadedFraction(
        youTubePlayer: YouTubePlayer,
        loadedFraction: Float,
    ) {
    }
}
