package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model

import android.os.Parcelable
import com.toyota.oneapp.model.subscriptionV2.SubscriptionPackage
import kotlinx.parcelize.Parcelize

/**
 * The Model - Used to hold Selected Package Information.
 */
@Parcelize
data class SelectedPackageInfo(
    val isSubscriptionBundle: Boolean,
    val pkg: SubscriptionPackage,
    var autoRenew: Boolean,
) : Parcelable
