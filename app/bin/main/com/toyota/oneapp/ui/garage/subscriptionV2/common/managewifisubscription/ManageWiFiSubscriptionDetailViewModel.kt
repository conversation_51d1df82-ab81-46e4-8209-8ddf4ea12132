package com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.mapper.ManageWiFiSubscriptionDetailUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.model.ManageWiFiSubscriptionDetailUIModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

/**
 * A ViewModel - For [ManageWiFiSubscriptionDetailFragment]
 */
@HiltViewModel
class ManageWiFiSubscriptionDetailViewModel
    @Inject
    constructor(
        private val state: SavedStateHand<PERSON>,
        private val mapper: ManageWiFiSubscriptionDetailUIMapper,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        sealed class Event {
            data class NavigateToWiFiSubscriptionCarrierScreen(
                val url: String,
            ) : Event()

            data class NavigateToCallCarrierScreen(
                val number: String,
            ) : Event()
        }

        private val args = ManageWiFiSubscriptionDetailFragmentArgs.fromSavedStateHandle(state)

        private val _uiModel = MutableLiveData<ManageWiFiSubscriptionDetailUIModel>()
        private val _event = SingleLiveEvent<Event>()

        val uiModel: LiveData<ManageWiFiSubscriptionDetailUIModel>
            get() = _uiModel
        val event: LiveData<Event>
            get() = _event

        init {
            initializeUIModel()
        }

        private fun initializeUIModel() {
            with(args) {
                _uiModel.value = mapper.map(initializationModel = initializationModel)
            }
        }

        fun isCallActionEnabled(): Boolean = args.initializationModel.externalContactDetail.isNotEmpty()

        fun onCallClick() {
            val number = args.initializationModel.externalContactDetail
            _event.value =
                Event.NavigateToCallCarrierScreen(
                    number = number,
                )
        }

        fun onActionClick() {
            val event = if (args.vehicle.isCY17) AnalyticsEvent.MANAGE_SUBSCRIPTION_3rd_PARTY_VERIZON_CLICKED else AnalyticsEvent.MANAGE_SUBSCRIPTION_3rd_PARTY_ATT_CLICKED
            analyticsLogger.logEvent(event)

            val url = args.initializationModel.externalTargetUrl
            _event.value =
                Event.NavigateToWiFiSubscriptionCarrierScreen(
                    url = url,
                )
        }
    }
