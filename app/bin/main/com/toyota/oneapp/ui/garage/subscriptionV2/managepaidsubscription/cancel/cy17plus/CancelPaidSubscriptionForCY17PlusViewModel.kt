package com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.cancel.cy17plus

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.extensions.getLocale
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscription.*
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.cancel.cy17plus.mapper.RefundUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.cancel.cy17plus.model.RefundUIModel
import dagger.hilt.android.lifecycle.HiltViewModel
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import kotlin.collections.ArrayList

/**
 * A ViewModel for [CancelPaidSubscriptionForCY17PlusFragment] Screen.
 */
@HiltViewModel
class CancelPaidSubscriptionForCY17PlusViewModel
    @Inject
    constructor(
        private val savedStateHandle: SavedStateHandle,
        private val mapper: RefundUIMapper,
        private val subscriptionApiManager: SubscriptionAPIManager,
        private val preferenceModel: OneAppPreferenceModel,
        private val languageManager: LanguageManager,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        private val args =
            CancelPaidSubscriptionForCY17PlusFragmentArgs.fromSavedStateHandle(
                savedStateHandle,
            )

        sealed class Event {
            data class NavigateToCancelSubscriptionSuccessElectronicRefundScreen(
                val isRefundAvailable: Boolean,
                val isManualRefund: Boolean,
                val customerEmail: String?,
            ) : Event()

            data class NavigateToCancelSubscriptionSuccessManualRefundScreen(
                val paymentRecord: PaymentRecord?,
            ) : Event()

            object NavigateToCancelSubscriptionFailureScreen : Event()
        }

        private val _refundUIModel = MutableLiveData<RefundUIModel>()
        private val _cancellationEnabled = MutableLiveData<Boolean>()
        private val _event = MutableLiveData<Event>()

        private var cancellationReasonCode: String? = null
        private var cancellationDataResponse: CancellationDataResponse? = null

        val refundUIModel: LiveData<RefundUIModel>
            get() = _refundUIModel
        val cancellationEnabled: LiveData<Boolean>
            get() = _cancellationEnabled
        val event: LiveData<Event>
            get() = _event

        init {
            getRefundReview()
        }

        private fun getRefundReview() {
            val subscription = args.subscription

            val subscriptionIds =
                ArrayList<String>().apply {
                    if (subscription.subscriptionID != null) {
                        add(subscription.subscriptionID) // all subscription must have the subscriptionID
                    }
                    if (subscription.consolidatedProductIds != null) {
                        addAll(subscription.consolidatedProductIds)
                    }
                }

            val cancellationDataRequest =
                CancellationDataRequest(
                    orderDate = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH).format(Date()),
                    guid = preferenceModel.getGuid(),
                    vin = args.vehicle.vin,
                    subscriptionsIds = subscriptionIds,
                )

            showProgress()

            subscriptionApiManager.sendGetRefundPreview(
                cancellationDataRequest,
                object : BaseCallback<CancellationDataResponse>() {
                    override fun onSuccess(response: CancellationDataResponse) {
                        <EMAIL> = response
                        _refundUIModel.postValue(
                            mapper.map(
                                model = response,
                                vehicleLocale =
                                    args.vehicle.getLocale(
                                        language = languageManager.getCurrentLanguage(),
                                    ),
                            ),
                        )
                    }

                    override fun onError() {
                        showErrorMessage(R.string.generic_error)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun onCancellationReasonSelected(cancellationReasonCode: String) {
            this.cancellationReasonCode = cancellationReasonCode

            _cancellationEnabled.value = (cancellationReasonCode.isNotEmpty()) && (cancellationDataResponse != null)
        }

        fun cancelSubscription() {
            val subscription = args.subscription

            analyticsLogger.logEvent(
                AnalyticsEvent.MANAGE_SUBSCRIPTION_CANCEL_FLOW_CANCEL_CONFIRMATION_CLICKED,
                SubscriptionConstants.ANALYTICS_KEY_SERVICE to subscription.displayProductName,
            )

            val subscriptionIDs =
                arrayListOf<String>().apply {
                    if (subscription.subscriptionID != null) {
                        add(subscription.subscriptionID) // all subscription must have the subscriptionID
                    }
                    if (subscription.consolidatedProductIds != null) {
                        addAll(subscription.consolidatedProductIds)
                    }
                    if (subscription.consolidatedGoodwillIds != null) {
                        addAll(subscription.consolidatedGoodwillIds)
                    }
                }

            showProgress()

            subscriptionApiManager.sendCancelSubscription(
                args.vehicle,
                subscriptionIDs,
                emptyList(),
                cancellationReasonCode.orEmpty(),
                cancellationDataResponse?.payload?.records,
                object : BaseCallback<CancelSubscriptionResponse>() {
                    override fun onSuccess(response: CancelSubscriptionResponse) {
                        analyticsLogger.logEvent(AnalyticsEvent.CANCEL_PAID_SUBSCRIPTION_SUCCESS)

                        showSuccessToastMessage(R.string.Cancellation_Request_In_Progress_note)

                        val creditMemo =
                            cancellationDataResponse?.payload?.previewResult?.creditMemos?.get(
                                0,
                            )
                        val isRefundAvailable =
                            creditMemo?.creditMemoItems?.get(0)?.refundEligibilityStatus
                                ?: false
                        val isManualRefund = (RefundResponse.MANUALCHECK == response.payload?.refundResponse?.type)

                        if (isManualRefund) {
                            _event.value =
                                Event.NavigateToCancelSubscriptionSuccessManualRefundScreen(
                                    paymentRecord = cancellationDataResponse?.payload?.records,
                                )
                        } else {
                            _event.value =
                                Event.NavigateToCancelSubscriptionSuccessElectronicRefundScreen(
                                    isRefundAvailable = isRefundAvailable,
                                    isManualRefund = isManualRefund,
                                    customerEmail =
                                        preferenceModel
                                            .getAccountInfoSubscriber()
                                            ?.customerEmails
                                            ?.get(
                                                0,
                                            )?.emailAddress
                                            ?: "",
                                )
                        }

                        if (isManualRefund) {
                            analyticsLogger.logEvent(
                                AnalyticsEvent.CANCEL_PAID_SUBSCRIPTION_MANUAL_REFUND,
                                Pair("reason", cancellationReasonCode),
                            )
                        }
                    }

                    override fun onError() {
                        analyticsLogger.logEvent(AnalyticsEvent.CANCEL_PAID_SUBSCRIPTION_FAILED)

                        _event.value = Event.NavigateToCancelSubscriptionFailureScreen
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }
    }
