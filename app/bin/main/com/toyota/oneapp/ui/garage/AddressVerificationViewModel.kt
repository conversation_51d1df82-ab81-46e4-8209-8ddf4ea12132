package com.toyota.oneapp.ui.garage

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.account.CustomerAddress
import com.toyota.oneapp.model.subscription.AccountInfoPayload
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.Brand
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class AddressVerificationViewModel
    @Inject
    constructor(
        private val accountAPIManager: AccountAPIManager,
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        private val preferenceModel: OneAppPreferenceModel,
    ) : BaseViewModel() {
        val adiUpdateSuccessfully: MutableLiveData<Boolean> = MutableLiveData()
        val onSelectAddressClick: MutableLiveData<Boolean> = MutableLiveData(false)
        private val onEditClicked = SingleLiveEvent<Any>()

        val onAddressEditClicked: LiveData<Any>
            get() = onEditClicked

        fun updateAccountInfo(
            address: String?,
            city: String?,
            state: String?,
            zipCode: String?,
            country: String?,
        ) {
            var savedAccountInfo = preferenceModel.getAccountInfoSubscriber()
            if (savedAccountInfo == null) {
                savedAccountInfo = AccountInfoSubscriber()
            }
            val customerAddresses: Array<CustomerAddress?>
            if (savedAccountInfo.customerAddresses == null || savedAccountInfo.customerAddresses.isEmpty()) {
                customerAddresses = arrayOfNulls(1)
                customerAddresses[0] =
                    CustomerAddress(
                        "HOME",
                        address!!,
                        city,
                        state,
                        zipCode,
                        country!!,
                    )
            } else {
                customerAddresses = savedAccountInfo.customerAddresses
                customerAddresses[0]?.address = address.orEmpty()
                customerAddresses[0]?.city = city
                customerAddresses[0]?.state = state
                customerAddresses[0]?.zipCode = zipCode
                customerAddresses[0]?.country = country.orEmpty()
            }
            val payload = AccountInfoPayload()
            val accountInfoSubscriber = AccountInfoSubscriber()
            accountInfoSubscriber.preferredLanguage = savedAccountInfo.preferredLanguage
            accountInfoSubscriber.guid = preferenceModel.getGuid()
            accountInfoSubscriber.uiLanguage = savedAccountInfo.uiLanguage
            accountInfoSubscriber.objectId = savedAccountInfo.objectId
            accountInfoSubscriber.customerAddresses = customerAddresses
            accountInfoSubscriber.firstName = savedAccountInfo.firstName
            accountInfoSubscriber.lastName = savedAccountInfo.lastName
            accountInfoSubscriber.customerEmails = savedAccountInfo.customerEmails
            accountInfoSubscriber.customerPhoneNumbers = savedAccountInfo.customerPhoneNumbers
            payload.primarySubscriber = accountInfoSubscriber
            val generation: String
            val brand: String
            if (applicationData.savedVehicles.isNotEmpty()) {
                brand = if (applicationData.savedVehicles[0].isToyotaBrand) "T" else "L"
                generation = applicationData.savedVehicles[0].generation ?: ToyotaConstants.CY17PLUS
            } else {
                generation = ToyUtil.getAssociatedSXMGeneration(applicationData.getVehicleList())
                brand = ToyUtil.getAssociatedSXMBrands(applicationData.getVehicleList())
            }
            showProgress()
            accountAPIManager.sendSetAccountInfoRequest(
                applicationData.getSelectedVehicle()?.brand ?: Brand.currentAppBrand().appBrand,
                generation,
                brand,
                payload,
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        adiUpdateSuccessfully.value = true
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        showErrorMessage(errorMsg)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun isAccountUpdated(
            streetAddress: String?,
            city: String?,
            country: String?,
            zipCode: String?,
            state: String?,
            customerAddress: CustomerAddress?,
        ): Boolean =
            if (customerAddress == null) {
                false
            } else {
                (
                    streetAddress.equals(customerAddress.address, ignoreCase = true) &&
                        city.equals(customerAddress.city, ignoreCase = true) &&
                        country.equals(customerAddress.country, ignoreCase = true) &&
                        zipCode.equals(customerAddress.zipCode, ignoreCase = true) &&
                        state.equals(customerAddress.state, ignoreCase = true)
                )
            }

        fun onContinueButtonClicked() {
            analyticsLogger.logEvent(AnalyticsEvent.CONTINUE_TO_PAYMENT_PAGE)
            onSelectAddressClick.value = true
        }

        fun onAddressEditClicked() {
            onEditClicked.call()
        }
    }
