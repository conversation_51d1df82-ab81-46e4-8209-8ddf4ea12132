package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.extensions.getLocale
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscriptionV2.SubscriptionPackage
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.mapper.SelectPackUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.model.SelectPackageUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.SelectedPackageInfo
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import java.util.Locale
import javax.inject.Inject

/**
 * A ViewModel - For [SelectPackageForServiceFragment]
 */
@HiltViewModel
class SelectPackageForServiceViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val uiMapper: SelectPackUIMapper,
        private val languageManager: LanguageManager,
    ) : BaseViewModel() {
        sealed class NavigationEvent {
            data class SendSelectedPackageResultAndFinishTheScreen(
                val productLine: String,
                val selectedPackageInfo: SelectedPackageInfo,
            ) : NavigationEvent()
        }

        private val args = SelectPackageForServiceFragmentArgs.fromSavedStateHandle(state)

        private val service = args.service
        private var selectedPackage: SubscriptionPackage? = null

        private var _productName = MutableLiveData<String>()
        private val _packages = MutableLiveData<List<SelectPackageUIModel>>()
        private val _selectedSubscriptionPackage = MutableLiveData<SelectPackageUIModel>()
        private val _isAutoRenewVisible = MutableLiveData<Boolean>()
        private val _isContinueEnabled = MutableLiveData<Boolean>()
        private val _navigationEvent = SingleLiveEvent<NavigationEvent>()

        val productName: LiveData<String>
            get() = _productName
        val packages: LiveData<List<SelectPackageUIModel>>
            get() = _packages
        val selectedSubscriptionPackage: LiveData<SelectPackageUIModel>
            get() = _selectedSubscriptionPackage
        val isAutoRenewVisible: LiveData<Boolean>
            get() = _isAutoRenewVisible
        val autoRenew = MutableLiveData<Boolean>()
        val isContinueEnabled: LiveData<Boolean>
            get() = _isContinueEnabled
        val navigationEvent: LiveData<NavigationEvent>
            get() = _navigationEvent

        init {
            _productName.value = service.displayProductName
            _packages.value = uiMapper.map(service, args.selectedPackage, args.vehicle)
            // Show AutoRenew - Only to CY17PLUS vehicle & Subscription should be renewable.
            _isAutoRenewVisible.value = (!args.vehicle.isCY17) && (service.renewable == true)

            if (args.selectedPackage != null) {
//            _selectedSubscriptionPackage.value = args.selectedPackage.pkg
                _selectedSubscriptionPackage.value =
                    _packages.value!!.first {
                        (it is SelectPackageUIModel.SelectServicePackageUIModel && it.isPackageSelected) ||
                            (it is SelectPackageUIModel.SelectBundlePackageUIModel && it.isPackageSelected)
                    }
                autoRenew.value = args.selectedPackage?.autoRenew
            } else {
                _isContinueEnabled.value = false
                autoRenew.value = false
            }
        }

        fun updateAutoRenewStatus(isAutoRenew: Boolean) {
            autoRenew.value = isAutoRenew
        }

        fun getVehicleLocale(): Locale = args.vehicle.getLocale(language = languageManager.getCurrentLanguage())

        fun onSubscriptionSelected(selectedPackageUIModel: SelectPackageUIModel) {
            when (selectedPackageUIModel) {
                is SelectPackageUIModel.SelectServicePackageUIModel -> {
                    this.selectedPackage = selectedPackageUIModel.packageInfo
                    _isContinueEnabled.value = true
                }

                is SelectPackageUIModel.SelectBundlePackageUIModel -> {
                    this.selectedPackage = selectedPackageUIModel.packageInfo
                    _isContinueEnabled.value = true
                }
            }
        }

        fun onSubscriptionDeSelected() {
            selectedPackage = null
            _isContinueEnabled.value = false
        }

        fun onContinue() {
            val selectedPackageInfo =
                SelectedPackageInfo(
                    isSubscriptionBundle = service.category == "BUNDLE",
                    pkg = selectedPackage!!,
                    autoRenew = autoRenew.value!!,
                )
            _navigationEvent.value =
                NavigationEvent.SendSelectedPackageResultAndFinishTheScreen(
                    productLine = service.productLine.orEmpty(),
                    selectedPackageInfo = selectedPackageInfo,
                )
        }
    }
