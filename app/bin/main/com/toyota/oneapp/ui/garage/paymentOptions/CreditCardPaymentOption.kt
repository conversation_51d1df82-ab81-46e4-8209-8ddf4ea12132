package com.toyota.oneapp.ui.garage.paymentOptions

import androidx.annotation.DrawableRes
import com.toyota.oneapp.R
import com.toyota.oneapp.model.subscription.PaymentRecord
import java.util.*

class CreditCardPaymentOption(
    paymentRecord: PaymentRecord,
) : PaymentOption(paymentRecord) {
    override val accountNumber = paymentRecord.creditCardMaskNumber

    @DrawableRes
    override val logoResourceId =
        when (paymentRecord.creditCardType?.lowercase(Locale.US)) {
            "visa" -> R.drawable.ic_visa_logo
            "mastercard" -> R.drawable.ic_mastercard_logo
            "americanexpress" -> R.drawable.ic_american_express_logo
            "discover" -> R.drawable.ic_discover_logo
            else -> R.drawable.ic_generic_card_logo
        }
} // CreditCardPaymentOption class
