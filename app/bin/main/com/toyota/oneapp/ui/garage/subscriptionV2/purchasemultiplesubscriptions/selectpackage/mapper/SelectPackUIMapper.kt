package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.mapper

import android.content.Context
import com.toyota.oneapp.R
import com.toyota.oneapp.extensions.getLocale
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.model.subscriptionV2.SubscriptionPackage
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectpackage.model.SelectPackageUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.SelectedPackageInfo
import com.toyota.oneapp.util.DoubleUtil.toDisplayPrice
import javax.inject.Inject

class SelectPackUIMapper
    @Inject
    constructor(
        private val context: Context,
        private val languageManager: LanguageManager,
    ) {
        fun map(
            service: AvailableSubscription,
            selectedPackageInfo: SelectedPackageInfo?,
            vehicle: VehicleInfo,
        ): List<SelectPackageUIModel> {
            val uiModels = mutableListOf<SelectPackageUIModel>()
            val vehicleLocale = vehicle.getLocale(language = languageManager.getCurrentLanguage())

            service.packages.forEach {
                if (service.category == "BUNDLE") {
                    val bundlePackageUIModel =
                        SelectPackageUIModel.SelectBundlePackageUIModel(
                            isPackageSelected = selectedPackageInfo != null && selectedPackageInfo.pkg.packageID == it.packageID,
                            title = service.displayProductName,
                            subscriptionAmount = it.price?.toDisplayPrice(vehicleLocale).orEmpty(),
                            subscriptionTerm = getDistrayTermForBundle(it),
                            bundleComponents = service.components ?: emptyList(),
                            packageInfo = it,
                        )

                    uiModels.add(bundlePackageUIModel)
                } else {
                    val servicePackageUIModel =
                        SelectPackageUIModel.SelectServicePackageUIModel(
                            isPackageSelected = selectedPackageInfo != null && selectedPackageInfo.pkg.packageID == it.packageID,
                            title = service.displayProductName,
                            subscriptionAmount = it.price?.toDisplayPrice(vehicleLocale).orEmpty(),
                            subscriptionTerm = getDisplayTerm(it),
                            packageInfo = it,
                        )

                    uiModels.add(servicePackageUIModel)
                }
            }

            return uiModels
        }

        private fun getDisplayTerm(packageInfo: SubscriptionPackage): String =
            with(packageInfo) {
                when (subscriptionTerm) {
                    SubscriptionConstants.SUBSCRIPTION_TERM_MONTH -> {
                        context.getString(R.string.Garage_plan_monthly)
                    }
                    SubscriptionConstants.SUBSCRIPTION_TERM_YEAR -> {
                        context.getString(R.string.Garage_plan_yearly)
                    }
                    else -> {
                        ""
                    }
                }
            }

        private fun getDistrayTermForBundle(packageInfo: SubscriptionPackage): String =
            with(packageInfo) {
                when (subscriptionTerm) {
                    SubscriptionConstants.SUBSCRIPTION_TERM_MONTH -> {
                        " /mo"
                    }
                    SubscriptionConstants.SUBSCRIPTION_TERM_YEAR -> {
                        " /yr"
                    }
                    else -> {
                        ""
                    }
                }
            }
    }
