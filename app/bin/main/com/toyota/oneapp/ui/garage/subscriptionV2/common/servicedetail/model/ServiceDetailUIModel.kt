package com.toyota.oneapp.ui.garage.subscriptionV2.common.servicedetail.model

import android.os.Parcelable
import androidx.annotation.DrawableRes
import com.toyota.oneapp.model.subscriptionV2.BundleComponent
import kotlinx.parcelize.Parcelize

@Parcelize
data class ServiceDetailUIModel(
    val productName: String,
    val productDescription: String,
    val productImageUrl: String?,
    val canShowBundleComponents: Boolean,
    val bundleComponents: List<BundleComponent>?,
    @DrawableRes val wifiCarrierIcon: Int?,
) : Parcelable
