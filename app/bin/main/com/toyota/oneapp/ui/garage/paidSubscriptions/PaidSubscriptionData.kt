package com.toyota.oneapp.ui.garage.paidSubscriptions

import android.content.Context
import android.content.Intent
import com.toyota.oneapp.model.subscription.SubscriptionGetPayload
import com.toyota.oneapp.model.subscription.SubscriptionPrices
import com.toyota.oneapp.model.vehicle.VehicleInfo

private const val PURCHASE_LIST_KEY = "purchaseList"
private const val SUBSCRIPTION_GET_PAYLOAD_KEY = "SubscriptionGetPayload"
private const val SUBSCRIPTION_PAYLOAD_KEY = "subscriptionPayload"
private const val NEXT_ACTIVITY_KEY = "nextActivity"
private const val ACCESS_TOKEN_KEY = "accessToken"
private const val VEHICLE = "vehicle"
private const val IS_AZURE = "isAzure"

data class PaidSubscriptionData(
    var purchaseList: ArrayList<SubscriptionPrices>,
    val subscriptionGetPayload: SubscriptionGetPayload?,
    val subscriptionPayload: SubscriptionGetPayload?,
    val accessToken: String?,
    val selectedVehicle: VehicleInfo?,
    val isAzure: Boolean? = true,
) {
    val vin = subscriptionGetPayload?.vin

    fun createIntent(
        context: Context,
        activityClass: Class<*>,
    ): Intent {
        val intent = Intent(context, activityClass)
        intent.putParcelableArrayListExtra(PURCHASE_LIST_KEY, purchaseList)
        intent.putExtra(SUBSCRIPTION_GET_PAYLOAD_KEY, subscriptionGetPayload)
        intent.putExtra(SUBSCRIPTION_PAYLOAD_KEY, subscriptionPayload)
        intent.putExtra(ACCESS_TOKEN_KEY, accessToken)
        intent.putExtra(VEHICLE, selectedVehicle)
        intent.putExtra(IS_AZURE, isAzure)
        return intent
    }

    companion object {
        @JvmStatic
        fun buildFromIntent(intent: Intent): PaidSubscriptionData {
            val purchaseList = intent.getParcelableArrayListExtra(PURCHASE_LIST_KEY) ?: ArrayList<SubscriptionPrices>()
            val subscriptionGetPayload =
                intent.getParcelableExtra<SubscriptionGetPayload>(
                    SUBSCRIPTION_GET_PAYLOAD_KEY,
                )
            val subscriptionPayload =
                intent.getParcelableExtra<SubscriptionGetPayload>(
                    SUBSCRIPTION_PAYLOAD_KEY,
                )
            val accessToken = intent.getStringExtra(ACCESS_TOKEN_KEY)
            val selectedVehicle = intent.getParcelableExtra<VehicleInfo>(VEHICLE)
            val isAzure = intent.getBooleanExtra(IS_AZURE, true)
            return PaidSubscriptionData(
                purchaseList,
                subscriptionGetPayload,
                subscriptionPayload,
                accessToken,
                selectedVehicle,
                isAzure,
            )
        }

        @JvmStatic
        fun builder() = Builder()
    } // companion object

    class Builder {
        private var purchaseList: ArrayList<SubscriptionPrices> = ArrayList()
        private var subscriptionGetPayload: SubscriptionGetPayload? = null
        private var subscriptionPayload: SubscriptionGetPayload? = null
        private var accessToken: String? = null
        private var selectedVehicle: VehicleInfo? = null
        private var isAzure: Boolean? = true

        fun setPurchaseList(list: ArrayList<SubscriptionPrices>): Builder {
            purchaseList = list
            return this
        }

        fun setSubscriptionGetPayload(payload: SubscriptionGetPayload?): Builder {
            subscriptionGetPayload = payload
            return this
        }

        fun setSubscriptionPayload(payload: SubscriptionGetPayload?): Builder {
            subscriptionPayload = payload
            return this
        }

        fun setAccessToken(token: String?): Builder {
            accessToken = token
            return this
        }

        fun setSelectedVehicle(selectedVehicle: VehicleInfo): Builder {
            this.selectedVehicle = selectedVehicle
            return this
        }

        fun setAzure(isAzure: Boolean): Builder {
            this.isAzure = isAzure
            return this
        }

        fun build() =
            PaidSubscriptionData(
                purchaseList,
                subscriptionGetPayload,
                subscriptionPayload,
                accessToken,
                selectedVehicle,
                isAzure,
            )
    } // Builder class
} // PaidSubscriptionData class
