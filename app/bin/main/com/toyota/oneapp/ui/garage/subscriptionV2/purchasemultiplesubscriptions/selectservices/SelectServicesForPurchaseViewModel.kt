package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.subscription.BillingAddress
import com.toyota.oneapp.model.subscription.CalculateTaxesResponse
import com.toyota.oneapp.model.subscription.TaxationItems
import com.toyota.oneapp.model.subscriptionV2.AvailableSubscription
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.network.api.repository.SubscriptionV2Repository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.dataconsent.VehicleSubscriptionAlertsArguments
import com.toyota.oneapp.ui.garage.paymentOptions.model.PaymentCY17PlusResult
import com.toyota.oneapp.ui.garage.subscriptionV2.common.managewifisubscription.model.ManageWiFiSubscriptionDetailInitializationModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.servicedetail.mapper.AvailableSubscriptionToServiceDetailUIModelMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.common.servicedetail.model.ServiceDetailUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionConstants
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.CheckoutInitializationModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.PaymentInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.checkout.model.PriceInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Args
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17Result
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.mapper.PurchaseServiceListUIMapper
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.PurchaseServiceListItemUIModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.SelectedPackageInfo
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject
import kotlin.collections.set

/**
 * A ViewModel - For [SelectServicesForPurchaseFragment]
 */
@HiltViewModel
class SelectServicesForPurchaseViewModel
    @Inject
    constructor(
        private val savedStateHandle: SavedStateHandle,
        private val uiMapper: PurchaseServiceListUIMapper,
        private val serviceDetailUIModelMapper: AvailableSubscriptionToServiceDetailUIModelMapper,
        private val preferenceModel: OneAppPreferenceModel,
        private val subscriptionManager: SubscriptionAPIManager,
        private val subscriptionV2Repository: SubscriptionV2Repository,
        private val combinedDataConsentRepository: CombinedDataConsentRepository,
    ) : BaseViewModel() {
        sealed class Event {
            object ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable : Event()
        }

        sealed class NavigationEvent {
            data class NavigateToPackageSelectionScreen(
                val isSubscriptionBundle: Boolean,
                val service: AvailableSubscription,
                val selectedPackage: SelectedPackageInfo?,
            ) : NavigationEvent()

            data class NavigateToServiceDetailScreen(
                val serviceDetailUIModel: ServiceDetailUIModel,
            ) : NavigationEvent()

            data class NavigateToManageWiFiConnectServiceDetailScreen(
                val initializationModel: ManageWiFiSubscriptionDetailInitializationModel,
            ) : NavigationEvent()

            data class NavigateToAlerts(
                val vehicleSubscriptionAlertsArguments: VehicleSubscriptionAlertsArguments,
            ) : NavigationEvent()

            data class NavigateToGetDateConsentScreen(
                val trialSubscriptions: Array<SubscriptionV2>,
                val paidSubscriptions: Array<SubscriptionV2>,
            ) : NavigationEvent()

            object NavigateToGetBillingAddress : NavigationEvent()

            data class NavigateToGetPaymentInfoScreenForCY17(
                val args: PaymentCY17Args,
            ) : NavigationEvent()

            object NavigateToGetPaymentInfoScreenForCY17Plus : NavigationEvent()

            data class NavigateToCheckoutScreen(
                val initializationModel: CheckoutInitializationModel,
            ) : NavigationEvent()

            data class NavigateToMyGarageScreen(
                val isMyGarageRefresh: Boolean,
            ) : NavigationEvent()

            data class NavigateToWaiveSubscriptionSuccessScreen(
                val isAddVehicleFlow: Boolean,
            ) : NavigationEvent()

            data class NavigateToActiveWifiSubscriptionFound(
                val trialSubscriptions: Array<SubscriptionV2>,
                val vehicle: VehicleInfo,
            ) : NavigationEvent()
        }

        private val args = SelectServicesForPurchaseFragmentArgs.fromSavedStateHandle(savedStateHandle)

        private val availableServices = args.availableSubscriptions.toList()
        private val selectedPackages = HashMap<String, SelectedPackageInfo>()
        private var consents: List<ConsentRequestItem>? = null
        private var billingAddress: BillingAddress? = null
        private var cy17PlusPaymentResult: PaymentCY17PlusResult? = null

        private val _serviceUIModels = MutableLiveData<List<PurchaseServiceListItemUIModel>>()
        private val _isContinueToPurchaseEnabled = MutableLiveData<Boolean>()
        private val _event = SingleLiveEvent<Event>()
        private val _navigationEvent = SingleLiveEvent<NavigationEvent>()

        val serviceUIModels: LiveData<List<PurchaseServiceListItemUIModel>>
            get() = _serviceUIModels
        val isContinueToPurchaseEnabled: LiveData<Boolean>
            get() = _isContinueToPurchaseEnabled
        val event: LiveData<Event>
            get() = _event
        val navigationEvent: LiveData<NavigationEvent>
            get() = _navigationEvent

        init {
            populateView()
        }

        private fun populateView() {
            _serviceUIModels.value =
                uiMapper.map(
                    availableServices = availableServices,
                    selectedPackages = selectedPackages,
                    vehicle = args.vehicle,
                )

            _isContinueToPurchaseEnabled.value = selectedPackages.isNotEmpty()
        }

        fun onToyotaServiceCheckboxClicked(
            selectedPackage: SelectedPackageInfo?,
            productLine: String,
        ) {
            val isSelected = selectedPackages.containsKey(productLine)
            if (isSelected) {
                selectedPackages.remove(productLine)
            } else {
                navigateToPackageSelectionScreen(false, selectedPackage, productLine)
            }

            populateView()
        }

        fun onToyotaServiceClicked(
            selectedPackage: SelectedPackageInfo?,
            productLine: String,
        ) {
            navigateToPackageSelectionScreen(false, selectedPackage, productLine)
        }

        private fun navigateToPackageSelectionScreen(
            isSubscriptionBundle: Boolean,
            selectedPackage: SelectedPackageInfo?,
            productLine: String,
        ) {
            val service = availableServices.first { it.productLine == productLine }
            _navigationEvent.value =
                NavigationEvent.NavigateToPackageSelectionScreen(
                    isSubscriptionBundle = isSubscriptionBundle,
                    service = service,
                    selectedPackage = selectedPackage,
                )
        }

        fun onToyotaServiceMoreIconClicked(productLine: String) {
            val service = availableServices.first { it.productLine == productLine }
            val serviceDetailUIModel = serviceDetailUIModelMapper.map(service)
            _navigationEvent.value =
                NavigationEvent.NavigateToServiceDetailScreen(
                    serviceDetailUIModel = serviceDetailUIModel,
                )
        }

        fun onSubscriptionBundleRadioButtonClicked(
            selectedBundleInfo: SelectedPackageInfo?,
            productLine: String,
        ) {
            val isBundleSelected = selectedPackages.containsKey(productLine)

            if (isBundleSelected) {
                // Bundle already selected, User clicks again to unselect the bundle
                selectedPackages.remove(productLine)
            } else {
                // User already selected a bundle, now he selects a different bundle. Replace old bundle with new one
                // User newly selects a bundle.
                selectedPackages.clear() // As only one bundle can be selected at a time.
                navigateToPackageSelectionScreen(true, selectedBundleInfo, productLine)
            }

            populateView()
        }

        fun onSubscriptionBundleClicked(
            selectedBundleInfo: SelectedPackageInfo?,
            productLine: String,
        ) {
            navigateToPackageSelectionScreen(true, selectedBundleInfo, productLine)
        }

        fun onSubscriptionBundleChevronClicked(productLine: String) {
            val bundle = availableServices.first { it.productLine == productLine }
            val serviceDetailUIModel = serviceDetailUIModelMapper.map(bundle)
            _navigationEvent.value =
                NavigationEvent.NavigateToServiceDetailScreen(
                    serviceDetailUIModel = serviceDetailUIModel,
                )
        }

        fun onExternalServiceClicked(productLine: String) {
            val service = availableServices.first { it.productLine == productLine }
            if (productLine == SubscriptionConstants.WIFI_CONNECT) {
                val wifiInitializationModel =
                    SubscriptionUtil.createWiFiSubscriptionInitializationModel(
                        service,
                    )
                _navigationEvent.value =
                    NavigationEvent.NavigateToManageWiFiConnectServiceDetailScreen(
                        initializationModel = wifiInitializationModel,
                    )
            }
        }

        fun onPackageSelectedForService(
            productLine: String,
            packageInfo: SelectedPackageInfo,
        ) {
            if (packageInfo.isSubscriptionBundle) {
                selectedPackages.clear()
            }
            selectedPackages[productLine] = packageInfo

            populateView()
        }

        fun onContinueToPurchase() {
            if (checkIfAnyServiceIsAlreadyPurchasedAndIsAutoRenewable()) {
                _event.value = Event.ShowMessageThatProductIsAlreadyPurchasedAndIsAutoRenewable
            } else {
                if (!args.alerts.isNullOrEmpty()) {
                    val allAvailableSubscriptionNames = availableServices.joinToString { it.displayProductName }
                    _navigationEvent.value =
                        NavigationEvent.NavigateToAlerts(
                            vehicleSubscriptionAlertsArguments =
                                VehicleSubscriptionAlertsArguments(
                                    allAvailableSubscriptionNames,
                                    ArrayList(args.alerts!!.toList()),
                                ),
                        )
                } else {
                    _navigationEvent.value =
                        NavigationEvent.NavigateToGetDateConsentScreen(
                            paidSubscriptions = args.paidSubscriptions,
                            trialSubscriptions = args.trialSubscriptions,
                        )
                }
            }
        }

        private fun checkIfAnyServiceIsAlreadyPurchasedAndIsAutoRenewable(): Boolean {
            val selectedServices = availableServices.filter { selectedPackages[it.productLine] != null }
            val isServiceIsAlreadyPurchasedAndIsAutoRenewable =
                selectedServices.any {
                    SubscriptionUtil.checkIsSubscriptionIsAlreadyPurchasedAndIsAutoRenewable(
                        args.vehicle,
                        it,
                    )
                }
            return isServiceIsAlreadyPurchasedAndIsAutoRenewable
        }

        fun onDataConsentReceived(consents: List<ConsentRequestItem>) {
            this.consents = consents

            if (SubscriptionUtil.checkIsMasterConsentAccepted(consents)) {
                _navigationEvent.value = NavigationEvent.NavigateToGetBillingAddress
            } else {
                // Master consent - Declined.
                if (!args.vehicle.isCY17) {
                    // For CY17Plus - Waive subscription.
                    waiveSubscriptions()
                } else {
                    // For CY17 - Navigate to MyGarage screen.
                    _navigationEvent.value =
                        NavigationEvent.NavigateToMyGarageScreen(
                            isMyGarageRefresh = false,
                        )
                }
            }
        }

        private fun waiveSubscriptions() {
            val subscriptionGetPayload =
                SubscriptionUtil.formSubscriptionGetPayload(
                    vehicle = args.vehicle,
                    guid = preferenceModel.getGuid(),
                    trialSubscriptions = args.trialSubscriptions.toList(),
                    accessToken = args.accessToken ?: "",
                    isCPOEligible = args.isCPOEligible,
                    isPPOEligible = args.isPPOEligible,
                )
            val capabilities = args.vehicle.capabilityItems

            viewModelScope.launch {
                showProgress()
                val resource =
                    combinedDataConsentRepository.waiveSubscription(
                        isWaiver = true,
                        asiCode = args.vehicle.asiCode.orEmpty(),
                        hwtType = args.vehicle.hwType.orEmpty(),
                        subscriptionGetPayload = subscriptionGetPayload,
                        capabilities = capabilities,
                    )

                when (resource) {
                    is Resource.Success -> {
                        hideProgress()
                        _navigationEvent.postValue(
                            NavigationEvent.NavigateToWaiveSubscriptionSuccessScreen(
                                isAddVehicleFlow = args.isAddVehicleFlow,
                            ),
                        )
                    }

                    is Resource.Failure -> {
                        hideProgress()
                        if (ToyotaConstants.WAIVE_SUBSCRIPTION_ATT_ACTIVE_WIFI_ERROR_CODE.equals(
                                resource.error?.responseCode,
                                true,
                            )
                        ) {
                            _navigationEvent.value =
                                NavigationEvent.NavigateToActiveWifiSubscriptionFound(
                                    trialSubscriptions = args.trialSubscriptions,
                                    vehicle = args.vehicle,
                                )
                        } else {
                            showErrorMessage(resource.message)
                        }
                    }
                    else -> {}
                }
            }
        }

        fun onBillingAddressReceived(billingAddress: BillingAddress) {
            this.billingAddress = billingAddress
            if (args.vehicle.isCY17) {
                val args =
                    PaymentCY17Args(
                        vehicle = args.vehicle,
                        subscriptionPackages = selectedPackages.values.map { it.pkg },
                        billingAddress = billingAddress,
                        accessToken = args.accessToken.orEmpty(),
                        isAzure = if (args.isAddVehicleFlow) args.isAzure else args.vehicle.isAzure,
                    )
                _navigationEvent.value = NavigationEvent.NavigateToGetPaymentInfoScreenForCY17(args)
            } else {
                _navigationEvent.value = NavigationEvent.NavigateToGetPaymentInfoScreenForCY17Plus
            }
        }

        fun onPaymentInfoReceivedForCY17(result: PaymentCY17Result) {
            val taxResponse = result.calculateTaxResponse.payload.taxResponse
            val priceInfo =
                PriceInfo(
                    totalAmountWithoutTax = taxResponse.productTotalAmount.amount,
                    totalTaxAmount = taxResponse.taxTotalAmount.amount,
                    totalAmount = taxResponse.totalPurchaseAmount.amount,
                    taxationItems = null,
                )
            val paymentInfo =
                PaymentInfo.CY17PaymentInfo(
                    paymentId = result.paymentMethodId,
                    paymentAccessToken = result.paymentAccessToken,
                )
            val checkoutInitializationModel =
                CheckoutInitializationModel(
                    services = availableServices,
                    selectedPackages = selectedPackages,
                    priceInfo = priceInfo,
                    paymentInfo = paymentInfo,
                    accessToken = args.accessToken,
                    consents = consents!!,
                )

            _navigationEvent.value =
                NavigationEvent.NavigateToCheckoutScreen(
                    initializationModel = checkoutInitializationModel,
                )
        }

        fun onPaymentInfoReceivedForCY17Plus(result: PaymentCY17PlusResult) {
            this.cy17PlusPaymentResult = result

            getCY17PlusTaxAmount()
        }

        private fun getCY17PlusTaxAmount() {
            viewModelScope.launch {
                showProgress()

                val pkgs = selectedPackages.values.map { it.pkg }
                val resource =
                    subscriptionV2Repository.getSubscriptionTaxAmount(
                        region = args.vehicle.region,
                        billingAddress = billingAddress!!,
                        packages = pkgs,
                    )
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.let { taxesResponse ->
                            onGetCY17PlusTaxAmountSuccess(taxesResponse)
                        }
                    }

                    is Resource.Failure -> {
                        showErrorMessage(R.string.generic_error)
                    }
                    else -> {}
                }

                hideProgress()
            }
        }

        private fun onGetCY17PlusTaxAmountSuccess(taxResponse: CalculateTaxesResponse) {
            val taxationItems = mutableListOf<TaxationItems>()
            for (item in taxResponse.payload.invoiceItems.orEmpty()) {
                for (tax in item.taxationItems.orEmpty()) {
                    taxationItems.add(tax)
                }
            }
            val priceInfo =
                PriceInfo(
                    totalAmountWithoutTax = taxResponse.payload.totalAmountWithoutTax,
                    totalTaxAmount = taxResponse.payload.totalTaxAmount,
                    totalAmount = taxResponse.payload.totalAmount,
                    taxationItems = taxationItems,
                )
            val paymentInfo =
                PaymentInfo.CY17PlusPaymentInfo(
                    paymentId = cy17PlusPaymentResult!!.paymentId,
                    isDefaultPayment = cy17PlusPaymentResult!!.isDefaultPayment,
                    paymentRecord = cy17PlusPaymentResult!!.paymentRecord,
                )
            val checkoutInitializationModel =
                CheckoutInitializationModel(
                    services = availableServices,
                    selectedPackages = selectedPackages,
                    priceInfo = priceInfo,
                    paymentInfo = paymentInfo,
                    accessToken = args.accessToken,
                    consents = consents!!,
                )

            _navigationEvent.value =
                NavigationEvent.NavigateToCheckoutScreen(
                    initializationModel = checkoutInitializationModel,
                )
        }
    }
