package com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.manage.mapper

import android.content.Context
import com.toyota.oneapp.R
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.subscriptionV2.productDescription
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.manage.model.ManagePaidSubscriptionUIModel
import com.toyota.oneapp.util.ToyotaConstants
import javax.inject.Inject

/**
 * A Mapper - Used to form [ManagePaidSubscriptionUIModel]
 */
class ManagePaidSubscriptionUIMapper
    @Inject
    constructor(
        private val context: Context,
    ) {
        fun map(
            subscription: SubscriptionV2,
            vehicle: VehicleInfo,
        ): ManagePaidSubscriptionUIModel {
            // Show AutoRenew - Only to CY17PLUS vehicle & Subscription should be renewable.
            val isAutoRenewVisible = !vehicle.isCY17 && subscription.renewable

            val cancelActionTxt =
                if (!vehicle.isCY17) {
                    R.string.Subscription_cancel_single_subscription
                } else {
                    R.string.Subscription_cancel_subscription
                }

            return ManagePaidSubscriptionUIModel(
                showBundleCard = subscription.category == "BUNDLE",
                screenTitle =
                    if (subscription.category == "BUNDLE") {
                        context.getString(R.string.current_plan)
                    } else {
                        subscription.displayProductName
                    },
                displayProductName = subscription.displayProductName,
                productLongDesc = subscription.productDescription(),
                isAutoRenewVisible = isAutoRenewVisible,
                displayTerms = subscription.displayTerm,
                futureCancel = subscription.futureCancel == true,
                autoRenewDisclaimer =
                    if (ToyotaConstants.BUNDLE.equals(subscription.category, true)) {
                        context.getString(R.string.auto_renew_description) + " " +
                            context.getString(
                                R.string.bundle_renew_disclaimer,
                            ) + " " +
                            context.getString(R.string.bundle_4G_network_disclaimer)
                    } else {
                        context.getString(R.string.auto_renew_description)
                    },
                autoRenewStatus =
                    if (subscription.autoRenew == true) {
                        R.string.Subscription_Auto_Renew_On
                    } else {
                        R.string.Subscription_Auto_Renew_Off
                    },
                updateActionTxt =
                    if (ToyotaConstants.BUNDLE.equals(subscription.category, true)) {
                        R.string.Subscription_update_bundle
                    } else {
                        R.string.Subscription_update_subscription
                    },
                cancelActionTxt =
                    if (ToyotaConstants.BUNDLE.equals(subscription.category, true)) {
                        R.string.Subscription_cancel_bundle
                    } else {
                        cancelActionTxt
                    },
            )
        }
    }
