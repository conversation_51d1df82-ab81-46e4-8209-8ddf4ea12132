package com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.cancel.cy17plus

import android.os.Bundle
import android.view.View
import android.widget.ArrayAdapter
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentCancelPaidSubscriptionForCy17plusBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.PaymentFailedActivity
import com.toyota.oneapp.ui.garage.SubscriptionEditFailure
import com.toyota.oneapp.ui.garage.subscriptionV2.managepaidsubscription.cancel.cy17plus.model.RefundUIModel
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

/**
 * Fragment - Used to cancel purchased subscription for CY17Plus vehicle.
 */
@AndroidEntryPoint
class CancelPaidSubscriptionForCY17PlusFragment : BaseDataBindingFragment<FragmentCancelPaidSubscriptionForCy17plusBinding>() {
    private val args: CancelPaidSubscriptionForCY17PlusFragmentArgs by navArgs()
    val viewModel: CancelPaidSubscriptionForCY17PlusViewModel by viewModels()

    private lateinit var cancellationTexts: Array<String>
    private lateinit var cancellationCodes: Array<String>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentCancelPaidSubscriptionForCy17plusBinding,
        savedInstance: Bundle?,
    ) {
        initializeFields()
        initializeViews()
        addListeners()
        populateViews()
        setUpViewModelBindings()
    }

    override fun getLayout(): Int = R.layout.fragment_cancel_paid_subscription_for_cy17plus

    private fun initializeFields() {
        cancellationTexts =
            requireContext().resources.getStringArray(R.array.Subscription_cancellation_reasons)
        cancellationCodes =
            requireContext().resources.getStringArray(
                R.array.Subscription_cancellation_reason_codes,
            )
    }

    private fun initializeViews() {
        viewDataBinding.tvCancellationDescription.visibility = View.GONE
        viewDataBinding.clRefundInfo.visibility = View.GONE
        viewDataBinding.btnConfirmCancelSubscription.isEnabled = false
    }

    private fun addListeners() {
        viewDataBinding.actvReasonForCancellation.setOnItemClickListener { _, _, position, _ ->
            viewModel.onCancellationReasonSelected(cancellationCodes[position])
        }
        viewDataBinding.btnConfirmCancelSubscription.setOnClickListener {
            showCancelConfirmationDialog()
        }
    }

    private fun populateViews() {
        with(viewDataBinding) {
            val subscription = args.subscription

            tvProductName.text =
                getString(
                    R.string.cancel_trial_subscription_message,
                    args.subscription.displayProductName,
                )
            tvProductDesc.text = subscription.productDescription

            val adapter =
                ArrayAdapter<String>(
                    requireContext(),
                    R.layout.item_spinner_cancelation_reason,
                    R.id.spinner_item,
                    cancellationTexts,
                )

            actvReasonForCancellation.setAdapter(adapter)
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.refundUIModel.observe(
            viewLifecycleOwner,
            Observer { uiModel ->
                populateRefundSection(uiModel)
            },
        )
        viewModel.cancellationEnabled.observe(
            viewLifecycleOwner,
            Observer { isEnabled ->
                viewDataBinding.btnConfirmCancelSubscription.isEnabled = isEnabled
            },
        )
        viewModel.event.observe(
            viewLifecycleOwner,
            Observer { event ->
                handleEvent(event)
            },
        )
    }

    private fun populateRefundSection(refundUIModel: RefundUIModel) {
        with(refundUIModel) {
            if (isRefundVisible) {
                viewDataBinding.tvCancellationDescription.visibility = View.GONE
                viewDataBinding.clRefundInfo.visibility = View.VISIBLE
                viewDataBinding.tvRefundAmountValue.text = refundAmount
                viewDataBinding.tvEstimatedTaxValue.text = estimatedTax
                viewDataBinding.tvRefundDueValue.text = refundDue
                viewDataBinding.tvRefundDisclaimer.text =
                    getString(R.string.Subscription_Cancellation_Disclaimer, endDate)
            } else {
                viewDataBinding.tvCancellationDescription.visibility = View.VISIBLE
                viewDataBinding.clRefundInfo.visibility = View.GONE
            }
        }
    }

    private fun handleEvent(event: CancelPaidSubscriptionForCY17PlusViewModel.Event) {
        when (event) {
            is CancelPaidSubscriptionForCY17PlusViewModel.Event.NavigateToCancelSubscriptionSuccessElectronicRefundScreen -> {
                navigateToCancelSuccessElectronicRefundScreen(event)
            }
            is CancelPaidSubscriptionForCY17PlusViewModel.Event.NavigateToCancelSubscriptionSuccessManualRefundScreen -> {
                navigateToCancelSuccessManualRefundScreen(event)
            }
            is CancelPaidSubscriptionForCY17PlusViewModel.Event.NavigateToCancelSubscriptionFailureScreen -> {
                val parameters =
                    PaymentFailedActivity.Parameters(SubscriptionEditFailure.CANCELLATION)
                startActivity(parameters.createIntent(requireContext()))
            }
        }.exhaustive
    }

    private fun showCancelConfirmationDialog() {
        DialogUtil.showDialog(
            requireActivity(),
            null,
            getString(
                R.string.Subscription_cancellation_confirm,
                args.subscription.displayProductName,
            ),
            getString(R.string.Common_confirm),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    viewModel.cancelSubscription()
                }

                override fun onCancelClick() {
                }
            },
            false,
        )
    }

    private fun navigateToCancelSuccessElectronicRefundScreen(
        event: CancelPaidSubscriptionForCY17PlusViewModel.Event.NavigateToCancelSubscriptionSuccessElectronicRefundScreen,
    ) {
        // Electronic Refund.
        val msg2 =
            if (event.isRefundAvailable) {
                getString(R.string.ManagePaidSubscription_cancellation_description, event.customerEmail)
            } else {
                null
            }

        val action =
            CancelPaidSubscriptionForCY17PlusFragmentDirections
                .actionCancelPaidSubscriptionForCY17PlusFragmentToSubscriptionActionSuccessActivity(
                    vehicle = args.vehicle,
                    icon = R.drawable.success_checkmark,
                    title = getString(R.string.ManagePaidSubscription_Cancellation_Complete),
                    msg1 = getString(R.string.ManagePaidSubscription_cancellation_succeeded),
                    msg2 = msg2,
                    actionTxt = getString(R.string.ServiceCampaign_back_to_dashboard),
                    msg3 = null,
                )
        findNavController().navigate(action)
    }

    private fun navigateToCancelSuccessManualRefundScreen(
        event: CancelPaidSubscriptionForCY17PlusViewModel.Event.NavigateToCancelSubscriptionSuccessManualRefundScreen,
    ) {
        // Manual Refund.
        val action =
            CancelPaidSubscriptionForCY17PlusFragmentDirections
                .actionCancelPaidSubscriptionForCY17PlusFragmentToCancellationSuccessManualRefundFragment(
                    vehicle = args.vehicle,
                    paymentRecord = event.paymentRecord,
                )
        findNavController().navigate(action)
    }
}
