package com.toyota.oneapp.ui.garage.subscriptionV2.common.addvehiclesuccess

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.map
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.constants.VIN_LIST_RETRY_TIMES
import com.toyota.oneapp.extensions.model
import com.toyota.oneapp.model.vehicle.VehiclelListResponse
import com.toyota.oneapp.network.api.manager.VehicleAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.common.addvehiclesuccess.model.AddVehicleSuccessUIModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class AddVehicleSuccessViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val vehicleManager: VehicleAPIManager,
        private val applicationData: ApplicationData,
    ) : BaseViewModel() {
        sealed class NavigationEvent {
            object NavigateToDashboardScreen : NavigationEvent()
        }

        private val args = AddVehicleSuccessFragmentArgs.fromSavedStateHandle(state)

        private val _uiModel = MutableLiveData<AddVehicleSuccessUIModel>()
        private val _navigationEvent = MutableLiveData<NavigationEvent>()

        val uiModel: LiveData<AddVehicleSuccessUIModel>
            get() = _uiModel
        val navigationEvent: LiveData<NavigationEvent>
            get() = _navigationEvent

        val modelText = _uiModel.map { it.vehicle.model }

        fun getVehicles() {
            showProgress()

            vehicleManager.sendGetVehicleListRequest(
                VIN_LIST_RETRY_TIMES,
                object : BaseCallback<VehiclelListResponse>() {
                    override fun onSuccess(response: VehiclelListResponse) {
                        onGetVehicleListSuccess(response)
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorCode: String?,
                        errorMsg: String?,
                    ) {
                        showErrorMessage(R.string.generic_error)
                    }

                    override fun onComplete() {
                        onGetVehicleListComplete()
                    }
                },
            )
        }

        private fun onGetVehicleListSuccess(response: VehiclelListResponse) {
            applicationData.setVehicleList(response.payload)
        }

        private fun onGetVehicleListComplete() {
            hideProgress()
            val addedVehicle =
                applicationData
                    .getVehicleList()
                    ?.find { vehicleInfo -> vehicleInfo.vin == args.vehicle.vin } ?: args.vehicle
            _uiModel.value =
                AddVehicleSuccessUIModel(
                    icon = args.icon,
                    title = args.title,
                    msg = args.msg,
                    ctaText = args.ctaText,
                    vehicle = addedVehicle,
                )
            applicationData.setSelectedVehicle(addedVehicle)
        }

        fun onFinishSetUpClicked() {
            _navigationEvent.value = NavigationEvent.NavigateToDashboardScreen
        }
    }
