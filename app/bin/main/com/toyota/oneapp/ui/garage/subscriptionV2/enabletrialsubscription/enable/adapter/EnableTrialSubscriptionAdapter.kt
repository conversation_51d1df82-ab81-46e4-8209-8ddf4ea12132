package com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.enable.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemEnableTrialSubscriptionBinding
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.util.SubscriptionUtil
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters

class EnableTrialSubscriptionAdapter(
    private var items: List<SubscriptionV2>,
    private val vehicleInfo: VehicleInfo,
    private val onItemCLickListener: OnItemClickListener,
) : RecyclerView.Adapter<EnableTrialSubscriptionAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<SubscriptionV2> {
    override fun getItemCount(): Int = items.size

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding =
            ItemEnableTrialSubscriptionBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.onBind(items[position])
    }

    inner class ViewHolder(
        private val binding: ItemEnableTrialSubscriptionBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        init {
            itemView.setOnClickListener {
                onItemCLickListener.onSubscriptionServiceItemClick(items[adapterPosition])
            }
        }

        fun onBind(subscription: SubscriptionV2) {
            binding.ivIcon.run {
                setImageResource(SubscriptionUtil.getIconForSubscriptionFromProductLine(subscription.productLine))
                DataBindingAdapters.setTint(this, SubscriptionUtil.getIconTintColor(vehicleInfo.brand))
            }
            binding.tvTitle.text = subscription.displayProductName
            binding.tvSubTitle.text = "${binding.root.resources.getString(R.string.trial_available)}\n${subscription.displayTerm}"
        }
    }

    override fun setData(data: List<SubscriptionV2>?) {
        this.items = data ?: emptyList()
        notifyDataSetChanged()
    }

    interface OnItemClickListener {
        fun onSubscriptionServiceItemClick(subscription: SubscriptionV2)
    }
}
