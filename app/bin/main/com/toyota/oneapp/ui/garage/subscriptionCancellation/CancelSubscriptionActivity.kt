package com.toyota.oneapp.ui.garage.subscriptionCancellation

import android.os.Bundle
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityCancelSubscriptionBinding
import com.toyota.oneapp.network.callback.CancelSubscriptionStatus
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.garage.EditSubscriptionData
import com.toyota.oneapp.ui.garage.PaymentFailedActivity
import com.toyota.oneapp.ui.garage.PaymentSuccessActivity
import com.toyota.oneapp.ui.garage.SubscriptionEditFailure
import com.toyota.oneapp.ui.garage.SubscriptionEditSuccess
import com.toyota.oneapp.util.dataBinding.BindableSpinnerAdapter
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

@AndroidEntryPoint
class CancelSubscriptionActivity : UiBaseActivity() {
    private val viewModel: CancelSubscriptionViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        val dataBinding =
            DataBindingUtil.setContentView<ActivityCancelSubscriptionBinding>(
                this,
                R.layout.activity_cancel_subscription,
            )
        dataBinding.lifecycleOwner = this

        viewModel.state.observe(this) { data ->
            dataBinding.subscriptionTitleTextview.text = data.title
            dataBinding.subscriptionDescriptionTextview.text = data.description
            DataBindingAdapters.setIsVisible(
                dataBinding.cancellationDescription,
                !data.isTotalVisible,
            )
            DataBindingAdapters.setIsVisible(dataBinding.totalTextview, data.isTotalVisible)
            DataBindingAdapters.setIsVisible(dataBinding.subtotalTextview, data.isTotalVisible)
            DataBindingAdapters.setIsVisible(dataBinding.subtotalValueTextview, data.isTotalVisible)
            dataBinding.subtotalValueTextview.text = data.refund
            DataBindingAdapters.setIsVisible(dataBinding.subtotalSeparator, data.isTotalVisible)
            DataBindingAdapters.setIsVisible(dataBinding.taxTextview, data.isTotalVisible)
            DataBindingAdapters.setIsVisible(dataBinding.taxValueTextview, data.isTotalVisible)
            dataBinding.taxValueTextview.text = data.tax
            DataBindingAdapters.setIsVisible(dataBinding.taxSeparator, data.isTotalVisible)
            DataBindingAdapters.setIsVisible(dataBinding.refundTextview, data.isTotalVisible)
            DataBindingAdapters.setIsVisible(dataBinding.refundValueTextview, data.isTotalVisible)
            dataBinding.refundValueTextview.text = data.subtotal
            DataBindingAdapters.setIsVisible(dataBinding.refundSeparator, data.isTotalVisible)
            DataBindingAdapters.setIsVisible(dataBinding.refundDisclaimer, data.isTotalVisible)
            dataBinding.refundDisclaimer.text =
                resources.getString(R.string.Subscription_Cancellation_Disclaimer, data.endDate)
        }

        dataBinding.cancelSubscriptionButton.setOnClickListener { viewModel.onCancelSubscriptionClicked() }

        val mediatorCancellation =
            MediatorLiveData<Pair<List<BindableSpinnerAdapter.SpinnerItem>?, BindableSpinnerAdapter.SpinnerItem?>>().apply {
                addSource(viewModel.cancellationReasons) { value = Pair(it, viewModel.cancellationReason.value) }
                addSource(viewModel.cancellationReason) { value = Pair(viewModel.cancellationReasons.value, it) }
            }

        mediatorCancellation.observe(this) { data ->
            BindableSpinnerAdapter.setSpinnerItems(dataBinding.cancellationReasonSpinner, data.first, data.second, null)
        }

        dataBinding.executePendingBindings()
        observeBaseEvents(viewModel)

        val defaultCancellationReasons =
            SubscriptionCancellationReasons(
                R.array.Subscription_cancellation_reasons,
                R.array.Subscription_cancellation_reason_codes,
            ).also { it.createReasonStrings(this) }

        performActivitySetup(dataBinding.toolbar)

        viewModel.apiCallStatus.observe(this, Observer { onApiCallResponse(it) })
        viewModel.cancellationReasonChanged.observe(this, Observer { /* empty observer */ })
        viewModel.populateView(
            EditSubscriptionData.createFromIntent(intent),
            defaultCancellationReasons,
        )

        viewModel.cancelSubscriptionNavigationEvents.observe(
            this,
            Observer {
                when (it) {
                    is CancelSubscriptionNavigationEvent.OnCancelSubscriptionClicked ->
                        DialogUtil.showDialog(
                            this,
                            null,
                            String.format(
                                getString(R.string.Subscription_cancellation_confirm),
                                it.subscriptionName,
                            ),
                            getString(R.string.Common_confirm),
                            getString(R.string.Common_cancel),
                            object :
                                OnCusDialogInterface {
                                override fun onConfirmClick() {
                                    viewModel.onCancelSubscription()
                                }

                                override fun onCancelClick() {
                                }
                            },
                            false,
                        )
                }
            },
        )
    }

    private fun onApiCallResponse(status: CancelSubscriptionStatus) {
        if (status.isSuccess) {
            when {
                status.isManualRefund -> {
                    val parameters =
                        CancellationSuccessRefundActivity.Parameters(
                            SubscriptionEditSuccess.CANCELLED_REFUND_PENDING,
                            viewModel.editSubscriptionData.cancellationDataPayload?.records,
                        )
                    startActivity(parameters.createIntent(this))
                }

                status.isRefundAvailable -> {
                    val parameters =
                        PaymentSuccessActivity.Parameters(
                            SubscriptionEditSuccess.CANCELLED,
                            viewModel.customerEmail,
                        )
                    startActivity(parameters.createIntent(this))
                }

                else -> {
                    val parameters =
                        PaymentSuccessActivity.Parameters(
                            SubscriptionEditSuccess.CANCELLED_NO_REFUND,
                            null,
                        )
                    startActivity(parameters.createIntent(this))
                }
            }
        } else {
            val parameters = PaymentFailedActivity.Parameters(SubscriptionEditFailure.CANCELLATION)
            startActivity(parameters.createIntent(this))
        }
    }
} // CancelSubscriptionActivity class
