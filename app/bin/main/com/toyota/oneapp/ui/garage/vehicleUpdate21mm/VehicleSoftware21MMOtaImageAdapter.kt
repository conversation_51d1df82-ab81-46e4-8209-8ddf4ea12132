package com.toyota.oneapp.ui.garage.vehicleUpdate21mm

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.DelegateOtaContentImagesBinding
import com.toyota.oneapp.extensions.KotlinExtensions.fetchUrl
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters

class VehicleSoftware21MMOtaImageAdapter : RecyclerView.Adapter<VehicleSoftware21MMOtaImageAdapter.OtaSoftwareUpdateViewHolder>() {
    private var otaContentList: List<String>? = null

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): OtaSoftwareUpdateViewHolder {
        val mDeveloperListItemBinding =
            DataBindingUtil.inflate<DelegateOtaContentImagesBinding>(
                LayoutInflater.from(parent.context),
                R.layout.delegate_ota_content_images,
                parent,
                false,
            )
        return OtaSoftwareUpdateViewHolder(mDeveloperListItemBinding)
    }

    override fun onBindViewHolder(
        holder: OtaSoftwareUpdateViewHolder,
        position: Int,
    ) {
        val item = otaContentList?.get(position)
        holder.delegateOtaContentImagesBinding.let {
            DataBindingAdapters.loadOtaContentImage(it.softwareImageUpdateOne, item?.fetchUrl())
        }
    }

    override fun getItemCount(): Int = otaContentList?.size ?: 0

    fun notifyDataSetChanged(otaContentList: List<String>) {
        this.otaContentList = otaContentList
        notifyDataSetChanged()
    }

    inner class OtaSoftwareUpdateViewHolder(
        val delegateOtaContentImagesBinding: DelegateOtaContentImagesBinding,
    ) : RecyclerView.ViewHolder(delegateOtaContentImagesBinding.root)
}
