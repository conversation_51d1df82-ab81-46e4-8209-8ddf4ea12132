package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model

import android.os.Parcelable
import com.toyota.oneapp.model.subscription.CY17CalculateTaxResponse
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.SubscriptionV2PaymentCY17Activity
import kotlinx.parcelize.Parcelize

/**
 * A Model - Used to hold result from [SubscriptionV2PaymentCY17Activity]
 */
@Parcelize
data class PaymentCY17Result(
    val paymentMethodId: String,
    val paymentAccessToken: String,
    val calculateTaxResponse: CY17CalculateTaxResponse,
) : Parcelable
