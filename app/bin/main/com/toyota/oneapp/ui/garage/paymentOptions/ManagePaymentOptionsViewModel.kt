package com.toyota.oneapp.ui.garage.paymentOptions

import androidx.databinding.ObservableBoolean
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.model.subscription.PaymentsResponse
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager.WebPaymentType
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.baseClasses.ConfirmationDialogData
import com.toyota.oneapp.ui.baseClasses.DialogData
import com.toyota.oneapp.ui.baseClasses.DialogEventCreator
import com.toyota.oneapp.ui.baseClasses.ToastDialogData
import com.toyota.oneapp.ui.garage.activity.WebPaymentArguments
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.Event
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class ManagePaymentOptionsViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val subscriptionApiManager: SubscriptionAPIManager,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel(),
        PaymentOptionsAdapter.ClickObserver,
        DialogEventCreator {
        private val dialogEventPoster: MediatorLiveData<Event<DialogData>> = MediatorLiveData()
        override val dialogEvent: LiveData<Event<DialogData>> = dialogEventPoster

        val creditCardOptions: MutableLiveData<OptionListData> = MutableLiveData()
        val bankAccountOptions: MutableLiveData<OptionListData> = MutableLiveData()
        val startNextActivity: MediatorLiveData<NextActivityData> = MediatorLiveData()
        val isInEditMode: MutableLiveData<Boolean> = MutableLiveData()
        var isACHEligible = ObservableBoolean(false)
        var isEditable = ObservableBoolean(false)
        var isPaymentMethodAvailable = ObservableBoolean(false)
        var isACHPaymentMethodAvailable = ObservableBoolean(false)

        private val addPaymentEvent = SingleLiveEvent<WebPaymentArguments>()
        val onAddPayment: LiveData<WebPaymentArguments> = addPaymentEvent

        fun onEditClick() {
            val newIsInEditModeValue = !(isInEditMode.value ?: false)
            val eventValue =
                if (newIsInEditModeValue) {
                    AnalyticsEventParam.ZUORA_EDIT_PAYMENT_METHOD_EDIT
                } else {
                    AnalyticsEventParam.ZUORA_EDIT_PAYMENT_METHOD_DONE
                }
            analyticsLogger.logEventWithParameter(AnalyticsEventParam.ZUORA_PAYMENT, eventValue)
            isInEditMode.postValue(newIsInEditModeValue)
            creditCardOptions.postValue(
                OptionListData(
                    newIsInEditModeValue,
                    creditCardOptions.value?.paymentOptions
                        ?: listOf(),
                ),
            )
            bankAccountOptions.postValue(
                OptionListData(
                    newIsInEditModeValue,
                    bankAccountOptions.value?.paymentOptions
                        ?: listOf(),
                ),
            )
        }

        fun onAddCreditCardClick() {
            applicationData.getSelectedVehicle()?.let {
                addPaymentEvent.value =
                    WebPaymentArguments(
                        WebPaymentType.CREDIT_CARD,
                        isPaymentMethodAvailable(),
                        creditCardOptions.value
                            ?.paymentOptions
                            ?.firstOrNull()
                            ?.accountId,
                        it.brand,
                        it.region,
                        it.generation,
                    )
            }
        }

        fun onAddBankAccountClick() {
            applicationData.getSelectedVehicle()?.let {
                addPaymentEvent.value =
                    WebPaymentArguments(
                        WebPaymentType.ACH,
                        isPaymentMethodAvailable(),
                        bankAccountOptions.value
                            ?.paymentOptions
                            ?.firstOrNull()
                            ?.accountId,
                        it.brand,
                        it.region,
                        it.generation,
                    )
            }
        }

        fun populatePaymentMethods() {
            if (applicationData.getSelectedVehicle() != null &&
                applicationData.getSelectedVehicle()!!.isFeatureEnabled(
                    Feature.ACH_PAYMENT,
                ) &&
                (
                    ToyotaConstants.REGION_US.equals(applicationData.getSelectedVehicle()!!.region, true) ||
                        ToyotaConstants.REGION_HI.equals(
                            applicationData.getSelectedVehicle()!!.region,
                            true,
                        )
                )
            ) {
                isACHEligible.set(true)
            }

            showProgress()
            subscriptionApiManager.sendGetPayments(
                object : BaseCallback<PaymentsResponse>() {
                    override fun onSuccess(response: PaymentsResponse) {
                        val records = response.payload.records ?: arrayListOf()

                        val cardOptions = mutableListOf<PaymentRecord>()
                        val bankOptions = mutableListOf<PaymentRecord>()

                        val recordExists = records.isNotEmpty()
                        isACHPaymentMethodAvailable.set(
                            records.any { PaymentOption.selectType(it) == PaymentOption.Type.BANK_ACCOUNT },
                        )
                        isPaymentMethodAvailable.set(recordExists)
                        if (recordExists) {
                            isEditable.set(recordExists)
                        } else {
                            isInEditMode.postValue(recordExists)
                            isEditable.set(recordExists)
                        }

                        for (paymentRecord in records) {
                            when (PaymentOption.selectType(paymentRecord)) {
                                PaymentOption.Type.CREDIT_CARD -> cardOptions.add(paymentRecord)
                                PaymentOption.Type.BANK_ACCOUNT -> bankOptions.add(paymentRecord)
                            }
                        }
                        analyticsLogger.logEvent(AnalyticsEvent.ZUORA_GET_PAYMENT_METHODS_SUCCESS)

                        creditCardOptions.postValue(
                            OptionListData(
                                isInEditMode.value
                                    ?: false,
                                cardOptions,
                            ),
                        )
                        bankAccountOptions.postValue(
                            OptionListData(
                                isInEditMode.value
                                    ?: false,
                                bankOptions,
                            ),
                        )
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        analyticsLogger.logEvent(AnalyticsEvent.ZUORA_GET_PAYMENT_METHODS_FAILED)
                        showErrorMessage(errorMsg)
                    }

                    override fun onComplete() {
                        super.onComplete()
                        hideProgress()
                    }
                },
            )
        }

        fun deletePaymentMethod(paymentRecord: PaymentRecord) {
            showProgress()
            subscriptionApiManager.deletePaymentMethod(
                paymentRecord,
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        analyticsLogger.logEventWithParameter(
                            AnalyticsEventParam.ZUORA_PAYMENT,
                            AnalyticsEventParam.ZUORA_DELETE_PAYMENT_METHOD_SUCCESS,
                        )
                        dialogEventPoster.postValue(
                            Event(
                                ToastDialogData(
                                    R.string.SubscriptionPaymentOptions_remove_payment_success_message,
                                ),
                            ),
                        )
                        populatePaymentMethods()
                    }

                    override fun onComplete() {
                        hideProgress()
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        analyticsLogger.logEventWithParameter(
                            AnalyticsEventParam.ZUORA_PAYMENT,
                            AnalyticsEventParam.ZUORA_DELETE_PAYMENT_METHOD_FAILED,
                        )
                        showErrorMessage(errorMsg)
                    }
                },
            )
        }

        private fun isPaymentMethodAvailable(): Boolean {
            val isCreditCardOptionsEmpty =
                creditCardOptions.value
                    ?.paymentOptions
                    .orEmpty()
                    .isEmpty()
            val isBankAccountOptionsEmpty =
                bankAccountOptions.value
                    ?.paymentOptions
                    .orEmpty()
                    .isEmpty()
            return !(isCreditCardOptionsEmpty && isBankAccountOptionsEmpty)
        }

        override fun addOptionClickSource(editClickSource: LiveData<PaymentRecord>) {
            if (isInEditMode.value != true) {
                startNextActivity.addSource(editClickSource) {
                    if (it.id != null) {
                        startNextActivity.postValue(
                            NextActivityData(
                                token = it.id,
                                defaultPayment = it.defaultPaymentMethod ?: false,
                                paymentRecord = it,
                            ),
                        )
                    }
                }
            }
        }

        override fun addDeleteClickSource(deleteClickSource: LiveData<PaymentRecord>) {
            dialogEventPoster.addSource(deleteClickSource) {
                val confirmationDialogData =
                    ConfirmationDialogData(
                        textResId = R.string.SubscriptionPaymentOptions_remove_payment_message,
                        confirmTextResId = R.string.Common_remove,
                        confirmAction = {
                            analyticsLogger.logEventWithParameter(
                                AnalyticsEventParam.ZUORA_PAYMENT,
                                AnalyticsEventParam.ZUORA_DELETE_PAYMENT_METHOD,
                            )
                            deletePaymentMethod(it)
                        },
                    )
                dialogEventPoster.postValue(Event(confirmationDialogData))
            }
        }

        data class NextActivityData(
            val token: String,
            val defaultPayment: Boolean,
            val paymentRecord: PaymentRecord? = null,
        )

        data class OptionListData(
            val isInEditMode: Boolean,
            val paymentOptions: List<PaymentRecord>,
        )
    } // ManagePaymentOptionsViewModel class
