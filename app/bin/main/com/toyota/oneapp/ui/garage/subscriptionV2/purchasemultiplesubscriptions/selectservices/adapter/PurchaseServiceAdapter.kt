package com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.text.Spannable
import android.text.style.TextAppearanceSpan
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.text.toSpannable
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemPurchaseServiceExternalServiceBinding
import com.toyota.oneapp.databinding.ItemPurchaseServiceHeaderBinding
import com.toyota.oneapp.databinding.ItemPurchaseServiceToyotaServiceBinding
import com.toyota.oneapp.databinding.ItemSubscriptionBundleBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.extensions.clearAndAdd
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.common.subscriptionbundle.IndividualServiceAdapter
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.selectservices.model.PurchaseServiceListItemUIModel
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter
import toyotaone.commonlib.recyclerview.decorator.SectionIdentifier

class PurchaseServiceAdapter(
    private val context: Context,
    private val vehicleInfo: VehicleInfo,
    private val listener: PurchaseServiceAdapterListener,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>(),
    BindableRecyclerViewAdapter<PurchaseServiceListItemUIModel> {
    private val items: MutableList<PurchaseServiceListItemUIModel> =
        mutableListOf<PurchaseServiceListItemUIModel>().toMutableList()

    companion object {
        const val ITEM_TYPE_HEADER = 1
        const val ITEM_TYPE_TOYOTA_SERVICE = 2
        const val ITEM_TYPE_EXTERNAL_SERVICE = 3
        const val ITEM_SUBSCRIPTION_BUNDLE = 4
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder =
        when (viewType) {
            ITEM_TYPE_HEADER -> {
                val binding =
                    ItemPurchaseServiceHeaderBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false,
                    )
                HeaderViewHolder(binding)
            }
            ITEM_TYPE_TOYOTA_SERVICE -> {
                val binding =
                    ItemPurchaseServiceToyotaServiceBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false,
                    )
                ToyotaServiceViewHolder(binding)
            }
            ITEM_TYPE_EXTERNAL_SERVICE -> {
                val binding =
                    ItemPurchaseServiceExternalServiceBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false,
                    )
                ExternalServiceViewHolder(binding)
            }
            ITEM_SUBSCRIPTION_BUNDLE -> {
                val binding =
                    ItemSubscriptionBundleBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false,
                    )
                SubscriptionBundleViewHolder(binding)
            }
            else -> {
                throw Exception("Invalid ViewType")
            }
        }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        when (holder.itemViewType) {
            ITEM_TYPE_HEADER -> {
                val headerVH = holder as PurchaseServiceAdapter.HeaderViewHolder
                val header = items[position] as PurchaseServiceListItemUIModel.Header
                headerVH.bind(header.title)
            }
            ITEM_TYPE_TOYOTA_SERVICE -> {
                val toyotaServiceVH = holder as PurchaseServiceAdapter.ToyotaServiceViewHolder
                val toyotaService = items[position] as PurchaseServiceListItemUIModel.Service
                toyotaServiceVH.bind(toyotaService)
            }
            ITEM_TYPE_EXTERNAL_SERVICE -> {
                val externalServiceVH = holder as PurchaseServiceAdapter.ExternalServiceViewHolder
                val externalService = items[position] as PurchaseServiceListItemUIModel.ExternalService
                externalServiceVH.bind(externalService)
            }
            ITEM_SUBSCRIPTION_BUNDLE -> {
                val subscriptionBundleVH = holder as PurchaseServiceAdapter.SubscriptionBundleViewHolder
                val subscriptionBundle = items[position] as PurchaseServiceListItemUIModel.SubscriptionBundle
                subscriptionBundleVH.bind(subscriptionBundle)
            }
        }
    }

    override fun getItemCount(): Int = items.size

    override fun getItemViewType(position: Int): Int =
        when (items[position]) {
            is PurchaseServiceListItemUIModel.Header -> ITEM_TYPE_HEADER
            is PurchaseServiceListItemUIModel.Service -> ITEM_TYPE_TOYOTA_SERVICE
            is PurchaseServiceListItemUIModel.ExternalService -> ITEM_TYPE_EXTERNAL_SERVICE
            is PurchaseServiceListItemUIModel.SubscriptionBundle -> ITEM_SUBSCRIPTION_BUNDLE
        }.exhaustive

    // BindableRecyclerViewAdapter Methods.
    @SuppressLint("NotifyDataSetChanged")
    override fun setData(data: List<PurchaseServiceListItemUIModel>?) {
        items.clearAndAdd(data ?: emptyList())
        notifyDataSetChanged()
    }

    inner class HeaderViewHolder(
        private val binding: ItemPurchaseServiceHeaderBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ),
        SectionIdentifier {
        fun bind(title: String) {
            binding.title = title
        }

        override fun isSection(): Boolean = true
    }

    inner class ToyotaServiceViewHolder(
        private val binding: ItemPurchaseServiceToyotaServiceBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ),
        SectionIdentifier {
        init {
            binding.cbCheck.setOnClickListener {
                val service = items[adapterPosition] as PurchaseServiceListItemUIModel.Service
                listener.onToyotaServiceCheckboxClicked(service)
            }
            binding.ivArrow.setOnClickListener {
                val service = items[adapterPosition] as PurchaseServiceListItemUIModel.Service
                listener.onToyotaServiceMoreIconClicked(service)
            }
            itemView.setOnClickListener {
                val service = items[adapterPosition] as PurchaseServiceListItemUIModel.Service
                listener.onToyotaServiceClicked(service)
            }
        }

        fun bind(service: PurchaseServiceListItemUIModel.Service) {
            binding.service = service
        }

        override fun isSection(): Boolean = false
    }

    inner class SubscriptionBundleViewHolder(
        private val binding: ItemSubscriptionBundleBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        init {
            itemView.setOnClickListener {
                val bundle = items[adapterPosition] as PurchaseServiceListItemUIModel.SubscriptionBundle
                listener.onSubscriptionBundleClicked(bundle)
            }

            binding.itemBundleCheckbox.setOnClickListener {
                val bundle = items[adapterPosition] as PurchaseServiceListItemUIModel.SubscriptionBundle
                listener.onSubscriptionBundleRadioButtonClicked(bundle)
            }

            binding.imgChevron.setOnClickListener {
                val bundle = items[adapterPosition] as PurchaseServiceListItemUIModel.SubscriptionBundle
                listener.onSubscriptionBundleChevronClicked(bundle)
            }
        }

        fun bind(bundle: PurchaseServiceListItemUIModel.SubscriptionBundle) {
            val individualServiceAdapter = IndividualServiceAdapter(vehicleInfo, bundle.services)
            binding.rvBundleServices.run {
                layoutManager = LinearLayoutManager(itemView.context)
                adapter = individualServiceAdapter
                addItemDecoration(DividerItemDecoration(itemView.context, LinearLayout.VERTICAL))
            }
            binding.subscriptionBundle = bundle
        }
    }

    inner class ExternalServiceViewHolder(
        private val binding: ItemPurchaseServiceExternalServiceBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ),
        SectionIdentifier {
        init {
            itemView.setOnClickListener {
                val service = items[adapterPosition] as PurchaseServiceListItemUIModel.ExternalService
                listener.onExternalServiceClicked(service)
            }
        }

        fun bind(service: PurchaseServiceListItemUIModel.ExternalService) {
            val provider = service.provider.orEmpty()
            val subtitle =
                context.getString(
                    R.string.third_party_service_subtitle,
                    service.provider,
                )
            val subtitleSpan = subtitle.toSpannable()
            subtitleSpan.setSpan(
                TextAppearanceSpan(context, com.toyota.one_ui.R.style.TextAppearance_OneUi_Caption1),
                0,
                subtitle.indexOf(provider) - 1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE,
            )
            binding.tvTitle.text = service.title
            binding.tvSubTitle.isVisible = service.provider != null
            binding.tvSubTitle.text = subtitleSpan
            binding.ivArrow.contentDescription = service.provider
        }

        override fun isSection(): Boolean = false
    }

    interface PurchaseServiceAdapterListener {
        fun onToyotaServiceCheckboxClicked(service: PurchaseServiceListItemUIModel.Service)

        fun onToyotaServiceClicked(service: PurchaseServiceListItemUIModel.Service)

        fun onToyotaServiceMoreIconClicked(service: PurchaseServiceListItemUIModel.Service)

        fun onSubscriptionBundleRadioButtonClicked(bundle: PurchaseServiceListItemUIModel.SubscriptionBundle)

        fun onSubscriptionBundleClicked(bundle: PurchaseServiceListItemUIModel.SubscriptionBundle)

        fun onSubscriptionBundleChevronClicked(bundle: PurchaseServiceListItemUIModel.SubscriptionBundle)

        fun onExternalServiceClicked(service: PurchaseServiceListItemUIModel.ExternalService)
    }
}
