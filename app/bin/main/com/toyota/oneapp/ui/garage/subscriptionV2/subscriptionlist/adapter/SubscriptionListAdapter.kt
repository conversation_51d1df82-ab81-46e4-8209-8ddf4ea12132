package com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemSubscriptionListHeaderBinding
import com.toyota.oneapp.databinding.ItemSubscriptionListServiceBinding
import com.toyota.oneapp.ui.garage.subscriptionV2.subscriptionlist.model.SubscriptionListItemUIModel
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter
import toyotaone.commonlib.recyclerview.decorator.SectionIdentifier

/**
 * Adapter - Used to populate SubscriptionList.
 */
class SubscriptionListAdapter(
    private var items: List<SubscriptionListItemUIModel>,
    private val onItemCLickListener: OnItemClickListener,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>(),
    BindableRecyclerViewAdapter<SubscriptionListItemUIModel> {
    companion object {
        const val ITEM_TYPE_HEADER = 1
        const val ITEM_TYPE_SERVICE = 2
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder =
        if (viewType == ITEM_TYPE_HEADER) {
            val binding =
                ItemSubscriptionListHeaderBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                )
            HeaderViewHolder(binding)
        } else {
            val binding =
                ItemSubscriptionListServiceBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                )
            ServiceViewHolder(binding)
        }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        if (holder.itemViewType == ITEM_TYPE_HEADER) {
            val headerVH = holder as HeaderViewHolder
            val header = items[position] as SubscriptionListItemUIModel.Header
            headerVH.bind(header.title)
        } else {
            val itemVH = holder as ServiceViewHolder
            val service = items[position] as SubscriptionListItemUIModel.Service
            itemVH.bind(service)
        }
    }

    override fun getItemCount(): Int = items.size

    override fun getItemViewType(position: Int): Int =
        when (items[position]) {
            is SubscriptionListItemUIModel.Header -> ITEM_TYPE_HEADER
            else -> ITEM_TYPE_SERVICE
        }

    override fun setData(data: List<SubscriptionListItemUIModel>?) {
        items = data ?: emptyList()
        notifyDataSetChanged()
    }

    inner class HeaderViewHolder(
        private val binding: ItemSubscriptionListHeaderBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ),
        SectionIdentifier {
        fun bind(title: String) {
            binding.title = title
        }

        override fun isSection(): Boolean = true
    }

    inner class ServiceViewHolder(
        private val binding: ItemSubscriptionListServiceBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ),
        SectionIdentifier {
        init {
            itemView.setOnClickListener {
                val service = items[adapterPosition] as SubscriptionListItemUIModel.Service
                onItemCLickListener.onServiceItemClick(service)
            }
        }

        fun bind(service: SubscriptionListItemUIModel.Service) {
            binding.containerLayout.isClickable = service.isActionable
            binding.ivIcon.setImageResource(service.icon)
            binding.tvTitle.text = service.title
            binding.ivArrow.isVisible = service.isActionable
            binding.tvSubTitle.text = service.subTitle
            binding.tvSubTitle.isVisible = !service.isHideSubTitle
        }

        override fun isSection(): Boolean = false
    }

    interface OnItemClickListener {
        fun onServiceItemClick(service: SubscriptionListItemUIModel.Service)
    }
}
