package com.toyota.oneapp.ui

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.extensions.createVehicleInfo
import com.toyota.oneapp.features.core.util.Constants.FLEET_SB1394
import com.toyota.oneapp.model.pref.LanguagePreferenceModel
import com.toyota.oneapp.model.vehicle.VehicleDetailPayload
import com.toyota.oneapp.model.vehicle.VehicleDetailResponse
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.VehicleDetailRepository
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.viewmodel.model.LexusUpdateModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class AddVINManualEntryViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val vehicleDetailRepository: VehicleDetailRepository,
        private val languagePreferenceModel: LanguagePreferenceModel,
    ) : BaseViewModel() {
        companion object {
            const val FLEET_PRE_17CY = "VS-ERROR-PRE17CY"
            const val FLEET_17CY = "VS-ERROR-17CY"
            const val FLEET_17CY_PLUS = "VS-ERROR-17CYPLUS"
            const val FLEET_NON_CV = "VS-ERROR-null"
            const val FLEET_21MM = "VS-ERROR-21MM"
        }

        private val mVehicleDetailResponse = MutableLiveData<VehicleDetailPayload>()
        val vehicleDetailResponse: LiveData<VehicleDetailPayload> get() = mVehicleDetailResponse

        private val mBlockFleetVehicle = MutableLiveData<String>()
        val blockFleetVehicle: LiveData<String> get() = mBlockFleetVehicle

        var californiaSenateBill = false

        private val mShowQRScanInstruction = MutableLiveData<Boolean>()
        val showQRScanInstruction: LiveData<Boolean> get() = mShowQRScanInstruction

        val showUpdateAvailable = SingleLiveEvent<Unit>()

        /**
         * Make the service call to get the vehicle details with the VIN
         * @param context
         * @param vinNumber
         */
        fun getVehicleInfo(
            context: Context,
            vinNumber: String,
        ) {
            if (!applicationData.getVehicleList().isNullOrEmpty() &&
                applicationData.getVehicleList()?.any {
                    it.vin.equals(
                        vinNumber,
                        false,
                    )
                } == true
            ) {
                showErrorMessage(R.string.vechile_already_added_to_your_account)
                return
            }
            showProgress()
            viewModelScope.launch {
                val resource = vehicleDetailRepository.getVehicleDetail(vinNumber)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.let { processCarDetails(it) }
                    }
                    else -> {
                        if (resource.error?.responseCode.equals(FLEET_PRE_17CY, true) ||
                            resource.error?.responseCode?.contains(FLEET_SB1394, ignoreCase = true) == true ||
                            resource.error?.responseCode.equals(FLEET_17CY, true) ||
                            resource.error?.responseCode.equals(FLEET_17CY_PLUS, true) ||
                            resource.error?.responseCode.equals(FLEET_NON_CV, true) ||
                            resource.error?.responseCode.equals(FLEET_21MM, true)
                        ) {
                            // Show block fleet screen
                            if (resource.error?.responseCode?.contains(FLEET_SB1394, ignoreCase = true) == true) {
                                mBlockFleetVehicle.postValue(resource.error?.message)
                                californiaSenateBill = true
                            } else {
                                mBlockFleetVehicle.postValue(resource.error?.message)
                            }
                        } else if (ToyotaConstants.APP_UPDATE_ERROR.equals(
                                resource.error?.responseCode,
                                true,
                            )
                        ) {
                            showUpdateAvailable.call()
                        } else {
                            resource.error?.message?.let {
                                showErrorMessage(
                                    if (ToyotaConstants.PROCESS_ERROR == it) {
                                        context.getString(R.string.Vehicle_Details_Process_Error)
                                    } else {
                                        it
                                    },
                                )
                            }
                        }
                    }
                }
            }
        }

        /**
         * @param carDetails
         */
        private fun processCarDetails(carDetails: VehicleDetailResponse?) {
            carDetails?.payload?.vehicle?.let {
                applicationData.generationCode = it.generation
                applicationData.saveVehicleToList(it)
                applicationData.setSelectedVehicle(
                    carDetails.payload.createVehicleInfo(
                        languagePreferenceModel,
                    ),
                )
                mSetLexusGeneration.postValue(
                    LexusUpdateModel(!carDetails.payload.vehicle.isToyotaBrand, false),
                )
                mVehicleDetailResponse.postValue(carDetails.payload)
                return
            }
            showErrorMessage(R.string.generic_error)
        }
    }
