package com.toyota.oneapp.ui

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.toyota.oneapp.R
import com.toyota.oneapp.model.vehicle.VehicleSkinType
import com.toyota.oneapp.network.Resource
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.eventbus.RxBus

/**
 * This class used to create BottomSheetDialogFragment. It is like BaseViewModelFragment.
 */
abstract class BaseBottomSheetDialogFragment :
    BottomSheetDialogFragment(),
    BasePresenter.BaseView {
    private var mProgressDialog: Dialog? = null

    fun observeBaseEvents(vm: BaseViewModel) {
        (requireActivity() as? BaseActivity)?.observeBaseEvents(vm)
    }

    open fun <T> onNetworkStateChange(resource: Resource<T>?) {
        when (resource) {
            is Resource.Loading -> showProgressDialog()
            is Resource.Success -> hideProgressDialog()
            else -> {
                hideProgressDialog()
                showDialog(resource?.message)
            }
        }
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        mProgressDialog = DialogUtil.createProgressDialog(activity)
    }

    open fun showDialog() {
        DialogUtil.showDialog(
            activity,
            getString(R.string.Login_error),
            getString(R.string.generic_error),
            getString(R.string.Common_ok),
        )
    }

    override fun showProgressDialog() {
        if (mProgressDialog != null) {
            mProgressDialog!!.show()
        }
    }

    override fun hideProgressDialog() {
        if (mProgressDialog != null) {
            mProgressDialog!!.dismiss()
        }
    }

    override fun showUnsupportedDemoDialog() {
        DialogUtil.showDialog(
            activity,
            null,
            getString(R.string.common_not_support_in_demo),
            getString(R.string.Common_ok),
        )
    }

    override fun showGenericErrorMessage() {
        showDialog()
    }

    override fun showDialog(errorMsg: String?) {
        if (errorMsg != null && !errorMsg.isEmpty()) {
            DialogUtil.showDialog(
                activity,
                getString(R.string.Login_error),
                errorMsg,
                getString(R.string.Common_ok),
            )
        } else {
            showDialog()
        }
    }

    override fun showMessage(errorMsg: String?) {
        if (errorMsg != null && !errorMsg.isEmpty()) {
            DialogUtil.showDialog(
                activity,
                null,
                getString(R.string.common_not_support_in_demo),
                getString(R.string.Common_ok),
            )
        } else {
            showDialog()
        }
    }

    override fun showProxyDetectedAlert() {}

    override fun setLexusGeneration(
        isLexus: Boolean,
        forceUpdate: Boolean,
    ) {
        RxBus.get().post(VehicleSkinType(isLexus, forceUpdate))
    }

    override fun getActivityContext(): Context? = activity

    open fun onNextButtonHandler(): Boolean = true
}
