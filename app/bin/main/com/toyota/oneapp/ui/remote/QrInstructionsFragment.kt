package com.toyota.oneapp.ui.remote

import android.content.Intent
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ImageSpan
import android.view.View
import androidx.core.content.ContextCompat
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentQrInstructionBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.vinscan.QRScanActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class QrInstructionsFragment : BaseDataBindingFragment<FragmentQrInstructionBinding>() {
    private var stepNum = 0

    companion object {
        val TAG = QrInstructionsFragment::class.java.simpleName

        fun newInstance() = QrInstructionsFragment()
    }

    private fun setupUI() {
        viewDataBinding.apply {
            continueBtn.setOnClickListener { updateUiBaseOnPage() }
            scanQrBtn.setOnClickListener { showQrScan(true) }
            enterCodeBtn.setOnClickListener { showQrScan(false) }
        }

        updateUiBaseOnPage()
    }

    private fun updateUiBaseOnPage() {
        viewDataBinding.apply {
            stepView.text = getString(R.string.RemoteActivation_step_text, stepNum + 1)

            when (stepNum) {
                0 -> {
                    ivHeadUnitSettings.setImageResource(R.drawable.settings_remote)
                    vStepFirst.setBackgroundResource(R.color.code_edit_text_background)
                    val str = getString(R.string.vehicle_settings_info)
                    val index = str.indexOf("(")
                    val spannable = SpannableString(str)

                    activity?.let { ContextCompat.getDrawable(it, R.drawable.settings_icon) }?.let { d ->
                        d.setBounds(0, 0, d.intrinsicWidth, d.intrinsicHeight)
                        val span = ImageSpan(d)
                        spannable.setSpan(
                            span,
                            index + 1,
                            index + 2,
                            Spannable.SPAN_INCLUSIVE_EXCLUSIVE,
                        )
                        this.tvVerificationDes.text = spannable
                    }
                    stepNum++
                }
                1 -> {
                    tvVerifyHeading.text = getString(R.string.remoteActivation_link_vehicle)
                    ivHeadUnitSettings.setImageResource(R.drawable.settings_scroll)
                    vStepSecond.setBackgroundResource(R.color.code_edit_text_background)
                    tvVerificationDes.text = getString(R.string.remote_link_profile_sub)
                    stepNum++
                }
                2 -> {
                    tvVerifyHeading.text = getString(R.string.remoteActivation_access_qr)
                    ivHeadUnitSettings.setImageResource(R.drawable.remote_activation_with_app)
                    vStepThird.setBackgroundResource(R.color.code_edit_text_background)
                    tvVerificationDes.text = getString(R.string.remote_linked_vehicle)
                    stepNum++
                }
                3 -> {
                    tvVerifyHeading.text = getString(R.string.remoteActivation_qr_access)
                    ivHeadUnitSettings.setImageResource(R.drawable.settings_apps_scanqr_code)
                    vStepFour.setBackgroundResource(R.color.code_edit_text_background)
                    tvVerifySubHeading.text = getString(R.string.RemoteActivation_use_mobile_text)
                    tvVerificationDes.text = getString(R.string.remote_access_qr_code)
                    continueBtn.visibility = View.GONE
                    scanQrBtn.visibility = View.VISIBLE
                    enterCodeBtn.visibility = View.VISIBLE
                }
            }
        }
    }

    private fun showQrScan(showQr: Boolean) {
        activity?.startActivity(
            Intent(activity, QRScanActivity::class.java).apply {
                putExtra(QRScanActivity.REMOTE_AUTH, true)
                putExtra(QRScanActivity.SHOW_QR_CODE, showQr)
            },
        )
    }

    override fun onViewBound(
        binding: FragmentQrInstructionBinding,
        savedInstance: Bundle?,
    ) {
        setupUI()
    }

    override fun getLayout(): Int = R.layout.fragment_qr_instruction
}
