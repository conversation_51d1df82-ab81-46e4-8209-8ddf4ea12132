package com.toyota.oneapp.ui.remote

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityRemoteActivationBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.vinscan.QRScanActivity
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class RemoteActivationActivity : UiBaseActivity() {
    companion object {
        val TAG = RemoteActivationActivity::class.java.simpleName
    }

    @Inject
    lateinit var applicationData: ApplicationData

    lateinit var binding: ActivityRemoteActivationBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_remote_activation)
        binding.qrInstrctionsBtn.setOnClickListener {
            binding.remoteInfoLl.visibility = View.GONE
            replaceFragment(QrInstructionsFragment.newInstance(), QrInstructionsFragment.TAG)
        }
        binding.skipQrBtn.setOnClickListener {
            showQrScan()
        }

        binding.remoteInfoAppbarToolbar.setOnClickListener {
            finish()
        }
        if (applicationData.getSelectedVehicle()?.isRemoteOnlyUser == true) {
            updateGuestUserDetails()
        }
    }

    private fun showQrScan() {
        startActivity(
            Intent(this, QRScanActivity::class.java).apply {
                putExtra(
                    QRScanActivity.REMOTE_AUTH,
                    true,
                )
            },
        )
    }

    private fun updateGuestUserDetails() {
        binding.remoteInfoAppbarToolbar.title = getString(R.string.guest_remote_title)
        binding.tvVerifyHeadingSub.text = getString(R.string.remoteActivation_verify_access_sub)
        binding.tvVerifyHeading.text = getString(R.string.guest_remote_title)
    }

    private fun replaceFragment(
        fragment: Fragment,
        tag: String,
    ) {
        if (applicationData.getSelectedVehicle()?.isRemoteOnlyUser == true) {
            binding.remoteInfoAppbarToolbar.title = getString(R.string.guest_verication_title)
        } else {
            binding.remoteInfoAppbarToolbar.title = getString(R.string.remoteActivation_active)
        }
        supportFragmentManager.beginTransaction().replace(R.id.guest_verification_fl, fragment, tag).commit()
    }
}
