package com.toyota.oneapp.ui.dealerInteractions.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.MediatorLiveData
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemDealerBinding
import com.toyota.oneapp.ui.dealerInteractions.DealerSelectedListener
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters

class DealerLocationItemAdapter(
    clickListener: DealerSelectedListener,
) : BaseDealerLocationItemAdapter(
        clickListener,
    ) {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): BaseDealerLocationItemViewHolder {
        val binding: ItemDealerBinding =
            DataBindingUtil.inflate(
                LayoutInflater.from(parent.context),
                R.layout.item_dealer,
                parent,
                false,
            )
        return ViewHolder(binding)
    }

    inner class ViewHolder(
        private val itemDealerBinding: ItemDealerBinding,
    ) : BaseDealerLocationItemViewHolder(
            itemDealerBinding,
        ) {
        override fun setViewModel(newViewModel: DealerLocationItemViewModel) {
            itemDealerBinding.itemDealerRoot.setOnClickListener {
                newViewModel.onClick()
            }
            newViewModel.dealerName.observeForever {
                itemDealerBinding.tvDealerName.text = it
            }
            MediatorLiveData<Pair<Float?, String?>>()
                .apply {
                    addSource(newViewModel.distanceToDealer) { value = it to value?.second }
                    addSource(newViewModel.distanceToDealerUnit) { value = value?.first to it }
                }.observeForever { distancePair ->
                    itemDealerBinding.tvDealerProximityMiles.text =
                        itemView.context.getString(
                            R.string.aa_dealer_distance_format,
                            distancePair.first,
                            distancePair.second,
                        )
                }
            newViewModel.distanceToDealerVisible.observeForever {
                DataBindingAdapters.setIsVisible(itemDealerBinding.tvDealerProximityMiles, it)
            }
            newViewModel.dealerAddress.observeForever {
                itemDealerBinding.tvDealerAddress.text = it
            }
            newViewModel.isDealerSelected.observeForever {
                itemDealerBinding.ivSelected.isChecked = it
            }
        }
    }
}
