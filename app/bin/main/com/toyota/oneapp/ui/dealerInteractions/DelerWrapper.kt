package com.toyota.oneapp.ui.dealerInteractions

import com.google.android.gms.maps.model.BitmapDescriptor
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.toyota.oneapp.R
import com.toyota.oneapp.model.dealer.Dealer
import toyotaone.commonlib.map.IGoogleMapManager
import toyotaone.commonlib.map.IGoogleMapPinItem

class DealerWrapper(
    val dealer: Dealer,
    val number: Int,
    private val isToyotaBrand: Boolean,
    private val useNumberForMapPins: Boolean,
    private val mapManager: IGoogleMapManager<DealerWrapper>?,
) : IGoogleMapPinItem {
    override val id: String = dealer.toyotaCode ?: ""
    override val bitmapDescriptor: BitmapDescriptor?
        get() {
            val drawableId = if (isToyotaBrand) R.drawable.ic_map_icon_dealer else R.drawable.ic_map_icon_dealer_lexus
            val bitmap = mapManager?.resolveResourceBitmap(drawableId)
            return bitmap?.let { BitmapDescriptorFactory.fromBitmap(it) }
        }
    override val bitmapDescriptorSelected: BitmapDescriptor?
        get() {
            val drawableId = if (isToyotaBrand) R.drawable.ic_map_icon_dealer else R.drawable.ic_map_icon_dealer_lexus
            val bitmap = mapManager?.resolveResourceBitmap(drawableId)
            return bitmap?.let { BitmapDescriptorFactory.fromBitmap(it) }
        }
    override val latitude: Double =
        if (!dealer.addresses.isNullOrEmpty()) {
            dealer.addresses
                ?.get(0)
                ?.coordinate
                ?.latitude ?: 0.0
        } else {
            0.0
        }
    override val longitude: Double =
        if (!dealer.addresses.isNullOrEmpty()) {
            dealer.addresses
                ?.get(0)
                ?.coordinate
                ?.longitude ?: 0.0
        } else {
            0.0
        }
    override val isPin: Boolean = true
}
