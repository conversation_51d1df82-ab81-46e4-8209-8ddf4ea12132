package com.toyota.oneapp.ui.dealerInteractions

import android.app.Activity
import android.content.Intent
import android.location.Location
import android.view.KeyEvent
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.lifecycle.MutableLiveData
import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.dashboard.OdometerDetailsResponse
import com.toyota.oneapp.model.dealer.Dealer
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.api.manager.DealerScheduleAPIManager
import com.toyota.oneapp.network.api.manager.TelemetryAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.AddVehicleInfoActivity
import com.toyota.oneapp.ui.dealerInteractions.adapters.DealerLocationItemAdapter
import com.toyota.oneapp.util.StringUtil.capitalizeFirstChar
import com.toyota.oneapp.util.ToyUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class SelectPreferredDealerViewModel
    @Inject
    constructor(
        private val telemetryAPIManager: TelemetryAPIManager,
        dealerScheduleAPIManager: DealerScheduleAPIManager,
        applicationData: ApplicationData,
        analyticsLogger: AnalyticsLogger,
        adapterFactory: SelectPreferredDealerAdaptersFactory,
    ) : DealerInformationViewModel(dealerScheduleAPIManager, applicationData, analyticsLogger) {
        override val mDealersAdapter: DealerLocationItemAdapter =
            adapterFactory.createDealerAdapter(
                this,
            )

        override val vehicle: VehicleInfo? get() = applicationData.getSelectedVehicle()

        private var mCarLocation: Location? = null

        val listMode = MutableLiveData<Boolean>(true)
        val dealerDetailBottomSheetVisible = MutableLiveData<Boolean>()
        val mapControlsVisible = MutableLiveData<Boolean>()

        val modeToggleButtonResource = MutableLiveData<Int>(R.drawable.icon_pd_map)

        val dealerInfoCardName = MutableLiveData<String>()
        val dealerInfoCardAddress = MutableLiveData<String>()
        val dealerInfoCardPhoneNumber = MutableLiveData<String>()
        val dealerInfoCardDistance = MutableLiveData<Float>()
        val dealerInfoCardDistanceUnit = MutableLiveData<String>()
        val dealerInfoCardWebsiteVisible = MutableLiveData<Boolean>()
        val dealerInfoCardCallDealerObservable = SingleLiveEvent<String>()
        val dealerInfoCardNavigateToWebsiteObservable = SingleLiveEvent<String>()

        val mapCarLocButtonSelected = MutableLiveData<Boolean>()
        val mapUserLocButtonSelected = MutableLiveData<Boolean>()

        override val mUseNumberForMapPins: Boolean = false

        fun currentLocationSearchBoxClicked() {
            analyticsLogger.logEvent(AnalyticsEvent.PREFFERED_SERVICING_DEALER_CURRENT_LOCATION)
            currentLocationClicked()
        }

        fun currentLocationClicked() {
            hideDealerDetailsCard()
            getCurrentLocAndDealersList()
        }

        override fun showDealersList(dealers: List<Dealer>?) {
            hideDealerDetailsCard()
            super.showDealersList(dealers)
        }

        override fun setSelectedDealer(dealer: Dealer?) {
            super.setSelectedDealer(dealer)

            if (dealer == null) {
                return
            }

            // ListMode goes straight to final select, map mode shows a card
            if (listMode.value == true) {
                finalSelectPreferredDealer(dealer)
            } else {
                dealerDetailBottomSheetVisible.value = true
                mapControlsVisible.value = false
                dealerInfoCardName.value = dealer.dealershipName ?: ""
                dealerInfoCardDistance.value = dealer.distance ?: 0f
                dealerInfoCardDistanceUnit.value = dealer.distance?.toString()?.capitalizeFirstChar() ?: ""
                dealerInfoCardAddress.value =
                    if (!dealer.addresses.isNullOrEmpty()) {
                        dealer.addresses?.get(0)?.line1 + "\n" + dealer.addresses?.get(0)?.city + ", " +
                            dealer.addresses
                                ?.get(
                                    0,
                                )?.state + " " + dealer.addresses?.get(0)?.zipCode
                    } else {
                        ""
                    }
                dealerInfoCardPhoneNumber.value =
                    if (!dealer.phoneNumbers.isNullOrEmpty()) {
                        ToyUtil.phoneNumberFormat(dealer.phoneNumbers?.get(0)?.number)
                    } else {
                        ""
                    }
                dealerInfoCardWebsiteVisible.value = dealer.website?.firstOrNull() != null
            }
        }

        private fun sendGetVehicleLocationRequest() {
            showProgress()
            vehicle?.let {
                telemetryAPIManager.sendGetTelemetryRequest(
                    it,
                    object : BaseCallback<OdometerDetailsResponse?>() {
                        override fun onSuccess(response: OdometerDetailsResponse?) {
                            response?.payload?.vehicleLocation?.let { vehicleLocation ->
                                val latLng = LatLng(vehicleLocation.latitude, vehicleLocation.longitude)
                                setCarLocation(latLng)
                                mapManger?.setVehicleLocationPin(true, latLng)
                                mapManger?.zoomToLatLng(latLng, defaultLowZoomLevel)
                            }
                        }

                        override fun onFailError(
                            httpCode: Int,
                            errorMsg: String?,
                        ) {
                            showErrorMessage(errorMsg)
                        }

                        override fun onComplete() {
                            hideProgress()
                        }
                    },
                )
            }
        }

        private fun setCarLocation(latLng: LatLng) {
            if (mCarLocation == null) {
                mCarLocation = Location("")
            }
            mCarLocation?.latitude = latLng.latitude
            mCarLocation?.longitude = latLng.longitude
        }

        override fun onMapClicked(location: LatLng?) {
            hideDealerDetailsCard()
        }

        private fun hideDealerDetailsCard() {
            dealerDetailBottomSheetVisible.value = false
            mapControlsVisible.value = true
            selectedDealerLiveData.value = null
        }

        fun onBackPressed() {
            if (dealerDetailBottomSheetVisible.value == true) {
                hideDealerDetailsCard()
            } else {
                finishActivity()
            }
        }

        fun onSearchEditorAction(
            textView: TextView?,
            actionId: Int,
            event: KeyEvent?,
        ): Boolean =
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                textView?.text?.let { searchDealers(it.toString()) }
                true
            } else {
                false
            }

        fun modeToggle() {
            hideDealerDetailsCard()

            // Only show the map view if we have dealers to display. If no dealers are found, default
            // to displaying the dealer list which also contains the empty state.
            if (listMode.value == false || mDealersList.isEmpty()) {
                // Switch to list mode
                listMode.value = true
                modeToggleButtonResource.value = R.drawable.icon_pd_map
            } else {
                // Switch to map mode
                listMode.value = false
                modeToggleButtonResource.value = R.drawable.icon_pd_list
            }
        }

        fun clickCar() {
            sendGetVehicleLocationRequest()
            mapCarLocButtonSelected.value = true
            mapUserLocButtonSelected.value = false
        }

        fun onVisitDealerWebsite() {
            selectedDealer.value?.website?.let {
                dealerInfoCardNavigateToWebsiteObservable.value = it
            }
        }

        fun onCallDealer() {
            selectedDealer.value?.phoneNumbers?.get(0)?.number?.let {
                dealerInfoCardCallDealerObservable.value = it
            }
        }

        fun onFinalSelectDealer() {
            selectedDealer.value?.let {
                finalSelectPreferredDealer(it)
            }
        }

        /**
         * List mode selections are final.  Map mode only shows a context card, which can then make a final selection
         */
        private fun finalSelectPreferredDealer(dealer: Dealer) {
            val intent = Intent()
            intent.putExtra(AddVehicleInfoActivity.KEY_PREFERRED_DEALER, dealer)
            finishActivity(Activity.RESULT_OK, intent)
        }
    }

class SelectPreferredDealerAdaptersFactory
    @Inject
    constructor() : IFindDealerAdapterFactory {
        override fun createDealerAdapter(clickListener: DealerSelectedListener): DealerLocationItemAdapter =
            DealerLocationItemAdapter(
                clickListener,
            )
    }
