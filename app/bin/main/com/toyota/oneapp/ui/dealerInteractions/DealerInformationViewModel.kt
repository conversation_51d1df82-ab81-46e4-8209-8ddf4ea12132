package com.toyota.oneapp.ui.dealerInteractions

import android.location.Location
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.model.dealer.Dealer
import com.toyota.oneapp.model.dealer.SearchDealerResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.api.manager.DealerScheduleAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.dealerInteractions.adapters.BaseDealerLocationItemAdapter
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import toyotaone.commonlib.location.UserLocationProvider
import toyotaone.commonlib.map.GoogleMapListener
import toyotaone.commonlib.map.IGoogleMapManager
import java.util.Locale

abstract class DealerInformationViewModel(
    private val dealerScheduleAPIManager: DealerScheduleAPIManager,
    protected val applicationData: ApplicationData,
    protected val analyticsLogger: AnalyticsLogger,
) : BaseViewModel(),
    DealerSelectedListener,
    GoogleMapListener<DealerWrapper> {
    @VisibleForTesting
    val defaultLowZoomLevel = 11f

    @VisibleForTesting
    val defaultHighZoomLevel = 13f

    protected var mapManger: IGoogleMapManager<DealerWrapper>? = null
    protected lateinit var mUserLocationProvider: UserLocationProvider

    private lateinit var mRegion: String
    private lateinit var mBrand: String
    private lateinit var mUsStatesAndCodes: List<String>
    private lateinit var mCanadaStatesAndCodes: List<String>

    val mDealersList: MutableList<Dealer> = ArrayList()

    protected abstract val vehicle: VehicleInfo?
    private val mIsToyotaBrand get() = vehicle?.isToyotaBrand ?: true

    private var mCurrentLocation: Location? = null
    private var mLastDealerRefreshLocation: Location? = null

    protected abstract val mUseNumberForMapPins: Boolean

    private val searchDealersCallback =
        object : BaseCallback<SearchDealerResponse?>() {
            override fun onSuccess(response: SearchDealerResponse?) {
                showDealersList(
                    response?.payload?.dealerships,
                )
            }

            override fun onFailError(
                httpCode: Int,
                errorMsg: String?,
            ) {
                showDealersList(null)
            }

            override fun onComplete() {
                hideProgress()
                dealersFetchInProgress = false
            }
        }

    abstract val mDealersAdapter: BaseDealerLocationItemAdapter

    val dealerListVisible = MutableLiveData<Boolean>()
    val dealersNotFoundVisible = MutableLiveData<Boolean>()
    val distanceToDealerVisible = MutableLiveData<Boolean>()

    /**
     * callback for if permissions were granted or not
     */
    val noLocationPermissionsObservable = SingleLiveEvent<(Boolean) -> Unit>()
    val systemLocationDisabledObservable = SingleLiveEvent<ResolvableApiException>()

    private var dealersFetchInProgress = false

    protected val selectedDealerLiveData = MutableLiveData<Dealer?>(null)
    val selectedDealer: LiveData<Dealer?> = selectedDealerLiveData

    companion object {
        var currentLocation: Location? = null
    }

    /**
     * This should be called from onAttach in a fragment or onCreate in an activity.
     */
    open fun initialize(
        userLocationProvider: UserLocationProvider,
        usStates: Array<String>,
        usStateCodes: Array<String>,
        caStates: Array<String>,
        caStateCodes: Array<String>,
        region: String?,
        brandCode: String?,
    ) {
        mUserLocationProvider = userLocationProvider
        mRegion = region ?: vehicle?.region ?: ToyotaConstants.REGION_US // use region if given, else use info from vehicle, else use US
        mBrand = brandCode ?: vehicle?.brand ?: VehicleInfo.BRAND_TOYOTA // use brand if given, else use info from vehicle, else use Toyota
        mUsStatesAndCodes =
            usStates.map { state -> state.uppercase(Locale.getDefault()) } +
            usStateCodes.map { stateCode ->
                stateCode.uppercase(
                    Locale.getDefault(),
                )
            }
        mCanadaStatesAndCodes =
            caStates.map { state -> state.uppercase(Locale.getDefault()) } +
            caStateCodes.map { stateCode ->
                stateCode.uppercase(
                    Locale.getDefault(),
                )
            }

        clearDealersList()

        // Kick off an initial location fetch
        getCurrentLocAndDealersList()
    }

    fun setMapManager(mapManager: IGoogleMapManager<DealerWrapper>) {
        mapManger = mapManager
        mapManger?.setItemSelectedListener(this)
        mapManger?.setUserLocationProvider(mUserLocationProvider)
        mapManger?.enableMyLocation()

        if (mDealersList.isNotEmpty()) {
            mapManger?.addItemMarkers(mDealersList.wrap())
            setSelectedDealer(selectedDealer.value)
        }
    }

    open fun searchDealers(searchQuery: String) {
        // Clear the list on a new text search
        clearDealersList()

        // Distance to dealer when searching is distance from center of search zone, not distance to
        // the user. This is a little bit confusing since it can be interpreted as distance from
        // the user. Don't display this information in that case.
        distanceToDealerVisible.value = false

        analyticsLogger.logEvent(AnalyticsEvent.PREFFERED_SERVICING_DEALER_SEARCH)
        hideSoftKeyboard()

        if (ToyUtil.isBlank(searchQuery)) {
            return
        }

        dealerListVisible.value = false
        val address = searchQuery.trim()

        val latitude = mCurrentLocation?.latitude
        val longitude = mCurrentLocation?.longitude

        val city = address.substringBefore(" ")
        val state = address.substringAfter(" ")

        when {
            ToyUtil.matchZipCode(address, mRegion) -> {
                getDealersListByZip(address, latitude, longitude)
            }
            isState(state) -> {
                getDealersListByAddress(city, state, latitude, longitude)
            }
            else -> {
                getDealersListByAddress(address, null, latitude, longitude)
            }
        }
    }

    private fun List<Dealer>.wrap() =
        mapIndexed { index, dealer ->
            DealerWrapper(dealer, index + 1, mIsToyotaBrand, mUseNumberForMapPins, mapManger)
        }

    open fun showDealersList(dealers: List<Dealer>?) {
        clearDealersList()

        if (dealers?.isNotEmpty() == true) {
            mDealersList.addAll(dealers)
            mDealersAdapter.updateDealerList(mDealersList)
            dealerListVisible.value = true
            dealersNotFoundVisible.value = false

            val wrappedList = dealers.wrap()
            mapManger?.addItemMarkers(wrappedList)

            // Reset the map state since pins were cleared
            selectedDealer.value.let { dealer ->
                if (dealer != null) {
                    zoomToSelectedDealer()
                } else {
                    zoomToDealersList()
                }
            }
        } else {
            dealerListVisible.value = false
            dealersNotFoundVisible.value = true
        }
    }

    private fun clearDealersList() {
        mDealersList.clear()
        mapManger?.clearMap()
        mDealersAdapter.updateDealerList(mutableListOf())
    }

    private fun getDealersListByZip(
        zipCode: String?,
        lat: Double?,
        lon: Double?,
    ) {
        showProgress()
        dealersFetchInProgress = true
        dealerScheduleAPIManager.sendGetDealerListByZipRequest(
            mBrand,
            zipCode,
            mRegion,
            lat,
            lon,
            searchDealersCallback,
        )
    }

    private fun getDealersListByAddress(
        city: String?,
        state: String?,
        lat: Double?,
        lon: Double?,
    ) {
        showProgress()
        dealersFetchInProgress = true
        dealerScheduleAPIManager.sendGetDealerListByCityRequest(
            mBrand,
            city,
            state,
            mRegion,
            lat,
            lon,
            searchDealersCallback,
        )
    }

    fun getCurrentLocAndDealersList() {
        // Enable location in map if it is not yet enabled. This will check if user has granted
        // permission before enabling
        mapManger?.enableMyLocation()

        val locationResult =
            mUserLocationProvider.getUserLocationAsync(
                object : UserLocationProvider.LocationCallback {
                    override fun locationReceived(location: Location) {
                        getCurrentLocDealersList(location, isFromUserLocation = true)
                        currentLocation = location
                    }
                },
            )

        when (locationResult) {
            UserLocationProvider.LocationRequestResult.FETCHING_LOCATION -> {
                // If we can get location, show progress while we wait for it
                showProgress()
            }
            UserLocationProvider.LocationRequestResult.PERMISSION_NOT_GRANTED -> {
                noLocationPermissionsObservable.value = { permissionGranted ->
                    if (permissionGranted) {
                        // Get our location provider running
                        mUserLocationProvider.startLocationUpdates()

                        // Enable location in our map
                        mapManger?.enableMyLocation()

                        // Call this function again
                        getCurrentLocAndDealersList()
                    }
                }
            }
            UserLocationProvider.LocationRequestResult.SYSTEM_LOCATION_DISABLED -> {
                mUserLocationProvider.enableSystemLocation { exception ->
                    // System toggle is disabled. Send the user over to the system activity
                    systemLocationDisabledObservable.value = exception
                }
            }
        }.exhaustive
    }

    /**
     * Get dealers list based on a location. Allows setting a threshold. If set, the list will only
     * be refreshed if the location is greater than or equal to this threshold. Defaults to -1 to
     * always fetch.
     *
     * @param location Location to fetch list for
     * @param thresholdInMeters Minimum value, in meters, that the location has to be from the last
     *                          location where this method was called in order to refresh the list.
     */
    fun getCurrentLocDealersList(
        location: Location,
        thresholdInMeters: Double = -1.0,
        isFromUserLocation: Boolean = true,
    ) {
        if ((mLastDealerRefreshLocation?.distanceTo(location) ?: Float.MAX_VALUE) > thresholdInMeters) {
            mLastDealerRefreshLocation = location
            getLatLngDealersList(location.latitude, location.longitude, isFromUserLocation)
        }
    }

    private fun getLatLngDealersList(
        lat: Double,
        lon: Double,
        isFromUserLocation: Boolean,
    ) {
        showProgress()
        dealersFetchInProgress = true
        // When we search from the user's current location, we want to display distance to dealer since it will be accurate.
        distanceToDealerVisible.value = isFromUserLocation

        dealerScheduleAPIManager.sendGetDealerListByLocationRequest(
            mBrand,
            lat,
            lon,
            mRegion,
            searchDealersCallback,
        )
    }

    private fun isState(state: String?): Boolean {
        if (state == null) {
            return false
        }

        val formattedState = state.trim().uppercase(Locale.getDefault())

        return when (mRegion) {
            ToyotaConstants.REGION_US, ToyotaConstants.REGION_PR ->
                mUsStatesAndCodes.contains(
                    formattedState,
                )
            ToyotaConstants.REGION_CA -> mCanadaStatesAndCodes.contains(formattedState)
            else -> false
        }
    }

    override fun onMapPinSelected(item: DealerWrapper?) {
        setSelectedDealer(item?.dealer)
    }

    override fun setSelectedDealer(dealer: Dealer?) {
        selectedDealerLiveData.value = dealer

        if (selectedDealer.value != null) {
            zoomToSelectedDealer()
        } else {
            zoomToDealersList()
        }
    }

    private fun zoomToDealersList() {
        mapManger?.deselectMarker()
        mapManger?.zoomToItems(true, mDealersList.wrap(), defaultLowZoomLevel)
    }

    private fun zoomToSelectedDealer() {
        selectedDealer.value?.let { dealer ->
            // Centering map on marker that is clicked
            mapManger?.selectItemPinFor(dealer.toyotaCode ?: "")
            if (!dealer.addresses.isNullOrEmpty()) {
                mapManger?.zoomToLatLng(
                    LatLng(
                        dealer.addresses
                            ?.get(0)
                            ?.coordinate
                            ?.latitude ?: 0.0,
                        dealer.addresses
                            ?.get(0)
                            ?.coordinate
                            ?.longitude ?: 0.0,
                    ),
                    defaultHighZoomLevel,
                )
            }
        }
    }
}

interface DealerSelectedListener {
    fun setSelectedDealer(dealer: Dealer?)
}

interface IFindDealerAdapterFactory {
    fun createDealerAdapter(clickListener: DealerSelectedListener): BaseDealerLocationItemAdapter
}
