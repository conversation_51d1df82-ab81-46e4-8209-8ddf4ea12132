package com.toyota.oneapp.ui.dealerInteractions.adapters

import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.model.dealer.Dealer
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.dealerInteractions.DealerSelectedListener
import com.toyota.oneapp.util.ToyotaConstants

class DealerLocationItemViewModel(
    private val listener: DealerSelectedListener,
) : BaseViewModel() {
    val dealerName = MutableLiveData<String>()
    val dealerAddress = MutableLiveData<String>()
    val distanceToDealer = MutableLiveData<Float>()
    val distanceToDealerUnit = MutableLiveData<String>()
    val distanceToDealerVisible = MutableLiveData<Boolean>()
    val isDealerSelected = MutableLiveData<Boolean>()
    val humanReadablePositionData = MutableLiveData<Int>()

    private var dealer: Dealer? = null

    fun bind(
        dealer: Dealer,
        position: Int,
    ) {
        this.dealer = dealer
        dealerName.value = dealer.dealershipName ?: ""
        dealerAddress.value =
            if (!dealer.addresses.isNullOrEmpty()) {
                dealer.addresses?.get(0)?.line1
            } else {
                ""
            }
        distanceToDealer.value = dealer.distance
        distanceToDealerUnit.value = distanceMetric(dealer.region ?: "")
        distanceToDealerVisible.value =
            if (dealer.distance != null) {
                dealer.distance!! > 0
            } else {
                false
            }
        isDealerSelected.value = false
        humanReadablePositionData.value = position + 1
    }

    fun distanceMetric(region: String): String =
        if (ToyotaConstants.REGION_US.equals(region, true)) {
            ToyotaConstants.MI
        } else {
            ToyotaConstants.KM
        }

    fun onClick() {
        listener.setSelectedDealer(dealer)
        isDealerSelected.value = true
    }
}
