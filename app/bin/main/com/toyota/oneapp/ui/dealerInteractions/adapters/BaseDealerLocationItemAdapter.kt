package com.toyota.oneapp.ui.dealerInteractions.adapters

import android.location.Location
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.model.dealer.Dealer
import com.toyota.oneapp.ui.dealerInteractions.DealerInformationViewModel
import com.toyota.oneapp.ui.dealerInteractions.DealerSelectedListener

abstract class BaseDealerLocationItemAdapter(
    private val clickListener: DealerSelectedListener,
) : RecyclerView.Adapter<BaseDealerLocationItemAdapter.BaseDealerLocationItemViewHolder>() {
    private lateinit var dealers: List<Dealer>

    override fun onBindViewHolder(
        holder: BaseDealerLocationItemViewHolder,
        position: Int,
    ) {
        val d1 = Location("")
        val currentLocation = DealerInformationViewModel.currentLocation
        if (dealers[position].addresses != null &&
            currentLocation != null
        ) {
            d1.longitude = dealers[position]
                .addresses
                ?.get(0)
                ?.coordinate
                ?.longitude ?: 0.0
            d1.latitude = dealers[position]
                .addresses
                ?.get(0)
                ?.coordinate
                ?.latitude ?: 0.0

            dealers[position].distance = (currentLocation.distanceTo(d1) * 0.0006214).toFloat()
            dealers[position].distanceUnit = dealers[position].region ?: ""
        }
        holder.bind(dealers[position], position)
    }

    override fun getItemCount(): Int = if (::dealers.isInitialized) dealers.size else 0

    fun updateDealerList(dealers: List<Dealer>) {
        this.dealers = dealers
        notifyDataSetChanged()
    }

    abstract inner class BaseDealerLocationItemViewHolder(
        protected val binding: ViewDataBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        private val viewModel = DealerLocationItemViewModel(clickListener)

        fun bind(
            dealer: Dealer,
            position: Int,
        ) {
            viewModel.bind(dealer, position)
            setViewModel(viewModel)
        }

        abstract fun setViewModel(newViewModel: DealerLocationItemViewModel)
    }
}
