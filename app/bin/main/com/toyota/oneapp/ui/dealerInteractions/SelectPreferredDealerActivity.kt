package com.toyota.oneapp.ui.dealerInteractions

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.Observer
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.tbruyelle.rxpermissions2.Permission
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.SelectPreferredDealerLayoutBinding
import com.toyota.oneapp.ui.BaseActivity
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.location.RealUserLocationProvider
import toyotaone.commonlib.location.UserLocationProvider
import toyotaone.commonlib.map.GoogleMapManager
import toyotaone.commonlib.map.IGoogleMapManager
import toyotaone.commonlib.permission.PermissionUtil

@AndroidEntryPoint
class SelectPreferredDealerActivity :
    BaseActivity(),
    OnMapReadyCallback {
    companion object {
        private const val KEY_REGION = "REGION"
        private const val KEY_BRAND_CODE = "BRAND_CODE"

        fun newIntent(
            context: Context,
            region: String? = null,
            brand: String? = null,
        ): Intent =
            Intent(context, SelectPreferredDealerActivity::class.java).apply {
                if (region != null) putExtra(KEY_REGION, region)
                if (brand != null) putExtra(KEY_BRAND_CODE, brand)
            }
    }

    private val viewModel: SelectPreferredDealerViewModel by viewModels()
    private lateinit var mUserLocationProvider: RealUserLocationProvider
    private var mMap: GoogleMap? = null
    private var mGoogleMapManger: IGoogleMapManager<DealerWrapper>? = null

    private var _binding: SelectPreferredDealerLayoutBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        mUserLocationProvider = RealUserLocationProvider(this)

        _binding =
            DataBindingUtil.setContentView(
                this,
                R.layout.select_preferred_dealer_layout,
            )
        binding.lifecycleOwner = this

        lifecycle.addObserver(mUserLocationProvider)
        val region = intent?.extras?.getString(KEY_REGION)
        val brandCode = intent?.extras?.getString(KEY_BRAND_CODE)
        viewModel.initialize(
            userLocationProvider = mUserLocationProvider,
            usStates = resources.getStringArray(R.array.state_names),
            usStateCodes = resources.getStringArray(R.array.state_codes),
            caStates = resources.getStringArray(R.array.canada_state_names),
            caStateCodes = resources.getStringArray(R.array.canada_state_codes),
            region = region,
            brandCode = brandCode,
        )

        val mapFragment =
            supportFragmentManager.findFragmentById(
                R.id.current_location_complete_map,
            ) as SupportMapFragment?
        mapFragment?.getMapAsync(this)

        observeBaseEvents(viewModel)
        setUpObservers()
        setUpOnClickListeners()
        DataBindingAdapters.setAdapter(binding.rvDealersList, viewModel.mDealersAdapter)
        binding.headerPd.buttonBack.setOnClickListener {
            viewModel.onBackPressed()
        }
        binding.headerPd.etTitle.setOnEditorActionListener { v, actionId, event ->
            viewModel.onSearchEditorAction(v, actionId, event)
        }
        viewModel.modeToggleButtonResource.observe(this) {
            binding.headerPd.buttonModeToggle.setImageResource(it)
        }
        binding.headerPd.buttonModeToggle.setOnClickListener {
            viewModel.modeToggle()
        }

        viewModel.dealerInfoCardName.observe(this@SelectPreferredDealerActivity) { data ->
            binding.bottomSheetDealer.bottomSheetLayoutDealerTitle.text = data
        }

        viewModel.dealerInfoCardDistance
        viewModel.dealerInfoCardDistanceUnit
        val mediatorDealerInfoCardDistance =
            MediatorLiveData<Pair<Float?, String?>>().apply {
                addSource(viewModel.dealerInfoCardDistance) { value = Pair(it, viewModel.dealerInfoCardDistanceUnit.value) }
                addSource(viewModel.dealerInfoCardDistanceUnit) { value = Pair(viewModel.dealerInfoCardDistance.value, it) }
            }

        mediatorDealerInfoCardDistance.observe(this) { data ->
            val dealerInfoCardDistance = data.first
            val dealerInfoCardDistanceUnit = data.second
            binding.bottomSheetDealer.bottomSheetLayoutDealerDistance.text =
                resources.getString(R.string.aa_dealer_distance_format, dealerInfoCardDistance, dealerInfoCardDistanceUnit)
        }

        viewModel.distanceToDealerVisible.observe(this@SelectPreferredDealerActivity) { data ->
            DataBindingAdapters.setIsVisible(binding.bottomSheetDealer.bottomSheetLayoutDealerDistance, data)
        }
        viewModel.dealerInfoCardAddress.observe(this@SelectPreferredDealerActivity) { data ->
            binding.bottomSheetDealer.bottomSheetLayoutDealerAddressOne.text = data
        }
        viewModel.dealerInfoCardPhoneNumber.observe(this@SelectPreferredDealerActivity) { data ->
            binding.bottomSheetDealer.bottomSheetLayoutDealerPhone.text = data
        }
        viewModel.dealerInfoCardWebsiteVisible.observe(this@SelectPreferredDealerActivity) { data ->
            DataBindingAdapters.setIsVisible(binding.bottomSheetDealer.bottomSheetLayoutDealerWebsite, data)
        }

        binding.bottomSheetDealer.bottomSheetLayoutDealerPhone.setOnClickListener { viewModel.onCallDealer() }
        binding.bottomSheetDealer.bottomSheetLayoutDealerWebsite.setOnClickListener { viewModel.onVisitDealerWebsite() }
        binding.bottomSheetDealer.bottomSheetLayoutDealerButton.setOnClickListener { viewModel.onFinalSelectDealer() }
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }

    private fun setUpObservers() {
        viewModel.listMode.observe(this) {
            DataBindingAdapters.setIsInvisible(binding.flLocationMap, it)
            DataBindingAdapters.setIsVisible(binding.svDealerList, it)
        }
        viewModel.mapControlsVisible.observe(this) {
            DataBindingAdapters.setIsVisible(binding.clFloatingActionButtonsDealer, it)
        }
        viewModel.dealerDetailBottomSheetVisible.observe(this) {
            DataBindingAdapters.setIsVisible(binding.bottomSheetDealer.bottomSheetLayout, it)
        }
        viewModel.dealerListVisible.observe(this) {
            DataBindingAdapters.setIsVisible(binding.rvDealersList, it)
        }
        viewModel.dealersNotFoundVisible.observe(this) {
            DataBindingAdapters.setIsVisible(binding.llLocationError, it)
        }

        viewModel.dealerInfoCardNavigateToWebsiteObservable.observe(
            this,
            Observer {
                ToyUtil.openCustomChromeTab(this, it)
            },
        )
        viewModel.dealerInfoCardCallDealerObservable.observe(
            this,
            Observer { phoneNumber ->
                DialogUtil.showDialog(
                    this,
                    "",
                    "Call Dealer?",
                    getString(R.string.Common_call),
                    getString(
                        R.string.Common_cancel,
                    ),
                    object :
                        OnCusDialogInterface {
                        override fun onConfirmClick() {
                            ToyUtil.phoneCall(this@SelectPreferredDealerActivity, phoneNumber)
                        }

                        override fun onCancelClick() {}
                    },
                    false,
                )
            },
        )

        // In case we need to ask for location permissions
        viewModel.noLocationPermissionsObservable.observe(
            this,
            Observer { callback ->
                PermissionUtil.checkLocationPermissions(this) { permission: Permission ->
                    if (!permission.granted && !permission.shouldShowRequestPermissionRationale) {
                        showForceAllowPermissionDialog()
                    } else {
                        callback.invoke(permission.granted)
                    }
                }
            },
        )

        viewModel.systemLocationDisabledObservable.observe(
            this,
            Observer {
                it.startResolutionForResult(this, UserLocationProvider.LOC_SETTINGS_RESOLUTION_KEY)
            },
        )
    }

    private fun setUpOnClickListeners() {
        binding.currentLocationCarBtn.setOnClickListener {
            viewModel.clickCar()
        }
        binding.currentLocationLocBtn.setOnClickListener {
            viewModel.currentLocationClicked()
        }
        binding.etSearchBox.setOnClickListener {
            viewModel.currentLocationSearchBoxClicked()
        }
    }

    @SuppressLint("MissingPermission")
    override fun onMapReady(googleMap: GoogleMap) {
        mMap = googleMap
        googleMap.uiSettings.isZoomControlsEnabled = false
        googleMap.uiSettings.isCompassEnabled = true
        googleMap.uiSettings.isRotateGesturesEnabled = true
        googleMap.uiSettings.isMyLocationButtonEnabled = false
        googleMap.uiSettings.isIndoorLevelPickerEnabled = false

        // This activity doesn't use night mode because it looks incongruous with the rest of the app.
        mGoogleMapManger = GoogleMapManager(this, googleMap, mUsesNightMode = false)
        val padding = resources.getDimensionPixelSize(R.dimen.standard_map_padding)
        mGoogleMapManger?.adjustMapPadding(padding, padding, padding, padding, true)
        mGoogleMapManger?.let {
            viewModel.setMapManager(it)
        }
    }

    /**
     * Avoid this pattern whenever possible, as it is deprecated! The new pattern does not work with
     * PendingIntent though and we are handling a PendingIntent result.
     */
    override fun onActivityResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
    ) {
        when (requestCode) {
            UserLocationProvider.LOC_SETTINGS_RESOLUTION_KEY -> {
                when (resultCode) {
                    Activity.RESULT_OK -> {
                        viewModel.getCurrentLocAndDealersList()
                    }
                }
            }
            else -> {
                super.onActivityResult(requestCode, resultCode, data)
            }
        }
    }
}
