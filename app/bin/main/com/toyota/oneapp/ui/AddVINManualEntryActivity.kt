package com.toyota.oneapp.ui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.MenuItem
import android.view.inputmethod.EditorInfo
import androidx.activity.viewModels
import androidx.core.widget.doAfterTextChanged
import androidx.databinding.DataBindingUtil
import com.tbruyelle.rxpermissions2.Permission
import com.toyota.oneapp.R
import com.toyota.oneapp.account.AccountManager
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.AddVinManualEntryLayoutBinding
import com.toyota.oneapp.model.vehicle.VehicleDetailPayload
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.vinscan.AddVehicleMethodSelectionActivity
import com.toyota.oneapp.ui.vinscan.VINScanActivity
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.WearUtil.sendToWear
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.permission.PermissionUtil
import toyotaone.commonlib.wear.WearAPIType
import toyotaone.commonlib.wear.WearConstants.BUS_OUTBOUND_DATA_KEY
import toyotaone.commonlib.wear.WearConstants.BUS_OUTBOUND_PATH
import toyotaone.commonlib.wear.WearResponse
import javax.inject.Inject

@AndroidEntryPoint
class AddVINManualEntryActivity : UiBaseActivity() {
    private val viewModel: AddVINManualEntryViewModel by viewModels()

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @Inject
    lateinit var applicationData: ApplicationData

    @Inject
    lateinit var accountManager: AccountManager

    lateinit var binding: AddVinManualEntryLayoutBinding

    private val scanContract =
        registerForActivityResult(VINScanActivity.Contract()) {
            if (it != null) {
                binding.etVin.setText(
                    if (it.length == VIN_LENGTH && (it[0] == 'i' || it[0] == 'I')) {
                        it.substring(1)
                    } else {
                        it
                    },
                )
            }
        }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        val isScanVin = intent.getBooleanExtra(SCAN_VIN, false)
        if (isScanVin) {
            launchVinScan()
        }
        observeEvent()
        binding = DataBindingUtil.setContentView(this, R.layout.add_vin_manual_entry_layout)
        setSupportActionBar(binding.toolbar)
        sendToWear(
            BUS_OUTBOUND_PATH,
            BUS_OUTBOUND_DATA_KEY,
            WearResponse(
                WearAPIType.SWITCH_VEHICLE,
                null,
                null,
                getString(R.string.SmartWatch_Please_Add_Vehicle),
            ).toJsonString(),
        )
        binding.toolbar.setNavigationOnClickListener {
            onBackPressed()
        }

        binding.tilVin.setEndIconOnClickListener {
            launchVinScan()
        }

        binding.etVin.run {
            setOnEditorActionListener { _, _, _ ->
                hideSoftKeyboard()
                false
            }
            doAfterTextChanged { editable ->
                binding.btnAddVehicle.isEnabled =
                    editable?.trim { it <= ' ' }?.length in VIN_LENGTH - 1..VIN_LENGTH
            }
            setOnEditorActionListener { _, actionId, event ->
                if (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER || actionId == EditorInfo.IME_ACTION_DONE) {
                    if (binding.btnAddVehicle.isEnabled) {
                        viewModel.getVehicleInfo(this@AddVINManualEntryActivity, text.toString())
                    }
                }
                false
            }
        }
        binding.btnAddVehicle.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.ADD_VIN)
            val vin = binding.etVin.text
            if (!TextUtils.isEmpty(vin)) {
                viewModel.getVehicleInfo(this@AddVINManualEntryActivity, vin.toString())
            }
        }
        handleErrorMessages(intent.extras)
    }

    override fun onBackPressed() {
        super.onBackPressed()
        applicationData.handleAddVehicleBackPressed(shouldClearPrevVehicle = false)
    }

    private fun launchVinScan() {
        analyticsLogger.logEvent(AnalyticsEvent.ADD_VEHICLE_SCAN_USING_CAMERA)
        PermissionUtil.checkOnlyCameraPermissions(this) { permission: Permission ->
            if (permission.granted) {
                scanContract.launch(null)
            } else if (!permission.shouldShowRequestPermissionRationale) {
                showForceAllowPermissionDialog()
            }
        }
    }

    private fun handleErrorMessages(arguments: Bundle?) {
        val oneVL1001ErrorMsg = arguments?.getString(ToyotaConstants.ONE_VL_10001_MESSAGE)
        if (oneVL1001ErrorMsg != null) {
            showDialog(oneVL1001ErrorMsg)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            onBackPressed()
        }
        return true
    }

    private fun showQrScanInstruction() {
        startActivity(Intent(this, AddVehicleMethodSelectionActivity::class.java))
    }

    private fun gotoVehicleInfo(vehicleDetailPayload: VehicleDetailPayload) {
        if (vehicleDetailPayload.vehicle.is21MMVehicle == true &&
            vehicleDetailPayload.vehicle.qrCodeEligible == true
        ) {
            showQrScanInstruction()
            applicationData.clearSavedVehicle()
        } else {
            val intent = Intent(this@AddVINManualEntryActivity, AddVehicleInfoActivity::class.java)
            intent.putExtra(KEY_VEHICLE_DETAILS, vehicleDetailPayload.vehicle)
            startActivity(intent)
        }
    }

    private fun showAppUpdateAvailable() {
        DialogUtil
            .showDialog(
                this,
                getString(R.string.Common_Update_Available),
                String.format(
                    getString(R.string.App_Update_Available_Message),
                    getString(R.string.app_name),
                ),
                getString(R.string.Common_update),
                getString(R.string.Common_cancel),
                object :
                    OnCusDialogInterface {
                    override fun onConfirmClick() {
                        startActivity(
                            Intent(Intent.ACTION_VIEW, Uri.parse(ToyotaConstants.APP_GOOGLE_PLAY_URL)),
                        )
                    }

                    override fun onCancelClick() {
                        // Not Implemented
                    }
                },
                false,
            ).setCanceledOnTouchOutside(false)
    }

    private fun observeEvent() {
        observeBaseEvents(viewModel)
        viewModel.vehicleDetailResponse.observe(this) {
            gotoVehicleInfo(it)
        }
        viewModel.showUpdateAvailable.observe(this) {
            showAppUpdateAvailable()
        }

        viewModel.blockFleetVehicle.observe(this) {
            val intent = Intent(this, BlockFleetVehicleActivity::class.java)
            intent.putExtra(BlockFleetVehicleActivity.EXTRA_BLOCK_VIN_FROM_MANUAL_ENTRY, true)
            intent.putExtra(BlockFleetVehicleActivity.EXTRA_BLOCK_FLEET_DESCRIPTION, it)
            intent.putExtra(BlockFleetVehicleActivity.EXTRA_CALIFORNIA_SENATE_BILL, viewModel.californiaSenateBill)
            startActivity(intent)
            finish()
        }

        viewModel.showQRScanInstruction.observe(this) {
            showQrScanInstruction()
        }
    }

    companion object {
        const val KEY_VEHICLE_DETAILS = "KEY_VEHICLE_DETAILS"
        const val KEY_VEHICLE_NICK_NAME = "KEY_VEHICLE_NICK_NAME"
        const val KEY_VEHICLE_ASSOCIATION_ID = "KEY_VEHICLE_ASSOCIATION_ID"
        const val SCAN_VIN = "SCAN_VIN"
        private const val VIN_LENGTH = 18
    }
}
