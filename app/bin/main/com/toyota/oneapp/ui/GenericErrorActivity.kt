package com.toyota.oneapp.ui

import android.os.Bundle
import android.text.util.Linkify
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityGenericErrorBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity

abstract class GenericErrorActivity : UiBaseActivity() {
    lateinit var binding: ActivityGenericErrorBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding =
            DataBindingUtil
                .setContentView<ActivityGenericErrorBinding>(
                    this,
                    R.layout.activity_generic_error,
                ).apply {
                    primaryText.text = getTitleText()
                    secondaryText.text = getSecondaryText()
                    Linkify.addLinks(secondaryText, Linkify.ALL)
                    btnFirst.text = getFirstButtonName()
                    btnSecond.text = getSecondButtonName()
                    btnFirst.setOnClickListener { onPositiveButtonClicked() }
                    btnSecond.setOnClickListener { onNegativeButtonClicked() }
                    menuCloseImg.setOnClickListener { onCloseButtonClicked() }
                }
    }

    abstract fun getTitleText(): String

    abstract fun getSecondaryText(): String

    abstract fun getFirstButtonName(): String

    abstract fun getSecondButtonName(): String

    abstract fun onPositiveButtonClicked()

    abstract fun onNegativeButtonClicked()

    abstract fun onCloseButtonClicked()
}
