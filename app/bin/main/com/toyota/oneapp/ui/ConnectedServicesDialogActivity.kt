package com.toyota.oneapp.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Html
import android.view.View
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.ActivityConnectedServicesDialogBinding
import com.toyota.oneapp.model.combineddataconsent.DeclinePayload
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ConnectedServicesDialogActivity :
    UiBaseActivity(),
    View.OnClickListener {
    companion object {
        const val TAG = "ConnectedServicesDialogActivity"
        private const val IS_GARAGE_FLOW = "is_from_garage_flow"
        private const val IS_TOYOTA = "is_toyota_brand"

        fun getIntent(
            context: Context?,
            isToyota: Boolean? = false,
            isGarageFlow: Boolean? = false,
        ): Intent =
            Intent(context, ConnectedServicesDialogActivity::class.java).apply {
                putExtra(IS_GARAGE_FLOW, isGarageFlow)
                putExtra(IS_TOYOTA, isToyota)
            }
    }

    private val viewModel: ConnectedServicesDialogViewModel by viewModels()

    private var isFromGarageFlow = false
    private var dataBinding: ActivityConnectedServicesDialogBinding? = null

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        dataBinding =
            DataBindingUtil
                .setContentView<ActivityConnectedServicesDialogBinding>(
                    this,
                    R.layout.activity_connected_services_dialog,
                ).apply {
                    lifecycleOwner = this@ConnectedServicesDialogActivity
                    executePendingBindings()
                }
        observeBaseEvents(viewModel)
        dataBinding?.toolbar?.let { performActivitySetup(it) }

        viewModel.connectedServicesDialogNavigationEvents.observe(
            this,
            Observer {
                when (it) {
                    is ConnectedServicesDialogNavigationEvent.ShowConnectedServicesDecline ->
                        setUpView(
                            it.datConsent,
                        )
                    is ConnectedServicesDialogNavigationEvent.ShowConnectedServicesDeclineFailed -> initView()
                }
            },
        )
    }

    private fun initView() {
        isFromGarageFlow = intent.getBooleanExtra(IS_GARAGE_FLOW, false)
        val isToyotaBrand = intent.getBooleanExtra(IS_TOYOTA, false)
        var title = ""
        var message = ""

        if (isToyotaBrand) {
            title = getString(R.string.Garage_cancel_subscription_title)
            message = getString(R.string.Garage_cancel_subscription_note)
        } else {
            title = getString(R.string.Garage_cancel_subscription_title_lexus)
            message = getString(R.string.Garage_cancel_subscription_note_lexus)
        }
        dataBinding?.tvTitle?.text = title
        dataBinding?.tvMessage?.text = message
        dataBinding?.btnConfirm?.text = getString(R.string.Common_confirm)
        dataBinding?.btnBack?.text = getString(R.string.Common_cancel)
        dataBinding?.btnBack?.setOnClickListener(this)
        dataBinding?.btnConfirm?.setOnClickListener(this)
    }

    private fun setUpView(datConsent: DeclinePayload) {
        dataBinding?.tvTitle?.text = datConsent.title
        dataBinding?.tvMessage?.text = Html.fromHtml(datConsent.body)
        dataBinding?.btnConfirm?.text = datConsent.positiveButtonText
        dataBinding?.btnBack?.text = datConsent.negativeButtonText
        dataBinding?.btnBack?.setOnClickListener(this)
        dataBinding?.btnConfirm?.setOnClickListener(this)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btn_confirm -> {
                if (isFromGarageFlow) {
                    setResult(RESULT_OK)
                    finish()
                } else {
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEventParam.CONNECTED_SERVICES_WAIVE,
                        AnalyticsEventParam.SERVICE_CONNECT,
                    )
                    setResult(RESULT_OK)
                    finish()
                }
            }
            R.id.btn_back -> finish()
        }
    }
}
