package com.toyota.oneapp.ui
import android.os.Bundle
import android.view.View
import androidx.annotation.UiThread
import com.toyota.oneapp.app.IDPData.Companion.instance
import toyotaone.commonlib.mvvmbase.EXBaseViewModel

open class BaseAuthenticatedModel : EXBaseViewModel() {
    fun getUserFirstName(): String? = instance?.getUserFirstName()

    fun getUserLastName(): String? = instance?.getUserLastName()

    fun getUserEmail(): String? = instance?.getUserEmail()

    fun getUserPhoneNumber(): String? = instance?.getPhoneNumber()

    fun getAccessToken(): String? = instance?.accessToken

    fun getIDToken(): String? = instance?.idToken

    @UiThread
    override fun onCreate() {}

    @UiThread
    override fun onResume() {}

    override fun onAttach(bundle: Bundle?) {}

    @UiThread
    open fun attachView(
        view: View?,
        bundle: Bundle?,
    ) {}

    @UiThread
    override fun detachView() {}

    @UiThread
    override fun onPause() {}

    override fun onDestroy() {}

    @UiThread
    open fun destroy() {}
}
