package com.toyota.oneapp.ui.remotepark

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityRemoteParkDetailsBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RemoteParkDetailActivity : UiBaseActivity() {
    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        DataBindingUtil
            .setContentView<ActivityRemoteParkDetailsBinding>(
                this,
                R.layout.activity_remote_park_details,
            ).let { binding ->
                performActivitySetup(binding.remoteParkToolbar)
            }
    }
}
