package com.toyota.oneapp.ui.newdashboard

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.NavGraph
import androidx.navigation.findNavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.plusAssign
import androidx.navigation.ui.NavigationUI
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityNewdashboardBinding
import com.toyota.oneapp.ui.accountsettings.AccountSettingsFragment
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.flutter.LaunchVehicleTabEvent
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.eventbus.RxBus
import toyotaone.commonlib.extension.onNavDestinationSelected

const val kNavItem = "NavItemType"

@AndroidEntryPoint
class DashboardActivity : UiBaseActivity() {
    private lateinit var binding: ActivityNewdashboardBinding
    val navController by lazy { findNavController(R.id.dashboard_navigation) }
    private val notificationViewModel: NotificationHistoryViewModel by viewModels()
    private var graph: NavGraph? = null

    private val notificationReceiver =
        object : BroadcastReceiver() {
            override fun onReceive(
                context: Context?,
                intent: Intent?,
            ) {
                refreshNotificationsBadge()
            }
        }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding = ActivityNewdashboardBinding.inflate(layoutInflater)
        setContentView(binding.root)
        isDashBoardNavigated = false

        binding.vehiclePageFab.setOnClickListener {
            RxBus.get().post(LaunchVehicleTabEvent())
            finish()
        }

        val navHostFragment = supportFragmentManager.findFragmentById(R.id.dashboard_navigation)!!
        val navigator =
            KeepCurrentStateFragment(
                this,
                navHostFragment.childFragmentManager,
                R.id.dashboard_navigation,
            )
        navController.navigatorProvider += navigator
        graph = navController.navInflater.inflate(R.navigation.dashboard_nav)
        NavigationUI.setupWithNavController(
            binding.bottomNavigation,
            navController,
        )

        binding.bottomNavigation.visibility = View.GONE
        binding.bottomNavigation.setOnNavigationItemSelectedListener {
            onNavDestinationSelected(it, navController, null)
            return@setOnNavigationItemSelectedListener true
        }
        binding.bottomNavigation.itemIconTintList = null

        val navItemType: NavItemType? = intent.getSerializableExtra(kNavItem) as NavItemType?
        if (navItemType != null) {
            navigateToView(navItemType)
        }

        observeBaseEvents(notificationViewModel)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
    }

    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this).registerReceiver(
            notificationReceiver,
            IntentFilter(ToyotaConstants.ACTION_NOTIFICATION_RECEIVED),
        )
        refreshNotificationsBadge()
    }

    override fun onStop() {
        super.onStop()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(notificationReceiver)
    }

    override fun onActivityResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
    ) {
        super.onActivityResult(requestCode, resultCode, data)
        val currentFragment: Fragment? =
            (supportFragmentManager.fragments.first() as? NavHostFragment)?.childFragmentManager?.fragments?.getOrNull(
                0,
            )
        currentFragment?.let {
            if (it is AccountSettingsFragment) {
                it.onActivityResultFromActivity(requestCode, resultCode, data)
            }
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }

    fun navigateToView(item: NavItemType) {
        when (item) {
            NavItemType.ACCOUNT -> {
                graph?.setStartDestination(R.id.profilePage)
                navController.setGraph(graph!!, intent.extras)
            }
            NavItemType.ALERTS -> {
                graph?.setStartDestination(R.id.notificationPage)
                navController.setGraph(graph!!, intent.extras)
            }
        }
    }

    fun refreshNotificationsBadge() {
        notificationViewModel.getNotificationHistoryRequest()
    }

    companion object {
        const val DASHBOARD_NEW_VEHICLE_ADDED_KEY = "NewVehicle"
        var isDashBoardNavigated: Boolean = false
    }

    enum class NavItemType {
        ALERTS,
        ACCOUNT,
    }
}
