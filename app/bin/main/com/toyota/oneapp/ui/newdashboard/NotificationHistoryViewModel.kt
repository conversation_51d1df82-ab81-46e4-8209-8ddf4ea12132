package com.toyota.oneapp.ui.newdashboard

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.common.base.Strings
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.dashboard.MarkReadRequestPayload
import com.toyota.oneapp.model.dashboard.NotificationHistoryItem
import com.toyota.oneapp.model.dashboard.NotificationHistoryPayload
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.NotificationHistoryRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.newdashboard.fragments.NotificationHistoryPageFragment.Companion.ALL_NOTIFICATION
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.log.LogTool
import java.util.SortedSet
import javax.inject.Inject

@HiltViewModel
class NotificationHistoryViewModel
    @Inject
    constructor(
        private val repository: NotificationHistoryRepository,
        private val preferenceModel: OneAppPreferenceModel,
        private val applicationData: ApplicationData,
    ) : BaseViewModel() {
        private val mNotificationHistory = MutableLiveData<List<NotificationHistoryPayload>>()
        val notificationHistory: LiveData<List<NotificationHistoryPayload>> get() = mNotificationHistory
        private val mShowBadge = MutableLiveData<Boolean>()
        val showBadge: LiveData<Boolean> get() = mShowBadge

        private val mCategories = mutableListOf<String>()
        val categories: List<String> get() = mCategories

        fun getNotificationHistoryRequest() {
            viewModelScope.launch {
                when (
                    val resource =
                        repository.sendNotificationHistoryRequest(
                            preferenceModel.getGuid(),
                        )
                ) {
                    is Resource.Success -> {
                        resource.data?.payload.let {
                            processNotifications(it)
                        }
                    }
                    is Resource.Failure -> {
                        mShowBadge.postValue(false)
                    }
                    else -> {}
                }
            }
        }

        fun markRead(messageIds: List<String>) {
            viewModelScope.launch {
                LogTool.d(javaClass.simpleName, "Message ids marked read: $messageIds")
                val resource =
                    repository.sendNotificationMarkReadRequest(
                        MarkReadRequestPayload(preferenceModel.getGuid(), messageIds),
                    )
                if (resource is Resource.Success) {
                    getNotificationHistoryRequest()
                }
            }
        }

        private fun processNotifications(notificationsPayload: List<NotificationHistoryPayload>?) {
            mCategories.clear()
            val set = sortedSetOf<String>()
            val generalNotifications = arrayListOf<NotificationHistoryItem>()
            val vehiclesMap = mutableMapOf<String, VehicleInfo>()
            val notificationsMap = linkedMapOf<String, NotificationHistoryPayload?>()
            applicationData.getVehicleList()?.forEach {
                vehiclesMap[it.vin] = it
                notificationsMap[it.vin] = null
            }
            val showBadge =
                processNotificationItems(
                    notificationsPayload,
                    generalNotifications,
                    vehiclesMap,
                    notificationsMap,
                    set,
                )
            if (set.isNotEmpty()) {
                mCategories.apply {
                    add(ALL_NOTIFICATION)
                    addAll(set)
                }
            }

            val notifications = mutableListOf<NotificationHistoryPayload>()
            val allNotifications = arrayListOf<NotificationHistoryItem>()
            notificationsMap.forEach {
                notifications.add(
                    it.value ?: NotificationHistoryPayload(
                        arrayListOf(),
                        getDisplayName(vehiclesMap[it.key]),
                        it.key,
                    ),
                )
                it.value?.payload?.let { items -> allNotifications.addAll(items) }
            }

            if (generalNotifications.isNotEmpty()) {
                notifications.add(
                    NotificationHistoryPayload(generalNotifications, GENERAL_NOTIFICATIONS),
                )
                allNotifications.addAll(generalNotifications)
            }

            if (notifications.size > 1) {
                allNotifications.sortByDescending { it.notificationDate }
                notifications.add(0, NotificationHistoryPayload(allNotifications, ALL_VEHICLES))
            }

            mShowBadge.postValue(showBadge)
            mNotificationHistory.postValue(notifications)
        }

        private fun processNotificationItems(
            notificationsPayload: List<NotificationHistoryPayload>?,
            undefinedNotifications: ArrayList<NotificationHistoryItem>,
            vehiclesMap: Map<String, VehicleInfo>,
            notificationsMap: LinkedHashMap<String, NotificationHistoryPayload?>,
            set: SortedSet<String>,
        ): Boolean {
            var showBadge = false
            notificationsPayload?.forEach { payload ->
                when {
                    payload.vin.isNullOrBlank() -> {
                        payload.payload?.let { undefinedNotifications.addAll(it) }
                    }
                    notificationsMap.containsKey(payload.vin!!) -> {
                        notificationsMap[payload.vin!!] =
                            payload.apply {
                                modelDesc = vehiclesMap[payload.vin!!]?.let {
                                    getDisplayName(
                                        it,
                                    )
                                } ?: payload.vin
                            }
                    }
                    else -> return@forEach
                }

                payload.payload?.forEach { item ->
                    if (!showBadge && item.isRead != true) {
                        showBadge = true
                    }

                    item.displayCategory?.let {
                        if (it.isNotBlank()) {
                            set.add(it)
                        }
                    }
                }
            }
            return showBadge
        }

        private fun getDisplayName(vehicleInfo: VehicleInfo?): String =
            if (vehicleInfo?.modelDescription.isNullOrBlank()) {
                Strings.nullToEmpty(vehicleInfo?.vin)
            } else {
                "%s %s"
                    .format(
                        Strings.nullToEmpty(vehicleInfo?.modelYear),
                        Strings.nullToEmpty(vehicleInfo?.modelDescription),
                    ).trim()
            }

        companion object {
            const val GENERAL_NOTIFICATIONS = "GENERAL"
            const val ALL_VEHICLES = "All Vehicles"
        }
    }
