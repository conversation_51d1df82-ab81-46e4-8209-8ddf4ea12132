package com.toyota.oneapp.ui.newdashboard.viewmodel

import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.constants.VIN_LIST_RETRY_TIMES
import com.toyota.oneapp.model.vehicle.VehiclelListResponse
import com.toyota.oneapp.network.api.manager.VehicleAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class VehicleListViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val vehicleManager: VehicleAPIManager,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        fun getVehiclesList() {
            vehicleManager.sendGetVehicleListRequest(
                VIN_LIST_RETRY_TIMES,
                object : BaseCallback<VehiclelListResponse>() {
                    override fun onSuccess(response: VehiclelListResponse) {
                        val payload = response.payload
                        applicationData.setVehicleList(payload)
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        analyticsLogger.logEvent(AnalyticsEvent.VIN_LIST_FAILURE)
                    }

                    override fun onComplete() {
                    }
                },
            )
        }
    }
