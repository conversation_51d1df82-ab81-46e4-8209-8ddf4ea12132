package com.toyota.oneapp.ui.newdashboard

import com.toyota.oneapp.model.vehicle.OwnersManualResponse
import com.toyota.oneapp.network.api.manager.VehicleAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BasePresenter
import javax.inject.Inject

class OwnersManualPresenter
    @Inject
    constructor(
        private val vehicleManager: VehicleAPIManager,
    ) : BasePresenter<OwnersManualPresenter.View>() {
        fun getOwnerManualPdfLink(documentURI: String) {
            view?.showProgressDialog()
            vehicleManager.getVehiclesOwnerManualLink(
                brand,
                documentURI,
                object : BaseCallback<OwnersManualResponse>() {
                    override fun onSuccess(response: OwnersManualResponse) {
                        val pdfLink = response.payload.pdfLink
                        view?.openPdfLink(pdfLink)
                    }

                    override fun onFailError(
                        code: Int,
                        errorMsg: String?,
                    ) {
                        view?.showDialog(errorMsg)
                    }

                    override fun onComplete() {
                        view?.hideProgressDialog()
                    }
                },
            )
        }

        interface View : BaseView {
            fun sendPdfLink(pdfLink: String?)

            fun openPdfLink(pdfLink: String?)
        }
    }
