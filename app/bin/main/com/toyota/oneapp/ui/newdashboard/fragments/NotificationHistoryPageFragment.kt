package com.toyota.oneapp.ui.newdashboard.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.adapter.NotificationHistoryAdapter
import com.toyota.oneapp.databinding.ActivityNotificationHistoryBinding
import com.toyota.oneapp.fcm.ToyotaFCMService
import com.toyota.oneapp.model.dashboard.NotificationHistoryItem
import com.toyota.oneapp.model.dashboard.NotificationHistoryPayload
import com.toyota.oneapp.ui.flutter.VisitedNotification
import com.toyota.oneapp.ui.newdashboard.NotificationHistoryFragment
import com.toyota.oneapp.util.IntentUtil
import toyotaone.commonlib.eventbus.RxBus
import toyotaone.commonlib.log.LogTool
import kotlin.collections.set

class NotificationHistoryPageFragment :
    Fragment(),
    NotificationCategoryListener,
    NotificationHistoryAdapter.NotificationDeepLinkListener {
    private lateinit var binding: ActivityNotificationHistoryBinding
    private var adapter: NotificationHistoryAdapter? = null
    private val historyItemMap = HashMap<String, MutableList<NotificationHistoryItem>>()
    private var selectedCategories = ALL_NOTIFICATION
    private var onScrollListener: RecyclerView.OnScrollListener? = null
    private var lastShownPosition: Int = 0
    private val viewedMessageIds = mutableSetOf<String>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = ActivityNotificationHistoryBinding.inflate(inflater, container, false)
        val notificationHistoryPayload: NotificationHistoryPayload? =
            arguments?.getParcelable(NOTIFICATION_INFO_TAG)
        binding.nhList.layoutManager = LinearLayoutManager(context)
        notificationHistoryPayload?.payload?.let {
            populateHistoryItems(it)
            LogTool.d(
                TAG,
                "vin is ${notificationHistoryPayload.vin} and History Item Map: $historyItemMap",
            )
        }
        setAdapter()
        lastShownPosition = 0
        return binding.root
    }

    companion object {
        const val NOTIFICATION_INFO_TAG = "NotificationInfo"
        const val ALL_NOTIFICATION = "All Notifications"
        private val TAG = NotificationHistoryPageFragment::class.java.simpleName

        fun newInstance(payload: NotificationHistoryPayload?): NotificationHistoryPageFragment {
            val args = Bundle()
            args.putParcelable(NOTIFICATION_INFO_TAG, payload)
            val fragment = NotificationHistoryPageFragment()
            fragment.arguments = args
            return fragment
        }
    }

    fun updateNHReadStatus(
        nhList: List<NotificationHistoryPayload>?,
        currentPagePosition: Int,
    ) {
        val updatedNotificationHistoryPayload = nhList?.getOrNull(currentPagePosition)
        updatedNotificationHistoryPayload?.payload?.let {
            populateHistoryItems(it)
            LogTool.d(
                TAG,
                "vin is ${updatedNotificationHistoryPayload.vin} and History Item Map: $historyItemMap",
            )
        }
        setAdapter()
    }

    override fun onPause() {
        super.onPause()
        (parentFragment as? NotificationHistoryFragment)?.let {
            adapter?.getViewedMessageIds(lastShownPosition)?.let { messageIds ->
                viewedMessageIds.addAll(messageIds)
                LogTool.d(TAG, "Message ids retrieved: $viewedMessageIds")
                if (!viewedMessageIds.isNullOrEmpty()) {
                    it.viewModel.markRead(viewedMessageIds.toList())
                }
            }
        }
        (parentFragment as? NotificationHistoryFragment)?.unregister(this)
        LogTool.d(TAG, "On pause view")
    }

    override fun onResume() {
        super.onResume()
        LogTool.d(TAG, "On resume view")
        (parentFragment as? NotificationHistoryFragment)?.registerForFilterChange(this)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        (parentFragment as? NotificationHistoryFragment)?.let {
            adapter?.getViewedMessageIds(lastShownPosition)?.let { messageIds ->
                viewedMessageIds.addAll(messageIds)
                LogTool.d(TAG, "Message ids retrieved: $viewedMessageIds")
                if (!viewedMessageIds.isNullOrEmpty()) {
                    // viewmodel scope will be cleared once activity is destroyed, In this case request will be cancelled.
                    RxBus.get().post(VisitedNotification(viewedMessageIds.toList()))
                }
            }
        }
    }

    override fun onCategorySelected(category: String) {
        LogTool.d(TAG, "On category selected: $category")
        adapter?.apply {
            getViewedMessageIds(lastShownPosition)?.let { viewedMessageIds.addAll(it) }
            lastShownPosition = 0
            historyItemMap[category].isNullOrEmpty().let {
                if (!it) {
                    filter(category)
                }
                toggleView(!it)
            }
        }
    }

    private fun setAdapter() {
        when {
            historyItemMap.isEmpty() || !historyItemMap.containsKey(selectedCategories) -> {
                toggleView(false)
                binding.nhList.adapter = null
                binding.nhList.invalidate()
                adapter = null
            }
            adapter == null -> {
                toggleView(true)
                adapter = NotificationHistoryAdapter(historyItemMap, selectedCategories, this)
                binding.nhList.adapter = adapter
            }
            adapter != null -> {
                adapter?.updateReadStatus(historyItemMap)
            }
        }
    }

    private fun toggleView(notificationsAvailable: Boolean) {
        binding.nhList.visibility = if (notificationsAvailable) View.VISIBLE else View.INVISIBLE
        binding.clNoNotification.visibility =
            if (notificationsAvailable) View.GONE else View.VISIBLE
        if (notificationsAvailable) {
            if (onScrollListener == null) {
                onScrollListener =
                    object : RecyclerView.OnScrollListener() {
                        override fun onScrolled(
                            recyclerView: RecyclerView,
                            dx: Int,
                            dy: Int,
                        ) {
                            super.onScrolled(recyclerView, dx, dy)
                            val position =
                                (recyclerView.layoutManager as LinearLayoutManager).findLastCompletelyVisibleItemPosition()
                            if (position > lastShownPosition) {
                                lastShownPosition = position
                            }
                            LogTool.d(
                                TAG,
                                "On scroll -> Last item position visible: $position and max position: $lastShownPosition",
                            )
                        }

                        override fun onScrollStateChanged(
                            recyclerView: RecyclerView,
                            newState: Int,
                        ) {
                            super.onScrollStateChanged(recyclerView, newState)
                            val position =
                                (recyclerView.layoutManager as LinearLayoutManager).findLastCompletelyVisibleItemPosition()
                            if (position > lastShownPosition) {
                                lastShownPosition = position
                            }
                        }
                    }
            }
            binding.nhList.addOnScrollListener(onScrollListener!!)
        } else {
            binding.nhList.clearOnScrollListeners()
        }
    }

    private fun populateHistoryItems(historyItems: ArrayList<NotificationHistoryItem>) {
        if (historyItems.isNullOrEmpty()) {
            return
        }
        historyItemMap[ALL_NOTIFICATION] = historyItems
        for (item in historyItems) {
            val identifier =
                (if (item.displayCategory.isNullOrEmpty()) item.category else item.displayCategory)?.trim()
                    ?: ""
            var historyList = historyItemMap[identifier]
            if (historyList == null) {
                historyList = mutableListOf()
                historyItemMap[identifier] = historyList
            }
            historyList.add(item)
        }
    }

    override fun onDeepLinkClicked(notificationHistoryItem: NotificationHistoryItem) {
        val intent = IntentUtil.getOADashBoardIntent(requireActivity())
        intent.putExtra(ToyotaFCMService.CATEGORY, notificationHistoryItem.category)
        intent.putExtra(ToyotaFCMService.VIN, notificationHistoryItem.vin)
        activity?.startActivity(intent)
    }
}

interface NotificationCategoryListener {
    fun onCategorySelected(category: String)
}
