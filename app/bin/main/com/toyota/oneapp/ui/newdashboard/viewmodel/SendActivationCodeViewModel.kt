package com.toyota.oneapp.ui.newdashboard.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.extensions.getPhoneNumberFormat
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.network.api.repository.SmsOptInRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.newdashboard.RemoteActivateActivity.ACTIVATION_TYPE_EMAIL
import com.toyota.oneapp.ui.newdashboard.RemoteActivateActivity.ACTIVATION_TYPE_MOBILE
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class SendActivationCodeViewModel
    @Inject
    constructor(
        private val idpData: IDPData,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val smsOptInRepository: SmsOptInRepository,
    ) : BaseViewModel() {
        init {
            getConsentStatus()
        }

        val email = idpData.getUserEmail()

        val phoneNumber: String?
            get() {
                return idpData.getPhoneNumber()?.getPhoneNumberFormat()
            }

        private val _smsConsent = MutableLiveData<String?>()
        val smsConsent: LiveData<String?> = _smsConsent

        private val _activationCodeNavEvents = SingleLiveEvent<ActivationCodeNavEvent>()
        val activationCodeNavEvents: LiveData<ActivationCodeNavEvent> = _activationCodeNavEvents

        private val _selectedActivationType = MutableLiveData(ACTIVATION_TYPE_MOBILE)
        val selectedActivationType: LiveData<Int> = _selectedActivationType

        fun getConsentStatus() {
            viewModelScope.launch {
                val response = smsOptInRepository.getConsentStatus(idpData.getPhoneNumber())
                if (response.data?.payLoad?.consent != null) {
                    _smsConsent.value = response.data?.payLoad?.consent
                } else {
                    // do nothing
                }
            }
        }

        fun onTextMessageClick() {
            _selectedActivationType.postValue(ACTIVATION_TYPE_MOBILE)
        }

        fun onEmailClick() {
            _selectedActivationType.postValue(ACTIVATION_TYPE_EMAIL)
        }

        fun onSendCodeClick() {
            if (_selectedActivationType.value == ACTIVATION_TYPE_MOBILE &&
                idpData.phoneNumberVerified() ||
                _selectedActivationType.value == ACTIVATION_TYPE_EMAIL
            ) {
                _activationCodeNavEvents.postValue(
                    selectedActivationType.value?.let {
                        ActivationCodeNavEvent.ActivationCodeSentEvent(it)
                    },
                )
            } else {
                _activationCodeNavEvents.postValue(
                    idpData.getPhoneNumber()?.let { number ->
                        ActivationCodeNavEvent.VerifyPhoneNavEvent(
                            number,
                            oneAppPreferenceModel.getAccountInfoSubscriber() ?: AccountInfoSubscriber(),
                        )
                    },
                )
            }
        }

        fun onCancelClick() {
            _activationCodeNavEvents.postValue(ActivationCodeNavEvent.CancelNavEvent)
        }

        sealed class ActivationCodeNavEvent {
            object CancelNavEvent : ActivationCodeNavEvent()

            data class ActivationCodeSentEvent(
                val activationType: Int,
            ) : ActivationCodeNavEvent()

            data class VerifyPhoneNavEvent(
                val phoneNum: String,
                val subscriber: AccountInfoSubscriber,
            ) : ActivationCodeNavEvent()
        }

        companion object {
            const val TAG = "SendActivationCodeViewModel"
        }
    }
