package com.toyota.oneapp.ui.newdashboard

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivitySendActivationCodeBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.ui.accountsettings.VerifyEmailPhoneActivity
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import com.toyota.oneapp.ui.newdashboard.RemoteActivateActivity.*
import com.toyota.oneapp.ui.newdashboard.viewmodel.SendActivationCodeViewModel
import com.toyota.oneapp.ui.newdashboard.viewmodel.SendActivationCodeViewModel.ActivationCodeNavEvent.*
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SendActivationCodeActivity : DataBindingBaseActivity<ActivitySendActivationCodeBinding>() {
    val mViewModel: SendActivationCodeViewModel by viewModels()

    override fun getLayoutId() = R.layout.activity_send_activation_code

    override fun initViews(savedInstance: Bundle?) {
        binding.apply {
            viewModel = mViewModel
            performActivitySetup(toolbar)
        }

        observeBaseEvents(mViewModel)
        observeNavEvents(mViewModel)
    }

    private fun observeBaseEvents(viewModel: SendActivationCodeViewModel) {
        viewModel.smsConsent.observe(this) {
            it?.let {
                if (it.isNotBlank() && it == ToyotaConstants.PHONE_VERIFICATION_CONSENT_YES) {
                    binding.textMessageLayout.visibility = View.VISIBLE
                } else {
                    binding.textMessageLayout.visibility = View.GONE
                }
            }
        }
    }

    private fun observeNavEvents(viewModel: SendActivationCodeViewModel) {
        viewModel.activationCodeNavEvents.observe(this) {
            when (it) {
                is CancelNavEvent -> {
                    finish()
                }

                is VerifyPhoneNavEvent -> {
                    val intent =
                        Intent(this, VerifyEmailPhoneActivity::class.java)
                            .apply {
                                putExtra(ToyotaConstants.EDIT_VALUE, it.phoneNum)
                                putExtra(ToyotaConstants.EDITING_ACCOUNTINFO, it.subscriber)
                            }

                    startActivity(intent)
                }

                is ActivationCodeSentEvent -> {
                    val intent =
                        Intent(this, RemoteActivateActivity::class.java).apply {
                            putExtra(ACTIVATION_TYPE_KEY, it.activationType)
                        }
                    startActivity(intent)
                }
            }
        }

        viewModel.selectedActivationType.observe(this) {
            when (it) {
                ACTIVATION_TYPE_MOBILE -> {
                    binding.textMessageLayout.background =
                        ContextCompat.getDrawable(
                            this,
                            R.drawable.shape_white_rounded_corners_red_border_8dp,
                        )

                    binding.emailLayout.background =
                        ContextCompat.getDrawable(this, R.drawable.shape_white_rounded_corners_8dp)
                }

                ACTIVATION_TYPE_EMAIL -> {
                    binding.emailLayout.background =
                        ContextCompat.getDrawable(
                            this,
                            R.drawable.shape_white_rounded_corners_red_border_8dp,
                        )

                    binding.textMessageLayout.background =
                        ContextCompat.getDrawable(this, R.drawable.shape_white_rounded_corners_8dp)
                }

                else -> {
                    // no-op
                }
            }.exhaustive
        }
    }

    companion object {
        const val TAG = "SendActivationCodeBottomSheetDialogFragment"
    }
}
