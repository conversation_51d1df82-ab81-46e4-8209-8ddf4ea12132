package com.toyota.oneapp.ui.newdashboard

import android.os.Bundle
import com.toyota.oneapp.R
import com.toyota.oneapp.ui.MVPBaseActivity
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.mvpcore.EXMVPBasePresenter

@AndroidEntryPoint
class RemoteCommandDetailsActivity : MVPBaseActivity<EXMVPBasePresenter<*>>() {
    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        setContentView(R.layout.activity_remote_command_details)
    }

    override fun createPresenter(): EXMVPBasePresenter<*>? = null
}
