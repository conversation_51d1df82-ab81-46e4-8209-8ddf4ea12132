package com.toyota.oneapp.ui.newdashboard

import android.os.Bundle
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.NotificationCategoryAdapter
import com.toyota.oneapp.adapter.NotificationHistoryPageAdapter
import com.toyota.oneapp.adapter.NotificationTitleAdapter
import com.toyota.oneapp.databinding.ActivityNotificationBinding
import com.toyota.oneapp.model.dashboard.NotificationHistoryPayload
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.newdashboard.fragments.NotificationCategoryListener
import com.toyota.oneapp.ui.newdashboard.fragments.NotificationHistoryPageFragment
import com.toyota.oneapp.ui.newdashboard.fragments.NotificationHistoryPageFragment.Companion.ALL_NOTIFICATION
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.log.LogTool

@AndroidEntryPoint
class NotificationHistoryFragment :
    BaseDataBindingFragment<ActivityNotificationBinding>(),
    NotificationTitleAdapter.NotificationListener,
    NotificationCategoryListener {
    val viewModel: NotificationHistoryViewModel by activityViewModels()
    private lateinit var bottomSheetBehavior: BottomSheetBehavior<View>
    private var notificationCategoryAdapter: NotificationCategoryAdapter? = null
    private var nhList: List<NotificationHistoryPayload>? = null
    private val listeners = HashSet<NotificationCategoryListener>()
    var notificationTileAdapter: NotificationTitleAdapter? = null
    var notificationPagerAdapter: NotificationHistoryPageAdapter? = null
    var selectedCategory = ALL_NOTIFICATION

    override fun onViewBound(
        binding: ActivityNotificationBinding,
        savedInstance: Bundle?,
    ) {
        setBottomPanel(binding)
        setAdapters(binding)
        setViewPager(binding)
        viewModel.notificationHistory.observe(viewLifecycleOwner) { payload ->
            nhList = payload
            onNotificationsReceived(payload)
        }
    }

    override fun getLayout(): Int = R.layout.activity_notification

    private fun setAdapters(binding: ActivityNotificationBinding) {
        if (notificationTileAdapter == null) {
            binding.notificationTitleList.layoutManager = LinearLayoutManager(activity)
            (binding.notificationTitleList.layoutManager as LinearLayoutManager).orientation = LinearLayoutManager.HORIZONTAL
            notificationTileAdapter = NotificationTitleAdapter(null, this)
            binding.notificationTitleList.adapter = notificationTileAdapter
        }

        if (notificationPagerAdapter == null) {
            notificationPagerAdapter = NotificationHistoryPageAdapter(this, null)
            binding.notificationViewpager.adapter = notificationPagerAdapter
            binding.notificationViewpager.isUserInputEnabled = true
        }
    }

    private fun collapseBottomSheet(binding: ActivityNotificationBinding) {
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
        binding.dimLayout.visibility = View.GONE
    }

    private fun refreshNotifications(list: List<NotificationHistoryPayload>) {
        notificationTileAdapter?.refresh(list)
        notificationPagerAdapter?.refresh(list)
    }

    private fun onNotificationsReceived(notifications: List<NotificationHistoryPayload>) {
        viewDataBinding.progressLoader.visibility = View.VISIBLE
        if (notifications.size <= 1) {
            viewDataBinding.notificationTitleList.visibility = View.GONE
            if (notifications.isEmpty()) {
                viewDataBinding.notificationViewpager.visibility = View.GONE
                viewDataBinding.noNotificationLayout.visibility = View.VISIBLE
                viewDataBinding.progressLoader.visibility = View.GONE
            } else {
                viewDataBinding.noNotificationLayout.visibility = View.GONE
            }
        } else {
            viewDataBinding.notificationTitleList.visibility = View.VISIBLE
            viewDataBinding.notificationViewpager.visibility = View.VISIBLE
            viewDataBinding.noNotificationLayout.visibility = View.GONE
        }
        refreshNotifications(notifications)
        viewDataBinding.progressLoader.visibility = View.GONE
    }

    override fun onPageSelected(position: Int) {
        viewDataBinding.notificationTitleList.adapter?.notifyDataSetChanged()
        viewDataBinding.notificationViewpager.setCurrentItem(position, false)
    }

    override fun onCategorySelected(category: String) {
        LogTool.d(TAG, "Category selected : $category")
        selectedCategory = category
        viewDataBinding.ivBottomMenu.isActivated = ALL_NOTIFICATION != category
        collapseBottomSheet(viewDataBinding)
        notifyListeners(category)
    }

    private fun notifyListeners(category: String) {
        for (listener in listeners) {
            listener.onCategorySelected(category)
        }
    }

    fun registerForFilterChange(listener: NotificationCategoryListener) {
        listeners.add(listener)
        listener.onCategorySelected(selectedCategory)
    }

    fun unregister(listener: NotificationCategoryListener) {
        listeners.remove(listener)
    }

    private fun setViewPager(binding: ActivityNotificationBinding) {
        binding.notificationViewpager.registerOnPageChangeCallback(
            object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    val activeFrag =
                        childFragmentManager.findFragmentByTag(
                            "f${notificationPagerAdapter?.getItemId(position)}",
                        ) as? NotificationHistoryPageFragment
                    activeFrag?.updateNHReadStatus(nhList, position)
                    (binding.notificationTitleList.adapter as NotificationTitleAdapter).selectedItem = position
                    (binding.notificationTitleList.adapter as NotificationTitleAdapter).notifyDataSetChanged()
                    binding.notificationTitleList.smoothScrollToPosition(position)
                }
            },
        )
        binding.notificationViewpager.offscreenPageLimit = 3
    }

    private fun setBottomPanel(binding: ActivityNotificationBinding) {
        val bottomSheet: View = binding.bottomSheetNavigation
        bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet)
        bottomSheetBehavior.addBottomSheetCallback(
            object : BottomSheetBehavior.BottomSheetCallback() {
                override fun onStateChanged(
                    bottomSheet: View,
                    newState: Int,
                ) {
                    LogTool.d(TAG, "On state changed: $newState")
                    when (newState) {
                        BottomSheetBehavior.STATE_EXPANDED -> viewDataBinding.dimLayout.visibility = View.VISIBLE
                        BottomSheetBehavior.STATE_COLLAPSED -> collapseBottomSheet(binding)
                        BottomSheetBehavior.STATE_HIDDEN -> {
                            if (viewDataBinding.dimLayout.visibility != View.GONE) viewDataBinding.dimLayout.visibility = View.GONE
                        }
                        else -> {}
                    }
                }

                override fun onSlide(
                    bottomSheet: View,
                    slideOffset: Float,
                ) {
                    // Intentionally unimplemented
                }
            },
        )
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN

        binding.ivBottomMenu.setOnClickListener {
            if (bottomSheetBehavior.state != BottomSheetBehavior.STATE_EXPANDED && viewModel.categories.isNotEmpty()) {
                LogTool.d(TAG, "The categories are ${viewModel.categories}")
                if (notificationCategoryAdapter == null) {
                    binding.notificationCategoryList.layoutManager = LinearLayoutManager(context)
                    notificationCategoryAdapter =
                        NotificationCategoryAdapter(
                            viewModel.categories,
                            this,
                            selectedCategory,
                        )
                    binding.notificationCategoryList.adapter = notificationCategoryAdapter
                }
                notificationCategoryAdapter?.let {
                    it.refreshList(viewModel.categories, selectedCategory)
                    bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
                    viewDataBinding.dimLayout.visibility = View.VISIBLE
                }
            } else {
                collapseBottomSheet(binding)
            }
        }

        binding.ivNotificationClose.setOnClickListener {
            LogTool.d(TAG, "Close button clicked.Collapsing")
            collapseBottomSheet(binding)
        }

        binding.toolbar.setNavigationOnClickListener {
            activityBackPressed()
        }
    }

    private fun activityBackPressed(): Boolean {
        val dispatcher = requireActivity().onBackPressedDispatcher
        dispatcher.onBackPressed()
        return true
    }

    companion object {
        const val TAG = "NotificationHistoryFragment"
    }
}
