package com.toyota.oneapp.ui.ubi

import com.toyota.oneapp.model.ubi.UserOptStatusRequest
import com.toyota.oneapp.model.ubi.UserOptStatusResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

interface UBIOffersAPIService {
    @Headers("Content-Type: application/json")
    @POST("/oneapi/v2/ubi/optdetails")
    suspend fun ubiUserOptStatus(
        @Body body: UserOptStatusRequest?,
    ): Response<UserOptStatusResponse?>
}
