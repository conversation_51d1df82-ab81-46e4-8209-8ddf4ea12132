package com.toyota.oneapp.ui.ubi

import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.SpecialOfferLayoutBinding
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseActivity
import com.toyota.oneapp.ui.DataConsentsDetailActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import javax.inject.Inject

@AndroidEntryPoint
class SpecialOfferActivity : BaseActivity() {
    private var elapseNoThanksTime: Long = 0

    private lateinit var binding: SpecialOfferLayoutBinding

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @Inject
    lateinit var applicationData: ApplicationData

    @Inject
    lateinit var preferenceModel: OneAppPreferenceModel
    private lateinit var vin: String
    private lateinit var guid: String
    private val offerViewModel: SpecialOfferViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding = DataBindingUtil.setContentView(this, R.layout.special_offer_layout)
        setupToolbar()
        vin = applicationData.getSelectedVehicle()?.vin.orEmpty()
        guid = preferenceModel.getGuid()
        binding.btnSavePrefs.text = getString(R.string.UBI_share_vehicle_information)
        setOnClickListeners()
    }

    override fun onStart() {
        super.onStart()
        offerViewModel.run {
            errorMessageObserver.observe(
                this@SpecialOfferActivity,
                Observer {
                    showDialog(it)
                },
            )

            payloadOptedInResponse.observe(
                this@SpecialOfferActivity,
                Observer {
                    backToDashBoard()
                },
            )

            payloadOptedOutResponse.observe(
                this@SpecialOfferActivity,
                Observer {
                    finish()
                },
            )
        }
    }

    private fun backToDashBoard() {
        DialogUtil.showDialog(
            this,
            getString(R.string.DataConsents_Wifi_success_title),
            getString(
                R.string.UBI_card_detail,
            ),
            getString(R.string.Common_ok),
            null,
            object :
                OnCusDialogInterface {
                override fun onConfirmClick() {
                    analyticsLogger.logEvent(AnalyticsEvent.UBI_CONFIRM)
                    finish()
                }

                override fun onCancelClick() {}
            },
            false,
        )
    }

    private fun setOnClickListeners() {
        binding.ubiCheckbox.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.btnSavePrefs.isEnabled = true
                binding.noThanksTv.isClickable = false
            } else {
                binding.btnSavePrefs.isEnabled = false
                binding.noThanksTv.isClickable = true
            }
        }

        binding.terms.setOnClickListener {
            val intent = Intent(this, DataConsentsDetailActivity::class.java)
            intent.putExtra(
                ToyotaConstants.DATA_CONSENT_UBI_TITLE,
                this.getString(R.string.DataConsents_UBI_title),
            )
            intent.putExtra(
                ToyotaConstants.UBI_LEARN_MORE,
                this.getString(R.string.UBI_learn_more_detail),
            )
            startActivity(intent)
        }

        binding.noThanksTv.setOnClickListener {
            if (SystemClock.elapsedRealtime() - elapseNoThanksTime < 2000) {
                return@setOnClickListener
            }
            elapseNoThanksTime = SystemClock.elapsedRealtime()
            DialogUtil.showMessageDialog(
                this,
                null,
                getString(R.string.DataConsents_detail_ubi_no_thanks_title),
                getString(R.string.Common_confirm),
                getString(R.string.Common_cancel),
                object : OnCusDialogInterface {
                    override fun onConfirmClick() {
                        analyticsLogger.logEvent(AnalyticsEvent.UBI_WAIVE)
                        offerViewModel.sendOptedOutRequest(vin, guid)
                    }

                    override fun onCancelClick() {}
                },
                false,
            )
        }

        binding.btnSavePrefs.setOnClickListener {
            offerViewModel.sendOptInRequest(vin, guid)
        }
    }

    private fun setupToolbar() {
        binding.mToolbarImgLogo.setImageResource(R.drawable.logo)
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
    }
}
