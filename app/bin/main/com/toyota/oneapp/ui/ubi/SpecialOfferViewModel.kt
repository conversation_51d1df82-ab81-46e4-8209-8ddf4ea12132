package com.toyota.oneapp.ui.ubi

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.model.ubi.UserOptStatusResponse
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class SpecialOfferViewModel
    @Inject
    constructor(
        private val specialOfferRepository: SpecialOfferRepository,
    ) : BaseViewModel() {
        val errorMessageObserver = SingleLiveEvent<String>()
        private var responseData: UserOptStatusResponse? = null
        val payloadOptedInResponse = SingleLiveEvent<UserOptStatusResponse>()
        val payloadOptedOutResponse = SingleLiveEvent<UserOptStatusResponse>()

        fun sendOptedOutRequest(
            vin: String,
            guid: String,
        ) {
            viewModelScope.launch {
                showProgress()
                val response = specialOfferRepository.callOptInAndOptOutRequest(vin, guid, "o", true)
                hideProgress()
                when (response) {
                    is Resource.Success -> {
                        response.data?.let {
                            responseData = it
                            payloadOptedOutResponse.value = it
                        }
                    }

                    is Resource.Failure -> {
                        errorMessageObserver.value = response.message
                    }
                    else -> {}
                }
            }
        }

        fun sendOptInRequest(
            vin: String,
            guid: String,
        ) {
            viewModelScope.launch {
                showProgress()
                val response = specialOfferRepository.callOptInAndOptOutRequest(vin, guid, "i", false)
                hideProgress()
                when (response) {
                    is Resource.Success -> {
                        response.data?.let {
                            responseData = it
                            payloadOptedInResponse.value = it
                        }
                    }
                    is Resource.Failure -> {
                        errorMessageObserver.value = response.message
                    }
                    else -> {}
                }
            }
        }
    }
