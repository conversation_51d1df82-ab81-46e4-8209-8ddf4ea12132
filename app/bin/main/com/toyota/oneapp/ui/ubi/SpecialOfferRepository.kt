package com.toyota.oneapp.ui.ubi

import com.toyota.oneapp.model.ubi.UserOptStatusRequest
import com.toyota.oneapp.model.ubi.UserOptStatusResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class SpecialOfferRepository
    @Inject
    constructor(
        private val ubiOffersAPIService: UBIOffersAPIService,
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun callOptInAndOptOutRequest(
            vin: String,
            guid: String,
            preference: String,
            value: Boolean,
        ): Resource<UserOptStatusResponse?> {
            val optStatusRequest =
                UserOptStatusRequest().apply {
                    this.vin = vin
                    this.guid = guid
                    this.ubiPreference = preference
                    this.signUpCardDeclineAction = value
                }
            return makeApiCall {
                ubiOffersAPIService.ubiUserOptStatus(optStatusRequest)
            }
        }
    }
