package com.toyota.oneapp.ui

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import androidx.databinding.DataBindingUtil
import com.tbruyelle.rxpermissions2.Permission
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.AddVehicleLayoutBinding
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.WearUtil.sendToWear
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.permission.PermissionUtil
import toyotaone.commonlib.wear.WearAPIType
import toyotaone.commonlib.wear.WearConstants.BUS_OUTBOUND_DATA_KEY
import toyotaone.commonlib.wear.WearConstants.BUS_OUTBOUND_PATH
import toyotaone.commonlib.wear.WearResponse
import javax.inject.Inject

@AndroidEntryPoint
class AddVehicleActivity : UiBaseActivity() {
    lateinit var binding: AddVehicleLayoutBinding

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @Inject
    lateinit var regionManager: RegionManager

    @Inject
    lateinit var preferenceModel: OneAppPreferenceModel

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding = DataBindingUtil.setContentView(this, R.layout.add_vehicle_layout)
        setSupportActionBar(binding.toolbar)
        sendToWear(
            BUS_OUTBOUND_PATH,
            BUS_OUTBOUND_DATA_KEY,
            WearResponse(
                WearAPIType.SWITCH_VEHICLE,
                null,
                null,
                getString(R.string.SmartWatch_Please_Add_Vehicle),
            ).toJsonString(),
        )

        binding.btScanVin.setOnClickListener {
            PermissionUtil.checkOnlyCameraPermissions(this) { permission: Permission ->
                if (permission.granted) {
                    val intent =
                        Intent(this, AddVINManualEntryActivity::class.java).apply {
                            putExtra(AddVINManualEntryActivity.SCAN_VIN, true)
                        }
                    startActivity(intent)
                } else if (!permission.shouldShowRequestPermissionRationale) {
                    showForceAllowPermissionDialog()
                }
            }
        }

        binding.btEnterVinManual.setOnClickListener {
            startActivity(Intent(this, AddVINManualEntryActivity::class.java))
        }

        handleErrorMessages(intent?.extras)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            onBackPressed()
        }
        return true
    }

    private fun handleErrorMessages(arguments: Bundle?) {
        val oneVL1001ErrorMsg = arguments?.getString(ToyotaConstants.ONE_VL_10001_MESSAGE)
        if (oneVL1001ErrorMsg != null) {
            showDialog(oneVL1001ErrorMsg)
        }
    }

    companion object {
        const val KEY_VEHICLE_DETAILS = "KEY_VEHICLE_DETAILS"
        const val KEY_VEHICLE_INFO = "KEY_VEHICLE_INFO"
        const val KEY_VEHICLE_NICK_NAME = "KEY_VEHICLE_NICK_NAME"
        const val KEY_VEHICLE_ASSOCIATION_ID = "KEY_VEHICLE_ASSOCIATION_ID"
    }
}
