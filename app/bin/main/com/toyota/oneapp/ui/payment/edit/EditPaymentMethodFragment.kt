package com.toyota.oneapp.ui.payment.edit

import android.os.Bundle
import android.view.*
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentPaymentMethodEditBinding
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.payment.select.PaymentsFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class EditPaymentMethodFragment : BaseViewModelFragment() {
    private val viewModel: EditPaymentMethodViewModel by viewModels()

    private lateinit var binding: FragmentPaymentMethodEditBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
        observeBaseEvents(viewModel)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View =
        FragmentPaymentMethodEditBinding
            .inflate(
                inflater,
                container,
                false,
            ).also { binding ->
                binding.viewModel =
                    viewModel.apply {
                        onSave.observe(
                            viewLifecycleOwner,
                            Observer {
                                setFragmentResult(
                                    PaymentsFragment.RESULT_PAYMENTS,
                                    bundleOf(PaymentsFragment.KEY_REFRESH to true),
                                )
                                findNavController().popBackStack(R.id.payments, false)
                            },
                        )
                    }
                binding.lifecycleOwner = viewLifecycleOwner
                this.binding = binding
            }.root

    override fun onCreateOptionsMenu(
        menu: Menu,
        inflater: MenuInflater,
    ) {
        super.onCreateOptionsMenu(menu, inflater)
        inflater.inflate(R.menu.payment_method_edit, menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            R.id.save -> {
                if (binding.creditCard.validate().and(binding.address.validate())) {
                    viewModel.save()
                }
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
}
