package com.toyota.oneapp.ui.payment.edit

import androidx.lifecycle.LiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.model.subscription.UpdatePaymentRequest
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.widget.AddressView
import com.toyota.oneapp.ui.widget.CreditCardView
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class EditPaymentMethodViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val subscriptionAPIManager: SubscriptionAPIManager,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        val paymentRecord: PaymentRecord = state["payment_record"] ?: PaymentRecord(id = null)

        val creditCard =
            object : CreditCardView.CreditCard {
                override var name: String? = paymentRecord.creditCardHolderName
                override var number: String? = paymentRecord.creditCardMaskNumber
                override var expirationMonth: Int? = paymentRecord.creditCardExpirationMonth
                override var expirationYear: Int? = paymentRecord.creditCardExpirationYear
                override var cvv: String? = null
            }

        val address =
            object : AddressView.Address {
                override var line1: String? = paymentRecord.creditCardAddress1
                override var line2: String? = null
                override var city: String? = paymentRecord.creditCardCity
                override var stateCode: String? = paymentRecord.creditCardState
                override var zipCode: String? = paymentRecord.creditCardPostalCode
                override var countryCode: String? = paymentRecord.creditCardCountry
            }

        private val onSaveEvent = SingleLiveEvent<Unit>()
        val onSave: LiveData<Unit> = onSaveEvent

        fun save() {
            showProgress()
            subscriptionAPIManager.updatePayment(
                paymentRecord,
                UpdatePaymentRequest(
                    cardHolderName = creditCard.name,
                    expirationMonth = creditCard.expirationMonth,
                    expirationYear = creditCard.expirationYear,
                    securityCode = creditCard.cvv,
                    addressLine1 = address.line1,
                    addressLine2 = address.line2,
                    city = address.city,
                    state = address.stateCode,
                    zipCode = address.zipCode,
                    country = address.countryCode,
                ),
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        analyticsLogger.logEvent(AnalyticsEvent.ZUORA_EDIT_PAYMENT_METHOD_SUCCESS)
                        showSuccessToastMessage(R.string.payment_method_updated)
                        onSaveEvent.call()
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        analyticsLogger.logEvent(AnalyticsEvent.ZUORA_EDIT_PAYMENT_METHOD_FAILED)
                        showErrorMessage(errorMsg)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }
    }
