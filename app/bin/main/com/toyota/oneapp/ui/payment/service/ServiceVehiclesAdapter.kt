package com.toyota.oneapp.ui.payment.service

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemVehicleImageBinding
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters

class ServiceVehiclesAdapter :
    RecyclerView.Adapter<ServiceVehiclesAdapter.VehicleImageViewHolder>(),
    BindableRecyclerViewAdapter<VehicleInfo> {
    private val vehicles = mutableListOf<VehicleInfo>()

    override fun setData(data: List<VehicleInfo>?) {
        vehicles.clear()
        data?.let { vehicles.addAll(data) }
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int = vehicles.size

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ) = VehicleImageViewHolder(
        ItemVehicleImageBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false,
        ),
    )

    override fun onBindViewHolder(
        holder: VehicleImageViewHolder,
        position: Int,
    ) {
        holder.bind(vehicles[position])
    }

    inner class VehicleImageViewHolder(
        private val binding: ItemVehicleImageBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(vehicle: VehicleInfo) {
            DataBindingAdapters.setVehicle(binding.image, vehicle)
        }
    }
}
