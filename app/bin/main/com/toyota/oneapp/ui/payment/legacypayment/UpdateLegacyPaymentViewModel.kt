package com.toyota.oneapp.ui.payment.legacypayment

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.subscription.SXMAccessTokenResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.SXMAPIManager
import com.toyota.oneapp.network.api.repository.FRIdpRepository
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17UIModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@HiltViewModel
class UpdateLegacyPaymentViewModel
    @Inject
    constructor(
        private val sxmManager: SXMAPIManager,
        private val preferenceModel: OneAppPreferenceModel,
        private val analyticsLogger: AnalyticsLogger,
        private val languageManager: LanguageManager,
        private val frIdpRepository: FRIdpRepository,
        private val state: SavedStateHandle,
    ) : BaseViewModel() {
        companion object {
            const val PAYMENT_INFORMATION_STORED = "Payment Information Stored"
            const val PAYMENT_INFORMATION_FAILED = "Payment Information Failed"
            const val PAYMENT_INFORMATION_CANCELLED = "Payment Information Cancelled"
            const val PAYMENT_INFORMATION_CONTINUE = "Payment Information Continue"
            const val USER_NEED_TO_RETRY = "User Need to Retry"
        }

        val updateLegacyPaymentNavigationEvents = SingleLiveEvent<UpdateLegacyPaymentNavigationEvent>()

        var vehicle: VehicleInfo = state["vehicleInfo"] ?: VehicleInfo()

        init {
            showProgress()
            getSXMTokenFromFRIDP()
        }

        private fun getSXMTokenFromFRIDP() {
            viewModelScope.launch {
                val resource = frIdpRepository.getSXMIdTokenFromFRIdp()
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.idToken?.let {
                            getSXMToken(sxmIdTokenFromFRIDP = it)
                        }
                            ?: updateLegacyPaymentNavigationEvents.postValue(
                                UpdateLegacyPaymentNavigationEvent.Error,
                            )
                        LogTool.d("SXM Token From IDP", resource.data?.idToken)
                    }
                    is Resource.Failure -> {
                        hideProgress()
                        updateLegacyPaymentNavigationEvents.postValue(
                            UpdateLegacyPaymentNavigationEvent.Error,
                        )
                    }
                    else -> {}
                }
            }
        }

        private fun getSXMToken(sxmIdTokenFromFRIDP: String = ToyotaConstants.EMPTY_STRING) {
            sxmManager.sendGetSxmToken(
                sxmIdTokenFromFRIDP,
                object : BaseCallback<SXMAccessTokenResponse>() {
                    override fun onSuccess(response: SXMAccessTokenResponse) {
                        analyticsLogger.logEvent(AnalyticsEvent.PAYMENT_TOKEN_RECEIVED)
                        updateLegacyPaymentNavigationEvents.postValue(
                            UpdateLegacyPaymentNavigationEvent.WebUIModel(createUIModel(response)),
                        )
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        updateLegacyPaymentNavigationEvents.postValue(
                            UpdateLegacyPaymentNavigationEvent.Error,
                        )
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        private fun createUIModel(accessTokenResponse: SXMAccessTokenResponse): PaymentCY17UIModel =
            PaymentCY17UIModel(
                url = BuildConfig.SXM_PAYMENT_PAGE_BASE_URL,
                authorization = accessTokenResponse.accessToken,
                guid = preferenceModel.getGuid(),
                vin = vehicle.vin,
                brand = if (vehicle.brand == "T") "toyota" else "lexus",
                productIds = "",
                totalAmount = "",
                totalTax = "",
                language = languageManager.getSXMPaymentLocaleString(),
            )

        fun webEvent(message: String) {
            LogTool.d("****", message)
            val storedPayment = if (message.contains(PAYMENT_INFORMATION_STORED, true)) message else ""
            val continuePayment = if (message.contains(PAYMENT_INFORMATION_CONTINUE, true)) message else ""
            when (message) {
                storedPayment -> {
                    analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_STORED)
                    showSuccessToastMessage(R.string.payment_method_updated)
                    updateLegacyPaymentNavigationEvents.postValue(
                        UpdateLegacyPaymentNavigationEvent.PaymentAddedSuccessfully,
                    )
                }
                PAYMENT_INFORMATION_FAILED -> {
                    analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_FAILED)
                    updateLegacyPaymentNavigationEvents.postValue(
                        UpdateLegacyPaymentNavigationEvent.Error,
                    )
                }
                PAYMENT_INFORMATION_CANCELLED -> {
                    analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_CANCELED)
                    updateLegacyPaymentNavigationEvents.postValue(
                        UpdateLegacyPaymentNavigationEvent.OnBack,
                    )
                }
                continuePayment -> {
                    analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_CONTINUE)
                    updateLegacyPaymentNavigationEvents.postValue(
                        UpdateLegacyPaymentNavigationEvent.PaymentAddedSuccessfully,
                    )
                }
                USER_NEED_TO_RETRY -> {
                    analyticsLogger.logEvent(AnalyticsEvent.SXM_PAYMENT_PAGE_ADD_PAYMENT_RETRY)
                    updateLegacyPaymentNavigationEvents.postValue(
                        UpdateLegacyPaymentNavigationEvent.Error,
                    )
                }
            }
        }
    }

sealed class UpdateLegacyPaymentNavigationEvent {
    data class WebUIModel(
        val paymentCY17UIModel: PaymentCY17UIModel,
    ) : UpdateLegacyPaymentNavigationEvent()

    object PaymentAddedSuccessfully : UpdateLegacyPaymentNavigationEvent()

    object Error : UpdateLegacyPaymentNavigationEvent()

    object OnBack : UpdateLegacyPaymentNavigationEvent()
}
