package com.toyota.oneapp.ui.payment.service

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentSelectServiceBinding
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.payment.service.SelectServiceFragmentDirections.Companion.actionPayments
import com.toyota.oneapp.ui.payment.service.SelectServiceFragmentDirections.Companion.actionUpdateLegacyPayment
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.recyclerview.HorizontalDividerItemDecoration

@AndroidEntryPoint
class SelectServiceFragment : BaseViewModelFragment() {
    private val viewModel: SelectServiceViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View =
        FragmentSelectServiceBinding
            .inflate(
                inflater,
                container,
                false,
            ).also { binding ->
                binding.recyclerView.run {
                    adapter = SelectServiceAdapter(viewModel::selectServiceItem)
                    addItemDecoration(
                        HorizontalDividerItemDecoration.Builder(requireContext())
                            .margin(resources.getDimensionPixelSize(R.dimen.margin_24dp), 0)
                            .colorResId(R.color.silver)
                            .showLastDivider()
                            .build(),
                    )

                    viewModel.subscriptions.observe(viewLifecycleOwner) { data ->
                        DataBindingAdapters.setRecyclerViewAdapterData(this, data, emptyList())
                    }
                }

                viewModel.apply {
                    onPayments.observe(viewLifecycleOwner) {
                        findNavController().navigate(actionPayments(it))
                    }
                    onUpdateLegacyPayments.observe(viewLifecycleOwner) {
                        findNavController().navigate(actionUpdateLegacyPayment(it))
                    }
                }

                binding.lifecycleOwner = viewLifecycleOwner
            }.root
}
