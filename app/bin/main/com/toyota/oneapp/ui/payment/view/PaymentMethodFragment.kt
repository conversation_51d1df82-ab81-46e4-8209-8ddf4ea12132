package com.toyota.oneapp.ui.payment.view

import android.os.Bundle
import android.view.*
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentPaymentMethodBinding
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.payment.select.PaymentsFragment
import com.toyota.oneapp.ui.payment.view.PaymentMethodFragmentDirections.Companion.actionEdit
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentMethodFragment : BaseViewModelFragment() {
    private val viewModel: PaymentMethodViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(viewModel)
        setHasOptionsMenu(true)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View =
        FragmentPaymentMethodBinding
            .inflate(
                inflater,
                container,
                false,
            ).also { binding ->
                binding.lifecycleOwner = viewLifecycleOwner
                binding.accountNumberLayout.hint =
                    getString(if (viewModel.isCreditCard) R.string.PaymentPage_CreditCardNumber else R.string.PaymentPage_BankAccountNumber)
                binding.accountNumberLayout.startIconDrawable = resources.getDrawable(viewModel.logoResId)
                binding.accountNumber.setText(viewModel.accountNumber)
                binding.expirationLayout.isVisible = viewModel.isCreditCard
                DataBindingAdapters.setExpiration(binding.expiration, viewModel.expirationMonth ?: 0, viewModel.expirationYear ?: 0)
                viewModel.isDefaultPayment.observe(viewLifecycleOwner) {
                    binding.defaultCard.isVisible = it
                }
            }.root

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.apply {
            onEdit.observe(viewLifecycleOwner) {
                findNavController().navigate(actionEdit(it))
            }
            onDelete.observe(viewLifecycleOwner) {
                setFragmentResult(
                    PaymentsFragment.RESULT_PAYMENTS,
                    bundleOf(PaymentsFragment.KEY_REFRESH to true),
                )
                findNavController().navigateUp()
            }
            onMakeDefault.observe(viewLifecycleOwner) {
                setFragmentResult(
                    PaymentsFragment.RESULT_PAYMENTS,
                    bundleOf(PaymentsFragment.KEY_REFRESH to true),
                )
            }
            onManageCard.observe(viewLifecycleOwner) {
                MaterialAlertDialogBuilder(requireContext()).apply {
                    setMessage(R.string.manage_payment_card_options)
                    setCancelable(true)
                    if (!it.isDefaultPayment) {
                        setNegativeButton(R.string.make_default) { _, _ ->
                            viewModel.makeDefault()
                        }
                    }
                    setNeutralButton(R.string.Common_remove) { _, _ ->
                        viewModel.delete()
                    }
                    if (it.isCreditCard) {
                        setPositiveButton(R.string.edit_card) { _, _ ->
                            viewModel.edit()
                        }
                    }
                }.show()
            }
            onDeleteDefault.observe(viewLifecycleOwner) {
                MaterialAlertDialogBuilder(requireContext()).apply {
                    setTitle(
                        getString(R.string.manage_payment_card_options_delete_default_title),
                    )
                    setMessage(
                        getString(R.string.manage_payment_card_options_delete_default_message),
                    )
                    setCancelable(true)
                    setPositiveButton(R.string.Common_okay) { _, _ ->
                    }
                }.show()
            }
        }
    }

    override fun onCreateOptionsMenu(
        menu: Menu,
        inflater: MenuInflater,
    ) {
        super.onCreateOptionsMenu(menu, inflater)
        inflater.inflate(R.menu.payment_method, menu)
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)
        menu.findItem(R.id.edit).isVisible = true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            R.id.edit -> {
                viewModel.chooseEdit()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
}
