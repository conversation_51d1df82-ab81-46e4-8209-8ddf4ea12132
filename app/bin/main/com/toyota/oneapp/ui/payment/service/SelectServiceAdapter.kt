package com.toyota.oneapp.ui.payment.service

import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.size
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import androidx.recyclerview.widget.RecyclerView.NO_POSITION
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemSelectServiceVehicleBinding
import com.toyota.oneapp.ui.payment.service.SelectServiceAdapter.ViewHolder.VehicleSubscriptionsViewHolder
import com.toyota.oneapp.ui.payment.service.SelectServiceItem.VehicleSubscriptions
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters

class SelectServiceAdapter(
    private val listener: ((SelectServiceItem) -> Unit),
) : RecyclerView.Adapter<SelectServiceAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<SelectServiceItem> {
    private val items = mutableListOf<SelectServiceItem>()

    override fun setData(data: List<SelectServiceItem>?) {
        items.clear()
        data?.let { items.addAll(it) }
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int = items.size

    override fun getItemViewType(position: Int): Int =
        when (items[position]) {
            is VehicleSubscriptions -> TYPE_VEHICLE
        }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder =
        LayoutInflater.from(parent.context).let { inflater ->
            when (viewType) {
                TYPE_VEHICLE ->
                    VehicleSubscriptionsViewHolder(
                        ItemSelectServiceVehicleBinding.inflate(
                            inflater,
                            parent,
                            false,
                        ),
                    ).also { viewHolder ->
                        viewHolder.itemView.setOnClickListener {
                            val adapterPosition = viewHolder.adapterPosition
                            if (adapterPosition != NO_POSITION) {
                                listener.invoke(items[adapterPosition])
                            }
                        }
                    }
                else -> throw IllegalArgumentException("Unknown viewType: $viewType")
            }
        }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        items[position].let {
            when (it) {
                is VehicleSubscriptions -> (holder as VehicleSubscriptionsViewHolder).bind(it)
            }
        }
    }

    sealed class ViewHolder(
        itemView: View,
    ) : RecyclerView.ViewHolder(itemView) {
        class VehicleSubscriptionsViewHolder(
            private val binding: ItemSelectServiceVehicleBinding,
        ) : ViewHolder(binding.root) {
            init {
                binding.recyclerView.apply {
                    layoutManager =
                        LinearLayoutManager(
                            binding.recyclerView.context,
                            LinearLayoutManager.HORIZONTAL,
                            false,
                        )
                    adapter = ServiceVehiclesAdapter()
                    addItemDecoration(
                        object : ItemDecoration() {
                            override fun getItemOffsets(
                                outRect: Rect,
                                view: View,
                                parent: RecyclerView,
                                state: RecyclerView.State,
                            ) {
                                val resources = view.context.resources
                                when (parent.getChildAdapterPosition(view)) {
                                    0 -> {
                                        outRect.set(
                                            resources.getDimensionPixelSize(toyotaone.commonlib.R.dimen.margin_32_dp),
                                            0,
                                            resources.getDimensionPixelSize(R.dimen.default_margin_end),
                                            0,
                                        )
                                    }
                                    parent.size - 1 -> {
                                        outRect.set(
                                            resources.getDimensionPixelSize(R.dimen.default_margin_end),
                                            0,
                                            resources.getDimensionPixelSize(toyotaone.commonlib.R.dimen.margin_32_dp),
                                            0,
                                        )
                                    }
                                    else -> {
                                        outRect.set(
                                            resources.getDimensionPixelSize(R.dimen.default_margin_end),
                                            0,
                                            resources.getDimensionPixelSize(R.dimen.default_margin_end),
                                            0,
                                        )
                                    }
                                }
                            }
                        },
                    )
                }
            }

            fun bind(item: VehicleSubscriptions) {
                binding.subtitle.text = itemView.context.getString(R.string.Subscription_payments_for_vehicles, item.subtitle)
                DataBindingAdapters.setRecyclerViewAdapterData(binding.recyclerView, item.vehicles, emptyList())
            }
        }
    }

    companion object {
        private const val TYPE_VEHICLE = 1
    }
}
