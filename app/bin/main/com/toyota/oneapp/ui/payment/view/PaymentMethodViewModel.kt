package com.toyota.oneapp.ui.payment.view

import androidx.annotation.DrawableRes
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.paymentOptions.CreditCardPaymentOption
import com.toyota.oneapp.ui.garage.paymentOptions.PaymentOption
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class PaymentMethodViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val subscriptionAPIManager: SubscriptionAPIManager,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        private var paymentRecord: PaymentRecord = PaymentRecord("1")
        private var paymentRecords: Array<PaymentRecord> = emptyArray()

        init {
            paymentRecord = state["payment_record"] ?: PaymentRecord("1")
            paymentRecords = state["payment_records"] ?: emptyArray()
        }

        private val paymentOption = PaymentOption.create(paymentRecord)
        val isCreditCard = paymentOption is CreditCardPaymentOption

        val accountNumber = paymentOption.accountNumber

        @DrawableRes
        val logoResId = paymentOption.logoResourceId
        val isDefaultPayment = MutableLiveData<Boolean>(paymentOption.isDefaultPayment)

        val expirationMonth = paymentRecord.creditCardExpirationMonth
        val expirationYear = paymentRecord.creditCardExpirationYear

        /**
         * Credit Card - always show
         * ACH - Only show if the card is not the default.
         */
        val isEditable =
            MediatorLiveData<Boolean>().apply {
                value =
                    isCreditCard.also {
                        if (!it) {
                            addSource(isDefaultPayment) { isDefault ->
                                value = !isDefault
                            }
                        }
                    }
            }

        private val editEvent = SingleLiveEvent<PaymentRecord>()
        val onEdit: LiveData<PaymentRecord> = editEvent

        fun edit() {
            analyticsLogger.logEvent(AnalyticsEvent.ZUORA_EDIT_PAYMENT_METHOD)
            editEvent.value = paymentRecord
        }

        private val manageCardEvent = SingleLiveEvent<EditState>()
        val onManageCard = manageCardEvent

        fun chooseEdit() {
            manageCardEvent.value = EditState(isCreditCard, isDefaultPayment.value ?: false)
        }

        private val makeDefaultEvent = SingleLiveEvent<Unit>()
        val onMakeDefault: LiveData<Unit> = makeDefaultEvent

        fun makeDefault() {
            showProgress()
            analyticsLogger.logEvent(AnalyticsEvent.ZUORA_MAKE_DEFAULT_PAYMENT_METHOD)
            subscriptionAPIManager.setDefaultPayment(
                paymentRecord,
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        analyticsLogger.logEvent(
                            AnalyticsEvent.ZUORA_MAKE_DEFAULT_PAYMENT_METHOD_SUCCESS,
                        )
                        isDefaultPayment.value = true
                        makeDefaultEvent.call()
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        analyticsLogger.logEvent(
                            AnalyticsEvent.ZUORA_MAKE_DEFAULT_PAYMENT_METHOD_FAILED,
                        )
                        showErrorMessage(errorMsg)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        private val deleteEvent = SingleLiveEvent<Unit>()
        val onDelete: LiveData<Unit> = deleteEvent

        private val deleteDefaultEvent = SingleLiveEvent<Unit>()
        val onDeleteDefault: LiveData<Unit> = deleteDefaultEvent

        fun delete() {
            if (isDefaultPayment.value == true && paymentRecords.size >= 2) {
                deleteDefaultEvent.call()
                return
            }
            showProgress()
            analyticsLogger.logEvent(AnalyticsEvent.ZUORA_DELETE_PAYMENT_METHOD)
            subscriptionAPIManager.deletePaymentMethod(
                paymentRecord,
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        analyticsLogger.logEvent(AnalyticsEvent.ZUORA_DELETE_PAYMENT_METHOD_SUCCESS)
                        deleteEvent.call()
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        analyticsLogger.logEvent(AnalyticsEvent.ZUORA_DELETE_PAYMENT_METHOD_FAILED)
                        showErrorMessage(errorMsg)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        data class EditState(
            val isCreditCard: Boolean,
            val isDefaultPayment: Boolean,
        )
    }
