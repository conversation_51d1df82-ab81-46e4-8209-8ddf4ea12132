package com.toyota.oneapp.ui.payment.legacypayment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.*
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentUpdateLegacyPaymentBinding
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.garage.subscriptionV2.purchasemultiplesubscriptions.payment.cy17.model.PaymentCY17UIModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class UpdateLegacyPaymentFragment : BaseViewModelFragment() {
    private val viewModel: UpdateLegacyPaymentViewModel by viewModels()

    private lateinit var binding: FragmentUpdateLegacyPaymentBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(viewModel)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View =
        FragmentUpdateLegacyPaymentBinding
            .inflate(
                inflater,
                container,
                false,
            ).also { binding ->
                this.binding = binding
                initializeWebView()
                setUpViewModelBindings()
                binding.lifecycleOwner = viewLifecycleOwner
            }.root

    private fun initializeWebView() {
        binding.webView.apply {
            settings.apply {
                displayZoomControls = false
                javaScriptEnabled = true
                builtInZoomControls = true
                setSupportZoom(true)
                loadWithOverviewMode = true
                useWideViewPort = true
                setGeolocationEnabled(false)
                domStorageEnabled = true
                allowFileAccess = false
                allowContentAccess = true
                loadsImagesAutomatically = true
                javaScriptCanOpenWindowsAutomatically = true
                mediaPlaybackRequiresUserGesture = false
                cacheMode = WebSettings.LOAD_NO_CACHE
//                setAppCacheEnabled(false)
                mixedContentMode = WebSettings.MIXED_CONTENT_NEVER_ALLOW
            }
            setLayerType(View.LAYER_TYPE_SOFTWARE, null)
            scrollBarStyle = WebView.SCROLLBARS_OUTSIDE_OVERLAY
            setInitialScale(1)
            webViewClient = WebViewClient()
            addJavascriptInterface(
                WebAppInterface(viewModel),
                "jsHandler",
            )
            webChromeClient = WebChromeClient()
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.updateLegacyPaymentNavigationEvents.observe(
            viewLifecycleOwner,
            androidx.lifecycle.Observer { navigationEvent ->
                when (navigationEvent) {
                    is UpdateLegacyPaymentNavigationEvent.WebUIModel ->
                        populateWebView(
                            navigationEvent.paymentCY17UIModel,
                        )
                    is UpdateLegacyPaymentNavigationEvent.PaymentAddedSuccessfully -> findNavController().popBackStack()
                    is UpdateLegacyPaymentNavigationEvent.Error -> onError()
                    is UpdateLegacyPaymentNavigationEvent.OnBack -> findNavController().popBackStack()
                }
            },
        )
    }

    private fun populateWebView(uiModel: PaymentCY17UIModel) {
        with(uiModel) {
            val headers = HashMap<String, String>()
            headers["cv-ip-cookie-request"] = "ip"
            headers["cv-cookie-at"] = authorization
            headers["Content-Type"] = "application/json"
            headers["cv-cookie-g"] = guid
            headers["cv-cookie-ot"] = "manage"
            headers["cv-cookie-v"] = vin
            headers["cv-cookie-b"] = brand
            headers["cv-cookie-l"] = language

            binding.webView.loadUrl(url, headers)
        }
    }

    private fun onError() {
        MaterialAlertDialogBuilder(requireContext())
            .apply {
                setTitle(getString(R.string.Login_error))
                setMessage(getString(R.string.generic_error))
                setCancelable(false)
                setPositiveButton(R.string.Common_okay) { _, _ ->
                    findNavController().popBackStack()
                }
            }.show()
    }

    inner class WebAppInterface(
        private val viewModel: UpdateLegacyPaymentViewModel,
    ) {
        @Suppress("unused")
        @JavascriptInterface
        fun postMessage(message: String) {
            viewModel.webEvent(message)
        }
    }
}
