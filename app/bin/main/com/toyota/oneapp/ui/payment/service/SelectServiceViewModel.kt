package com.toyota.oneapp.ui.payment.service

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.payment.service.SelectServiceItem.VehicleSubscriptions
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

@HiltViewModel
class SelectServiceViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        coroutineContext: CoroutineContext,
    ) : BaseViewModel() {
        val subscriptions: LiveData<List<SelectServiceItem>> =
            liveData(coroutineContext) {
                val items = mutableListOf<SelectServiceItem>()
                applicationData.getVehicleList()?.filter { !it.isCY17 && it.generation != null }?.let {
                    if (it.isNotEmpty()) {
                        items += VehicleSubscriptions(it)
                    }
                }

                applicationData
                    .getVehicleList()
                    ?.filter {
                        it.isOnlyCY17 &&
                            it.isToyotaBrand &&
                            it.generation != null &&
                            it.isFeatureEnabled(
                                Feature.PAID_SUBSCRIPTION,
                            )
                    }?.let {
                        if (it.isNotEmpty()) {
                            items += VehicleSubscriptions(it)
                        }
                    }

                applicationData
                    .getVehicleList()
                    ?.filter {
                        it.isOnlyCY17 &&
                            !it.isToyotaBrand &&
                            it.generation != null &&
                            it.isFeatureEnabled(
                                Feature.PAID_SUBSCRIPTION,
                            )
                    }?.let {
                        if (it.isNotEmpty()) {
                            items += VehicleSubscriptions(it)
                        }
                    }
                // TODO What happens if this is list is empty?
                emit(items)
            }

        private val goToPayments = SingleLiveEvent<VehicleInfo>()
        val onPayments: LiveData<VehicleInfo> = goToPayments

        private val updateLegacyPayments = SingleLiveEvent<VehicleInfo>()
        val onUpdateLegacyPayments: LiveData<VehicleInfo> = updateLegacyPayments

        fun selectServiceItem(item: SelectServiceItem) {
            when (item) {
                is VehicleSubscriptions -> {
                    if (item.vehicles.any { it.isCY17 }) {
                        updateLegacyPayments.postValue(item.vehicles.first())
                    } else {
                        analyticsLogger.logEventWithParameter(
                            AnalyticsEventParam.ZUORA_PAYMENT,
                            AnalyticsEventParam.ZUORA_MANAGE_FLOW_GET_PAYMENTS,
                        )
                        goToPayments.postValue(item.vehicles.first())
                    }
                }
            }
        }
    }
