package com.toyota.oneapp.ui.payment

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.NavigationUI
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityPaymentMethodsBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentMethodsActivity : UiBaseActivity() {
    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        DataBindingUtil
            .setContentView<ActivityPaymentMethodsBinding>(
                this,
                R.layout.activity_payment_methods,
            ).let { binding ->
                performActivitySetup(binding.toolbar)
                (supportFragmentManager.findFragmentById(binding.navHost.id) as NavHostFragment).navController.let {
                    NavigationUI.setupWithNavController(
                        binding.toolbar,
                        it,
                        AppBarConfiguration
                            .Builder()
                            .setFallbackOnNavigateUpListener {
                                finish()
                                true
                            }.build(),
                    )
                }
            }
    }
}
