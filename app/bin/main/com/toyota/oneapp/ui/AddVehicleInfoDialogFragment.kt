package com.toyota.oneapp.ui

import android.graphics.Typeface
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentDialogAddVehicleInfoBinding
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AddVehicleInfoDialogFragment(
    listener: AddVehicleInfoDialogDialogListener,
) : BaseBottomSheetDialogFragment() {
    private lateinit var binding: FragmentDialogAddVehicleInfoBinding
    var isToyota: Boolean = false

    private var mBottomSheetListener: AddVehicleInfoDialogDialogListener? = null

    init {
        this.mBottomSheetListener = listener
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentDialogAddVehicleInfoBinding.inflate(inflater, container, false)
        binding.apply {
            lifecycleOwner = viewLifecycleOwner
        }
        isToyota = arguments?.getBoolean(IS_TOYOTA, false) ?: false
        initializeUi()
        return binding.root
    }

    private fun initializeUi() {
        binding.run {
            addVehicleInfoDialogDescription.text =
                String.format(
                    getString(R.string.add_vehicle_info_3g_network_retirement_description),
                    if (isToyota) {
                        getString(R.string.Connected)
                    } else {
                        getString(
                            R.string.Lexus_Enform,
                        )
                    },
                )

            addVehicleInfoDialogMoreInfo.text =
                getString(
                    R.string.add_vehicle_info_3g_network_retirement_more_info,
                )
            val spannableString =
                SpannableString(
                    " " + getString(R.string.add_vehicle_info_3g_network),
                )

            val spannableClickableSpan: ClickableSpan =
                object : ClickableSpan() {
                    override fun onClick(view: View) {
                        ToyUtil.openCustomChromeTab(
                            activityContext,
                            if (isToyota) ToyotaConstants.TOYOTA_3G_TERMS else ToyotaConstants.LEXUS_3G_TERMS,
                        )
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        ds.isUnderlineText = false
                    }
                }
            spannableString.setSpan(
                spannableClickableSpan,
                0,
                spannableString.length,
                Spanned.SPAN_INCLUSIVE_INCLUSIVE,
            )
            spannableString.setSpan(
                StyleSpan(Typeface.BOLD),
                0,
                spannableString.length,
                Spanned.SPAN_INCLUSIVE_INCLUSIVE,
            )
            spannableString.setSpan(
                activity?.getColor(R.color.high_light)?.let { ForegroundColorSpan(it) },
                0,
                spannableString.length,
                Spanned.SPAN_INCLUSIVE_INCLUSIVE,
            )

            addVehicleInfoDialogMoreInfo.append(spannableString)
            addVehicleInfoDialogMoreInfo.movementMethod = LinkMovementMethod.getInstance()

            addVehicleInfoDialogClose.setOnClickListener {
                mBottomSheetListener?.onPRECY17BottomSheetClose()
                <EMAIL>()
            }
        }
    }

    companion object {
        const val TAG = "AddVehicleInfoDialogFragment"
        private const val IS_TOYOTA: String = "IS_TOYOTA"

        fun newInstance(
            isToyota: Boolean,
            listener: AddVehicleInfoDialogDialogListener,
        ): AddVehicleInfoDialogFragment =
            AddVehicleInfoDialogFragment(
                listener,
            ).apply {
                arguments = Bundle().also { it.putBoolean(IS_TOYOTA, isToyota) }
            }
    }

    interface AddVehicleInfoDialogDialogListener {
        fun onPRECY17BottomSheetClose()
    }
}
