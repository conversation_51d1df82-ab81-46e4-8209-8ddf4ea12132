package com.toyota.oneapp.ui

import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.mvvmbase.EXMvvmBaseFragment
import toyotaone.commonlib.permission.PermissionPageUtil
import toyotaone.commonlib.toast.ToastUtil

abstract class BaseMvvmFragment<VM : BaseFragmentViewModel> : EXMvvmBaseFragment<VM>() {
    private var mProgressDialog: Dialog? = null
    abstract val layoutRes: Int

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        R.style.AppTheme
        activity?.let {
            it.window?.statusBarColor = it.getColor(com.toyota.one_ui.R.color.oneUiColorSecondaryBase)
            it.window?.decorView?.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        }
        return inflater.inflate(layoutRes, container, false)
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        mProgressDialog = DialogUtil.createProgressDialog(activity)
    }

    fun observeBaseEvents(viewModel: BaseFragmentViewModel) {
        viewModel.progressLiveData.observe(
            this,
            Observer { model ->
                if (model.isShow) {
                    showProgressDialog()
                } else {
                    dismissProgressDialog()
                }
            },
        )
        viewModel.showToast.observe(
            this,
            Observer { model ->
                ToastUtil.show(activity, model.text, model.iconRes)
            },
        )
        viewModel.showDialog.observe(
            this,
            Observer { mes ->
                showCustomDialog(mes ?: getString(R.string.generic_error))
            },
        )
        viewModel.showUnsupportedDemoDialog.observe(
            this,
            Observer {
                showUnsupportedDemoDialog()
            },
        )
    }

    fun showSuccessToast(message: String?) {
        ToastUtil.show(activity, message, R.drawable.toast_check)
    }

    private fun showProgressDialog() {
        mProgressDialog?.show()
    }

    private fun dismissProgressDialog() {
        mProgressDialog?.dismiss()
    }

    fun showDialog(message: String) {
        showCustomDialog(message)
    }

    private fun showCustomDialog(message: String): AlertDialog =
        if (message.isBlank()) {
            DialogUtil.showDialog(
                activity,
                getString(R.string.Login_error),
                getString(R.string.generic_error),
                getString(R.string.Common_ok),
            )
        } else {
            DialogUtil.showDialog(
                activity,
                getString(R.string.Login_error),
                message,
                getString(R.string.Common_ok),
            )
        }

    fun startActivityWithoutAnim(intent: Intent?) {
        if (intent != null) {
            startActivity(intent)
        }
    }

    fun showUnsupportedDemoDialog() {
        DialogUtil.showDialog(
            activity,
            null,
            getString(R.string.common_not_support_in_demo),
            getString(R.string.Common_ok),
        )
    }

    fun finish() {
        activity?.finish()
    }

    fun showErrorToast(errorMessage: String?) {
        ToastUtil.show(activity, errorMessage, R.drawable.toast_remove)
    }

    fun showForceAllowPermissionDialog(message: String) {
        DialogUtil.showDialog(
            activity,
            null,
            message,
            getString(R.string.Common_ok),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    PermissionPageUtil.intentToAppSettings(activity)
                }

                override fun onCancelClick() {}
            },
            false,
        )
    }

    protected open fun performLogout() {
        (activity as UiBaseActivity).performLogout()
    }
}
