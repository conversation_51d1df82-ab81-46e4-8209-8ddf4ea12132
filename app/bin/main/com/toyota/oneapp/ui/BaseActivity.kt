package com.toyota.oneapp.ui

import android.content.Intent
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.util.ToyUtil
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil

/**
 * The BaseActivity to be used when the MVP pattern is not required (E.g. for an MVVM implementation).
 * Currently this uses a dummy presenter so that the inheritance chain is preserved.
 * Injection happens here and a ViewModel factory is setup for implementers after onCreate happens.
 */
@AndroidEntryPoint
abstract class BaseActivity : MVPBaseActivity<DummyPresenter>() {
    override fun createPresenter(): DummyPresenter = DummyPresenter()

    fun observeBaseEvents(vm: BaseViewModel) {
        vm.navigationEvents.observe(
            this,
            Observer {
                when (it) {
                    is BaseViewModelNavigationEvent.FinishActivity -> {
                        setResult(it.resultCode, it.data)
                        finish()
                    }
                    is BaseViewModelNavigationEvent.HideSoftKeyboard -> hideSoftKeyboard()
                    is BaseViewModelNavigationEvent.ShowProxyDetectedAlert -> showProxyDetectedAlert()
                    else -> {}
                }
            },
        )

        vm.progressEvents.observe(
            this,
            Observer {
                when (it) {
                    is BaseViewModelNavigationEvent.ShowProgress -> showProgressDialog()
                    is BaseViewModelNavigationEvent.HideProgress -> hideProgressDialog()
                    else -> {}
                }
            },
        )

        vm.toastEvents.observe(
            this,
            Observer {
                when (it) {
                    is BaseViewModelNavigationEvent.ShowSuccessToast -> showSuccessToast(it.message)
                    is BaseViewModelNavigationEvent.ShowErrorToast ->
                        showErrorToast(
                            it.errorMessage,
                        )
                    is BaseViewModelNavigationEvent.ShowSuccessToastId ->
                        showSuccessToast(
                            getString(it.messageId),
                        )
                    is BaseViewModelNavigationEvent.ShowErrorToastId ->
                        showErrorToast(
                            getString(it.errorMessageId),
                        )
                    is BaseViewModelNavigationEvent.ShowSuccessToastFormat ->
                        showSuccessToast(
                            getString(it.messageId, *it.args),
                        )
                    is BaseViewModelNavigationEvent.ShowErrorToastFormat ->
                        showErrorToast(
                            getString(it.errorMessageId, *it.args),
                        )
                    is BaseViewModelNavigationEvent.ShowRegularToast ->
                        showRegularToast(
                            getString(it.messageId),
                        )
                    else -> {}
                }
            },
        )

        vm.messageEvents.observe(
            this,
            Observer {
                when (it) {
                    is BaseViewModelNavigationEvent.ShowErrorMessage -> showDialog(it.errorMessage)
                    is BaseViewModelNavigationEvent.ShowErrorMessageAndNavigateBack ->
                        showDialog(
                            it.errorMessage,
                        ) { onBackPressed() }
                    is BaseViewModelNavigationEvent.ShowErrorMessageId ->
                        showDialog(
                            getString(it.errorMessageId),
                        )
                    is BaseViewModelNavigationEvent.ShowErrorMessageIdAndNavigateBack ->
                        showDialog(
                            getString(it.errorMessageId),
                        ) { onBackPressed() }
                    is BaseViewModelNavigationEvent.ShowErrorMessageFormat ->
                        showDialog(
                            getString(it.errorMessageId, *it.args),
                        )
                    is BaseViewModelNavigationEvent.ShowErrorMessageFormatAndNavigateBack ->
                        showDialog(
                            getString(it.errorMessageId, *it.args),
                        ) { onBackPressed() }
                    is BaseViewModelNavigationEvent.ShowUnsupportedInDemo -> showUnsupportedDemoDialog()
                    is BaseViewModelNavigationEvent.OpenWebView ->
                        ToyUtil.openCustomChromeTab(
                            this,
                            it.url,
                        )
                    is BaseViewModelNavigationEvent.ShowMessageDialog ->
                        showMessageDialog(
                            it.message,
                        )
                    else -> {}
                }
            },
        )
    }

    fun showMessageDialog(message: String) {
        DialogUtil.showDialog(
            this,
            null,
            message,
            getString(R.string.ok_label),
        )
    }

    /**
     * Passes onActivityResult to nested child fragments. This becomes more necessary as we use more
     * jetpack navigation.
     *
     * TRY NOT TO USE THIS! It is not necessary most of the time, as you can usually use
     * ActivityResultContract.
     *
     * It is necessary for handling PendingIntents still though.
     */
    override fun onActivityResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
    ) {
        super.onActivityResult(requestCode, resultCode, data)
        supportFragmentManager.fragments.forEach { handleResult(it, requestCode, resultCode, data) }
    }

    private fun handleResult(
        frag: Fragment,
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
    ) {
        frag.onActivityResult(requestCode, resultCode, data)
        frag.childFragmentManager.fragments.forEach {
            handleResult(
                it,
                requestCode,
                resultCode,
                data,
            )
        }
    }
}
