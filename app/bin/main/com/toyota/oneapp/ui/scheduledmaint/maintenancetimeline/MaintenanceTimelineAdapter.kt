package com.toyota.oneapp.ui.scheduledmaint.maintenancetimeline

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemMaintenanceTimelineBinding
import com.toyota.oneapp.model.dealer.ScheduleMaintenanceDetails
import com.toyota.oneapp.util.ToyotaConstants
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.*

class MaintenanceTimelineAdapter(
    private val maintenanceScheduleList: ArrayList<ScheduleMaintenanceDetails>,
    val context: Context,
) : RecyclerView.Adapter<MaintenanceTimelineAdapter.MaintenanceTimeViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ) = MaintenanceTimeViewHolder(
        DataBindingUtil.inflate(
            LayoutInflater.from(parent.context),
            R.layout.item_maintenance_timeline,
            parent,
            false,
        ),
    )

    override fun getItemCount(): Int = maintenanceScheduleList.size

    override fun onBindViewHolder(
        holder: MaintenanceTimeViewHolder,
        position: Int,
    ) {
        val scheduleMaintenance = maintenanceScheduleList[position]
        try {
            val mileageString = getFormattedNumberInK(scheduleMaintenance.intervalMileage.toLong())
            val milegeStringWithComma =
                getFormatedNumberWithComma(
                    scheduleMaintenance.intervalMileage,
                )
            holder.itemMaintenanceTimelineBinding.textmaintenanceSubHeaderList.text =
                context.getString(
                    R.string.maintenance_detail_sub_header_text,
                    mileageString,
                    scheduleMaintenance.mileageUnit,
                    scheduleMaintenance.serviceIntervalTime,
                    scheduleMaintenance.timeUnit,
                )
            holder.itemMaintenanceTimelineBinding.textmaintenanceList.text =
                context.getString(
                    R.string.maintenance_detail_sub_header_list_text,
                    milegeStringWithComma,
                    scheduleMaintenance.mileageUnit,
                )
            holder.itemMaintenanceTimelineBinding.maintenanceTimeLineStatus.text = scheduleMaintenance.interval
            holder.itemMaintenanceTimelineBinding.maintenancetimeline = scheduleMaintenance
        } catch (e: NumberFormatException) {
            e.printStackTrace()
        }
        holder.itemMaintenanceTimelineBinding.root.setOnClickListener {
            val intent = Intent(context, MaintenanceTimelineDetailActivity::class.java)
            intent.putExtra(ToyotaConstants.TIMELINE_LIST, maintenanceScheduleList[position])
            context.startActivity(intent)
        }
    }

    inner class MaintenanceTimeViewHolder(
        val itemMaintenanceTimelineBinding: ItemMaintenanceTimelineBinding,
    ) : RecyclerView.ViewHolder(itemMaintenanceTimelineBinding.root)

    fun getFormatedNumberWithComma(number: String): String? =
        if (!number.isEmpty()) {
            val `val` = number.toDouble()
            NumberFormat.getNumberInstance(Locale.US).format(`val`)
        } else {
            "0"
        }

    fun getFormattedNumberInK(count: Long): String {
        if (count < 1000) return "" + count
        val exp = (Math.log(count.toDouble()) / Math.log(1000.0)).toInt()
        val format = DecimalFormat("0.#")
        val value: String = format.format(count / Math.pow(1000.0, exp.toDouble()))
        return String.format("%s%c", value, "kMBTPE"[exp - 1])
    }
}
