package com.toyota.oneapp.ui.scheduledmaint

import android.content.Context
import androidx.annotation.VisibleForTesting
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.ScheduleMaintenanceManager
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class ScheduleMaintenanceMileageViewModel
    @Inject
    constructor(
        private val context: Context,
        applicationData: ApplicationData,
    ) : BaseViewModel() {
        companion object {
            @VisibleForTesting
            const val MAX_MILEAGE = 1000000
            private const val phoneRegex = "^\\(?([0-9]{3})\\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})\$"
        }

        private var scheduleMaintenanceManager: ScheduleMaintenanceManager =
            ScheduleMaintenanceManager.getInstance()
        val mileage =
            MutableLiveData<String>().apply {
                if (applicationData.mileage[applicationData.getSelectedVehicle()?.vin] != 0) {
                    postValue(applicationData.mileage[applicationData.getSelectedVehicle()?.vin].toString())
                }
            }
        private val errorMileage = MutableLiveData<String>()
        val phoneNumber =
            MutableLiveData<String?>().apply {
                scheduleMaintenanceManager.phoneNumber?.let {
                    value = it
                }
            }
        private val errorPhoneNumber = MutableLiveData<String>()
        private val mileageEditTextMaxLength = MutableLiveData<Int>(MAX_MILEAGE.toString().length)
        var isPhoneNumberRequired = ObservableBoolean(scheduleMaintenanceManager.phoneNumberRequired)
        var mileageError = ObservableBoolean(false)
        var phoneNumberError = ObservableBoolean(false)
        var enableContinueButton = ObservableBoolean(false)
        private val continueEvent = SingleLiveEvent<Unit>()
        val onDetails: LiveData<Unit> = continueEvent

        fun getMileageEditTextMaxLength(): MutableLiveData<Int> = mileageEditTextMaxLength

        fun getErrorMileage(): MutableLiveData<String> = errorMileage

        fun getErrorPhoneNumber(): MutableLiveData<String> = errorPhoneNumber

        fun onContinueToAppointmentClick() {
            scheduleMaintenanceManager.mileage = this.mileage.value?.toInt() ?: 0
            if (isPhoneNumberRequired.get()) {
                scheduleMaintenanceManager.phoneNumber =
                    this.phoneNumber.value
            }
            continueEvent.call()
        }

        fun validateMileage(mileage: String?) {
            mileage?.toIntOrNull().let {
                when {
                    it == null ->
                        setMileageError(
                            context.getString(
                                R.string.Schedule_Maintanence_Mileage_Error__Too_high_error,
                                MAX_MILEAGE,
                            ),
                        )
                    it >= MAX_MILEAGE ->
                        setMileageError(
                            context.getString(
                                R.string.Schedule_Maintanence_Mileage_Error__Too_high_error,
                                MAX_MILEAGE,
                            ),
                        )
                    it <= 0 ->
                        setMileageError(
                            context.getString(
                                R.string.Schedule_Maintanence_Mileage__Less_Than_Zero_Error,
                            ),
                        )
                    else -> {
                        mileageError.set(false)
                        enableContinue()
                    }
                }
            }
        }

        fun validatePhoneNumber(phoneNumber: String?) {
            if (phoneNumber?.matches(Regex(phoneRegex)) == true) {
                phoneNumberError.set(false)
                enableContinue()
            } else {
                phoneNumberError.set(true)
                errorPhoneNumber.postValue(
                    context.getString(R.string.Update_email_phone_enter_valid_phone_number),
                )
                enableContinueButton.set(false)
            }
        }

        private fun setMileageError(errorMessage: String) {
            mileageError.set(true)
            errorMileage.postValue(errorMessage)
            enableContinueButton.set(false)
        }

        private fun enableContinue() {
            enableContinueButton.set(
                if (isPhoneNumberRequired.get()) {
                    !phoneNumberError.get() && (!mileageError.get() && !mileage.value.isNullOrBlank())
                } else {
                    !mileageError.get()
                },
            )
        }
    }
