package com.toyota.oneapp.ui.scheduledmaint

import android.content.Intent
import android.os.Bundle
import android.text.InputFilter
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.databinding.Observable
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityScheduleMaintenanceMileageBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ScheduleMaintenanceMileageActivity : UiBaseActivity() {
    private val viewModel: ScheduleMaintenanceMileageViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        val binding =
            DataBindingUtil
                .setContentView<ActivityScheduleMaintenanceMileageBinding>(
                    this,
                    R.layout.activity_schedule_maintenance_mileage,
                ).also {
                    it.lifecycleOwner = this
                    performActivitySetup(it.toolbar)
                }
        observeBaseEvents(viewModel)
        viewModel.mileage.observe(
            this,
            Observer {
                viewModel.validateMileage(it)
                binding.etMileage.text
            },
        )
        viewModel.phoneNumber.observe(
            this,
            Observer {
                viewModel.validatePhoneNumber(it)
                binding.etPhoneNumber.setText(it)
            },
        )
        viewModel.onDetails.observe(
            this,
            Observer {
                startActivity(Intent(this, ScheduledMaintDetailsActivity::class.java))
            },
        )
        viewModel.mileageError.addOnPropertyChangedCallback(
            object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(
                    sender: Observable?,
                    propertyId: Int,
                ) {
                    binding.tilMileage.isErrorEnabled = viewModel.mileageError.get()
                }
            },
        )
        viewModel.getErrorMileage().observe(this) {
            binding.tilMileage.error = it
        }
        viewModel.getMileageEditTextMaxLength().observe(this) {
            binding.etMileage.filters += InputFilter.LengthFilter(it)
        }
        viewModel.isPhoneNumberRequired.addOnPropertyChangedCallback(
            object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(
                    sender: Observable?,
                    propertyId: Int,
                ) {
                    binding.tilPhoneNumber.isVisible = viewModel.isPhoneNumberRequired.get()
                }
            },
        )
        viewModel.getErrorPhoneNumber().observe(this) {
            binding.tilPhoneNumber.error = it
        }
        viewModel.phoneNumberError.addOnPropertyChangedCallback(
            object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(
                    sender: Observable?,
                    propertyId: Int,
                ) {
                    binding.tilPhoneNumber.isErrorEnabled = viewModel.phoneNumberError.get()
                }
            },
        )
        viewModel.enableContinueButton.addOnPropertyChangedCallback(
            object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(
                    sender: Observable?,
                    propertyId: Int,
                ) {
                    binding.continueToAppointment.isEnabled = viewModel.enableContinueButton.get()
                }
            },
        )
        binding.continueToAppointment.setOnClickListener {
            viewModel.onContinueToAppointmentClick()
        }
    }
}
