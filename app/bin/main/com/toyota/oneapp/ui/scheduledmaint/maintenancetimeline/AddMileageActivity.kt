package com.toyota.oneapp.ui.scheduledmaint.maintenancetimeline

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityAddMileageBinding
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AddMileageActivity : UiBaseActivity() {
    lateinit var vehicle: VehicleInfo

    companion object {
        const val EXTRA_VEHICLE_INFO = "EXTRA_VEHICLE_INFO"

        fun getIntent(
            context: Context,
            vehicle: VehicleInfo,
        ): Intent {
            val intent = Intent(context, AddMileageActivity::class.java)
            intent.putExtra(EXTRA_VEHICLE_INFO, vehicle)
            return intent
        }
    }

    private val viewModel: AddMileageViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        val dataBinding =
            DataBindingUtil.setContentView<ActivityAddMileageBinding>(
                this,
                R.layout.activity_add_mileage,
            )
        dataBinding.viewModel = viewModel
        dataBinding.lifecycleOwner = this
        dataBinding.executePendingBindings()
        performActivitySetup(dataBinding.addMileageToolBar)
        vehicle = intent.getParcelableExtra(EXTRA_VEHICLE_INFO) ?: throw IllegalArgumentException(
            "VehicleInfo cannot be null",
        )

        viewModel.addMileageNavigationEvents.observe(
            this,
            Observer { navigationEvent ->
                when (navigationEvent) {
                    is AddMileageViewModel.NavigationEvent.NavigateToMaintenanceTimelineScreen -> {
                        val intent =
                            MaintenanceTimelineActivity.getIntent(
                                this,
                                vehicle,
                                navigationEvent.odoMeterValue,
                            )
                        startActivity(intent)
                        finish()
                    }
                }
            },
        )
    }
}
