package com.toyota.oneapp.ui.scheduledmaint.maintenancetimeline

import android.content.Context
import androidx.databinding.ObservableField
import com.toyota.oneapp.R
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class AddMileageViewModel
    @Inject
    constructor(
        private val context: Context,
    ) : BaseViewModel() {
        var odometerValue = ObservableField<String>()
        val odoMeterError = ObservableField<String>()

        sealed class NavigationEvent {
            data class NavigateToMaintenanceTimelineScreen(
                val odoMeterValue: String?,
            ) : NavigationEvent()
        }

        val addMileageNavigationEvents = SingleLiveEvent<NavigationEvent>()

        fun onAddMileageClick() {
            val enteredOdoMeterValue = odometerValue.get()
            var enterednum = 0
            if (enteredOdoMeterValue != null) {
                enterednum = enteredOdoMeterValue.toIntOrNull() ?: 0
            }
            if (enteredOdoMeterValue.isNullOrEmpty() || enterednum == 0) {
                odoMeterError.set(context.resources.getString(R.string.valid_odometer_txt))
                return
            }
            addMileageNavigationEvents.postValue(
                NavigationEvent.NavigateToMaintenanceTimelineScreen(enteredOdoMeterValue),
            )
        }
    }
