package com.toyota.oneapp.ui.scheduledmaint.maintenancetimeline

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Html
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityMaintenanceTimelineBinding
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.log.LogTool
import java.text.NumberFormat
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class MaintenanceTimelineActivity : UiBaseActivity() {
    companion object {
        const val EXTRA_VEHICLE_INFO = "EXTRA_VEHICLE_INFO"
        const val TAG = "MaintenanceTimelineActivity"

        fun getIntent(
            context: Context,
            vehicle: VehicleInfo,
            odometerValue: String? = null,
        ): Intent {
            val intent = Intent(context, MaintenanceTimelineActivity::class.java)
            intent.putExtra(EXTRA_VEHICLE_INFO, vehicle)
            if (odometerValue != null) {
                intent.putExtra(ToyotaConstants.ODOMETER_VALUE, odometerValue)
                intent.putExtra(ToyotaConstants.ODOMETER_VALUE_AVAILABLE, true)
            }
            return intent
        }
    }

    @Inject
    lateinit var applicationData: ApplicationData
    lateinit var vehicle: VehicleInfo
    private var mileage: String = ""
    private lateinit var lastKnownMileage: String
    private lateinit var dataBinding: ActivityMaintenanceTimelineBinding
    private val viewModel: MaintenanceTimelineViewModel by viewModels()
    private var customDialog: AlertDialog? = null

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        dataBinding =
            DataBindingUtil.setContentView<ActivityMaintenanceTimelineBinding>(
                this,
                R.layout.activity_maintenance_timeline,
            )
        dataBinding.viewModel = viewModel
        dataBinding.lifecycleOwner = this
        dataBinding.executePendingBindings()
        performActivitySetup(dataBinding.timelineToolBar)
        observeBaseEvents(viewModel)
        vehicle = intent.getParcelableExtra(EXTRA_VEHICLE_INFO) ?: throw IllegalArgumentException(
            "VehicleInfo cannot be null",
        )
        val isAddButtonClicked =
            intent.getBooleanExtra(
                ToyotaConstants.ODOMETER_VALUE_AVAILABLE,
                false,
            )
        mileage =
            if (applicationData.mileage[vehicle.vin] != 0 && !isAddButtonClicked) {
                applicationData.mileage[vehicle.vin].toString()
            } else {
                intent.getStringExtra(ToyotaConstants.ODOMETER_VALUE)
                    ?: throw IllegalArgumentException("OdometerValue is mandatory")
            }

        if (mileage.isNotEmpty()) {
            lastKnownMileage = getFormattedNumberWithComma(mileage)
            dataBinding.lastKnownMielageTxt.text =
                getString(
                    R.string.last_known_mileage,
                    lastKnownMileage,
                )
            viewModel.getMaintenanceTimeLineSchedule(vehicle.vin, mileage)
        } else {
            lastKnownMileage =
                getFormattedNumberWithComma(
                    intent.getStringExtra(ToyotaConstants.ODOMETER_VALUE),
                )
            dataBinding.lastKnownMielageTxt.text =
                getString(
                    R.string.last_known_mileage,
                    lastKnownMileage,
                )
            try {
                viewModel.getMaintenanceTimeLineSchedule(
                    vehicle.vin,
                    intent.getStringExtra(ToyotaConstants.ODOMETER_VALUE)
                        ?: throw IllegalArgumentException("OdometerValue is mandatory"),
                )
            } catch (e: IllegalArgumentException) {
                LogTool.e(TAG, e.message)
            }
        }

        viewModel.maintenanceScheduleList.observe(
            this,
            Observer { timelineList ->
                dataBinding.rvTimelineList.also {
                    val linearLayoutManager = LinearLayoutManager(applicationContext)
                    it.layoutManager = linearLayoutManager
                    it.setHasFixedSize(true)
                    it.adapter = MaintenanceTimelineAdapter(timelineList, this)
                }
            },
        )

        viewModel.maintenanceListObserver.observe(
            this,
            Observer { isTimelineListEmpty ->
                if (isTimelineListEmpty) {
                    dataBinding.noMaintenanceLayout.visibility = View.VISIBLE
                    if (vehicle.isToyotaBrand) {
                        dataBinding.textNoMaintenanceSchedule.text =
                            getString(
                                R.string.maintence_timeline_no_factory_text,
                            )
                        val sourceString =
                            "<b>" + "Note:" + "</b> " +
                                getString(
                                    R.string.toyota_note_text,
                                )
                        dataBinding.noMaintienanceTimelineNoteText.text =
                            Html.fromHtml(
                                sourceString,
                                Html.FROM_HTML_MODE_COMPACT,
                            )
                    } else {
                        dataBinding.textNoMaintenanceSchedule.text =
                            getString(
                                R.string.maintence_timeline_no_factory_text,
                            )
                        dataBinding.noMaintienanceTimelineNoteText.text =
                            getString(
                                R.string.maintenanc_lexus_note_text,
                            )
                    }
                } else {
                    dataBinding.rvTimelineList.visibility = View.VISIBLE
                }
            },
        )

        viewModel.maintenanceListerrorResponseObserver.observe(
            this,
            Observer { isError ->
                if (isError) {
                    customDialog =
                        DialogUtil.showDialog(
                            this,
                            getString(R.string.maintenance_error_title),
                            getString(R.string.maintenance_error_msg),
                            "",
                            getString(R.string.Common_ok),
                            object : OnCusDialogInterface {
                                override fun onCancelClick() {
                                    finish()
                                }

                                override fun onConfirmClick() {
                                }
                            },
                            false,
                        )
                }
            },
        )

        viewModel.scheduleMaintenanceTimelineNavigationEvent.observe(
            this,
            Observer { isClicked ->
                if (isClicked) {
                    startActivity(AddMileageActivity.getIntent(applicationContext, vehicle))
                    finish()
                }
            },
        )
    }

    private fun getFormattedNumberWithComma(number: String?): String =
        if (!number.isNullOrBlank()) {
            val `val` = number.toDoubleOrNull() ?: 0.0
            NumberFormat.getNumberInstance(Locale.US).format(`val`)
        } else {
            "0"
        }
}
