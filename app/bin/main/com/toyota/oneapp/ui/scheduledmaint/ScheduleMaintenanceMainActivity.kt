package com.toyota.oneapp.ui.scheduledmaint

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.activity.viewModels
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityScheduleMaintenanceMainBinding
import com.toyota.oneapp.model.dealer.Dealer
import com.toyota.oneapp.ui.AddVehicleInfoActivity
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import com.toyota.oneapp.ui.dealerInteractions.DealerWrapper
import com.toyota.oneapp.ui.dealerInteractions.SelectPreferredDealerActivity
import com.toyota.oneapp.ui.scheduledmaint.ScheduleMaintenanceMainViewModel.ScheduleMaintenanceNavigationEvent.*
import com.toyota.oneapp.util.ToyUtil
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.map.GoogleMapManager
import toyotaone.commonlib.permission.PermissionUtil

@AndroidEntryPoint
class ScheduleMaintenanceMainActivity :
    DataBindingBaseActivity<ActivityScheduleMaintenanceMainBinding>(),
    OnMapReadyCallback {
    private val mViewModel: ScheduleMaintenanceMainViewModel by viewModels()
    private lateinit var googleMap: GoogleMap
    private var mGoogleMapManger: GoogleMapManager<DealerWrapper>? = null
    private val preferredDealerContract =
        registerForActivityResult<Intent, ActivityResult>(StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == RESULT_OK) {
                val preferredDealer =
                    result.data?.getParcelableExtra<Dealer>(
                        AddVehicleInfoActivity.KEY_PREFERRED_DEALER,
                    )
                mViewModel.setPreferredDealer(preferredDealer)
            }
        }

    companion object {
        private const val TAG = "ScheduleMaintenanceMainActivity"
    }

    override fun initViews(savedInstance: Bundle?) {
        checkLocationPermission()
        performActivitySetup(binding.toolbar)
        initMap()
        observeBaseEvents(mViewModel)
        observeNavigationEvents(mViewModel)
        observeLiveData(mViewModel)
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        super.onCreateOptionsMenu(menu)
        menuInflater.inflate(R.menu.menu_toolbar_call_search, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            R.id.action_call -> {
                mViewModel.invokeCallNumberNavigationEvent()
                true
            }

            R.id.action_search -> {
                goToSearchForLocationIfNoDemo()
                true
            }

            else -> super.onOptionsItemSelected(item)
        }

    override fun onMapReady(googleMap: GoogleMap) {
        this.googleMap = googleMap
        mGoogleMapManger = GoogleMapManager(this, googleMap, mUsesNightMode = false)
    }

    override fun getLayoutId() = R.layout.activity_schedule_maintenance_main

    private fun initMap() {
        val mapFragment = supportFragmentManager.findFragmentById(R.id.map) as SupportMapFragment
        mapFragment.getMapAsync(this)
    }

    private fun observeNavigationEvents(viewModel: ScheduleMaintenanceMainViewModel) {
        viewModel.onMainNavigationEvent.observe(
            this,
            Observer {
                when (it) {
                    is GoToWebsite -> ToyUtil.openCustomChromeTab(this, it.website)

                    is CallNumber -> showCallNumberDialog(it.number)

                    is SearchForLocation -> goToSearchForLocationIfNoDemo()

                    is ContinueToScheduling -> launchScheduleMaintenanceSecondaryActivity()
                }
            },
        )
    }

    private fun observeLiveData(viewModel: ScheduleMaintenanceMainViewModel) {
        viewModel.apply {
            preferredDealer.observe(
                this@ScheduleMaintenanceMainActivity,
                Observer {
                    mGoogleMapManger?.let {
                        setMapManager(it)
                    }
                    binding.tvDealerName.text = it?.dealershipName
                },
            )

            isDealerDown.observe(
                this@ScheduleMaintenanceMainActivity,
                Observer { down ->
                    binding.apply {
                        try {
                            toolbar.menu.findItem(R.id.action_call).isVisible = !down
                            toolbar.menu.findItem(R.id.action_search).isVisible = down
                        } catch (e: IllegalStateException) {
                            LogTool.e(
                                TAG,
                                "Toolbar menu item not found and will not display properly",
                            )
                        }
                    }
                    binding.downtimeGroup.isVisible = down
                    binding.nonDowntimeGroup.isGone = down
                },
            )

            isCardViewVisible.observe(this@ScheduleMaintenanceMainActivity) {
                binding.tvDesc.isVisible = it
                binding.etSearchBox.isGone = it
                binding.cardView.isVisible = it
                binding.btnContinue.isEnabled = it
            }

            isNameVisible.observe(this@ScheduleMaintenanceMainActivity) {
                binding.tvDealerName.isVisible = it
            }
            isContinueButtonVisible.observe(this@ScheduleMaintenanceMainActivity) {
                binding.btnContinue.isVisible = it
            }
            isAddressVisible.observe(this@ScheduleMaintenanceMainActivity) {
                binding.tvDealerAddress.isVisible = it
                dealerAddress.observe(this@ScheduleMaintenanceMainActivity) {
                    binding.tvDealerAddress.text = it
                }
                dealerPhone.observe(this@ScheduleMaintenanceMainActivity) {
                    binding.tvDealerPhone.text = it
                }
            }
            isWebsiteVisible.observe(this@ScheduleMaintenanceMainActivity) {
                binding.tvDealerWebsite.isVisible = it
            }
            binding.etSearchBox.setOnClickListener {
                mViewModel.onChangeLocationClick()
            }
            binding.tvDealerPhone.setOnClickListener {
                mViewModel.onPhoneClick()
            }
            binding.tvDealerWebsite.setOnClickListener {
                mViewModel.onWebsiteClick()
            }
            binding.tvChangeLocation.setOnClickListener {
                mViewModel.onChangeLocationClick()
            }
            binding.btnContinue.setOnClickListener {
                mViewModel.onContinueButtonClick()
            }
            mViewModel.canShowAppointmentDetails.observe(this@ScheduleMaintenanceMainActivity) {
                val text =
                    if (it) {
                        R.string.Appointment_continue_to_appointment_details
                    } else {
                        R.string.Common_call
                    }
                binding.btnContinue.setText(text)
            }
        }
    }

    private fun checkLocationPermission() {
        PermissionUtil.checkLocationPermissions(this) { permission ->
            if (!permission.granted && !permission.shouldShowRequestPermissionRationale) {
                showForceAllowPermissionDialog()
            }
        }
    }

    private fun showCallNumberDialog(number: String?) {
        DialogUtil.showDialog(
            this,
            "",
            getString(R.string.Appointment_schedule_maintenance_call_dealer),
            getString(R.string.Common_call),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    ToyUtil.phoneCall(this@ScheduleMaintenanceMainActivity, number)
                }

                override fun onCancelClick() {
                    // Unused
                }
            },
            false,
        )
    }

    private fun goToSearchForLocationIfNoDemo() {
        launchPreferredDealerActivityForResult()
    }

    private fun launchPreferredDealerActivityForResult() {
        val intent = Intent(this, SelectPreferredDealerActivity::class.java)
        preferredDealerContract.launch(intent)
    }

    private fun launchScheduleMaintenanceSecondaryActivity() {
        startActivity(Intent(this, ScheduleMaintenanceMileageActivity::class.java))
    }
}
