package com.toyota.oneapp.ui.scheduledmaint.maintenancetimeline

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemMaintenaneTimelineDetailsBinding
import com.toyota.oneapp.model.dealer.MaintenanceTasks

class MaintenanceTimelineDetailsAdapter(
    private val maintenanceScheduleDetailsList: ArrayList<MaintenanceTasks>,
    var context: Context,
) : RecyclerView.Adapter<MaintenanceTimelineDetailsAdapter.MaintenanceTimeViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ) = MaintenanceTimeViewHolder(
        DataBindingUtil.inflate(
            LayoutInflater.from(parent.context),
            R.layout.item_maintenane_timeline_details,
            parent,
            false,
        ),
    )

    override fun getItemCount(): Int = maintenanceScheduleDetailsList.size

    override fun onBindViewHolder(
        holder: MaintenanceTimeViewHolder,
        position: Int,
    ) {
        val scheduleMaintenanceDetails = maintenanceScheduleDetailsList[position]
        holder.itemMaintenanceTimelineDetailsBinding.maintenancetimelinedetails = scheduleMaintenanceDetails
    }

    inner class MaintenanceTimeViewHolder(
        val itemMaintenanceTimelineDetailsBinding: ItemMaintenaneTimelineDetailsBinding,
    ) : RecyclerView.ViewHolder(itemMaintenanceTimelineDetailsBinding.root)
}
