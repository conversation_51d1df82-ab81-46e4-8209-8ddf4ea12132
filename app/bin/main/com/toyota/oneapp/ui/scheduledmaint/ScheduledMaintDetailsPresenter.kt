package com.toyota.oneapp.ui.scheduledmaint

import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.ScheduleMaintenanceManager
import com.toyota.oneapp.model.Advisor
import com.toyota.oneapp.model.AdvisorsResponse
import com.toyota.oneapp.model.DealerScheduleSlotsResponse
import com.toyota.oneapp.model.RepairServicesPayload
import com.toyota.oneapp.model.RepairServicesResponse
import com.toyota.oneapp.model.ServiceRepair
import com.toyota.oneapp.model.ServiceTime
import com.toyota.oneapp.model.Transport
import com.toyota.oneapp.model.TransportOptionsResponse
import com.toyota.oneapp.network.api.manager.DealerScheduleAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BasePresenter
import java.util.Calendar
import javax.inject.Inject

class ScheduledMaintDetailsPresenter
    @Inject
    internal constructor(
        private val dealerScheduleManager: DealerScheduleAPIManager,
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
    ) : BasePresenter<ScheduledMaintDetailsPresenter.View?>() {
        var repairServicesPayload: RepairServicesPayload? = null
        var advisorsList: List<Advisor>? = null
        var transportationList: List<Transport>? = null

        fun getRepairsList() {
            val dealerId = ScheduleMaintenanceManager.getInstance().dealerId
            val mileage = ScheduleMaintenanceManager.getInstance().mileage
            val vin = applicationData.getSelectedVehicle()?.vin
            if (null != dealerId) {
                view?.showProgressDialog()
                dealerScheduleManager.sendGetRepairServiceRequest(
                    vin,
                    dealerId,
                    mileage,
                    object : BaseCallback<RepairServicesResponse?>() {
                        override fun onSuccess(response: RepairServicesResponse?) {
                            if (response?.payload != null) {
                                analyticsLogger.logEvent(
                                    AnalyticsEvent.DAS_SERVICE_REPAIRS_SUCCESSFUL,
                                )
                                repairServicesPayload = response.payload

                                val serviceRepairs = ArrayList<ServiceRepair>()
                                when {
                                    response.payload.grs.isNotEmpty() ->
                                        serviceRepairs.addAll(
                                            response.payload.grs,
                                        )
                                    response.payload.drs.isNotEmpty() ->
                                        serviceRepairs.addAll(
                                            response.payload.drs,
                                        )
                                    response.payload.frs.isNotEmpty() ->
                                        serviceRepairs.addAll(
                                            response.payload.frs,
                                        )
                                }
                                view?.showRepairs(
                                    response.payload.drsPricingAvailable,
                                    serviceRepairs,
                                )
                            }
                        }

                        override fun onFailError(
                            code: Int,
                            errorMsg: String?,
                        ) {
                            analyticsLogger.logEvent(
                                AnalyticsEvent.DAS_SERVICE_REPAIRS_UNSUCCESSFUL,
                            )
                            view?.showDialog(errorMsg)
                        }

                        override fun onComplete() {
                            view?.hideProgressDialog()
                        }
                    },
                )
            } else {
                view?.showMessage()
            }
        }

        fun getCalendarAvailableTime(
            selectedDate: Calendar,
            serviceIds: String,
            advisorId: String?,
            transportationType: String?,
        ) {
            val endCalendar = Calendar.getInstance()
            endCalendar.time = selectedDate.time
            endCalendar.add(Calendar.DAY_OF_MONTH, 5)
            getAvailableSlots(
                selectedDate,
                endCalendar,
                ScheduleMaintenanceManager.getInstance().dealerId,
                ScheduleMaintenanceManager.getInstance().mileage,
                serviceIds,
                advisorId,
                transportationType,
            )
        }

        private fun getAvailableSlots(
            startDate: Calendar,
            endDate: Calendar,
            dealerId: String,
            mileage: Int,
            serviceIds: String,
            advisorId: String?,
            transportationType: String?,
        ) {
            applicationData.getSelectedVehicle()?.let {
                view?.showProgressDialog()
                dealerScheduleManager.sendGetDealerAvailableTimeRequest(
                    it.vin,
                    dealerId,
                    mileage,
                    serviceIds,
                    startDate,
                    endDate,
                    advisorId,
                    transportationType,
                    object : BaseCallback<DealerScheduleSlotsResponse?>() {
                        override fun onSuccess(response: DealerScheduleSlotsResponse?) {
                            if (null != response) {
                                analyticsLogger.logEvent(AnalyticsEvent.DAS_AVAILABLE_SLOTS_SUCCESSFUL)
                                view?.showAvailableSlots(response.getDealerScheduleSlotsFromResponse())
                            } else {
                                view?.showNoAvailableSlots()
                            }
                        }

                        override fun onFailError(
                            httpCode: Int,
                            errorMsg: String?,
                        ) {
                            analyticsLogger.logEvent(AnalyticsEvent.DAS_AVAILABLE_SLOTS_UNSUCCESSFUL)
                            view?.showNoAvailableSlots()
                        }

                        override fun onComplete() {
                            view?.hideProgressDialog()
                        }
                    },
                )
            }
        }

        fun addRepairs(serviceRepair: ServiceRepair) {
            val instance = ScheduleMaintenanceManager.getInstance().mservicesRepairsList
            when {
                !repairServicesPayload!!.drs.filter { it.serviceId == serviceRepair.serviceId }.isNullOrEmpty() -> {
                    if (serviceRepair.selected) {
                        instance.drs.add(serviceRepair)
                    } else {
                        instance.drs.remove(serviceRepair)
                    }
                }
                !repairServicesPayload!!.frs.filter { it.serviceId == serviceRepair.serviceId }.isNullOrEmpty() -> {
                    if (serviceRepair.selected) {
                        instance.frs.add(serviceRepair)
                    } else {
                        instance.frs.remove(serviceRepair)
                    }
                }
                !repairServicesPayload!!.grs.filter { it.serviceId == serviceRepair.serviceId }.isNullOrEmpty() -> {
                    if (serviceRepair.selected) {
                        instance.grs.add(serviceRepair)
                    } else {
                        instance.grs.remove(serviceRepair)
                    }
                }
            }
        }

        fun getAdvisors(serviceIds: String) {
            applicationData.getSelectedVehicle()?.let {
                val instance = ScheduleMaintenanceManager.getInstance()
                view?.showProgressDialog()
                dealerScheduleManager.sendGetAdvisorsRequest(
                    it.vin,
                    serviceIds,
                    instance.dealerId,
                    instance.mileage,
                    object : BaseCallback<AdvisorsResponse?>() {
                        override fun onComplete() {
                            view?.hideProgressDialog()
                        }

                        override fun onSuccess(response: AdvisorsResponse?) {
                            analyticsLogger.logEvent(AnalyticsEvent.DAS_GET_ADVISOR_SUCCESSFUL)
                            if (response != null && response.payload != null && response.payload.isNotEmpty()) {
                                advisorsList = response.payload
                                view?.showSelectAdvisor(true)
                            } else {
                                view?.showSelectAdvisor(false)
                            }
                        }

                        override fun onFailError(
                            code: Int,
                            errorMsg: String?,
                        ) {
                            analyticsLogger.logEvent(AnalyticsEvent.DAS_GET_ADVISOR_UNSUCCESSFUL)
                            view?.showSelectAdvisor(false)
                        }
                    },
                )
            }
        }

        fun getTransportation(serviceIds: String) {
            applicationData.getSelectedVehicle()?.let {
                val instance = ScheduleMaintenanceManager.getInstance()
                view?.showProgressDialog()
                dealerScheduleManager.sendGetTransportRequest(
                    it.vin,
                    serviceIds,
                    instance.dealerId,
                    instance.mileage,
                    object : BaseCallback<TransportOptionsResponse?>() {
                        override fun onComplete() {
                            view?.hideProgressDialog()
                        }

                        override fun onSuccess(response: TransportOptionsResponse?) {
                            analyticsLogger.logEvent(AnalyticsEvent.DAS_TRANSPORTATIONS_SUCCESSFUL)
                            if (response != null && response.payload != null && response.payload.isNotEmpty()) {
                                transportationList = response.payload
                                view?.showSelectTransportation(true)
                            } else {
                                view?.showSelectTransportation(false)
                            }
                        }

                        override fun onFailError(
                            code: Int,
                            errorMsg: String?,
                        ) {
                            analyticsLogger.logEvent(AnalyticsEvent.DAS_TRANSPORTATIONS_UNSUCCESSFUL)
                            view?.showSelectTransportation(false)
                        }
                    },
                )
            }
        }

        interface View : BaseView {
            fun showMessage()

            fun showRepairs(
                dealerPricingAvailable: Boolean,
                serviceRepairList: ArrayList<ServiceRepair>,
            )

            fun showAvailableSlots(serviceTimeList: ArrayList<ServiceTime>)

            fun showNoAvailableSlots()

            fun showSelectAdvisor(show: Boolean)

            fun showSelectTransportation(show: Boolean)
        }
    }
