package com.toyota.oneapp.ui.scheduledmaint

import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.ServiceRepair
import com.toyota.oneapp.network.api.manager.DealerScheduleAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.ui.BasePresenter
import javax.inject.Inject

class ScheduledAppointmentConfirmationPresenter
    @Inject
    internal constructor(
        private val dealerScheduleManager: DealerScheduleAPIManager,
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
    ) : BasePresenter<ScheduledAppointmentConfirmationPresenter.View?>() {
        fun cancelAppointment(
            dealerId: String?,
            appointmentID: String?,
            services: List<ServiceRepair>,
        ) {
            view?.showProgressDialog()
            applicationData.getSelectedVehicle()?.let { vehicleInfo ->
                dealerScheduleManager.sendCancelAppointmentRequest(
                    vehicleInfo.vin,
                    services.joinToString { it.serviceId },
                    vehicleInfo.generation,
                    dealerId,
                    appointmentID,
                    object : BaseCallback<BaseResponse?>() {
                        override fun onSuccess(response: BaseResponse?) {
                            analyticsLogger.logEvent(AnalyticsEvent.DAS_CANCEL_APPOINTMENT_SUCCESSFUL)
                            view?.cancelAppointmentSuccessful()
                        }

                        override fun onFailError(
                            code: Int,
                            errorMsg: String?,
                        ) {
                            analyticsLogger.logEvent(AnalyticsEvent.DAS_CANCEL_APPOINTMENT_UNSUCCESSFUL)
                            view?.showDialog(errorMsg)
                        }

                        override fun onComplete() {
                            view?.hideProgressDialog()
                        }
                    },
                )
            }
        }

        interface View : BaseView {
            fun cancelAppointmentSuccessful()

            override fun showProgressDialog()

            override fun hideProgressDialog()
        }
    }
