package com.toyota.oneapp.ui.scheduledmaint

import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.ScheduleMaintenanceManager
import com.toyota.oneapp.model.DealerScheduleSlotsResponse
import com.toyota.oneapp.model.ServiceRepair
import com.toyota.oneapp.model.ServiceTime
import com.toyota.oneapp.network.api.manager.DealerScheduleAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BasePresenter
import java.util.*
import javax.inject.Inject
import kotlin.collections.ArrayList

class ScheduleCalendarPresenter
    @Inject
    internal constructor(
        private val dealerScheduleManager: DealerScheduleAPIManager,
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
    ) : BasePresenter<ScheduleCalendarPresenter.View?>() {
        fun getAvailableSlots(selectedDate: Calendar) {
            view?.showProgressDialog()
            val endCalendar = Calendar.getInstance()
            endCalendar.time = selectedDate.time
            endCalendar.add(Calendar.DAY_OF_MONTH, 5)
            val scheduleMaintenanceManager = ScheduleMaintenanceManager.getInstance()
            val serviceRepairsList: ArrayList<ServiceRepair> = ArrayList()
            scheduleMaintenanceManager.mservicesRepairsList.apply {
                serviceRepairsList.addAll(this.drs)
                serviceRepairsList.addAll(this.frs)
                serviceRepairsList.addAll(this.grs)
            }

            val advisorId =
                if (scheduleMaintenanceManager.advisor != null &&
                    scheduleMaintenanceManager.advisor.advisorId != null
                ) {
                    scheduleMaintenanceManager.advisor.advisorId
                } else {
                    null
                }
            val transportationType =
                if (scheduleMaintenanceManager.transportOption != null &&
                    scheduleMaintenanceManager.transportOption.description != null
                ) {
                    scheduleMaintenanceManager.transportOption.description
                } else {
                    null
                }
            getAvailableSlots(
                selectedDate,
                endCalendar,
                scheduleMaintenanceManager.dealerId,
                scheduleMaintenanceManager.mileage,
                serviceRepairsList.joinToString { it.serviceId },
                advisorId,
                transportationType,
            )
        }

        private fun getAvailableSlots(
            startDate: Calendar,
            endDate: Calendar,
            dealerId: String,
            mileage: Int,
            serviceIds: String,
            advisorId: String?,
            transportationType: String?,
        ) {
            applicationData.getSelectedVehicle()?.let {
                dealerScheduleManager.sendGetDealerAvailableTimeRequest(
                    it.vin,
                    dealerId,
                    mileage,
                    serviceIds,
                    startDate,
                    endDate,
                    advisorId,
                    transportationType,
                    object : BaseCallback<DealerScheduleSlotsResponse?>() {
                        override fun onSuccess(response: DealerScheduleSlotsResponse?) {
                            if (null != response) {
                                analyticsLogger.logEvent(AnalyticsEvent.DAS_AVAILABLE_SLOTS_SUCCESSFUL)
                                view?.updateAvailableSlots(
                                    response.getDealerScheduleSlotsFromResponse(),
                                )
                            } else {
                                view?.showNoAvailableSlots()
                            }
                        }

                        override fun onFailError(
                            httpCode: Int,
                            errorMsg: String?,
                        ) {
                            analyticsLogger.logEvent(AnalyticsEvent.DAS_AVAILABLE_SLOTS_UNSUCCESSFUL)
                            view?.showNoAvailableSlots()
                        }

                        override fun onComplete() {
                            view?.hideProgressDialog()
                        }
                    },
                )
            }
        }

        interface View : BaseView {
            fun showNoAvailableSlots()

            fun updateAvailableSlots(serviceTimes: ArrayList<ServiceTime>?)
        }
    }
