package com.toyota.oneapp.ui.scheduledmaint.maintenancetimeline

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityMaintenanceTimelineDetailBinding
import com.toyota.oneapp.model.dealer.ScheduleMaintenanceDetails
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.scheduledmaint.ScheduleMaintenanceMainActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import java.text.DecimalFormat

@AndroidEntryPoint
class MaintenanceTimelineDetailActivity : UiBaseActivity() {
    private lateinit var binding: ActivityMaintenanceTimelineDetailBinding
    lateinit var scheduleMaintenanceDetails: ScheduleMaintenanceDetails

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding =
            DataBindingUtil.setContentView(
                this,
                R.layout.activity_maintenance_timeline_detail,
            )
        performActivitySetup(binding.timelineDetailsToolBar)
        scheduleMaintenanceDetails = intent.extras?.getParcelable(ToyotaConstants.TIMELINE_LIST)!!
        initList()
        initView(scheduleMaintenanceDetails)
    }

    private fun initList() {
        val linearLayoutManager = LinearLayoutManager(applicationContext)
        binding.rvDetailList.layoutManager = linearLayoutManager
        binding.rvDetailList.setHasFixedSize(true)
        binding.rvDetailList.adapter =
            MaintenanceTimelineDetailsAdapter(
                scheduleMaintenanceDetails.maintenanceTasks,
                this,
            )
    }

    private fun initView(scheduleMaintenanceDetails: ScheduleMaintenanceDetails) {
        val mileageString =
            getFormattedNumberInK(
                scheduleMaintenanceDetails.intervalMileage.toLong(),
            )
        supportActionBar?.title = getString(R.string.maintenance_detail_header_text, mileageString)
        binding.textHeader.text =
            getString(
                R.string.maintenance_detail_sub_header_text,
                mileageString,
                scheduleMaintenanceDetails.mileageUnit,
                scheduleMaintenanceDetails.serviceIntervalTime,
                scheduleMaintenanceDetails.timeUnit,
            )
        if (scheduleMaintenanceDetails.interval.equals("CURRENT")) {
            binding.scheduleMaintButton.visibility = View.VISIBLE
        }
        binding.scheduleMaintButton.setOnClickListener {
            startActivity(
                Intent(this, ScheduleMaintenanceMainActivity::class.java),
            )
        }
    }

    fun getFormattedNumberInK(count: Long): String {
        if (count < 1000) return "" + count
        val exp = (Math.log(count.toDouble()) / Math.log(1000.0)).toInt()
        val format = DecimalFormat("0.#")
        val value: String = format.format(count / Math.pow(1000.0, exp.toDouble()))
        return String.format("%s%c", value, "kMBTPE"[exp - 1])
    }
}
