package com.toyota.oneapp.ui.scheduledmaint

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.ScheduleMaintenanceManager
import com.toyota.oneapp.model.dealer.Dealer
import com.toyota.oneapp.model.dealer.DealerId
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.ScheduleMaintenanceRepository
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.dealerInteractions.DealerWrapper
import com.toyota.oneapp.util.ToyUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import toyotaone.commonlib.map.GoogleMapManager
import javax.inject.Inject

@HiltViewModel
class ScheduleMaintenanceMainViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val repository: ScheduleMaintenanceRepository,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        init {
            sendPreferredDealerRequest()
        }

        /**
         * Object holding all preferred / selected dealer information
         * */
        private val _preferredDealer = MutableLiveData<Dealer?>()
        val preferredDealer: LiveData<Dealer?> = _preferredDealer

        var _dealerAddress = MutableLiveData<String?>()
        val dealerAddress: LiveData<String?> = _dealerAddress

        var _dealerPhone = MutableLiveData<String?>()
        val dealerPhone: LiveData<String?> = _dealerPhone

        /**
         * Boolean that will determine if the we should show the downtime UI, or if the user can
         * continue with booking an appointment or making a phone call
         * */
        private val _isDealerDown = MutableLiveData<Boolean>()
        val isDealerDown: LiveData<Boolean> = _isDealerDown

        /**
         * Boolean determining if the selected dealer can continue with booking an appointment online.
         * If not, we use the inverse of this to determine if the user should have access to the call
         * functionality to this dealer instead
         * */
        private val _canShowAppointmentDetails = MutableLiveData<Boolean>()
        val canShowAppointmentDetails: LiveData<Boolean> = _canShowAppointmentDetails

        /**
         * Click related navigation events
         * */
        private val _mainNavigationEvents = SingleLiveEvent<ScheduleMaintenanceNavigationEvent>()
        val onMainNavigationEvent: LiveData<ScheduleMaintenanceNavigationEvent> = _mainNavigationEvents

        /**
         * Transformation determining if the website should be displayed to the user or not
         * */
        val isWebsiteVisible =
            preferredDealer.map {
                val webUrl: String? =
                    try {
                        it?.website
                    } catch (e: NoSuchElementException) {
                        null
                    }

                !webUrl.isNullOrEmpty() && ToyUtil.hasProtocol(webUrl)
            }

        /**
         * Transformation determining if the address TextView should
         * be hidden to make the UI better looking
         * */
        val isAddressVisible =
            preferredDealer.map {
                !it
                    ?.addresses
                    ?.get(0)
                    ?.line1
                    .isNullOrEmpty()
            }

        /**
         * Transformation determining if the name TextView should
         * be hidden to make the UI better looking
         * */
        val isNameVisible = preferredDealer.map { !it?.dealershipName.isNullOrEmpty() }

        /**
         * Transformation determining if the card view should be hidden to make the UI better looking,
         * given the user has a preferred dealer
         * */
        val isCardViewVisible = preferredDealer.map { (it != null) }

        /**
         * Whenever the canShowAppointmentDetails value is updated. this boolean is set to true
         * to show the continue button
         * */
        val isContinueButtonVisible = canShowAppointmentDetails.map { true }

        private var mMapManager: GoogleMapManager<DealerWrapper>? = null

        /**
         * Pass the preferred dealer to all related singleton classes.
         * This operation is done inside a method, as it can be used from UI components that are using
         * this viewmodel to change the VM's selected dealer
         * */
        fun setPreferredDealer(dealer: Dealer?) {
            _preferredDealer.value = dealer
            // May be redundant but will be passing the tempDealer to the manager class in
            // case it is referenced elsewhere
            ScheduleMaintenanceManager.getInstance().selectedDealer = dealer
            if (!dealer?.addresses.isNullOrEmpty()) {
                _dealerAddress.value = dealer?.addresses?.get(0)?.line1 ?: ""
            }
            if (!dealer?.phoneNumbers.isNullOrEmpty()) {
                _dealerPhone.value = ToyUtil.phoneNumberFormat(dealer?.phoneNumbers?.get(0)?.number)
            }
            analyticsLogger.logEvent(AnalyticsEvent.ALERTS_PREFFERED_DEALER)

            sendDealerIdRequest()
        }

        fun invokeCallNumberNavigationEvent() {
            val number =
                preferredDealer.value
                    ?.phoneNumbers
                    ?.get(0)
                    ?.number
            _mainNavigationEvents.postValue(ScheduleMaintenanceNavigationEvent.CallNumber(number))
        }

        fun onWebsiteClick() {
            val websiteUrl = preferredDealer.value?.website
            _mainNavigationEvents.postValue(ScheduleMaintenanceNavigationEvent.GoToWebsite(websiteUrl))
        }

        fun onPhoneClick() {
            analyticsLogger.logEvent(AnalyticsEvent.ALERTS_SCHEDULE_MAINTENACE_CALL)
            invokeCallNumberNavigationEvent()
        }

        fun onChangeLocationClick() {
            _mainNavigationEvents.postValue(ScheduleMaintenanceNavigationEvent.SearchForLocation)
        }

        /**
         * If the dealership can book appointments online, we allow the user to do so via the continue
         * button. Otherwise, the user will be able to use the same button to call the dealer.
         * */
        fun onContinueButtonClick() {
            if (canShowAppointmentDetails.value == true) {
                analyticsLogger.logEvent(AnalyticsEvent.ALERTS_SCHEDULE_MAINTENACE_CONTINUE)
                _mainNavigationEvents.postValue(ScheduleMaintenanceNavigationEvent.ContinueToScheduling)
            } else {
                invokeCallNumberNavigationEvent()
            }
        }

        fun setMapManager(mapManager: GoogleMapManager<DealerWrapper>) {
            mMapManager = mapManager
            val markerItem = preferredDealer.value?.wrap()
            mMapManager?.apply {
                markerItem?.let {
                    addItemMarker(it)
                    zoomToItem(it, 15f)
                }
            }
        }

        private fun sendPreferredDealerRequest() {
            applicationData.getSelectedVehicle()?.let {
                viewModelScope.launch {
                    showProgress()
                    val vin = it.vin
                    val region = it.region
                    val response = repository.getPreferredDealer(vin, region)
                    hideProgress()

                    when (response) {
                        is Resource.Success -> {
                            if (response.data?.payload?.isNullOrEmpty() == false) {
                                val dealerFromResponse = response.data?.payload?.first()
                                setPreferredDealer(dealerFromResponse)
                            }
                        }

                        else -> {
                            // do nothing
                        }
                    }
                }
            }
        }

        private fun sendDealerIdRequest() {
            viewModelScope.launch {
                val preferredDealerCode = preferredDealer.value?.dealershipName ?: return@launch

                showProgress()
                val response =
                    applicationData.getSelectedVehicle()?.let {
                        repository.getDealerId(
                            it.vin,
                            preferredDealerCode,
                        )
                    }
                hideProgress()

                when (response) {
                    is Resource.Success -> {
                        val payload = response.data?.payload
                        val isDealerCodeAvailableBool = !payload?.providerDealerCode.isNullOrBlank()
                        val isDealerDownBool = !(payload?.isDealerAvailable ?: true)
                        payload?.let { setPayLoadDataToScheduleMaintenanceManager(it) }
                        // We only want to let the check the logic if the dealer is down or not
                        // ONLY when we already have a dealer code available. Otherwise, the UI is
                        // populated and we show the user the call button, from which they can call
                        // the dealer
                        if (isDealerCodeAvailableBool) {
                            analyticsLogger.logEvent(AnalyticsEvent.DAS_SUPPORTED_DEALER_CODE)
                            _isDealerDown.postValue(isDealerDownBool)
                        }

                        _canShowAppointmentDetails
                            .postValue(isDealerCodeAvailableBool && !isDealerDownBool)
                    }
                    is Resource.Failure -> {
                        _canShowAppointmentDetails.postValue(false)
                    }

                    else -> {
                        // do nothing
                    }
                }
            }
        }

        private fun setPayLoadDataToScheduleMaintenanceManager(payload: DealerId) {
            ScheduleMaintenanceManager.getInstance().apply {
                dealerId = payload.providerDealerCode
                phoneNumberRequired = payload.isPhoneNumberRequired == true
                phoneNumber = payload.phoneNumber
            }
        }

        private fun Dealer.wrap(): DealerWrapper {
            val isToyota = applicationData.getSelectedVehicle()?.isToyotaBrand == true
            return DealerWrapper(
                this,
                0,
                isToyota,
                false,
                mMapManager,
            )
        }

        sealed class ScheduleMaintenanceNavigationEvent {
            object SearchForLocation : ScheduleMaintenanceNavigationEvent()

            object ContinueToScheduling : ScheduleMaintenanceNavigationEvent()

            data class CallNumber(
                val number: String?,
            ) : ScheduleMaintenanceNavigationEvent()

            data class GoToWebsite(
                val website: String?,
            ) : ScheduleMaintenanceNavigationEvent()
        }
    }
