package com.toyota.oneapp.ui.scheduledmaint.maintenancetimeline

import android.content.Context
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.dealer.ScheduleMaintenanceDetails
import com.toyota.oneapp.model.dealer.ScheduleMaintenanceTimeline
import com.toyota.oneapp.network.api.manager.VehicleAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.scheduledmaint.ScheduleMaintenanceMainActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class MaintenanceTimelineViewModel
    @Inject
    constructor(
        private val vehicleAPIManager: VehicleAPIManager,
        private val analyticsLogger: AnalyticsLogger,
        private val applicationData: ApplicationData,
    ) : BaseViewModel() {
        val maintenanceScheduleList: MutableLiveData<ArrayList<ScheduleMaintenanceDetails>> = MutableLiveData()
        val maintenanceListObserver: MutableLiveData<Boolean> = MutableLiveData()
        val maintenanceListerrorResponseObserver: MutableLiveData<Boolean> = MutableLiveData()
        val scheduleMaintenanceTimelineNavigationEvent = SingleLiveEvent<Boolean>()

        fun getMaintenanceTimeLineSchedule(
            vin: String,
            lastKnowMielage: String,
        ) {
            showProgress()
            vehicleAPIManager.getMaintenanceSchedule(
                vin,
                lastKnowMielage,
                object : BaseCallback<ScheduleMaintenanceTimeline?>() {
                    override fun onSuccess(response: ScheduleMaintenanceTimeline?) {
                        val timelineList = response?.payload?.scheduleMaintenanceDetails
                        if (timelineList?.get(0)?.maintenanceTasks.isNullOrEmpty()) {
                            maintenanceListObserver.value = true
                            if (applicationData.getSelectedVehicle() != null) {
                                val vehicleModel = applicationData.getSelectedVehicle()!!.modelDescription
                                val mParam = Pair(ToyotaConstants.VEHICLE_MODEL, vehicleModel)
                                analyticsLogger.logEvent(
                                    AnalyticsEvent.SERVICE_TIMELINE_ADD_MILEAGE_CLICKED,
                                    mParam,
                                )
                            }
                        } else {
                            maintenanceListObserver.value = false
                            maintenanceScheduleList.value = timelineList
                        }
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        maintenanceListerrorResponseObserver.value = true
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun onAddMielageClicked() {
            scheduleMaintenanceTimelineNavigationEvent.value = true
        }

        fun onScheduleMaintButtonClick(context: Context) {
            val intent = Intent(context, ScheduleMaintenanceMainActivity::class.java)
            context.startActivity(intent)
        }
    }
