package com.toyota.oneapp.ui

import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.model.ProgressStatusModel
import com.toyota.oneapp.viewmodel.model.DialogModel
import com.toyota.oneapp.viewmodel.model.LexusUpdateModel
import com.toyota.oneapp.viewmodel.model.ToastModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent

abstract class BaseFragmentViewModel : BaseAuthenticatedModel() {
    private var mBundle: Bundle? = null
    private val mBaseProgressLiveData = MutableLiveData<ProgressStatusModel>()
    protected val mShowDialog = SingleLiveEvent<String?>()
    val showDialog: LiveData<String?> get() = mShowDialog

    protected val mShowMessage = SingleLiveEvent<String>()
    val showMessage: LiveData<String> get() = mShowMessage

    protected val mShowToast = SingleLiveEvent<ToastModel>()
    val showToast: LiveData<ToastModel> get() = mShowToast

    val progressLiveData: LiveData<ProgressStatusModel> get() = mBaseProgressLiveData

    protected val mSetLexusGeneration = MutableLiveData<LexusUpdateModel>()
    val setLexusGeneration: LiveData<LexusUpdateModel> get() = mSetLexusGeneration

    protected val mShowUnsupportedDemoDialog = MutableLiveData<Boolean>()
    val showUnsupportedDemoDialog: LiveData<Boolean> get() = mShowUnsupportedDemoDialog

    protected val mShowCustomDialog = MutableLiveData<DialogModel>()
    val showCustomDialog: LiveData<DialogModel> get() = mShowCustomDialog

    fun showProgressDialog() {
        mBaseProgressLiveData.postValue(ProgressStatusModel(true))
    }

    fun dismissProgressDialog() {
        mBaseProgressLiveData.postValue(ProgressStatusModel(false))
    }

    @Deprecated("Remove when viewContext dependency removed")
    override fun onAttach(bundle: Bundle?) {
        mBundle = bundle
    }

    @Deprecated("Remove when viewContext dependency removed")
    override fun detachView() {
    }

    @Deprecated("Remove when viewContext dependency removed")
    override fun onPause() {
    }

    @Deprecated("Remove when viewContext dependency removed")
    override fun onDestroy() {
    }
}
