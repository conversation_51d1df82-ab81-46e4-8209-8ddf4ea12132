package com.toyota.oneapp.ui.accountsettings

import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.account.AccountManager
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.model.account.CustomerEmail
import com.toyota.oneapp.model.account.CustomerPhoneNumber
import com.toyota.oneapp.model.account.CustomerUpdate
import com.toyota.oneapp.model.account.UpdateEmailOrPhonePayload
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.api.repository.SmsOptInRepository
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BasePresenter
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class UpdateEmailPhonePresenter
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val accountApiManager: AccountAPIManager,
        private val accountManager: AccountManager,
        private val preferenceModel: OneAppPreferenceModel,
        private val smsOptInRepository: SmsOptInRepository,
        private val regionManager: RegionManager,
    ) : BasePresenter<UpdateEmailPhonePresenter.View>(),
        CoroutineScope {
        private val job = Job()
        override val coroutineContext: CoroutineContext = job + Dispatchers.IO
        var editType: Int = 0
        lateinit var editName: String
        lateinit var editValue: String
        lateinit var accountInfo: AccountInfoSubscriber

        fun updateEmailOrPhone() {
            view?.showProgressDialog()
            // Object Id removed as mandatory field
            val updateEmailOrPhonePayload: UpdateEmailOrPhonePayload =
                when (editType) {
                    1 ->
                        UpdateEmailOrPhonePayload(
                            CustomerUpdate(
                                ToyotaConstants.EMPTY_STRING,
                                guid = preferenceModel.getGuid(),
                                emails =
                                    listOf(
                                        CustomerEmail(
                                            emailAddress = editValue,
                                            emailType = accountInfo.customerEmails[0].emailType,
                                            emailVerified = false,
                                        ),
                                    ),
                            ),
                        )
                    else ->
                        UpdateEmailOrPhonePayload(
                            CustomerUpdate(
                                ToyotaConstants.EMPTY_STRING,
                                guid = preferenceModel.getGuid(),
                                phoneNumbers =
                                    listOf(
                                        CustomerPhoneNumber(
                                            phoneNumber = editValue,
                                            phoneType =
                                                if (accountInfo.customerPhoneNumbers?.isNullOrEmpty() ==
                                                    false
                                                ) {
                                                    accountInfo.customerPhoneNumbers[0].phoneType
                                                } else {
                                                    "MOBILE"
                                                },
                                            phoneVerified = false,
                                            countryCode =
                                                if (accountInfo.customerPhoneNumbers?.isNullOrEmpty() ==
                                                    false
                                                ) {
                                                    accountInfo.customerPhoneNumbers[0].countryCode
                                                } else {
                                                    regionManager.getRegionBasedCountryCode()
                                                },
                                        ),
                                    ),
                            ),
                        )
                }
            accountApiManager.sendUpdateVerifyEmailOrPhone(
                BuildConfig.APP_BRAND,
                preferenceModel.getGuid(),
                ToyUtil.getAssociatedSXMGeneration(applicationData.getVehicleList()),
                ToyUtil.getAssociatedSXMBrands(applicationData.getVehicleList()),
                updateEmailOrPhonePayload,
                object : BaseCallback<BaseResponse?>() {
                    override fun onComplete() {
                        view?.hideProgressDialog()
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        if (errorMsg != null) {
                            view?.updateFailed(errorMsg)
                        } else {
                            view?.showDialog(errorMsg)
                        }
                    }

                    override fun onSuccess(response: BaseResponse?) {
                        view?.updateComplete()
                    }
                },
            )
        }

        fun verify(confirmationCode: String) {
            view?.showProgressDialog()
            accountApiManager.sendUpdateVerifyEmailOrPhone(
                BuildConfig.APP_BRAND,
                preferenceModel.getGuid(),
                ToyUtil.getAssociatedSXMGeneration(applicationData.getVehicleList()),
                ToyUtil.getAssociatedSXMBrands(applicationData.getVehicleList()),
                UpdateEmailOrPhonePayload(
                    CustomerUpdate(
                        "",
                        guid = preferenceModel.getGuid(),
                        verificationCode = confirmationCode,
                    ),
                ),
                object : BaseCallback<BaseResponse?>() {
                    override fun onComplete() {
                        view?.hideProgressDialog()
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        view?.verifyFailed(errorMsg)
                    }

                    override fun onSuccess(response: BaseResponse?) {
                        view?.verifyComplete()
                    }
                },
            )
        }

        fun getConsentStatus() {
            view?.showProgressDialog()
            launch {
                val response = smsOptInRepository.getConsentStatus("+1".plus(editValue))
                view?.hideProgressDialog()
                when (response) {
                    is Resource.Success -> {
                        response.data?.let {
                            view?.consentStatus(it.payLoad.consent)
                        }
                    }
                    else -> {
                        view?.consentStatus(null)
                    }
                }
            }
        }

        override fun logout() {
            accountManager.logout()
        }

        interface View : BaseView {
            fun updateComplete()

            fun updateFailed(errorMessage: String)

            fun verifyComplete()

            fun verifyFailed(errorMsg: String?)

            fun consentStatus(status: String?)
        }
    }
