package com.toyota.oneapp.ui.accountsettings.helpandfeedback.vehiclesupport

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.core.MainCoroutineDispatcher
import com.toyota.oneapp.features.find.domain.model.Dealer
import com.toyota.oneapp.features.find.domain.model.PreferredDealer
import com.toyota.oneapp.model.VehicleReportDetails
import com.toyota.oneapp.model.dashboard.OdometerDetailsResponse
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.OwnersManualResponse
import com.toyota.oneapp.model.vehicle.TelemetryItem
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.model.vehicle.isOwnerManualDataAvailable
import com.toyota.oneapp.network.api.manager.TelemetryAPIManager
import com.toyota.oneapp.network.api.manager.VehicleAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject
import kotlin.coroutines.suspendCoroutine

@HiltViewModel
class VehicleSupportViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val vehicleManager: VehicleAPIManager,
        private val telemetryManager: TelemetryAPIManager,
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        @MainCoroutineDispatcher private val dispatcher: CoroutineDispatcher,
    ) : BaseViewModel() {
        sealed class NavigationEvent {
            data class NavigateToVehicleHealthReportScreen(
                val vehicle: VehicleInfo,
            ) : NavigationEvent()

            data class NavigateToManualsAndWarrantiesScreen(
                val ownersManualResponse: OwnersManualResponse,
            ) : NavigationEvent()

            data class NavigateToMaintenanceTimelineScreen(
                val vehicle: VehicleInfo,
            ) : NavigationEvent()

            data class NavigateToAddMileageScreen(
                val vehicle: VehicleInfo,
                val telemetryPayload: TelemetryItem?,
            ) : NavigationEvent()

            data class NavigateToScheduleMaintenanceScreen(
                val dealer: Dealer?,
            ) : NavigationEvent()

            data class NavigateToFAQScreen(
                val vehicle: VehicleInfo,
            ) : NavigationEvent()
        }

        companion object {
            const val ACTION_RECALL = "ACTION_RECALL"
            const val ACTION_MANUALS_AND_WARRANTIES = "ACTION_MANUALS_AND_WARRANTIES"
            const val ACTION_MAINTENANCE_TIMELINE = "ACTION_MAINTENANCE_TIMELINE"
            const val ACTION_CONTACT_DEALER = "ACTION_CONTACT_DEALER"
            const val ACTION_FAQS = "ACTION_FAQS"
        }

        private val mVehicleSupportOptions = MutableLiveData<List<VehicleSupportOptionUIModel>>()
        val vehicleSupportNavigationEvents = SingleLiveEvent<NavigationEvent>()

        val vehicleSupportOptions: LiveData<List<VehicleSupportOptionUIModel>>
            get() = mVehicleSupportOptions

        private var vehicleHealthReportResponse: VehicleReportDetails? = null
        private var ownersManualResponse: OwnersManualResponse? = null
        private var preferredDealerResponse: PreferredDealer? = null
        var vehicle: VehicleInfo = VehicleInfo()

        init {
            vehicle = state["vehicle"] ?: VehicleInfo()
            analyticsLogger.logEvent(AnalyticsEvent.VEHICLE_INFORMATION)

            getVehicleSupportOptionDetails()
        }

        private fun getVehicleSupportOptionDetails() {
            viewModelScope.launch(dispatcher) {
                showProgress()

                coroutineScope {
                    val vehicleHealthReportDeferred = async { getVehicleHealthReport() }
                    val ownerManualDeferred = async { getVehiclesOwnerManual() }
                    val preferredDealerDeffered = async { getPreferredDealer() }

                    vehicleHealthReportResponse = vehicleHealthReportDeferred.await()
                    ownersManualResponse = ownerManualDeferred.await()
                    preferredDealerResponse = preferredDealerDeffered.await()
                }

                hideProgress()
                populateVehicleSupportOptions()
            }
        }

        private suspend fun getVehicleHealthReport(): VehicleReportDetails? =
            suspendCoroutine { continuation ->
                var vehicleHealthReportResponse: VehicleReportDetails? = null
                telemetryManager.sendGetVehicleHealthReportDetailsRequest(
                    vehicle.vin,
                    vehicle.brand,
                    vehicle.generation,
                    vehicle.region,
                    object : BaseCallback<VehicleReportDetails>() {
                        override fun onSuccess(response: VehicleReportDetails) {
                            vehicleHealthReportResponse = response
                        }

                        override fun onComplete() {
                            continuation.resumeWith(Result.success(vehicleHealthReportResponse))
                        }
                    },
                )
            }

        private suspend fun getVehiclesOwnerManual(): OwnersManualResponse? =
            suspendCoroutine { continuation ->
                var ownersManualResponse: OwnersManualResponse? = null
                vehicleManager.getVehiclesOwnerManual(
                    vehicle.vin,
                    vehicle.brand,
                    vehicle.modelDescription,
                    vehicle.modelYear,
                    object : BaseCallback<OwnersManualResponse>() {
                        override fun onSuccess(response: OwnersManualResponse) {
                            ownersManualResponse = response
                        }

                        override fun onComplete() {
                            continuation.resumeWith(Result.success(ownersManualResponse))
                        }
                    },
                )
            }

        private suspend fun getPreferredDealer(): PreferredDealer? =
            suspendCoroutine { continuation ->
                var preferredDealerResponse: PreferredDealer? = null
                telemetryManager.sendGetPreferDealerRequest(
                    vehicle.vin,
                    vehicle.region,
                    vehicle.brand,
                    object : BaseCallback<PreferredDealer>() {
                        override fun onSuccess(response: PreferredDealer) {
                            preferredDealerResponse = response
                        }

                        override fun onComplete() {
                            continuation.resumeWith(Result.success(preferredDealerResponse))
                        }
                    },
                )
            }

        private fun populateVehicleSupportOptions() {
            val options =
                listOfNotNull(
                    VehicleSupportOptionUIModel(name = R.string.recalls, action = ACTION_RECALL).takeIf {
                        vehicleHealthReportResponse?.payload != null &&
                            vehicle.brand != VehicleInfo.BRAND_SUBARU
                    },
                    VehicleSupportOptionUIModel(
                        name = R.string.Garage_manuals_warranties,
                        action = ACTION_MANUALS_AND_WARRANTIES,
                    ).takeIf {
                        vehicle.isFeatureEnabled(Feature.OWNERS_MANUAL) && (
                            ownersManualResponse?.payload?.isOwnerManualDataAvailable()
                                ?: false
                        )
                    },
                    VehicleSupportOptionUIModel(
                        name = R.string.maintenance_schedule,
                        action = ACTION_MAINTENANCE_TIMELINE,
                    ).takeIf {
                        vehicle.isFeatureEnabled(Feature.MAINTENANCE_TIMELINE)
                    },
                    VehicleSupportOptionUIModel(
                        name = R.string.contact_dealer,
                        action = ACTION_CONTACT_DEALER,
                    ),
                    VehicleSupportOptionUIModel(
                        name = R.string.faqs,
                        action = ACTION_FAQS,
                    ).takeIf {
                        vehicle.brand != VehicleInfo.BRAND_SUBARU
                    },
                )
            mVehicleSupportOptions.value = options
        }

        fun onVehicleSupportOptionClicked(option: VehicleSupportOptionUIModel) {
            when (option.action) {
                ACTION_RECALL -> {
                    analyticsLogger.logEvent(AnalyticsEvent.RECALLS)
                    onRecallsClicked()
                }

                ACTION_MANUALS_AND_WARRANTIES -> {
                    analyticsLogger.logEvent(AnalyticsEvent.MANUALS_AND_WARRANTIES)
                    onManualsAndWarrantiesClicked()
                }

                ACTION_MAINTENANCE_TIMELINE -> {
                    analyticsLogger.logEvent(AnalyticsEvent.MAINTENANCE_TIMELINE)
                    onMaintenanceTimelineClicked()
                }

                ACTION_CONTACT_DEALER -> {
                    analyticsLogger.logEvent(AnalyticsEvent.CONTACT_DEALER)
                    onContactDealerClicked()
                }

                ACTION_FAQS -> {
                    analyticsLogger.logEvent(AnalyticsEvent.VEHICLE_FAQ)
                    onFAQClicked()
                }
            }
        }

        private fun onRecallsClicked() {
            vehicleSupportNavigationEvents.value =
                NavigationEvent.NavigateToVehicleHealthReportScreen(
                    vehicle,
                )
        }

        private fun onManualsAndWarrantiesClicked() {
            vehicleSupportNavigationEvents.value =
                NavigationEvent.NavigateToManualsAndWarrantiesScreen(
                    ownersManualResponse!!,
                )
        }

        private fun onMaintenanceTimelineClicked() {
            if (vehicle.generation != null && applicationData.mileage[vehicle.vin] != null && applicationData.mileage[vehicle.vin] != 0) {
                vehicleSupportNavigationEvents.value =
                    NavigationEvent.NavigateToMaintenanceTimelineScreen(
                        vehicle,
                    )
            } else if (vehicle.isNonCvtVehicle || vehicle.generation == null) {
                vehicleSupportNavigationEvents.value =
                    NavigationEvent.NavigateToAddMileageScreen(
                        vehicle,
                        null,
                    )
            } else {
                showProgress()
                telemetryManager.sendGetTelemetryRequest(
                    vehicle,
                    object : BaseCallback<OdometerDetailsResponse?>() {
                        override fun onSuccess(response: OdometerDetailsResponse?) {
                            hideProgress()
                            vehicleSupportNavigationEvents.value =
                                NavigationEvent.NavigateToAddMileageScreen(
                                    vehicle,
                                    response?.payload?.odometer,
                                )
                        }

                        override fun onFailError(
                            httpCode: Int,
                            errorMsg: String?,
                        ) {
                            hideProgress()
                            vehicleSupportNavigationEvents.value =
                                NavigationEvent.NavigateToAddMileageScreen(
                                    vehicle,
                                    null,
                                )
                        }

                        override fun onComplete() {
                            hideProgress()
                        }
                    },
                )
            }
        }

        private fun onContactDealerClicked() {
            val dealer = preferredDealerResponse?.payload?.firstOrNull()
            val event = NavigationEvent.NavigateToScheduleMaintenanceScreen(dealer)
            vehicleSupportNavigationEvents.value = event
        }

        private fun onFAQClicked() {
            vehicleSupportNavigationEvents.value = NavigationEvent.NavigateToFAQScreen(vehicle)
        }
    }
