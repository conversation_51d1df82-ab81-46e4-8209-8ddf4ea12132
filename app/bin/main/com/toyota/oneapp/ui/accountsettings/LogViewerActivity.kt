package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.view.Menu
import android.view.MenuItem
import android.widget.TextView
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.widget.Toolbar
import androidx.core.content.FileProvider
import androidx.lifecycle.Observer
import androidx.lifecycle.coroutineScope
import com.idemia.deviceagent.utils.LogUtils
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.NUMBER_1
import com.toyota.oneapp.digitalkey.NUMBER_2
import com.toyota.oneapp.digitalkey.logName
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.viewmodel.LogCatViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import toyotaone.commonlib.log.LogTool
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

@AndroidEntryPoint
class LogViewerActivity : UiBaseActivity() {
    private val createFileRequestLauncher =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { createFileResult: ActivityResult ->
            if (createFileResult.resultCode == Activity.RESULT_OK) {
                val uri = createFileResult.data?.data ?: Uri.parse(ToyotaConstants.EMPTY_STRING)
                writeToFile(uri)
            }
        }

    private val logcatViewModel by viewModels<LogCatViewModel>()

    private var logTextView: TextView? = null

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        if (!BuildConfig.DEBUG) {
            // This activity should not be opened in Release build
            finish()
        }
        setContentView(R.layout.activity_log_viewer)
        val toolBar = findViewById<Toolbar>(R.id.log_viewer_toolbar)
        performActivitySetup(toolBar)
        observeBaseEvents(logcatViewModel)
        if (intent.hasExtra("LOG_TYPE")) {
            readLog(intent.getIntExtra("LOG_TYPE", 0))
        } else {
            logcatViewModel.showProgress()
            Handler(this.mainLooper).postDelayed({
                logcatViewModel.hideProgress()
            }, 5000)
            logTextView = findViewById<TextView>(R.id.tv_debug_logs_text)
            logTextView?.text = DigitalMopKeyUtils.digitalLog.toString()
            logTextView?.append(LogUtils.getLogs(this))
            logcatViewModel.logcatOutput().observe(
                this,
                Observer { logMessage ->
                    logTextView?.append("$logMessage\n")
                },
            )
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        super.onCreateOptionsMenu(menu)
        menuInflater.inflate(R.menu.menu_toolbar_log_viewer, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_lv_save -> {
                saveLog()
                return true
            }
            R.id.action_lv_share -> {
                saveLocalFile()
                return true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun saveLog() {
        val defaultFileName = "OneAppDebugLog.log"

        val createDocumentIntent = Intent(Intent.ACTION_CREATE_DOCUMENT)
        createDocumentIntent.addCategory(Intent.CATEGORY_OPENABLE)
        createDocumentIntent.type = "text/plain"
        createDocumentIntent.putExtra(Intent.EXTRA_TITLE, defaultFileName)
        createFileRequestLauncher.launch(createDocumentIntent)
    }

    private fun shareToMailAccount() {
        val emailIntent = Intent(Intent.ACTION_SEND)
        val logDir = getExternalFilesDir(ONEAPP_LOG_FOLDER_NAME)

        val to = arrayOf("<EMAIL>")
        emailIntent.apply {
            putExtra(Intent.EXTRA_EMAIL, to)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            putExtra(Intent.EXTRA_TEXT, "Android Logs")
            type = "vnd.android.cursor.dir/email"
            putExtra(Intent.EXTRA_SUBJECT, "Android Logs")
        }
        if (logDir?.listFiles()?.size ?: 0 > 0) {
            logDir?.listFiles()?.get(0)?.let {
                emailIntent.putExtra(
                    Intent.EXTRA_STREAM,
                    FileProvider.getUriForFile(
                        this,
                        this.applicationContext.packageName + ".GenericFileProvider",
                        it,
                    ),
                )
            }
        }

        startActivity(Intent.createChooser(emailIntent, "Send Android Logs"))
    }

    private fun saveLocalFile() {
        val file = getExternalFilesDir(ONEAPP_LOG_FOLDER_NAME)
        if (file?.exists() == false) {
            file.mkdir()
        }
        val tmpFilePath = File(file, "Logcat.txt")
        if (tmpFilePath.exists()) tmpFilePath.delete()
        try {
            val out = FileOutputStream(tmpFilePath)
            val pdfAsBytes = logTextView?.text?.toString()?.toByteArray()
            out.write(pdfAsBytes ?: ByteArray(0))
            out.flush()
            out.close()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        shareToMailAccount()
    }

    private fun writeToFile(uri: Uri) {
        try {
            val pfd = contentResolver.openFileDescriptor(uri, "w")

            pfd?.run {
                val outputStream = FileOutputStream(fileDescriptor)
                outputStream.write(logTextView?.text.toString().toByteArray())
                outputStream.close()
                close()
            }
        } catch (e: IOException) {
            LogTool.e("File write failure", e.message, e)
        }
    }

    private fun readLog(logSource: Int) {
        showProgressDialog()
        lifecycle.coroutineScope.launch {
            logTextView?.text =
                when (logSource) {
                    NUMBER_1 -> {
                        DigitalMopKeyUtils.digitalLog.toString()
                    }
                    NUMBER_2 -> {
                        LogUtils.getLogs(this@LogViewerActivity)
                    }
                    else -> {
//                TODO: change Environment.getExternalStorageDirectory() for not deprecated option
                        val filePath = Environment.getExternalStorageDirectory()
                        val logFileName = """${filePath}${File.separator}DigitalKey${File.separator}$logName"""
                        if (File(logFileName).exists()) {
                            File(logFileName).readText()
                        } else {
                            "File not found"
                        }
                    }
                }
            hideProgressDialog()
        }
    }

    companion object {
        private const val ONEAPP_LOG_FOLDER_NAME = "OneAppLogFolder"
    }
}
