package com.toyota.oneapp.ui.accountsettings.helpandfeedback.contactus

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemContactUsBinding
import com.toyota.oneapp.model.account.helpandfeedback.ContactOption
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

class ContactUsAdapter(
    private var options: List<ContactOption>,
    private val onItemClickListener: OnItemClickListener,
) : RecyclerView.Adapter<ContactUsAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<ContactOption> {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding =
            ItemContactUsBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.onBind(options[position])
    }

    override fun getItemCount(): Int = options.size

    // BindableRecyclerViewAdapter Methods.
    override fun setData(data: List<ContactOption>?) {
        this.options = data ?: emptyList()
        notifyDataSetChanged()
    }

    inner class ViewHolder(
        private val binding: ItemContactUsBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        init {
            binding.root.setOnClickListener {
                onItemClickListener.onItemClick(options[adapterPosition])
            }
        }

        fun onBind(option: ContactOption) {
            binding.option = option
        }
    }

    interface OnItemClickListener {
        fun onItemClick(option: ContactOption)
    }
}
