package com.toyota.oneapp.ui.accountsettings.helpandfeedback.contactus

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.account.helpandfeedback.ContactMode
import com.toyota.oneapp.model.account.helpandfeedback.ContactOption
import com.toyota.oneapp.model.account.helpandfeedback.ContactResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.api.manager.HelpAndFeedbackAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ContactUsViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val applicationData: ApplicationData,
        private val helpAndFeedbackAPIManager: HelpAndFeedbackAPIManager,
        private val languageManager: LanguageManager,
    ) : BaseViewModel() {
        private var vehicle: VehicleInfo? = null

        sealed class NavigationEvent {
            data class NavigateToPhoneDialer(
                val number: String,
            ) : NavigationEvent()

            data class NavigateToEmailClient(
                val email: String,
            ) : NavigationEvent()
        }

        private val mOptions = MutableLiveData<List<ContactOption>>()
        private val mContactUsNavigationEvent = MutableLiveData<NavigationEvent>()

        val options: LiveData<List<ContactOption>>
            get() = mOptions
        val contactUsNavigationEvent: LiveData<NavigationEvent>
            get() = mContactUsNavigationEvent

        init {
            vehicle = state["vehicle"]
            fetchContactOptions()
        }

        private fun fetchContactOptions() {
            showProgress()
            helpAndFeedbackAPIManager.getContactOptions(
                hasVehicle = applicationData.getVehicleList()?.isNotEmpty() == true,
                brand = vehicle?.brand ?: BuildConfig.APP_BRAND,
                region = vehicle?.region ?: languageManager.getCurrentRegion().regionCode,
                callback =
                    object : BaseCallback<ContactResponse>() {
                        override fun onSuccess(response: ContactResponse) {
                            response.payload?.response?.let {
                                mOptions.value = it
                            }
                        }

                        override fun onFailError(
                            httpCode: Int,
                            errorMsg: String?,
                        ) {
                            showErrorMessage(errorMsg)
                        }

                        override fun onComplete() {
                            hideProgress()
                        }
                    },
            )
        }

        fun onContactOptionClicked(option: ContactOption) {
            when (option.type) {
                ContactMode.EMAIL -> {
                    mContactUsNavigationEvent.value =
                        NavigationEvent.NavigateToEmailClient(
                            option.targetValue
                                ?: "",
                        )
                }
                ContactMode.PHONE -> {
                    mContactUsNavigationEvent.value =
                        NavigationEvent.NavigateToPhoneDialer(
                            option.targetValue
                                ?: "",
                        )
                }
            }
        }
    }
