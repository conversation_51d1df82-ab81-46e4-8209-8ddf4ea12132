package com.toyota.oneapp.ui.accountsettings.personalinfo

import android.app.Activity
import android.os.Bundle
import android.os.Handler
import androidx.activity.viewModels
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivitySmsHangTightBinding
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SmsHangTightActivity : DataBindingBaseActivity<ActivitySmsHangTightBinding>() {
    private val smsHangTightViewModel: SmsHangTightViewModel by viewModels()

    override fun getLayoutId() = R.layout.activity_sms_hang_tight

    private val delayPolling: Long = 5000
    private var timeoutSmsConsentStatus = 0

    override fun initViews(savedInstance: Bundle?) {
        smsHangTightViewModel.phoneNumber = intent.getStringExtra(ToyotaConstants.PHONE_NUMBER)
            ?: ToyotaConstants.EMPTY_STRING
        smsHangTightViewModel.consentInfo.observe(this) {
            it?.let {
                processConsent(it)
            } ?: kotlin.run {
                setResult(Activity.RESULT_CANCELED)
                finish()
            }
        }

        smsHangTightViewModel.hangTightTimerStatus.observe(this) {
            timeoutSmsConsentStatus = it
        }

        setSupportActionBar(binding.toolbar)
        smsHangTightViewModel.getConsentStatus()
        smsHangTightViewModel.startTimer()
        binding.tvWaitingMessageTitle.text =
            getString(
                org.forgerock.android.auth.ui.R.string.sms_consent_hang_tight,
                getString(R.string.app_name),
            )
    }

    private fun processConsent(consent: String) {
        when (consent) {
            ToyotaConstants.PHONE_VERIFICATION_CONSENT_YES, ToyotaConstants.PHONE_VERIFICATION_CONSENT_NO -> {
                setResult(Activity.RESULT_OK)
                finish()
            }
            else -> {
                if (timeoutSmsConsentStatus != SmsHangTightViewModel.TIMER_FINISHED) {
                    Handler(mainLooper).postDelayed({
                        smsHangTightViewModel.getConsentStatus()
                    }, delayPolling)
                } else {
                    setResult(Activity.RESULT_CANCELED)
                    finish()
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        smsHangTightViewModel.stopTimer()
    }
}
