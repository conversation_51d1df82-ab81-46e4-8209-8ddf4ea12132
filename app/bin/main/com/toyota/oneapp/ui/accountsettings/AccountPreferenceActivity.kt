package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityPreferenceBinding
import com.toyota.oneapp.model.subscription.MusicPreferencePayload
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

private const val PREFERENCE_MODIFIED = "preference_modified"

@AndroidEntryPoint
class AccountPreferenceActivity : UiBaseActivity() {
    lateinit var binding: ActivityPreferenceBinding
    private val viewModel: AccountPreferenceViewModel by viewModels()
    private var fallbackDefaultMusicAccount: LinkedAccounts = LinkedAccounts.NONE

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding =
            DataBindingUtil.setContentView<ActivityPreferenceBinding>(
                this,
                R.layout.activity_preference,
            )
        val preferences = intent.getParcelableExtra(LinkedAccountsActivity.DATA) as? MusicPreferencePayload
        val preferenceType = intent.getParcelableExtra(LinkedAccountsActivity.PREFERENCE_TYPE) as? LinkedAccounts
        (intent.getParcelableExtra(LinkedAccountsActivity.DEFAULT_MUSIC_SERVICE_FALLBACK) as? LinkedAccounts)?.let {
            fallbackDefaultMusicAccount =
                it
        }
        if (preferences == null || preferenceType == null) {
            finish(false)
            return
        }
        viewModel.initialize(preferences, preferenceType)
        observeEvents()
        setSupportActionBar(binding.preferenceInfoToolbar)
        setUpUi()
    }

    private fun setUpUi() {
        if (viewModel.currentMusicAccount == LinkedAccounts.AMAZON) {
            setUpAmazonUi()
        } else {
            setUpAppleUi()
        }
        updateDefaultOptions()
        setUpClickListeners()
    }

    private fun setUpAmazonUi() {
        binding.preferenceInfoToolbar.title = getString(R.string.amazon_music)
        binding.musicPreferenceIv.setImageResource(R.drawable.amazon_music)
        binding.streamQualityLayout.visibility = View.VISIBLE
        with(viewModel.musicPreferencePayload) {
            binding.streamQualityTv.text =
                getString(
                    StreamQuality.getStreamQuality(streamingQuality).textId,
                )
            binding.musicDefaultPreferenceSwitch.isChecked = LinkedAccounts.AMAZON.mediaSource == mediaSource
        }
    }

    private fun setUpAppleUi() {
        binding.preferenceInfoToolbar.title = getString(R.string.apple_music)
        binding.musicPreferenceIv.setImageResource(R.drawable.apple_music)
        binding.streamQualityLayout.visibility = View.GONE
        binding.musicDefaultPreferenceSwitch.isChecked = LinkedAccounts.APPLE.mediaSource == viewModel.musicPreferencePayload.mediaSource
        binding.streamPickerLayout.visibility = View.GONE
    }

    private fun setUpClickListeners() {
        binding.unlinkBtn.setOnClickListener {
            showUnlinkCautionDialog()
        }

        binding.streamQualityLayout.setOnClickListener {
            if (binding.streamPickerLayout.visibility == View.VISIBLE) {
                onStreamQualitySelected()
            } else {
                showStreamQualityPicker()
            }
        }

        binding.musicDefaultPreferenceSwitch.setOnCheckedChangeListener { buttonView, isChecked ->
            if (buttonView.isPressed) {
                onDefaultSwitchToggled(isChecked)
            }
        }

        binding.tvMusicSave.setOnClickListener {
            if (binding.streamPickerLayout.visibility == View.VISIBLE) {
                onStreamQualitySelected()
            }
            viewModel.savePreferences()
        }

        binding.doneButton.setOnClickListener {
            onStreamQualitySelected()
        }
    }

    private fun onDefaultSwitchToggled(isChecked: Boolean) {
        val default = if (isChecked) viewModel.currentMusicAccount else fallbackDefaultMusicAccount
        viewModel.setDefault(default)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }

    private fun updateDefaultOptions() {
        // If a fallback music service is not available, disable the Set as default switch
        if (fallbackDefaultMusicAccount == LinkedAccounts.NONE) {
            binding.musicDefaultPreferenceSwitch.isEnabled = false
        }
    }

    private fun showStreamQualityPicker() {
        binding.streamPickerLayout.visibility = View.VISIBLE
        binding.arrowIv.setImageResource(R.drawable.ic_upward_arrow)
        binding.streamPicker.apply {
            wrapSelectorWheel = true
            minValue = 0
            maxValue = StreamQuality.values().size - 1
            displayedValues = StreamQuality.values().map { getString(it.textId) }.toTypedArray()
            value =
                StreamQuality
                    .getStreamQuality(
                        viewModel.musicPreferencePayload.streamingQuality,
                    ).ordinal
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                textColor = getColor(R.color.plain_black_russian)
            }
        }
    }

    override fun onBackPressed() {
        if (viewModel.haveUnsavedChanges()) {
            DialogUtil.showDialog(
                this,
                getString(R.string.linked_accounts_unsaved_dlg_title),
                getString(R.string.linked_accounts_unsaved_dlg_msg),
                getString(R.string.Common_save),
                getString(R.string.Common_cancel),
                object : OnCusDialogInterface {
                    override fun onConfirmClick() {
                        viewModel.savePreferences()
                    }

                    override fun onCancelClick() {
                        finish(false)
                    }
                },
                true,
            )
        } else {
            finish(false)
        }
    }

    private fun showUnlinkCautionDialog() {
        DialogUtil.showDialog(
            this,
            getString(R.string.linked_accounts_unlink_caution_dlg_title),
            getString(R.string.linked_accounts_unlink_caution_dlg_msg),
            getString(R.string.Common_continue),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    viewModel.unlinkService()
                }

                override fun onCancelClick() {
                    // Do nothing
                }
            },
            true,
        )
    }

    private fun observeEvents() {
        observeBaseEvents(viewModel)
        viewModel.dismissScreenEvent.observe(this, Observer { unlinked -> finish(unlinked) })
        viewModel.saveButtonStateChanged.observe(
            this,
            Observer { binding.tvMusicSave.isEnabled = it },
        )
    }

    private fun finish(unLinked: Boolean) {
        // Returning with Unlink status
        val resultIntent =
            Intent().apply {
                putExtra(LinkedAccountsActivity.UNLINK_RESULT, unLinked)
                putExtra(PREFERENCE_MODIFIED, viewModel.preferenceModified)
            }
        setResult(Activity.RESULT_OK, resultIntent)
        finish()
    }

    private fun onStreamQualitySelected() {
        val streamQuality = StreamQuality.getStreamQuality(binding.streamPicker.value)
        binding.streamQualityTv.text = getString(streamQuality.textId)
        binding.streamPickerLayout.visibility = View.GONE
        binding.arrowIv.setImageResource(R.drawable.ic_downward_arrow)
        viewModel.setStreamingQuality(streamQuality)
    }

    enum class StreamQuality(
        val streamQuality: String,
        val textId: Int,
    ) {
        LOW("low", R.string.music_stream_quality_low_val),
        MEDIUM("medium", R.string.music_stream_quality_medium_val),
        HIGH("high", R.string.music_stream_quality_high_val),
        ;

        companion object {
            private val streamQualities = values().associateBy { it.streamQuality }

            fun getStreamQuality(streamQuality: String?): StreamQuality = streamQualities.getOrDefault(streamQuality, MEDIUM)

            fun getStreamQuality(ordinal: Int): StreamQuality = values().getOrNull(ordinal) ?: MEDIUM
        }
    }

    class Contract : ActivityResultContract<Bundle, AccountPreferenceResult?>() {
        override fun createIntent(
            context: Context,
            input: Bundle,
        ): Intent = Intent(context, AccountPreferenceActivity::class.java).apply { putExtras(input) }

        override fun parseResult(
            resultCode: Int,
            intent: Intent?,
        ): AccountPreferenceResult? {
            if (Activity.RESULT_OK != resultCode) {
                return null
            }
            return intent?.let {
                AccountPreferenceResult(
                    it.getBooleanExtra(PREFERENCE_MODIFIED, false),
                    it.getBooleanExtra(LinkedAccountsActivity.UNLINK_RESULT, false),
                )
            }
        }
    }
}
