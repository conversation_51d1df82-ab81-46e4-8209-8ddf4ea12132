package com.toyota.oneapp.ui.accountsettings.personalinfo

import android.os.Bundle
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.LanguageAdapter.LanguageItemClickListener
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.component.popup.LanguagePopup
import com.toyota.oneapp.databinding.FragmentPreferredLanguageBinding
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.language.LanguageItem
import com.toyota.oneapp.model.language.RegionItem
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.flutter.DASHBOARD_ENGINE_ID
import com.toyota.oneapp.ui.flutter.UPDATE_LOCALE_CHANNEL
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.FlutterOneAppUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.language.SupportedLanguages.Selector.getLanguageNameFromAcronym
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import javax.inject.Inject

@AndroidEntryPoint
class PreferredLanguageFragment :
    BaseDataBindingFragment<FragmentPreferredLanguageBinding>(),
    LanguageItemClickListener {
    private val viewModel: PreferredLanguageViewModel by viewModels()

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @Inject
    lateinit var languageManager: LanguageManager

    @Inject
    lateinit var applicationData: ApplicationData

    private lateinit var languagePopup: LanguagePopup

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentPreferredLanguageBinding,
        savedInstance: Bundle?,
    ) {
        binding.viewModel = viewModel
        activity?.let {
            viewModel.onInit(
                it,
                requireActivity().intent.getParcelableExtra(ToyotaConstants.ACCOUNT_INFO),
            )
        }
        viewModel.preferredLanguageNavigationEvent.observe(viewLifecycleOwner) {
            when (it) {
                is PreferredLanguageNavigationEvent.OnAccountUpdateSuccess -> {
                    AppLanguageUtils.changeAppLanguage(activity, it.language)
                    applicationData.setSelectedLanguage(it.language)
                    FlutterOneAppUtil.sendBasicChannelMessage(
                        engineId = DASHBOARD_ENGINE_ID,
                        channelName = UPDATE_LOCALE_CHANNEL,
                        param = AppLanguageUtils.getCurrentLocaleString(),
                    )
                    findNavController().popBackStack()
                }
                is PreferredLanguageNavigationEvent.OnLanguageItemClick ->
                    showLanguagePopUp(
                        it.regionItem,
                    )
                is PreferredLanguageNavigationEvent.OnBack -> findNavController().popBackStack()
                is PreferredLanguageNavigationEvent.ShowSaveDialog -> {
                    DialogUtil.showDialog(
                        activity,
                        null,
                        getString(R.string.Preferred_Language_confirmation),
                        getString(R.string.Common_okay),
                        getString(R.string.Common_cancel),
                        object : OnCusDialogInterface {
                            override fun onConfirmClick() {
                                viewModel.save()
                            }

                            override fun onCancelClick() {
                            }
                        },
                        true,
                    )
                }
            }
        }
    }

    override fun getLayout(): Int = R.layout.fragment_preferred_language

    private fun showLanguagePopUp(regionItem: RegionItem) {
        val languagePopup =
            LanguagePopup(
                activity,
                viewDataBinding.language.width,
                ViewGroup.LayoutParams.WRAP_CONTENT,
            )
        languagePopup.setRegionData(regionItem, this)
        languagePopup.showPopupWindow(viewDataBinding.language)
    }

    override fun onLanguageItemClick(item: LanguageItem) {
        val name =
            activity?.let { getLanguageNameFromAcronym(item.code, it) } ?: getString(
                R.string.Language_English,
            )
        viewModel.updateUI(item.code, name)
        if (this::languagePopup.isInitialized && languagePopup.isShowing) {
            languagePopup.dismiss()
        }
        activity?.let { viewModel.didUserChangeLanguage() }
    }
}
