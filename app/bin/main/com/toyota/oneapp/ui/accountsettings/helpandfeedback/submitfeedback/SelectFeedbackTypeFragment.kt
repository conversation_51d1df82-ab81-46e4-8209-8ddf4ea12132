package com.toyota.oneapp.ui.accountsettings.helpandfeedback.submitfeedback

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.databinding.FragmentSelectFeedbackTypeBinding
import com.toyota.oneapp.model.account.helpandfeedback.FeedbackType

/**
 * The Fragment allows the user to select the FeedbackType.
 */
class SelectFeedbackTypeFragment :
    Fragment(),
    FeedbackTypeAdapter.OnItemClickListener {
    companion object {
        const val SELECTED_TYPE_REQUEST_KEY = "SELECTED_TYPE_REQUEST_KEY"
        const val SELECTED_TYPE = "SELECTED_TYPE"
    }

    private lateinit var binding: FragmentSelectFeedbackTypeBinding

    private lateinit var types: List<FeedbackType>
    private var currentSelectedType: FeedbackType? = null

    private lateinit var adapter: FeedbackTypeAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentSelectFeedbackTypeBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        initializeExtraData()
        initializeViews()

        return binding.root
    }

    // FeedbackTypeAdapter.OnItemClickListener
    override fun onItemClick(type: FeedbackType) {
        setFragmentResult(SELECTED_TYPE_REQUEST_KEY, bundleOf(SELECTED_TYPE to type))
        findNavController().popBackStack()
    }

    private fun initializeExtraData() {
        val args = SelectFeedbackTypeFragmentArgs.fromBundle(requireArguments())
        types = args.types.toList()
        currentSelectedType = args.currentSelectedType
    }

    private fun initializeViews() {
        adapter = FeedbackTypeAdapter(types, currentSelectedType, this)
        binding.rvTypes.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            addItemDecoration(DividerItemDecoration(requireContext(), LinearLayout.VERTICAL))
        }
    }
}
