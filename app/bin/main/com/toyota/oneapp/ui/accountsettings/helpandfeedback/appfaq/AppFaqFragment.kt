package com.toyota.oneapp.ui.accountsettings.helpandfeedback.appfaq

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.FragmentAppFaqBinding
import com.toyota.oneapp.model.account.helpandfeedback.Faq
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.recyclerview.decorator.SectionItemDecorator
import javax.inject.Inject

@AndroidEntryPoint
class AppFaqFragment :
    BaseViewModelFragment(),
    AppFaqAdapter.OnItemClickListener {
    private val viewModel: AppFaqViewModel by viewModels()

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    private lateinit var binding: FragmentAppFaqBinding
    private lateinit var adapter: AppFaqAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        analyticsLogger.logEvent(AnalyticsEvent.APP_FAQ)

        observeBaseEvents(viewModel)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentAppFaqBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        initializeViews()

        return binding.root
    }

    // On Item Clicked.
    override fun onItemClick(faq: Faq) {
        analyticsLogger.logStringEvent(faq.firebaseEvent)
        ToyUtil.openCustomChromeTab(requireContext(), faq.url)
    }

    private fun initializeViews() {
        adapter = AppFaqAdapter(emptyList(), this)
        binding.rvFaqs.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            addItemDecoration(SectionItemDecorator(requireContext()))
        }

        viewModel.faqUIModels.observe(viewLifecycleOwner) { data ->
            DataBindingAdapters.setRecyclerViewAdapterData(binding.rvFaqs, data, emptyList())
        }
    }
}
