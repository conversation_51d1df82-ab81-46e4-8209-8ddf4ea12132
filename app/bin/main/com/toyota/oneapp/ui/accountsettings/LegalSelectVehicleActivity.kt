package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.LinearLayout
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.widget.Toolbar
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.model.pref.LanguagePreferenceModel
import com.toyota.oneapp.model.reconsent.ReConsentData
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModelNavigationEvent
import com.toyota.oneapp.ui.MVPBaseActivity
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.selectvehicle.SelectVehicleAdapter
import com.toyota.oneapp.ui.dataconsent.activities.MasterDataConsentActivity
import com.toyota.oneapp.ui.dataconsent.viewmodels.MasterDataConsentViewModel
import com.toyota.oneapp.util.IntentUtil
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class LegalSelectVehicleActivity :
    MVPBaseActivity<LegalSelectVehiclePresenter>(),
    LegalSelectVehiclePresenter.View,
    SelectVehicleAdapter.OnItemClickListener {
    @Inject
    lateinit var presenter: LegalSelectVehiclePresenter

    @Inject
    lateinit var languagePreferenceModel: LanguagePreferenceModel

    @Inject
    lateinit var applicationData: ApplicationData

    @Inject
    lateinit var regionManager: RegionManager
    private lateinit var adapter: SelectVehicleAdapter

    var selectedVehicle: VehicleInfo? = null
    private val masterDataConsentViewModel: MasterDataConsentViewModel by viewModels()
    private lateinit var reConsentLauncher: ActivityResultLauncher<Intent>

    override fun createPresenter(): LegalSelectVehiclePresenter = presenter

    private val vehicleResult =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            onActivityNewResult(result)
        }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        setContentView(R.layout.activity_legal_select_vehicle)
        val toolbar = findViewById<View>(R.id.account_settings_toolbar) as Toolbar
        setSupportActionBar(toolbar)

        reConsentLauncher =
            registerForActivityResult(
                ActivityResultContracts.StartActivityForResult(),
            ) {
                if (it.resultCode == Activity.RESULT_OK) {
                    launchEditConsent()
                }
            }
        masterDataConsentViewModel.reConsentData.observe(this) { reConsentData: ReConsentData ->
            if (reConsentData.reConsentEnabled) {
                val intent = Intent(this, MasterDataConsentActivity::class.java)
                intent.putExtra(MasterDataConsentActivity.EXTRA_RE_CONSENT_DATA, reConsentData)
                intent.putExtra(MasterDataConsentActivity.EXTRA_VEHICLE, selectedVehicle)
                reConsentLauncher.launch(intent)
            } else {
                launchEditConsent()
            }
        }

        masterDataConsentViewModel.progressEvents.observe(
            this,
            Observer {
                when (it) {
                    is BaseViewModelNavigationEvent.ShowProgress -> showProgressDialog()
                    is BaseViewModelNavigationEvent.HideProgress -> hideProgressDialog()
                    else -> {}
                }
            },
        )
    }

    override fun onStart() {
        super.onStart()
        initVehicleList(applicationData.getVehicleList())
    }

    override fun initVehicleList(list: List<VehicleInfo>?) {
        adapter =
            SelectVehicleAdapter(
                list?.filter { !it.generation.isNullOrBlank() && !it.isRemoteOnlyUser } ?: emptyList(),
                this,
            )
        findViewById<RecyclerView>(R.id.rv_vehicles_list).run {
            layoutManager = LinearLayoutManager(this@LegalSelectVehicleActivity)
            adapter = <EMAIL>
            addItemDecoration(
                DividerItemDecoration(this@LegalSelectVehicleActivity, LinearLayout.VERTICAL),
            )
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) onBackPressed()
        return true
    }

    private fun onActivityNewResult(result: ActivityResult) {
        if (result.resultCode == Activity.RESULT_OK) {
            if (result.data != null && result.data!!.getBooleanExtra("updated", false)) {
                presenter.getVehiclesList()
            }
        }
    }

    override fun onItemClick(vehicle: VehicleInfo) {
        selectedVehicle = vehicle
        if (vehicle.isNonConnectedVehicle) {
            launchEditConsent()
        } else {
            masterDataConsentViewModel.fetchReConsentEligibility(vehicle)
        }
    }

    private fun launchEditConsent() {
        val intent = IntentUtil.getEditConsentIntent(this, selectedVehicle, regionManager)
        vehicleResult.launch(intent)
    }
}
