package com.toyota.oneapp.ui.accountsettings.helpandfeedback.vehiclesupport

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentVehicleSupportBinding
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.dealerservice.domain.model.Dealer
import com.toyota.oneapp.features.dealerservice.domain.model.PREFERRED_DEALER_DATA_KEY
import com.toyota.oneapp.features.dealerservice.domain.model.PREFERRED_DEALER_KEY
import com.toyota.oneapp.features.find.domain.model.toServiceDealer
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.vehiclesupport.VehicleSupportViewModel.NavigationEvent.NavigateToScheduleMaintenanceScreen
import com.toyota.oneapp.ui.flutter.DashboardFlutterActivity
import com.toyota.oneapp.ui.flutter.GO_TO_MAINTENANCE_SCHEDULE
import com.toyota.oneapp.ui.garage.FAQActivity
import com.toyota.oneapp.ui.garage.OwnerManualActivity
import com.toyota.oneapp.ui.newdashboard.VehicleHealthReportActivity
import com.toyota.oneapp.ui.scheduledmaint.maintenancetimeline.MaintenanceTimelineActivity
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.ToyotaConstants.Companion.DASHBOARD_ROUTE
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VehicleSupportFragment :
    BaseViewModelFragment(),
    VehicleSupportAdapter.OnItemClickListener {
    private val args: VehicleSupportFragmentArgs by navArgs()

    private val viewModel: VehicleSupportViewModel by viewModels()

    private lateinit var binding: FragmentVehicleSupportBinding
    private lateinit var adapter: VehicleSupportAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentVehicleSupportBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        initializeViews()
        setUpViewModelBindings()

        return binding.root
    }

    // On Item Clicked.
    override fun onItemClick(option: VehicleSupportOptionUIModel) {
        viewModel.onVehicleSupportOptionClicked(option)
    }

    private fun initializeViews() {
        adapter = VehicleSupportAdapter(emptyList(), this)
        binding.rvVehicleSupport.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            addItemDecoration(DividerItemDecoration(requireContext(), LinearLayout.VERTICAL))
        }

        DataBindingAdapters.loadImage(
            binding.ivCarImage,
            viewModel.vehicle.image,
            resources.getDrawable(
                R.drawable.car_placeholder,
            ),
        )

        binding.tvCarDetails.text = "${viewModel.vehicle.modelYear} ${viewModel.vehicle.modelDescription}"
        binding.tvVin.text = viewModel.vehicle.vin

        viewModel.vehicleSupportOptions.observe(viewLifecycleOwner) { data ->
            DataBindingAdapters.setRecyclerViewAdapterData(binding.rvVehicleSupport, data, emptyList())
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.vehicleSupportNavigationEvents.observe(viewLifecycleOwner) {
            handleVehicleSupportNavigationEvents(it)
        }
    }

    private fun handleVehicleSupportNavigationEvents(navigationEvent: VehicleSupportViewModel.NavigationEvent) {
        when (navigationEvent) {
            is VehicleSupportViewModel.NavigationEvent.NavigateToVehicleHealthReportScreen -> {
                vehicleHealthReportActivityResultLauncher.launch(
                    VehicleHealthReportActivity.getIntent(
                        requireContext(),
                        navigationEvent.vehicle,
                    ),
                )
            }

            is VehicleSupportViewModel.NavigationEvent.NavigateToManualsAndWarrantiesScreen -> {
                val response = navigationEvent.ownersManualResponse
                val intent =
                    OwnerManualActivity.getIntent(
                        requireContext(),
                        response.payload.documents,
                        response.payload,
                    )
                startActivity(intent)
            }

            is VehicleSupportViewModel.NavigationEvent.NavigateToMaintenanceTimelineScreen -> {
                val intent =
                    MaintenanceTimelineActivity.getIntent(
                        requireContext(),
                        navigationEvent.vehicle,
                    )
                startActivity(intent)
            }

            is VehicleSupportViewModel.NavigationEvent.NavigateToAddMileageScreen -> {
                val data = HashMap<String, String?>()
                val bundle = Bundle()

                if (navigationEvent.telemetryPayload?.value == null) {
                    data["odometerValue"] = ToyotaConstants.EMPTY_STRING
                    data["odometerUnit"] = ToyotaConstants.EMPTY_STRING
                } else {
                    data["odometerValue"] = navigationEvent.telemetryPayload.value.toString()
                    data["odometerUnit"] = navigationEvent.telemetryPayload.unit
                }
                data["vin"] = navigationEvent.vehicle.vin

                bundle.putSerializable(ToyotaConstants.FLUTTER_DATA, data)

                requireContext().startActivity(
                    DashboardFlutterActivity.createIntent(
                        requireContext(),
                        bundle,
                        GO_TO_MAINTENANCE_SCHEDULE,
                    ),
                )
            }

            is NavigateToScheduleMaintenanceScreen -> {
                context?.let {
                    startOADashboardActivityWithDeepLink(
                        context = it,
                        navigationEvent = navigationEvent,
                    )
                }
            }

            is VehicleSupportViewModel.NavigationEvent.NavigateToFAQScreen -> {
                val intent = FAQActivity.getIntent(requireContext(), args.vehicle)
                startActivity(intent)
            }
        }
    }

    private fun startOADashboardActivityWithDeepLink(
        context: Context,
        navigationEvent: NavigateToScheduleMaintenanceScreen,
    ) {
        val intent =
            Intent(context, OADashboardActivity::class.java).apply {
                putExtra(DASHBOARD_ROUTE, Constants.DEEP_LINK_PREFERRED_DEALER)
                putExtra(PREFERRED_DEALER_DATA_KEY, navigationEvent.dealer?.toServiceDealer()?.toBundle())
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
        context.startActivity(intent)
    }

    private val vehicleHealthReportActivityResultLauncher =
        registerForActivityResult<Intent, ActivityResult>(
            ActivityResultContracts.StartActivityForResult(),
            ActivityResultCallback { result: ActivityResult ->
                if (result.resultCode == Activity.RESULT_OK) {
                    activity?.setResult(Activity.RESULT_OK)
                    activity?.finish()
                }
            },
        )
}

fun Dealer.toBundle() = Bundle().also { it.putSerializable(PREFERRED_DEALER_KEY, this) }

@Suppress("DEPRECATION")
fun Bundle.toServiceDealer() =
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        getSerializable(PREFERRED_DEALER_KEY, Dealer::class.java)
    } else {
        getSerializable(PREFERRED_DEALER_KEY) as? Dealer?
    }
