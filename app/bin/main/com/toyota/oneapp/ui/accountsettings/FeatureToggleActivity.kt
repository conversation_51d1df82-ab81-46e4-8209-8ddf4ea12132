package com.toyota.oneapp.ui.accountsettings

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.app.AppFeatureFlags
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityFeatureToggleBinding
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.IntentUtil
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.recyclerview.decorator.SectionItemDecorator
import javax.inject.Inject

@AndroidEntryPoint
class FeatureToggleActivity : UiBaseActivity() {
    private lateinit var binding: ActivityFeatureToggleBinding

    @Inject
    lateinit var appFeatureFlags: AppFeatureFlags

    @Inject
    lateinit var applicationData: ApplicationData

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        if (!BuildConfig.DEBUG) {
            // This activity should not be opened in Release build
            finish()
        }

        val featureToggleAdapter = FeatureToggleAdapter(Feature.getFeatureList(), appFeatureFlags)
        binding =
            DataBindingUtil.setContentView(
                this,
                R.layout.activity_feature_toggle,
            )

        binding.recyclerview.run {
            layoutManager = LinearLayoutManager(context)
            adapter = featureToggleAdapter
            addItemDecoration(SectionItemDecorator(context))
        }

        binding.resetButton.setOnClickListener {
            applicationData.getSelectedVehicle()?.let { it1 -> appFeatureFlags.reset(it1.vin) }
            runOnUiThread(Runnable { featureToggleAdapter.notifyDataSetChanged() })
        }

        binding.toolbar.setNavigationOnClickListener {
            onBackPressed()
        }
    }

    override fun onBackPressed() {
        startActivity(IntentUtil.getOADashBoardIntent(context = this, isDashboardRefresh = true))
        finish()
    }
}
