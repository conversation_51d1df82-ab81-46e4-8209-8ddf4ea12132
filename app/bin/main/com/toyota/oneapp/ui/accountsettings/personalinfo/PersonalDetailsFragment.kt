package com.toyota.oneapp.ui.accountsettings.personalinfo

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.FragmentPersonalDetailsBinding
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.ui.accountsettings.AccountSettingsFragment
import com.toyota.oneapp.ui.accountsettings.UpdateEmailPhoneActivity
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.widget.validator.NonBlankValidator
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class PersonalDetailsFragment : BaseDataBindingFragment<FragmentPersonalDetailsBinding>() {
    private val viewModel: PersonalDetailsViewModel by viewModels()

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @Inject
    lateinit var regionManager: RegionManager

    private var accountInfo: AccountInfoSubscriber? = null

    private lateinit var profileName: String

    private lateinit var mBinding: FragmentPersonalDetailsBinding

    private lateinit var firstNameValidator: NonBlankValidator
    private lateinit var lastNameValidator: NonBlankValidator

    private lateinit var profileNameValidator: NonBlankValidator

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentPersonalDetailsBinding,
        savedInstance: Bundle?,
    ) {
        mBinding = binding
        initViewModelObserver(binding)
    }

    private fun initViewModelObserver(binding: FragmentPersonalDetailsBinding) {
        populateFields()

        initView(binding)

        viewModel.run {
            successUpdateAccount.observe(viewLifecycleOwner) {
                // close the activity
                requireActivity().setResult(Activity.RESULT_OK)
                requireActivity().finish()
            }

            onBackButtonClicked.observe(viewLifecycleOwner) {
                findNavController().popBackStack()
            }

            personalDetailSaveBtnClicked.observe(viewLifecycleOwner) {
                analyticsLogger.logEventWithParameter(
                    AnalyticsEventParam.PROFILE_INFO_SAVE,
                    AnalyticsEventParam.PROFILE_UPDATE_NAME,
                )
                viewModel.savePersonalInfo(
                    binding.etProfileName.text.toString(),
                    binding.etFirstName.text.toString(),
                    binding.etLastName.text.toString(),
                )
            }
        }
    }

    private fun populateFields() {
        accountInfo = requireActivity().intent.getParcelableExtra(ToyotaConstants.ACCOUNT_INFO)
        // As Profile name is lateinit variable , So variable to initialize irrespective of Account Info to avoid app crash
        profileName = requireActivity().intent.getStringExtra(
            AccountSettingsFragment.PROFILE_NAME_KEY,
        ) ?: buildProfileName()
        if (accountInfo == null) {
            requireActivity().finish()
            return
        }
    }

    private fun buildProfileName(): String =
        "${accountInfo?.firstName ?: viewModel.getUserFirstName()} ${accountInfo?.lastName ?: viewModel.getUserLastName()}"

    @SuppressLint("ClickableViewAccessibility")
    private fun initView(binding: FragmentPersonalDetailsBinding) {
        binding.etFirstName.run {
            addTextChangedListener(mWatcher)
            DataBindingAdapters.setRegexFilter(this, getString(R.string.first_name_regex))
        }

        binding.etLastName.run {
            addTextChangedListener(mWatcher)
            DataBindingAdapters.setRegexFilter(this, getString(R.string.last_name_regex))
        }

        binding.tilEmailAddress.setOnKeyListener(null)
        binding.etEmailAddress.run {
            setOnTouchListener(unableEditClickListener)
            addTextChangedListener(mWatcher)
        }

        binding.tilPhoneNumber.setOnKeyListener(null)
        binding.etPhoneNumber.run {
            setOnTouchListener(unableEditClickListener)
            addTextChangedListener(mWatcher)
        }

        binding.etProfileName.addTextChangedListener(mWatcher)

        binding.personalDetailSaveBtn.setOnClickListener {
            viewModel.personalDetailSaveBtnClicked()
        }

        showAccountInfo(
            accountInfo?.firstName ?: viewModel.getUserFirstName(),
            accountInfo?.lastName ?: viewModel.getUserLastName(),
            accountInfo?.customerEmails?.get(0)?.emailAddress,
            accountInfo?.customerPhoneNumbers?.firstOrNull()?.phoneNumber,
            binding,
        )

        firstNameValidator =
            NonBlankValidator(
                binding.tilFirstName,
                binding.etFirstName,
                R.string.AccountSettings_name_cant_be_blank,
            ).also {
                binding.etFirstName.addTextChangedListener(it)
                binding.etFirstName.onFocusChangeListener = it
            }
        lastNameValidator =
            NonBlankValidator(
                binding.tilLastName,
                binding.etLastName,
                R.string.AccountSettings_name_cant_be_blank,
            ).also {
                viewDataBinding.etLastName.addTextChangedListener(it)
                viewDataBinding.etLastName.onFocusChangeListener = it
            }
        profileNameValidator =
            NonBlankValidator(
                binding.tilProfileName,
                binding.etProfileName,
                R.string.AccountSettings_profile_name_cant_be_blank,
            ).also {
                binding.etProfileName.addTextChangedListener(it)
                binding.etProfileName.onFocusChangeListener = it
            }

        binding.personalInfoToolbar.setOnClickListener {
            viewModel.onBackButtonClicked()
        }
    }

    private fun showAccountInfo(
        firstName: String?,
        lastName: String?,
        email: String?,
        phoneNumber: String?,
        binding: FragmentPersonalDetailsBinding,
    ) {
        if (firstName != null) {
            binding.etFirstName.setText(firstName)
        }
        if (lastName != null) {
            binding.etLastName.setText(lastName)
        }
        if (email != null) {
            binding.etEmailAddress.setText(email)
        }
        if (phoneNumber != null) {
            binding.etPhoneNumber.setText(phoneNumber)
        }

        binding.etProfileName.setText(profileName)
    }

    @SuppressLint("ClickableViewAccessibility")
    private val unableEditClickListener =
        View.OnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                when (v.id) {
                    R.id.et_phone_number -> {
                        analyticsLogger.logEventWithParameter(
                            AnalyticsEventParam.PROFILE_INFO_SAVE,
                            AnalyticsEventParam.PHONE,
                        )
                        startActivity(
                            Intent(requireContext(), UpdateEmailPhoneActivity::class.java)
                                .putExtra(ToyotaConstants.TYPE, 0)
                                .putExtra(ToyotaConstants.ACCOUNT_INFO, accountInfo),
                        )
                    }

                    R.id.et_email_address -> {
                        analyticsLogger.logEventWithParameter(
                            AnalyticsEventParam.PROFILE_INFO_SAVE,
                            AnalyticsEventParam.EMAIL,
                        )
                        startActivity(
                            Intent(requireContext(), UpdateEmailPhoneActivity::class.java)
                                .putExtra(ToyotaConstants.TYPE, 1)
                                .putExtra(ToyotaConstants.ACCOUNT_INFO, accountInfo),
                        )
                    }

                    else -> {
                    }
                }
            }
            false
        }

    fun invalidateValidation() {
        if (mBinding.etFirstName.text
                .toString()
                .isEmpty() ||
            mBinding.etLastName.text
                .toString()
                .isEmpty() ||
            mBinding.etEmailAddress.text
                .toString()
                .isEmpty() ||
            mBinding.etPhoneNumber.text
                .toString()
                .isEmpty() ||
            mBinding.etProfileName.text
                .toString()
                .isEmpty()
        ) {
            mBinding.personalDetailSaveBtn.isEnabled = false
        } else {
            mBinding.personalDetailSaveBtn.isEnabled =
                mBinding.etFirstName.text.toString() != accountInfo?.firstName ||
                mBinding.etLastName.text.toString() != accountInfo?.lastName ||
                mBinding.etEmailAddress.text.toString() !=
                accountInfo?.customerEmails
                    ?.get(
                        0,
                    )?.emailAddress ||
                mBinding.etPhoneNumber.text.toString() != accountInfo?.customerPhoneNumbers?.firstOrNull()?.phoneNumber ||
                mBinding.etProfileName.text.toString() != profileName
        }
    }

    private var mWatcher: TextWatcher =
        object : TextWatcher {
            override fun onTextChanged(
                s: CharSequence?,
                start: Int,
                before: Int,
                count: Int,
            ) {
                invalidateValidation()
            }

            override fun beforeTextChanged(
                s: CharSequence?,
                start: Int,
                count: Int,
                after: Int,
            ) {
                // TODO Auto-generated method stub
            }

            override fun afterTextChanged(s: Editable?) {
                // TODO Auto-generated method stub
            }
        }

    override fun getLayout(): Int = R.layout.fragment_personal_details
}
