package com.toyota.oneapp.ui.accountsettings.helpandfeedback.selectvehicle

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemSelectVehicleBinding
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters

class SelectVehicleAdapter(
    private var vehicles: List<VehicleInfo>,
    private val onItemClickListener: OnItemClickListener,
) : RecyclerView.Adapter<SelectVehicleAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<VehicleInfo> {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding =
            ItemSelectVehicleBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.onBind(vehicles[position])
    }

    override fun getItemCount(): Int = vehicles.size

    // BindableRecyclerViewAdapter methods
    override fun setData(data: List<VehicleInfo>?) {
        this.vehicles = data ?: emptyList()
        notifyDataSetChanged()
    }

    inner class ViewHolder(
        private val binding: ItemSelectVehicleBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        init {
            binding.root.setOnClickListener {
                onItemClickListener.onItemClick(vehicles[adapterPosition])
            }
        }

        fun onBind(vehicle: VehicleInfo) {
            DataBindingAdapters.loadImage(
                binding.ivCarImage,
                vehicle.image,
                binding.root.context.resources.getDrawable(
                    R.drawable.car_placeholder,
                ),
            )
            binding.tvCarDetails.text = "${vehicle.modelYear} ${vehicle.modelDescription}"
            binding.tvVin.text = vehicle.vin
        }
    }

    interface OnItemClickListener {
        fun onItemClick(vehicle: VehicleInfo)
    }
}
