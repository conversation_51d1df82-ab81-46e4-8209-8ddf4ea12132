package com.toyota.oneapp.ui.accountsettings.helpandfeedback.submitfeedback

import androidx.lifecycle.*
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.app.BuildVersionProvider
import com.toyota.oneapp.model.account.helpandfeedback.*
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.api.manager.HelpAndFeedbackAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class SubmitFeedbackViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val helpAndFeedbackManager: HelpAndFeedbackAPIManager,
        private val preferenceModel: OneAppPreferenceModel,
        private val buildVersionProvider: BuildVersionProvider,
    ) : BaseViewModel() {
        companion object {
            private const val APP_FEATURE = "feedback"
            private const val DEVICE_PLATFORM = "ANDROID"
        }

        sealed class NavigationEvent {
            data class NavigateToSelectFeedbackTypeScreen(
                val types: List<FeedbackType>,
                val selectedType: FeedbackType?,
            ) : NavigationEvent()

            data class NavigateToSelectFeedbackCategoryScreen(
                val categories: List<FeedbackCategory>,
                val selectedCategory: FeedbackCategory?,
            ) : NavigationEvent()

            object NavigateToSubmitFeedbackSuccessScreen : NavigationEvent()
        }

        private val mSelectedType = MutableLiveData<FeedbackType?>()
        private val mSelectedCategory = MutableLiveData<FeedbackCategory?>()
        val feedbackMessage = MutableLiveData<String>()
        private val mSubmitFeedbackEnabled =
            MediatorLiveData<Boolean>().apply {
                addSource(
                    mSelectedType,
                    Observer {
                        value = isSubmitFeedbackEnabled(it, mSelectedCategory.value, feedbackMessage.value)
                    },
                )
                addSource(
                    mSelectedCategory,
                    Observer {
                        value = isSubmitFeedbackEnabled(mSelectedType.value, it, feedbackMessage.value)
                    },
                )
                addSource(
                    feedbackMessage,
                    Observer {
                        value = isSubmitFeedbackEnabled(mSelectedType.value, mSelectedCategory.value, it)
                    },
                )
            }
        private val mSubmitFeedbackNavigationEvent = SingleLiveEvent<NavigationEvent>()

        val selectedType: LiveData<FeedbackType?>
            get() = mSelectedType
        val selectedCategory: LiveData<FeedbackCategory?>
            get() = mSelectedCategory
        val submitFeedbackEnabled: LiveData<Boolean>
            get() = mSubmitFeedbackEnabled
        val submitFeedbackNavigationEvent: LiveData<NavigationEvent>
            get() = mSubmitFeedbackNavigationEvent

        private var metadata: FeedbackMetadataPayload? = null

        private var vehicle: VehicleInfo = VehicleInfo()

        init {
            vehicle = state["vehicle"] ?: VehicleInfo()
            feedbackMessage.value = ""
            mSubmitFeedbackEnabled.value = false
        }

        private fun isSubmitFeedbackEnabled(
            feedbackType: FeedbackType?,
            category: FeedbackCategory?,
            message: String?,
        ): Boolean = (feedbackType != null && category != null && message != null && message.isNotEmpty())

        fun onSelectTypeClicked() {
            getFeedbackMetadata {
                mSubmitFeedbackNavigationEvent.value =
                    NavigationEvent.NavigateToSelectFeedbackTypeScreen(
                        it.feedbackTypes,
                        mSelectedType.value,
                    )
            }
        }

        fun onSelectCategoryClicked() {
            getFeedbackMetadata {
                mSubmitFeedbackNavigationEvent.value =
                    NavigationEvent.NavigateToSelectFeedbackCategoryScreen(
                        it.categories,
                        mSelectedCategory.value,
                    )
            }
        }

        private fun getFeedbackMetadata(callback: (metadata: FeedbackMetadataPayload) -> Unit) =
            if (metadata != null) {
                callback(metadata!!)
            } else {
                showProgress()
                val cvFlag: Boolean = (vehicle.generation != null)
                helpAndFeedbackManager.getFeedbackMetadata(
                    cvFlag = cvFlag,
                    brand = vehicle.brand,
                    region = vehicle.region,
                    generation = vehicle.generation ?: "",
                    callback =
                        object : BaseCallback<FeedbackMetadataResponse>() {
                            override fun onSuccess(response: FeedbackMetadataResponse) {
                                metadata = response.payload
                                if (metadata != null) {
                                    callback(metadata!!)
                                } else {
                                    showErrorMessage(R.string.generic_error)
                                }
                            }

                            override fun onFailError(
                                httpCode: Int,
                                errorMsg: String?,
                            ) {
                                showErrorMessage(errorMsg)
                            }

                            override fun onComplete() {
                                hideProgress()
                            }
                        },
                )
            }

        fun onTypeSelected(selectedFeedbackType: FeedbackType) {
            mSelectedType.value = selectedFeedbackType
        }

        fun onCategorySelected(selectedCategory: FeedbackCategory) {
            mSelectedCategory.value = selectedCategory
        }

        fun onFeedbackMessageUpdated(message: CharSequence) {
            feedbackMessage.value = message.toString()
        }

        fun onSubmitFeedbackClicked() {
            showProgress()
            helpAndFeedbackManager.submitFeedbackV2(
                brand = vehicle.brand,
                submitFeedbackV2Request = getSubmitFeedbackRequest(),
                callback =
                    object : BaseCallback<BaseResponse>() {
                        override fun onSuccess(response: BaseResponse) {
                            mSubmitFeedbackNavigationEvent.value = NavigationEvent.NavigateToSubmitFeedbackSuccessScreen
                        }

                        override fun onFailError(
                            httpCode: Int,
                            errorMsg: String?,
                        ) {
                            showErrorMessage(errorMsg)
                        }

                        override fun onComplete() {
                            hideProgress()
                        }
                    },
            )
        }

        private fun getSubmitFeedbackRequest(): SubmitFeedbackV2Request =
            SubmitFeedbackV2Request(
                guid = preferenceModel.getGuid(),
                vin = vehicle.vin,
                feedbackType = selectedType.value!!.feedbackType,
                category = selectedCategory.value!!.categoryName,
                feedbackMessage = feedbackMessage.value!!,
                appFeature = APP_FEATURE,
                devicePlatform = DEVICE_PLATFORM,
                osVersion = buildVersionProvider.osVersion(),
                appVersion = BuildConfig.VERSION_NAME,
                deviceMode = BuildConfig.BUILD_TYPE,
            )
    }
