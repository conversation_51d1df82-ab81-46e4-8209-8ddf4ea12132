package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.NotificationSettingsEvent
import com.toyota.oneapp.databinding.ActivityNotificationSettingsBinding
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.ui.BaseActivity
import com.toyota.oneapp.ui.accountsettings.personalinfo.NewPersonalInfoActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

@AndroidEntryPoint
class NotificationSettingsActivity : BaseActivity() {
    private val viewModel: NotificationSettingsViewModel by viewModels()
    private val accountSettingsViewModel: AccountSettingsViewModel by viewModels()

    private val personalInfoRequestContract =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                accountSettingsViewModel.updateAccountInfo()
            }
        }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        val binding: ActivityNotificationSettingsBinding =
            DataBindingUtil.setContentView(
                this,
                R.layout.activity_notification_settings,
            )
        binding.lifecycleOwner = this
        binding.viewModel = viewModel

        findViewById<Toolbar>(R.id.np_toolbar).setNavigationOnClickListener {
            viewModel.sendNotificationsUpdate(true)
        }

        observeBaseEvents(viewModel)
        observeViewModelEvents()
    }

    private fun observeViewModelEvents() {
        viewModel.events.observe(this) { event ->
            when (event) {
                is NotificationSettingsEvent.UpdatePhoneNumber -> {
                    event.profileData?.accountSubscriber?.let {
                        showDialogAndNavigateToPersonalInfo(it)
                    }
                }
                is NotificationSettingsEvent.SignOut -> {
                    finish()
                    (this.activityContext as? OADashboardActivity)?.signOut()
                }

                is NotificationSettingsEvent.ShowSignOutDialog -> {
                    val signOutDialogData =
                        DialogData(
                            title = getString(R.string.phoneNoVerificationRequired),
                            message = getString(R.string.signOutAndLoginWithPhone),
                            positiveCTA = getString(R.string.phoneNumberVerificationSignOut),
                            negativeCTA = getString(R.string.cancel),
                        )
                    showDialog(signOutDialogData, viewModel::signOut)
                }
            }
        }
    }

    private fun showDialogAndNavigateToPersonalInfo(it: AccountInfoSubscriber) {
        val firstName = it.firstName ?: ToyotaConstants.EMPTY_STRING
        val lastName = it.lastName ?: ToyotaConstants.EMPTY_STRING
        val intent =
            Intent(this.activityContext, NewPersonalInfoActivity::class.java).apply {
                putExtra(ToyotaConstants.NAVIGATE_TO_PERSONAL_DETAIL, true)
                putExtra(ToyotaConstants.ACCOUNT_INFO, it)
                putExtra(AccountSettingsFragment.PROFILE_NAME_KEY, "$firstName $lastName")
            }
        val personalInfoDialogData =
            DialogData(
                title = getString(R.string.phoneNoRequired),
                message = getString(R.string.addPhoneNumber),
                positiveCTA = getString(R.string.accountSettings),
                negativeCTA = getString(R.string.cancel),
            )
        showDialog(personalInfoDialogData) {
            personalInfoRequestContract.launch(intent)
        }
    }

    private fun showDialog(
        data: DialogData,
        onConfirmClick: () -> Unit,
    ) {
        DialogUtil.showDialog(
            this,
            data.title,
            data.message,
            data.positiveCTA,
            data.negativeCTA,
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    onConfirmClick()
                }

                override fun onCancelClick() {
                    // Do nothing
                }
            },
            false,
        )
    }

    override fun onBackPressed() {
        viewModel.sendNotificationsUpdate(true)
    }
}

data class DialogData(
    val title: String,
    val message: String,
    val positiveCTA: String,
    val negativeCTA: String,
)
