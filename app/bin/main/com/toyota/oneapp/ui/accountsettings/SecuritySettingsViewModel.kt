/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.ui.accountsettings

import android.content.Context
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.model.account.PinCheckRequest
import com.toyota.oneapp.network.HttpCode
import com.toyota.oneapp.network.api.repository.PinRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.BiometryUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import org.forgerock.android.auth.ui.FRNative
import org.forgerock.android.auth.ui.FRNativeResponse
import org.forgerock.android.auth.ui.FRNativeResultListener
import org.forgerock.android.auth.ui.entity.Constants
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
open class SecuritySettingsViewModel
    @Inject
    constructor(
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val pinRepository: PinRepository? = null,
    ) : BaseViewModel() {
        var isToyotaApp = ObservableBoolean(false)
        var isSecuritySettingsEnabled = ObservableBoolean(false)
        var isBiometricEnabled = ObservableBoolean(false)
        val isKeepMeSignInBioEnabled = ObservableBoolean(false)
        val isDigitalkeyPushEnabled = ObservableBoolean(false)
        val securityTimeText = MutableLiveData<String?>()
        var isSystemBiometricPresent = false
        var isDigitalKeyPushFeatureVisible = ObservableBoolean(false)

        private val mCheckForPin = MutableLiveData<Boolean?>()
        val checkForPin: LiveData<Boolean?> get() = mCheckForPin

        val mSendToResetPin = MutableLiveData<Boolean>()
        val sendToResetPin: LiveData<Boolean> get() = mSendToResetPin

        val mOnUserAuthError = MutableLiveData<Boolean>()
        val onUserAuthError: LiveData<Boolean> get() = mOnUserAuthError

        val securitySettingsViewModelNavigationEvent = SingleLiveEvent<SecuritySettingsViewModelNavigationEvent>()

        init {
            isToyotaApp.set(BuildConfig.IS_TOYOTA_APP)
            isSecuritySettingsEnabled.set(BuildConfig.SECURITY_SETTINGS_ENABLED)
            isDigitalKeyPushFeatureVisible.set(BuildConfig.DK_PULL_MECHANISM_ENABLED)
        }

        fun onInit(context: Context) {
            isKeepMeSignInBioEnabled.set(oneAppPreferenceModel.isKeepMeLogin())
            isBiometricEnabled.set(oneAppPreferenceModel.isBiometricEnabled())
            isDigitalkeyPushEnabled.set(oneAppPreferenceModel.isDigitalKeyPushEnabled())
            securityTimeText.postValue(getSharePreferenceData(context))
            isSystemBiometricPresent = BiometryUtil.canAuthenticate(context)
        }

        fun onKeepMeSignInBioSettingChanged(checked: Boolean) {
            oneAppPreferenceModel.setKeepMeLogin(
                if (checked) oneAppPreferenceModel.getGuid() else ToyotaConstants.EMPTY_STRING,
            )
            isKeepMeSignInBioEnabled.set(oneAppPreferenceModel.isKeepMeLogin())
        }

        fun onDigitalKeyPushEnabledUpdated(checked: Boolean) {
            oneAppPreferenceModel.setDigitalKeyPushEnabled(checked)
            isDigitalkeyPushEnabled.set(oneAppPreferenceModel.isDigitalKeyPushEnabled())
        }

        fun doSignIn(context: Context) {
            // Pin reset Logic need to maintain old user instance , So not calling logout
            FRNative.pushTokenId = oneAppPreferenceModel.getDeviceToken()
            FRNative.biometricSupport = BiometryUtil.canAuthenticate(context)
            FRNative.setCurrentUserPersist(true)
            FRNative.login(context, frNativeResultListener, forceLogin = true)
        }

        private val frNativeResultListener: FRNativeResultListener =
            object : FRNativeResultListener {
                override fun onResult(result: FRNativeResponse) {
                    if (result.success && result.accessToken != null) {
                        IDPData.instance?.handleAccessToken(result.accessToken!!, oneAppPreferenceModel)
                        mSendToResetPin.postValue(true)
                    } else if (Constants.userAuthError == result.message) {
                        mOnUserAuthError.postValue(true)
                    }
                }
            }

        fun onBiometricSwitchChanged(checked: Boolean) {
            if (isSystemBiometricPresent) {
                oneAppPreferenceModel.setBiometricEnabled(checked)
                isBiometricEnabled.set(oneAppPreferenceModel.isBiometricEnabled())
                securitySettingsViewModelNavigationEvent.postValue(
                    SecuritySettingsViewModelNavigationEvent.RequireAuthColor(checked),
                )
            } else {
                securitySettingsViewModelNavigationEvent.postValue(
                    SecuritySettingsViewModelNavigationEvent.ShowBiometricNotEnabledDialog,
                )
            }
        }

        fun onRequiredAuthClicked() {
            if (isSystemBiometricPresent) {
                securitySettingsViewModelNavigationEvent.postValue(
                    SecuritySettingsViewModelNavigationEvent.RequiredAuth,
                )
            } else {
                securitySettingsViewModelNavigationEvent.postValue(
                    SecuritySettingsViewModelNavigationEvent.ShowBiometricNotEnabledDialog,
                )
            }
        }

        fun getSharePreferenceSessionTime(): String = oneAppPreferenceModel.getBiometricSettingsTime()

        fun getSharePreferenceData(context: Context): String =
            when (oneAppPreferenceModel.getBiometricSettingsTime()) {
                SecuritySettingsExpActivity.IMMEDIATE -> context.getString(R.string.Immediate)
                SecuritySettingsExpActivity.ONE_MIN -> context.getString(R.string.One_min)
                SecuritySettingsExpActivity.FIVE_MIN -> context.getString(R.string.Five_min)
                SecuritySettingsExpActivity.FIFTEEN_MIN -> context.getString(R.string.Fifteen_min)
                SecuritySettingsExpActivity.ONE_HOUR -> context.getString(R.string.One_hour)
                SecuritySettingsExpActivity.SIX_HOUR -> context.getString(R.string.Six_hour)
                SecuritySettingsExpActivity.TWELVE_HOUR -> context.getString(R.string.Twelve_hour)
                else -> context.getString(R.string.Fifteen_min)
            }

        fun setSessionTime(string: String) {
            oneAppPreferenceModel.setBiometricSettingsTime(string)
        }

        fun checkForPIN() {
            pinRepository ?: return
            val pinCheckRequest = PinCheckRequest(oneAppPreferenceModel.getGuid())
            viewModelScope.launch {
                showProgress()
                val resource = pinRepository.sendPinCheckRequest(pinCheckRequest)
                hideProgress()
                mCheckForPin.postValue(
                    !(
                        resource.error?.code == HttpCode.NOT_FOUND.code ||
                            false == resource.data?.result
                    ),
                )
            }
        }
    }

sealed class SecuritySettingsViewModelNavigationEvent {
    object RequiredAuth : SecuritySettingsViewModelNavigationEvent()

    data class RequireAuthColor(
        val checked: Boolean,
    ) : SecuritySettingsViewModelNavigationEvent()

    object ShowBiometricNotEnabledDialog : SecuritySettingsViewModelNavigationEvent()
}
