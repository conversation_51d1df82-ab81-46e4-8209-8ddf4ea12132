package com.toyota.oneapp.ui.accountsettings.helpandfeedback.appfaq

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.account.helpandfeedback.FaqsResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.api.manager.HelpAndFeedbackAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.appfaq.mapper.FaqUIMapper
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.appfaq.model.FaqUIModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class AppFaqViewModel
    @Inject
    constructor(
        private val state: SavedStateHandle,
        private val applicationData: ApplicationData,
        private val languageManager: LanguageManager,
        private val helpAndFeedbackManager: HelpAndFeedbackAPIManager,
        private val mapper: FaqUIMapper,
    ) : BaseViewModel() {
        private val mFaqUIModels = MutableLiveData<List<FaqUIModel>>()
        private var vehicle: VehicleInfo? = null

        val faqUIModels: LiveData<List<FaqUIModel>>
            get() = mFaqUIModels

        init {
            vehicle = state["vehicle"]
            fetchFaqs()
        }

        private fun fetchFaqs() {
            showProgress()
            val cvFlag: Boolean = if (vehicle != null) vehicle!!.generation != null else false
            helpAndFeedbackManager.getFaqs(
                cvFlag = cvFlag,
                hasVehicle = applicationData.getVehicleList()?.isNotEmpty() == true,
                brand = vehicle?.brand ?: BuildConfig.APP_BRAND,
                region = vehicle?.region ?: languageManager.getCurrentRegion().regionCode,
                generation = vehicle?.generation.orEmpty(),
                callback =
                    object : BaseCallback<FaqsResponse>() {
                        override fun onSuccess(response: FaqsResponse) {
                            if (response.payload != null) {
                                mFaqUIModels.value = mapper.mapToUI(response.payload.faqs)
                            } else {
                                showErrorMessage(R.string.generic_error)
                            }
                        }

                        override fun onError() {
                            showErrorMessage(R.string.generic_error)
                        }

                        override fun onComplete() {
                            hideProgress()
                        }
                    },
            )
        }
    }
