/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.ui.accountsettings.legalinfo

import android.os.Bundle
import androidx.activity.viewModels
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityLegalInfoBinding
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import com.toyota.oneapp.util.Brand
import com.toyota.oneapp.util.ToyUtil
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class LegalInfoActivity : DataBindingBaseActivity<ActivityLegalInfoBinding>() {
    private val viewModel: LegalInfoViewModel by viewModels()

    override fun getLayoutId(): Int = R.layout.activity_legal_info

    override fun initViews(savedInstance: Bundle?) {
        binding.legalInfoViewModel = viewModel
        performActivitySetup(binding.legalTermsToolbar)

        viewModel.observableLegalItemClick.observe(
            this,
        ) {
            ToyUtil.openCustomChromeTab(
                this,
                ToyUtil.getAccountTerms(if (ToyUtil.isSubaru()) Brand.TOYOTA.appBrand else BuildConfig.APP_BRAND),
            )
        }

        viewModel.observablePrivacyNoticeClick.observe(
            this,
        ) {
            ToyUtil.openCustomChromeTab(
                this,
                ToyUtil.getAccountPrivacy(if (ToyUtil.isSubaru()) Brand.TOYOTA.appBrand else BuildConfig.APP_BRAND),
            )
        }

        viewModel.observableSubaruPrivacyNoticeClick.observe(this) {
            ToyUtil.openCustomChromeTab(this, ToyUtil.getAccountPrivacy(Brand.SUBARU.appBrand))
        }
    }
}
