package com.toyota.oneapp.ui.accountsettings

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class DeleteAccountViewModel
    @Inject
    constructor(
        @SuppressLint("StaticFieldLeak") private val context: Context,
        private val accountApiManager: AccountAPIManager,
        private val preferenceModel: OneAppPreferenceModel,
        val languageManager: LanguageManager,
    ) : BaseViewModel() {
        val state: MutableLiveData<State> = MutableLiveData()
        val onDeleteAccountNavigationEvents = SingleLiveEvent<DeleteAccountNavigationEvent>()

        init {
            state.postValue(
                if (AppLanguageUtils.getCurrentLocale().equals(AppLanguageUtils.US_ENGLISH)) {
                    State(
                        showUSRegionContent = true,
                        showNonUSRegionContent = false,
                        appBarTitle = context.getString(R.string.delete_account_manage_your_data),
                    )
                } else {
                    State(showUSRegionContent = false, showNonUSRegionContent = true, appBarTitle = "")
                },
            )
        }

        fun onDoNotSellMyInformation() {
            onDeleteAccountNavigationEvents.postValue(
                DeleteAccountNavigationEvent.OnDoNotSellMyInformation,
            )
        }

        fun onDeleteMyPersonalInformation() {
            onDeleteAccountNavigationEvents.postValue(
                DeleteAccountNavigationEvent.OnDeleteMyPersonalInformation,
            )
        }

        fun onDeleteAccountConfirm() {
            showProgress()
            accountApiManager.sendDeleteAccount(
                preferenceModel.getGuid(),
                BuildConfig.APP_BRAND,
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        super.onSuccess(response)
                        state.postValue(State(showSuccess = View.VISIBLE, showAppBar = View.GONE))
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorCode: String?,
                        errorMsg: String?,
                    ) {
                        super.onFailError(httpCode, errorCode, errorMsg)
                        when (errorCode) {
                            ToyotaConstants.DELETE_ACCOUNT_ALREADY_SUBMITTED ->
                                onDeleteAccountNavigationEvents.postValue(
                                    DeleteAccountNavigationEvent.ErrorAlreadySubmitted,
                                )
                            ToyotaConstants.DELETE_ACCOUNT_HEADERS_MISSING ->
                                onDeleteAccountNavigationEvents.postValue(
                                    DeleteAccountNavigationEvent.ErrorMissingHeader,
                                )
                            else -> showErrorMessage(errorMessage = errorMsg)
                        }
                    }

                    override fun onComplete() {
                        super.onComplete()
                        hideProgress()
                    }
                },
            )
        }

        fun onDeleteAccountSuccessBackToDashboard() {
            onDeleteAccountNavigationEvents.postValue(DeleteAccountNavigationEvent.BackToDashboard)
        }

        fun onDeleteAccountCancel() {
            onDeleteAccountNavigationEvents.postValue(DeleteAccountNavigationEvent.Cancel)
        }

        data class State(
            val showUSRegionContent: Boolean = false,
            val showNonUSRegionContent: Boolean = false,
            val appBarTitle: String = "",
            var showSuccess: Int = View.GONE,
            var showAppBar: Int = View.VISIBLE,
        )
    }

sealed class DeleteAccountNavigationEvent {
    object OnDeleteMyPersonalInformation : DeleteAccountNavigationEvent()

    object OnDoNotSellMyInformation : DeleteAccountNavigationEvent()

    object ErrorAlreadySubmitted : DeleteAccountNavigationEvent()

    object ErrorMissingHeader : DeleteAccountNavigationEvent()

    object BackToDashboard : DeleteAccountNavigationEvent()

    object Cancel : DeleteAccountNavigationEvent()
}
