package com.toyota.oneapp.ui.accountsettings.helpandfeedback.submitfeedback

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivitySubmitFeedbackSuccessBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint

/**
 * The Activity responsible to display Submit feedback success message.
 */
@AndroidEntryPoint
class SubmitFeedbackSuccessActivity : UiBaseActivity() {
    private lateinit var binding: ActivitySubmitFeedbackSuccessBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        binding = DataBindingUtil.setContentView(this, R.layout.activity_submit_feedback_success)
        binding.lifecycleOwner = this

        initializeViews()
        addListeners()
    }

    private fun initializeViews() {
        performActivitySetup(binding.toolbar)
    }

    private fun addListeners() {
        binding.btnDone.setOnClickListener {
            finish()
        }
    }
}
