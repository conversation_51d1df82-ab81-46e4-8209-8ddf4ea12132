package com.toyota.oneapp.ui.accountsettings.helpandfeedback

import androidx.lifecycle.LiveData
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.core.BuildWrapper
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.AppLanguageUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class HelpAndFeedbackViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        private val buildWrapper: BuildWrapper,
    ) : BaseViewModel() {
        sealed class NavigationEvent {
            data class NavigateToSelectVehicleScreen(
                val option: HelpAndFeedbackOptions,
                val vehicles: Array<VehicleInfo>?,
            ) : NavigationEvent()

            data class NavigateToAppFaqScreen(
                val vehicle: VehicleInfo?,
            ) : NavigationEvent()

            data class NavigateToVehicleSupportScreen(
                val vehicle: VehicleInfo,
            ) : NavigationEvent()

            data class NavigateToSubmitFeedbackScreen(
                val vehicle: VehicleInfo,
            ) : NavigationEvent()

            data class NavigateToContactUsScreen(
                val vehicle: VehicleInfo?,
            ) : NavigationEvent()

            data class NavigateExternalLink(
                val url: String? = null,
            ) : NavigationEvent()
        }

        private var appFaqVehicleList: List<VehicleInfo>? = null

        val helpAndFeedbackNavigationEvent = SingleLiveEvent<NavigationEvent>()
        private val _hasVehicle = SingleLiveEvent<Boolean>()
        val hasVehicle: LiveData<Boolean>
            get() = _hasVehicle

        private val _canShowAppFAQ = SingleLiveEvent<Boolean>()
        val canShowAppFAQ: LiveData<Boolean>
            get() = _canShowAppFAQ

        private val _canShowSubmitFeedback = SingleLiveEvent<Boolean>()
        val canShowSubmitFeedback: LiveData<Boolean>
            get() = _canShowSubmitFeedback

        init {
            analyticsLogger.logEvent(AnalyticsEvent.HELP_AND_FEEDBACK)
            _hasVehicle.value = applicationData.getVehicleList()?.isNotEmpty()
            _canShowSubmitFeedback.value = !buildWrapper.isSubaruApp()
        }

        fun checkForLMEX() {
            val appLocale = AppLanguageUtils.getCurrentLocaleString()
            appFaqVehicleList = applicationData.getVehicleList()?.filterNot { it.region.equals("MX") }

            if ((appLocale.contains("MX") && applicationData.getVehicleList().isNullOrEmpty())) {
                _canShowAppFAQ.value = false
            } else if (buildWrapper.isSubaruApp()) {
                _canShowAppFAQ.value = false
            } else if (applicationData.getVehicleList().isNullOrEmpty() && appFaqVehicleList.isNullOrEmpty()) {
                _canShowAppFAQ.value = true
            } else {
                _canShowAppFAQ.value = !appFaqVehicleList.isNullOrEmpty()
            }
        }

        // If user has more than one vehicle then show vehicle selection screen.
        fun onHelpAndFeedbackOptionClicked(option: HelpAndFeedbackOptions) {
            val vehicles = applicationData.getVehicleList()
            when {
                vehicles.isNullOrEmpty() -> {
                    onVehicleNotLinked(option)
                }
                vehicles.size == 1 -> {
                    onVehicleSelected(option, vehicles.first())
                }
                else -> {
                    helpAndFeedbackNavigationEvent.value =
                        NavigationEvent.NavigateToSelectVehicleScreen(
                            option = option,
                            vehicles =
                                if (option == HelpAndFeedbackOptions.APP_FAQ) {
                                    appFaqVehicleList?.toTypedArray()
                                } else {
                                    applicationData.getVehicleList()?.toTypedArray()
                                },
                        )
                }
            }
        }

        private fun onVehicleNotLinked(option: HelpAndFeedbackOptions) {
            when (option) {
                HelpAndFeedbackOptions.APP_FAQ -> {
                    helpAndFeedbackNavigationEvent.value =
                        NavigationEvent.NavigateToAppFaqScreen(
                            vehicle = null,
                        )
                }

                HelpAndFeedbackOptions.CONTACT_US -> {
                    helpAndFeedbackNavigationEvent.value =
                        NavigationEvent.NavigateToContactUsScreen(
                            vehicle = null,
                        )
                }

                else -> {
                    showRegularToastMessage(R.string.no_vehicle_message)
                }
            }
        }

        fun onVehicleSelected(
            option: HelpAndFeedbackOptions,
            selectedVehicle: VehicleInfo,
        ) {
            when (option) {
                HelpAndFeedbackOptions.APP_FAQ -> {
                    helpAndFeedbackNavigationEvent.value =
                        NavigationEvent.NavigateToAppFaqScreen(
                            vehicle = selectedVehicle,
                        )
                }
                HelpAndFeedbackOptions.VEHICLE_SUPPORT -> {
                    helpAndFeedbackNavigationEvent.value =
                        NavigationEvent.NavigateToVehicleSupportScreen(
                            vehicle = selectedVehicle,
                        )
                }
                HelpAndFeedbackOptions.SUBMIT_FEEDBACK -> {
                    helpAndFeedbackNavigationEvent.value =
                        NavigationEvent.NavigateToSubmitFeedbackScreen(
                            vehicle = selectedVehicle,
                        )
                }
                HelpAndFeedbackOptions.CONTACT_US -> {
                    helpAndFeedbackNavigationEvent.value =
                        NavigationEvent.NavigateToContactUsScreen(
                            vehicle = selectedVehicle,
                        )
                }
            }
        }

        fun onManagePreferenceClicked() {
            helpAndFeedbackNavigationEvent.value = NavigationEvent.NavigateExternalLink()
        }
    }
