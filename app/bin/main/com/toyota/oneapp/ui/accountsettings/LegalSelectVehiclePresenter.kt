package com.toyota.oneapp.ui.accountsettings

import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.constants.VIN_LIST_RETRY_TIMES
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.model.vehicle.VehiclelListResponse
import com.toyota.oneapp.network.api.manager.VehicleAPIManager
import com.toyota.oneapp.network.callback.VinListCallback
import com.toyota.oneapp.ui.BasePresenter
import javax.inject.Inject

class LegalSelectVehiclePresenter
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val vehicleManager: VehicleAPIManager,
        private val analyticsLogger: AnalyticsLogger,
    ) : BasePresenter<LegalSelectVehiclePresenter.View>() {
        fun getVehiclesList() {
            view?.showProgressDialog()
            vehicleManager.sendGetVehicleListRequest(
                VIN_LIST_RETRY_TIMES,
                object : VinListCallback(
                    getString(R.string.Vin_List_Failure),
                ) {
                    override fun onVinListSuccess(
                        response: VehiclelListResponse,
                        errorMessage: String?,
                    ) {
                        val payload = response.payload
                        applicationData.setVehicleList(payload)
                        view?.initVehicleList(payload)
                    }

                    override fun onVinListFailure(
                        responseCode: String?,
                        errorMessage: String?,
                    ) {
                        analyticsLogger.logEvent(AnalyticsEvent.VIN_LIST_FAILURE)
                        view?.showDialog(errorMessage)
                    }

                    override fun onComplete() {
                        view?.hideProgressDialog()
                    }
                },
            )
        }

        interface View : BaseView {
            fun initVehicleList(list: List<VehicleInfo>?)
        }
    }
