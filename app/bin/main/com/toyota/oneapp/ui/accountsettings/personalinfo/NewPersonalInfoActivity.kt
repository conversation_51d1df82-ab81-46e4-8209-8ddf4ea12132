package com.toyota.oneapp.ui.accountsettings.personalinfo

import android.os.Bundle
import androidx.navigation.NavGraph
import androidx.navigation.fragment.NavHostFragment
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityNewPersonalInfoBinding
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class NewPersonalInfoActivity : DataBindingBaseActivity<ActivityNewPersonalInfoBinding>() {
    private lateinit var navGraph: NavGraph

    override fun getLayoutId(): Int = R.layout.activity_new_personal_info

    override fun initViews(savedInstance: Bundle?) {
        val navHostFragment = supportFragmentManager.findFragmentById(binding.navHost.id) as NavHostFragment
        val navController = navHostFragment.navController
        val graphInflater = navController.navInflater
        navGraph = graphInflater.inflate(R.navigation.personal_info_nav_graph)

        val isNavigateToPersonalInfo =
            intent.getBooleanExtra(
                ToyotaConstants.NAVIGATE_TO_PERSONAL_DETAIL,
                false,
            )
        val destination = if (isNavigateToPersonalInfo) R.id.personalDetailsFragment else R.id.personalInfoFragment
        navGraph.setStartDestination(destination)
        navController.graph = navGraph
    }
}
