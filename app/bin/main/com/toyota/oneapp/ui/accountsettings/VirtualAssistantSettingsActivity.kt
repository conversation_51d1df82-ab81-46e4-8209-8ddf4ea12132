package com.toyota.oneapp.ui.accountsettings

import android.os.Bundle
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityVaSettingsBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VirtualAssistantSettingsActivity : UiBaseActivity() {
    private val viewModel: VirtualAssistantSettingsViewModel by viewModels()
    private var _binding: ActivityVaSettingsBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        _binding =
            DataBindingUtil.setContentView(
                this,
                R.layout.activity_va_settings,
            )
        observeBaseEvents(viewModel)
        binding.lifecycleOwner = this
        binding.executePendingBindings()
        performActivitySetup(binding.vaToolbar)
        viewModel.getVANotificationSettings()
        viewModel.vaPostFailed.observe(this) {
            when (it) {
                VirtualAssistantSettingsViewModel.NAVIGATION -> {
                    binding.vaConnectedNavigationSwitch.toggle()
                }
                VirtualAssistantSettingsViewModel.FUEL -> {
                    binding.vaMaintenanceFuelSwitch.toggle()
                }
                VirtualAssistantSettingsViewModel.WEATHER -> {
                    binding.vaWeatherSwitch.toggle()
                }
                else -> {
                    binding.vaAllowNotificationsSwitch.toggle()
                }
            }
        }
        setUpOnCheckedChangedListeners()
        setUpObservers()
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }

    private fun setUpOnCheckedChangedListeners() {
        binding.vaAllowNotificationsSwitch.setOnCheckedChangeListener { switch, isChecked ->
            viewModel.updateVANotifications(
                VirtualAssistantSettingsViewModel.GLOBAL,
                switch.isPressed,
                isChecked,
            )
        }
        binding.vaConnectedNavigationSwitch.setOnCheckedChangeListener { switch, isChecked ->
            viewModel.updateVANotifications(
                VirtualAssistantSettingsViewModel.NAVIGATION,
                switch.isPressed,
                isChecked,
            )
        }
        binding.vaMaintenanceFuelSwitch.setOnCheckedChangeListener { switch, isChecked ->
            viewModel.updateVANotifications(
                VirtualAssistantSettingsViewModel.FUEL,
                switch.isPressed,
                isChecked,
            )
        }
        binding.vaWeatherSwitch.setOnCheckedChangeListener { switch, isChecked ->
            viewModel.updateVANotifications(
                VirtualAssistantSettingsViewModel.WEATHER,
                switch.isPressed,
                isChecked,
            )
        }
    }

    private fun setUpObservers() {
        viewModel.vaNotifications.observe(this) {
            DataBindingAdapters.setIsVisible(binding.vaNotificationsLayout, it != null)
            binding.vaAllowNotificationsSwitch.isChecked = it.vaNotificationsEnabled
            DataBindingAdapters.setIsVisible(
                binding.vaConnectedNavigationCl,
                it.vaNotificationsEnabled,
            )
            binding.vaConnectedNavigationSwitch.isChecked = it.navigationNotificationEnabled ?: false
            DataBindingAdapters.setIsVisible(binding.vaMaintenanceFuelCl, it.vaNotificationsEnabled)
            binding.vaMaintenanceFuelSwitch.isChecked = it.mntAndFuelNotificationEnabled ?: false
            DataBindingAdapters.setIsVisible(binding.vaWeatherCl, it.vaNotificationsEnabled)
            binding.vaWeatherSwitch.isChecked = it.weatherNotificationEnabled ?: false
        }
    }
}
