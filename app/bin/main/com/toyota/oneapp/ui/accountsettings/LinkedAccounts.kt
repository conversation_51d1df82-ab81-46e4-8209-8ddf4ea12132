package com.toyota.oneapp.ui.accountsettings

import android.os.Parcelable
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.parcelize.Parcelize
import java.util.Locale

@Parcelize
enum class LinkedAccounts(
    val musicName: String,
    val mediaSource: Int = 0,
    val redirectUrl: String = ToyotaConstants.EMPTY_STRING,
) : Parcelable {
    APPLE(ToyotaConstants.APPLE, 1, BuildConfig.APPLE_REDIRECT_URL),
    AMAZON(ToyotaConstants.AMAZON, 2, BuildConfig.AMAZON_REDIRECT_URL),
    NONE(ToyotaConstants.NONE),
    ;

    companion object {
        private val accounts = values().associateBy { it.musicName }

        fun getLinkedAccount(musicName: String?): LinkedAccounts =
            musicName?.let {
                accounts[
                    it.lowercase(
                        Locale.ROOT,
                    ),
                ]
            } ?: NONE
    }
}
