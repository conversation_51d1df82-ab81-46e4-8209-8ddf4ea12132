package com.toyota.oneapp.ui.accountsettings.personalinfo

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.app.IDPData.Companion.instance
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.ToyUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import io.grpc.stub.StreamObserver
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@HiltViewModel
class PersonalDetailsViewModel
    @Inject
    constructor(
        private val accountManager: AccountAPIManager,
        private val idpData: IDPData,
        private val applicationData: ApplicationData,
        private val userProfileAPIManager: UserProfileAPIManager,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : BaseViewModel() {
        companion object {
            private val TAG = PersonalDetailsViewModel::class.java.simpleName
        }

        val successUpdateAccount: MutableLiveData<Boolean> = MutableLiveData()
        private val onBackClicked = SingleLiveEvent<Any>()
        private val personalDetailSaveBtnClick = SingleLiveEvent<Any>()

        val onBackButtonClicked: LiveData<Any>
            get() = onBackClicked

        val personalDetailSaveBtnClicked: LiveData<Any>
            get() = personalDetailSaveBtnClick

        fun savePersonalInfo(
            profileName: String,
            firstName: String,
            lastName: String,
        ) {
            showProgress()
            accountManager.sendUpdateUsernameRequest(
                firstName.trim(),
                lastName.trim(),
                applicationData.getSelectedVehicle()?.brand ?: BuildConfig.APP_BRAND,
                ToyUtil.getAssociatedSXMGeneration(applicationData.getVehicleList()),
                ToyUtil.getAssociatedSXMBrands(applicationData.getVehicleList()),
                oneAppPreferenceModel.getGuid(),
                IDPData.getInstance(oneAppPreferenceModel).getOID(),
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        updateUserProfile(profileName)
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        showErrorMessage(errorMsg)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun onBackButtonClicked() {
            onBackClicked.call()
        }

        fun getUserFirstName(): String? = instance?.getUserFirstName()

        fun getUserLastName(): String? = instance?.getUserLastName()

        fun personalDetailSaveBtnClicked() {
            personalDetailSaveBtnClick.call()
        }

        private fun updateUserProfile(profileName: String) {
            showProgress()
            idpData.setUserProfileName(profileName)
            userProfileAPIManager.updateProfileName(
                profileName,
                object : StreamObserver<ProfileServiceServer.UpdateUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.UpdateUserProfileResponse) {
                        successUpdateAccount.postValue(true)
                    }

                    override fun onError(t: Throwable) {
                        LogTool.e(TAG, "updateUserProfile() onError : $t.message")
                    }

                    override fun onCompleted() {
                        hideProgress()
                    }
                },
            )
        }
    }
