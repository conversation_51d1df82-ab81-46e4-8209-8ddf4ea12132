package com.toyota.oneapp.ui.accountsettings

import android.os.Bundle
import android.text.TextUtils
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.account.AccountManager
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.databinding.SecurityBiometricContentBinding
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseActivity
import com.toyota.oneapp.util.BiometryUtil
import com.toyota.oneapp.util.BiometryUtil.BiometryCallback
import com.toyota.oneapp.util.BiometryUtil.showBiometryDialog
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import java.security.Signature
import javax.inject.Inject

@AndroidEntryPoint
class BiometricActivity : BaseActivity() {
    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @Inject
    lateinit var accountManager: AccountManager

    @Inject
    lateinit var oneAppPreferenceModel: OneAppPreferenceModel

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        val activityMainBinding =
            DataBindingUtil.setContentView<SecurityBiometricContentBinding>(
                this,
                R.layout.security_biometric_content,
            )
        activityMainBinding.lifecycleOwner = this
        activityMainBinding.biometricUnlock.setOnClickListener {
            showBiometryDialog()
        }
        activityMainBinding.biometricSignout.setOnClickListener {
            performLogout()
        }
    }

    override fun onBackPressed() {
        performLogout()
    }

    private fun showBiometryDialog() {
        if (!TextUtils.isEmpty(IDPData.getInstance(oneAppPreferenceModel).accessToken)) {
            if (BiometryUtil.canAuthenticate(this)) {
                showBiometryDialog(
                    this,
                    getString(R.string.Biometry_dialog_title),
                    getString(
                        R.string.Biometry_login_to_OneApp,
                    ),
                    getString(R.string.Common_cancel),
                    object : BiometryCallback {
                        override fun onSuccess(signature: Signature?) {
//                            if (digitalKeyPlatformUtils.isDkEnrolled()) {
//                                digitalMopKeyUtils.initializeDigitalKey()
//                            }
                            finish()
                        }

                        override fun onFailure() {}

                        override fun onLockedFailure() {}

                        override fun onCancelled() { }

                        override fun onError(errorCode: Int) { }
                    },
                )
            } else {
                DialogUtil.showDialog(
                    this,
                    null,
                    getString(R.string.Login_biometry_not_enrolled),
                    getString(R.string.Common_ok),
                )
            }
        } else {
            DialogUtil.showDialog(
                this,
                getString(R.string.Login_login),
                getString(R.string.Login_use_biometry_note),
                getString(R.string.Common_ok),
            )
        }
    }

    override fun performLogout() {
        accountManager.logout()
        oneAppPreferenceModel.setLoggedIn(false)
        super.performLogout()
    }
}
