package com.toyota.oneapp.ui.accountsettings.personalinfo

import android.app.Activity
import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.BillingAddressFragmentBinding
import com.toyota.oneapp.model.account.CustomerAddress
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.model.subscription.AddressEnteredByUser
import com.toyota.oneapp.model.subscription.Addresses
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.widget.AddressView
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class BillingAddressFragment : BaseDataBindingFragment<BillingAddressFragmentBinding>() {
    private val viewModel: BillingAddressViewModel by viewModels()

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    private var accountInfo: AccountInfoSubscriber? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: BillingAddressFragmentBinding,
        savedInstance: Bundle?,
    ) {
        binding.billingAddressViewModel = viewModel
        initViewModelObserver(binding)
    }

    private fun initViewModelObserver(binding: BillingAddressFragmentBinding) {
        intView(binding)
        viewModel.run {
            billingAddressSaveBtnClicked.observe(
                viewLifecycleOwner,
                Observer {
                    val address1: AddressView.Address = binding.address.address
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEventParam.PROFILE_INFO_SAVE,
                        AnalyticsEventParam.PROFILE_UPDATE_ADDRESS,
                    )
                    if (viewDataBinding.address.validate()) {
                        if (viewModel.isAccountUpdated(
                                address1.line1,
                                address1.city,
                                address1.countryCode,
                                address1.zipCode,
                                address1.stateCode,
                                accountInfo?.customerAddresses?.get(0)
                                    ?: CustomerAddress(
                                        ToyotaConstants.HOME,
                                        "",
                                        null,
                                        null,
                                        null,
                                        ToyotaConstants.REGION_US,
                                    ),
                            )
                        ) {
                            requireActivity().setResult(Activity.RESULT_OK)
                            requireActivity().finish()
                        } else {
                            verifyHomeAddress(
                                address1.city,
                                address1.countryCode,
                                address1.stateCode,
                                address1.zipCode,
                                address1.line1,
                            )
                        }
                    }
                },
            )

            onBackButtonClicked.observe(
                viewLifecycleOwner,
                Observer {
                    requireActivity().onBackPressed()
                },
            )

            successResponseObserver.observe(
                viewLifecycleOwner,
                Observer {
                    val addressResultNumber = "1"
                    val addressList: ArrayList<Addresses> = it.addresses
                    for (i in addressList.indices) {
                        val addressObj = addressList[i]
                        val resultNumber = addressObj.resultNumber
                        val resultPercentage = addressObj.resultPercentage!!.toDouble()
                        if (resultNumber == addressResultNumber && resultPercentage > 0.00) {
                            goToAddressSuggestionScreen(addressObj, binding)
                        } else if (resultPercentage == 0.00) {
                            goToAddressSuggestionScreen(addressObj, binding)
                        }
                    }
                },
            )

            apiErrorObserverEvent.observe(
                viewLifecycleOwner,
                Observer {
                    if (getString(R.string.home_address_invalid_attribute).equals(
                            it,
                            ignoreCase = true,
                        )
                    ) {
                        DialogUtil.showDialog(
                            activity,
                            getString(R.string.Address_Verification_incorrect_address_alert_title),
                            getString(R.string.Address_Verification_incorrect_address_alert_body),
                            getString(R.string.Common_okay),
                        )
                    } else {
                        showErrorMessage(
                            getString(R.string.subscribtion_address_verifiction_failure_message),
                        )
                    }
                },
            )
        }
    }

    private fun goToAddressSuggestionScreen(
        addressObj: Addresses,
        binding: BillingAddressFragmentBinding,
    ) {
        val address: AddressView.Address = binding.address.address
        val addressEnteredByUser =
            AddressEnteredByUser(
                address.city,
                address.stateCode,
                address.zipCode,
                address.countryCode,
                address.line1,
            )

        findNavController().navigate(
            BillingAddressFragmentDirections.actionBillingAddressFragmentToAccountSettingAddressVerificationFragment2(
                addressObj,
                addressEnteredByUser,
            ),
        )
    }

    private fun intView(binding: BillingAddressFragmentBinding) {
        val extraAccountInfo =
            requireActivity().intent.getParcelableExtra<AccountInfoSubscriber?>(
                ToyotaConstants.ACCOUNT_INFO,
            )
        if (extraAccountInfo == null) {
            requireActivity().finish()
            return
        }
        accountInfo = extraAccountInfo
        showAccountInfo(
            accountInfo?.customerAddresses?.get(0)
                ?: CustomerAddress(
                    ToyotaConstants.HOME,
                    "",
                    null,
                    null,
                    null,
                    ToyotaConstants.REGION_US,
                ),
        )

        binding.address.setOnValidListener(
            object : AddressView.OnValidListener {
                override fun onValid(isValid: Boolean) {
                    invalidateAddressValidation(isValid, binding)
                }
            },
        )
    }

    private fun invalidateAddressValidation(
        isValid: Boolean,
        binding: BillingAddressFragmentBinding,
    ) {
        val address1: AddressView.Address = binding.address.address
        if (address1.city?.lowercase(Locale.ROOT) ==
            accountInfo?.customerAddresses?.get(0)?.city?.lowercase(
                Locale.ROOT,
            ) &&
            address1.line1?.lowercase(Locale.ROOT) ==
            accountInfo
                ?.customerAddresses
                ?.get(
                    0,
                )?.address
                ?.lowercase(Locale.ROOT) &&
            address1.zipCode?.lowercase(Locale.ROOT) ==
            accountInfo
                ?.customerAddresses
                ?.get(
                    0,
                )?.zipCode
                ?.lowercase(Locale.ROOT)
        ) {
            binding.billingAddressBtn.isEnabled = false
        } else {
            binding.billingAddressBtn.isEnabled = isValid
        }
    }

    private fun showAccountInfo(customerAddress: CustomerAddress?) {
        customerAddress?.let {
            viewDataBinding.address.setAddress(it)
        }
    }

    override fun getLayout(): Int = R.layout.billing_address_fragment
}
