package com.toyota.oneapp.ui.accountsettings.personalinfo

import android.os.CountDownTimer
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.network.api.repository.SmsOptInRepository
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SmsHangTightViewModel
    @Inject
    constructor(
        private val smsOptInRepository: SmsOptInRepository,
    ) : BaseViewModel() {
        private val mConsentInfo = MutableLiveData<String?>()
        val consentInfo: LiveData<String?> get() = mConsentInfo

        var phoneNumber = ""

        companion object {
            const val TIMER_STARTED = 1
            const val TIMER_FINISHED = 2
            const val HANGTIGHT_ELAPSED_TIME = 300000L
        }

        private val _hangTightTimerStatus = MutableLiveData<Int>()
        val hangTightTimerStatus: LiveData<Int> get() = _hangTightTimerStatus
        private val hangTightTimer =
            object : CountDownTimer(HANGTIGHT_ELAPSED_TIME, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    // Do nothing
                }

                override fun onFinish() {
                    _hangTightTimerStatus.value = TIMER_FINISHED
                }
            }

        fun getConsentStatus() {
            viewModelScope.launch {
                val response = smsOptInRepository.getConsentStatus("+1".plus(phoneNumber))
                mConsentInfo.postValue(response.data?.payLoad?.consent)
            }
        }

        fun startTimer() {
            _hangTightTimerStatus.value = TIMER_STARTED
            hangTightTimer.start()
        }

        fun stopTimer() {
            hangTightTimer.cancel()
        }
    }
