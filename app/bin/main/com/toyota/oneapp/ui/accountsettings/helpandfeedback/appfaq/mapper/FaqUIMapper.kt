package com.toyota.oneapp.ui.accountsettings.helpandfeedback.appfaq.mapper

import com.toyota.oneapp.R
import com.toyota.oneapp.model.account.helpandfeedback.Faq
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.appfaq.model.FaqUIModel
import javax.inject.Inject

/**
 * A Mapper used to convert Faq Domain model to UI Model.
 */
class FaqUIMapper
    @Inject
    constructor() {
        fun mapToUI(faqs: List<Faq>): List<FaqUIModel> =
            if (faqs.size == 1) {
                val faqUIModels = listOf(FaqUIModel.Item(faqs[0]))

                faqUIModels
            } else {
                val faqUIModels = mutableListOf<FaqUIModel>()

                val faqCategoryMap = faqs.groupBy { it.category }
                // Popular Faqs.
                val popularFaqs = faqCategoryMap[Faq.Category.POPULAR]
                if (popularFaqs != null && popularFaqs.isNotEmpty()) {
                    faqUIModels.add(FaqUIModel.Header(R.string.popular_topics))
                    faqUIModels.addAll(popularFaqs.map { FaqUIModel.Item(it) })
                }
                // Additional Faqs.
                val additionalFaqs = faqCategoryMap[Faq.Category.ADDITIONAL]
                if (additionalFaqs != null && additionalFaqs.isNotEmpty()) {
                    faqUIModels.add(FaqUIModel.Header(R.string.additional_topics))
                    faqUIModels.addAll(additionalFaqs.map { FaqUIModel.Item(it) })
                }

                faqUIModels
            }
    }
