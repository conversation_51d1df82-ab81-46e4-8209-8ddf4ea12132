package com.toyota.oneapp.ui.accountsettings

import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.account.VaNotificationPrefsPayload
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.VaRepository
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class VirtualAssistantSettingsViewModel
    @Inject
    constructor(
        private val vaRepository: VaRepository,
        val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        private val mVaNotificationData = MutableLiveData<VaNotificationPrefsPayload>()
        val vaNotifications: LiveData<VaNotificationPrefsPayload> get() = mVaNotificationData

        private val mVaPostFailed = MutableLiveData<String>()
        val vaPostFailed: LiveData<String> get() = mVaPostFailed

        fun getVANotificationSettings() {
            analyticsLogger.logEvent(AnalyticsEvent.USERS_VA_SETTINGS_GET_CTA)
            viewModelScope.launch {
                showProgress()
                val resource = vaRepository.getVaNotificationPrefs()
                hideProgress()
                if (resource is Resource.Success) {
                    resource.data?.payload?.let {
                        mVaNotificationData.postValue(it)
                    }
                } else if (resource is Resource.Failure) {
                    showErrorMessage(R.string.generic_error)
                }
            }
        }

        @VisibleForTesting
        fun updateVANotifications(
            switchType: String,
            isPressed: Boolean,
            isChecked: Boolean,
        ) {
            if (!isPressed) return
            val vaSettings = getVaPayload(switchType, isChecked)
            vaSettings ?: return
            showProgress()
            viewModelScope.launch {
                val resource = vaRepository.updateVaNotificationPrefs(vaSettings)
                hideProgress()
                if (resource is Resource.Success) {
                    if (switchType == GLOBAL) {
                        mVaNotificationData.postValue(vaSettings!!)
                    }
                } else if (resource is Resource.Failure) {
                    mVaPostFailed.postValue(switchType)
                    showErrorMessage(R.string.generic_error)
                }
            }
        }

        private fun getVaPayload(
            switchType: String,
            isChecked: Boolean,
        ): VaNotificationPrefsPayload? =
            when (switchType) {
                NAVIGATION -> {
                    logEvent(
                        if (isChecked) AnalyticsEvent.USERS_VA_CONNECTED_NAVI_ON_CTA else AnalyticsEvent.USERS_VA_CONNECTED_NAVI_OFF_CTA,
                    )
                    VaNotificationPrefsPayload(true, navigationNotificationEnabled = isChecked)
                }
                FUEL -> {
                    logEvent(
                        if (isChecked) AnalyticsEvent.USERS_VA_MAINTENANCE_AND_FUEL_ON_CTA else AnalyticsEvent.USERS_VA_MAINTENANCE_AND_FUEL_OFF_CTA,
                    )
                    VaNotificationPrefsPayload(true, mntAndFuelNotificationEnabled = isChecked)
                }
                WEATHER -> {
                    logEvent(
                        if (isChecked) AnalyticsEvent.USERS_VA_WEATHER_ON_CTA else AnalyticsEvent.USERS_VA_WEATHER_OFF_CTA,
                    )
                    VaNotificationPrefsPayload(true, weatherNotificationEnabled = isChecked)
                }
                GLOBAL -> {
                    logEvent(
                        if (isChecked) AnalyticsEvent.USERS_VA_ALLOW_NOTIFICATION_ON_CTA else AnalyticsEvent.USERS_VA_ALLOW_NOTIFICATION_OFF_CTA,
                    )
                    VaNotificationPrefsPayload(
                        isChecked,
                        navigationNotificationEnabled = isChecked,
                        mntAndFuelNotificationEnabled = isChecked,
                        weatherNotificationEnabled = isChecked,
                    )
                }
                else -> null
            }

        private fun logEvent(analyticsEvent: AnalyticsEvent) {
            analyticsLogger.logEvent(analyticsEvent)
        }

        companion object {
            const val WEATHER = "weather"
            const val NAVIGATION = "navi"
            const val FUEL = "fuel"
            const val GLOBAL = "global"
        }
    }
