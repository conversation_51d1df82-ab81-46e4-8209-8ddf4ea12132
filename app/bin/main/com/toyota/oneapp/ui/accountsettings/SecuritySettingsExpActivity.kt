package com.toyota.oneapp.ui.accountsettings

import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.widget.Toolbar
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityAccountSecurityAuthentificationBinding
import com.toyota.oneapp.ui.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SecuritySettingsExpActivity : BaseActivity() {
    private val viewModel: SecuritySettingsViewModel by viewModels()

    companion object {
        const val IMMEDIATE = "immediate"
        const val ONE_MIN = "one_min"
        const val FIVE_MIN = "five_min"
        const val FIFTEEN_MIN = "fifteen_min"
        const val ONE_HOUR = "one_hour"
        const val SIX_HOUR = "six_hour"
        const val TWELVE_HOUR = "twelve_hour"
    }

    private lateinit var binding: ActivityAccountSecurityAuthentificationBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding = ActivityAccountSecurityAuthentificationBinding.inflate(layoutInflater)
        setContentView(binding.root)
        val toolbar = findViewById<View>(R.id.account_security_authent_toolbar) as Toolbar
        setSupportActionBar(toolbar)

        when {
            viewModel.getSharePreferenceSessionTime() == IMMEDIATE -> {
                binding.immtick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            }
            viewModel.getSharePreferenceSessionTime() == ONE_MIN -> {
                binding.onetick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            }
            viewModel.getSharePreferenceSessionTime() == FIVE_MIN -> {
                binding.fivetick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            }
            viewModel.getSharePreferenceSessionTime() == FIFTEEN_MIN -> {
                binding.fifteentick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            }
            viewModel.getSharePreferenceSessionTime() == ONE_HOUR -> {
                binding.onehourtick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            }
            viewModel.getSharePreferenceSessionTime() == SIX_HOUR -> {
                binding.sixhourtick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            }
            viewModel.getSharePreferenceSessionTime() == TWELVE_HOUR -> {
                binding.twelvehourtick.setImageDrawable(
                    resources.getDrawable(R.drawable.ic_check_red),
                )
            }
            else -> {
                binding.fifteentick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            }
        }

        binding.one.setOnClickListener {
            binding.immtick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            binding.onetick.setImageDrawable(null)
            binding.fivetick.setImageDrawable(null)
            binding.fifteentick.setImageDrawable(null)
            binding.onehourtick.setImageDrawable(null)
            binding.sixhourtick.setImageDrawable(null)
            binding.twelvehourtick.setImageDrawable(null)
            viewModel.setSessionTime(IMMEDIATE)
        }
        binding.two.setOnClickListener {
            binding.onetick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            binding.immtick.setImageDrawable(null)
            binding.fivetick.setImageDrawable(null)
            binding.fifteentick.setImageDrawable(null)
            binding.onehourtick.setImageDrawable(null)
            binding.sixhourtick.setImageDrawable(null)
            binding.twelvehourtick.setImageDrawable(null)
            viewModel.setSessionTime(ONE_MIN)
        }
        binding.three.setOnClickListener {
            binding.fivetick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            binding.immtick.setImageDrawable(null)
            binding.onetick.setImageDrawable(null)
            binding.fifteentick.setImageDrawable(null)
            binding.onehourtick.setImageDrawable(null)
            binding.sixhourtick.setImageDrawable(null)
            binding.twelvehourtick.setImageDrawable(null)
            viewModel.setSessionTime(FIVE_MIN)
        }
        binding.four.setOnClickListener {
            binding.fifteentick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            binding.immtick.setImageDrawable(null)
            binding.onetick.setImageDrawable(null)
            binding.fivetick.setImageDrawable(null)
            binding.onehourtick.setImageDrawable(null)
            binding.sixhourtick.setImageDrawable(null)
            binding.twelvehourtick.setImageDrawable(null)
            viewModel.setSessionTime(FIFTEEN_MIN)
        }
        binding.five.setOnClickListener {
            binding.onehourtick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            binding.immtick.setImageDrawable(null)
            binding.onetick.setImageDrawable(null)
            binding.fivetick.setImageDrawable(null)
            binding.sixhourtick.setImageDrawable(null)
            binding.twelvehourtick.setImageDrawable(null)
            binding.fifteentick.setImageDrawable(null)
            viewModel.setSessionTime(ONE_HOUR)
        }
        binding.six.setOnClickListener {
            binding.sixhourtick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            binding.twelvehourtick.setImageDrawable(null)
            binding.immtick.setImageDrawable(null)
            binding.fivetick.setImageDrawable(null)
            binding.onehourtick.setImageDrawable(null)
            binding.onetick.setImageDrawable(null)
            binding.fifteentick.setImageDrawable(null)
            viewModel.setSessionTime(SIX_HOUR)
        }
        binding.seven.setOnClickListener {
            binding.twelvehourtick.setImageDrawable(resources.getDrawable(R.drawable.ic_check_red))
            binding.immtick.setImageDrawable(null)
            binding.onetick.setImageDrawable(null)
            binding.fivetick.setImageDrawable(null)
            binding.fifteentick.setImageDrawable(null)
            binding.onehourtick.setImageDrawable(null)
            binding.sixhourtick.setImageDrawable(null)
            viewModel.setSessionTime(TWELVE_HOUR)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
}
