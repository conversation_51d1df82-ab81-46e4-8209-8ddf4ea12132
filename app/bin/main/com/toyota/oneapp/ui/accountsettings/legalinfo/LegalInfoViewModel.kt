/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.ui.accountsettings.legalinfo

import androidx.lifecycle.LiveData
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class LegalInfoViewModel
    @Inject
    constructor() : BaseViewModel() {
        private val onLegalItemClickEvent = SingleLiveEvent<Any>()
        private val onPrivacyNoticeClickEvent = SingleLiveEvent<Any>()
        private val onSubaruPrivacyNoticeClickEvent = SingleLiveEvent<Any>()

        val observableLegalItemClick: LiveData<Any>
            get() = onLegalItemClickEvent

        val observablePrivacyNoticeClick: LiveData<Any>
            get() = onPrivacyNoticeClickEvent

        val observableSubaruPrivacyNoticeClick: LiveData<Any>
            get() = onSubaruPrivacyNoticeClickEvent

        fun onLegalItemClicked() {
            onLegalItemClickEvent.call()
        }

        fun onPrivacyNoticeClicked() {
            onPrivacyNoticeClickEvent.call()
        }

        fun onSubaruPrivacyNoticeClicked() {
            onSubaruPrivacyNoticeClickEvent.call()
        }
    }
