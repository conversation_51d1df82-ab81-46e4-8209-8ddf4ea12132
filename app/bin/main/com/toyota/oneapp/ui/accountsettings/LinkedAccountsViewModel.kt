package com.toyota.oneapp.ui.accountsettings

import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.subscription.MusicPreferencePayload
import com.toyota.oneapp.model.subscription.MusicSubscriptionRequest
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.MusicServicesRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.accountsettings.AccountPreferenceActivity.StreamQuality
import com.toyota.oneapp.ui.accountsettings.LinkedAccounts.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LinkedAccountsViewModel
    @Inject
    constructor(
        private val repository: MusicServicesRepository,
        private val preferenceModel: OneAppPreferenceModel,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        var musicPreferencePayload: MusicPreferencePayload =
            MusicPreferencePayload(
                preferenceModel.getGuid(),
                StreamQuality.MEDIUM.streamQuality,
                false,
                NONE.mediaSource,
            )
        private val linkedMusicAccounts = linkedSetOf<String>()
        private val mAmazonLinkStatus = MutableLiveData<LinkStatus>()
        val amazonLinkStatus: LiveData<LinkStatus> get() = mAmazonLinkStatus
        private val mAppleLinkStatus = MutableLiveData<LinkStatus>()
        val appleLinkStatus: LiveData<LinkStatus> get() = mAppleLinkStatus
        private val mExplicitSongs = MutableLiveData<Boolean>()
        val explicitSongs: LiveData<Boolean> get() = mExplicitSongs

        fun getMusicSubscriptions() {
            viewModelScope.launch {
                showProgress()
                val resource = repository.getMusicSubscriptions(preferenceModel.getGuid())
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        linkedMusicAccounts.clear()
                        resource.data?.payload?.let { linkedMusicAccounts.addAll(it) }
                        getMusicPreferences()
                    } else -> {
                        resource.error?.message?.let { showErrorMessage(it) }
                    }
                }
            }
        }

        fun blockExplicitSongs(block: Boolean) {
            musicPreferencePayload.let {
                val current = it.explicitSongs == true
                it.explicitSongs = block
                savePreferences(it) { success ->
                    if (!success) {
                        it.explicitSongs = current
                        mExplicitSongs.postValue(current)
                    }
                }
            }
        }

        fun getMusicPreferences() {
            viewModelScope.launch {
                showProgress()
                val resource = repository.getMusicPreferences(preferenceModel.getGuid())
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.payload?.let { handleMusicPreferences(it) }
                    } else -> {
                        resource.error?.apply {
                            // We will get 404 when there is no preference and this happens for new user.For new user the linkedMusicAccounts will be empty.
                            // We will suppress the error in that case, as the preference is not relevant at that time.
                            // When linkedMusicAccounts is not empty, the error from preference should be treated as error and is shown to user.
                            if (linkedMusicAccounts.isNotEmpty()) {
                                showErrorMessage(message)
                            }
                        }
                    }
                }
            }
        }

        fun linkMusicAccount(
            authCode: String,
            linkedAccount: LinkedAccounts,
        ) {
            val musicRequest =
                MusicSubscriptionRequest(
                    authCode,
                    preferenceModel.getGuid(),
                    linkedAccount.musicName,
                    linkedAccount.redirectUrl,
                )
            viewModelScope.launch {
                showProgress()
                val resource = repository.updateMusicAuthToken(musicRequest)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        linkedMusicAccounts.add(linkedAccount.musicName)
                        logMusicAccountLinkedEvent(linkedAccount)
                        // After linking a new account, if the default music source is NONE, set it to the new account and if the default streaming quality
                        // is not MEDIUM and if the account linked is AMAZON, set it to MEDIUM
                        val streamQuality =
                            if (linkedAccount == AMAZON &&
                                musicPreferencePayload.streamingQuality != StreamQuality.MEDIUM.streamQuality
                            ) {
                                StreamQuality.MEDIUM
                            } else {
                                null
                            }
                        val mediaSource = if (musicPreferencePayload.mediaSource == NONE.mediaSource) linkedAccount.mediaSource else null

                        if (streamQuality == null && mediaSource == null) {
                            updateLinkStatus(musicPreferencePayload.mediaSource)
                        } else {
                            updateDefaultMusicService(mediaSource, streamQuality)
                        }
                    }
                    else -> {
                        resource.error?.let { showErrorMessage(it.message) }
                    }
                }
            }
        }

        fun getAlternateMusicSubscription(isApple: Boolean): LinkedAccounts =
            if (isApple) {
                if (amazonLinkStatus.value?.linked == true) AMAZON else NONE
            } else {
                if (appleLinkStatus.value?.linked == true) APPLE else NONE
            }

        private fun savePreferences(
            musicPrefsRequest: MusicPreferencePayload,
            callback: ((Boolean) -> Unit),
        ) {
            viewModelScope.launch {
                showProgress()
                val resource = repository.updateMusicPreferences(musicPrefsRequest)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        callback.invoke(true)
                    }
                    else -> {
                        resource.error?.let {
                            showErrorMessage(it.message)
                            callback.invoke(false)
                        }
                    }
                }
            }
        }

        private fun handleMusicPreferences(payload: MusicPreferencePayload) {
            musicPreferencePayload = payload
            mExplicitSongs.postValue(payload.explicitSongs == true)
            handleDefaultMusicService(payload)
        }

        private fun handleDefaultMusicService(payload: MusicPreferencePayload) {
            val newDefault =
                if ((
                        payload.mediaSource == APPLE.mediaSource &&
                            !linkedMusicAccounts.contains(
                                APPLE.musicName,
                            )
                    ) ||
                    // Default preference is apple, but no apple subscription available
                    (
                        payload.mediaSource == AMAZON.mediaSource &&
                            !linkedMusicAccounts.contains(
                                AMAZON.musicName,
                            )
                    ) ||
                    // Default preference is amazon, but no amazon subscription available
                    (payload.mediaSource == NONE.mediaSource && linkedMusicAccounts.isNotEmpty())
                ) {
                    // The current default is either not in subscription or there are valid subscriptions but default is still none
                    LinkedAccounts.getLinkedAccount(linkedMusicAccounts.firstOrNull())
                } else {
                    null // Condition where the default service is present in subscription. No need to update the new default
                }
            if (newDefault != null) {
                updateDefaultMusicService(newDefault.mediaSource)
            } else {
                updateLinkStatus(payload.mediaSource)
            }
        }

        // Updates the default music account and notifies the link and default status to both amazon and apple.
        private fun updateDefaultMusicService(
            mediaSource: Int?,
            streamQuality: StreamQuality? = null,
        ) {
            musicPreferencePayload.let {
                val currentDefault = it.mediaSource
                val currentStreamQuality = it.streamingQuality
                mediaSource?.run { it.mediaSource = this }
                streamQuality?.run { it.streamingQuality = this.streamQuality }
                savePreferences(it) { success ->
                    updateLinkStatus(if (success) it.mediaSource else currentDefault)
                    if (success) {
                        mediaSource?.let { source ->
                            logDefaultAccountChangeEvent(
                                currentDefault,
                                source,
                            )
                        }
                    } else {
                        // If default update failed, revert it to older value
                        it.mediaSource = currentDefault
                        it.streamingQuality = currentStreamQuality
                    }
                }
            }
        }

        private fun updateLinkStatus(defaultMediaSource: Int?) {
            val amazonLinked = linkedMusicAccounts.contains(AMAZON.musicName)
            mAmazonLinkStatus.postValue(
                LinkStatus(amazonLinked, amazonLinked && defaultMediaSource == AMAZON.mediaSource),
            )
            val appleLinked = linkedMusicAccounts.contains(APPLE.musicName)
            mAppleLinkStatus.postValue(
                LinkStatus(appleLinked, appleLinked && defaultMediaSource == APPLE.mediaSource),
            )
        }

        private fun logMusicAccountLinkedEvent(account: LinkedAccounts) {
            val eventTag =
                when (account) {
                    AMAZON -> AnalyticsEvent.LINKED_ACCOUNT_LINK_AMAZON
                    APPLE -> AnalyticsEvent.LINKED_ACCOUNT_LINK_APPLE
                    else -> null
                }
            eventTag?.let { analyticsLogger.logEvent(eventTag) }
        }

        private fun logDefaultAccountChangeEvent(
            currentDefault: Int,
            newDefault: Int,
        ) {
            getEventTagByMediaSource(currentDefault, false)?.let { analyticsLogger.logEvent(it) }
            getEventTagByMediaSource(newDefault, true)?.let { analyticsLogger.logEvent(it) }
        }

        private fun getEventTagByMediaSource(
            mediaSource: Int,
            defaultOn: Boolean,
        ): AnalyticsEvent? =
            when (mediaSource) {
                APPLE.mediaSource -> if (defaultOn) AnalyticsEvent.LINKED_ACCOUNT_APPLE_SAVE_DEFAULT_ON else AnalyticsEvent.LINKED_ACCOUNT_APPLE_SAVE_DEFAULT_OFF
                AMAZON.mediaSource -> if (defaultOn) AnalyticsEvent.LINKED_ACCOUNT_AMAZON_SAVE_DEFAULT_ON else AnalyticsEvent.LINKED_ACCOUNT_AMAZON_SAVE_DEFAULT_OFF
                else -> null
            }

        @VisibleForTesting
        fun addLinkedAccount(account: LinkedAccounts) {
            linkedMusicAccounts.add(account.musicName)
        }

        data class LinkStatus(
            val linked: Boolean,
            val isDefault: Boolean,
        )
    }
