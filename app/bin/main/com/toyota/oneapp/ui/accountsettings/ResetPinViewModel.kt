package com.toyota.oneapp.ui.accountsettings

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.model.account.PinRegisterRequest
import com.toyota.oneapp.model.account.PinResponse
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.PinRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ResetPinViewModel
    @Inject
    constructor(
        private val pinRepository: PinRepository,
        private val preferenceModel: OneAppPreferenceModel,
    ) : BaseViewModel() {
        var pinExists = false

        private var mUpdateResponse = MutableLiveData<PinResponse>()
        val updateResponse: LiveData<PinResponse> get() = mUpdateResponse

        fun pinReset(pin: String) {
            val pinRequest = PinRegisterRequest(pin, preferenceModel.getGuid())
            showProgress()
            viewModelScope.launch {
                val resource = pinRepository.sendPinResetRequest(pinRequest)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        resource.data.let {
                            if (it?.result == true) {
                                mUpdateResponse.postValue(it)
                            } else {
                                showErrorMessage(
                                    R.string.accountsecurity_accountPin_fail,
                                )
                            }
                        }
                    }
                    else -> {
                        resource.error?.message.let { showErrorMessage(it) }
                    }
                }
            }
        }
    }
