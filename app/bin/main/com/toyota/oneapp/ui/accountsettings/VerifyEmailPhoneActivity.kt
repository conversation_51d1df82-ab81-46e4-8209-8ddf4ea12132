package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.MenuItem
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.databinding.ActivityVerifyEmailPhoneBinding
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.MVPBaseActivity
import com.toyota.oneapp.ui.accountsettings.personalinfo.SmsHangTightActivity
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.toast.ToastUtil
import javax.inject.Inject

@AndroidEntryPoint
class VerifyEmailPhoneActivity :
    MVPBaseActivity<UpdateEmailPhonePresenter>(),
    UpdateEmailPhonePresenter.View {
    lateinit var binding: ActivityVerifyEmailPhoneBinding

    @Inject
    lateinit var presenter: UpdateEmailPhonePresenter

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @Inject
    lateinit var regionManager: RegionManager

    @Inject
    lateinit var oneAppPreferenceModel: OneAppPreferenceModel

    val verificationCodeRegex = "^\\d{6}\$"

    override fun createPresenter(): UpdateEmailPhonePresenter = presenter

    private val phoneVerificationContract =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            phoneVerificationResult(result.resultCode)
        }

    private fun phoneVerificationResult(resultCode: Int) {
        when (resultCode) {
            Activity.RESULT_OK -> {
                presenter.getConsentStatus()
            }

            else -> {
                showSmsOptErrorDialog()
            }
        }
    }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        setContentView(R.layout.activity_verify_email_phone)
        binding =
            DataBindingUtil.setContentView<ActivityVerifyEmailPhoneBinding>(
                this,
                R.layout.activity_verify_email_phone,
            )

        val toolbar = findViewById<View>(R.id.toolbar) as Toolbar
        try {
            presenter.editType = intent.getIntExtra(ToyotaConstants.EDIT_TYPE, 0)
            presenter.accountInfo = intent.getParcelableExtra(ToyotaConstants.EDITING_ACCOUNTINFO)
                ?: throw IllegalArgumentException(
                    getString(R.string.consent_account_info_argument_exception),
                )
            presenter.editValue = intent.getStringExtra(ToyotaConstants.EDIT_VALUE)
                ?: throw IllegalArgumentException(
                    getString(R.string.consent_edit_value_argument_exception),
                )
            presenter.editName =
                when (presenter.editType) {
                    1 -> getString(R.string.AccountSettings_email_address)
                    else -> getString(R.string.PersonalInfoActivity_phone_number)
                }
        } catch (e: IllegalArgumentException) {
            LogTool.e(TAG, e.message)
        }

        binding.tvVerifyEmailPhone.text = presenter.editValue

        DataBindingAdapters.setRegexFilter(binding.etVerifyEmailPhone, verificationCodeRegex)

        toolbar.title =
            String.format(
                "%s %s",
                getString(R.string.Update_email_phone_verify),
                presenter.editName,
            )
        binding.tvVerifyEmailPhoneHeader.text =
            String.format(
                getString(R.string.Verify_email_phone_verify_your),
                presenter.editName,
            )
        binding.tvVerifyEmailPhoneBody.text =
            String.format(
                getString(R.string.Verify_email_phone_header),
                presenter.editName,
            )

        setSupportActionBar(toolbar)

        binding.verifyEmailOrPhone.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.PROFILE_UPDATE_VERIFY_EMAIL_PHONE)
            presenter.verify(binding.etVerifyEmailPhone.text.toString())
        }

        binding.etVerifyEmailPhone.addTextChangedListener(
            object : TextWatcher {
                override fun afterTextChanged(s: Editable) {
                    // no-op
                }

                override fun beforeTextChanged(
                    s: CharSequence,
                    start: Int,
                    count: Int,
                    after: Int,
                ) {
                    // no-op
                }

                override fun onTextChanged(
                    s: CharSequence,
                    start: Int,
                    before: Int,
                    count: Int,
                ) {
                    binding.verifyEmailOrPhone.isEnabled =
                        binding.etVerifyEmailPhone.text.toString().matches(
                            Regex(verificationCodeRegex),
                        )
                }
            },
        )

        binding.tvRequestNewCode.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.PROFILE_UPDATE_REQUEST_NEW_CONFIRMATION_CODE)
            presenter.updateEmailOrPhone()
        }

        if (presenter.editType != 1 && !regionManager.isPreferredRegionMexico()) {
            presenter.getConsentStatus()
        } else {
            presenter.updateEmailOrPhone()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            super.onBackPressed()
        }
        return true
    }

    override fun updateComplete() {
        ToastUtil.show(
            this,
            getString(R.string.Verify_email_or_phone_request_a_new_code_success),
            R.drawable.toast_check,
        )
    }

    override fun updateFailed(errorMessage: String) {
        ToastUtil.show(this, errorMessage, R.drawable.toast_remove)
    }

    override fun verifyComplete() {
        presenter.logout()
        IDPData.getInstance(oneAppPreferenceModel).clearToken()
        DialogUtil
            .showDialog(
                this,
                getString(R.string.Verify_email_phone_verification_successful),
                getString(R.string.Verify_email_phone_confirmation),
                getString(R.string.Common_ok),
                null,
                object : OnCusDialogInterface {
                    override fun onConfirmClick() {
                        IDPData.getInstance(oneAppPreferenceModel).clearToken()
                        goToLogin(forLogout = true)
                    }

                    override fun onCancelClick() {
                        // no-op
                    }
                },
                false,
            ).setCanceledOnTouchOutside(false)
    }

    override fun verifyFailed(errorMsg: String?) {
        binding.tilVerifyEmailPhone.isErrorEnabled = true
        binding.tilVerifyEmailPhone.error =
            if (errorMsg.isNullOrBlank()) {
                getString(R.string.Verify_email_phone_confirmation_incorrect)
            } else {
                errorMsg
            }
    }

    override fun consentStatus(status: String?) {
        when (status) {
            ToyotaConstants.PHONE_VERIFICATION_CONSENT_NO -> {
                showConsentErrorDialog()
            }

            ToyotaConstants.PHONE_VERIFICATION_CONSENT_NC -> {
                startHangTightScreen()
            }

            else -> {
                startActivity(
                    Intent(this, VerifyEmailPhoneActivity::class.java)
                        .putExtra(ToyotaConstants.EDIT_TYPE, presenter.editType)
                        .putExtra(ToyotaConstants.EDIT_VALUE, presenter.editValue)
                        .putExtra(ToyotaConstants.EDITING_ACCOUNTINFO, presenter.accountInfo),
                )
            }
        }
    }

    private fun showConsentErrorDialog() {
        DialogUtil.showDialog(
            this,
            getString(R.string.important),
            getString(R.string.consent_error_dialog_message),
            null,
            getString(R.string.Common_ok),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    finish()
                }

                override fun onCancelClick() {
                    finish()
                }
            },
            false,
        )
    }

    private fun startHangTightScreen() {
        val intent =
            Intent(this, SmsHangTightActivity::class.java).apply {
                putExtra(ToyotaConstants.PHONE_NUMBER, presenter.editValue)
            }
        phoneVerificationContract.launch(intent)
    }

    private fun showSmsOptErrorDialog() {
        val appName = getString(R.string.app_name)

        DialogUtil.showDialog(
            this,
            getString(org.forgerock.android.auth.ui.R.string.important_title),
            getString(
                org.forgerock.android.auth.ui.R.string.sms_consent_hang_tight,
                appName,
            ),
            getString(org.forgerock.android.auth.ui.R.string.fr_ok),
            null,
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    finish()
                }

                override fun onCancelClick() {
                    finish()
                }
            },
            false,
        )
    }

    companion object {
        private const val TAG = "VerifyEmailPhoneActivity"
    }
}
