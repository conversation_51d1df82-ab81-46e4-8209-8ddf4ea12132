package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.SecondaryVehicleProfileAdapter
import com.toyota.oneapp.databinding.ActivitySavedVehicleProfileLayoutBinding
import com.toyota.oneapp.model.vehicle.SecondaryVehicleProfilePayload
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint

private const val TOY_VEHICLE_DISCLAIMER = "2020 Toyota Tundra"
private const val LEX_VEHICLE_DISCLAIMER = "2022 Lexus NX, 2022 Lexus LX"

@AndroidEntryPoint
class SavedVehicleProfileActivity :
    UiBaseActivity(),
    SecondaryVehicleProfileAdapter.SecondaryVehicleProfileSelectedListener {
    private val viewModel: SaveVehicleProfileViewModel by viewModels()

    private lateinit var secondaryVehicleProfileAdapter: SecondaryVehicleProfileAdapter

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        val activityMainBinding =
            DataBindingUtil.setContentView<ActivitySavedVehicleProfileLayoutBinding>(
                this,
                R.layout.activity_saved_vehicle_profile_layout,
            )
        activityMainBinding.apply {
            lifecycleOwner = this@SavedVehicleProfileActivity
            setSupportActionBar(saveProfileVehicleToolbar)
        }
        observeEvents(activityMainBinding, viewModel)
    }

    private fun observeEvents(
        activityMainBinding: ActivitySavedVehicleProfileLayoutBinding,
        viewModel: SaveVehicleProfileViewModel,
    ) {
        observeBaseEvents(viewModel)
        secondaryVehicleProfileAdapter =
            SecondaryVehicleProfileAdapter(this@SavedVehicleProfileActivity, this)
        activityMainBinding.apply {
            adapter = secondaryVehicleProfileAdapter
            executePendingBindings()
            val recyclerView = rvVehicleProfile
            recyclerView.apply {
                layoutManager = LinearLayoutManager(context as Context)
                isNestedScrollingEnabled = false
            }
            viewModel.profileVehicleData.observe(this@SavedVehicleProfileActivity, {
                addVehicleProfileList(it)
                displaySavedProfileList(this, it)
            })
        }
    }

    private fun addVehicleProfileList(vehicleProfileList: List<SecondaryVehicleProfilePayload>) {
        secondaryVehicleProfileAdapter.notifyDataSetChanged(vehicleProfileList)
    }

    private fun displaySavedProfileList(
        activityMainBinding: ActivitySavedVehicleProfileLayoutBinding,
        vehicleProfileList: List<SecondaryVehicleProfilePayload>,
    ) {
        activityMainBinding.apply {
            if (vehicleProfileList.isEmpty()) {
                driverProfileScrollview.visibility = View.GONE
                noSavedProfileLayout.visibility = View.VISIBLE
            } else {
                driverProfileScrollview.visibility = View.VISIBLE
                noSavedProfileLayout.visibility = View.GONE
            }
        }
    }

    override fun setSelectedVehicleProfile(
        secondaryVehicleProfilePayload: SecondaryVehicleProfilePayload?,
        position: Int,
    ) {
        val intent =
            Intent(this, SecondaryVehicleProfileDetailActivity::class.java).apply {
                putExtra(ToyotaConstants.VEHICLE_PROFILE, secondaryVehicleProfilePayload)
            }
        resultLauncher.launch(intent)
    }

    private fun removeVehicleProfile(selectedVin: String?) {
        viewModel.deleteVehicle(this@SavedVehicleProfileActivity, selectedVin)
    }

    private var resultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult? ->
            if (result?.resultCode == Activity.RESULT_OK) {
                val selectedVehicle: String? =
                    result.data?.getStringExtra(ToyotaConstants.SELECTED_VEHICLE)
                removeVehicleProfile(selectedVehicle)
            }
        }
}
