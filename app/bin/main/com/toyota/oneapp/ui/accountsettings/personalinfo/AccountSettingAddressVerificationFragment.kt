package com.toyota.oneapp.ui.accountsettings.personalinfo

import android.app.Activity
import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityAddressVerificationBinding
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.model.subscription.AddressEnteredByUser
import com.toyota.oneapp.model.subscription.Addresses
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.garage.AddressVerificationViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class AccountSettingAddressVerificationFragment : BaseDataBindingFragment<ActivityAddressVerificationBinding>() {
    private var suggestedAddressObj: Addresses? = null
    private lateinit var getSelectedAddress: String
    private var enteredByUserObject: AddressEnteredByUser? = null
    private val viewModel: AddressVerificationViewModel by viewModels()
    private val args: AccountSettingAddressVerificationFragmentArgs by navArgs()

    @Inject
    lateinit var applicationData: ApplicationData

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger
    private lateinit var accountInfoSubscriber: AccountInfoSubscriber

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: ActivityAddressVerificationBinding,
        savedInstance: Bundle?,
    ) {
        binding.viewModel = viewModel
        initViewModelObserver(binding)
    }

    private fun initViewModelObserver(binding: ActivityAddressVerificationBinding) {
        suggestedAddressObj = args.suggestedAddress
        enteredByUserObject = args.enteredAddress
        accountInfoSubscriber = AccountInfoSubscriber()
        binding.enteredAddressTextview.text =
            getString(
                R.string.entered_address_text,
                enteredByUserObject?.streetAddress,
                enteredByUserObject?.enteredCity,
                enteredByUserObject?.enteredState,
                enteredByUserObject?.enteredZipCode,
                enteredByUserObject?.enteredCountry,
            )
        binding.suggestedAddressTextview.text =
            getString(
                R.string.entered_address_text,
                suggestedAddressObj?.address1,
                suggestedAddressObj?.city,
                suggestedAddressObj?.state,
                suggestedAddressObj?.zipCode,
                suggestedAddressObj?.country,
            )
        initAddressSuggestionUI(suggestedAddressObj, binding)

        viewModel.run {
            viewModel.onSelectAddressClick.observe(viewLifecycleOwner, {
                savedSelectedAddress(it, binding)
            })
            viewModel.adiUpdateSuccessfully.observe(viewLifecycleOwner, {
                onApiCallResponse(it)
            })

            viewModel.onAddressEditClicked.observe(viewLifecycleOwner, {
                findNavController().popBackStack()
            })
        }

        binding.addVerificationToolbar.setOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun onApiCallResponse(status: Boolean) {
        if (status) {
            activity?.setResult(Activity.RESULT_OK)
            activity?.finish()
        }
    }

    private fun savedSelectedAddress(
        isClicked: Boolean?,
        binding: ActivityAddressVerificationBinding,
    ) {
        if (isClicked == true) {
            if (binding.radioButtonYouEntered.isChecked) {
                getSelectedAddress = "1"
                if (viewModel.isAccountUpdated(
                        enteredByUserObject?.streetAddress,
                        enteredByUserObject?.enteredCity,
                        enteredByUserObject?.enteredCountry,
                        enteredByUserObject?.enteredZipCode,
                        enteredByUserObject?.enteredState,
                        if (accountInfoSubscriber.customerAddresses != null) accountInfoSubscriber.customerAddresses[0] else null,
                    )
                ) {
                    onApiCallResponse(true)
                } else {
                    viewModel.updateAccountInfo(
                        enteredByUserObject?.streetAddress,
                        enteredByUserObject?.enteredCity,
                        enteredByUserObject?.enteredState,
                        enteredByUserObject?.enteredZipCode,
                        enteredByUserObject?.enteredCountry,
                    )
                }
            } else if (binding.radioButtonWeSuggest.isChecked) {
                getSelectedAddress = "2"
                if (viewModel.isAccountUpdated(
                        suggestedAddressObj?.address1,
                        suggestedAddressObj?.city,
                        suggestedAddressObj?.country,
                        suggestedAddressObj?.zipCode,
                        suggestedAddressObj?.state,
                        if (accountInfoSubscriber.customerAddresses != null) accountInfoSubscriber.customerAddresses[0] else null,
                    )
                ) {
                    onApiCallResponse(true)
                } else {
                    viewModel.updateAccountInfo(
                        suggestedAddressObj?.address1,
                        suggestedAddressObj?.city,
                        suggestedAddressObj?.state,
                        suggestedAddressObj?.zipCode,
                        suggestedAddressObj?.country,
                    )
                }
            }
        }
    }

    private fun initAddressSuggestionUI(
        suggestedAddressObj: Addresses?,
        binding: ActivityAddressVerificationBinding,
    ) {
        if (suggestedAddressObj?.resultPercentage?.toDouble()!! > 0.00) {
            binding.textViewAddressVerification.text =
                getString(
                    R.string.subscription_address_verification_text_with_suggestion,
                )
            binding.radioButtonWeSuggest.isChecked = true
        } else {
            binding.textViewAddressVerification.text =
                getString(
                    R.string.subscription_address_verfication_no_suggestion,
                )
            binding.radioButtonWeSuggest.visibility = View.GONE
            binding.suggestedAddressTextview.visibility = View.GONE
            binding.radioButtonYouEntered.isChecked = true
        }
    }

    override fun getLayout(): Int = R.layout.activity_address_verification
}
