package com.toyota.oneapp.ui.accountsettings.helpandfeedback.selectvehicle

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class SelectVehicleViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
    ) : BaseViewModel() {
        private val mVehicles: MutableLiveData<List<VehicleInfo>> = MutableLiveData()

        val vehicles: LiveData<List<VehicleInfo>>
            get() = mVehicles

        fun fetchVehicles(vehicles: Array<VehicleInfo>?) {
            mVehicles.value = vehicles?.toList() ?: emptyList()
        }
    }
