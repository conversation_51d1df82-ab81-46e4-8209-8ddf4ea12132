package com.toyota.oneapp.ui.accountsettings

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.subscription.MusicPreferencePayload
import com.toyota.oneapp.model.subscription.MusicSubscriptionRequest
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.MusicServicesRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.accountsettings.AccountPreferenceActivity.StreamQuality
import com.toyota.oneapp.ui.accountsettings.LinkedAccounts.AMAZON
import com.toyota.oneapp.ui.accountsettings.LinkedAccounts.APPLE
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class AccountPreferenceViewModel
    @Inject
    constructor(
        private val musicServicesRepository: MusicServicesRepository,
        private val preferenceModel: OneAppPreferenceModel,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        lateinit var musicPreferencePayload: MusicPreferencePayload
        lateinit var currentMusicAccount: LinkedAccounts
        private var savedDefaultMediaSource: Int = 0
        private var savedStreamingQuality: String? = null
        private val mDismissScreenEvent = MutableLiveData<Boolean>()
        val dismissScreenEvent: LiveData<Boolean> get() = mDismissScreenEvent
        private val mSaveButtonStateChanged = MutableLiveData(false)
        val saveButtonStateChanged: LiveData<Boolean> get() = mSaveButtonStateChanged
        var preferenceModified = false

        fun initialize(
            payload: MusicPreferencePayload,
            currentAccount: LinkedAccounts,
        ) {
            musicPreferencePayload = payload
            currentMusicAccount = currentAccount
            savedDefaultMediaSource = payload.mediaSource
            savedStreamingQuality = payload.streamingQuality?.lowercase(Locale.getDefault())
        }

        fun setDefault(linkedAccounts: LinkedAccounts) {
            musicPreferencePayload.mediaSource = linkedAccounts.mediaSource
            stateChanged()
        }

        fun setStreamingQuality(quality: StreamQuality) {
            musicPreferencePayload.streamingQuality = quality.streamQuality
            stateChanged()
        }

        fun haveUnsavedChanges(): Boolean =
            with(musicPreferencePayload) {
                streamingQuality != savedStreamingQuality || savedDefaultMediaSource != mediaSource
            }

        fun savePreferences() {
            viewModelScope.launch {
                showProgress()
                val resource = musicServicesRepository.updateMusicPreferences(musicPreferencePayload)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        logDefaultAccountChangeEvent()
                        preferenceModified = true
                        mDismissScreenEvent.postValue(false)
                    }
                    else -> {
                        resource.error?.let { showErrorMessage(it.message) }
                    }
                }
            }
        }

        fun unlinkService() {
            val request =
                MusicSubscriptionRequest(
                    authServerName = currentMusicAccount.musicName,
                    userProfileID = preferenceModel.getGuid(),
                )
            viewModelScope.launch {
                showProgress()
                val resource = musicServicesRepository.deleteMusicToken(request)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        logUnlinkEvent()
                        mDismissScreenEvent.postValue(true)
                    } else -> {
                        resource.error?.let { showErrorMessage(it.message) }
                    }
                }
            }
        }

        private fun stateChanged() {
            mSaveButtonStateChanged.postValue(haveUnsavedChanges())
        }

        private fun logUnlinkEvent() {
            val eventTag =
                when (currentMusicAccount) {
                    AMAZON -> AnalyticsEvent.LINKED_ACCOUNT_UNLINK_AMAZON
                    APPLE -> AnalyticsEvent.LINKED_ACCOUNT_UNLINK_APPLE
                    else -> null
                }
            eventTag?.let { analyticsLogger.logEvent(it) }
        }

        private fun logDefaultAccountChangeEvent() {
            // Current media source and saved media source are different indicating the default preference has changed
            if (musicPreferencePayload.mediaSource != savedDefaultMediaSource) {
                // The current media source is made default
                getEventTagByMediaSource(musicPreferencePayload.mediaSource, defaultOn = true)?.let {
                    analyticsLogger.logEvent(
                        it,
                    )
                }
                // The saved media source is turned off from being default
                getEventTagByMediaSource(savedDefaultMediaSource, defaultOn = false)?.let {
                    analyticsLogger.logEvent(
                        it,
                    )
                }
            }
        }

        private fun getEventTagByMediaSource(
            mediaSource: Int,
            defaultOn: Boolean,
        ): AnalyticsEvent? =
            when (mediaSource) {
                APPLE.mediaSource ->
                    if (defaultOn) {
                        AnalyticsEvent.LINKED_ACCOUNT_APPLE_SAVE_DEFAULT_ON
                    } else {
                        AnalyticsEvent.LINKED_ACCOUNT_APPLE_SAVE_DEFAULT_OFF
                    }
                AMAZON.mediaSource ->
                    if (defaultOn) {
                        AnalyticsEvent.LINKED_ACCOUNT_AMAZON_SAVE_DEFAULT_ON
                    } else {
                        AnalyticsEvent.LINKED_ACCOUNT_AMAZON_SAVE_DEFAULT_OFF
                    }
                else -> null
            }
    }

data class AccountPreferenceResult(
    val preferenceModified: Boolean,
    val unlink: Boolean,
)
