package com.toyota.oneapp.ui.accountsettings.helpandfeedback.submitfeedback

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemFeedbackCategoryBinding
import com.toyota.oneapp.model.account.helpandfeedback.FeedbackCategory
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

class FeedbackCategoryAdapter(
    private var categories: List<FeedbackCategory>,
    private val currentSelectedCategory: FeedbackCategory?,
    private val onItemClickListener: OnItemClickListener,
) : RecyclerView.Adapter<FeedbackCategoryAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<FeedbackCategory> {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding =
            ItemFeedbackCategoryBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.bind(categories[position])
    }

    override fun getItemCount(): Int = categories.size

    // BindableRecyclerViewAdapter Methods.
    override fun setData(data: List<FeedbackCategory>?) {
        this.categories = data ?: emptyList()
        notifyDataSetChanged()
    }

    inner class ViewHolder(
        val binding: ItemFeedbackCategoryBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        init {
            binding.root.setOnClickListener {
                onItemClickListener.onItemClick(categories[adapterPosition])
            }
        }

        fun bind(category: FeedbackCategory) {
            binding.category = category
            binding.selectedCategory = currentSelectedCategory
        }
    }

    interface OnItemClickListener {
        fun onItemClick(category: FeedbackCategory)
    }
}
