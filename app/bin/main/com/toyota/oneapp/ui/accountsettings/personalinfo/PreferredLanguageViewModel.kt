package com.toyota.oneapp.ui.accountsettings.personalinfo

import android.content.Context
import androidx.databinding.ObservableBoolean
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.language.RegionItem
import com.toyota.oneapp.model.subscription.AccountInfoPayload
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.language.SupportedLanguages
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class PreferredLanguageViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val preferenceModel: OneAppPreferenceModel,
        private val accountManager: AccountAPIManager,
        private val languageManager: LanguageManager,
    ) : BaseViewModel() {
        private lateinit var currentLanguageCode: String
        private lateinit var changedLanguageCode: String
        val currentLanguage = SingleLiveEvent<String>()
        val isLanguageChanged = ObservableBoolean(false)

        fun onInit(
            context: Context,
            personalInfo: AccountInfoSubscriber?,
        ) {
            val accountInfo = preferenceModel.getAccountInfoSubscriber() ?: personalInfo
            val language =
                languageManager.getLanguageStringFromLocale(
                    accountInfo?.uiLanguage ?: languageManager.getCurrentLocaleString(),
                )
            currentLanguageCode = language
            updateUI(currentLanguageCode, acronymForLanguage(context, language))
        }

        val preferredLanguageNavigationEvent = SingleLiveEvent<PreferredLanguageNavigationEvent>()

        fun onBackButtonClicked() {
            preferredLanguageNavigationEvent.postValue(PreferredLanguageNavigationEvent.OnBack)
        }

        fun saveBtnClicked() {
            preferredLanguageNavigationEvent.postValue(PreferredLanguageNavigationEvent.ShowSaveDialog)
        }

        fun onLanguageClick() {
            preferredLanguageNavigationEvent.postValue(
                PreferredLanguageNavigationEvent.OnLanguageItemClick(languageManager.getCurrentRegion()),
            )
        }

        fun updateAccountInfoLanguage(language: String) {
            showProgress()
            val payload = AccountInfoPayload()
            val savedAccountInfo: AccountInfoSubscriber = preferenceModel.getAccountInfoSubscriber() ?: AccountInfoSubscriber()
            val accountInfoSubscriber = AccountInfoSubscriber()
            accountInfoSubscriber.preferredLanguage = AppLanguageUtils.getPreferredLanguage(language)
            accountInfoSubscriber.uiLanguage = language
            accountInfoSubscriber.guid = preferenceModel.getGuid()
            accountInfoSubscriber.objectId = savedAccountInfo.objectId
            accountInfoSubscriber.customerAddresses = savedAccountInfo.customerAddresses
            accountInfoSubscriber.firstName = savedAccountInfo.firstName
            accountInfoSubscriber.lastName = savedAccountInfo.lastName
            accountInfoSubscriber.customerPhoneNumbers = savedAccountInfo.customerPhoneNumbers
            accountInfoSubscriber.customerEmails = savedAccountInfo.customerEmails
            payload.primarySubscriber = accountInfoSubscriber
            accountManager.sendSetAccountLanguage(
                BuildConfig.APP_BRAND,
                payload,
                object : BaseCallback<BaseResponse?>() {
                    override fun onSuccess(response: BaseResponse?) {
                        val accounLocal = preferenceModel.getAccountInfoSubscriber() ?: AccountInfoSubscriber()
                        accounLocal.uiLanguage = language
                        accounLocal.preferredLanguage = AppLanguageUtils.getPreferredLanguage(language)
                        preferenceModel.setAccountInfoSubscriber(accounLocal)
                        preferredLanguageNavigationEvent.postValue(
                            PreferredLanguageNavigationEvent.OnAccountUpdateSuccess(
                                languageManager.getLocaleFromCode(language).language,
                            ),
                        )
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        showErrorMessage(errorMsg)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun updateUI(
            languageCode: String,
            language: String,
        ) {
            changedLanguageCode = languageCode
            currentLanguage.postValue(language)
        }

        fun didUserChangeLanguage() {
            isLanguageChanged.set(!currentLanguageCode.equals(changedLanguageCode, true))
        }

        fun save() {
            updateAccountInfoLanguage(
                changedLanguageCode.lowercase(Locale.getDefault()) + "-" + languageManager.getCurrentRegion().regionCode,
            )
        }

        private fun acronymForLanguage(
            context: Context,
            language: String,
        ): String =
            context.getString(
                when (language.uppercase(Locale.getDefault())) {
                    SupportedLanguages.ENGLISH.acronym -> SupportedLanguages.ENGLISH.nameResId
                    SupportedLanguages.FRENCH.acronym -> SupportedLanguages.FRENCH.nameResId
                    SupportedLanguages.SPANISH.acronym -> SupportedLanguages.SPANISH.nameResId
                    else -> SupportedLanguages.ENGLISH.nameResId
                },
            )
    }

sealed class PreferredLanguageNavigationEvent {
    object OnBack : PreferredLanguageNavigationEvent()

    data class OnAccountUpdateSuccess(
        val language: String,
    ) : PreferredLanguageNavigationEvent()

    object ShowSaveDialog : PreferredLanguageNavigationEvent()

    data class OnLanguageItemClick(
        val regionItem: RegionItem,
    ) : PreferredLanguageNavigationEvent()
}
