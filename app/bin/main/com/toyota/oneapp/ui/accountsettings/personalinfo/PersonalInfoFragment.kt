package com.toyota.oneapp.ui.accountsettings.personalinfo

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.PersonalInfoFragmentBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PersonalInfoFragment : BaseDataBindingFragment<PersonalInfoFragmentBinding>() {
    private val viewModel: PersonalInfoViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: PersonalInfoFragmentBinding,
        savedInstance: Bundle?,
    ) {
        binding.personalInfoViewModel = viewModel
        initViewModelObservers(binding)
    }

    private fun initViewModelObservers(binding: PersonalInfoFragmentBinding) {
        viewModel.run {
            onPersonalInfoClicked.observe(
                viewLifecycleOwner,
                Observer {
                    findNavController().navigate(
                        PersonalInfoFragmentDirections.actionPersonalInfoFragmentToPersonalDetailsFragment(),
                    )
                },
            )

            onBillingAddressClicked.observe(
                viewLifecycleOwner,
                Observer {
                    findNavController().navigate(
                        PersonalInfoFragmentDirections.actionPersonalInfoFragmentToBillingAddressFragment(),
                    )
                },
            )

            onPreferredLanguageClicked.observe(
                viewLifecycleOwner,
                Observer {
                    findNavController().navigate(
                        PersonalInfoFragmentDirections.actionPersonalInfoFragmentToPreferredLanguageFragment(),
                    )
                },
            )

            onToolbarBackButtonClicked.observe(
                viewLifecycleOwner,
                Observer {
                    requireActivity().onBackPressedDispatcher.onBackPressed()
                    finishActivity()
                },
            )
        }
    }

    override fun getLayout(): Int = R.layout.personal_info_fragment
}
