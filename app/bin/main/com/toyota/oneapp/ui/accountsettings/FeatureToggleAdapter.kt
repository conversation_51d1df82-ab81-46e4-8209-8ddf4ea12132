package com.toyota.oneapp.ui.accountsettings

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButtonToggleGroup
import com.toyota.oneapp.R
import com.toyota.oneapp.app.AppFeatureFlags
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.Features
import java.util.*
import javax.inject.Inject

class FeatureToggleAdapter
    @Inject
    constructor(
        private var featureList: EnumSet<Feature>,
        private val appFeatureFlags: AppFeatureFlags,
    ) : RecyclerView.Adapter<FeatureToggleAdapter.ViewHolder>() {
        private var count = 0

        init {
            getTotalEnabledCount()
        }

        private fun getTotalEnabledCount() {
            for (feature in featureList) {
                if (appFeatureFlags.isFeatureEnabled(feature)) count++
            }
        }

        override fun getItemViewType(position: Int): Int = position

        override fun onCreateViewHolder(
            parent: ViewGroup,
            viewType: Int,
        ): FeatureToggleAdapter.ViewHolder {
            val view =
                LayoutInflater.from(parent.context).inflate(
                    R.layout.item_feature_toggle,
                    parent,
                    false,
                )
            return ViewHolder(view)
        }

        override fun onBindViewHolder(
            holder: FeatureToggleAdapter.ViewHolder,
            position: Int,
        ) {
            holder.bind(featureList.elementAt(position))
        }

        override fun getItemCount(): Int = featureList.size

        inner class ViewHolder(
            itemView: View,
        ) : RecyclerView.ViewHolder(itemView) {
            private var title: TextView = itemView.findViewById(R.id.feature_title)
            private var toggleGroup: MaterialButtonToggleGroup =
                itemView.findViewById(
                    R.id.toggle_group,
                )
            private var maintenanceButton: View = itemView.findViewById(R.id.maintenance)

            fun bind(featureFlag: Feature) {
                val isFeatureEnabled = appFeatureFlags.isFeatureEnabled(featureFlag)
                title.text = featureFlag.name

                if (isFeatureEnabled) {
                    toggleGroup.check(R.id.enable)
                } else if (!isFeatureEnabled) {
                    toggleGroup.check(R.id.disable)
                }

                toggleGroup.addOnButtonCheckedListener { _, checkedId, isChecked ->
                    if (isChecked) {
                        when (checkedId) {
                            R.id.enable -> {
                                appFeatureFlags.setFeatureOverrides(
                                    featureFlag,
                                    Features.ENABLED,
                                )
                            }
                            R.id.disable -> {
                                appFeatureFlags.setFeatureOverrides(
                                    featureFlag,
                                    Features.DISABLED,
                                )
                            }
                            R.id.maintenance -> {
                                appFeatureFlags.setFeatureOverrides(
                                    featureFlag,
                                    Features.MAINTENANCE,
                                )
                            }
                        }
                    }
                }

                // For now only allow dk_key to have maintenance override
                if (Feature.DIGITAL_KEY.name == featureFlag.name) {
                    val isMaintenanceEnabled = appFeatureFlags.isMaintenanceEnabled(featureFlag)
                    maintenanceButton.visibility = View.VISIBLE
                    if (isMaintenanceEnabled) {
                        toggleGroup.check(R.id.maintenance)
                    }
                }
            }
        }
    }
