package com.toyota.oneapp.ui.accountsettings.helpandfeedback.submitfeedback

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.FragmentSubmitFeedbackBinding
import com.toyota.oneapp.model.account.helpandfeedback.FeedbackCategory
import com.toyota.oneapp.model.account.helpandfeedback.FeedbackType
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.submitfeedback.SubmitFeedbackFragmentDirections.Companion.actionSubmitFeedbackFragmentToSelectFeedbackCategoryFragment
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.submitfeedback.SubmitFeedbackFragmentDirections.Companion.actionSubmitFeedbackFragmentToSelectFeedbackTypeFragment
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class SubmitFeedbackFragment : BaseViewModelFragment() {
    private val viewModel: SubmitFeedbackViewModel by viewModels()

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    private lateinit var binding: FragmentSubmitFeedbackBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        analyticsLogger.logEvent(AnalyticsEvent.SUBMIT_FEEDBACK)

        observeBaseEvents(viewModel)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentSubmitFeedbackBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel

        setUpViewModelBindings()

        return binding.root
    }

    private fun setUpViewModelBindings() {
        viewModel.submitFeedbackNavigationEvent.observe(
            viewLifecycleOwner,
            Observer { navigationEvent ->
                handleSubmitFeedbackNavigationEvent(navigationEvent)
            },
        )
    }

    private fun handleSubmitFeedbackNavigationEvent(navigationEvent: SubmitFeedbackViewModel.NavigationEvent) {
        when (navigationEvent) {
            is SubmitFeedbackViewModel.NavigationEvent.NavigateToSelectFeedbackTypeScreen -> {
                navigateToSelectFeedbackTypeScreen(
                    navigationEvent.types,
                    navigationEvent.selectedType,
                )
            }
            is SubmitFeedbackViewModel.NavigationEvent.NavigateToSelectFeedbackCategoryScreen -> {
                navigateToSelectFeedbackCategoryScreen(
                    navigationEvent.categories,
                    navigationEvent.selectedCategory,
                )
            }
            is SubmitFeedbackViewModel.NavigationEvent.NavigateToSubmitFeedbackSuccessScreen -> {
                navigateToSubmitFeedbackSuccessScreen()
            }
        }
    }

    private fun navigateToSelectFeedbackTypeScreen(
        types: List<FeedbackType>,
        currentSelectedType: FeedbackType?,
    ) {
        // Register for Select Type result.
        setFragmentResultListener(SelectFeedbackTypeFragment.SELECTED_TYPE_REQUEST_KEY, listener = { key, bundle ->
            val selectedType: FeedbackType? =
                bundle.getParcelable(
                    SelectFeedbackTypeFragment.SELECTED_TYPE,
                )
            selectedType?.let {
                viewModel.onTypeSelected(selectedType)
            }
        })

        val action =
            actionSubmitFeedbackFragmentToSelectFeedbackTypeFragment(
                types.toTypedArray(),
                currentSelectedType,
            )
        findNavController().navigate(action)
    }

    private fun navigateToSelectFeedbackCategoryScreen(
        categories: List<FeedbackCategory>,
        currentSelectedCategory: FeedbackCategory?,
    ) {
        // Register for Select Category result.
        setFragmentResultListener(SelectFeedbackCategoryFragment.SELECTED_CATEGORY_REQUEST_KEY, listener = { key, bundle ->
            val selectedCategory: FeedbackCategory? =
                bundle.getParcelable(
                    SelectFeedbackCategoryFragment.SELECTED_CATEGORY,
                )
            selectedCategory?.let {
                viewModel.onCategorySelected(selectedCategory)
            }
        })

        val action =
            actionSubmitFeedbackFragmentToSelectFeedbackCategoryFragment(
                categories.toTypedArray(),
                currentSelectedCategory,
            )
        findNavController().navigate(action)
    }

    private fun navigateToSubmitFeedbackSuccessScreen() {
        val intent = Intent(requireContext(), SubmitFeedbackSuccessActivity::class.java)
        startActivity(intent)
        requireActivity().finish()
    }
}
