/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.ui.accountsettings

import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.android.gms.common.util.Base64Utils
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.account.AccountManager
import com.toyota.oneapp.app.AppFeatureFlags
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.component.LinkedAccountsEnabler
import com.toyota.oneapp.component.LinkedAccountsEnabler.LinkedAccountsEnableOutcome
import com.toyota.oneapp.model.account.PhotoResponse
import com.toyota.oneapp.model.pref.SingletonPreferenceModel
import com.toyota.oneapp.model.subscription.AccountInfoResponse
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseFragmentViewModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import io.grpc.stub.StreamObserver
import toyotaone.commonlib.location.UserLocationProvider
import toyotaone.commonlib.log.LogTool
import java.io.File
import javax.inject.Inject

@HiltViewModel
class AccountSettingsViewModel
    @Inject
    constructor(
        private val accountApiManager: AccountAPIManager,
        private val userProfileAPIManager: UserProfileAPIManager,
        private val applicationData: ApplicationData,
        private val accountManager: AccountManager,
        private val singletonPreferenceModel: SingletonPreferenceModel,
        private val preferenceModel: OneAppPreferenceModel,
        private val linkedAccountsEnabler: LinkedAccountsEnabler,
        private val appFeatureFlags: AppFeatureFlags,
    ) : BaseFragmentViewModel() {
        private val mShowDataPrivacyPortal = MutableLiveData<Boolean>()
        val showDataPrivacyPortal: LiveData<Boolean> get() = mShowDataPrivacyPortal
        private val mRefreshPhotoFromBase64 = MutableLiveData<String>()
        val refreshPhotoFromBase64: LiveData<String> get() = mRefreshPhotoFromBase64

        private val mUserProfileInfo = MutableLiveData<String>()
        val uerProfileInfo: LiveData<String> get() = mUserProfileInfo

        private val mRefreshAccountInfo = MutableLiveData<AccountInfoSubscriber>()
        val refreshAccountInfo: LiveData<AccountInfoSubscriber> get() = mRefreshAccountInfo
        private val mEnableLinkedAccounts = MutableLiveData<Boolean>()
        val enableLinkedAccounts: LiveData<Boolean> get() = mEnableLinkedAccounts

        private val mEnableVaSettings = MutableLiveData<Boolean>()
        val enableVaSettings: LiveData<Boolean> get() = mEnableVaSettings

        private lateinit var userLocationProvider: UserLocationProvider

        fun initialize(userLocationProvider: UserLocationProvider) {
            this.userLocationProvider = userLocationProvider
        }

        fun start() {
            getPhoto()
            getAccountInfo()
            enableLinkedAccounts()
            setVaSettingsStatus()
            mShowDataPrivacyPortal.postValue(shouldShowPrivacyPortal())
        }

        fun setPhoto(string: String) {
            val file = File(string)
            mRefreshPhotoFromBase64.postValue(Base64Utils.encode(file.readBytes()))
            accountApiManager.sendSetPhotoRequest(preferenceModel.getGuid(), file, BaseCallback())
        }

        fun deletePhoto() {
            showProgressDialog()
            accountApiManager.sendDeletePhotoRequest(
                preferenceModel.getGuid(),
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        mRefreshPhotoFromBase64.postValue(ToyotaConstants.EMPTY_STRING)
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        LogTool.e(this@AccountSettingsViewModel::class.java.simpleName, errorMsg)
                    }

                    override fun onComplete() {
                        dismissProgressDialog()
                    }
                },
            )
        }

        @VisibleForTesting
        fun getPhoto() {
            accountApiManager.sendGetPhotoRequest(
                preferenceModel.getGuid(),
                object : BaseCallback<PhotoResponse>() {
                    override fun onSuccess(response: PhotoResponse) {
                        if (response.payload?.equals(mRefreshPhotoFromBase64.value) != true) {
                            mRefreshPhotoFromBase64.postValue(
                                response.payload ?: ToyotaConstants.EMPTY_STRING,
                            )
                        }
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        LogTool.e(
                            this@AccountSettingsViewModel::class.java.simpleName,
                            "$httpCode: $errorMsg",
                        )
                    }

                    // Gets invoked when no photo is found
                    override fun onRedirect(location: String?) {
                        // Posting an empty string to update image in UI to remove potentially
                        // cached image
                        mRefreshPhotoFromBase64.postValue(ToyotaConstants.EMPTY_STRING)
                    }
                },
            )
        }

        fun updateAccountInfo() {
            showProgressDialog()
            accountApiManager.sendGetAccountInfoRequest(
                BuildConfig.APP_BRAND,
                preferenceModel.getGuid(),
                object : BaseCallback<AccountInfoResponse>() {
                    override fun onSuccess(response: AccountInfoResponse) {
                        response.payload?.primarySubscriber?.let {
                            preferenceModel.setAccountInfoSubscriber(response.payload.primarySubscriber)
                            getAccountInfo()
                            getUserProfileName()
                        }
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        errorMsg?.let { mShowDialog.postValue(it) }
                    }

                    override fun onComplete() {
                        dismissProgressDialog()
                    }
                },
            )
        }

        fun getUserProfileName() {
            showProgressDialog()

            userProfileAPIManager.getUserProfile(
                object : StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.GetUserProfileResponse) {
                        mUserProfileInfo.postValue(value.userProfile.userInfo.profileName.value)
                        dismissProgressDialog()
                    }

                    override fun onError(t: Throwable) {
                        mUserProfileInfo.postValue(ToyotaConstants.EMPTY_STRING)
                        dismissProgressDialog()
                    }

                    override fun onCompleted() {
                        dismissProgressDialog()
                    }
                },
            )
        }

        fun getBrand(): String? {
            val selectedVehicle = applicationData.getSelectedVehicle()
            return if (selectedVehicle != null) selectedVehicle.brand else BuildConfig.APP_BRAND
        }

        fun getAccountInfo() {
            showProgressDialog()
            accountApiManager.sendGetAccountInfoRequest(
                getBrand(),
                preferenceModel.getGuid(),
                object : BaseCallback<AccountInfoResponse>() {
                    override fun onSuccess(response: AccountInfoResponse) {
                        if (response.payload != null && response.payload.primarySubscriber != null) {
                            mRefreshAccountInfo.postValue(response.payload.primarySubscriber)
                            preferenceModel.setAccountInfoSubscriber(response.payload.primarySubscriber)
                        }
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        if (preferenceModel.getAccountInfoSubscriber() != null) {
                            mRefreshAccountInfo.postValue(preferenceModel.getAccountInfoSubscriber())
                        } else {
                            // we are not dependent for profile name on accountInfo data so if accountInfo is null, we should get it from grpc
                            getUserProfileName()
                        }
                    }

                    override fun onComplete() {
                        dismissProgressDialog()
                    }
                },
            )
        }

        fun logout() {
            preferenceModel.deleteBiometricsSettingsTime()
            accountManager.logout()
        }

        @VisibleForTesting
        fun enableLinkedAccounts() {
            linkedAccountsEnabler.shouldEnableLinkedAccounts(userLocationProvider) {
                mEnableLinkedAccounts.postValue(it == LinkedAccountsEnableOutcome.ENABLE)
            }
        }

        @VisibleForTesting
        fun setVaSettingsStatus() {
            val enable =
                applicationData.getVehicleList()?.any { _ ->
                    appFeatureFlags.isFeatureEnabled(Feature.VA_SETTING)
                } == true
            mEnableVaSettings.postValue(enable)
        }

        private fun shouldShowPrivacyPortal(): Boolean {
            // Atleast one vehicle should be available with generation
            return !applicationData.getVehicleList().isNullOrEmpty() &&
                applicationData.getVehicleList()?.any { !it.generation.isNullOrBlank() } == true
        }
    }
