package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.viewModels
import androidx.appcompat.widget.SwitchCompat
import androidx.appcompat.widget.Toolbar
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import net.openid.appauth.*
import toyotaone.commonlib.log.LogTool

@AndroidEntryPoint
class LinkedAccountsActivity : UiBaseActivity() {
    private val viewModel: LinkedAccountsViewModel by viewModels()

    companion object {
        const val DATA: String = "pref_data"
        const val PREFERENCE_TYPE: String = "preference"
        const val UNLINK_RESULT: String = "unlink"
        const val DEFAULT_MUSIC_SERVICE_FALLBACK: String = "defaultMusicServiceFallback"
        const val AMAZON_AUTH_CODE = "auth_code"
        const val TAG = "LinkedAccountsActivity"
        private const val MUSIC = "music"
        private const val STATE = "12345"
    }

    private val amazonAppAuthContract =
        registerForActivityResult(AmazonAppAuthActivity.Contract()) {
            LogTool.d(TAG, "AuthCode: $it")
            it?.let { viewModel.linkMusicAccount(it, LinkedAccounts.AMAZON) }
        }

    private val appleAppAuthContract =
        registerForActivityResult(AuthorizationContract()) {
            LogTool.d(TAG, "AuthCode: $it")
            it?.let { viewModel.linkMusicAccount(it, LinkedAccounts.APPLE) }
        }

    private val accountPreferenceContract =
        registerForActivityResult(
            AccountPreferenceActivity.Contract(),
        ) {
            handleAccountPreferenceResult(it)
        }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        setContentView(R.layout.activity_linked_account)
        val toolbar = findViewById<Toolbar>(R.id.linked_accounts_info_toolbar)
        setSupportActionBar(toolbar)
        observeEvents()
        setUpClickListeners()
        viewModel.getMusicSubscriptions()
        findViewById<SwitchCompat>(R.id.music_explicit_preference_switch).setOnCheckedChangeListener { buttonView, isChecked ->
            if (buttonView.isPressed) {
                viewModel.blockExplicitSongs(isChecked)
            }
        }
    }

    private fun observeEvents() {
        observeBaseEvents(viewModel)
        viewModel.amazonLinkStatus.observe(this) {
            handleAmazonLinkStatus(it)
        }
        viewModel.appleLinkStatus.observe(this) {
            handleAppleLinkStatus(it)
        }
        viewModel.explicitSongs.observe(this, {
            findViewById<SwitchCompat>(R.id.music_explicit_preference_switch).isChecked = it
        })
    }

    private fun setUpClickListeners() {
        findViewById<View>(R.id.apple_music_main_cl).setOnClickListener {
            if (viewModel.appleLinkStatus.value?.linked == true) {
                startMusicPreference(isApple = true)
            } else {
                doAppleAuth()
            }
        }

        findViewById<View>(R.id.amazon_music_main_cl).setOnClickListener {
            if (viewModel.amazonLinkStatus.value?.linked == true) {
                startMusicPreference(isApple = false)
            } else {
                amazonAppAuthContract.launch(null)
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }

    private fun startMusicPreference(isApple: Boolean) {
        val bundle =
            Bundle().apply {
                putParcelable(
                    PREFERENCE_TYPE,
                    if (isApple) LinkedAccounts.APPLE else LinkedAccounts.AMAZON,
                )
                putParcelable(
                    DEFAULT_MUSIC_SERVICE_FALLBACK,
                    viewModel.getAlternateMusicSubscription(isApple),
                )
                putParcelable(DATA, viewModel.musicPreferencePayload)
            }
        startActivityForResult(bundle)
    }

    private fun startActivityForResult(bundle: Bundle) {
        accountPreferenceContract.launch(bundle)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        val response = intent?.let { AuthorizationResponse.fromIntent(it) }
        if (response != null) {
            val mAmazonAuthCode = response.authorizationCode.toString()
            viewModel.linkMusicAccount(mAmazonAuthCode, LinkedAccounts.AMAZON)
        }
    }

    private fun doAppleAuth() {
        val serviceConfig =
            AuthorizationServiceConfiguration(
                Uri.parse(BuildConfig.APPLE_AUTHCODE_ENDPOINT),
                Uri.parse(BuildConfig.APPLE_TOKEN_ENDPOINT),
            )

        val authRequestBuilder =
            AuthorizationRequest.Builder(
                serviceConfig,
                BuildConfig.APPLE_CLIENT_ID,
                ResponseTypeValues.CODE,
                Uri.parse(BuildConfig.APPLE_REDIRECT_URL),
            )

        val authRequest =
            authRequestBuilder
                .setScope(MUSIC)
                .setState(STATE)
                .setCodeVerifier(null, null, null)
                .build()
        val authIntent = AuthorizationService(this).getAuthorizationRequestIntent(authRequest)
        appleAppAuthContract.launch(authIntent)
    }

    private fun handleAccountPreferenceResult(accountPreferenceResult: AccountPreferenceResult?) {
        accountPreferenceResult?.let {
            if (it.unlink) {
                viewModel.getMusicSubscriptions()
            } else if (it.preferenceModified) {
                viewModel.getMusicPreferences()
            }
        }
    }

    private fun handleAmazonLinkStatus(linkStatus: LinkedAccountsViewModel.LinkStatus) {
        when {
            !linkStatus.linked -> {
                findViewById<View>(R.id.amazon_music_link_tv).visibility = View.VISIBLE
                findViewById<View>(R.id.amazon_music_linked_tv).visibility = View.GONE
            }
            else -> {
                findViewById<View>(R.id.amazon_music_link_tv).visibility = View.GONE
                val default =
                    if (linkStatus.isDefault) {
                        (",%s").format(
                            getString(R.string.music_default_status),
                        )
                    } else {
                        ToyotaConstants.EMPTY_STRING
                    }
                findViewById<TextView>(R.id.amazon_music_linked_tv).apply {
                    visibility = View.VISIBLE
                    text = getString(R.string.music_connected).plus(default)
                }
            }
        }
    }

    private fun handleAppleLinkStatus(linkStatus: LinkedAccountsViewModel.LinkStatus) {
        when {
            !linkStatus.linked -> {
                findViewById<TextView>(R.id.apple_music_link_tv).visibility = View.VISIBLE
                findViewById<TextView>(R.id.apple_music_linked_tv).visibility = View.GONE
            }
            else -> {
                findViewById<TextView>(R.id.apple_music_link_tv).visibility = View.GONE
                val default =
                    if (linkStatus.isDefault) {
                        (",%s").format(
                            getString(R.string.music_default_status),
                        )
                    } else {
                        ToyotaConstants.EMPTY_STRING
                    }
                findViewById<TextView>(R.id.apple_music_linked_tv).apply {
                    visibility = View.VISIBLE
                    text = getString(R.string.music_connected).plus(default)
                }
            }
        }
    }

    class AuthorizationContract : ActivityResultContract<Intent, String?>() {
        override fun createIntent(
            context: Context,
            input: Intent,
        ): Intent = input

        override fun parseResult(
            resultCode: Int,
            intent: Intent?,
        ): String? =
            if (resultCode == Activity.RESULT_OK) {
                intent?.let { AuthorizationResponse.fromIntent(it)?.authorizationCode }
            } else {
                null
            }
    }
}
