package com.toyota.oneapp.ui.accountsettings.helpandfeedback.contactus

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.databinding.FragmentContactUsBinding
import com.toyota.oneapp.model.account.helpandfeedback.ContactOption
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ContactUsFragment :
    BaseViewModelFragment(),
    ContactUsAdapter.OnItemClickListener {
    private val viewModel: ContactUsViewModel by viewModels()

    private lateinit var binding: FragmentContactUsBinding
    private lateinit var adapter: ContactUsAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentContactUsBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        initializeViews()
        setUpViewModelBindings()

        return binding.root
    }

    // On Item Clicked.
    override fun onItemClick(option: ContactOption) {
        viewModel.onContactOptionClicked(option)
    }

    private fun initializeViews() {
        adapter = ContactUsAdapter(emptyList(), this)
        binding.rvContactOptions.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            addItemDecoration(DividerItemDecoration(requireContext(), LinearLayout.VERTICAL))
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.contactUsNavigationEvent.observe(
            viewLifecycleOwner,
            Observer { navigationEvent ->
                handleContactUsNavigationEvent(navigationEvent)
            },
        )
        viewModel.options.observe(viewLifecycleOwner) { data ->
            DataBindingAdapters.setRecyclerViewAdapterData(binding.rvContactOptions, data, emptyList())
        }
    }

    private fun handleContactUsNavigationEvent(navigationEvent: ContactUsViewModel.NavigationEvent) {
        when (navigationEvent) {
            is ContactUsViewModel.NavigationEvent.NavigateToEmailClient -> {
                ToyUtil.sendEmail(requireContext(), arrayOf(navigationEvent.email), "", "")
            }
            is ContactUsViewModel.NavigationEvent.NavigateToPhoneDialer -> {
                ToyUtil.phoneCall(requireContext(), navigationEvent.number)
            }
        }
    }
}
