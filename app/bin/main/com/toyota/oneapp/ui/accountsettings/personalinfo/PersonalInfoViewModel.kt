package com.toyota.oneapp.ui.accountsettings.personalinfo

import androidx.lifecycle.LiveData
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class PersonalInfoViewModel
    @Inject
    constructor() : BaseViewModel() {
        private val onPersonalInfoClick = SingleLiveEvent<Any>()
        private val onBillingAddressClick = SingleLiveEvent<Any>()
        private val onPreferredLanguageClick = SingleLiveEvent<Any>()
        private val onEmergencyContactClick = SingleLiveEvent<Any>()
        private val onToolbarButtonClick = SingleLiveEvent<Any>()

        val onPersonalInfoClicked: LiveData<Any>
            get() = onPersonalInfoClick

        val onBillingAddressClicked: LiveData<Any>
            get() = onBillingAddressClick

        val onPreferredLanguageClicked: LiveData<Any>
            get() = onPreferredLanguageClick

        val onEmergencyContactClicked: LiveData<Any>
            get() = onEmergencyContactClick

        val onToolbarBackButtonClicked: LiveData<Any>
            get() = onToolbarButtonClick

        fun onPersonalDetailClicked() {
            onPersonalInfoClick.call()
        }

        fun onBillingAddressClicked() {
            onBillingAddressClick.call()
        }

        fun onPreferredLanguageClicked() {
            onPreferredLanguageClick.call()
        }

        fun onEmergencyContactClicked() {
            onEmergencyContactClick.call()
        }

        fun onToolBarBackButtonClicked() {
            onToolbarButtonClick.call()
        }
    }
