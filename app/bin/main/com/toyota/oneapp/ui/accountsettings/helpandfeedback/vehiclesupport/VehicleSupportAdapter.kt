package com.toyota.oneapp.ui.accountsettings.helpandfeedback.vehiclesupport

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemVehicleSupportBinding
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

class VehicleSupportAdapter(
    private var options: List<VehicleSupportOptionUIModel>,
    private val onItemClickListener: OnItemClickListener,
) : RecyclerView.Adapter<VehicleSupportAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<VehicleSupportOptionUIModel> {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding =
            ItemVehicleSupportBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.onBind(options[position])
    }

    override fun getItemCount(): Int = options.size

    // BindableRecyclerViewAdapter Methods.
    override fun setData(data: List<VehicleSupportOptionUIModel>?) {
        this.options = data ?: emptyList()
        notifyDataSetChanged()
    }

    inner class ViewHolder(
        private val binding: ItemVehicleSupportBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        init {
            binding.root.setOnClickListener {
                onItemClickListener.onItemClick(options[adapterPosition])
            }
        }

        fun onBind(option: VehicleSupportOptionUIModel) {
            binding.tvName.setText(option.name)
        }
    }

    interface OnItemClickListener {
        fun onItemClick(option: VehicleSupportOptionUIModel)
    }
}
