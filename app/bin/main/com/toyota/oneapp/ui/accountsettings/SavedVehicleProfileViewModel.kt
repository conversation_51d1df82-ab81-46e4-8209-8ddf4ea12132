package com.toyota.oneapp.ui.accountsettings

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.ctp.v1.VehicleRegistrationServiceOuterClass
import com.toyota.oneapp.R
import com.toyota.oneapp.model.vehicle.SecondaryVehicleProfilePayload
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.network.api.repository.VehicleProfileRepository
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import io.grpc.stub.StreamObserver
import kotlinx.coroutines.launch
import org.jetbrains.anko.runOnUiThread
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@HiltViewModel
class SaveVehicleProfileViewModel
    @Inject
    constructor(
        private val vehicleProfileRepository: VehicleProfileRepository,
        private val userProfileAPIManager: UserProfileAPIManager,
    ) : BaseViewModel() {
        companion object {
            private val TAG = SaveVehicleProfileViewModel::class.java.simpleName
        }

        private val mProfileVehicleData = MutableLiveData<List<SecondaryVehicleProfilePayload>>()
        val profileVehicleData: LiveData<List<SecondaryVehicleProfilePayload>> get() = mProfileVehicleData

        init {
            getSecondaryProfileData()
        }

        fun getSecondaryProfileData() {
            showProgress()
            viewModelScope.launch {
                val resource = vehicleProfileRepository.getSecondaryVehicleProfile()
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.payload?.let {
                            mProfileVehicleData.postValue(it)
                        }
                    }
                    else -> {
                        showErrorMessage(resource.message)
                    }
                }
            }
        }

        fun deleteVehicle(
            context: Context?,
            selectedVin: String?,
        ) {
            if (selectedVin == null) {
                return
            }

            showProgress()
            viewModelScope.launch {
                userProfileAPIManager.unRegisterVehicle(
                    selectedVin,
                    object :
                        StreamObserver<VehicleRegistrationServiceOuterClass.DeregisterVehicleResponse> {
                        override fun onNext(value: VehicleRegistrationServiceOuterClass.DeregisterVehicleResponse) {
                            context?.runOnUiThread {
                                showSuccessToastMessage(R.string.remove_vehicle_successfully)
                            }
                            getSecondaryProfileData()
                        }

                        override fun onError(t: Throwable) {
                            context?.runOnUiThread {
                                hideProgress()
                                showErrorMessage(t.message)
                            }
                        }

                        override fun onCompleted() {
                            LogTool.d(TAG, "onComplete()")
                        }
                    },
                )
            }
        }
    }
