package com.toyota.oneapp.ui.accountsettings.helpandfeedback.selectvehicle

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.databinding.FragmentSelectVehicleBinding
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SelectVehicleFragment :
    BaseViewModelFragment(),
    SelectVehicleAdapter.OnItemClickListener {
    companion object {
        const val SELECTED_VEHICLE_REQUEST_KEY = "SELECTED_VEHICLE_REQUEST_KEY"
        const val SELECTED_VEHICLE = "SELECTED_VEHICLE"
    }

    private val args: SelectVehicleFragmentArgs by navArgs()
    private val viewModel: SelectVehicleViewModel by viewModels()

    private lateinit var binding: FragmentSelectVehicleBinding
    private lateinit var adapter: SelectVehicleAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentSelectVehicleBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        initializeViews()
        populateViews()

        return binding.root
    }

    // On Vehicle Selected.
    override fun onItemClick(vehicle: VehicleInfo) {
        setFragmentResult(SELECTED_VEHICLE_REQUEST_KEY, bundleOf(SELECTED_VEHICLE to vehicle))
        findNavController().popBackStack()
    }

    private fun initializeViews() {
        viewModel.fetchVehicles(args.vehicleList)
        adapter = SelectVehicleAdapter(emptyList(), this)
        binding.rvVehicles.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            addItemDecoration(DividerItemDecoration(requireContext(), LinearLayout.VERTICAL))
        }

        viewModel.vehicles.observe(viewLifecycleOwner) { vehicles ->
            DataBindingAdapters.setRecyclerViewAdapterData(binding.rvVehicles, vehicles, emptyList())
        }
    }

    private fun populateViews() {
        (requireActivity() as AppCompatActivity).supportActionBar?.title = args.screenTitle
    }
}
