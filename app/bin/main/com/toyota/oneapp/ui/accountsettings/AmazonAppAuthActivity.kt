package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.webkit.WebView
import androidx.activity.result.contract.ActivityResultContract
import androidx.appcompat.widget.Toolbar
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.appauth.AppAuthWebView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AmazonAppAuthActivity : UiBaseActivity() {
    private lateinit var amazonWebview: WebView

    private lateinit var appAuthWebView: AppAuthWebView

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        setContentView(R.layout.activity_amazon_music)
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        amazonWebview = findViewById<WebView>(R.id.amazon_webview)
        setSupportActionBar(toolbar)
        appAuthWebView = AppAuthWebView(getAppAuthData(), amazonWebview)
        appAuthWebView.loadView {
            sendAuthCode(it)
        }
    }

    override fun onBackPressed() {
        if (amazonWebview.canGoBack()) {
            amazonWebview.goBack()
        } else {
            super.onBackPressed()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                appAuthWebView.closeAuthService()
                onBackPressed()
            }
        }
        return true
    }

    private fun sendAuthCode(code: String?) {
        val intent = Intent()
        intent.putExtra(LinkedAccountsActivity.AMAZON_AUTH_CODE, code)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    private fun getAppAuthData(): AppAuthWebView.AppAuthData =
        AppAuthWebView.AppAuthData(
            BuildConfig.AMAZON_CLIENT_ID,
            BuildConfig.AMAZON_REDIRECT_URL,
            BuildConfig.AMAZON_ACCOUNT_SCOPE,
            BuildConfig.AMAZON_AUTHCODE_ENDPOINT,
            BuildConfig.AMAZON_TOKEN_ENDPOINT,
        )

    class Contract : ActivityResultContract<Void?, String?>() {
        override fun createIntent(
            context: Context,
            input: Void?,
        ): Intent = Intent(context, AmazonAppAuthActivity::class.java)

        override fun parseResult(
            resultCode: Int,
            intent: Intent?,
        ): String? {
            if (Activity.RESULT_OK != resultCode) {
                return null
            }
            return intent?.getStringExtra(LinkedAccountsActivity.AMAZON_AUTH_CODE)
        }
    }
}
