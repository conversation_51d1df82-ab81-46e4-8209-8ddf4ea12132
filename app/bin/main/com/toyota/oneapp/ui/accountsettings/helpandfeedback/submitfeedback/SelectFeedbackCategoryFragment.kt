package com.toyota.oneapp.ui.accountsettings.helpandfeedback.submitfeedback

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.databinding.FragmentSelectFeedbackCategoryBinding
import com.toyota.oneapp.model.account.helpandfeedback.FeedbackCategory

class SelectFeedbackCategoryFragment :
    Fragment(),
    FeedbackCategoryAdapter.OnItemClickListener {
    companion object {
        const val SELECTED_CATEGORY_REQUEST_KEY = "SELECTED_CATEGORY_REQUEST_KEY"
        const val SELECTED_CATEGORY = "SELECTED_CATEGORY"
    }

    private lateinit var binding: FragmentSelectFeedbackCategoryBinding

    private lateinit var categories: List<FeedbackCategory>
    private var currentSelectedCategory: FeedbackCategory? = null

    private lateinit var adapter: FeedbackCategoryAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentSelectFeedbackCategoryBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        initializeExtraData()
        initializeViews()

        return binding.root
    }

    // FeedbackCategoryAdapter.OnItemClickListener
    override fun onItemClick(category: FeedbackCategory) {
        setFragmentResult(SELECTED_CATEGORY_REQUEST_KEY, bundleOf(SELECTED_CATEGORY to category))
        findNavController().popBackStack()
    }

    private fun initializeExtraData() {
        val args = SelectFeedbackCategoryFragmentArgs.fromBundle(requireArguments())
        categories = args.categories.toList()
        currentSelectedCategory = args.currentSelectedCategory
    }

    private fun initializeViews() {
        adapter = FeedbackCategoryAdapter(categories, currentSelectedCategory, this)
        binding.rvCategories.run {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            addItemDecoration(DividerItemDecoration(requireContext(), LinearLayout.VERTICAL))
        }
    }
}
