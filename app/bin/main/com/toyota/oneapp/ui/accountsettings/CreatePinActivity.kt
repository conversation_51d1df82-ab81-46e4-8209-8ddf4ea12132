package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityCreatePinBinding
import com.toyota.oneapp.model.account.PinResponse
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

private const val MAX_NO_TIMES = 3

@AndroidEntryPoint
class CreatePinActivity : UiBaseActivity() {
    private val viewModel: ResetPinViewModel by viewModels()
    private var pinEntered: String = ToyotaConstants.EMPTY_STRING
    private var noOfTimes = 1
    private lateinit var dataBinding: ActivityCreatePinBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        dataBinding = DataBindingUtil.setContentView(this, R.layout.activity_create_pin)
        dataBinding.lifecycleOwner = this
        performActivitySetup(dataBinding.resetPinToolbar)

        observeBaseEvents(viewModel)

        dataBinding.apply {
            etPin.setOnPinEnteredListener { onPinEntered(it.toString()) }
        }

        viewModel.updateResponse.observe(this) {
            updateResponse(it)
        }
    }

    private fun onBackButtonPressed(dataBinding: ActivityCreatePinBinding) {
        dataBinding.apply {
            if (registerAccountPinLabel.text == getString(R.string.register_account_pin)) {
                pinEntered = ToyotaConstants.EMPTY_STRING
                finish()
            } else {
                tvCreatePinError.visibility = View.INVISIBLE
                registerAccountPinLabel.text = getString(R.string.register_account_pin)
                pinEntered = ToyotaConstants.EMPTY_STRING
                etPin.text?.clear()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        ToyUtil.openKeyboard(this@CreatePinActivity, dataBinding.etPin)
    }

    override fun onBackPressed() {
        onBackButtonPressed(dataBinding)
    }

    private fun showWeakPinAlert() {
        DialogUtil.showDialog(
            this,
            null,
            getString(R.string.pin_regex_alert),
            getString(R.string.Common_ok),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    showConfirmPinScreen()
                }

                override fun onCancelClick() {
                    clearEnteredPin()
                }
            },
            false,
        )
    }

    private fun showConfirmPinScreen() {
        dataBinding.apply {
            registerAccountPinLabel.text = getString(R.string.confirm_new_pin)
            registerAccountPinDescription.visibility = View.INVISIBLE
            tvCreatePinDetails.visibility = View.INVISIBLE
            etPin.text?.clear()
        }
    }

    private fun clearEnteredPin() {
        dataBinding.apply {
            tvCreatePinError.visibility = View.INVISIBLE
            tvRegexWarningText.visibility = View.VISIBLE
            registerAccountPinLabel.text = getString(R.string.register_account_pin)
            pinEntered = ToyotaConstants.EMPTY_STRING
            etPin.text?.clear()
        }
    }

    private fun updateResponse(response: PinResponse) {
        if (response.result) {
            setResult(Activity.RESULT_OK)
            finish()
        }
    }

    private fun onPinEntered(pin: String) {
        dataBinding.apply {
            tvCreatePinError.visibility = View.INVISIBLE
            tvRegexWarningText.visibility = View.GONE
            when {
                pinEntered.isBlank() -> {
                    pinEntered = pin
                    if (ToyUtil.isPinStrong(pinEntered)) {
                        showConfirmPinScreen()
                    } else {
                        showWeakPinAlert()
                    }
                }
                pinEntered.isNotBlank() && (pin == pinEntered) -> {
                    viewModel.pinReset(pinEntered)
                }
                pinEntered.isNotBlank() && (pin != pinEntered) -> {
                    if (noOfTimes >= MAX_NO_TIMES) {
                        tvCreatePinDetails.visibility = View.VISIBLE
                        registerAccountPinLabel.text = getString(R.string.register_account_pin)
                        etPin.text?.clear()
                        pinEntered = ToyotaConstants.EMPTY_STRING
                        noOfTimes = 1
                    } else {
                        noOfTimes++
                        registerAccountPinDescription.visibility = View.INVISIBLE
                        tvCreatePinError.visibility = View.VISIBLE
                        etPin.text?.clear()
                    }
                }
            }
        }
    }

    class Contract : ActivityResultContract<Void?, Boolean>() {
        override fun parseResult(
            resultCode: Int,
            intent: Intent?,
        ): Boolean = resultCode == Activity.RESULT_OK

        override fun createIntent(
            context: Context,
            input: Void?,
        ): Intent = Intent(context, CreatePinActivity::class.java)
    }
}
