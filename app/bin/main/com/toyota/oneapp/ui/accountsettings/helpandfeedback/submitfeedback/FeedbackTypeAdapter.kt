package com.toyota.oneapp.ui.accountsettings.helpandfeedback.submitfeedback

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemFeedbackTypeBinding
import com.toyota.oneapp.model.account.helpandfeedback.FeedbackType
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

class FeedbackTypeAdapter(
    private var types: List<FeedbackType>,
    private val currentSelectedType: FeedbackType?,
    private val onItemClickListener: OnItemClickListener,
) : RecyclerView.Adapter<FeedbackTypeAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<FeedbackType> {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding =
            ItemFeedbackTypeBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.bind(types[position])
    }

    override fun getItemCount(): Int = types.size

    // BindableRecyclerViewAdapter Methods.
    override fun setData(data: List<FeedbackType>?) {
        this.types = data ?: emptyList()
        notifyDataSetChanged()
    }

    inner class ViewHolder(
        val binding: ItemFeedbackTypeBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        init {
            binding.root.setOnClickListener {
                onItemClickListener.onItemClick(types[adapterPosition])
            }
        }

        fun bind(type: FeedbackType) {
            binding.type = type
            binding.selectedType = currentSelectedType
        }
    }

    interface OnItemClickListener {
        fun onItemClick(type: FeedbackType)
    }
}
