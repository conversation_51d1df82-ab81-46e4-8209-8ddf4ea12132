package com.toyota.oneapp.ui.accountsettings.helpandfeedback

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.setupWithNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityHelpAndFeedbackHostActivityBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HelpAndFeedbackHostActivity : UiBaseActivity() {
    private lateinit var binding: ActivityHelpAndFeedbackHostActivityBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding =
            DataBindingUtil.setContentView(
                this,
                R.layout.activity_help_and_feedback_host_activity,
            )

        initializeViews()
    }

    private fun initializeViews() {
        performActivitySetup(binding.toolbar)
        val navController = findNavController(R.id.nav_host_fragment)
        val appBarConfiguration =
            AppBarConfiguration
                .Builder()
                .setFallbackOnNavigateUpListener {
                    finish()
                    true
                }.build()
        binding.toolbar.setupWithNavController(navController, appBarConfiguration)
    }
}
