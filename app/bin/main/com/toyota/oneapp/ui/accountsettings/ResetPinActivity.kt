package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityResetPinBinding
import com.toyota.oneapp.model.account.PinResponse
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.toast.ToastUtil

private const val MAX_NO_TIMES = 3
const val PIN_STATUS = "pin_status"

@AndroidEntryPoint
class ResetPinActivity : UiBaseActivity() {
    private val viewModel: ResetPinViewModel by viewModels()
    private var pinEntered: String = ToyotaConstants.EMPTY_STRING
    private var noOfTimes = 1
    private lateinit var dataBinding: ActivityResetPinBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        dataBinding = DataBindingUtil.setContentView(this, R.layout.activity_reset_pin)
        dataBinding.lifecycleOwner = this
        observeBaseEvents(viewModel)
        viewModel.pinExists = intent.getBooleanExtra(PIN_STATUS, false)
        dataBinding.apply {
            resetPinToolbar.title =
                if (viewModel.pinExists) {
                    getString(R.string.reset_pin)
                } else {
                    getString(
                        R.string.create_account_pin,
                    )
                }
            tvRestPinLabel.text =
                if (viewModel.pinExists) {
                    getString(R.string.enter_new_pin)
                } else {
                    getString(
                        R.string.create_pin,
                    )
                }
            tvCreatePinDescription.visibility = if (viewModel.pinExists) View.GONE else View.VISIBLE
            etPin.setOnPinEnteredListener {
                onPinEntered(it.toString())
            }
            resetPinToolbar.setNavigationOnClickListener {
                onBackPressed()
            }
        }
        viewModel.updateResponse.observe(this) {
            updateResponse(it)
        }
    }

    override fun onResume() {
        super.onResume()
        ToyUtil.openKeyboard(this@ResetPinActivity, dataBinding.etPin)
    }

    private fun onBackButtonPressed(dataBinding: ActivityResetPinBinding) {
        dataBinding.apply {
            if (tvRestPinLabel.text == getString(R.string.enter_new_pin)) {
                pinEntered = ToyotaConstants.EMPTY_STRING
                finish()
            } else {
                tvResetPinError.visibility = View.INVISIBLE
                tvRestPinLabel.text = getString(R.string.enter_new_pin)
                pinEntered = ToyotaConstants.EMPTY_STRING
                etPin.text?.clear()
            }
        }
    }

    override fun onBackPressed() {
        onBackButtonPressed(dataBinding)
    }

    private fun updateResponse(response: PinResponse) {
        if (response.result) {
            setResult(Activity.RESULT_OK)
            ToastUtil.show(
                this,
                if (viewModel.pinExists) {
                    getString(R.string.reset_pin_success)
                } else {
                    getString(R.string.create_pin_success)
                },
                R.drawable.toast_check,
            )
            finish()
        }
    }

    private fun showWeakPinAlert() {
        DialogUtil.showDialog(
            this,
            null,
            getString(R.string.pin_regex_alert),
            getString(R.string.Common_ok),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    showConfirmPinScreen()
                }

                override fun onCancelClick() {
                    clearEnteredPin()
                }
            },
            false,
        )
    }

    private fun showConfirmPinScreen() {
        dataBinding.apply {
            tvCreatePinDescription.visibility = View.INVISIBLE
            tvRestPinLabel.text = getString(R.string.confirm_new_pin)
            etPin.text?.clear()
        }
    }

    private fun clearEnteredPin() {
        dataBinding.apply {
            tvResetPinError.visibility = View.INVISIBLE
            tvRegexWarningText.visibility = View.VISIBLE
            tvRestPinLabel.text =
                if (viewModel.pinExists) {
                    getString(R.string.enter_new_pin)
                } else {
                    getString(
                        R.string.create_pin,
                    )
                }
            pinEntered = ToyotaConstants.EMPTY_STRING
            etPin.text?.clear()
        }
    }

    private fun onPinEntered(pin: String) {
        dataBinding.apply {
            tvResetPinDetails.visibility = View.INVISIBLE
            tvResetPinError.visibility = View.INVISIBLE
            tvRegexWarningText.visibility = View.GONE
            when {
                pinEntered.isBlank() -> {
                    pinEntered = pin
                    if (ToyUtil.isPinStrong(pinEntered)) {
                        showConfirmPinScreen()
                    } else {
                        showWeakPinAlert()
                    }
                }
                pinEntered.isNotBlank() && (pin == pinEntered) -> {
                    viewModel.pinReset(pinEntered)
                }
                pinEntered.isNotBlank() && (pin != pinEntered) -> {
                    if (noOfTimes >= MAX_NO_TIMES) {
                        tvRestPinLabel.text =
                            if (viewModel.pinExists) {
                                getString(
                                    R.string.enter_new_pin,
                                )
                            } else {
                                getString(R.string.create_pin)
                            }
                        tvResetPinDetails.visibility = View.VISIBLE
                        etPin.text?.clear()
                        pinEntered = ToyotaConstants.EMPTY_STRING
                        noOfTimes = 1
                    } else {
                        noOfTimes++
                        tvRestPinLabel.text = getString(R.string.confirm_pin)
                        tvResetPinError.visibility = View.VISIBLE
                        etPin.text?.clear()
                    }
                }
            }
        }
    }
}
