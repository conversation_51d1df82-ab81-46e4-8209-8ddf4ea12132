package com.toyota.oneapp.ui.accountsettings.helpandfeedback.appfaq

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.StringRes
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.databinding.ItemFaqHeaderBinding
import com.toyota.oneapp.databinding.ItemFaqQuestionBinding
import com.toyota.oneapp.model.account.helpandfeedback.Faq
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.appfaq.model.FaqUIModel
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter
import toyotaone.commonlib.recyclerview.decorator.SectionIdentifier

class AppFaqAdapter(
    private var faqUIModels: List<FaqUIModel>,
    private val onItemCLickListener: OnItemClickListener,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>(),
    BindableRecyclerViewAdapter<FaqUIModel> {
    companion object {
        const val ITEM_TYPE_HEADER = 1
        const val ITEM_TYPE_QUESTION = 2
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder =
        if (viewType == ITEM_TYPE_HEADER) {
            val binding =
                ItemFaqHeaderBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                )
            HeaderViewHolder(binding)
        } else {
            val binding =
                ItemFaqQuestionBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                )
            QuestionViewHolder(binding)
        }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        if (holder.itemViewType == ITEM_TYPE_HEADER) {
            val headerVH = holder as HeaderViewHolder
            val header = faqUIModels[position] as FaqUIModel.Header
            headerVH.bind(header.title)
        } else {
            val questionVH = holder as QuestionViewHolder
            val item = faqUIModels[position] as FaqUIModel.Item
            questionVH.bind(item.faq)
        }
    }

    override fun getItemCount(): Int = faqUIModels.size

    override fun getItemViewType(position: Int): Int =
        when (faqUIModels[position]) {
            is FaqUIModel.Header -> ITEM_TYPE_HEADER
            is FaqUIModel.Item -> ITEM_TYPE_QUESTION
        }

    override fun setData(data: List<FaqUIModel>?) {
        faqUIModels = data ?: emptyList()
        notifyDataSetChanged()
    }

    inner class HeaderViewHolder(
        private val binding: ItemFaqHeaderBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ),
        SectionIdentifier {
        fun bind(
            @StringRes title: Int,
        ) {
            binding.tvTitle.text = itemView.context.getString(title)
        }

        // SectionIdentifier Methods.
        override fun isSection(): Boolean = true
    }

    inner class QuestionViewHolder(
        private val binding: ItemFaqQuestionBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ),
        SectionIdentifier {
        init {
            itemView.setOnClickListener {
                val faqUIItem = faqUIModels[adapterPosition] as FaqUIModel.Item
                onItemCLickListener.onItemClick(faqUIItem.faq)
            }
        }

        fun bind(faq: Faq) {
            binding.faq = faq
        }

        // SectionIdentifier Methods.
        override fun isSection(): Boolean = false
    }

    interface OnItemClickListener {
        fun onItemClick(faq: Faq)
    }
}
