package com.toyota.oneapp.ui.accountsettings

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.account.CustomerAddress
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.ui.BasePresenter
import com.toyota.oneapp.util.ToyUtil
import javax.inject.Inject

class PersonalInfoPresenter
    @Inject
    constructor(
        private val accountManager: AccountAPIManager,
        private val applicationData: ApplicationData,
    ) : BasePresenter<PersonalInfoPresenter.View>() {
        fun savePersonalInfo(
            firstName: String,
            lastName: String,
            customerAddress: CustomerAddress,
        ) {
            view?.showProgressDialog()
            accountManager.sendUpdateUsernameRequest(
                firstName.trim(),
                lastName.trim(),
                brand,
                ToyUtil.getAssociatedSXMGeneration(applicationData.getVehicleList()),
                ToyUtil.getAssociatedSXMBrands(applicationData.getVehicleList()),
                guid,
                oid,
                customerAddress,
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        view?.successUpdateAccount()
                    }

                    override fun onFailError(
                        code: Int,
                        errorMsg: String?,
                    ) {
                        view?.showDialog(errorMsg)
                    }

                    override fun onComplete() {
                        view?.hideProgressDialog()
                    }
                },
            )
        }

        fun savePersonalInfo(
            firstName: String,
            lastName: String,
        ) {
            view?.showProgressDialog()
            accountManager.sendUpdateUsernameRequest(
                firstName.trim(),
                lastName.trim(),
                brand,
                ToyUtil.getAssociatedSXMGeneration(applicationData.getVehicleList()),
                ToyUtil.getAssociatedSXMBrands(applicationData.getVehicleList()),
                guid,
                oid,
                object : BaseCallback<BaseResponse>() {
                    override fun onSuccess(response: BaseResponse) {
                        view?.successUpdateAccount()
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        view?.showDialog(errorMsg)
                    }

                    override fun onComplete() {
                        view?.hideProgressDialog()
                    }
                },
            )
        }

        interface View : BaseView {
            fun successUpdateAccount()

            fun showAccountInfo(
                firstName: String?,
                lastName: String?,
                email: String?,
                customerAddress: CustomerAddress?,
                preferredLanguage: String?,
                phoneNumber: String?,
            )
        }
    }
