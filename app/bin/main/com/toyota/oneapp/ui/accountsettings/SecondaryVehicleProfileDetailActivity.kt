package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityVehicleProfileDetailBinding
import com.toyota.oneapp.model.vehicle.SecondaryVehicleProfilePayload
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

@AndroidEntryPoint
class SecondaryVehicleProfileDetailActivity : UiBaseActivity() {
    private var secondaryVehicleProfilePayload: SecondaryVehicleProfilePayload? = null

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        val dataBinding =
            DataBindingUtil.setContentView<ActivityVehicleProfileDetailBinding>(
                this,
                R.layout.activity_vehicle_profile_detail,
            )
        val toolbar = findViewById<View>(R.id.vehicle_profile_toolbar) as Toolbar
        setSupportActionBar(toolbar)
        secondaryVehicleProfilePayload = intent.getParcelableExtra(ToyotaConstants.VEHICLE_PROFILE) as? SecondaryVehicleProfilePayload

        dataBinding.apply {
            removeVehicleBtn.setOnClickListener {
                openRemoveVehicleProfileDialog()
            }

            vehicleProfileToolbar.title = secondaryVehicleProfilePayload?.modelName
            DataBindingAdapters.loadImage(imgVehicleProfileDetail, secondaryVehicleProfilePayload?.image, null)
            vehicleProfileDetailYear.text = "${secondaryVehicleProfilePayload?.modelYear} ${secondaryVehicleProfilePayload?.modelName}"
            vehicleProfileDescription.text = secondaryVehicleProfilePayload?.modelDescription
            vehicleProfileVin.text = secondaryVehicleProfilePayload?.vin
        }
    }

    private fun openRemoveVehicleProfileDialog() {
        DialogUtil.showDialog(
            this,
            getString(R.string.remove_profile_vehicle),
            getString(R.string.remove_profile_vehicle_message),
            getString(R.string.Common_remove),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    updateSavedVehicleProfile()
                }

                override fun onCancelClick() {
                    // Unused
                }
            },
            false,
        )
    }

    private fun updateSavedVehicleProfile() {
        val returnIntent =
            Intent().run {
                putExtra(
                    ToyotaConstants.SELECTED_VEHICLE,
                    secondaryVehicleProfilePayload?.vin,
                )
            }
        setResult(Activity.RESULT_OK, returnIntent)
        finish()
    }
}
