package com.toyota.oneapp.ui.accountsettings

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityDeleteAccountBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.Brand
import com.toyota.oneapp.util.IntentUtil.getOADashBoardIntent
import com.toyota.oneapp.util.ToyUtil
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DeleteAccountActivity : UiBaseActivity() {
    private var _binding: ActivityDeleteAccountBinding? = null
    private val binding get() = _binding!!

    companion object {
        fun getIntent(context: Context) {
            context.startActivity(Intent(context, DeleteAccountActivity::class.java))
        }
    }

    private val viewModel: DeleteAccountViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        _binding =
            DataBindingUtil
                .setContentView<ActivityDeleteAccountBinding>(
                    this,
                    R.layout.activity_delete_account,
                ).apply {
                    lifecycleOwner = this@DeleteAccountActivity
//            viewModel = <EMAIL>
                }
        observeBaseEvents(viewModel)
        setSupportActionBar(binding.toolbar)

        viewModel.state.observe(this) {
            configureToolbar(it.showAppBar, it.appBarTitle)
            showSuccess(it.showSuccess)
            showRegionalContent(it.showUSRegionContent, it.showNonUSRegionContent)
        }

        setUpOnClickListeners()

        viewModel.onDeleteAccountNavigationEvents.observe(this) {
            when (it) {
                is DeleteAccountNavigationEvent.OnDoNotSellMyInformation ->
                    ToyUtil.openCustomChromeTab(
                        this,
                        getString(R.string.do_not_share_my_info_link),
                    )
                is DeleteAccountNavigationEvent.OnDeleteMyPersonalInformation ->
                    ToyUtil.openCustomChromeTab(
                        this,
                        if (BuildConfig.APP_BRAND == Brand.LEXUS.appBrand) {
                            getString(R.string.delete_account_lexus_link)
                        } else {
                            getString(R.string.delete_account_toyota_subaru_link)
                        },
                    )
                is DeleteAccountNavigationEvent.ErrorAlreadySubmitted ->
                    showDialog(
                        getString(R.string.delete_account_already_requested),
                    )
                is DeleteAccountNavigationEvent.ErrorMissingHeader ->
                    showDialog(
                        String.format(
                            getString(R.string.delete_account_missing_info),
                            getString(R.string.app_name),
                            getString(R.string.Customer_Service),
                            ToyUtil.getPhoneNO(
                                this,
                                viewModel.languageManager.getCurrentRegion().regionCode,
                                BuildConfig.APP_BRAND,
                            ),
                        ),
                    )
                is DeleteAccountNavigationEvent.BackToDashboard -> {
                    startActivity(getOADashBoardIntent(context = this))
                    finish()
                }
                is DeleteAccountNavigationEvent.Cancel -> finish()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }

    private fun configureToolbar(
        showAppBar: Int,
        toolbarTitle: String,
    ) {
        binding.toolbar.visibility = showAppBar
        binding.toolbar.title = toolbarTitle
    }

    private fun showSuccess(showSuccess: Int) {
        binding.deleteAccountSuccessLayout.visibility = showSuccess
    }

    private fun showRegionalContent(
        showUsRegionalContent: Boolean,
        showNonUsRegionalContent: Boolean,
    ) {
        binding.deleteAccountUsRegionalContentLayout.visibility =
            if (showUsRegionalContent) View.VISIBLE else View.GONE
        binding.deleteAccountNonUsRegionalContentLayout.visibility =
            if (showNonUsRegionalContent) View.VISIBLE else View.GONE
    }

    private fun setUpOnClickListeners() {
        binding.deleteMyPersonalInfo.setOnClickListener {
            viewModel.onDeleteMyPersonalInformation()
        }
        binding.privacyChoicesLayout.setOnClickListener {
            viewModel.onDoNotSellMyInformation()
        }
        binding.btnDeleteAccountConfirm.setOnClickListener {
            viewModel.onDeleteAccountConfirm()
        }
        binding.btnDeleteAccountCancel.setOnClickListener {
            viewModel.onDeleteAccountCancel()
        }
        binding.btnDeleteAccountSuccess.setOnClickListener {
            viewModel.onDeleteAccountSuccessBackToDashboard()
        }
    }
}
