package com.toyota.oneapp.ui.accountsettings

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.R
import com.toyota.oneapp.account.AccountManager
import com.toyota.oneapp.adapter.NotificationSettingsEvent
import com.toyota.oneapp.adapter.NotificationsPreferenceAdapter
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.model.account.NotificationPreferenceResponse
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.callback.LogoutCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class NotificationSettingsViewModel
    @Inject
    constructor(
        private val accountManager: AccountAPIManager,
        private val signOutAccountManager: AccountManager,
        private val analyticsLogger: AnalyticsLogger,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : BaseViewModel(),
        NotificationsPreferenceAdapter.OnEventClickListener {
        val notificationsPreferenceAdapter: NotificationsPreferenceAdapter =
            NotificationsPreferenceAdapter(
                analyticsLogger,
                oneAppPreferenceModel,
            ).apply {
                setOnEventClickListener(this@NotificationSettingsViewModel)
            }

        private var item: NotificationPreferenceResponse? = null

        init {
            getNotificationsRequest()
        }

        fun bind(item: NotificationPreferenceResponse) {
            this.item = item
            notificationsPreferenceAdapter.updatePreferencesList(item.payload)
        }

        private val _events = MutableLiveData<NotificationSettingsEvent>()
        val events: LiveData<NotificationSettingsEvent> = _events

        override fun onEventClick(event: NotificationSettingsEvent) {
            _events.value = event
        }

        fun signOut() {
            showProgress()
            signOutAccountManager.logout(
                object : LogoutCallback() {
                    override fun onLogoutAPISuccess() {
                        hideProgress()
                        _events.value = NotificationSettingsEvent.SignOut
                    }

                    override fun onLogoutAPIError(errorMsg: String?) {
                        hideProgress()
                        _events.value = NotificationSettingsEvent.SignOut
                    }
                },
            )
        }

        private fun getNotificationsRequest() {
            analyticsLogger.logEvent(AnalyticsEvent.NOTIFICATION_PREFERENCE_FETCHED)
            showProgress()
            accountManager.sendGetNotificationsRequest(
                object : BaseCallback<NotificationPreferenceResponse>() {
                    override fun onSuccess(response: NotificationPreferenceResponse) {
                        bind(response)
                    }

                    override fun onFailError(
                        code: Int,
                        errorMsg: String?,
                    ) {
                        showErrorMessage(errorMsg)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun sendNotificationsUpdate(finishOnComplete: Boolean) {
            val list = notificationsPreferenceAdapter.getList()

            var completed = true
            if (notificationsPreferenceAdapter.updated && list.isNotEmpty()) {
                showProgress()
                for (item in list) {
                    accountManager.sendUpdateNotificationsRequest(
                        item,
                        object : BaseCallback<BaseResponse>() {
                            override fun onSuccess(response: BaseResponse) {
                                analyticsLogger.logEvent(
                                    AnalyticsEvent.NOTIFICATION_PREFERENCE_UPDATE_SUCCESSFUL,
                                )
                                if (finishOnComplete) finishActivity()
                            }

                            override fun onError() {
                                completed = false
                            }

                            override fun onComplete() {
                                hideProgress()
                                if (!completed) {
                                    analyticsLogger.logEvent(
                                        AnalyticsEvent.NOTIFICATION_PREFERENCE_UPDATE_UNSUCCESSFUL,
                                    )
                                    showErrorMessage(R.string.np_error)
                                }
                            }
                        },
                    )
                }
            } else {
                if (finishOnComplete) finishActivity()
            }
        }
    }
