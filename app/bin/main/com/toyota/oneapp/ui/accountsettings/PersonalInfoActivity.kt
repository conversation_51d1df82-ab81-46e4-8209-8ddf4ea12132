package com.toyota.oneapp.ui.accountsettings

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.ActivityPersonalInfoBinding
import com.toyota.oneapp.model.account.CustomerAddress
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.ui.MVPBaseActivity
import com.toyota.oneapp.ui.widget.toCustomerAddress
import com.toyota.oneapp.ui.widget.validator.NonBlankValidator
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.language.SupportedLanguages
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.toast.ToastUtil
import javax.inject.Inject

@AndroidEntryPoint
class PersonalInfoActivity :
    MVPBaseActivity<PersonalInfoPresenter>(),
    PersonalInfoPresenter.View {
    @Inject
    lateinit var presenter: PersonalInfoPresenter

    @Inject lateinit var analyticsLogger: AnalyticsLogger

    private var accountInfo: AccountInfoSubscriber? = null

    private lateinit var firstNameValidator: NonBlankValidator
    private lateinit var lastNameValidator: NonBlankValidator

    private lateinit var binding: ActivityPersonalInfoBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding = ActivityPersonalInfoBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.tilPhoneNumber.setOnKeyListener(null)
        binding.etPhoneNumber.setOnTouchListener(unableEditClickListener)
        binding.tilEmailAddress.setOnKeyListener(null)
        binding.etEmailAddress.setOnTouchListener(unableEditClickListener)
        binding.tilPreferLanguage.setOnKeyListener(null)
        binding.etPreferLanguage.setOnTouchListener(unableEditClickListener)

        val extraAccountInfo =
            intent.getParcelableExtra<AccountInfoSubscriber?>(
                ToyotaConstants.ACCOUNT_INFO,
            )
        if (extraAccountInfo == null) {
            finish()
            return
        }
        accountInfo = extraAccountInfo

        showAccountInfo(
            accountInfo?.firstName ?: presenter.userFirstName,
            accountInfo?.lastName ?: presenter.userLastName,
            accountInfo?.customerEmails?.get(0)?.emailAddress,
            accountInfo?.customerAddresses?.get(0)
                ?: CustomerAddress("HOME", "", null, null, null, "US"),
            accountInfo?.preferredLanguage,
            accountInfo?.customerPhoneNumbers?.firstOrNull()?.phoneNumber,
        )
        setupToolBar()

        firstNameValidator =
            NonBlankValidator(
                binding.tilFirstName,
                binding.etFirstName,
                R.string.AccountSettings_name_cant_be_blank,
            ).also {
                binding.etFirstName.addTextChangedListener(it)
                binding.etFirstName.onFocusChangeListener = it
            }
        lastNameValidator =
            NonBlankValidator(
                binding.tilLastName,
                binding.etLastName,
                R.string.AccountSettings_name_cant_be_blank,
            ).also {
                binding.etLastName.addTextChangedListener(it)
                binding.etLastName.onFocusChangeListener = it
            }
    }

    private fun setupToolBar() {
        setSupportActionBar(binding.personalInfoToolbar)
        binding.btnSave.setOnClickListener {
            analyticsLogger.logEventWithParameter(
                AnalyticsEventParam.PROFILE_INFO_SAVE,
                AnalyticsEventParam.PROFILE_UPDATE_NAME,
            )
            if (firstNameValidator.validate(true).and(
                    lastNameValidator.validate(true),
                )
            ) {
                if (binding.address.validate()) {
                    presenter.savePersonalInfo(
                        binding.etFirstName.text.toString(),
                        binding.etLastName.text.toString(),
                        binding.address.address.toCustomerAddress("HOME"),
                    )
                } else {
                    presenter.savePersonalInfo(
                        binding.etFirstName.text.toString(),
                        binding.etLastName.text.toString(),
                    )
                }
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }

    override fun successUpdateAccount() {
        // close the activity
        setResult(Activity.RESULT_OK)
        finish()
    }

    override fun createPresenter(): PersonalInfoPresenter = this.presenter

    override fun showAccountInfo(
        firstName: String?,
        lastName: String?,
        email: String?,
        customerAddress: CustomerAddress?,
        preferredLanguage: String?,
        phoneNumber: String?,
    ) {
        if (firstName != null) {
            binding.etFirstName.setText(firstName)
        }
        if (lastName != null) {
            binding.etLastName.setText(lastName)
        }
        if (email != null) {
            binding.etEmailAddress.setText(email)
        }
        if (phoneNumber != null) {
            binding.etPhoneNumber.setText(phoneNumber)
        }

        customerAddress?.let {
            binding.address.setAddress(it)
        }
        if (preferredLanguage != null) {
            val languageCode = SupportedLanguages.getLanguageCode(preferredLanguage)
            binding.etPreferLanguage.setText(
                SupportedLanguages.getLanguageNameFromAcronym(languageCode, this),
            )
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private val unableEditClickListener =
        View.OnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                when (v.id) {
                    R.id.et_prefer_language -> {
                        ToastUtil.show(
                            this,
                            getString(R.string.AccountSettings_diable_area_toast),
                            R.drawable.toast_remove,
                        )
                    }
                    R.id.et_phone_number -> {
                        analyticsLogger.logEventWithParameter(
                            AnalyticsEventParam.PROFILE_INFO_SAVE,
                            AnalyticsEventParam.PHONE,
                        )
                        startActivity(
                            Intent(this, UpdateEmailPhoneActivity::class.java)
                                .putExtra("type", 0)
                                .putExtra("AccountInfo", accountInfo),
                        )
                    }
                    R.id.et_email_address -> {
                        analyticsLogger.logEventWithParameter(
                            AnalyticsEventParam.PROFILE_INFO_SAVE,
                            AnalyticsEventParam.EMAIL,
                        )
                        startActivity(
                            Intent(this, UpdateEmailPhoneActivity::class.java)
                                .putExtra("type", 1)
                                .putExtra("AccountInfo", accountInfo),
                        )
                    }
                    else -> {
                    }
                }
            }
            false
        }
}
