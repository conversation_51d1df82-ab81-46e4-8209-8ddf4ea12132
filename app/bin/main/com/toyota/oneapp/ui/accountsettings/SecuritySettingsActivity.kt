package com.toyota.oneapp.ui.accountsettings

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.component.IDPHelper
import com.toyota.oneapp.databinding.ActivityAccountSecuritySettingsBinding
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.BiometryUtil
import dagger.hilt.android.AndroidEntryPoint
import org.forgerock.android.auth.ui.FRNative
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import java.security.Signature
import javax.inject.Inject

private const val REFRESH_TOKEN_BUFF = 2 * 60

@AndroidEntryPoint
class SecuritySettingsActivity : UiBaseActivity() {
    @Inject
    lateinit var regionManager: RegionManager

    @Inject
    lateinit var oneAppPreferenceModel: OneAppPreferenceModel

    private lateinit var activityMainBinding: ActivityAccountSecuritySettingsBinding
    private val viewModel: SecuritySettingsViewModel by viewModels()
    private val pinResetLauncher =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result: ActivityResult? ->
            if (result?.resultCode == RESULT_OK) {
                viewModel.checkForPIN()
            }
        }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        activityMainBinding =
            DataBindingUtil.setContentView<ActivityAccountSecuritySettingsBinding>(
                this,
                R.layout.activity_account_security_settings,
            )
        activityMainBinding.lifecycleOwner = this
        activityMainBinding.viewModel = viewModel
        viewModel.isSystemBiometricPresent =
            BiometryUtil.canAuthenticate(
                this@SecuritySettingsActivity,
            )
        setSupportActionBar(activityMainBinding.accountSecurityToolbar)
        observeEvents()
        activityMainBinding.apply {
            val appName = getString(R.string.app_name)
            messageauthentication.text =
                getString(R.string.Accountsecurity_message2, appName)
            resetPinTextview.setOnClickListener {
                launchResetPin()
            }
            profileSavedTextview.setOnClickListener {
                launchSavedVehicleProfile()
            }

            deleteAccount.setOnClickListener {
                DeleteAccountActivity.getIntent(this@SecuritySettingsActivity)
            }
        }

        FRNative.initResultLauncher(this)
        viewModel.checkForPIN()
    }

    override fun onResume() {
        super.onResume()
        viewModel.onInit(this)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }

    private fun observeEvents() {
        observeBaseEvents(viewModel)
        viewModel.checkForPin.observe(this) { item ->
            item?.let {
                activityMainBinding.accountPinLayout.visibility = View.VISIBLE
                activityMainBinding.resetPinTextview.text =
                    if (item) {
                        getString(R.string.reset_pin)
                    } else {
                        getString(
                            R.string.create_account_pin,
                        )
                    }
            }
        }

        viewModel.sendToResetPin.observe(this) {
            if (it) {
                sendToResetPin()
            } else {
                doLogin()
            }
        }
        viewModel.onUserAuthError.observe(this) {
            showErrorToast(getString(R.string.login_as_same_user))
        }

        viewModel.securitySettingsViewModelNavigationEvent.observe(this) {
            when (it) {
                is SecuritySettingsViewModelNavigationEvent.RequiredAuth -> {
                    startActivity(Intent(this, SecuritySettingsExpActivity::class.java))
                }
                is SecuritySettingsViewModelNavigationEvent.RequireAuthColor ->
                    activityMainBinding.account5minutes.setTextColor(
                        getColor(if (it.checked) R.color.textBlack else R.color.lightGray),
                    )
                is SecuritySettingsViewModelNavigationEvent.ShowBiometricNotEnabledDialog -> showBiometricNotEnabledDialog()
            }
        }
    }

    private fun launchSavedVehicleProfile() {
        startActivity(Intent(this, SavedVehicleProfileActivity::class.java))
    }

    private fun showBiometricNotEnabledDialog() {
        showDialog(getString(R.string.SecuritySettings_No_Biometric_Message))
    }

    private fun launchResetPin() {
        if (IDPHelper.isRefreshTokenValid(REFRESH_TOKEN_BUFF, oneAppPreferenceModel)) {
            sendToResetPin()
        } else {
            doLogin()
        }
    }

    private fun sendToResetPin() {
        val intent =
            Intent(this, ResetPinActivity::class.java).apply {
                putExtra(PIN_STATUS, viewModel.checkForPin.value)
            }
        pinResetLauncher.launch(intent)
    }

    private fun showBiometricDialog() {
        if (BiometryUtil.canAuthenticate(this)) {
            BiometryUtil.showBiometryDialog(
                this,
                getString(R.string.Biometry_dialog_title),
                getString(R.string.Biometry_login_to_OneApp),
                getString(R.string.Common_cancel),
                object : BiometryUtil.BiometryCallback {
                    override fun onSuccess(signature: Signature?) {
                        sendToResetPin()
                    }

                    override fun onFailure() {
                        doLogin()
                    }

                    override fun onLockedFailure() {
                        doLogin()
                    }

                    override fun onCancelled() {
                        doLogin()
                    }

                    override fun onError(errorCode: Int) {
                        doLogin()
                    }
                },
            )
        } else {
            DialogUtil.showMessageDialog(
                this,
                null,
                getString(R.string.show_login_msg),
                getString(R.string.Common_ok),
                null,
                object :
                    OnCusDialogInterface {
                    override fun onConfirmClick() {
                        viewModel.doSignIn(this@SecuritySettingsActivity)
                    }

                    override fun onCancelClick() {
                    }
                },
                true,
            )
        }
    }

    private fun doLogin() {
        DialogUtil.showMessageDialog(
            this,
            getString(R.string.verification_title),
            getString(
                R.string.verification_message,
            ),
            getString(R.string.Common_confirm),
            getString(R.string.Common_cancel),
            object :
                OnCusDialogInterface {
                override fun onConfirmClick() {
                    showBiometricDialog()
                }

                override fun onCancelClick() {
                }
            },
            true,
        )
    }

    companion object {
        const val TAG = "SecuritySettingsActivity"
    }
}
