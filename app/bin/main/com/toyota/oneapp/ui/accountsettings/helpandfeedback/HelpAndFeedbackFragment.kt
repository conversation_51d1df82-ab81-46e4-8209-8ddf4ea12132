package com.toyota.oneapp.ui.accountsettings.helpandfeedback

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.FragmentHelpAndFeedbackBinding
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.HelpAndFeedbackFragmentDirections.Companion.actionHelpAndFeedbackFragmentToAppFaqFragment
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.HelpAndFeedbackFragmentDirections.Companion.actionHelpAndFeedbackFragmentToContactUsFragment
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.HelpAndFeedbackFragmentDirections.Companion.actionHelpAndFeedbackFragmentToSelectVehicleFragment
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.HelpAndFeedbackFragmentDirections.Companion.actionHelpAndFeedbackFragmentToSubmitFeedbackFragment
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.HelpAndFeedbackFragmentDirections.Companion.actionHelpAndFeedbackFragmentToVehicleSupportFragment
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.selectvehicle.SelectVehicleFragment
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class HelpAndFeedbackFragment : BaseViewModelFragment() {
    private val viewModel: HelpAndFeedbackViewModel by viewModels()

    private lateinit var binding: FragmentHelpAndFeedbackBinding

    @Inject lateinit var languageManger: LanguageManager

    @Inject lateinit var analyticsLogger: AnalyticsLogger

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeBaseEvents(viewModel)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentHelpAndFeedbackBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.managePreferenceLayout.visibility = if (enableManagePreference() && !ToyUtil.isSubaru()) View.VISIBLE else View.GONE
        binding.submitFeedbackLayout.visibility =
            if (viewModel.hasVehicle.value == true && viewModel.canShowSubmitFeedback.value == true) {
                View.VISIBLE
            } else {
                View.GONE
            }
        initView()

        setUpViewModelBindings()

        return binding.root
    }

    private fun initView() {
        viewModel.checkForLMEX()

        viewModel.canShowAppFAQ.observe(viewLifecycleOwner) {
            DataBindingAdapters.setIsVisible(binding.appFAQLayout, it)
        }
        binding.appFAQLayout.setOnClickListener {
            viewModel.onHelpAndFeedbackOptionClicked(HelpAndFeedbackOptions.APP_FAQ)
        }

        viewModel.hasVehicle.observe(viewLifecycleOwner) {
            DataBindingAdapters.setIsVisible(binding.vehicleSupportLayout, it)
            if (!ToyUtil.isSubaru()) {
                DataBindingAdapters.setIsVisible(binding.submitFeedbackLayout, it)
            }
        }

        binding.vehicleSupportLayout.setOnClickListener {
            viewModel.onHelpAndFeedbackOptionClicked(HelpAndFeedbackOptions.VEHICLE_SUPPORT)
        }
        binding.submitFeedbackLayout.setOnClickListener {
            viewModel.onHelpAndFeedbackOptionClicked(HelpAndFeedbackOptions.SUBMIT_FEEDBACK)
        }
        binding.contactUsLayout.setOnClickListener {
            viewModel.onHelpAndFeedbackOptionClicked(HelpAndFeedbackOptions.CONTACT_US)
        }
        binding.managePreferenceLayout.setOnClickListener {
            viewModel.onManagePreferenceClicked()
        }
    }

    private fun setUpViewModelBindings() {
        viewModel.helpAndFeedbackNavigationEvent.observe(viewLifecycleOwner) {
            handleHelpAndFeedbackNavigationEvent(it)
        }
    }

    private fun handleHelpAndFeedbackNavigationEvent(navigationEvent: HelpAndFeedbackViewModel.NavigationEvent) {
        when (navigationEvent) {
            is HelpAndFeedbackViewModel.NavigationEvent.NavigateToSelectVehicleScreen -> {
                navigateToSelectVehicleScreen(navigationEvent)
            }

            is HelpAndFeedbackViewModel.NavigationEvent.NavigateToAppFaqScreen -> {
                val action = actionHelpAndFeedbackFragmentToAppFaqFragment(navigationEvent.vehicle)
                findNavController().navigate(action)
            }

            is HelpAndFeedbackViewModel.NavigationEvent.NavigateToVehicleSupportScreen -> {
                val action =
                    actionHelpAndFeedbackFragmentToVehicleSupportFragment(
                        navigationEvent.vehicle,
                    )
                findNavController().navigate(action)
            }

            is HelpAndFeedbackViewModel.NavigationEvent.NavigateToSubmitFeedbackScreen -> {
                val action =
                    actionHelpAndFeedbackFragmentToSubmitFeedbackFragment(
                        navigationEvent.vehicle,
                    )
                findNavController().navigate(action)
            }

            is HelpAndFeedbackViewModel.NavigationEvent.NavigateToContactUsScreen -> {
                val action =
                    actionHelpAndFeedbackFragmentToContactUsFragment(
                        navigationEvent.vehicle,
                    )
                findNavController().navigate(action)
            }
            is HelpAndFeedbackViewModel.NavigationEvent.NavigateExternalLink -> {
                analyticsLogger.logEvent(AnalyticsEvent.MANAGE_PREFERENCE)
                startActivity(
                    Intent(
                        Intent.ACTION_VIEW,
                        Uri.parse(resources.getString(R.string.manage_preference_url)),
                    ),
                )
            }
        }
    }

    private fun navigateToSelectVehicleScreen(event: HelpAndFeedbackViewModel.NavigationEvent.NavigateToSelectVehicleScreen) {
        // Register for Vehicle selection result.
        setFragmentResultListener(SelectVehicleFragment.SELECTED_VEHICLE_REQUEST_KEY, listener = { _, bundle ->
            val vehicle: VehicleInfo? = bundle.getParcelable(SelectVehicleFragment.SELECTED_VEHICLE)
            vehicle?.let {
                viewModel.onVehicleSelected(event.option, it)
            }
        })

        val actionSelectVehicle =
            actionHelpAndFeedbackFragmentToSelectVehicleFragment(
                screenTitle = getSelectVehicleScreenTitle(event.option),
                option = event.option,
                vehicleList = event.vehicles,
            )
        findNavController().navigate(actionSelectVehicle)
    }

    private fun getSelectVehicleScreenTitle(option: HelpAndFeedbackOptions): String =
        getString(
            when (option) {
                HelpAndFeedbackOptions.APP_FAQ -> {
                    R.string.app_FAQ
                }
                HelpAndFeedbackOptions.VEHICLE_SUPPORT -> {
                    R.string.vehicle_support
                }
                HelpAndFeedbackOptions.SUBMIT_FEEDBACK -> {
                    R.string.submit_feedback
                }
                HelpAndFeedbackOptions.CONTACT_US -> {
                    R.string.contact_us
                }
            },
        )

    private fun enableManagePreference(): Boolean {
        val local: Locale = languageManger.getCurrentLocale()
        val regionItem = AppLanguageUtils.getRegion(local.country)
        regionItem?.regionCode?.let {
            return (it.equals("US", ignoreCase = true) || it.equals("HI", ignoreCase = true))
        } ?: return false
    }
}
