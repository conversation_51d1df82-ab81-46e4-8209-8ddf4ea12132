package com.toyota.oneapp.ui.accountsettings.personalinfo

import androidx.lifecycle.LiveData
import com.toyota.oneapp.model.account.CustomerAddress
import com.toyota.oneapp.model.subscription.HomeAddressApiResponse
import com.toyota.oneapp.model.subscription.Payload
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class BillingAddressViewModel
    @Inject
    constructor(
        private val accountManager: AccountAPIManager,
    ) : BaseViewModel() {
        private val onBackClicked = SingleLiveEvent<Any>()
        private val billingAddressSaveBtnClick = SingleLiveEvent<Any>()
        val successResponseObserver = SingleLiveEvent<Payload>()

        val onBackButtonClicked: LiveData<Any>
            get() = onBackClicked

        val billingAddressSaveBtnClicked: LiveData<Any>
            get() = billingAddressSaveBtnClick

        private val apiErrorObserver = SingleLiveEvent<String?>()

        val apiErrorObserverEvent: LiveData<String?>
            get() = apiErrorObserver

        fun verifyHomeAddress(
            city: String?,
            country: String?,
            state: String?,
            zipCode: String?,
            address: String?,
        ) {
            showProgress()
            accountManager.verifyHomeAddress(
                "HOME",
                city,
                country,
                state,
                zipCode,
                address,
                object : BaseCallback<HomeAddressApiResponse?>() {
                    override fun onSuccess(response: HomeAddressApiResponse?) {
                        successResponseObserver.postValue(response?.payload)
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        apiErrorObserver.postValue(errorMsg)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun onBackButtonClicked() {
            onBackClicked.call()
        }

        fun billingAddressSaveBtnClicked() {
            billingAddressSaveBtnClick.call()
        }

        fun isAccountUpdated(
            streetAddress: String?,
            city: String?,
            country: String?,
            zipCode: String?,
            state: String?,
            customerAddress: CustomerAddress?,
        ): Boolean =
            if (customerAddress == null) {
                false
            } else {
                (
                    streetAddress.equals(customerAddress.address, ignoreCase = true) &&
                        city.equals(customerAddress.city, ignoreCase = true) &&
                        country.equals(customerAddress.country, ignoreCase = true) &&
                        zipCode.equals(customerAddress.zipCode, ignoreCase = true) &&
                        state.equals(customerAddress.state, ignoreCase = true)
                )
            }
    }
