package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.view.MenuItem
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityUpdateEmailPhoneBinding
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.ui.MVPBaseActivity
import com.toyota.oneapp.ui.accountsettings.personalinfo.SmsHangTightActivity
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.toast.ToastUtil
import javax.inject.Inject

@Suppress("ktlint:standard:max-line-length")
@AndroidEntryPoint
class UpdateEmailPhoneActivity :
    MVPBaseActivity<UpdateEmailPhonePresenter>(),
    UpdateEmailPhonePresenter.View {
    private lateinit var binding: ActivityUpdateEmailPhoneBinding

    @Inject
    lateinit var presenter: UpdateEmailPhonePresenter

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @Inject
    lateinit var applicationData: ApplicationData

    @Inject
    lateinit var regionManager: RegionManager

    val emailRegex =
        "^(?i)[-a-z0-9_]+(\\.[-a-z0-9_]+)*@(?!yopmail|mailinator)([a-z0-9_][-a-z0-9_]*(\\.(?!yopmail|mailinator)[-a-z0-9_]+)*\\.(AERO|ARPA|BIZ|COM|COOP|EDU|GOV|INFO|INT|MIL|MUSEUM|NAME|NET|ORG|PRO|TRAVEL|MOBI|LAW|BANK|SPACE|TECH|LTD|BIS|GLOBAL|DOSSIER|AIRBUS|ONCA|GOM|STUDIO|CHEESE|STORE|TEAM|UAE|TMNA|aero|arpa|biz|com|coop|edu|gov|info|int|mil|museum|name|net|org|pro|travel|mobi|law|bank|space|tech|ltd|bis|global|dossier|airbus|onca|gom|studio|cheese|store|team|uae|tmna|[a-z][a-z])|([0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}))(:[0-9]{1,5})?\$"
    val phoneRegex = "^\\s*(?:\\+?(\\d{1,3}))?(\\d{3})[-. )]*(\\d{3})(\\d{4})(?: *x(\\d+))?\\s*\$"

    private val phoneVerificationContract =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            phoneVerificationResult(result.resultCode)
        }

    private fun phoneVerificationResult(resultCode: Int) {
        when (resultCode) {
            Activity.RESULT_OK -> {
                presenter.getConsentStatus()
            }

            else -> {
                showSmsOptErrorDialog()
            }
        }
    }

    override fun createPresenter(): UpdateEmailPhonePresenter = presenter

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding =
            DataBindingUtil.setContentView<ActivityUpdateEmailPhoneBinding>(
                this,
                R.layout.activity_update_email_phone,
            )
        dispatchTouchEventEnable = false
        val toolbar = findViewById<View>(R.id.toolbar) as Toolbar
        presenter.editType = intent.getIntExtra(ToyotaConstants.EDIT_TYPE, 0)
        try {
            presenter.accountInfo = intent.getParcelableExtra(ToyotaConstants.EDITING_ACCOUNTINFO)
                ?: throw IllegalArgumentException(
                    getString(R.string.consent_account_info_argument_exception),
                )
        } catch (e: IllegalArgumentException) {
            LogTool.e(TAG, e.message)
        }

        presenter.editName =
            when (presenter.editType) {
                1 -> getString(R.string.AccountSettings_email_address)
                else -> getString(R.string.PersonalInfoActivity_phone_number)
            }

        presenter.editValue =
            when (presenter.editType) {
                1 -> presenter.accountInfo.customerEmails[0].emailAddress
                else ->
                    if (!presenter.accountInfo.customerPhoneNumbers.isNullOrEmpty() &&
                        !presenter.accountInfo.customerPhoneNumbers[0]
                            .phoneNumber
                            .isNullOrBlank()
                    ) {
                        presenter.accountInfo.customerPhoneNumbers[0].phoneNumber!!
                    } else {
                        ""
                    }
            }

        toolbar.title = String.format("%s %s", getString(R.string.common_edit), presenter.editName)
        binding.tilEditEmailPhone.hint = presenter.editName
        binding.etEditEmailPhone.inputType =
            when (presenter.editType) {
                1 -> InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS
                else -> InputType.TYPE_CLASS_PHONE
            }

        DataBindingAdapters.setRegexFilter(
            binding.etEditEmailPhone,
            if (presenter.editType == 1) emailRegex else phoneRegex,
        )
        setSupportActionBar(toolbar)

        binding.tvUpdateEmailOrPhoneSubHeader.text =
            String.format(
                getString(R.string.Update_email_phone_disclaimer),
                presenter.editName,
            )

        binding.updateEmailOrPhone.setOnClickListener {
            hideSoftKeyboard()
            val countryCode =
                presenter.accountInfo.customerPhoneNumbers
                    ?.firstOrNull()
                    ?.let { "+${it.countryCode}" }
            var mobileNumber = binding.etEditEmailPhone.text.toString()
            if (countryCode?.let { mobileNumber.startsWith(countryCode) } == true) {
                mobileNumber = mobileNumber.substring(countryCode.length)
            }
            presenter.editValue = mobileNumber
            analyticsLogger.logEvent(AnalyticsEvent.PROFILE_UPDATE_VERIFY_EMAIL_PHONE)
            presenter.updateEmailOrPhone()
        }

        binding.etEditEmailPhone.addTextChangedListener(
            object : TextWatcher {
                override fun afterTextChanged(s: Editable) {}

                override fun beforeTextChanged(
                    s: CharSequence,
                    start: Int,
                    count: Int,
                    after: Int,
                ) {
                }

                override fun onTextChanged(
                    s: CharSequence,
                    start: Int,
                    before: Int,
                    count: Int,
                ) {
                    var emailPhone = binding.etEditEmailPhone.text.toString()
                    return when (presenter.editType) {
                        1 -> {
                            if (emailPhone.matches(Regex(emailRegex))) {
                                binding.tilEditEmailPhone.isErrorEnabled = false
                                binding.updateEmailOrPhone.isEnabled =
                                    !emailPhone.equals(
                                        presenter.accountInfo.customerEmails
                                            ?.firstOrNull()
                                            ?.emailAddress,
                                        true,
                                    )
                            } else {
                                binding.tilEditEmailPhone.isErrorEnabled = true
                                binding.tilEditEmailPhone.error =
                                    getString(
                                        R.string.Update_email_phone_enter_valid_email,
                                    )
                                binding.updateEmailOrPhone.isEnabled = false
                            }
                        }

                        else -> {
                            val customerPhoneNumbers = presenter.accountInfo.customerPhoneNumbers
                            if (!customerPhoneNumbers.isNullOrEmpty()) {
                                val countryCode = "+${customerPhoneNumbers.first().countryCode}"
                                if (!s.toString().startsWith(countryCode)) {
                                    emailPhone = countryCode.plus(emailPhone)
                                    binding.etEditEmailPhone.setText(emailPhone)
                                    binding.etEditEmailPhone.setSelection(
                                        binding.etEditEmailPhone.length(),
                                    )
                                }
                            }
                            if (emailPhone.matches(Regex(phoneRegex))) {
                                binding.tilEditEmailPhone.isErrorEnabled = false
                                binding.updateEmailOrPhone.isEnabled =
                                    customerPhoneNumbers.let {
                                        it.isNullOrEmpty() ||
                                            !emailPhone.equals(it.firstOrNull()?.phoneNumber, true)
                                    }
                            } else {
                                binding.tilEditEmailPhone.isErrorEnabled = true
                                binding.tilEditEmailPhone.error =
                                    getString(
                                        R.string.Update_email_phone_enter_valid_phone_number,
                                    )
                                binding.updateEmailOrPhone.isEnabled = false
                            }
                        }
                    }
                }
            },
        )
        binding.updateConstraintLayout.setOnClickListener {
            hideSoftKeyboard()
        }
        binding.etEditEmailPhone.setText(presenter.editValue)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            super.onBackPressed()
        }
        return true
    }

    override fun updateComplete() {
        if (presenter.editType != 1 && !regionManager.isPreferredRegionMexico()) {
            presenter.getConsentStatus()
        } else {
            startActivity(
                Intent(this, VerifyEmailPhoneActivity::class.java)
                    .putExtra(ToyotaConstants.EDIT_TYPE, presenter.editType)
                    .putExtra(ToyotaConstants.EDIT_VALUE, presenter.editValue)
                    .putExtra(ToyotaConstants.EDITING_ACCOUNTINFO, presenter.accountInfo),
            )
        }
    }

    override fun updateFailed(errorMessage: String) {
        ToastUtil.show(this, errorMessage, R.drawable.toast_remove)
    }

    override fun verifyComplete() {}

    override fun verifyFailed(errorMsg: String?) {}

    override fun consentStatus(status: String?) {
        runOnUiThread {
            when (status) {
                ToyotaConstants.PHONE_VERIFICATION_CONSENT_NO -> {
                    showConsentErrorDialog()
                }

                ToyotaConstants.PHONE_VERIFICATION_CONSENT_NC -> {
                    startHangTightScreen()
                }

                else -> {
                    startActivity(
                        Intent(this, VerifyEmailPhoneActivity::class.java)
                            .putExtra(ToyotaConstants.EDIT_TYPE, presenter.editType)
                            .putExtra(ToyotaConstants.EDIT_VALUE, presenter.editValue)
                            .putExtra(ToyotaConstants.EDITING_ACCOUNTINFO, presenter.accountInfo),
                    )
                }
            }
        }
    }

    private fun startHangTightScreen() {
        val intent =
            Intent(this, SmsHangTightActivity::class.java).apply {
                putExtra(ToyotaConstants.PHONE_NUMBER, presenter.editValue)
            }
        phoneVerificationContract.launch(intent)
    }

    private fun showConsentErrorDialog() {
        DialogUtil.showDialog(
            this,
            getString(R.string.important),
            getString(R.string.consent_error_dialog_message),
            null,
            getString(R.string.Common_ok),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    finish()
                }

                override fun onCancelClick() {
                    finish()
                }
            },
            false,
        )
    }

    private fun showSmsOptErrorDialog() {
        DialogUtil.showDialog(
            this,
            getString(org.forgerock.android.auth.ui.R.string.important_title),
            getString(
                org.forgerock.android.auth.ui.R.string.sms_consent_hang_tight,
                getString(R.string.app_name),
            ),
            getString(org.forgerock.android.auth.ui.R.string.fr_ok),
            null,
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    finish()
                }

                override fun onCancelClick() {
                    finish()
                }
            },
            false,
        )
    }

    companion object {
        const val TAG = "UpdateEmailPhoneActivity"
    }
}
