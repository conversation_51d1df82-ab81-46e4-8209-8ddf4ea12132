package com.toyota.oneapp.ui.accountsettings

import android.app.Dialog
import android.view.View
import com.allattentionhere.fabulousfilter.AAH_FabulousFragment
import com.toyota.oneapp.R
import dagger.hilt.android.AndroidEntryPoint

/**
 * Created by Spyros on 2021/09/17.
 */
@AndroidEntryPoint
class ChoosePictureFragment : AAH_FabulousFragment() {
    var isDeleteEnabled = false

    override fun setupDialog(
        dialog: Dialog,
        style: Int,
    ) {
        val contentView = View.inflate(context, R.layout.fragment_choose_photo, null)
        val rlContent = contentView.findViewById<View>(R.id.rl_content)
        val deleteView = contentView.findViewById<View>(R.id.choose_delete)
        contentView.run {
            findViewById<View>(R.id.choose_camera).setOnClickListener { takePhoto() }
            findViewById<View>(R.id.choose_photo).setOnClickListener { choosePhoto() }
            deleteView.setOnClickListener { deletePhoto() }
        }

        if (isDeleteEnabled) {
            deleteView.visibility = View.VISIBLE
        } else {
            deleteView.visibility = View.GONE
        }

        // params to set
        setAnimationDuration(200) // optional; default 500ms
        setPeekHeight(300) // optional; default 400dp
        setViewMain(rlContent) // necessary; main bottomsheet view
        setMainContentView(contentView) // necessary; call at end before super
        super.setupDialog(dialog, style) // call super at last
    }

    private fun choosePhoto() {
        closeFilter("Photo")
    }

    private fun takePhoto() {
        closeFilter("Camera")
    }

    private fun deletePhoto() {
        closeFilter("Delete")
    }
}
