package com.toyota.oneapp.ui.accountsettings

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.View.GONE
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.allattentionhere.fabulousfilter.AAH_FabulousFragment
import com.google.android.gms.common.util.Base64Utils
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityAccountSettingsBinding
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseMvvmFragment
import com.toyota.oneapp.ui.ImageCropActivity
import com.toyota.oneapp.ui.accountsettings.helpandfeedback.HelpAndFeedbackHostActivity
import com.toyota.oneapp.ui.accountsettings.legalinfo.LegalInfoActivity
import com.toyota.oneapp.ui.accountsettings.personalinfo.NewPersonalInfoActivity
import com.toyota.oneapp.ui.payment.PaymentMethodsActivity
import com.toyota.oneapp.util.BiometryUtil
import com.toyota.oneapp.util.Brand
import com.toyota.oneapp.util.FileProviderManager
import com.toyota.oneapp.util.GlideUtil
import com.toyota.oneapp.util.ImageRotationUtil
import com.toyota.oneapp.util.IntentUtil
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import org.jetbrains.anko.startActivity
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.location.RealUserLocationProvider
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.permission.PermissionUtil
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class AccountSettingsFragment :
    BaseMvvmFragment<AccountSettingsViewModel>(),
    AAH_FabulousFragment.Callbacks {
    private var binding: ActivityAccountSettingsBinding? = null

    override val layoutRes: Int = R.layout.activity_account_settings

    override val viewModel: AccountSettingsViewModel by activityViewModels()

    @Inject
    lateinit var applicationData: ApplicationData

    @Inject
    lateinit var preferenceModel: OneAppPreferenceModel

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @Inject
    lateinit var regionManager: RegionManager
    private var accountInfo: AccountInfoSubscriber? = null
    private val dialogFrag = ChoosePictureFragment()
    private var currentPhotoPath: String? = null
    private var isSignOutDialogShown = false

    private val helpAndFeedbackHostActivityResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult(), { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                activity?.onBackPressed()
            }
        })

    private val personalInfoRequestContract =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            onActivityResultFromActivity(PERSONAL_INFO_REQUEST, result.resultCode, result.data)
        }

    private val imageByCameraContract =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            onActivityResultFromActivity(GET_IMAGE_BY_CAMERA, result.resultCode, result.data)
        }

    private val imageFromPhoneContract =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            onActivityResultFromActivity(GET_IMAGE_FROM_PHONE, result.resultCode, result.data)
        }

    private val cropImageContract =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            onActivityResultFromActivity(CROP_IMAGE_REQUEST, result.resultCode, result.data)
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = ActivityAccountSettingsBinding.inflate(inflater, container, false)
        return binding!!.root
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        setupUI()
        observeLiveDataEvents()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    override fun observeBaseEvents(viewModel: AccountSettingsViewModel) {
        super.observeBaseEvents(viewModel)
    }

    override fun performLogout() {
        super.performLogout()
        preferenceModel.setLoggedIn(false)
    }

    override fun initialize() {
        activity?.let { activity ->
            viewModel.initialize(
                RealUserLocationProvider(activity).also { lifecycle.addObserver(it) },
            )
            viewModel.start()
        }
    }

    private fun observeLiveDataEvents() {
        observeBaseEvents(viewModel)
        observeEvents()
    }

    private fun observeEvents() {
        viewModel.apply {
            refreshAccountInfo.observe(viewLifecycleOwner) {
                refreshAccountInfo(it)
            }
            refreshPhotoFromBase64.observe(viewLifecycleOwner) {
                refreshPhotoFromBase64(it)
                dialogFrag.isDeleteEnabled = !it.isNullOrEmpty()
            }
            uerProfileInfo.observe(viewLifecycleOwner) {
                updateProfileInfo(it)
            }
            showDataPrivacyPortal.observe(viewLifecycleOwner) {
                binding?.legalLayout?.isVisible = it
            }
            enableVaSettings.observe(viewLifecycleOwner, {
                binding?.vaSettingsLayout?.visibility = if (it) View.VISIBLE else View.GONE
            })
            enableLinkedAccounts.observe(viewLifecycleOwner, {
                binding?.linkedAccounts?.visibility = if (it) View.VISIBLE else View.GONE
            })
        }
    }

    private fun setupUI() {
        context?.let {
            if (BiometryUtil.canAuthenticate(it)) {
                binding?.notificationSecurityLayout?.visibility = View.VISIBLE
            }
        }
        binding?.versionName?.text =
            String.format(
                Locale.getDefault(),
                "%s(%d) - %s",
                BuildConfig.VERSION_NAME,
                BuildConfig.VERSION_CODE,
                BuildConfig.FLAVOR_environment,
            )
        dialogFrag.setCallbacks(this)
        dialogFrag.setParentFab(binding?.ivEdit)
        binding?.toolbar?.setNavigationOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
            finish()
        }

        binding?.btnSignout?.setText(R.string.AccountSettings_sign_out)
        val text: String = getString(R.string.AccountSettings_sign_out_note)
        binding?.paymentMethodLayout?.visibility =
            when {
                applicationData.getVehicleList()?.any {
                    it.isFeatureEnabled(Feature.PAID_SUBSCRIPTION) && it.isPrimarySubscriber
                } == true -> View.VISIBLE
                else -> View.GONE
            }
        binding?.btnSignout?.setOnClickListener {
            if (isSignOutDialogShown) {
                return@setOnClickListener
            }

            isSignOutDialogShown = true
            DialogUtil.showDialog(
                activity,
                getString(R.string.AccountSettings_sign_out),
                text,
                getString(R.string.AccountSettings_sign_out),
                getString(R.string.Common_cancel),
                object : OnCusDialogInterface {
                    override fun onConfirmClick() {
                        analyticsLogger.logEvent(AnalyticsEvent.LOGOUT)
                        viewModel.logout()
                        performLogout()
                        isSignOutDialogShown = false
                    }

                    override fun onCancelClick() {
                        isSignOutDialogShown = false
                    }
                },
                false,
            )
        }
        binding?.vaSettingsLayout?.setOnClickListener {
            activity?.startActivity<VirtualAssistantSettingsActivity>()
        }

        binding?.linkedAccounts?.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.LINKED_ACCOUNT_CLICKED)
            activity?.startActivity<LinkedAccountsActivity>()
        }

        binding?.feedbackLayout?.setOnClickListener {
            analyticsLogger.logEventWithParameter(
                AnalyticsEventParam.ACCOUNT_SETTING_SELECT,
                AnalyticsEventParam.PROFILE_FEEDBACK,
            )
            analyticsLogger.logEventWithParameter(
                AnalyticsEventParam.ACCOUNT_SETTING_SELECT,
                AnalyticsEventParam.PROFILE_FEEDBACK,
            )
            helpAndFeedbackHostActivityResultLauncher.launch(
                Intent(context, HelpAndFeedbackHostActivity::class.java),
            )
        }
        binding?.personalInfoLayout?.setOnClickListener {
            analyticsLogger.logEventWithParameter(
                AnalyticsEventParam.ACCOUNT_SETTING_SELECT,
                AnalyticsEventParam.PROFILE_INFO,
            )

            accountInfo?.let {
                val intent =
                    Intent(requireActivity(), NewPersonalInfoActivity::class.java).apply {
                        putExtra(ToyotaConstants.ACCOUNT_INFO, accountInfo)
                        putExtra(PROFILE_NAME_KEY, binding?.tvGreetingsName?.text.toString())
                    }
                personalInfoRequestContract.launch(intent)
            }
        }
        binding?.ivEdit?.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.PROFILE_PICTURE)
            activity?.let {
                dialogFrag.show(it.supportFragmentManager, dialogFrag.tag)
            }
        }
        binding?.notificationSettingsLayout?.setOnClickListener {
            analyticsLogger.logEventWithParameter(
                AnalyticsEventParam.ACCOUNT_SETTING_SELECT,
                AnalyticsEventParam.PROFILE_NSETTINGS,
            )
            val intent = Intent(activity, NotificationSettingsActivity::class.java)
            startActivity(intent)
        }

        binding?.notificationSecurityLayout?.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.SECURITY_SETTINGS)
            val intent = Intent(activity, SecuritySettingsActivity::class.java)
            startActivity(intent)
        }
        binding?.paymentMethodLayout?.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.PAYMENT_METHODS)
            startActivity(Intent(activity, PaymentMethodsActivity::class.java))
        }
        binding?.legalLayout?.setOnClickListener {
            navigateToDataPrivacy()
        }

        binding?.legalInfoLayout?.setOnClickListener {
            val intent = Intent(activity, LegalInfoActivity::class.java)
            startActivity(intent)
        }

        binding?.featureToggleLayout?.setOnClickListener {
            val intent = Intent(activity, FeatureToggleActivity::class.java)
            startActivity(intent)
        }

        if (regionManager.isPreferredRegionUS()) {
            binding?.disconnectRemoteVehicleAccessLayout?.setOnClickListener {
                ToyUtil.openCustomChromeTab(
                    context,
                    ToyUtil.getDisconnectRemoteVehicleAccessUrls(
                        when (BuildConfig.APP_BRAND) {
                            Brand.SUBARU.appBrand, Brand.TOYOTA.appBrand -> Brand.TOYOTA
                            Brand.LEXUS.appBrand -> Brand.LEXUS
                            else -> Brand.TOYOTA
                        },
                    ),
                )
            }
        } else {
            binding?.disconnectRemoteVehicleAccessLayout?.visibility = GONE
        }

        binding?.tvDebugLogs?.setOnClickListener {
            activity?.startActivity<LogViewerActivity>()
        }

        if (BuildConfig.DEBUG) binding?.debugLogsLayout?.visibility = View.VISIBLE

        if (BuildConfig.DEBUG &&
            BuildConfig.FEATURE_FLAG_TOGGLE &&
            applicationData.getVehicleList() != null &&
            applicationData.getVehicleList()?.isNotEmpty() == true
        ) {
            binding?.featureToggleLayout?.visibility = View.VISIBLE
        }
    }

    private fun navigateToDataPrivacy() {
        analyticsLogger.logEventWithParameter(
            AnalyticsEventParam.ACCOUNT_SETTING_SELECT,
            AnalyticsEventParam.PROFILE_DATACONSENTS,
        )
        val intent =
            if (applicationData.getVehicleList()?.size == 1) {
                IntentUtil.getEditConsentIntent(
                    requireContext(),
                    applicationData.getSelectedVehicle(),
                    regionManager,
                )
            } else {
                Intent(requireContext(), LegalSelectVehicleActivity::class.java)
            }
        startActivity(intent)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }

    private fun cropImage(uri: Uri?) {
        if (uri != null) {
            val intent = Intent(activity, ImageCropActivity::class.java)
            intent.putExtra(ToyotaConstants.IMAGE_URI, uri.toString())
            cropImageContract.launch(intent)
        }
    }

    private fun refreshAccountInfo(accountInfoSubscriber: AccountInfoSubscriber) {
        accountInfo = accountInfoSubscriber
        preferenceModel.setAccountInfoSubscriber(accountInfoSubscriber)
        viewModel.getUserProfileName()
    }

    private fun refreshPhotoFromBase64(string: String) {
        if (string.isEmpty()) {
            binding?.ivProfilePic?.borderWidth = 0
            GlideUtil.loadImage(
                activity,
                R.drawable.pro_pic2,
                R.drawable.pro_pic2,
                binding?.ivProfilePic,
            )
        } else {
            binding?.ivProfilePic?.borderWidth =
                resources.getDimensionPixelSize(
                    R.dimen.lineSpace_standard,
                )
            GlideUtil.loadImageFromBase64(
                activity,
                string,
                R.drawable.pro_pic2,
                binding?.ivProfilePic,
            )
        }
    }

    private fun refreshPhotoFromFile(file: File?) {
        if (file == null) {
            binding?.ivProfilePic?.borderWidth = 0
        } else {
            binding?.ivProfilePic?.borderWidth =
                resources.getDimensionPixelSize(R.dimen.lineSpace_standard)
        }

        file?.readBytes()?.let { byteArray ->
            val base64String = Base64Utils.encode(byteArray)
            dialogFrag.isDeleteEnabled = base64String.isNotEmpty()
        }

        GlideUtil.loadImageFromFile(activity, file, R.drawable.pro_pic2, binding?.ivProfilePic)
        setProfilePicUpdateResult()
    }

    private fun setProfilePicUpdateResult() {
        val intent = Intent()
        intent.putExtra(EXTRA_PROFILE_PIC_UPDATE, true)
        requireActivity().setResult(Activity.RESULT_OK, intent)
    }

    private fun updateProfileInfo(profileName: String?) {
        activity?.runOnUiThread {
            binding?.tvGreetingsName?.text =
                if (TextUtils.isEmpty(profileName)) {
                    val firstName = accountInfo?.firstName ?: viewModel.getUserFirstName()
                    val lastName = accountInfo?.lastName ?: viewModel.getUserLastName()
                    String.format("%s %s", firstName, lastName)
                } else {
                    profileName
                }
            val intent = Intent()
            intent.putExtra(EXTRA_PROFILE_NAME_UPDATE, true)
            requireActivity().setResult(Activity.RESULT_OK, intent)
        }
    }

    override fun onResult(result: Any?) {
        try {
            when (result.toString()) {
                "Camera" -> {
                    PermissionUtil.checkOnlyCameraPermissions(this) { permission ->
                        when {
                            permission.granted -> {
                                dispatchTakePictureIntent()
                            }
                            permission.shouldShowRequestPermissionRationale -> {
                                // Denied permission without ask never again
                            }
                            else ->
                                showForceAllowPermissionDialog(
                                    getString(R.string.Common_permission_msg),
                                )
                        }
                    }
                }
                "Photo" ->
                    PermissionUtil.checkFileReadPermissions(this) { permission ->
                        when {
                            permission.granted -> {
                                val intent = Intent()
                                intent.type = "image/*"
                                intent.action = Intent.ACTION_GET_CONTENT
                                imageFromPhoneContract.launch(
                                    Intent.createChooser(
                                        intent,
                                        "Select Picture",
                                    ),
                                )
                            }
                            permission.shouldShowRequestPermissionRationale -> {
                                // Denied permission without ask never again
                            }
                            else ->
                                showForceAllowPermissionDialog(
                                    getString(R.string.Common_permission_msg),
                                )
                        }
                    }

                "Delete" -> {
                    viewModel.deletePhoto()
                }
            }
        } catch (e: Exception) {
            LogTool.e(TAG, "Error occurred on action result", e)
        }
    }

    fun onActivityResultFromActivity(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
    ) {
        when (requestCode) {
            GET_IMAGE_BY_CAMERA -> {
                if (resultCode == Activity.RESULT_OK) {
                    currentPhotoPath?.let { path ->
                        ImageRotationUtil.fixImageRotation(path)
                        cropImage(photoURI)
                    }
                }
            }
            GET_IMAGE_FROM_PHONE -> {
                if (resultCode == Activity.RESULT_OK) {
                    cropImage(data?.data)
                }
            }
            CROP_IMAGE_REQUEST -> {
                if (resultCode == Activity.RESULT_OK) {
                    data?.getStringExtra(ToyotaConstants.IMAGE_URI)?.let { imageUri ->
                        lifecycleScope.launch {
                            viewModel.setPhoto(imageUri)
                            refreshPhotoFromFile(File(imageUri))
                            setProfilePicUpdateResult()
                        }
                    }
                }
            }
            PERSONAL_INFO_REQUEST -> {
                activity?.recreate()
                if (resultCode == Activity.RESULT_OK) {
                    viewModel.updateAccountInfo()
                }
            }
        }
    }

    @Throws(IOException::class)
    private fun createImageFile(): File? {
        // Create an image file name
        val timeStamp: String =
            SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(
                Date(),
            )
        val imageFileName = "JPEG_" + timeStamp + "_"
        val storageDir: File? = activity?.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        val image =
            File.createTempFile(
                imageFileName,
                ".jpg",
                storageDir,
            )

        // Save a file: path for use with ACTION_VIEW intents
        currentPhotoPath = image.absolutePath
        return image
    }

    private fun dispatchTakePictureIntent() {
        val takePictureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        // Ensure that there's a camera activity to handle the intent
        if (activity?.packageManager?.let { takePictureIntent.resolveActivity(it) } != null) {
            // Create the File where the photo should go
            val photoFile: File?
            try {
                photoFile = createImageFile()
            } catch (ex: IOException) {
                LogTool.e(TAG, ex.cause?.message)
                return
            }
            // Continue only if the File was successfully created
            if (photoFile != null && activity != null) {
                photoURI = FileProviderManager(requireActivity()).openFiles(photoFile)
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
                imageByCameraContract.launch(takePictureIntent)
            }
        }
    }

    var photoURI: Uri? = null

    companion object {
        const val PERSONAL_INFO_REQUEST = 100
        private val TAG = AccountSettingsFragment::class.java.simpleName
        private const val CROP_IMAGE_REQUEST = 1
        const val PROFILE_NAME_KEY = "profile_name"
        private const val ACCOUNT_INFO_REQUEST = 42
        private const val GET_IMAGE_BY_CAMERA = 43
        private const val GET_IMAGE_FROM_PHONE = 44
        const val EXTRA_PROFILE_PIC_UPDATE = "UPDATE_PROFILE_PIC"
        const val EXTRA_PROFILE_NAME_UPDATE = "UPDATE_PROFILE_NAME"
    }
}
