package com.toyota.oneapp.ui.destinations.stcLocation

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentStclocationsBinding
import com.toyota.oneapp.model.dashboard.card.MyDestinationsItem
import com.toyota.oneapp.model.poi.StcDeleteRequest
import com.toyota.oneapp.model.poi.StcLocationResponse
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.destinations.destination.DestinationsFragment
import com.toyota.oneapp.util.LocationUtil
import com.toyota.oneapp.util.PixelUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.recyclerview.HorizontalDividerItemDecoration

@AndroidEntryPoint
class StcLocationsFragment :
    BaseDataBindingFragment<FragmentStclocationsBinding>(),
    StcListAdapter.StcFilterListener {
    private val viewModel: StcLocationsViewModel by viewModels()
    private lateinit var stcListAdapter: StcListAdapter
    private var myDestinationsItem: MyDestinationsItem? = MyDestinationsItem()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentStclocationsBinding,
        savedInstance: Bundle?,
    ) {
        binding.lifecycleOwner = this
        initViewModelObservers()
        initViews()
    }

    override fun getLayout(): Int = R.layout.fragment_stclocations

    fun onBackPressed() {
        findNavController().popBackStack()
    }

    private fun initViewModelObservers() {
        stcListAdapter = StcListAdapter(context as Context, this)
        viewDataBinding.apply {
            executePendingBindings()
            val recyclerView = stcList
            recyclerView.apply {
                layoutManager = LinearLayoutManager(context as Context)
                addItemDecoration(
                    HorizontalDividerItemDecoration
                        .Builder(context)
                        .color(context.getColor(R.color.divider_color))
                        .size(PixelUtil.dp2px(context, 1f))
                        .build(),
                )
            }
            toolbar.apply {
                setNavigationOnClickListener { requireActivity().onBackPressed() }
            }
        }

        viewModel.stcList.observe(
            this,
            Observer {
                it.let(stcListAdapter::updateList)
                refreshMode()
            },
        )
        viewModel.deleteSuccess.observe(
            this,
            Observer {
                stcListAdapter.isEditMode = true
                viewModel.getStcLocationList()
            },
        )
        viewModel.getProfile.observe(
            this,
            Observer { profile ->
                myDestinationsItem = LocationUtil.getLocationDetails(profile)
            },
        )
        viewModel.run {
            onTvStcDoneClicked.observe(
                viewLifecycleOwner,
                Observer {
                    stcListAdapter.isEditMode = !stcListAdapter.isEditMode
                    refreshMode()
                },
            )
        }

        viewModel.isStcListAvailable.observe(viewLifecycleOwner) {
            viewDataBinding.tvStcDone.isEnabled = it
            refreshMode()
            DataBindingAdapters.setIsVisible(viewDataBinding.noStcPrefLl, !it)
        }
    }

    private fun initViews() {
        viewDataBinding.tvStcDone.setOnClickListener {
            viewModel.onTvStcDoneClicked()
        }

        DataBindingAdapters.setAdapter(viewDataBinding.stcList, stcListAdapter)
    }

    override fun onResume() {
        super.onResume()
        viewModel.getStcLocationList()
    }

    private fun refreshMode() {
        viewDataBinding.apply {
            if (stcListAdapter.isEditMode) {
                tvStcDone.text = getString(R.string.done_label)
            } else {
                tvStcDone.text = getString(R.string.common_edit)
            }
        }
        viewModel.getLocationDetails()
        stcListAdapter.notifyDataSetChanged()
    }

    override fun onItemClickListener(placeInfo: StcLocationResponse?) {
        val preferenceType = getPreferencesType(placeInfo)
        val route =
            if (preferenceType == DestinationsFragment.SEARCH_ALL) {
                DestinationsFragment.SEARCH_PATH
            } else {
                DestinationsFragment.VIEW_PATH
            }
        findNavController().navigate(
            StcLocationsFragmentDirections.actionShareStcPoi(
                preferenceType,
                placeInfo?.location,
                route,
                ToyotaConstants.EMPTY_STRING,
            ),
        )
    }

    private fun getPreferencesType(placeInfo: StcLocationResponse?): String {
        var preferenceType = DestinationsFragment.SEARCH_ALL
        myDestinationsItem?.let {
            if (it.home.placeId == placeInfo?.location?.placeId) {
                preferenceType = DestinationsFragment.HOME_PREFERENCE
            } else if (it.work.placeId == placeInfo?.location?.placeId) {
                preferenceType = DestinationsFragment.WORK_PREFERENCE
            } else {
                for (fav in it.favList) {
                    if (fav.placeId == placeInfo?.location?.placeId) {
                        preferenceType = DestinationsFragment.FAV_PREFERENCE
                        break
                    }
                }
            }
        }
        return preferenceType
    }

    override fun onDeleteItem(placeInfo: StcLocationResponse?) {
        removeStcFromList(placeInfo)
    }

    private fun removeStcFromList(placeInfo: StcLocationResponse?) {
        DialogUtil.showDialog(
            requireActivity(),
            getString(R.string.remove_saved_stc),
            getString(R.string.remove_saved_stc_description),
            getString(R.string.Common_remove),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    viewModel.deleteStcLocationList(StcDeleteRequest(placeInfo?.location?.placeId))
                }

                override fun onCancelClick() {
                    // Unused
                }
            },
            false,
        )
    }
}
