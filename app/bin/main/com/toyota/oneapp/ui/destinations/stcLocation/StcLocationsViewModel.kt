package com.toyota.oneapp.ui.destinations.stcLocation

import android.content.res.Resources
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.oneapp.R
import com.toyota.oneapp.model.poi.StcDeleteRequest
import com.toyota.oneapp.model.poi.StcLocationResponse
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.network.api.repository.LocationRepository
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import io.grpc.stub.StreamObserver
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class StcLocationsViewModel
    @Inject
    constructor(
        private val locationRepository: LocationRepository,
        private val userProfileAPIManager: UserProfileAPIManager,
        private val resources: Resources,
    ) : BaseViewModel() {
        private val mIsStcListAvailable = MutableLiveData<Boolean>()
        val isStcListAvailable: LiveData<Boolean>
            get() = mIsStcListAvailable
        private val mStcList = MutableLiveData<List<StcLocationResponse>>()
        val stcList: LiveData<List<StcLocationResponse>>
            get() = mStcList
        private val mDeleteSuccess = MutableLiveData<Boolean>()
        val deleteSuccess: LiveData<Boolean>
            get() = mDeleteSuccess
        private val mGetProfile = MutableLiveData<ProfileServiceServer.UserProfile>()
        val getProfile: LiveData<ProfileServiceServer.UserProfile>
            get() = mGetProfile

        private val onTvStcDoneClick = SingleLiveEvent<Any>()
        val onTvStcDoneClicked: LiveData<Any>
            get() = onTvStcDoneClick

        fun onTvStcDoneClicked() {
            onTvStcDoneClick.call()
        }

        fun getStcLocationList() {
            showProgress()
            viewModelScope.launch {
                val resource = locationRepository.getSharedLocations()
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        val values = resource.data?.payload?.toMutableList()
                        (values?.also { filterValidLocation(it) } ?: ArrayList()).let { responseData ->
                            mStcList.postValue(responseData)
                            mIsStcListAvailable.postValue(responseData.isNotEmpty())
                        }
                    }

                    else -> {
                        mIsStcListAvailable.postValue(false)
                    }
                }
            }
        }

        fun deleteStcLocationList(stcDeleteRequest: StcDeleteRequest) {
            showProgress()
            viewModelScope.launch {
                val resource =
                    locationRepository.deleteSharedLocations(stcDeleteRequest)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        mDeleteSuccess.postValue(true)
                    }
                    else -> {
                        mDeleteSuccess.postValue(false)
                    }
                }
            }
        }

        private fun filterValidLocation(resource: MutableList<StcLocationResponse>) {
            val iterator = resource.iterator()
            for (stcLocation in iterator) {
                if (stcLocation.location?.formattedAddress.isNullOrBlank()) {
                    iterator.remove()
                    continue
                }
                val re = Regex(ToyotaConstants.ADDRESS_REGEX)
                stcLocation.location?.run {
                    formattedAddress = formattedAddress?.replace(re, ToyotaConstants.EMPTY_STRING)
                }
                stcLocation.location?.run {
                    name = name?.replace(re, ToyotaConstants.EMPTY_STRING)
                }
            }
        }

        fun getLocationDetails() {
            showProgress()

            userProfileAPIManager.getUserProfile(
                object :
                    StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.GetUserProfileResponse) {
                        mGetProfile.postValue(value.userProfile)
                    }

                    override fun onError(t: Throwable) {
                        hideProgress()
                        if (t.message == resources.getString(R.string.grpc_channel_null_exception)) {
                            showErrorMessage(t.message)
                        }
                    }

                    override fun onCompleted() {
                        hideProgress()
                    }
                },
            )
        }
    }
