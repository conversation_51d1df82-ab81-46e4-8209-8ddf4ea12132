package com.toyota.oneapp.ui.destinations.favorite

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentFavoritesBinding
import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.destinations.*
import com.toyota.oneapp.ui.destinations.destination.DestinationsFragment
import com.toyota.oneapp.ui.destinations.sharePOI.SharePOIFragment
import com.toyota.oneapp.util.*
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.recyclerview.HorizontalDividerItemDecoration

@AndroidEntryPoint
class FavoritesFragment :
    BaseDataBindingFragment<FragmentFavoritesBinding>(),
    FavoriteListAdapter.FavoriteFilterListener {
    private val viewModel: FavoritesViewModel by viewModels()

    private lateinit var mFilterAdapter: FavoriteListAdapter
    private val listData: ArrayList<LocationDetails> = ArrayList()
    private lateinit var routeName: String
    private val arguments: FavoritesFragmentArgs by navArgs()

    companion object {
        const val MAX_SIZE = 20
    }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(viewModel)
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        routeName = arguments.route
    }

    override fun onViewBound(
        binding: FragmentFavoritesBinding,
        savedInstance: Bundle?,
    ) {
        viewModel.getFavoriteDetails()
        binding.favoritesViewModel = viewModel
        initViewModelObservers()
    }

    override fun getLayout(): Int = R.layout.fragment_favorites

    private fun initViewModelObservers() {
        setupUi()

        viewDataBinding.favouriteInfoToolbar.apply {
            setNavigationOnClickListener { requireActivity().onBackPressed() }
        }

        viewModel.run {
            viewDataBinding.tvFavDone.setOnClickListener {
                mFilterAdapter.isEditMode = !mFilterAdapter.isEditMode
                refreshMode()
            }

            favouriteDetailsUserProfile.observe(
                viewLifecycleOwner,
                Observer {
                    updateFavouritesFromProfile(it)
                },
            )

            updateFavLocation.observe(
                viewLifecycleOwner,
                Observer {
                    deleteFavorite(it)
                },
            )
        }
    }

    private fun refreshMode() {
        viewDataBinding.apply {
            if (mFilterAdapter.isEditMode) {
                tvFavDone.text = getString(R.string.done_label)
            } else {
                tvFavDone.text = getString(R.string.common_edit)
            }
        }
        mFilterAdapter.refreshLocationList(listData)
    }

    private fun setupUi() {
        mFilterAdapter = FavoriteListAdapter(listData, this)
        mFilterAdapter.isEditMode = arguments.isEditMode
        viewDataBinding.rvMapCategory.apply {
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            addItemDecoration(
                HorizontalDividerItemDecoration
                    .Builder(context)
                    .color(context.getColor(R.color.divider_color))
                    .size(PixelUtil.dp2px(context, 1f))
                    .build(),
            )
            adapter = mFilterAdapter
        }
    }

    private fun updateUi() {
        viewDataBinding.apply {
            noFavPrefLl.visibility = View.GONE
            when {
                listData.size >= MAX_SIZE -> {
                    tvFavDone.visibility = View.VISIBLE
                    favouriteInfoToolbar.title = getString(R.string.max_string, MAX_SIZE)
                }
                listData.isEmpty() -> {
                    noFavPrefLl.visibility = View.VISIBLE
                    favouriteInfoToolbar.title = getString(R.string.save_favorites)
                }
                else -> {
                    favouriteInfoToolbar.title =
                        getString(R.string.save_favorites_count, listData.size, MAX_SIZE)
                }
            }
        }
    }

    override fun onItemClickListener(placeInfo: LocationDetails) {
        findNavController().navigate(
            FavoritesFragmentDirections.actionShareFavoritePoi(
                DestinationsFragment.FAV_PREFERENCE,
                placeInfo,
                DestinationsFragment.VIEW_PATH,
                ToyotaConstants.EMPTY_STRING,
            ),
        )
    }

    override fun onDeleteItem(placeInfo: LocationDetails) {
        val tmpList = ArrayList<LocationDetails>(listData)
        removeFavoriteFromList(tmpList, placeInfo)
    }

    private fun removeFavoriteFromList(
        tmpList: ArrayList<LocationDetails>,
        placeInfo: LocationDetails,
    ) {
        DialogUtil.showDialog(
            requireActivity(),
            getString(R.string.remove_saved_destination),
            getString(R.string.remove_save_destination_description),
            getString(R.string.Common_remove),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    tmpList.remove(placeInfo)
                    viewModel.updateFavoriteLocation(tmpList)
                }

                override fun onCancelClick() {
                    // Unused
                }
            },
            false,
        )
    }

    private fun updateFavouritesFromProfile(userProfile: ProfileServiceServer.UserProfile) {
        activity?.runOnUiThread {
            listData.clear()
            val favoriteList =
                userProfile.commonVehicleSettings.navigationSettings.favorites.valueList
            favoriteList
                .map {
                    it.toLocationDetails()
                }.map {
                    listData.add(it)
                }
            refreshMode()
            updateUi()
        }
    }

    fun onBackPressed() {
        findNavController().popBackStack()
    }

    private fun deleteFavorite(list: ArrayList<LocationDetails>) {
        activity?.runOnUiThread {
            if (routeName == SharePOIFragment.ROUTE_NAME) {
                onBackPressed()
            }
            listData.clear()
            listData.addAll(list)
            mFilterAdapter.isEditMode = (list.isNotEmpty())
            updateUi()
            refreshMode()
        }
    }
}
