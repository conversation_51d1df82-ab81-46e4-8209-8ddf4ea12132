package com.toyota.oneapp.ui.destinations.favorite

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.RelativeLayout
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemDestinationFavoritesBinding
import com.toyota.oneapp.model.poi.LocationDetails
import org.jetbrains.anko.startOf

class FavoriteListAdapter(
    private var addressList: ArrayList<LocationDetails>,
    private val favoriteFilterListener: FavoriteFilterListener,
) : RecyclerView.Adapter<FavoriteListAdapter.FavoriteHolder>() {
    var isEditMode: Boolean = false

    inner class FavoriteHolder(
        private val binding: ItemDestinationFavoritesBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(favorite: LocationDetails) {
            binding.apply {
                isItemEditMode = isEditMode
                itemFavoriteDestination = favorite
                executePendingBindings()
                locationFavoriteItemMainRl.setOnClickListener {
                    favoriteFilterListener.onItemClickListener(favorite)
                }
                destinationFavoriteMinusIv.setOnClickListener {
                    locationFavoriteItemMainRl.layoutParams.height =
                        root.context.resources
                            .getDimension(toyotaone.commonlib.R.dimen.margin_100_dp)
                            .toInt()
                    favoriteFilterListener.onDeleteItem(favorite)
                }
                if (!isEditMode) {
                    locationFavoriteItemMainRl.layoutParams.height =
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    (destinationDetailLl.layoutParams as RelativeLayout.LayoutParams)
                        .startOf(R.id.destination_arrow_iv)
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): FavoriteHolder {
        val binding =
            DataBindingUtil.inflate<ItemDestinationFavoritesBinding>(
                LayoutInflater.from(parent.context),
                R.layout.item_destination_favorites,
                parent,
                false,
            )
        return FavoriteHolder(binding)
    }

    override fun getItemCount(): Int = addressList.size

    override fun onBindViewHolder(
        holder: FavoriteHolder,
        position: Int,
    ) = holder.bind(addressList[position])

    fun refreshLocationList(addressList: ArrayList<LocationDetails>) {
        this.addressList = addressList
        notifyDataSetChanged()
    }

    interface FavoriteFilterListener {
        fun onItemClickListener(placeInfo: LocationDetails)

        fun onDeleteItem(placeInfo: LocationDetails)
    }
}
