package com.toyota.oneapp.ui.destinations

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContract
import androidx.navigation.NavGraph
import androidx.navigation.fragment.NavHostFragment
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityDestinationsBinding
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DestinationsActivity : DataBindingBaseActivity<ActivityDestinationsBinding>() {
    private lateinit var navGraph: NavGraph

    override fun getLayoutId(): Int = R.layout.activity_destinations

    override fun initViews(savedInstance: Bundle?) {
        (
            supportFragmentManager.findFragmentById(binding.destinationsNavHost.id)
                as NavHostFragment
        ).navController.let {
            val graphInflater = it.navInflater
            navGraph = graphInflater.inflate(R.navigation.destinations_nav_graph)
        }
    }

    class Contract : ActivityResultContract<Bundle, Unit>() {
        override fun createIntent(
            context: Context,
            input: Bundle,
        ): Intent = Intent(context, DestinationsActivity::class.java).apply { putExtras(input) }

        override fun parseResult(
            resultCode: Int,
            intent: Intent?,
        ) {
            // Not implemented
        }
    }
}
