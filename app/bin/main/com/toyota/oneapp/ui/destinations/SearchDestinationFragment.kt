package com.toyota.oneapp.ui.destinations

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.MenuItem
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.libraries.places.api.Places
import com.google.android.libraries.places.api.model.AutocompleteSessionToken
import com.google.android.libraries.places.api.net.FindAutocompletePredictionsRequest
import com.google.android.libraries.places.api.net.PlacesClient
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.SearchDestinationBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.destinations.destination.DestinationsFragment
import com.toyota.oneapp.util.PixelUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.recyclerview.HorizontalDividerItemDecoration

@AndroidEntryPoint
class SearchDestinationFragment :
    BaseDataBindingFragment<SearchDestinationBinding>(),
    SearchFilterAdapter.SearchFilterListener {
    private var preferenceType: String? = null

    companion object {
        private const val MIN_WORD_LEN = 2
    }

    private lateinit var mFilterAdapter: SearchFilterAdapter
    private lateinit var placesClient: PlacesClient
    private val listData: ArrayList<PlaceInfo> = ArrayList()
    private val arguments: SearchDestinationFragmentArgs by navArgs()

    private lateinit var binding: SearchDestinationBinding

    override fun onViewBound(
        binding: SearchDestinationBinding,
        savedInstance: Bundle?,
    ) {
        this.binding = binding
        initObserveModels()
    }

    private fun initObserveModels() {
        preferenceType = arguments.preference
        when (preferenceType) {
            DestinationsFragment.HOME_PREFERENCE -> {
                viewDataBinding.searchInfoToolbar.setTitle(R.string.set_home)
            }
            DestinationsFragment.WORK_PREFERENCE -> {
                viewDataBinding.searchInfoToolbar.setTitle(R.string.set_work)
            }
        }
        viewDataBinding.searchInfoToolbar.setNavigationOnClickListener {
            onBackPressed()
        }
        initLocationView()
        placesClient = Places.createClient(context as Context)
        binding.etSearchBox.addTextChangedListener(
            object : TextWatcher {
                override fun afterTextChanged(s: Editable?) {}

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int,
                ) {}

                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int,
                ) {
                    s?.length?.let {
                        if (it > MIN_WORD_LEN) {
                            findLocations(s)
                        } else {
                            listData.clear()
                            mFilterAdapter.notifyDataSetChanged()
                        }
                    }
                }
            },
        )

        viewDataBinding.searchPrefClearIv.setOnClickListener {
            binding.etSearchBox.setText(ToyotaConstants.EMPTY_STRING)
        }
    }

    override fun getLayout(): Int = R.layout.search_destination

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
            }
        }
        return true
    }

    fun onBackPressed() {
        findNavController().popBackStack()
    }

    private fun findLocations(s: CharSequence) {
        val token = AutocompleteSessionToken.newInstance()
        val request =
            FindAutocompletePredictionsRequest
                .builder()
                .setSessionToken(token)
                .setCountries("US", "CA", "MX")
                .setQuery(s.toString())
                .build()
        placesClient.findAutocompletePredictions(request).addOnSuccessListener { response ->
            response?.let {
                listData.clear()
                for (prediction in it.autocompletePredictions) {
                    val plInfo =
                        PlaceInfo(
                            prediction.getPrimaryText(null).toString(),
                            prediction.getSecondaryText(null).toString(),
                            prediction.placeId,
                        )
                    listData.add(plInfo)
                }
                mFilterAdapter.notifyDataSetChanged()
            }
        }
    }

    private fun initLocationView() {
        mFilterAdapter = SearchFilterAdapter(context as Context, listData, this)
        viewDataBinding.rvMapCategory.apply {
            layoutManager
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            addItemDecoration(
                HorizontalDividerItemDecoration
                    .Builder(context)
                    .color(context.getColor(R.color.divider_color))
                    .size(PixelUtil.dp2px(context, 1f))
                    .build(),
            )
            adapter = mFilterAdapter
        }
    }

    override fun onItemClickListener(placeId: String) {
        findNavController().navigate(
            SearchDestinationFragmentDirections.actionShareSearchedPoi(
                preferenceType ?: ToyotaConstants.EMPTY_STRING,
                null,
                DestinationsFragment.SEARCH_PATH,
                placeId,
            ),
        )
    }
}
