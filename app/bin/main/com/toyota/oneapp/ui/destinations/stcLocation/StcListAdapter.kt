package com.toyota.oneapp.ui.destinations.stcLocation

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemStcLocationBinding
import com.toyota.oneapp.model.poi.StcLocationResponse
import org.jetbrains.anko.startOf

class StcListAdapter(
    private val context: Context,
    private val listener: StcFilterListener,
) : RecyclerView.Adapter<StcListAdapter.StcListHolder>() {
    var isEditMode: Boolean = false
    private var stcLocationList: List<StcLocationResponse>? = null

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): StcListHolder {
        val mDeveloperListItemBinding =
            DataBindingUtil.inflate<ItemStcLocationBinding>(
                LayoutInflater.from(context),
                R.layout.item_stc_location,
                parent,
                false,
            )

        return StcListHolder(mDeveloperListItemBinding)
    }

    override fun getItemCount(): Int = stcLocationList?.size ?: 0

    override fun onBindViewHolder(
        holder: StcListHolder,
        position: Int,
    ) {
        stcLocationList?.let {
            holder.itemStcLocationBinding.apply {
                itemModel = it[position]
                executePendingBindings()
                root.isEnabled = !isEditMode
                destinationMinusIv.visibility = if (isEditMode) View.VISIBLE else View.GONE
                destinationArrowIv.visibility = if (isEditMode) View.GONE else View.VISIBLE
                locationItemMainRl.setOnClickListener {
                    listener.onItemClickListener(stcLocationList?.get(position))
                }
                destinationMinusIv.setOnClickListener {
                    listener.onDeleteItem(stcLocationList?.get(position))
                }
                if (!isEditMode) {
                    locationItemMainRl.layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
                    (destinationDetailLl.layoutParams as RelativeLayout.LayoutParams)
                        .startOf(R.id.destination_arrow_iv)
                }
            }
        }
    }

    fun updateList(list: List<StcLocationResponse>?) {
        stcLocationList = list
        notifyDataSetChanged()
    }

    inner class StcListHolder(
        val itemStcLocationBinding: ItemStcLocationBinding,
    ) : RecyclerView.ViewHolder(itemStcLocationBinding.root)

    interface StcFilterListener {
        fun onItemClickListener(placeInfo: StcLocationResponse?)

        fun onDeleteItem(placeInfo: StcLocationResponse?)
    }
}
