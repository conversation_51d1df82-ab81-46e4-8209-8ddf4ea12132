package com.toyota.oneapp.ui.destinations.sharePOI

import android.os.Bundle
import android.view.View
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.drawable.toBitmap
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.tbruyelle.rxpermissions2.Permission
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.FragmentSharePoiBinding
import com.toyota.oneapp.model.dashboard.card.MyDestinationsItem
import com.toyota.oneapp.model.poi.*
import com.toyota.oneapp.ui.BaseActivity
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.destinations.destination.DestinationsFragment
import com.toyota.oneapp.ui.destinations.destination.DestinationsViewModel
import com.toyota.oneapp.ui.destinations.favorite.FavoritesFragment
import com.toyota.oneapp.util.LocationUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.location.UserLocationProvider
import toyotaone.commonlib.permission.PermissionUtil
import javax.inject.Inject

@AndroidEntryPoint
class SharePOIFragment :
    BaseDataBindingFragment<FragmentSharePoiBinding>(),
    OnMapReadyCallback {
    private var mapFragment: SupportMapFragment? = null
    private lateinit var bottomSheetBehavior: BottomSheetBehavior<View>
    private var pathOfShare: String? = null
    private var currentPreference: String? = null
    private var isFavorite = false
    private var locationDetails: LocationDetails? = null
    private var mapIconRes = R.drawable.ic_destination_favorite_pin
    private var myDestinationsItem: MyDestinationsItem? = null
    private val arguments: SharePOIFragmentArgs by navArgs()
    private val viewModel: SharePOIViewModel by viewModels()
    private val destinationsViewModel: DestinationsViewModel by activityViewModels()

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentSharePoiBinding,
        savedInstance: Bundle?,
    ) {
        handleExtras()
        bindUI()
    }

    override fun getLayout(): Int = R.layout.fragment_share_poi

    private fun bindUI() {
        bottomSheetBehavior = BottomSheetBehavior.from(viewDataBinding.bottomSheetNavigation)
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
        viewDataBinding.tvShareFavorites.setOnClickListener { handleShareFavoritesClicked() }
        viewDataBinding.tvShareSend.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.MYDEST_TOTAL_USERS_SENDTOCAR_CTA)
            locationDetails?.let {
                viewModel.sendPOIToCar(it)
            }
        }
        viewDataBinding.closeDetailPrefTv.setOnClickListener {
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
        }
        viewDataBinding.executePendingBindings()
        viewDataBinding.sharePoiToolbar.setNavigationOnClickListener {
            if (arguments.dashboardRoute == getString(R.string.deep_link_share_poi)) {
                activity?.finish()
            } else {
                activity?.onBackPressed()
            }
        }
    }

    private fun handleShareFavoritesClicked() {
        when (pathOfShare) {
            DestinationsFragment.VIEW_PATH -> {
                if (currentPreference == DestinationsFragment.FAV_PREFERENCE ||
                    currentPreference.isNullOrEmpty()
                ) {
                    if (isFavorite) {
                        removeFavAddress()
                    } else {
                        uploadFavAddress()
                    }
                    isFavorite = !isFavorite
                }
                viewModel.updateLocation(
                    LocationDetails(),
                    currentPreference
                        ?: ToyotaConstants.EMPTY_STRING,
                )
            }

            DestinationsFragment.SEARCH_PATH, getString(R.string.deep_link_share_poi) -> {
                if (currentPreference == DestinationsFragment.HOME_PREFERENCE ||
                    currentPreference == DestinationsFragment.WORK_PREFERENCE
                ) {
                    uploadNavigationAddress(currentPreference ?: ToyotaConstants.EMPTY_STRING)
                } else if (currentPreference == DestinationsFragment.FAV_PREFERENCE) {
                    uploadFavAddress()
                } else {
                    displaySaveDestinationDialog()
                }
            }
        }
    }

    private fun displaySaveDestinationDialog() {
        myDestinationsItem?.let {
            if (it.home.exists() && it.work.exists()) {
                // no need to display save destination dialogue, save as a fav address
                uploadFavAddress()
            } else {
                when (bottomSheetBehavior.state) {
                    BottomSheetBehavior.STATE_EXPANDED -> {
                        bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
                    }
                    else -> {
                        bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
                    }
                }
            }
        }
    }

    private fun observeEvents(viewModel: SharePOIViewModel) {
        observeBaseEvents(viewModel)
        viewModel.updateLocation.observe(
            this,
            Observer {
                updateLocation(it)
            },
        )

        viewModel.updatedLocation.observe(
            this,
            Observer {
                updatedLocation(it)
                destinationsViewModel.locationUpdated.value = true
            },
        )

        viewModel.updatedFavLocation.observe(
            this,
            Observer {
                updatedFavLocation(it)
            },
        )

        viewModel.updateVehicleProfile.observe(
            this,
            Observer {
                updateVehicleProfile(it)
            },
        )

        viewModel.updateLocationInfo.observe(
            this,
            Observer {
                updateLocationInfo(it)
            },
        )

        // In case we need to ask for location permissions
        viewModel.noLocationPermissionsObservable.observe(
            this,
            Observer { callback ->
                PermissionUtil.checkLocationPermissions(this) { permission: Permission ->
                    if (!permission.granted && !permission.shouldShowRequestPermissionRationale) {
                        (requireActivity() as BaseActivity).showForceAllowPermissionDialog()
                    } else {
                        callback.invoke(permission.granted)
                    }
                }
            },
        )

        viewModel.systemLocationDisabledObservable.observe(
            this,
            Observer {
                it.startResolutionForResult(
                    requireActivity(),
                    UserLocationProvider.LOC_SETTINGS_RESOLUTION_KEY,
                )
            },
        )
    }

    private fun updateLocation(response: SharePOIResponse) {
        val coordinates = Coordinates(response.location.latitude, response.location.longitude)
        // if route is null set the default value for coordinates.
        val routeCoordinates = Coordinates(response.routing.latitude, response.routing.longitude)
        locationDetails =
            LocationDetails(
                response.formattedAddress
                    ?: ToyotaConstants.EMPTY_STRING,
                coordinates,
                response.name ?: ToyotaConstants.EMPTY_STRING,
                response.address
                    ?: Address(),
                response.placeId,
                routeCoordinates,
                response.intersection
                    ?: ToyotaConstants.EMPTY_STRING,
                response.locationType,
                response.phoneNumber
                    ?: ToyotaConstants.EMPTY_STRING,
                System.currentTimeMillis() / 1000,
                System.currentTimeMillis() / 1000,
            )
        viewModel.getVehicleProfile()
    }

    private fun updatedLocation(value: String?) {
        activity?.runOnUiThread {
            value?.let {
                currentPreference = value
                updateLatestInfo()
            }
        }
    }

    private fun updatedFavLocation(favList: ArrayList<LocationDetails>?) {
        activity?.runOnUiThread {
            favList?.let {
                currentPreference = DestinationsFragment.FAV_PREFERENCE
                myDestinationsItem?.favList = it
                updateLatestInfo()
            }
        }
    }

    private fun updateLocationInfo(response: Directions) {
        if (response.routes.isNotEmpty() && response.routes[0].legs.isNotEmpty()) {
            val distance =
                response.routes[0]
                    .legs[0]
                    .distance.text
            val duration =
                response.routes[0]
                    .legs[0]
                    .duration.text
            viewDataBinding.shareItemDisTxt.text = distance.plus(" . ").plus(duration)
        }
    }

    // Class private Calls
    private fun updateLatestInfo() {
        updateSharePath()
        updateBottomPanelUi()
        showOnMap()
    }

    private fun updateSharePath() {
        when (pathOfShare) {
            DestinationsFragment.VIEW_PATH -> {
                pathOfShare = DestinationsFragment.SEARCH_PATH
                if (isFavorite && currentPreference == DestinationsFragment.FAV_PREFERENCE) {
                    viewDataBinding.tvShareFavorites.apply {
                        text = getText(R.string.Common_remove)
                        isSelected = true
                    }
                } else {
                    viewDataBinding.tvShareFavorites.apply {
                        text = getText(R.string.Common_save)
                        isSelected = false
                    }
                }
            }

            DestinationsFragment.SEARCH_PATH, getString(R.string.deep_link_share_poi) -> {
                pathOfShare = DestinationsFragment.VIEW_PATH
                viewDataBinding.tvShareFavorites.text = getText(R.string.Common_remove)
                viewDataBinding.tvShareFavorites.isSelected = true
            }
        }
    }

    private fun updateVehicleProfile(userProfile: ProfileServiceServer.UserProfile) {
        activity?.runOnUiThread {
            userProfile.let {
                myDestinationsItem = LocationUtil.getLocationDetails(userProfile)
            }
            updateBottomPanelUi()
            showOnMap()
        }
    }

    private fun updateBottomPanelUi() {
        /*
         * we are not showing save for home or work if already exist
         * In case of favorites if size less than MAX size
         */
        myDestinationsItem?.let {
            if (it.home.exists()) {
                viewDataBinding.updateHomePrefTv.visibility = View.GONE
            }
            if (it.work.exists()) {
                viewDataBinding.updateWorkPrefTv.visibility = View.GONE
            }
            myDestinationsItem?.favList?.firstOrNull { item -> item.exists() && item.placeId == locationDetails?.placeId }?.let {
                currentPreference = DestinationsFragment.FAV_PREFERENCE
                viewDataBinding.tvShareFavorites.text = getText(R.string.Common_remove)
                mapIconRes = R.drawable.ic_destination_favorite_pin
                pathOfShare = DestinationsFragment.VIEW_PATH
                isFavorite = true
                viewDataBinding.tvShareFavorites.text = getString(R.string.Common_remove)
                viewDataBinding.tvShareFavorites.isSelected = true
            }
        }
        // Bottom Controls
        viewDataBinding.updateHomePrefTv.setOnClickListener {
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
            uploadNavigationAddress(DestinationsFragment.HOME_PREFERENCE)
        }
        viewDataBinding.updateWorkPrefTv.setOnClickListener {
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
            uploadNavigationAddress(DestinationsFragment.WORK_PREFERENCE)
        }
        viewDataBinding.updateFavPrefTv.setOnClickListener {
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
            myDestinationsItem?.let {
                if (it.favList.size >= FavoritesFragment.MAX_SIZE) {
                    DialogUtil.showDialog(
                        activity,
                        getString(R.string.fav_max_warning_title),
                        getString(R.string.fav_max_warning),
                        getString(R.string.Common_continue),
                        getString(R.string.Common_cancel),
                        object : OnCusDialogInterface {
                            override fun onConfirmClick() {
                                activity?.runOnUiThread {
                                    findNavController().navigate(
                                        SharePOIFragmentDirections.actionFavoritesList(
                                            true,
                                            ROUTE_NAME,
                                        ),
                                    )
                                }
                            }

                            override fun onCancelClick() {
                                // Implementation not required
                            }
                        },
                        false,
                    )
                } else {
                    uploadFavAddress()
                }
            }
        }
    }

    private fun uploadNavigationAddress(preferenceType: String) {
        viewModel.updateLocation(locationDetails ?: LocationDetails(), preferenceType)
    }

    private fun uploadFavAddress() {
        /*
         * Below functionality will update fav if already exist.
         * Add if not present.
         */

        myDestinationsItem?.let {
            val tmpList = it.favList

            tmpList.stream().filter { location ->
                if (location.isSameAs(locationDetails)) {
                    tmpList.remove(location)
                } else {
                    false
                }
            }
            locationDetails?.let { lastLocationItem ->
                tmpList.add(lastLocationItem)
                lastLocationItem.refreshDate = System.currentTimeMillis() / 1000
                lastLocationItem.timeStamp = System.currentTimeMillis() / 1000
            }
            viewModel.updateFavoriteLocation(tmpList)
        }
    }

    private fun removeFavAddress() {
        myDestinationsItem?.let {
            val tmpList = it.favList.filterNot { location -> location.isSameAs(locationDetails) }
            viewModel.updateFavoriteLocation(ArrayList(tmpList))
        }
    }

    private fun handleExtras() {
        /*
         * Path of share is to find back trace
         * Preference type is to find current location with respect to
         * We are syncing profile to know to  Home , Work and fav list exist.
         *
         */
        pathOfShare = arguments.dashboardRoute
        currentPreference = arguments.preference
        isFavorite = currentPreference == DestinationsFragment.FAV_PREFERENCE
        when (pathOfShare) {
            getString(R.string.deep_link_share_poi) -> {
                val sharedText = arguments.locationAddress
                viewModel.getPoiInfo(sharedText)
            }
            DestinationsFragment.VIEW_PATH -> {
                if (isFavorite ||
                    currentPreference == DestinationsFragment.HOME_PREFERENCE ||
                    currentPreference == DestinationsFragment.WORK_PREFERENCE
                ) {
                    viewDataBinding.tvShareFavorites.apply {
                        text = getText(R.string.Common_remove)
                        isSelected = true
                    }
                } else {
                    viewDataBinding.tvShareFavorites.apply {
                        text = getText(R.string.Common_save)
                        isSelected = false
                    }
                }

                showSharedLocation()

                if (currentPreference != DestinationsFragment.HOME_PREFERENCE &&
                    currentPreference != DestinationsFragment.WORK_PREFERENCE
                ) {
                    viewModel.getVehicleProfile()
                }
            }
            DestinationsFragment.SEARCH_PATH -> {
                val address: String = arguments.locationAddress
                if (address.isEmpty()) {
                    showSharedLocation()
                    viewModel.getVehicleProfile()
                } else {
                    viewModel.getPoiInfo(address)
                }
            }
        }
    }

    private fun showSharedLocation() {
        locationDetails = arguments.locationDetail
        if (locationDetails != null) {
            showOnMap()
        }
    }

    override fun onMapReady(googleMap: GoogleMap) {
        locationDetails?.let {
            val latLng =
                LatLng(
                    it.locCoordinate?.latitude ?: 0.0,
                    it.locCoordinate?.longitude ?: 0.0,
                )
            viewModel.getLocationDetails(latLng)
            updatePreferenceUi()
            val iconBitmap = AppCompatResources.getDrawable(requireContext(), mapIconRes)?.toBitmap()
            val marker =
                MarkerOptions()
                    .position(latLng)
                    .title(ToyotaConstants.EMPTY_STRING)
                    .snippet(ToyotaConstants.EMPTY_STRING)
                    .icon(iconBitmap?.let { it1 -> BitmapDescriptorFactory.fromBitmap(it1) })
                    .anchor(0.5f, 1f)
            googleMap.addMarker(marker)
            googleMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, 12f))

            // get the address and phoneNumber from locationDetails and set it in textView when map is ready
            getAddressAndPhone(locationDetails)
        }
    }

    private fun getAddressAndPhone(locationDetails: LocationDetails?) {
        viewDataBinding.shareItemAddressValue.text =
            locationDetails?.address.let {
                "${it?.houseNumber} ${it?.street}"
            }
        viewDataBinding.shareItemAddressValueTwo.text =
            locationDetails?.address.let {
                "${it?.city} , ${it?.adminRegionShort} ${it?.postalCode}"
            }

        if (!locationDetails?.phoneNumber.isNullOrEmpty()) {
            viewDataBinding.shareItemPhoneValue.text = locationDetails?.phoneNumber
            viewDataBinding.shareItemPhoneValue.visibility = View.VISIBLE
        } else {
            viewDataBinding.shareItemPhoneValue.visibility = View.GONE
        }
    }

    private fun showOnMap() {
        if (null == mapFragment) {
            mapFragment =
                childFragmentManager.findFragmentById(R.id.share_poi_details_map) as?
                    SupportMapFragment
        }
        mapFragment?.getMapAsync(this)
    }

    private fun updatePreferenceUi() {
        // Updating strings and map ICon based on preference.
        // We are updating Address heading only after saving as
        // a specific navigation.(Home/Work)
        viewDataBinding.shareItemNameTxt.text = locationDetails?.name
        mapIconRes = R.drawable.ic_destination_favorite_pin
        if (pathOfShare == DestinationsFragment.VIEW_PATH) {
            when (currentPreference) {
                DestinationsFragment.HOME_PREFERENCE -> {
                    mapIconRes = R.drawable.ic_destination_home_pin
                    viewDataBinding.shareItemNameTxt.text = getString(R.string.save_home)
                }
                DestinationsFragment.WORK_PREFERENCE -> {
                    mapIconRes = R.drawable.ic_destination_work_pin
                    viewDataBinding.shareItemNameTxt.text = getString(R.string.save_work)
                }
            }
        }
        /*
         * In case of save icon , we will show navigation
         * type to User
         */
        when (currentPreference) {
            DestinationsFragment.HOME_PREFERENCE -> {
                viewDataBinding.tvShareFavorites.setCompoundDrawablesWithIntrinsicBounds(
                    null,
                    ResourcesCompat.getDrawable(resources, R.drawable.ic_home, context?.theme),
                    null,
                    null,
                )
            }
            DestinationsFragment.WORK_PREFERENCE -> {
                viewDataBinding.tvShareFavorites.setCompoundDrawablesWithIntrinsicBounds(
                    null,
                    ResourcesCompat.getDrawable(resources, R.drawable.ic_work, context?.theme),
                    null,
                    null,
                )
            }
            DestinationsFragment.FAV_PREFERENCE -> {
                viewDataBinding.tvShareFavorites.isSelected = isFavorite
            }
        }
    }

    companion object {
        const val ROUTE_NAME: String = "SharePOIFragment"
    }
}
