package com.toyota.oneapp.ui.destinations.destination

import android.content.res.Resources
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.oneapp.R
import com.toyota.oneapp.model.dashboard.card.MyDestinationsItem
import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.LocationUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import io.grpc.stub.StreamObserver
import javax.inject.Inject

@HiltViewModel
class DestinationsViewModel
    @Inject
    constructor(
        private val userProfileAPIManager: UserProfileAPIManager,
        private val resources: Resources,
    ) : BaseViewModel() {
        private val mRemovedLocation = MutableLiveData<LocationDetails>()
        val removedLocation: LiveData<LocationDetails> get() = mRemovedLocation

        private val mLocationFromUserProfile = MutableLiveData<MyDestinationsItem>()
        val locationFromUserProfile: LiveData<MyDestinationsItem> get() =
            mLocationFromUserProfile

        var locationUpdated = MutableLiveData<Boolean>()

        fun removeLocation(
            currentLocationItem: LocationDetails,
            currentPreference: String,
        ) {
            showProgress()

            userProfileAPIManager.updateLocation(
                currentLocationItem,
                currentPreference,
                object : StreamObserver<ProfileServiceServer.UpdateUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.UpdateUserProfileResponse) {
                        mRemovedLocation.postValue(currentLocationItem)
                    }

                    override fun onError(t: Throwable) {
                        hideProgress()
                        if (t.message == resources.getString(R.string.grpc_channel_null_exception)) {
                            showErrorMessage(t.message)
                        }
                    }

                    override fun onCompleted() {
                        hideProgress()
                    }
                },
            )
        }

        fun getLocationDetails() {
            showProgress()

            userProfileAPIManager.getUserProfile(
                object :
                    StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.GetUserProfileResponse) {
                        hideProgress()
                        mLocationFromUserProfile.postValue(
                            LocationUtil.getLocationDetails(value.userProfile),
                        )
                    }

                    override fun onError(t: Throwable) {
                        hideProgress()
                    }

                    override fun onCompleted() {
                        hideProgress()
                    }
                },
            )
        }
    }
