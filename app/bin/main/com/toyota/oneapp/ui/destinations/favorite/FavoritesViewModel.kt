package com.toyota.oneapp.ui.destinations.favorite

import android.content.res.Resources
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.oneapp.R
import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import io.grpc.stub.StreamObserver
import java.util.*
import javax.inject.Inject

@HiltViewModel
class FavoritesViewModel
    @Inject
    constructor(
        private val userProfileAPIManager: UserProfileAPIManager,
        private val resources: Resources,
    ) : BaseViewModel() {
        private val mFavouriteDetailsUserProfile = MutableLiveData<ProfileServiceServer.UserProfile>()
        val favouriteDetailsUserProfile: LiveData<ProfileServiceServer.UserProfile>
            get() = mFavouriteDetailsUserProfile

        private val mUpdateFavLocation = MutableLiveData<ArrayList<LocationDetails>>()
        val updateFavLocation: LiveData<ArrayList<LocationDetails>> get() = mUpdateFavLocation

        val isFavoriteAvailable = ObservableBoolean(true)

        fun getFavoriteDetails() {
            showProgress()
            userProfileAPIManager.getUserProfile(
                object :
                    StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.GetUserProfileResponse) {
                        isFavoriteAvailable.set(
                            value.userProfile.commonVehicleSettings.navigationSettings.favorites.valueList
                                .isNotEmpty(),
                        )
                        mFavouriteDetailsUserProfile.postValue(value.userProfile)
                    }

                    override fun onError(t: Throwable) {
                        hideProgress()
                        isFavoriteAvailable.set(false)
                        if (t.message == resources.getString(R.string.grpc_channel_null_exception)) {
                            showErrorMessage(t.message)
                        }
                    }

                    override fun onCompleted() {
                        hideProgress()
                    }
                },
            )
        }

        fun updateFavoriteLocation(favList: ArrayList<LocationDetails>) {
            showProgress()
            userProfileAPIManager.updateFavouriteLocation(
                favList,
                object : StreamObserver<ProfileServiceServer.UpdateUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.UpdateUserProfileResponse) {
                        mUpdateFavLocation.postValue(favList)
                        isFavoriteAvailable.set(favList.isNotEmpty())
                        hideProgress()
                    }

                    override fun onError(t: Throwable) {
                        hideProgress()
                        isFavoriteAvailable.set(false)
                    }

                    override fun onCompleted() {
                        hideProgress()
                    }
                },
            )
        }
    }
