package com.toyota.oneapp.ui.destinations.destination

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemDestinationBinding
import com.toyota.oneapp.model.poi.LocationDetails

class DestinationItemAdapter(
    private val destinationList: ArrayList<LocationDetails>,
    private val destinationActionListener: LocationListener,
) : RecyclerView.Adapter<DestinationItemAdapter.LocationViewHolder>() {
    inner class LocationViewHolder(
        private val binding: ItemDestinationBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(location: LocationDetails) {
            binding.apply {
                destination = location
                executePendingBindings()
                rlDestinationItem.setOnClickListener {
                    destinationActionListener.onItemClickListener(location)
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): LocationViewHolder {
        val binding =
            DataBindingUtil.inflate<ItemDestinationBinding>(
                LayoutInflater.from(parent.context),
                R.layout.item_destination,
                parent,
                false,
            )

        return LocationViewHolder(binding)
    }

    override fun getItemCount(): Int = destinationList.size

    override fun onBindViewHolder(
        holder: LocationViewHolder,
        position: Int,
    ) = holder.bind(destinationList[position])

    interface LocationListener {
        fun onItemClickListener(locationDetails: LocationDetails)
    }
}
