package com.toyota.oneapp.ui.destinations.sharePOI

import android.content.res.Resources
import android.location.Location
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.maps.model.LatLng
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.oneapp.R
import com.toyota.oneapp.model.poi.Address
import com.toyota.oneapp.model.poi.Coordinates
import com.toyota.oneapp.model.poi.Directions
import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.model.poi.SendPOIToCarRequest
import com.toyota.oneapp.model.poi.SendToCarLocation
import com.toyota.oneapp.model.poi.SharePOIRequest
import com.toyota.oneapp.model.poi.SharePOIResponse
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.network.api.repository.DirectionServiceRepository
import com.toyota.oneapp.network.api.repository.LocationRepository
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import io.grpc.stub.StreamObserver
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import toyotaone.commonlib.location.UserLocationProvider
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@HiltViewModel
class SharePOIViewModel
    @Inject
    constructor(
        val resources: Resources,
        val preferenceModel: OneAppPreferenceModel,
        val userProfileAPIManager: UserProfileAPIManager,
        private val directionServiceRepository: DirectionServiceRepository,
        private val sharePoiRepository: LocationRepository,
        private val userLocationProvider: UserLocationProvider,
    ) : BaseViewModel() {
        private val mUpdateLocation = MutableLiveData<SharePOIResponse>()
        val updateLocation: LiveData<SharePOIResponse> get() = mUpdateLocation

        private val mUpdatedLocation = MutableLiveData<String>()
        val updatedLocation: LiveData<String> get() = mUpdatedLocation

        private val mUpdatedFavLocation = MutableLiveData<ArrayList<LocationDetails>?>()
        val updatedFavLocation: LiveData<ArrayList<LocationDetails>?> get() = mUpdatedFavLocation

        private val mUpdateVehicleProfile = MutableLiveData<ProfileServiceServer.UserProfile>()
        val updateVehicleProfile: LiveData<ProfileServiceServer.UserProfile> get() =
            mUpdateVehicleProfile

        private val mUpdateLocationInfo = MutableLiveData<Directions>()
        val updateLocationInfo: LiveData<Directions> get() = mUpdateLocationInfo

        val noLocationPermissionsObservable = SingleLiveEvent<(Boolean) -> Unit>()
        val systemLocationDisabledObservable = SingleLiveEvent<ResolvableApiException>()

        companion object {
            private const val TAG: String = "SharePOIViewModel"
        }

        fun updateLocation(
            currentLocationItem: LocationDetails,
            preferenceType: String,
        ) {
            showProgress()
            userProfileAPIManager.updateLocation(
                currentLocationItem,
                preferenceType,
                object : StreamObserver<ProfileServiceServer.UpdateUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.UpdateUserProfileResponse) {
                        mUpdatedLocation.postValue(preferenceType)
                    }

                    override fun onError(t: Throwable) {
                        LogTool.e(TAG, "updateLocation failed", t)
                        hideProgress()
                    }

                    override fun onCompleted() {
                        hideProgress()
                    }
                },
            )
        }

        fun updateFavoriteLocation(favList: ArrayList<LocationDetails>) {
            showProgress()
            userProfileAPIManager.updateFavouriteLocation(
                favList,
                object : StreamObserver<ProfileServiceServer.UpdateUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.UpdateUserProfileResponse) {
                        mUpdatedFavLocation.postValue(favList)
                    }

                    override fun onError(t: Throwable) {
                        LogTool.e(TAG, "updateFavoriteLocation failed", t)
                        hideProgress()
                    }

                    override fun onCompleted() {
                        hideProgress()
                    }
                },
            )
        }

        fun getVehicleProfile() {
            showProgress()
            userProfileAPIManager.getUserProfile(
                object :
                    StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.GetUserProfileResponse) {
                        mUpdateVehicleProfile.postValue(value.userProfile)
                    }

                    override fun onError(t: Throwable) {
                        LogTool.e(TAG, "getVehicleProfile failed", t)
                        hideProgress()
                        if (t.message == resources.getString(R.string.grpc_channel_null_exception)) {
                            showErrorMessage(t.message)
                        }
                    }

                    override fun onCompleted() {
                        hideProgress()
                    }
                },
            )
        }

        fun getLocationDetails(latLang: LatLng) {
            // Get our location provider running
            userLocationProvider.startLocationUpdates()

            val locationResult =
                userLocationProvider.getUserLocationAsync(
                    object :
                        UserLocationProvider.LocationCallback {
                        override fun locationReceived(location: Location) {
                            findCurrentPlace(location, latLang)
                        }
                    },
                )

            when (locationResult) {
                UserLocationProvider.LocationRequestResult.PERMISSION_NOT_GRANTED -> {
                    noLocationPermissionsObservable.value = { permissionGranted ->
                        if (permissionGranted) {
                            // Call this function again
                            getLocationDetails(latLang)
                        }
                    }
                }
                UserLocationProvider.LocationRequestResult.SYSTEM_LOCATION_DISABLED -> {
                    userLocationProvider.enableSystemLocation { exception ->
                        // System toggle is disabled. Send the user over to the system activity
                        systemLocationDisabledObservable.value = exception
                    }
                }
                UserLocationProvider.LocationRequestResult.FETCHING_LOCATION -> {
                    showProgress()
                }
            }
        }

        fun findCurrentPlace(
            location: Location,
            poiLatLong: LatLng,
        ) {
            viewModelScope.launch {
                showProgress()
                val resource =
                    directionServiceRepository.getDirection(
                        location.latitude.toString() +
                            "," + location.longitude.toString(),
                        poiLatLong.latitude.toString() + "," + poiLatLong.longitude.toString(),
                    )
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.let { mUpdateLocationInfo.postValue(it) }
                    }
                    is Resource.Failure -> {
                        resource.error?.message?.let { showErrorMessage(it) }
                    }
                    else -> {
                    }
                }
            }
        }

        fun sendPOIToCar(locationDetails: LocationDetails) {
            val location =
                Coordinates(
                    locationDetails.locCoordinate?.latitude ?: 0.0,
                    locationDetails.locCoordinate?.longitude ?: 0.0,
                )
            val sendToCarLocation =
                SendToCarLocation(
                    location.latitude,
                    location.longitude,
                    "google_maps",
                    locationDetails.name ?: ToyotaConstants.EMPTY_STRING,
                    location,
                    locationDetails.formattedAddress ?: ToyotaConstants.EMPTY_STRING,
                    locationDetails.address ?: Address(),
                    locationDetails.location_type ?: 0,
                    location,
                    locationDetails.placeId ?: ToyotaConstants.EMPTY_STRING,
                    locationDetails.phoneNumber ?: ToyotaConstants.EMPTY_STRING,
                )
            val poiRequest =
                SendPOIToCarRequest(
                    preferenceModel.getGuid(),
                    locationDetails.formattedAddress ?: ToyotaConstants.EMPTY_STRING,
                    sendToCarLocation,
                )
            viewModelScope.launch {
                showProgress()
                val resource = sharePoiRepository.sendPOIToCar(poiRequest)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        showSuccessToastMessage(R.string.send_to_car_message)
                    }
                    is Resource.Failure -> {
                        resource.error?.message?.let { showErrorMessage(it) }
                    }
                    else -> {
                        // Do nothing.
                    }
                }
            }
        }

        fun getPoiInfo(sharedText: String) {
            val locationRequest = SharePOIRequest("android", sharedText)
            viewModelScope.launch {
                showProgress()
                val resource = sharePoiRepository.getPOIDataFromSharedText(locationRequest)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.payload?.let {
                            mUpdateLocation.postValue(it)
                        }
                    }
                    else -> {
                        showLocationError(resource)
                    }
                }
            }
        }

        private fun showLocationError(vehicleDetailResponse: Resource<ApiResponse<SharePOIResponse?>?>) {
            vehicleDetailResponse.error?.message?.let {
                // Currently we are matching with wrong error message since there is an issue with parsing error in coroutines.
                if (resources
                        .getString(R.string.generic_error)
                        .equals(it, ignoreCase = true)
                ) {
                    showErrorMessage(R.string.search_destination_error)
                } else {
                    showErrorMessage(it)
                }
            }
        }
    }
