package com.toyota.oneapp.ui.destinations.destination

import android.os.Bundle
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.activityViewModels
import androidx.navigation.NavController
import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.FragmentMyDestinationsBinding
import com.toyota.oneapp.extensions.filterInvalidCharacters
import com.toyota.oneapp.model.dashboard.card.MyDestinationsItem
import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.model.poi.exists
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.util.LocationUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class DestinationsFragment : BaseDataBindingFragment<FragmentMyDestinationsBinding>() {
    private val viewModel: DestinationsViewModel by activityViewModels()
    private var myDestinationsItem: MyDestinationsItem? = MyDestinationsItem()
    private var currentPreference = ToyotaConstants.EMPTY_STRING
    private lateinit var bottomSheetBehavior: BottomSheetBehavior<View>
    private val recentDestinations: ArrayList<LocationDetails> by lazy {
        myDestinationsItem?.recentDestinations ?: arrayListOf()
    }

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    companion object {
        const val EXTRAS_MY_DESTINATIONS = "DESTINATION_ITEMS"
        const val PREFERENCE_TYPE: String = "preference"
        const val HOME_PREFERENCE: String = "home"
        const val WORK_PREFERENCE: String = "work"
        const val FAV_PREFERENCE: String = "fav"
        const val SEARCH_PATH = "search"
        const val VIEW_PATH = "view"
        const val SEARCH_ALL = "search-all"
        const val MAX_DESTINATIONS_LENGTH = 20
    }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(viewModel)
        activity?.onBackPressedDispatcher?.addCallback(
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    if (BottomSheetBehavior.STATE_HIDDEN != bottomSheetBehavior.state) {
                        bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
                    } else {
                        isEnabled = false
                        activity?.onBackPressed()
                    }
                }
            },
        )
        handleExtras()
        viewModel.getLocationDetails()
    }

    override fun onViewBound(
        binding: FragmentMyDestinationsBinding,
        savedInstance: Bundle?,
    ) {
        binding.destinationsViewModel = viewModel
        setupUi()
        initObserveModels()
    }

    private fun initObserveModels() {
        viewModel.removedLocation.observe(this) { item ->
            successRemovedLocation(item)
        }

        viewModel.locationFromUserProfile.observe(this) {
            myDestinationsItem = it
            updateLocationFromUserProfile()
        }

        viewModel.locationUpdated.observe(this) {
            viewModel.getLocationDetails()
        }
    }

    override fun getLayout(): Int = R.layout.fragment_my_destinations

    fun onBackPressed() {
        findNavController().popBackStack()
    }

    override fun onResume() {
        super.onResume()
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
    }

    private fun handleExtras() {
        currentPreference = ToyotaConstants.EMPTY_STRING

        // handle POI share from google/yelp app
        when (val route = activity?.intent?.getStringExtra(ToyotaConstants.DASHBOARD_ROUTE)) {
            getString(R.string.deep_link_share_poi) -> {
                val sharedText =
                    activity?.intent?.getStringExtra(ToyotaConstants.DASHBOARD_POI_DATA)
                        ?: ToyotaConstants.EMPTY_STRING // it's an address
                findNavController().navigate(
                    DestinationsFragmentDirections.actionSharePoi(
                        currentPreference,
                        null,
                        route,
                        sharedText,
                    ),
                )
            }
            VIEW_PATH -> {
                currentPreference =
                    activity?.intent?.getStringExtra(PREFERENCE_TYPE)
                        ?: ToyotaConstants.EMPTY_STRING
                val locationDetails: LocationDetails? =
                    activity?.intent?.getParcelableExtra(LocationUtil.LOCATION_OBJECT)
                findNavController().navigate(
                    DestinationsFragmentDirections.actionSharePoi(
                        currentPreference,
                        locationDetails,
                        route,
                        ToyotaConstants.EMPTY_STRING,
                    ),
                )
            }
            else -> {
                when (activity?.intent?.getStringExtra(PREFERENCE_TYPE)) {
                    HOME_PREFERENCE -> {
                        startSearchDestinationFragment(HOME_PREFERENCE)
                    }
                    WORK_PREFERENCE -> {
                        startSearchDestinationFragment(WORK_PREFERENCE)
                    }
                    FAV_PREFERENCE -> {
                        findNavController().navigate(
                            DestinationsFragmentDirections.actionFavoritesList(),
                        )
                    }
                }
            }
        }
    }

    private fun setupUi() {
        bottomSheetBehavior = BottomSheetBehavior.from(viewDataBinding.buttonSheetNavigation)

        bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
        viewDataBinding.homeMainCl.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.MYDEST_TOTAL_USERS_SETUP_HOME_CTA)
            viewDataBinding.closeDetailPrefTv.text = getString(R.string.save_home)
            if (true == myDestinationsItem?.home?.exists()) {
                currentPreference = HOME_PREFERENCE
                if (bottomSheetBehavior.state != BottomSheetBehavior.STATE_EXPANDED) {
                    viewDataBinding.updateWorkPrefTv.text =
                        String.format(
                            getString(R.string.update_destination),
                            getString(R.string.save_home_small),
                        )
                    viewDataBinding.removePrefTv.text =
                        String.format(
                            getString(R.string.remove_destination),
                            getString(R.string.save_home_small),
                        )
                    bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
                } else {
                    bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
                }
            } else {
                startSearchDestinationFragment(HOME_PREFERENCE)
            }
        }

        viewDataBinding.etMapSearch.setOnClickListener {
            startSearchDestinationFragment(SEARCH_ALL)
        }
        viewDataBinding.favMainCl.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.MYDEST_TOTAL_USERS_SETUP_FAVORITES_CTA)
            findNavController().navigate(DestinationsFragmentDirections.actionFavoritesList())
        }
        viewDataBinding.stcMainCl.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.MYDEST_TOTAL_USERS_VIEW_SENDTOCAR_CTA)
            findNavController().safeNavigate(DestinationsFragmentDirections.actionStcLocations())
        }
        viewDataBinding.workMainCl.setOnClickListener {
            analyticsLogger.logEvent(AnalyticsEvent.MYDEST_TOTAL_USERS_SETUP_WORK_CTA)
            viewDataBinding.closeDetailPrefTv.text = getString(R.string.save_work)
            if (myDestinationsItem?.work?.exists() == true) {
                currentPreference = WORK_PREFERENCE
                if (bottomSheetBehavior.state != BottomSheetBehavior.STATE_EXPANDED) {
                    viewDataBinding.updateWorkPrefTv.text =
                        String.format(
                            getString(R.string.update_destination),
                            getString(R.string.save_work_small),
                        )
                    viewDataBinding.removePrefTv.text =
                        String.format(
                            getString(R.string.remove_destination),
                            getString(R.string.save_work_small),
                        )
                    bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
                } else {
                    bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
                }
            } else {
                startSearchDestinationFragment(WORK_PREFERENCE)
            }
        }

        viewDataBinding.viewMainBottomCl.setOnClickListener {
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
            myDestinationsItem?.let {
                var location: LocationDetails? = null
                when (currentPreference) {
                    HOME_PREFERENCE -> {
                        location = myDestinationsItem?.home
                    }
                    WORK_PREFERENCE -> {
                        location = myDestinationsItem?.work
                    }
                }

                findNavController().navigate(
                    DestinationsFragmentDirections.actionSharePoi(
                        currentPreference,
                        location,
                        VIEW_PATH,
                        ToyotaConstants.EMPTY_STRING,
                    ),
                )
            }
        }
        viewDataBinding.updateWorkMainCl.setOnClickListener {
            startSearchDestinationFragment(currentPreference)
        }
        viewDataBinding.removeMainCl.setOnClickListener {
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
            removeLocation()
        }
        viewDataBinding.myDestinationInfoToolbar.setOnClickListener {
            onBackPressed()
        }

        viewDataBinding.closeMainBottomCl.setOnClickListener {
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
        }
        viewDataBinding.rvMapCategory.run {
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)

            adapter =
                DestinationItemAdapter(
                    recentDestinations,
                    object : DestinationItemAdapter.LocationListener {
                        override fun onItemClickListener(locationDetails: LocationDetails) {
                            var preference = ToyotaConstants.EMPTY_STRING
                            var route: String = SEARCH_PATH
                            if (checkRecentAlreadyExist(locationDetails)) {
                                route = VIEW_PATH
                                preference = currentPreference
                            }
                            findNavController().navigate(
                                DestinationsFragmentDirections.actionSharePoi(
                                    preference,
                                    locationDetails,
                                    route,
                                    ToyotaConstants.EMPTY_STRING,
                                ),
                            )
                        }
                    },
                )
        }
        viewDataBinding.myDestinationInfoToolbar.setNavigationOnClickListener {
            activity?.onBackPressed()
        }
    }

    private fun removeLocation() {
        viewModel.removeLocation(LocationDetails(), currentPreference)
    }

    private fun updateUi() {
        myDestinationsItem?.let {
            if (it.home.exists()) {
                viewDataBinding.homePrefDotIv.setImageResource(R.drawable.vertical_dots)
                viewDataBinding.homeSetupTv.visibility = View.GONE
            } else {
                viewDataBinding.homePrefDotIv.setImageResource(R.drawable.chevron_right)
                viewDataBinding.homeSetupTv.visibility = View.VISIBLE
            }
            if (it.work.exists()) {
                viewDataBinding.workPrefDotIv.setImageResource(R.drawable.vertical_dots)
                viewDataBinding.workSetupTv.visibility = View.GONE
            } else {
                viewDataBinding.workPrefDotIv.setImageResource(R.drawable.chevron_right)
                viewDataBinding.workSetupTv.visibility = View.VISIBLE
            }
            viewDataBinding.rvMapCategory.adapter?.notifyDataSetChanged()
        }
    }

    private fun startSearchDestinationFragment(preference: String) {
        findNavController().navigate(
            DestinationsFragmentDirections.actionSearchDestination(preference),
        )
    }

    private fun successRemovedLocation(currentLocationItem: LocationDetails) {
        activity?.runOnUiThread {
            when (currentPreference) {
                HOME_PREFERENCE -> {
                    this.myDestinationsItem?.home = currentLocationItem
                }
                WORK_PREFERENCE -> {
                    this.myDestinationsItem?.work = currentLocationItem
                }
            }
            updateUi()
        }
    }

    private fun updateLocationFromUserProfile() {
        activity?.runOnUiThread {
            recentDestinations.clear()
            recentDestinations.addAll(
                myDestinationsItem
                    ?.recentDestinations
                    ?.take(MAX_DESTINATIONS_LENGTH)
                    ?: arrayListOf(),
            )
            filterInvalidCharacters(recentDestinations)
            updateUi()
        }
    }

    // If recent already exists in any of location category we will not request for save again.
    private fun checkRecentAlreadyExist(locationDetails: LocationDetails): Boolean {
        var isExists = false
        myDestinationsItem?.let {
            if (it.home.exists() && it.home.placeId == locationDetails.placeId) {
                currentPreference = HOME_PREFERENCE
                isExists = true
            } else if (it.work.exists() && it.work.placeId == locationDetails.placeId) {
                currentPreference = WORK_PREFERENCE
                isExists = true
            } else {
                it.favList
                    .find { fav ->
                        fav.exists() && fav.placeId == locationDetails.placeId
                    }?.let {
                        currentPreference = FAV_PREFERENCE
                        isExists = true
                    }
            }
        }
        return isExists
    }
}

fun NavController.safeNavigate(direction: NavDirections) {
    currentDestination?.getAction(direction.actionId)?.run { navigate(direction) }
}
