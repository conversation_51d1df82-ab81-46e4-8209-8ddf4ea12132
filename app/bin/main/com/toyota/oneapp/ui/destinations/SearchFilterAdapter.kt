package com.toyota.oneapp.ui.destinations

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R

class SearchFilterAdapter(
    private val context: Context,
    private val addressList: ArrayList<PlaceInfo>,
    private val searchFilterListener: SearchFilterListener,
) : RecyclerView.Adapter<SearchFilterAdapter.SearchFilterHolder>() {
    inner class SearchFilterHolder(
        itemView: View,
    ) : RecyclerView.ViewHolder(itemView) {
        internal var addressName: TextView = itemView.findViewById(R.id.address_name_tv)
        internal var completeAddress: TextView = itemView.findViewById(R.id.address_complete_tv)
        internal var layout: View = itemView.findViewById(R.id.location_item_main_ll)
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): SearchFilterHolder {
        val view =
            LayoutInflater.from(context).inflate(R.layout.search_destination_item, parent, false)
        return SearchFilterHolder(view)
    }

    override fun getItemCount(): Int = addressList.size

    override fun onBindViewHolder(
        holder: SearchFilterHolder,
        position: Int,
    ) {
        val address = addressList[position]
        holder.addressName.text = address.heading
        holder.completeAddress.text = address.description
        holder.layout.setOnClickListener {
            searchFilterListener.onItemClickListener(address.heading + " " + address.description)
        }
    }

    interface SearchFilterListener {
        fun onItemClickListener(placeId: String)
    }
}
