package com.toyota.oneapp.ui

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.google.common.base.Strings
import com.toyota.oneapp.viewmodel.model.LexusUpdateModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
open class BaseViewModel
    @Inject
    constructor() : ViewModel() {
        // There are multiple categories because SingleLiveEvent only guarantees that the latest event
        // actually reaches the observer. If multiple values are posted to the same SingleLiveEvent
        // object at once (for example if the vm wanted to show a toast, hide the soft keyboard, and
        // stop the loading indicator all at once, there is a good chance some of those events could be
        // swallowed.
        //
        // If you want to add new events and they may be called simultaneously with other events,
        // ensure you add them into different categories to avoid race conditions. Create a new category
        // if needed.
        private val _navigationEvents = SingleLiveEvent<BaseViewModelNavigationEvent>()
        val navigationEvents: LiveData<BaseViewModelNavigationEvent> = _navigationEvents

        // ProgressEvents is NOT a singleLive event because we want to pick up the last value after navigation.
        // We also want to not show progress initially.
        private val _progressEvents =
            MutableLiveData<BaseViewModelNavigationEvent>(
                BaseViewModelNavigationEvent.HideProgress,
            )
        val progressEvents: LiveData<BaseViewModelNavigationEvent> = _progressEvents

        private val _toastEvents = SingleLiveEvent<BaseViewModelNavigationEvent>()
        val toastEvents: LiveData<BaseViewModelNavigationEvent> = _toastEvents

        private val _messageEvents = SingleLiveEvent<BaseViewModelNavigationEvent>()
        val messageEvents: LiveData<BaseViewModelNavigationEvent> = _messageEvents
        protected val mSetLexusGeneration = MutableLiveData<LexusUpdateModel>()

        fun finishActivity(
            resultCode: Int = Activity.RESULT_CANCELED,
            data: Intent? = null,
        ) {
            _navigationEvents.postValue(BaseViewModelNavigationEvent.FinishActivity(resultCode, data))
        }

        fun hideSoftKeyboard() {
            _navigationEvents.postValue(BaseViewModelNavigationEvent.HideSoftKeyboard)
        }

        open fun showProgress() {
            _progressEvents.postValue(BaseViewModelNavigationEvent.ShowProgress)
        }

        open fun hideProgress() {
            _progressEvents.postValue(BaseViewModelNavigationEvent.HideProgress)
        }

        fun showErrorMessage(errorMessage: String?) {
            _messageEvents.postValue(
                BaseViewModelNavigationEvent.ShowErrorMessage(Strings.nullToEmpty(errorMessage)),
            )
        }

        fun showErrorMessageAndNavigateBack(errorMessage: String?) {
            _messageEvents.postValue(
                BaseViewModelNavigationEvent.ShowErrorMessageAndNavigateBack(
                    Strings.nullToEmpty(errorMessage),
                ),
            )
        }

        fun showSuccessToastMessage(message: String) {
            _toastEvents.postValue(
                BaseViewModelNavigationEvent.ShowSuccessToast(if (message.isNotBlank()) message else ""),
            )
        }

        fun showErrorToastMessage(errorMessage: String) {
            _toastEvents.postValue(
                BaseViewModelNavigationEvent.ShowErrorToast(
                    if (errorMessage.isNotBlank()) errorMessage else "",
                ),
            )
        }

        fun showErrorMessage(errorMessageId: Int) {
            _messageEvents.postValue(BaseViewModelNavigationEvent.ShowErrorMessageId(errorMessageId))
        }

        fun showErrorMessageAndNavigateBack(errorMessageId: Int) {
            _messageEvents.postValue(
                BaseViewModelNavigationEvent.ShowErrorMessageIdAndNavigateBack(errorMessageId),
            )
        }

        fun showSuccessToastMessage(messageId: Int) {
            _toastEvents.postValue(BaseViewModelNavigationEvent.ShowSuccessToastId(messageId))
        }

        fun showErrorToastMessage(errorMessageId: Int) {
            _toastEvents.postValue(BaseViewModelNavigationEvent.ShowErrorToastId(errorMessageId))
        }

        fun showErrorMessage(
            errorMessageId: Int,
            vararg args: Any?,
        ) {
            _messageEvents.postValue(
                BaseViewModelNavigationEvent.ShowErrorMessageFormat(
                    errorMessageId,
                    *args,
                ),
            )
        }

        fun showErrorMessageAndNavigateBack(
            errorMessageId: Int,
            vararg args: Any?,
        ) {
            _messageEvents.postValue(
                BaseViewModelNavigationEvent.ShowErrorMessageFormatAndNavigateBack(
                    errorMessageId,
                    *args,
                ),
            )
        }

        fun showSuccessToastMessage(
            messageId: Int,
            vararg args: Any?,
        ) {
            _toastEvents.postValue(
                BaseViewModelNavigationEvent.ShowSuccessToastFormat(messageId, *args),
            )
        }

        fun showErrorToastMessage(
            errorMessageId: Int,
            vararg args: Any?,
        ) {
            _toastEvents.postValue(
                BaseViewModelNavigationEvent.ShowErrorToastFormat(
                    errorMessageId,
                    *args,
                ),
            )
        }

        fun showRegularToastMessage(errorMessageId: Int) {
            _toastEvents.postValue(BaseViewModelNavigationEvent.ShowRegularToast(errorMessageId))
        }

        fun showUnsupportedInDemo() {
            _messageEvents.postValue(BaseViewModelNavigationEvent.ShowUnsupportedInDemo)
        }

        fun openWebView(url: String) {
            _messageEvents.postValue(BaseViewModelNavigationEvent.OpenWebView(url))
        }

        fun showProxyDetectedAlert() {
            _navigationEvents.postValue(BaseViewModelNavigationEvent.ShowProxyDetectedAlert)
        }

        fun showMessageDialog(message: String) {
            _messageEvents.postValue(BaseViewModelNavigationEvent.ShowMessageDialog(message))
        }
    }

sealed class BaseViewModelNavigationEvent {
    object ShowProgress : BaseViewModelNavigationEvent()

    object HideProgress : BaseViewModelNavigationEvent()

    object HideSoftKeyboard : BaseViewModelNavigationEvent()

    data class FinishActivity(
        val resultCode: Int,
        val data: Intent?,
    ) : BaseViewModelNavigationEvent()

    data class ShowErrorMessage(
        val errorMessage: String,
    ) : BaseViewModelNavigationEvent()

    data class ShowErrorMessageAndNavigateBack(
        val errorMessage: String,
    ) : BaseViewModelNavigationEvent()

    data class ShowErrorToast(
        val errorMessage: String,
    ) : BaseViewModelNavigationEvent()

    data class ShowSuccessToast(
        val message: String,
    ) : BaseViewModelNavigationEvent()

    data class ShowErrorMessageId(
        val errorMessageId: Int,
    ) : BaseViewModelNavigationEvent()

    data class ShowErrorMessageIdAndNavigateBack(
        val errorMessageId: Int,
    ) : BaseViewModelNavigationEvent()

    data class ShowErrorToastId(
        val errorMessageId: Int,
    ) : BaseViewModelNavigationEvent()

    data class ShowSuccessToastId(
        val messageId: Int,
    ) : BaseViewModelNavigationEvent()

    data class ShowRegularToast(
        val messageId: Int,
    ) : BaseViewModelNavigationEvent()

    class ShowErrorMessageFormat(
        val errorMessageId: Int,
        vararg val args: Any?,
    ) : BaseViewModelNavigationEvent()

    class ShowErrorMessageFormatAndNavigateBack(
        val errorMessageId: Int,
        vararg val args: Any?,
    ) : BaseViewModelNavigationEvent()

    class ShowErrorToastFormat(
        val errorMessageId: Int,
        vararg val args: Any?,
    ) : BaseViewModelNavigationEvent()

    class ShowSuccessToastFormat(
        val messageId: Int,
        vararg val args: Any?,
    ) : BaseViewModelNavigationEvent()

    object ShowUnsupportedInDemo : BaseViewModelNavigationEvent()

    data class OpenWebView(
        val url: String,
    ) : BaseViewModelNavigationEvent()

    object ShowProxyDetectedAlert : BaseViewModelNavigationEvent()

    data class ShowMessageDialog(
        val message: String,
    ) : BaseViewModelNavigationEvent()
}
