package com.toyota.oneapp.ui

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.account.AccountManager
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.extensions.createVehicleInfo
import com.toyota.oneapp.model.pref.LanguagePreferenceModel
import com.toyota.oneapp.model.vehicle.VehicleDetail
import com.toyota.oneapp.model.vehicle.VehicleDetailResponse
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.VehicleDetailRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.launch
import javax.inject.Inject

class AddVehicleViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val accountManager: AccountManager,
        private val preferenceModel: OneAppPreferenceModel,
        private val vehicleDetailRepository: VehicleDetailRepository,
        private val languagePreferenceModel: LanguagePreferenceModel,
    ) : BaseViewModel() {
        private val mVehicleDetailResponse = MutableLiveData<VehicleDetail>()
        val vehicleDetailResponse: LiveData<VehicleDetail> get() = mVehicleDetailResponse

        /**
         * Make the service call to get the vehicle details with the VIN
         * @param vinNumber
         */
        fun getVehicleInfo(
            vinNumber: String,
            context: Context,
        ) {
            if (!applicationData.getVehicleList().isNullOrEmpty() &&
                applicationData.getVehicleList()?.any {
                    it.vin.equals(
                        vinNumber,
                        false,
                    )
                } == true
            ) {
                showErrorMessage(R.string.vechile_already_added_to_your_account)
                return
            }
            showProgress()
            viewModelScope.launch {
                val resource = vehicleDetailRepository.getVehicleDetail(vinNumber)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.let { processCarDetails(it) }
                    }
                    else -> {
                        resource.error?.message?.let {
                            showErrorMessage(
                                if (ToyotaConstants.PROCESS_ERROR == it) {
                                    context.getString(R.string.Vehicle_Details_Process_Error)
                                } else {
                                    it
                                },
                            )
                        }
                    }
                }
            }
        }

        fun logout() {
            accountManager.logout()
        }

        /**
         * @param carDetails
         */
        private fun processCarDetails(carDetails: VehicleDetailResponse?) {
            carDetails?.payload?.vehicle?.let {
                // TODO enable when Subaru comes in
            /*if(!ToyUtil.isNotSubaru() != it.isSubaruBrand){
                showSuccessToastMessage(R.string.addVehicle_vin_error)
                return
            }*/
                applicationData.generationCode = it.generation
                applicationData.saveVehicleToList(it)
                applicationData.setSelectedVehicle(
                    carDetails.payload.createVehicleInfo(
                        languagePreferenceModel,
                    ),
                )
                mVehicleDetailResponse.postValue(it)
                return
            }
            showErrorMessage(R.string.generic_error)
        }
    }
