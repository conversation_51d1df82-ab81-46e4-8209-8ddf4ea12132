package com.toyota.oneapp.ui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.model.pref.LanguagePreferenceModel
import com.toyota.oneapp.ui.vinscan.QRScanActivity
import com.toyota.oneapp.util.Brand
import com.toyota.oneapp.util.RegistrationErrorType
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class RegistrationErrorActivity : GenericErrorActivity() {
    private lateinit var errorType: RegistrationErrorType

    private lateinit var brandName: String

    @Inject
    lateinit var languagePreferenceModel: LanguagePreferenceModel

    override fun onCreate(savedInstance: Bundle?) {
        errorType = intent.getSerializableExtra(RegistrationErrorType.ERROR_TYPE) as RegistrationErrorType
        brandName =
            if (BuildConfig.APP_BRAND == Brand.LEXUS.appBrand) {
                ToyotaConstants.LEXUS
            } else if (BuildConfig.APP_BRAND == Brand.SUBARU.appBrand) {
                ToyotaConstants.SUBARU
            } else {
                ToyotaConstants.TOYOTA
            }

        super.onCreate(savedInstance)
        showVisibility()
    }

    private fun showVisibility() {
        if (errorType == RegistrationErrorType.USER_LOGIN_MISMATCH) {
            binding.textviewCall.visibility = View.VISIBLE
            val intent =
                Intent(
                    Intent.ACTION_DIAL,
                ).setData(
                    Uri.parse(
                        "tel:${
                            ToyUtil.getPhoneNO(
                                this,
                                languagePreferenceModel.getRegion().regionCode,
                                BuildConfig.APP_BRAND,
                            )}",
                    ),
                )
            binding.textviewCall.setOnClickListener {
                startActivity(intent)
            }
        }
    }

    override fun getTitleText(): String = getString(errorType.titleId)

    override fun getSecondaryText(): String =
        String.format(
            getString(errorType.detailsId),
            brandName,
            ToyUtil.getPhoneNO(
                this,
                languagePreferenceModel.getRegion().regionCode,
                BuildConfig.APP_BRAND,
            ),
        )

    override fun getFirstButtonName(): String = getString(errorType.firstButtonId)

    override fun getSecondButtonName(): String = getString(errorType.secondButtonId)

    override fun onPositiveButtonClicked() {
        startQrScanActivity(true)
    }

    override fun onNegativeButtonClicked() {
        when (errorType) {
            RegistrationErrorType.VEHICLE_LINK_FAILURE -> {
                goToDashboard()
            }
            RegistrationErrorType.USER_LOGIN_MISMATCH -> {
                goToAddVehicle()
            }
            else -> {
                startQrScanActivity(false)
            }
        }
    }

    override fun onCloseButtonClicked() {
        goToDashboard()
    }

    override fun onBackPressed() {
        goToDashboard()
    }

    private fun startQrScanActivity(showQr: Boolean) {
        startActivity(
            Intent(this, QRScanActivity::class.java).apply {
                putExtras(
                    Bundle().apply {
                        putExtra(
                            QRScanActivity.REMOTE_AUTH,
                            RegistrationErrorType.REMOTE_ACTIVATION_FAILURE == errorType,
                        )
                        putExtra(QRScanActivity.SHOW_QR_CODE, showQr)
                    },
                )
            },
        )
        finish()
    }
}
