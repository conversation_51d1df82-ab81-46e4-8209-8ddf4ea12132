package com.toyota.oneapp.ui

import com.toyota.oneapp.network.Resource

abstract class BaseViewModelFragment :
    BaseFragment(),
    BasePresenter.BaseView {
    fun observeBaseEvents(vm: BaseViewModel) {
        (requireActivity() as? BaseActivity)?.observeBaseEvents(vm)
    }

    final override fun createPresenter(): DummyPresenter = DummyPresenter()

    open fun <T> onNetworkStateChange(resource: Resource<T>?) {
        when (resource) {
            is Resource.Loading -> showProgressDialog()
            is Resource.Success -> hideProgressDialog()
            else -> {
                hideProgressDialog()
                showDialog(resource?.message)
            }
        }
    }
}
