package com.toyota.oneapp.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.databinding.Observable
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.NewLoginSettingScreenBinding
import com.toyota.oneapp.ui.accountsettings.SecuritySettingsExpActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BiometricSettingActivity : BaseActivity() {
    private val viewModel: BiometricSettingViewModel by viewModels()

    companion object {
        fun getIntentForBiometricSettingActivity(context: Context): Intent = Intent(context, BiometricSettingActivity::class.java)
    }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        val activityMainBinding =
            DataBindingUtil.setContentView<NewLoginSettingScreenBinding>(
                this,
                R.layout.new_login_setting_screen,
            )
        activityMainBinding.lifecycleOwner = this

        setUpUi(activityMainBinding)
        setUpObservers(activityMainBinding)
    }

    override fun onResume() {
        super.onResume()
        viewModel.onBioInit(this)
    }

    private fun setUpUi(viewBinding: NewLoginSettingScreenBinding) {
        onBiometricSettingEnabledChanged(viewBinding)
        onSecuritySettingEnabledChanged(viewBinding)
        onKeepMeSignInEnabledChanged(viewBinding)

        viewBinding.switchEnable.setOnCheckedChangeListener { _, isChecked ->
            viewModel.onBiometricSwitchSettingChanged(isChecked)
        }
        viewBinding.authent.setOnClickListener {
            viewModel.onRequiredAuthSettingClicked()
        }
        viewBinding.keepmeloginSwitchEnable.setOnCheckedChangeListener { _, isChecked ->
            viewModel.onKeepMeSignInSettingChanged(isChecked)
        }
        viewBinding.completePurchase.setOnClickListener {
            viewModel.onContinueClicked()
        }
    }

    private fun setUpObservers(viewBinding: NewLoginSettingScreenBinding) {
        viewModel.biometricSettingViewModelNavigationEvent.observe(this) {
            when (it) {
                is BiometricSettingViewModelNavigationEvent.Continue -> {
                    setResult(Activity.RESULT_OK)
                    finish()
                }
                is BiometricSettingViewModelNavigationEvent.RequiredAuthSetting -> {
                    startActivity(Intent(this, SecuritySettingsExpActivity::class.java))
                }
            }
        }
        viewModel.isBiometricSettingEnabled.addOnPropertyChangedCallback(
            object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(
                    sender: Observable?,
                    propertyId: Int,
                ) {
                    onBiometricSettingEnabledChanged(viewBinding)
                }
            },
        )
        viewModel.isSecuritySettingsEnabled.addOnPropertyChangedCallback(
            object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(
                    sender: Observable?,
                    propertyId: Int,
                ) {
                    onSecuritySettingEnabledChanged(viewBinding)
                }
            },
        )
        viewModel.isKeepMeSignInEnabled.addOnPropertyChangedCallback(
            object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(
                    sender: Observable?,
                    propertyId: Int,
                ) {
                    onKeepMeSignInEnabledChanged(viewBinding)
                }
            },
        )
        viewModel.biometricTimeText.observe(this) { data ->
            viewBinding.account5minutes.text = data
        }
    }

    private fun onBiometricSettingEnabledChanged(viewBinding: NewLoginSettingScreenBinding) {
        val isEnabled = viewModel.isBiometricSettingEnabled.get()
        viewBinding.switchEnable.isChecked = isEnabled
        viewBinding.authent.isClickable = isEnabled
    }

    private fun onSecuritySettingEnabledChanged(viewBinding: NewLoginSettingScreenBinding) {
        val isEnabled = viewModel.isSecuritySettingsEnabled.get()
        viewBinding.authent.isVisible = isEnabled
        viewBinding.keepmelogin.isVisible = isEnabled
    }

    private fun onKeepMeSignInEnabledChanged(viewBinding: NewLoginSettingScreenBinding) {
        val isEnabled = viewModel.isKeepMeSignInEnabled.get()
        viewBinding.keepmeloginSwitchEnable.isChecked = isEnabled
    }
}
