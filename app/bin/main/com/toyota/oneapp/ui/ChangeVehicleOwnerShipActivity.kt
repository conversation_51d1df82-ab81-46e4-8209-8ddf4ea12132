package com.toyota.oneapp.ui

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityVehicleOwnerShipChangeBinding
import com.toyota.oneapp.model.dealer.Dealer
import com.toyota.oneapp.model.dealer.DealerOld
import com.toyota.oneapp.model.vehicle.VehicleDetail
import com.toyota.oneapp.network.models.ResponseDataClass
import com.toyota.oneapp.util.IntentUtil.getManageSubscriptionIntent
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@AndroidEntryPoint
class ChangeVehicleOwnerShipActivity :
    MVPBaseActivity<AddVehicleInfoPresenter>(),
    AddVehicleInfoPresenter.View {
    @Inject lateinit var addVehicleInfoPresenter: AddVehicleInfoPresenter

    @Inject lateinit var analyticsLogger: AnalyticsLogger

    @Inject lateinit var applicationData: ApplicationData
    private lateinit var vehicle: VehicleDetail

    lateinit var binding: ActivityVehicleOwnerShipChangeBinding

    override fun createPresenter(): AddVehicleInfoPresenter = addVehicleInfoPresenter

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding =
            DataBindingUtil.setContentView<ActivityVehicleOwnerShipChangeBinding>(
                this,
                R.layout.activity_vehicle_owner_ship_change,
            )
        val toolbar = findViewById<View>(R.id.toolbar) as Toolbar
        setSupportActionBar(toolbar)
        toolbar.title = ""

        var nickName = ""
        var vehicleAssociationId = ""

        try {
            intent.let {
                nickName = it.getStringExtra(AddVehicleActivity.KEY_VEHICLE_NICK_NAME)
                    ?: throw IllegalArgumentException("NickName is mandatory")
                vehicleAssociationId = it.getStringExtra(
                    AddVehicleActivity.KEY_VEHICLE_ASSOCIATION_ID,
                )
                    ?: throw IllegalArgumentException("VehicleAssociationId is mandatory")
                vehicle = it.getParcelableExtra(AddVehicleActivity.KEY_VEHICLE_DETAILS)
                    ?: throw IllegalArgumentException("Vehicle is mandatory")
            }
            binding.changeOwnershipConfirm.setOnClickListener {
                analyticsLogger.logEvent(AnalyticsEvent.OVERRIDE_AND_ASSOCIATE_EXISTING_USER_VIN)
                addVehicleInfoPresenter.changeOwnerShipVehicle(nickName, vehicleAssociationId)
            }
        } catch (e: IllegalArgumentException) {
            LogTool.e(TAG, e.message)
        }

        binding.changeOwnershipCancel.setOnClickListener {
            super.onBackPressed()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            super.onBackPressed()
        }
        return true
    }

    override fun gotoAddVINSuccess() {
        if (!vehicle.is21MMVehicle) {
            Toast
                .makeText(
                    applicationContext,
                    getString(R.string.AddVehicle_vehicle_has_been_added),
                    Toast.LENGTH_LONG,
                ).show()
        }
        val intent: Intent
        if (vehicle.generation == null || vehicle.isNonCvtVehicle) {
            intent = Intent(this, AddedVehiclesActivity::class.java)
            intent.putExtra("IsWaiveFirstConsent", true)
            intent.putExtra(AddVehicleActivity.KEY_VEHICLE_DETAILS, vehicle)
            startActivity(intent)
            finish()
        } else {
            applicationData.getSelectedVehicle()?.let {
                intent =
                    getManageSubscriptionIntent(
                        this,
                        it,
                        true,
                        null,
                    )
                startActivity(intent)
                finish()
            }
        }
    }

    override fun isPreferredDealerAvailable(): Boolean {
        TODO("Unused")
    }

    override fun displayChangeOwnerShipAlert(payload: ResponseDataClass.Payload) {}

    override fun showVINAssociatedWithAnotherCustomerFromAPI(payload: ResponseDataClass.Payload) {}

    override fun launchResetPinScreen(lunchResetPin: Boolean) {}

    override fun setPinRequired(isPinRequired: Boolean) {}

    override fun setPreferredDealer(dealer: Dealer?) {}

    override fun setPreferredDealerOld(dealer: DealerOld?) {}

    companion object {
        const val TAG = "ChangeVehicleOwnerShipActivity"
    }
}
