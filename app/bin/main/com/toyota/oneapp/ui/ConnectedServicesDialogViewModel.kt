package com.toyota.oneapp.ui

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.combineddataconsent.DeclinePayload
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class ConnectedServicesDialogViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val repository: CombinedDataConsentRepository,
    ) : BaseViewModel() {
        val connectedServicesDialogNavigationEvents = SingleLiveEvent<ConnectedServicesDialogNavigationEvent>()

        init {
            fetchCombinedDataConsents()
        }

        fun fetchCombinedDataConsents() {
            applicationData.getSelectedVehicle()?.let {
                viewModelScope.launch {
                    showProgress()
                    val resource =
                        repository.getCombinedDataConsent(
                            vin = it.vin,
                            brand = it.brand,
                            gen = it.generation,
                            region = it.region,
                        )
                    hideProgress()
                    when (resource) {
                        is Resource.Success -> {
                            resource.data?.payload?.eligibleConsents?.let {
                                it.firstOrNull { consent -> consent.masterConsent }?.description?.declinePayload?.let { declinePayload ->
                                    connectedServicesDialogNavigationEvents.postValue(
                                        ConnectedServicesDialogNavigationEvent.ShowConnectedServicesDecline(
                                            declinePayload,
                                        ),
                                    )
                                } ?: connectedServicesDialogNavigationEvents.postValue(
                                    ConnectedServicesDialogNavigationEvent.ShowConnectedServicesDeclineFailed,
                                )
                            }
                        }
                        is Resource.Failure -> {
                            connectedServicesDialogNavigationEvents.postValue(
                                ConnectedServicesDialogNavigationEvent.ShowConnectedServicesDeclineFailed,
                            )
                        }
                        else -> {}
                    }
                }
            }
        }
    }

sealed class ConnectedServicesDialogNavigationEvent {
    data class ShowConnectedServicesDecline(
        val datConsent: DeclinePayload,
    ) : ConnectedServicesDialogNavigationEvent()

    object ShowConnectedServicesDeclineFailed : ConnectedServicesDialogNavigationEvent()
}
