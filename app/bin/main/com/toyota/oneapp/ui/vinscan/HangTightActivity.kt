package com.toyota.oneapp.ui.vinscan

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.activity.viewModels
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.toyota.oneapp.R
import com.toyota.oneapp.component.IDPHelper
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.AddVehicleActivity
import com.toyota.oneapp.ui.AddVehicleInfoActivity
import com.toyota.oneapp.ui.BlockFleetVehicleActivity
import com.toyota.oneapp.ui.RegistrationErrorActivity
import com.toyota.oneapp.ui.accountsettings.CreatePinActivity
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.vinscan.HangTightViewModel.VehicleLinkEvent.CreatePinEvent
import com.toyota.oneapp.ui.vinscan.HangTightViewModel.VehicleLinkEvent.ErrorEvent
import com.toyota.oneapp.ui.vinscan.HangTightViewModel.VehicleLinkEvent.GoToDashboardEvent
import com.toyota.oneapp.ui.vinscan.HangTightViewModel.VehicleLinkEvent.ShowBlockFleetVehicleEvent
import com.toyota.oneapp.ui.vinscan.HangTightViewModel.VehicleLinkEvent.SuccessEvent
import com.toyota.oneapp.util.BiometryUtil
import com.toyota.oneapp.util.IntentUtil
import com.toyota.oneapp.util.RegistrationErrorType
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import java.security.Signature
import javax.inject.Inject

@AndroidEntryPoint
class HangTightActivity : UiBaseActivity() {
    @Inject
    lateinit var oneAppPreferenceModel: OneAppPreferenceModel

    private val createPinLauncher =
        registerForActivityResult(CreatePinActivity.Contract()) {
            block21MMFleet(viewModel.blockFleetDescription)
        }

    private val viewModel: HangTightViewModel by viewModels()
    private val broadcastReceiver =
        object : BroadcastReceiver() {
            override fun onReceive(
                context: Context?,
                intent: Intent?,
            ) {
                intent?.let { viewModel.onIntentReceived(it) }
            }
        }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        setContentView(R.layout.activity_hang_tight)
        observeEvents()
        registerForBroadcast()
    }

    override fun onStart() {
        super.onStart()
        viewModel.start(intent.getBooleanExtra(ACTIVATION_KEY, false))
    }

    override fun onStop() {
        super.onStop()
        viewModel.stop()
    }

    override fun onDestroy() {
        super.onDestroy()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(broadcastReceiver)
    }

    private fun registerForBroadcast() {
        with(LocalBroadcastManager.getInstance(this)) {
            registerReceiver(
                broadcastReceiver,
                IntentFilter(
                    ToyotaConstants.VEHICLE_ASSOCIATION_ACTION,
                ).also {
                    it.addAction(ToyotaConstants.REMOTE_USER_ACTIVATED)
                },
            )
        }
    }

    private fun observeEvents() {
        observeBaseEvents(viewModel)
        viewModel.vehicleLinkEvent.observe(this, {
            handleVehicleLinkEvent(it)
        })
    }

    private fun handleVehicleLinkEvent(vehicleLinkEvent: HangTightViewModel.VehicleLinkEvent) {
        when (vehicleLinkEvent) {
            is SuccessEvent -> {
                val intent = Intent(this, AddVehicleInfoActivity::class.java)
                intent.putExtra(
                    AddVehicleActivity.KEY_VEHICLE_DETAILS,
                    vehicleLinkEvent.vehicleDetail,
                )
                startActivity(intent)
                finish()
            }
            is ErrorEvent -> {
                viewModel.resetSelectedVehicle()
                val intent =
                    Intent(this, RegistrationErrorActivity::class.java).apply {
                        putExtra(
                            RegistrationErrorType.ERROR_TYPE,
                            if (viewModel.isRemoteActivation) RegistrationErrorType.REMOTE_ACTIVATION_FAILURE else RegistrationErrorType.VEHICLE_LINK_FAILURE,
                        )
                    }
                startActivity(intent)
                finish()
            }
            // Delay of 4 seconds to give enough buffer time for the backend to update remote status
            is GoToDashboardEvent -> {
                Handler(Looper.getMainLooper()).postDelayed(
                    { backToDashboard() },
                    4000,
                )
            }
            is CreatePinEvent -> {
                if (IDPHelper.isRefreshTokenValid(2 * 60, oneAppPreferenceModel)) {
                    createPinLauncher.launch(null)
                } else {
                    showBiometricDialog(vehicleLinkEvent.blockFleetDescription)
                }
            }
            is ShowBlockFleetVehicleEvent -> {
                block21MMFleet(vehicleLinkEvent.blockFleetDescription)
            }
        }
    }

    private fun block21MMFleet(blockFleetDescription: String) {
        val intent = Intent(this, BlockFleetVehicleActivity::class.java)
        intent.putExtra(BlockFleetVehicleActivity.EXTRA_BLOCK_VIN_FROM_QR_SCAN, true)
        intent.putExtra(
            BlockFleetVehicleActivity.EXTRA_BLOCK_FLEET_DESCRIPTION,
            blockFleetDescription,
        )
        intent.putExtra(BlockFleetVehicleActivity.EXTRA_CALIFORNIA_SENATE_BILL, viewModel.caliSenateBill)
        startActivity(intent)
        finish()
    }

    private fun backToDashboard() {
        startActivity(
            IntentUtil.getOADashBoardIntent(
                context = this,
                isDashboardRefresh = true,
                newAddVehicle = true,
            ),
        )
        finish()
    }

    private fun showBiometricDialog(blockFleetDescription: String) {
        if (BiometryUtil.canAuthenticate(this)) {
            BiometryUtil.showBiometryDialog(
                this,
                getString(R.string.Biometry_dialog_title),
                getString(R.string.Biometry_login_to_OneApp),
                getString(R.string.Common_cancel),
                object : BiometryUtil.BiometryCallback {
                    override fun onSuccess(signature: Signature?) {
                        createPinLauncher.launch(null)
                    }

                    override fun onFailure() {
                        doLogin(blockFleetDescription)
                    }

                    override fun onLockedFailure() {
                        doLogin(blockFleetDescription)
                    }

                    override fun onCancelled() {
                        doLogin(blockFleetDescription)
                    }

                    override fun onError(errorCode: Int) {
                        doLogin(blockFleetDescription)
                    }
                },
            )
        } else {
            DialogUtil.showMessageDialog(
                this,
                null,
                getString(R.string.show_login_msg),
                getString(R.string.Common_ok),
                getString(R.string.Common_cancel),
                object : OnCusDialogInterface {
                    override fun onConfirmClick() {
                        doLogin(blockFleetDescription)
                    }

                    override fun onCancelClick() {
                        block21MMFleet(blockFleetDescription)
                    }
                },
                true,
            )
        }
    }

    private fun doLogin(blockFleetDescription: String) {
        DialogUtil.showMessageDialog(
            this,
            getString(R.string.verification_title),
            getString(R.string.verification_message),
            getString(R.string.Common_confirm),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    viewModel.doSignIn(this@HangTightActivity)
                }

                override fun onCancelClick() {
                    block21MMFleet(blockFleetDescription)
                }
            },
            true,
        )
    }

    companion object {
        const val ACTIVATION_KEY = "remote_activation"
    }
}
