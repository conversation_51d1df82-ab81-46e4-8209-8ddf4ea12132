package com.toyota.oneapp.ui.vinscan

import android.content.Context
import android.content.Intent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.extensions.createVehicleInfo
import com.toyota.oneapp.features.core.util.Constants.FLEET_SB1394
import com.toyota.oneapp.model.account.PinCheckRequest
import com.toyota.oneapp.model.account.PinResponse
import com.toyota.oneapp.model.account.VehicleRegistrationPendingPayload
import com.toyota.oneapp.model.pref.LanguagePreferenceModel
import com.toyota.oneapp.model.vehicle.VehicleDetail
import com.toyota.oneapp.network.HttpCode
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.api.repository.VehicleDetailRepository
import com.toyota.oneapp.network.api.repository.VehiclePendingRegistrationRepository
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.BiometryUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import org.forgerock.android.auth.ui.FRNative
import org.forgerock.android.auth.ui.FRNativeResponse
import org.forgerock.android.auth.ui.FRNativeResultListener
import org.forgerock.android.auth.ui.entity.Constants
import toyotaone.commonlib.log.LogTool
import java.util.Timer
import java.util.TimerTask
import javax.inject.Inject

private const val TAG = "HangTightViewModel"

@HiltViewModel
class HangTightViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val preferenceModel: OneAppPreferenceModel,
        private val accountAPIManager: AccountAPIManager,
        private val languagePreferenceModel: LanguagePreferenceModel,
        private val vehicleDetailRepository: VehicleDetailRepository,
        private val vehiclePendingRegistrationRepository: VehiclePendingRegistrationRepository,
    ) : BaseViewModel() {
        private val timer = Timer()
        private var task: TimerTask? = null

        @Volatile
        private var taskExecuting = false
        var isRemoteActivation = false
        private val mVehicleLinkEvent = MutableLiveData<VehicleLinkEvent>()
        val vehicleLinkEvent: LiveData<VehicleLinkEvent> get() = mVehicleLinkEvent

        var caliSenateBill = false

        var blockFleetDescription = ""

        fun start(remoteActivation: Boolean) {
            isRemoteActivation = remoteActivation
            LogTool.d(TAG, "On Start, scheduling timer task")
            taskExecuting = false
            task =
                object : TimerTask() {
                    override fun run() {
                        taskExecuting = true
                        if (isRemoteActivation) {
                            mVehicleLinkEvent.postValue(VehicleLinkEvent.GoToDashboardEvent)
                        } else {
                            callVprs()
                        }
                        cancelTask()
                    }
                }
            timer.schedule(task, ToyotaConstants.VEHICLE_REGISTRATION_PUSH_TIMEOUT_MS)
        }

        fun stop() {
            cancelTask()
        }

        fun onIntentReceived(intent: Intent) {
            handleIntent(intent)
        }

        fun getVehicleInfo(vinNumber: String) {
            LogTool.d(TAG, "Getting vehicle detail for vin: $vinNumber")
            viewModelScope.launch {
                val resource = vehicleDetailRepository.getVehicleDetail(vinNumber)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        updateRegistrationStatus(vinNumber)
                        val event =
                            resource.data?.payload?.vehicle?.let {
                                with(applicationData) {
                                    saveVehicleToList(it)
                                    generationCode = it.generation
                                    setSelectedVehicle(
                                        resource.data?.payload?.createVehicleInfo(
                                            languagePreferenceModel,
                                        ),
                                    )
                                }
                                VehicleLinkEvent.SuccessEvent(it)
                            } ?: VehicleLinkEvent.ErrorEvent
                        mVehicleLinkEvent.postValue(event)
                    }
                    is Resource.Failure -> {
                        if (resource.error?.responseCode.equals("VS-ERROR-21MM")) {
                            // 21MM fleet vin
                            blockFleetDescription = resource.error?.message.orEmpty()
                            checkForPin()
                        } else if (resource.error?.responseCode?.contains(FLEET_SB1394) == true) {
                            caliSenateBill = true
                            blockFleetDescription = resource.error?.message.orEmpty()
                            checkForPin()
                        } else {
                            LogTool.e(
                                TAG,
                                "Received error for vehicle detail request: ${resource.error?.message}",
                            )
                            mVehicleLinkEvent.postValue(VehicleLinkEvent.ErrorEvent)
                        }
                    }
                    else -> {}
                }
            }
        }

        private fun checkForPin() {
            val pinCheckRequest = PinCheckRequest(preferenceModel.getGuid())
            accountAPIManager.sendPinCheckRequest(
                pinCheckRequest,
                object : BaseCallback<PinResponse>() {
                    override fun onSuccess(response: PinResponse) {
                        if (!response.result) {
                            // Pin not created
                            mVehicleLinkEvent.postValue(
                                VehicleLinkEvent.CreatePinEvent(blockFleetDescription),
                            )
                        } else {
                            mVehicleLinkEvent.postValue(
                                VehicleLinkEvent.ShowBlockFleetVehicleEvent(blockFleetDescription),
                            )
                        }
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        if (httpCode == HttpCode.NOT_FOUND.code) {
                            // Pin not created
                            mVehicleLinkEvent.postValue(
                                VehicleLinkEvent.CreatePinEvent(blockFleetDescription),
                            )
                        } else {
                            mVehicleLinkEvent.postValue(
                                VehicleLinkEvent.ShowBlockFleetVehicleEvent(blockFleetDescription),
                            )
                        }
                    }
                },
            )
        }

        private fun callVprs() {
            LogTool.i(
                TAG,
                "Push not received for ${ToyotaConstants.VEHICLE_REGISTRATION_PUSH_TIMEOUT_MS} " +
                    "ms and hence calling vehicle registration pending service for status",
            )
            viewModelScope.launch {
                when (val resource = vehiclePendingRegistrationRepository.getPendingVehicles()) {
                    is Resource.Success -> {
                        LogTool.d(
                            TAG,
                            "Received success response from VRPS, response: ${resource.data}",
                        )
                        val vinNumber = extractPendingVin(resource.data)
                        if (vinNumber.isNullOrEmpty() || isVinAlreadyAdded(vinNumber)) {
                            LogTool.w(
                                TAG,
                                "No valid pending vin received from the vehicle registration pending service,redirecting to error screen",
                            )
                            mVehicleLinkEvent.postValue(VehicleLinkEvent.ErrorEvent)
                        } else {
                            getVehicleInfo(vinNumber)
                        }
                    }
                    else -> {
                        LogTool.e(
                            TAG,
                            "Received error while getting vehicle pending response: ${resource.error}",
                        )
                        mVehicleLinkEvent.postValue(VehicleLinkEvent.ErrorEvent)
                    }
                }
            }
        }

        private fun handleIntent(intent: Intent) {
            LogTool.d(TAG, "Received push intent with action: ${intent.action}")
            if (ToyotaConstants.VEHICLE_ASSOCIATION_ACTION == intent.action) {
                if (taskExecuting) {
                    LogTool.d(TAG, "VPRS call started,ignoring the push")
                    return
                }
                cancelTask()
                val vin = intent.getStringExtra("vin") ?: ToyotaConstants.EMPTY_STRING
                if (vin.contains('"')) {
                    vin.replace("\"", "", true)
                }
                getVehicleInfo(vin)
            }

            if (ToyotaConstants.REMOTE_USER_ACTIVATED == intent.action && !taskExecuting) {
                mVehicleLinkEvent.postValue(VehicleLinkEvent.GoToDashboardEvent)
            }
        }

        private fun cancelTask() {
            task?.cancel()
            task = null
        }

        private fun isVinAlreadyAdded(vin: String): Boolean = applicationData.getVehicleList()?.any { it.vin == vin } == true

        private fun updateRegistrationStatus(vin: String) {
            LogTool.i(TAG, "Updating registration status for vin : $vin")
            vehiclePendingRegistrationRepository.updateVehicleRegistrationStatusAsync(vin)
        }

        private fun extractPendingVin(response: ApiResponse<List<VehicleRegistrationPendingPayload>>?): String? =
            response?.payload?.firstOrNull()?.vin

        fun doSignIn(context: Context) {
            FRNative.apply {
                pushTokenId = preferenceModel.getDeviceToken()
                biometricSupport = BiometryUtil.canAuthenticate(context)
                login(context, frNativeResultListener, true, false)
                setCurrentUserPersist(true)
            }
        }

        fun resetSelectedVehicle() {
            applicationData.handleAddVehicleBackPressed()
        }

        private val frNativeResultListener =
            object : FRNativeResultListener {
                override fun onResult(result: FRNativeResponse) {
                    if (result.success && result.accessToken != null) {
                        IDPData.instance?.handleAccessToken(result.accessToken!!, preferenceModel)
                        mVehicleLinkEvent.postValue(VehicleLinkEvent.CreatePinEvent(blockFleetDescription))
                    } else if (Constants.userAuthError.equals(result.message)) {
                        mVehicleLinkEvent.postValue(
                            VehicleLinkEvent.ShowBlockFleetVehicleEvent(blockFleetDescription),
                        )
                    }
                }
            }

        sealed class VehicleLinkEvent {
            class SuccessEvent(
                val vehicleDetail: VehicleDetail,
            ) : VehicleLinkEvent()

            object ErrorEvent : VehicleLinkEvent()

            object GoToDashboardEvent : VehicleLinkEvent()

            data class CreatePinEvent(
                val blockFleetDescription: String,
            ) : VehicleLinkEvent()

            data class ShowBlockFleetVehicleEvent(
                val blockFleetDescription: String,
            ) : VehicleLinkEvent()
        }
    }
