package com.toyota.oneapp.ui.vinscan

import android.Manifest.permission.CAMERA
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Paint
import android.net.UrlQuerySanitizer
import android.os.Bundle
import android.text.InputType
import android.util.Patterns
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.activity.viewModels
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import com.google.common.util.concurrent.ListenableFuture
import com.google.mlkit.vision.barcode.common.Barcode
import com.tbruyelle.rxpermissions2.Permission
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.component.QRScanHelper
import com.toyota.oneapp.databinding.ActivityQrscanBinding
import com.toyota.oneapp.ui.RegistrationErrorActivity
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import com.toyota.oneapp.ui.vinscan.analzer.BarCodeAnalyzer
import com.toyota.oneapp.ui.vinscan.view.BarcodeBoxView
import com.toyota.oneapp.util.Brand
import com.toyota.oneapp.util.RegistrationErrorType
import com.toyota.oneapp.util.RootUtil
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import org.forgerock.android.auth.ui.BuildConfig
import org.forgerock.android.auth.ui.FRNative
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.permission.PermissionUtil
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

@AndroidEntryPoint
class QRScanActivity : DataBindingBaseActivity<ActivityQrscanBinding>() {
    private val viewModel: QRScanViewModel by viewModels()
    private var showQrScanner = true
    private var isRemoteAuth = false
    private var manualUserCode = ToyotaConstants.EMPTY_STRING
    private var isQrCodeScanned = false
    private var brandName = ToyotaConstants.EMPTY_STRING
    private var flashMode = false

    private lateinit var cameraProviderFuture: ListenableFuture<ProcessCameraProvider>

    private lateinit var cameraExecutor: ExecutorService
    private lateinit var barcodeBoxView: BarcodeBoxView

    companion object {
        const val SHOW_QR_CODE = "show_qr_screen"
        const val REMOTE_AUTH = "remote_auth"
        private const val MANUAL_CODE_LENGTH = 8
        private const val USER_CODE = "user_code"
        private const val REGISTRATION_DEEPLINK = "sms_registration"
        private const val ACTIVATE_DEEPLINK = "activate"
        private val TAG = QRScanActivity::class.java.simpleName
    }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        FRNative.initResultLauncher(this)

        if (!isRemoteAuth) {
            // Sync VPRS before triggering vehicle association flow
            viewModel.syncVprs()
        }
        observeEvents()
    }

    override fun getLayoutId(): Int = R.layout.activity_qrscan

    override fun initViews(savedInstance: Bundle?) {
        cameraProviderFuture = ProcessCameraProvider.getInstance(application)

        cameraExecutor = Executors.newSingleThreadExecutor()
        barcodeBoxView = BarcodeBoxView(this)
        addContentView(
            barcodeBoxView,
            ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT,
            ),
        )
        setUpClickEvents()
        showQrScanner = intent.getBooleanExtra(SHOW_QR_CODE, true)
        isRemoteAuth = intent.getBooleanExtra(REMOTE_AUTH, false)
        if (RootUtil.checkEmulator()) {
            showQrScanner = false // For emulators, only manual pin entry is supported
        } else if (showQrScanner) {
            PermissionUtil.checkOnlyCameraPermissions(this) { permission: Permission ->
                if (permission.granted) {
                    initScan()
                } else if (!permission.shouldShowRequestPermissionRationale) {
                    showForceAllowPermissionDialog()
                }
            }
        }
        val userCode =
            intent.extras?.getString(
                ToyotaConstants.BRANCH_USER_CODE_KEY,
                ToyotaConstants.EMPTY_STRING,
            )
        if (userCode.isNullOrBlank()) {
            showView()
        } else {
            displayManualUI()
            binding.manualCodeEt.text = userCode.toString()
        }
    }

    override fun onStart() {
        super.onStart()
        isQrCodeScanned = false
    }

    override fun onDestroy() {
        super.onDestroy()
        if (!RootUtil.checkEmulator()) {
            cameraExecutor.shutdown()
        }
    }

    private fun observeEvents() {
        observeBaseEvents(viewModel)
        viewModel.qrScanResult.observe(this) {
            onResult(it)
        }
    }

    private fun setUpClickEvents() {
        LogTool.d(TAG, "Setting up click events")
        binding.showManualText.setOnClickListener {
            displayManualUI()
        }

        binding.scanQrText.setOnClickListener {
            if (RootUtil.checkEmulator()) {
                showDialog(getString(R.string.qr_scan_not_supported))
            } else {
                displayQRUI()
            }
        }

        binding.manualCodeEt.setOnCodeChangedListener { (code, completed) ->
            LogTool.d(TAG, "Code edit text: \"$code\"")
            code.let {
                if (code.length == MANUAL_CODE_LENGTH && !binding.manualCodeEt.maskTheCode && code != binding.manualCodeEt.codeErrorChar) {
                    if (completed) hideKeyboard()
                    if (code != manualUserCode) {
                        manualUserCode = code
                        viewModel.processUserCode(this@QRScanActivity, code)
                    }
                }
            }
        }

        binding.btClose.setOnClickListener {
            viewModel.logEvent(AnalyticsEvent.QR_SCAN_CANCEL)
            finish()
        }
    }

    private fun initScan() {
        cameraProviderFuture.addListener(
            {
                val cameraProvider = cameraProviderFuture.get()
                // Preview
                val preview =
                    Preview
                        .Builder()
                        .build()
                        .also {
                            it.setSurfaceProvider(binding.scanView.surfaceProvider)
                        }
                // Image analyzer
                val imageAnalyzer =
                    ImageAnalysis
                        .Builder()
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .build()
                        .also {
                            it.setAnalyzer(
                                cameraExecutor,
                                BarCodeAnalyzer(
                                    this,
                                    barcodeBoxView,
                                    binding.scanView.width.toFloat(),
                                    binding.scanView.height.toFloat(),
                                    Barcode.FORMAT_QR_CODE,
                                ) { result ->
                                    processResult(result ?: "")
                                },
                            )
                        }

                // Select back camera as a default
                val cameraSelector: CameraSelector =
                    CameraSelector
                        .Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()

                try {
                    // Unbind use cases before rebinding
                    cameraProvider.unbindAll()

                    // Bind use cases to camera
                    cameraProvider
                        .bindToLifecycle(
                            this,
                            cameraSelector,
                            preview,
                            imageAnalyzer,
                        ).apply {
                            if (cameraInfo.hasFlashUnit()) {
                                binding.scanQrFlashImg.visibility = View.VISIBLE
                                cameraControl.enableTorch(flashMode)

                                binding.scanQrFlashImg.setOnClickListener {
                                    flashMode = !flashMode
                                    cameraControl.enableTorch(flashMode)
                                }
                            }
                        }
                } catch (exc: Exception) {
                    exc.printStackTrace()
                }
            },
            ContextCompat.getMainExecutor(this),
        )
    }

    private fun processResult(resultCode: String) {
        if (isQrCodeScanned) {
            return
        }
        isQrCodeScanned = true

        LogTool.d(TAG, "Scanned code: $resultCode")

        if (checkIfUINeedsRecreated(resultCode)) return

        val scannedCode = getScannedCode(resultCode)

        if (scannedCode.isNullOrEmpty()) {
            showInvalidUserCode()
        } else {
            viewModel.processUserCode(this@QRScanActivity, scannedCode)
        }
    }

    private fun getScannedCode(resultCode: String) =
        if (resultCode.isNotBlank() &&
            Patterns.WEB_URL.matcher(resultCode).matches() &&
            // Adding the deeplink check, as the endpoint for remote auth has the deeplink path in it (CSTDM-2111)
            (
                resultCode.contains(ToyotaConstants.IDP_AUTH_CODE_SCHEMA) ||
                    resultCode.contains(
                        REGISTRATION_DEEPLINK,
                    )
            )
        ) {
            if (ToyUtil.isSubaru()) {
                if (!resultCode.contains(Brand.SUBARU.name, true)) {
                    null
                }
            } else {
                null
            }

            brandName =
                when {
                    resultCode.contains("toyota") -> {
                        Brand.TOYOTA.brandName
                    } resultCode.contains("lexus") -> {
                        Brand.LEXUS.brandName
                    }

                    else -> {
                        Brand.SUBARU.brandName
                    }
                }
            val uri = UrlQuerySanitizer(resultCode).parameterList
            if (resultCode.contains("\$user_code")) {
                uri.firstOrNull { it.mParameter == "\$user_code" }?.mValue
            } else {
                uri.firstOrNull { it.mParameter == USER_CODE }?.mValue
            }
        } else {
            null
        }

    private fun checkIfUINeedsRecreated(resultCode: String): Boolean {
        if (resultCode.contains(ACTIVATE_DEEPLINK)) {
            intent.putExtra(SHOW_QR_CODE, false)
            recreate() // recreating instead of just calling displayManualUI to unbind the scanning frame
            return true
        }
        return false
    }

    private fun showView() {
        if (showQrScanner) {
            displayQRUI()
        } else {
            displayManualUI()
        }
    }

    private fun displayManualUI() {
        showQrScanner = true
        viewModel.logEvent(AnalyticsEvent.QR_SCAN_MANUAL_INPUT)
        binding.apply {
            scanLayoutFooter.visibility = View.GONE
//            scanView.stop()
            scanView.visibility = View.GONE
            manualCodeEt.codeError = false
            manualCodeEt.clearText()
            manualEtLayout.visibility = View.VISIBLE
            manualCodeEt.visibility = View.VISIBLE
            manualQrDesc.visibility = View.VISIBLE
            manualCodeEt.inputType = InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_VARIATION_PASSWORD
            manualCodeEt.transformationMethod = null
            scanQrText.apply {
                visibility = View.VISIBLE
                paintFlags = Paint.UNDERLINE_TEXT_FLAG
                binding.manualCodeEt.text = ToyotaConstants.EMPTY_STRING
            }
        }
    }

    private fun displayQRUI() {
        showQrScanner = false
        viewModel.logEvent(AnalyticsEvent.QR_SCAN_CAMERA_SCAN)
        if (ContextCompat.checkSelfPermission(applicationContext, CAMERA) == -1) {
            PermissionUtil.checkOnlyCameraPermissions(this) { permission ->
                if (permission.granted) {
                    recreate()
                } else {
                    viewModel.logEvent(AnalyticsEvent.QR_PERMISSIONS_CAMERA_DENIED)
                    showForceAllowPermissionDialog()
                }
            }
        } else if (ContextCompat.checkSelfPermission(applicationContext, CAMERA) == 0) {
            viewModel.logEvent(AnalyticsEvent.QR_PERMISSIONS_CAMERA_GRANTED)
            initScan() // to re-enable camera when coming from manual code UI
            showQrScanner()
        }
    }

    private fun showQrScanner() {
        binding.showManualText.paintFlags = Paint.UNDERLINE_TEXT_FLAG
        restartScanner()
        binding.scanView.visibility = View.VISIBLE
        binding.manualEtLayout.visibility = View.GONE
        binding.manualCodeEt.visibility = View.GONE
        binding.manualQrDesc.visibility = View.GONE
        binding.scanLayoutFooter.visibility = View.VISIBLE
        binding.scanQrText.visibility = View.GONE
    }

    private fun Activity.hideKeyboard() = currentFocus?.also { it.hideKeyboard() }

    private fun View.hideKeyboard() =
        (context.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager)?.also {
            it.hideSoftInputFromWindow(windowToken, 0)
        }

    private fun showErrorDialog(
        title: String? = null,
        message: String,
    ) {
        DialogUtil.showDialog(
            this,
            title,
            message,
            getString(R.string.Common_ok),
            null,
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    restartScanner()
                }

                override fun onCancelClick() { // Implementation not needed
                }
            },
            false,
        )
    }

    private fun showInvalidUserCode() {
        startActivity(
            Intent(this, RegistrationErrorActivity::class.java).apply {
                putExtra(
                    RegistrationErrorType.ERROR_TYPE,
                    RegistrationErrorType.INVALID_USER_CODE,
                )
            },
        )
        finish()
    }

    private fun showLoginMismatch() {
        startActivity(
            Intent(this, RegistrationErrorActivity::class.java).apply {
                putExtra(
                    RegistrationErrorType.ERROR_TYPE,
                    RegistrationErrorType.USER_LOGIN_MISMATCH,
                )
            },
        )
        finish()
    }

    private fun restartScanner() {
        isQrCodeScanned = false
    }

    private fun onResult(result: QRScanHelper.ResultType) {
        hideProgressDialog()
        when (result) {
            QRScanHelper.ResultType.SUCCESS -> {
                startActivity(
                    Intent(this, HangTightActivity::class.java).apply {
                        putExtra(
                            HangTightActivity.ACTIVATION_KEY,
                            isRemoteAuth,
                        )
                    },
                )
                finish()
            }

            QRScanHelper.ResultType.INVALID_QR -> {
                runOnUiThread {
                    showInvalidUserCode()
                }
            }

            QRScanHelper.ResultType.QR_AUTH_FAILED -> {
                viewModel.resetSelectedVehicle()
                brandName =
                    if (BuildConfig.APP_BRAND == Brand.LEXUS.appBrand) {
                        ToyotaConstants.LEXUS
                    } else if (BuildConfig.APP_BRAND == Brand.TOYOTA.appBrand) {
                        ToyotaConstants.TOYOTA
                    } else {
                        ToyotaConstants.SUBARU
                    }
                startActivity(
                    Intent(this, RegistrationErrorActivity::class.java).apply {
                        putExtra(
                            RegistrationErrorType.ERROR_TYPE,
                            if (isRemoteAuth) RegistrationErrorType.REMOTE_ACTIVATION_FAILURE else RegistrationErrorType.QR_SCAN,
                        )
                    },
                )
                finish()
            }

            QRScanHelper.ResultType.IDP_VALIDATION_FAILED -> {
                showErrorDialog(message = getString(R.string.generic_error))
            }

            QRScanHelper.ResultType.IDP_TAMPERED_LOGIN -> {
                runOnUiThread {
                    showLoginMismatch()
                }
            }

            QRScanHelper.ResultType.IDP_VALIDATION_CANCELLED -> {
                restartScanner()
            }
        }
    }
}
