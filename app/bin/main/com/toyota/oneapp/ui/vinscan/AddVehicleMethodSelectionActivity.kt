package com.toyota.oneapp.ui.vinscan

import android.content.Intent
import android.os.Bundle
import com.tbruyelle.rxpermissions2.Permission
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.ActivityAddVehicleMethodSelectionBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.permission.PermissionUtil
import javax.inject.Inject

@AndroidEntryPoint
class AddVehicleMethodSelectionActivity : UiBaseActivity() {
    lateinit var binding: ActivityAddVehicleMethodSelectionBinding

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding = ActivityAddVehicleMethodSelectionBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setSupportActionBar(binding.toolbar)

        binding.btnAddVehicleMethodQr.setOnClickListener {
            launchActivity(true)
        }

        binding.btnAddVehicleMethodCode.setOnClickListener {
            analyticsLogger.logEventWithParameter(
                AnalyticsEventParam.ADD_VEHICLE,
                AnalyticsEventParam.ADD_VEHICLE_BY_CODE_CTA,
            )
            launchActivity(false)
        }
    }

    private fun launchActivity(showQrScan: Boolean = true) {
        val intent = Intent(this, QRScanActivity::class.java)
        intent.putExtra(QRScanActivity.SHOW_QR_CODE, showQrScan)

        if (!showQrScan) {
            startActivity(intent)
            return
        }

        PermissionUtil.checkOnlyCameraPermissions(this) { permission: Permission ->
            if (permission.granted) {
                startActivity(intent)
            } else if (!permission.shouldShowRequestPermissionRationale) {
                showForceAllowPermissionDialog()
            }
        }
    }
}
