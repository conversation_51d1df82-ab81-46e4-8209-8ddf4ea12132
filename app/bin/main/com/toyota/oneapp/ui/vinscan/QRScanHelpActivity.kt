package com.toyota.oneapp.ui.vinscan

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityQrscanHelpBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class QRScanHelpActivity : UiBaseActivity() {
    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        DataBindingUtil
            .setContentView<ActivityQrscanHelpBinding>(
                this,
                R.layout.activity_qrscan_help,
            ).apply {
                preferenceInfoToolbar.setOnClickListener {
                    finish()
                }
            }
    }
}
