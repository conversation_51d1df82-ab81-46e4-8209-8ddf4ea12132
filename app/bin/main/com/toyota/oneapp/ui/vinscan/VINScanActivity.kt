package com.toyota.oneapp.ui.vinscan

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Vibrator
import android.view.View
import android.view.WindowManager
import android.widget.RadioGroup
import androidx.activity.result.contract.ActivityResultContract
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import com.google.common.util.concurrent.ListenableFuture
import com.google.mlkit.vision.barcode.common.Barcode
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityScanVinBinding
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import com.toyota.oneapp.ui.vinscan.analzer.BarCodeAnalyzer
import com.toyota.oneapp.ui.vinscan.analzer.TextAnalyzer
import com.toyota.oneapp.ui.vinscan.view.BarcodeBoxView
import dagger.hilt.android.AndroidEntryPoint
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

@AndroidEntryPoint
class VINScanActivity :
    DataBindingBaseActivity<ActivityScanVinBinding>(),
    View.OnClickListener {
    private lateinit var cameraProviderFuture: ListenableFuture<ProcessCameraProvider>

    private lateinit var cameraExecutor: ExecutorService
    private lateinit var barcodeBoxView: BarcodeBoxView
    private var flashMode = false

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        val decorView = window.decorView
        // Hide the status bar.
        val uiOptions = View.SYSTEM_UI_FLAG_FULLSCREEN
        decorView.systemUiVisibility = uiOptions
    }

    override fun getLayoutId(): Int = R.layout.activity_scan_vin

    override fun initViews(savedInstance: Bundle?) {
        cameraProviderFuture = ProcessCameraProvider.getInstance(application)

        cameraExecutor = Executors.newSingleThreadExecutor()
        barcodeBoxView = BarcodeBoxView(this)

        initVinTextScan()
        binding.apply {
            scanVinCloseImg.setOnClickListener(this@VINScanActivity)
            scanVinHelpImg.setOnClickListener(this@VINScanActivity)
            overlayView.setContent {
                AddBarcodeView()
            }
            scanRadioButton.setOnCheckedChangeListener { group: RadioGroup?, checkedId: Int ->
                if (checkedId == R.id.vin_scan) {
                    initVinTextScan()
                } else {
                    initBarCodeScan()
                }
            }
        }
    }

    private fun vibrate() {
        val vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator
        vibrator.vibrate(200)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.scan_vin_close_img -> onBackPressed()
            R.id.scan_vin_help_img -> startActivity(Intent(this, VINScanHelpActivity::class.java))
            else -> {}
        }
    }

    private fun processResult(result: String) {
        if (result.isNotEmpty()) {
            vibrate()
            setResult(RESULT_OK, Intent().putExtra(Contract.SCANNED_CODE, result))
            finish()
        }
    }

    private fun initVinTextScan() {
        cameraProviderFuture.addListener(
            Runnable {
                val cameraProvider = cameraProviderFuture.get()
                // Preview
                val preview =
                    Preview
                        .Builder()
                        .build()
                        .also {
                            it.setSurfaceProvider(binding.scanView.surfaceProvider)
                        }
                // Image analyzer
                val imageAnalyzer =
                    ImageAnalysis
                        .Builder()
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .build()
                        .also {
                            it.setAnalyzer(
                                cameraExecutor,
                                TextAnalyzer(
                                    this,
                                    barcodeBoxView,
                                    binding.scanView.width.toFloat(),
                                    binding.scanView.height.toFloat(),
                                ) { result ->
                                    processResult(result ?: "")
                                },
                            )
                        }

                // Select back camera as a default
                val cameraSelector: CameraSelector =
                    CameraSelector
                        .Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()

                try {
                    // Unbind use cases before rebinding
                    cameraProvider.unbindAll()

                    // Bind use cases to camera
                    cameraProvider
                        .bindToLifecycle(
                            this,
                            cameraSelector,
                            preview,
                            imageAnalyzer,
                        ).apply {
                            if (cameraInfo.hasFlashUnit()) {
                                binding.scanVinFlashImg.visibility = View.VISIBLE
                                cameraControl.enableTorch(flashMode)

                                binding.scanVinFlashImg.setOnClickListener {
                                    flashMode = !flashMode
                                    cameraControl.enableTorch(flashMode)
                                }
                            }
                        }
                } catch (exc: Exception) {
                    exc.printStackTrace()
                }
            },
            ContextCompat.getMainExecutor(this),
        )
    }

    private fun initBarCodeScan() {
        cameraProviderFuture.addListener(
            Runnable {
                val cameraProvider = cameraProviderFuture.get()
                // Preview
                val preview =
                    Preview
                        .Builder()
                        .build()
                        .also {
                            it.setSurfaceProvider(binding.scanView.surfaceProvider)
                        }
                // Image analyzer
                val imageAnalyzer =
                    ImageAnalysis
                        .Builder()
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .build()
                        .also {
                            it.setAnalyzer(
                                cameraExecutor,
                                BarCodeAnalyzer(
                                    this,
                                    barcodeBoxView,
                                    binding.scanView.width.toFloat(),
                                    binding.scanView.height.toFloat(),
                                    Barcode.FORMAT_CODE_39,
                                    Barcode.FORMAT_CODE_128,
                                ) { result ->
                                    processResult(result ?: "")
                                },
                            )
                        }

                // Select back camera as a default
                val cameraSelector: CameraSelector =
                    CameraSelector
                        .Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()

                try {
                    // Unbind use cases before rebinding
                    cameraProvider.unbindAll()

                    // Bind use cases to camera
                    cameraProvider
                        .bindToLifecycle(
                            this,
                            cameraSelector,
                            preview,
                            imageAnalyzer,
                        ).apply {
                            if (cameraInfo.hasFlashUnit()) {
                                binding.scanVinFlashImg.visibility = View.VISIBLE
                                cameraControl.enableTorch(flashMode)

                                binding.scanVinFlashImg.setOnClickListener {
                                    flashMode = !flashMode
                                    cameraControl.enableTorch(flashMode)
                                }
                            }
                        }
                } catch (exc: Exception) {
                    exc.printStackTrace()
                }
            },
            ContextCompat.getMainExecutor(this),
        )
    }

    class Contract : ActivityResultContract<Void?, String?>() {
        override fun createIntent(
            context: Context,
            input: Void?,
        ): Intent = Intent(context, VINScanActivity::class.java)

        override fun parseResult(
            resultCode: Int,
            intent: Intent?,
        ): String? =
            if (intent != null && resultCode == RESULT_OK) {
                intent.getStringExtra(SCANNED_CODE)
            } else {
                null
            }

        companion object {
            const val SCANNED_CODE = "scanned_code"
        }
    }
}
